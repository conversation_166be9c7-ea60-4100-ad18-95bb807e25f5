<%--
    Document   : LoginUserList
    Created on : Jul 8, 2012, 10:34:11 PM
    Author     : <PERSON><PERSON>
--%>

<%@page import="com.misyn.mcms.admin.LoginHashMap" %>
<%@page import="com.misyn.mcms.admin.User" %>
<%@page import="java.util.Iterator" %>
<%@page import="java.util.Map" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%
    Map<String, User> mMap = LoginHashMap.loginUserMap;
    Iterator iter = mMap.entrySet().iterator();

%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>${CompanyTitle}</title>
</head>
<body>
<table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#666666">
    <tr>
        <td>
            <table width="100%" border="0" cellspacing="1">
                <thead>
                <tr style="background-color:#CCC;font-family:Arial, Helvetica, sans-serif;font-size:12px;">
                    <th>No</th>
                    <th>User ID</th>
                    <th>User Type</th>
                    <th>Login Date/Time</th>
                    <th>Session ID</th>
                    <th>IP Address</th>
                </tr>
                </thead>
                <tbody>
                <%
                    User user = null;
                    int i = 1;
                    while (iter.hasNext()) {
                        Map.Entry mEntry = (Map.Entry) iter.next();
                        user = (User) mEntry.getValue();

                %>
                <tr style="background-color:#FFF;font-family:Arial, Helvetica, sans-serif;font-size:12px;">
                    <td><%=i%>
                    </td>
                    <td><%=user.getV_usrid()%>
                    </td>
                    <td><%=user.getV_usrtype_desc()%>
                    </td>
                    <td><%=user.getD_lastlogindate()%> <%=user.getD_lastlogintime()%>
                    </td>
                    <td><%=user.getSessionID()%>
                    </td>
                    <td><%=user.getIpAddress()%>
                    </td>
                </tr>
                <%
                        i++;
                    }
                %>
                </tbody>
            </table>
        </td>
    </tr>
</table>


<script type="text/javascript">
    parent.document.getElementById("loading").style.display = "none";
    parent.document.getElementById("cell1").style.display = "none";
</script>
</body>
</html>
