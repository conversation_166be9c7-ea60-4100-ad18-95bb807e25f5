/*
    ColorBox Core Style
    The following rules are the styles that are consistant between themes.
    Avoid changing this area to maintain compatability with future versions of ColorBox. 
*/
#colorbox, #cboxOverlay, #cboxWrapper { position: absolute; top: 0; left: 0; z-index: 9999; overflow: hidden; }
#cboxOverlay { position: fixed; width: 100%; height: 100%; }
#cboxMiddleLeft, #cboxBottomLeft { clear: left; }
#cboxContent { position: relative; overflow: visible; }
#cboxLoadedContent { overflow: auto; }
#cboxLoadedContent iframe { display: block; width: 100%; height: 100%; border: 0; margin: 0; padding: 0; }
#cboxTitle { margin: 0; }
#cboxLoadingOverlay, #cboxLoadingGraphic { position: absolute; top: 0; left: 0; width: 100%; }
#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow { cursor: pointer; }
/*
    ColorBox example user style
    The following rules are ordered and tabbed in a way that represents the
    order/nesting of the generated HTML, so that the structure easier to understand.
*/
#cboxOverlay { background: #000000; }
#colorbox { }
#cboxTopLeft { width: 14px; height: 14px; background: url(images/controls.png) no-repeat 0 0; }
#cboxTopCenter { height: 14px; background: url(images/border.png) repeat-x top left; }
#cboxTopRight { width: 14px; height: 14px; background: url(images/controls.png) no-repeat -36px 0; }
#cboxBottomLeft { width: 14px; height: 43px; background: url(images/controls.png) no-repeat 0 -32px; }
#cboxBottomCenter { height: 43px; background: url(images/border.png) repeat-x bottom left; }
#cboxBottomRight { width: 14px; height: 43px; background: url(images/controls.png) no-repeat -36px -32px; }
#cboxMiddleLeft { width: 14px; background: url(images/controls.png) repeat-y -175px 0; }
#cboxMiddleRight { width: 14px; background: url(images/controls.png) repeat-y -211px 0; }
#cboxContent { background: #FFFFFF; }
#cboxLoadedContent { margin-bottom: 5px; }
#cboxLoadingOverlay { background: url(images/loading_background.png) no-repeat center center; }
#cboxLoadingGraphic { background: url(images/loading.gif) no-repeat center center; }
#cboxTitle { position: absolute; bottom: -25px; left: 0; text-align: center; width: 100%; font-weight: bold; color: #7C7C7C; }
#cboxCurrent { position: absolute; bottom: -25px; left: 58px; font-weight: bold; color: #7C7C7C; }
#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow { position: absolute; bottom: -29px; background: url(images/controls.png) no-repeat 0px 0px; width: 23px; height: 23px; text-indent: -9999px; }
#cboxPrevious { left: 0px; background-position: -51px -25px;  border: 0;}
#cboxPrevious.hover { background-position: -51px 0px; }
#cboxNext { left: 27px; background-position: -75px -25px;  border: 0;}
#cboxNext.hover { background-position: -75px 0px; }
#cboxClose { right: 0; background-position: -100px -25px; border: 0; }
#cboxClose.hover { background-position: -100px 0px; }
.cboxSlideshow_on #cboxSlideshow { background-position: -125px 0px; right: 27px; }
.cboxSlideshow_on #cboxSlideshow.hover { background-position: -150px 0px; }
.cboxSlideshow_off #cboxSlideshow { background-position: -150px -25px; right: 27px; }
.cboxSlideshow_off #cboxSlideshow.hover { background-position: -125px 0px; }