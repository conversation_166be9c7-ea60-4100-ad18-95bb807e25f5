/* 
    Document   : auto_complete
    Created on : May 3, 2012, 1:05:29 PM
    Author     : ghost
    Description:
        Purpose of the stylesheet follows.
*/

/* 
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/

.ac_results {
        padding: 0px;
        border: 1px solid black;
        background-color: white;
        overflow: hidden;
        z-index: 99999;
}

.ac_results ul {
        width: 100%;
        list-style-position: outside;
        list-style: none;
        padding: 0;
        margin: 0;
}

.ac_results li {
        margin: 0px;
        padding: 2px 5px;
        cursor: default;
        display: block;
        /* 
        if width will be 100% horizontal scrollbar will apear 
        when scroll mode will be used
        */
        /*width: 100%;*/
        font: menu;
        font-size: 12px;
        /* 
        it is very important, if line-height not setted or setted 
        in relative units scroll will be broken in firefox
        */
        line-height: 16px;
        overflow: hidden;
}

.ac_loading {
        background: white url('indicator.gif') right center no-repeat;
}

.ac_odd {
        background-color: #eee;
}

.ac_over {
        background-color: #0A246A;
        color: white;
}

