/* 
    Document   : forms_new
    Created on : Nov 17, 2010, 10:05:34 AM
    Author     : <PERSON><PERSON>
    Description:
        Purpose of the stylesheet follows.
*/

/*
    Document   : forms_new
    Created on : Nov 17, 2010, 10:05:34 AM
    Author     : Kelum
    Description:
        Purpose of the stylesheet follows.
*/

/*
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/

root {
    display: block;
}

body {
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 5px;
	margin-bottom: 0px;
	font: 62.5% "Trebuchet MS", sans-serif;
 	scrollbar-face-color: #E8E8E8;
	scrollbar-highlight-color: #FFFFFF;
	scrollbar-shadow-color: #C5C5C5;
	scrollbar-3dlight-color: #D1D7DC;
	scrollbar-arrow-color: #5D5D5D;
	scrollbar-track-color: #EFEFEF;
	scrollbar-darkshadow-color: #C5C5C5;
}

body table{
	font-family:Arial, Helvetica, sans-serif;
	font-size:12px;
}

body table td{
	font-family:Arial, Helvetica, sans-serif;
	font-size:12px;
	padding:5px 0px 3px 5px;
}

body table input{
	font-family:Arial, Helvetica, sans-serif;
	font-size:12px;
	width:50px;
}

.button {
	background-image:url(/image/tables/button.jpg);
	background-repeat: no-repeat;
	height:27px;
	width:95px;
	border:0px;
	cursor:pointer;
	font-family:Tahoma,Arial, Helvetica, sans-serif;
	color: #15428B;
	font-size: 12px;
}
.button_dwn {
	background-image:url(/image/tables/button_dwn.jpg);
	background-repeat: no-repeat;
	height:27px;
	width:95px;
	border:0px;
	cursor:pointer;
	font-family: Tahoma,Arial, Helvetica, sans-serif;
	color: #15428B;
	font-size: 12px;
}

#frmheader {
	border:1px solid #999999;
	background-color: #C7C7C7;
	font-family:Arial, Helvetica, sans-serif;
	font-size: 18px;
	color: #333333;
	font-weight: normal;
	position:relative;
	height:50px;
}

#caption {
	position:absolute;
	top:13px;
	left:10px;
}

#caption span{
	font-size: 12px;
	color:#333333;
}

#frmheader img{
	float:right;
	top:0px;
	z-index:0;
}

#spacer {
	height:10px;
}

/*=======form css ====*/

form {
  margin: 0;
  padding: 0;
  font-size: 100%;
  /*min-width: 780px;
  max-width: 800px;*/
  width: 100%;
  font-size:10px;
  font-family:Verdana, Arial, Helvetica, sans-serif;
  color: #666666;

}

form fieldset {
  clear: both;
  font-size: 100%;
  border-color: #cccccc;
  border-width: 1px 0 0 0;
  border-style: dotted none none none;
  padding: 10px;
  margin: 0 0 0 0;
  /*width:50%;*/
}

form fieldset legend {
  font-size: 150%;
  font-weight: normal;
  color: #568DD1;
  margin: 0 0 0 0;
  padding: 0 5px;
  background-color:#FFFFFF;
  cursor:pointer;
}

form fieldset legend img{
	cursor:pointer;
}

label {
  font-size:10px;
  font-family:Verdana, Arial, Helvetica, sans-serif;
}

label u {
  font-style: normal;
  text-decoration: underline;
}

input, select, textarea, file {
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size: 10px;
	color: #666666;
	border: 1px solid #999999;
	width:300px;
	z-index:1;
}

input.listtxt{
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size: 10px;
	color: #666666;
	border: 1px solid #999999;
	width:283px;
	z-index:1;
	float:left;
}

input.drop_down_btn {
	background-image: url(/image/forms/drop_down.jpg);
	background-repeat: no-repeat;
	height: 16px;
	width: 15px;
	float:left;
	border-color: #999999;
	border-width: 1px 1px 1px 0;
	border-style: solid solid solid none;
}
input.drop_down_btn_up {
	background-image: url(/image/forms/drop_down_up.jpg);
	background-repeat: no-repeat;
	height: 16px;
	width: 15px;
	float:left;
	border-color: #999999;
	border-width: 1px 1px 1px 0;
	border-style: solid solid solid none;
}

input.ReadOnly {
  font-family:Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #666666;
    border: 1px solid #999999;
    width:300px;
    z-index:1;
	background-color:#CCCCCC;

}

textarea {
  overflow: auto;
}

form div {
  clear: left;
  display: block;
 /* width: 354px;*/
  zoom: 1;
  margin: 5px 0 0 0;
  padding: 1px 3px;
}

form div.list {
  clear: left;
  display: none;
  margin:0px;
  padding: 2px 0px 0px 0px;
  background:#EFEFEF;
  width:302px;
  border:#333333 solid 1px;
}

form div.list input{
	width:298px;
}

form fieldset div.notes {
  float: right;
  width: 158px;
  height: auto;
  margin: 0 0 10px 10px;
  padding: 5px;
  border: 1px solid #666666;
  background-color: #ffffe1;
  color: #666666;
  font-size: 88%;
}

form fieldset div.notes h4 {
	background-image: url(/image/icon_info.gif);
	background-repeat: no-repeat;
	background-position: left top;
	padding: 3px 0 3px 27px;
	border-width: 0 0 1px 0;
	border-style: solid;
	border-color: #666666;
	color: #666666;
	font-size: 110%;
}

form fieldset div.notes p {
  margin: 0em 0em 1.2em 0em;
  color: #666666;
}

form fieldset div.notes p.last {
  margin: 0em;
  color:#B30000;
}

form div fieldset {
  clear: none;
  border-width: 1px;
  border-style: solid;
  border-color: #666666;
  margin: 0 0 0 144px;
  padding: 0 5px 5px 5px;
  width: 197px;
}

form div fieldset legend {
  font-size: 100%;
  padding: 0 3px 0 9px;
}

form div.required fieldset legend {
  font-weight: bold;
}

form div label {
  display: block;
  margin: 0 0 5px 0;
  text-align: left;
  color: #666666;
}

form div.optional label, label.optional {
  font-weight: normal;
}

form div.required label, label.required {
  font-weight: bold;
}

form div.required div label {
	width:75px;
	float:left;
	font-weight: normal;
}

form div.required div input {
	float:left;
}

form div label.labelCheckbox, form div label.labelRadio {
  float: none;
  display: block;
  width: 200px;
  zoom: 1;
  padding: 0;
  margin: 0 0 5px 142px;
  text-align: left;
}

form div fieldset label.labelCheckbox, form div fieldset label.labelRadio {
  margin: 0 0 5px 0;
  width: 170px;
}

form div img {
  float: left;
  border: 1px solid #000000;
  margin: 0 0 5px 0;
}

p.error {
  background-color: #ff0000;
  background-image: url(/images/icon_error.gif);
  background-repeat: no-repeat;
  background-position: 3px 3px;
  color: #ffffff;
  padding: 3px 3px 5px 27px;
  border: 1px solid #000000;
  margin: auto 100px;
}

form div.error {
  background-color: #ffffe1;
  background-image: url(/images/required_bg.gif);
  background-repeat: no-repeat;
  background-position: top left;
  color: #666666;
  border: 1px solid #ff0000;
}

form div.error p.error {
  background-image: url(/images/icon_error.gif);
  background-position: top left;
  background-color: transparent;
  border-style: none;
  font-size: 88%;
  font-weight: bold;
  margin: 0 0 0 118px;
  width: 200px;
  color: #ff0000;
}

form div select, form div textarea {
  width: 300px;
  padding: 1px 3px;
  margin: 0 0 0 0;
}

form div input.inputText, form div input.inputPassword {
  width: 200px;
  padding: 1px 3px;
  margin: 0 0 0 0;
}

form div input.inputFile {
  width: 211px;
}

form div select.selectOne, form div select.selectMultiple {
  width: 211px;
  padding: 1px 3px;
}

form div input.inputCheckbox, form div input.inputRadio, input.inputCheckbox, input.inputRadio {
  display: inline;
  height: auto;
  width: auto;
  background-color: transparent;
  border-width: 0;
  padding: 0;
  margin: 0 0 0 140px;
}

form div.submit {
  width: 214px;
  padding: 0 0 0 146px;
}

form div.submit div {
  display: inline;
  float: left;
  text-align: left;
  width: auto;

  padding: 0;
  margin: 0;
}

form div input.inputSubmit, form div input.inputButton, input.inputSubmit, input.inputButton {


  /*background-color: #cccccc;
  color: #000000;
  width: auto;
  padding: 0 6px;
  margin: 0;
  */

  	background-image:url(/image/tables/button.jpg);
	background-repeat: no-repeat;
	height:27px;
	width:95px;
	border:0px;
	cursor:pointer;
	font-family:Tahoma,Arial, Helvetica, sans-serif;
	color: #15428B;
	font-size: 12px;
}

form div.submit div input.inputSubmit, form div.submit div input.inputButton {
  float: right;
  margin: 0 0 0 5px;
}

form div small {
  display: block;
  margin: 0 0 5px 142px;
  padding: 1px 3px;
  font-size: 88%;
  zoom: 1;
}

.tbl_Note {
	padding:3px;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:9px;
	color:#115376;
}
.multiDivTag
{
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	display: none;
}

.textReadOnly {
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size: 10px;
	color: #666666;
	border: 1px solid #999999;
	width:300px;
	z-index:1;
	background-color: #CCCCCC;
	font-weight: bold;
}
