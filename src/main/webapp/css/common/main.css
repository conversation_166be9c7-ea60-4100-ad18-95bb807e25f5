@charset "utf-8";
/* CSS Document */

body {
	text-align: center;
	margin: 0px;
	padding: 0px;
	background-repeat: repeat-x;
}
#wrapper {
	text-align: left;
	margin-right: auto;
	margin-left: auto;
	width: 1280px;
	background-color: #FFF;
}
#header {
	background-color: #ffb323;
	background-image: url(../../image/top_header/header_bg.jpg);
	height: 75px;
	width: 100%;
	padding-top: 10px;
}
#header #aviva_logo {
	background-image: url(../../image/top_header/logo.png);
	background-repeat: no-repeat;
	height: 70px;
	width: 187px;
	float: left;
	margin-top: 5px;
	margin-left: 5px;
}
#header #wealth_planner_logo {
	background-image: url(../../image/top_header/headertitle.png);
	background-repeat: no-repeat;
	height: 32px;
	width: 270px;
	float: right;
	margin-top: 15px;
	margin-right: 5px;
	
}
#menu_container {
	background-image: url(../images/menu_bg.jpg);
	background-repeat: repeat-x;
	height: 35px;
	width: 100%;
}
#menu_container table tr td {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #FFF;
}
#body_container table tr td #menu {
	width: 100%;
	/*height: 400px;*/
}
#body_container table tr td #contents{
	/*height: 400px;*/
	width: 100%;
}

.autoHeight {
	height: 800px;
	width: 100%;
}


#footer_container {
	width: 100%;
	background-color: #333;
	font-family: Verdana, Geneva, sans-serif;
	color: #FFF;
	font-size: 10px;
}
#footer_container table tr td {
	padding: 5px;
}

#content_slider {
	width: 100%;
	background-color: #D7D7D7;
}
#content_slider table tr td {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #0776FE;
	padding-top: 10px;
	font-weight: normal;
}
.clearfloat {
	clear:both;
	height:0;
	font-size: 1px;
	line-height: 0px;
}
