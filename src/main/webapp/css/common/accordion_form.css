/* 
    Document   : accordion_form
    Created on : Nov 17, 2010, 10:39:37 AM
    Author     : <PERSON>lum
    Description:
        Purpose of the stylesheet follows.
*/

/* 
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/



body {
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 0px;
	margin-bottom: 0px;
	font: 62.5% "Trebuchet MS", sans-serif;
 	scrollbar-face-color: #E8E8E8;
	scrollbar-highlight-color: #FFFFFF;
	scrollbar-shadow-color: #C5C5C5;
	scrollbar-3dlight-color: #D1D7DC;
	scrollbar-arrow-color: #5D5D5D;
	scrollbar-track-color: #EFEFEF;
	scrollbar-darkshadow-color: #C5C5C5;
}

#frmheader{
	border:#B3B3B3 solid;
	border-width:0px 1px 0px 1px;
	
	padding: 15px 15px 15px 15px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:17px;
	font-weight:bold;
	color:#666;
	background-image: url(../../image/forms/header_img.jpg);
	background-repeat: repeat-x;
}

#frmheader_ {
	border:1px solid #999999;
	background-color: #D6D6D6;
	font-family:Arial, Helvetica, sans-serif;
	font-size: 18px;
	color: #333333;
	font-weight: normal;
	position:relative;
	height:50px;
}

.noteDivClass
{
	border-right-width: 1px;
	border-left-width: 1px;
	border-right-style: solid;
	border-bottom-style: none;
	border-left-style: solid;
	border-top-color: #999;
	border-right-color: #999;
	border-bottom-color: #999;
	border-left-color: #999;
	padding-left:5px; 
	padding-top:5px;
}

#caption {
	position:absolute;
	top:13px;
	left:10px;
	width: 319px;
}

#caption span{
	font-size: 12px;
	color:#333333;
}

#spacer {
	height:10px;
}

form {
  margin: 0;
  padding: 0;
  font-size: 100%;
  /*min-width: 780px;
  max-width: 800px;*/
  width: 100%;
  font-size:10px;
  font-family:Verdana, Arial, Helvetica, sans-serif;
  color: #666666;

}



.MainMenu_Heading
{
	font-family:  Verdana, Arial;
	font-size: 12px;
	font-weight: bold;
	color: #333333;
	padding-top: 2px;
	padding-right: 2px;
	padding-bottom: 2px;
	padding-left: 2px;
	text-align: left;
	border-bottom: 1px dashed #ccc;
}
.SubMenu_Heading
{
	
	font-family:  Verdana, Arial;
	font-size: 11px;
	color: #666666;
	padding-top: 2px;
	padding-right: 2px;
	padding-bottom: 2px;
	padding-left: 2px;
	text-align: left;
	border-bottom: 1px dashed #ccc;
}

.lableDiv{
	height:30px; 
	width:700px;
}
.label1 {
	float: left;
	width: 250px;
	text-align: right;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#666666;
	padding-top:4px;
	padding-right:10px;
	height:20px;
}
.label2 {
	float: left;
	width: 350px;
	text-align: left;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#666666;
	padding-top:4px;
	height:20px;
}

.label_chkbox {
	width:100px;
	float:left;
	height:20px;
	padding-top:4px;	
	text-align: left;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#666666;
	
}

.textFields
{
	float: left;
	width: 200px;
	text-align: left;
	padding-left:2px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#666666;
	border: 1px solid #999;
}

.tbl_textFields
{
	width: 200px;
	text-align: left;
	padding-left:2px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#666666;
	border: 1px solid #999;
}

.lstBox
{
	width:200px;
	height: 20px;
	font-family:  Verdana, Arial;
	font-size: 12px;
	border:gray 1px solid;
    margin:0;
	color: #666666;	
}

.tbl_Note {
	padding:3px;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:9px;
	color:#115376;
}

.drop_down_btn {
	background-image: url(../../image/forms/drop_down.jpg);
	background-repeat: no-repeat;
	height: 16px;
	width: 15px;
	border-color: #999999;
	border-width: 1px 1px 1px 0;
	border-style: solid solid solid none;
}
.drop_down_btn_up {
	background-image: url(/image/forms/drop_down_up.jpg);
	background-repeat: no-repeat;
	height: 16px;
	width: 15px;
	border-color: #999999;
	border-width: 1px 1px 1px 0;
	border-style: solid solid solid none;
}
.button_upload{
	background-image:url(../../image/common/fileupload.png);
	background-repeat: no-repeat;
	height:81px;
	width:81px;
	border:0px;
	cursor:pointer;
	font-family:Tahoma,Arial, Helvetica, sans-serif;
	color: #15428B;
	font-size: 12px;
}
.button{
	background-image:url(../../image/tables/button.jpg);
	background-repeat: no-repeat;
	height:27px;
	width:95px;
	border:0px;
	cursor:pointer;
	font-family:Tahoma,Arial, Helvetica, sans-serif;
	color: #15428B;
	font-size: 12px;
}
.button_dwn {
	background-image:url(/image/tables/button_dwn.jpg);
	background-repeat: no-repeat;
	height:27px;
	width:95px;
	border:0px;
	cursor:pointer;
	font-family: Tahoma,Arial, Helvetica, sans-serif;
	color: #15428B;
	font-size: 12px;
}
.button_fb {
	border:#999999 1px solid;
	background-image: url(fb_form/but_mid.jpg);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#666666;
}
