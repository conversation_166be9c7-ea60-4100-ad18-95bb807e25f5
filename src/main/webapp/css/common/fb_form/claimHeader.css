/* 
    Document   : claimHeader
    Created on : Dec 30, 2012, 9:14:48 AM
    Author     : <PERSON>lum
    Description:
        Purpose of the stylesheet follows.
*/

body {
	background-color: #F2F2F2;
}
 .autoScroll
            {
                height:200px;
                overflow: scroll;	
            }
.hideControl
{
	display: none;	
}
.fbSelectBox {
	border: 1px solid #0C0;
	padding: 3px;
}
.selectbox {
	margin: 0px 5px 10px 0px;
	padding-left:2px;
	font-family:Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size:1em;/* Resize Font*/
	width : 190px; /* Resize Width */
	display : block;
	text-align:left;
	background: url('bg_select.png') right;
	cursor: pointer;
	border:1px solid #D1E4F6;
	color:#333;
}

/* This is the css style that will make your text blink.*/
.blinkytext {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 1.2em;
	text-decoration: blink;
	font-style: normal;
}

.stageView
{
	border:#B3B3B3 solid;
	border-width:0px 1px 0px 1px;
	/*width:746px;*/
	
	
	font-family:Arial, Helvetica, sans-serif;
	font-size:17px;
	font-weight:bold;
	color:#666;
	background-color:#D8DCDF;
	
}
#DivPrgbar
{
	
	background-repeat: no-repeat;
	height:35px;
	width:585px;
	text-align: right;
}

.triangle1
{
	background-image: url("../../../image/claim_stages/Tran1.png");
	background-repeat: no-repeat;
	background-position: 0px;
}
.triangle2
{
	background-image: url(../../../image/claim_stages/Tran2.png);
	background-repeat: no-repeat;
	background-position: 0px;
}
.triangle3
{
	background-image: url(../../../image/claim_stages/Tran3.png);
	background-repeat: no-repeat;
	background-position: 0px;
}
.triangle4
{
	background-image: url(../../../image/claim_stages/Tran4.png);
	background-repeat: no-repeat;
	background-position: 0px;
}
.triangle5
{
	background-image: url(../../../image/claim_stages/Tran5.png);
	background-repeat: no-repeat;
	background-position: 0px;
}

#DivCallCenter
{
	background-repeat: no-repeat;	
	height:30px;
}
#DivAssessorCoord
{
	background-image: url(../../../image/claimprgs/assign_assessor_uncheck.png);
	background-repeat: no-repeat;
	height:30px;
        cursor: pointer;
}
#DivInspection
{
	background-image: url(../../../image/claimprgs/assessor_uncheck.png);
	background-repeat: no-repeat;
	height:30px;
    cursor: pointer;
}

