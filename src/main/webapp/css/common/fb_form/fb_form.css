@charset "utf-8";
/* CSS Document*/

div.container {
	border:#B3B3B3 solid 1px;	
	background-color:#F2F2F2;
	width:800px;
}

.col_full {
	padding: 5px 15px 5px 15px;
	clear:both;
}

.col_full .checkbox, .radio {
	width: 5px;
	height: 21px;
	padding: 0 5px 0 0;
	background: url(checkbox.gif) no-repeat;
	display: block;
	display:inline-block;
	clear:both;
	margin-top:4px;
	padding-left:22px;
	padding-top:4px;
}
.col_full .radio {
	background: url(radio.gif) no-repeat;
} 

.col_full .select {
	position: absolute;
	width: 163px; /* With the padding included, the width is 190 pixels: the actual width of the image. */
	height: 16px;
	padding: 0px 22px 0px 1px;
	color:#333333;
	font: 11px/18px Arial, Helvetica, sans-serif;
	overflow: hidden;
	border: #BDC7D8 solid 1px;
	background:#FFFFFF url(select.gif)  no-repeat   right 0px;
}


.col_full div.row {
	clear: both;
	padding: 5px;
}
.col_full div.row span.label {
	float: left;
	width: 160px;
	text-align: right;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#666666;
	padding-top:4px;
}

.col_half div.row span.label_disp {
	float: left;
	width: 374px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#666666;
	padding-top:4px;
}


.col_full div.row span.opt_cont {
	float: left;
	width: 374px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#666666;
}

.col_full div.row span.txt_cont {
	float: left;
	width: 374px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#666666;
}

.col_full div.row span.txt_cont img{
	padding-top:0px;
	vertical-align:bottom;
}
.col_full div.row span.txt_cont input, textarea, select {
	border: #BDC7D8 solid 1px;
	width:100%;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#333333;
}

.col_full div.row span.txt_cont input.dtpicker {
	border: #BDC7D8 solid 1px;
	width:100px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
}

.col_full div.row span.txt_cont input.drop_down_btn {
	width:17px;
	height:18px;
	background-image: url(drop_down.jpg);
	background-repeat: no-repeat;
	border:none;
}

.col_full div.row span.txt_cont input.drop_down_btn_up {
	width:17px;
	height:18px;
	background-image: url(drop_down_up.jpg);
	background-repeat: no-repeat;
	border:none;
}

.col_full div.row span.txt_cont input.dropdown {
	border: #BDC7D8 solid 1px;
	width:353px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	float:left;
	/*background-image: url(drop_down.jpg);
	background-repeat: no-repeat;
	background-position: 100px;*/
}

.col_full div.spacer {
	clear: both;
}

.col_full div.but_container {
	padding: 15px 0px 0px 175px;
	clear:both;
}
.col_full .button {
	border:#999999 1px solid;
	background-image: url(but_mid.jpg);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#666666;
}
.col_full .button_sel {
	border:#29447E 1px solid;
	background-image: url(but_sel_mid.png);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#FFFFFF;
}
.col_full fieldset {
	border-color:#D9D9D9;
	border-style:solid;
	border-width:0px 0px 1px 0px;
	padding: 10px 0px 12px 0px;
}

body {
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 5px;
	margin-bottom: 0px;
	font: 62.5% "Trebuchet MS", sans-serif;
      
}





