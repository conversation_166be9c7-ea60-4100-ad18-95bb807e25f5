@charset "utf-8";
/* CSS Document*/

div.form_header_{
	border:#B3B3B3 solid;
	border-width:1px 1px 0px 1px;
	/*width:765px;*/
	padding: 8px 15px 5px 15px;
	background-color:#F2F2F2;
	font-family:Arial;
	font-size:15px;
	font-weight:bold;
	color:#000;
	background-image: url(head_line.png);
	background-repeat: repeat-x;
	height:28px;
}

div.form_header {
	border:#B3B3B3 solid;
	border-width:0px 1px 0px 1px;
	/*width:746px;*/
	padding: 15px 15px 15px 15px;
	
	font-family:Arial, Helvetica, sans-serif;
	font-size:17px;
	font-weight:bold;
	color:#666;
	background-image: url(header_img.jpg);
	background-repeat: repeat-x;	
}

div.container {
	background-color:#F2F2F2;
	/*width:746px;*/
	padding: 5px 15px 5px 15px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #B3B3B3;
	border-right-color: #B3B3B3;
	border-bottom-color: #B3B3B3;
	border-left-color: #B3B3B3;
}

div.spacer {
	clear: both;
}

.col_half {
	width:48%;
	float:left;
	padding:5px;
}

.col_half .checkbox, .radio {
	width: 5px;
	height: 21px;
	padding: 0 5px 0 0;
	background: url(checkbox.gif) no-repeat;
	display: block;
	display:inline-block;
	clear:both;
	margin-top:4px;
	padding-left:22px;
	padding-top:4px;
}
.col_half .radio {
	background: url(radio.gif) no-repeat;
} 

.col_half .select {
	position: absolute;
	width: 163px; /* With the padding included, the width is 190 pixels: the actual width of the image. */
	height: 16px;
	padding: 0px 22px 0px 1px;
	color:#333333;
	font: 11px/18px Arial, Helvetica, sans-serif;
	overflow: hidden;
	border: #BDC7D8 solid 1px;
	background:#FFFFFF url(select.gif)  no-repeat   right 0px;
}

.col_half div.row {
	clear: both;
	padding: 5px;
}
.col_half div.row span.label {
	float: left;
	width: 140px;
	text-align: right;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#666666;
	padding-top:4px;
}

.col_half div.row span.label_disp {
	float: left;
	width: 254px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#666666;
	padding-top:4px;
}

.col_half div.row span.opt_cont {
	float: left;
	width: 224px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#666666;
}

.col_half div.row span.txt_cont {
	float: left;
	width: 224px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#666666;
}

.col_half div.row span.txt_cont img{
	padding-top:0px;
	vertical-align:bottom;
}
.col_half div.row span.txt_cont input, textarea, select {
	border: #BDC7D8 solid 1px;
	width:100%;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#333333;
	text-transform: uppercase;
}

.col_half div.row span.txt_cont input.dtpicker {
	border: #BDC7D8 solid 1px;
	width:100px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
}

.col_half div.row span.txt_cont input.drop_down_btn {
	width:17px;
	height:18px;
	background-image: url(drop_down.jpg);
	background-repeat: no-repeat;
	border:none;
	cursor:pointer;
}

.col_half div.row span.txt_cont input.drop_down_btn_up {
	width:17px;
	height:18px;
	background-image: url(drop_down_up.jpg);
	background-repeat: no-repeat;
	border:none;
	cursor:pointer;
}

.col_half div.row span.txt_cont input.dropdown {
	border: #BDC7D8 solid 1px;
	width:200px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	float:left;
	/*background-image: url(drop_down.jpg);
	background-repeat: no-repeat;
	background-position: 100px;*/
}


.col_half div.but_container {
	padding: 15px 0px 0px 120px;
	clear:both;
}
.col_half .button {
	border:#999999 1px solid;
	background-image: url(but_mid.jpg);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#666666;
}
.col_half .button_sel {
	border:#29447E 1px solid;
	background-image: url(but_sel_mid.png);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#FFFFFF;
}
.col_half #mainFieldset  {
	border-color:#D9D9D9;
	border-style:solid;
	border-width:1px 1px 1px 1px;
	padding-top: 10px;
	padding-right: 5px;
	padding-bottom: 12px;
	padding-left: 0px;
}

body {
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 0px;
	margin-bottom: 0px;
	font: 62.5% "Trebuchet MS", sans-serif;
      
}





.col_full {
	padding: 5px;
	clear:both;
}

.col_full .checkbox, .radio {
	width: 5px;
	height: 21px;
	padding: 0 5px 0 0;
	background: url(checkbox.gif) no-repeat;
	display: block;
	display:inline-block;
	clear:both;
	margin-top:4px;
	padding-left:22px;
	padding-top:4px;
}
.col_full .radio {
	background: url(radio.gif) no-repeat;
} 

.col_full .select {
	position: absolute;
	width: 163px; /* With the padding included, the width is 190 pixels: the actual width of the image. */
	height: 16px;
	padding: 0px 22px 0px 1px;
	color:#333333;
	font: 11px/18px Arial, Helvetica, sans-serif;
	overflow: hidden;
	border: #BDC7D8 solid 1px;
	background:#FFFFFF url(select.gif)  no-repeat   right 0px;
}


.col_full div.row {
	clear: both;
	padding: 5px;
}

.col_full div.row span.label {
	float: left;
	width: 100px;
	text-align: right;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#666666;
	padding-top:4px;
}

.col_full div.row span.label_disp {
	float: left;
	width: 374px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#666666;
	padding-top:4px;
}


.col_full div.row span.opt_cont {
	float: left;
	width: 374px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#666666;
}

.col_full div.row span.txt_cont {
	float: left;
	width: 374px;
	text-align: left;
	padding-left:10px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	color:#666666;
}

.col_full div.row span.txt_cont img{
	padding-top:0px;
	vertical-align:bottom;
}
.col_full div.row span.txt_cont input, textarea, select {
	border: #BDC7D8 solid 1px;
	width:100%;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#333333;
}

.col_full div.row span.txt_cont input.dtpicker {
	border: #BDC7D8 solid 1px;
	width:100px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
}

.col_full div.row span.txt_cont input.drop_down_btn {
	width:17px;
	height:18px;
	background-image: url(drop_down.jpg);
	background-repeat: no-repeat;
	border:none;
	cursor:pointer;
}

.col_full div.row span.txt_cont input.drop_down_btn_up {
	width:17px;
	height:18px;
	background-image: url(drop_down_up.jpg);
	background-repeat: no-repeat;
	border:none;
	cursor:pointer;
}

.col_full div.row span.txt_cont input.dropdown {
	border: #BDC7D8 solid 1px;
	width:353px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	float:left;
	/*background-image: url(drop_down.jpg);
	background-repeat: no-repeat;
	background-position: 100px;*/
}

.col_full div.spacer {
	clear: both;
}

.col_full div.but_container {
	padding: 15px 0px 0px 120px;
	clear:both;
}
.col_full .button {
	border:#999999 1px solid;
	background-image: url(but_mid.jpg);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#666666;
}
.col_full .button_sel {
	border:#29447E 1px solid;
	background-image: url(but_sel_mid.png);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#FFFFFF;
}
.col_full fieldset {
	border-color:#D9D9D9;
	border-style:solid;
	border-width:0px 0px 1px 0px;
	padding: 10px 0px 12px 0px;
}

.label_Value {
	float: left;
	width: 140px;
	text-align: left;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#333;
	padding-top:4px;
	padding-left:5px;
}
.label_Value_Border {
	float: left;
	width: 140px;
	text-align: left;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#333;
	padding-top:4px;
	padding-left:5px;
	border: 1px solid #BDC7D8;
	height: 18px;
}

.label_Value_Red {
	float: left;
	width: 140px;
	text-align: left;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#333;
	padding-top:4px;
	padding-left:5px;
	background-color: #F00;
}

.label_Value_Yellow {
	float: left;
	width: 140px;
	text-align: left;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#333;
	padding-top:4px;
	padding-left:5px;
	background-color: #FF0;
}

.ErrorNote {
	font-family:Arial, Helvetica, sans-serif;
	font-size:12px;
	font-weight:bold;
	color:#D50000;
	border-top-width: 1px;
	border-top-color: #CCCCCC;
	padding-top: 3px;
	padding-right: 3px;
	padding-bottom: 3px;
	padding-left: 15px;
	text-decoration: blink;
}

.label_Enable {
	float: left;
	width: 140px;
	text-align: right;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#666666;
	padding-top:4px;
}
.label_diable {
	float: left;
	width: 140px;
	text-align: right;
	font-family:Arial;
	font-size:11px;
	font-weight:bold;
	color:#CCC;
	padding-top:4px;
}

.textField {
	border: #BDC7D8 solid 1px;
	width:100%;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#333333;
	text-transform: uppercase;
}

.hightLight_textField {
	border: #BDC7D8 solid 1px;
	width:100%;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#333333;
	text-transform: uppercase;
	background-color: #FF6;
}

.lstbox {
	font-family:Arial, Helvetica, sans-serif;
	font-size: 11px;
	border:#BDC7D8 1px solid;    
	padding:5px;
	color: #333333;
	width:100%;
	z-index:1;
	
}

.popButton {
	border:#999999 1px solid;
	background-image: url(but_mid.jpg);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:19px;
	cursor:pointer;
	color:#666666;
	width:20px;
	text-align:center;
}

.popButton_normal {
	border:0px none #999999;
	background-image: url(../../../image/forms/drop_down_normal.png);
	background-repeat: no-repeat;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:18px;
	cursor:pointer;
	color:#666666;
	width:18px;
	text-align:center;
	
}



