/* 
    Document   : tableStyle
    Created on : Jun 19, 2010, 12:37:21 PM
    Author     : <PERSON><PERSON>
    Description:
        Purpose of the stylesheet follows.
*/

/* 
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/



#frmheader1 {
	border:#B3B3B3 solid;
	border-width:1px 1px 0px 1px;
	width:781px;
	padding: 8px 15px 0px 15px;
	background-color:#F2F2F2;
	font-family:Arial;
	font-size:15px;
	font-weight:bold;
	color:#000;
	background-image: url(../../image/forms/head_line.png);
	background-repeat: repeat-x;
	height:28px;
}
#frmheader{
	border:#B3B3B3 solid;
	border-width:0px 1px 0px 1px;
	/*width:97%;*/
	padding: 15px 15px 15px 15px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:17px;
	font-weight:bold;
	color:#666;
	background-image: url(../../image/forms/header_img.jpg);
	background-repeat: repeat-x;
}

#caption {
	position:absolute;
	top:20px;
	left:10px;
}


#caption span{
	font-size: 12px;
	color:#333333;
}


#frmheader img{
	float:right;
	top:0px;
	z-index:0;
}

.ErrorNote {
	font-family:Arial, Helvetica, sans-serif;
	font-size:12px;
	font-weight:bold;
	color:#D50000;
	border-top-width: 1px;
	border-top-color: #CCCCCC;
	padding-top: 3px;
	padding-right: 3px;
	padding-bottom: 3px;
	padding-left: 1px;
}

.tableWidth
{
	width:100%;
}

body {
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 0px;
	margin-bottom: 0px;
	 font: 62.5% "Trebuchet MS", sans-serif;
      
}

.container {
	background-image:url(/image/dashboard/container_bg.jpg);
	background-repeat:repeat-x;
	background-position: bottom;
	border: 1px solid #CACACA;
	padding:10px;
}

.PageHeader {
	font-family: arial, verdana, helvetica, sans-serif;
	text-align:left;
	clear:both;
	color: #004fb6;
	font-size: 22px;
	font-weight: normal;
	padding: 10px 5px 20px 0px;
	border-bottom: 1px dashed #ccc;

}

.Sub_Header {
	font-family:  arial, verdana, helvetica, sans-serif;
	color: #004fb6;
	font-size: 14px;
	padding: 5px 0px 10px 0px;
	border-bottom: 1px dashed #ccc;
}

.Sub_Header span{
	color:#666666;
	font-size: 12px;
}


.tbl {
	border:1px solid #79A0D3;
}
.tbl_header {
	border:1px solid #79A0D3;
	background-color: #E3EFFF;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 18px;
	color: #568DD1;
	font-weight: normal;
	padding: 10px;
	position:relative;
}

.tbl_header div{
	width:300px;
	float:right;
	top:0px;
}
.tbl_sub_header {
	background-color: #F2F2F2;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 15px;
	color: #333333;
	font-weight: normal;
	padding-top: 5px;
	padding-right: 15px;
	padding-bottom: 5px;
	padding-left: 15px;
	border-right-width: 1px;
	border-left-width: 1px;
	border-right-style: solid;
	border-left-style: solid;
	border-top-color: #999999;
	border-right-color: #999999;
	border-bottom-color: #999999;
	border-left-color: #999999;
}

.tbl_but_cont {
	padding-top: 10px;
	padding-bottom: 10px;
}
.tbl_row_header {
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	text-align: center;
	vertical-align: middle;
	background-image:url(../../image/tables/header.png);
	background-repeat: repeat-x;
	background-position: bottom;
	background-color:#F0F0F0;
	padding:5px;
	cursor:pointer;
}

.tbl_row_header_left {
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	text-align: left;
	vertical-align: middle;
	background-image:url(../../image/tables/header.png);
	background-repeat: repeat-x;
	background-position: bottom;
	background-color:#F3F9FF;
	padding:5px;
}

.tbl_row_header_left span{
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	text-align: left;
	color:#666666;
}

.tbl_cat_header {
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	text-align: left;
	font-weight: bold;
	color:#CD5C01;
	vertical-align: middle;
	background-image:url(../../image/tables/tbl_cat_header.jpg);
	background-repeat: repeat-x;
	background-position: bottom;
	background-color:#FFFFFF;
	padding-left:20px;
	height:35px;
	border: 1px solid #FEBA52;
}

.tbl_cat_header_sub {
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	text-align: left;
	font-weight: bold;
	color:#4C4C4C;
	vertical-align: middle;
	background-image:url(../../image/tables/tbl_cat_header_sub.jpg);
	background-repeat: repeat-x;
	background-position: bottom;
	background-color:#FFFFFF;
	padding-left:20px;
	height:30px;
	border-color: #cccccc;
	border-width: 0 1px 1px 1px;
	border-style: none solid solid solid;
}

.tbl_row {
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	background:#FFFFFF;
	height:22px;
	color:#333333;
}

.tbl_row td{
	padding:2px 5px 2px 5px;
}

.tbl_row td a, a:hover{
	text-decoration:none;
	color:#333333;
}


.tbl_row2 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	height:22px;
	background-color: #F8F8F8;
}

.tbl_row2 td{
	padding:2px 5px 2px 5px;
}
.tbl_row2 td a, a:hover{
	text-decoration:none;
	color:#333333;
}


.tbl_row_selected {
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	background:#FCDAE3;
	height:22px;
	cursor:pointer;
}

.tbl_row_selected td{
	padding:2px 5px 2px 5px;
}

.tbl_row_selected td a, a:hover{
	text-decoration:none;
	color:#333333;
}


.tbl_Note {
	padding:3px;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:9px;
	color:#115376;
}

.tbl_spacer {
	height:10px;
	background-color:#FFFFFF;
}

.tbl_spacer_small {
	height:5px;
	background-color:#FFFFFF;
}
.button {
	background-image:url(../../image/tables/button.jpg);
	background-repeat: no-repeat;
	height:27px;
	width:95px;
	border:0px;
	cursor:pointer;
	font-family:Tahoma,Arial, Helvetica, sans-serif;
	color: #666;
	font-size: 12px;
}
 .buttonFB {
	border:#999999 1px solid;
	background-image: url(/fb_form/but_mid.jpg);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#666666;
}
.button_dwn {
	background-image:url(../../image/tables/button_dwn.jpg);
	background-repeat: no-repeat;
	height:27px;
	width:95px;
	border:0px;
	cursor:pointer;
	font-family: Tahoma,Arial, Helvetica, sans-serif;
	color: #FFF;
	font-size: 12px;
}

.trClick {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #9DB1CC;
	cursor:pointer;
	padding:5px;
}
.trClickPos {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #D77779;
	font-weight: bold;
	cursor:pointer;
	padding:5px;
}






.tableTopHeader {
	font-family: Tahoma;
	font-size: 11px;
	color: #FFFFFF;
	font-weight: bold;
}
.tableRowColor1 {
	font-family: Tahoma;
	font-size: 11px;
	background-color: #F7F7F7;
	color: #333333;
	padding: 5px;
	border-style:solid;
	border-color:#999999;
	border-width: 1px 1px 0px 1px;
}


.tableRowColor2 {
	font-family: Tahoma;
	font-size: 11px;
	color: #333333;
	padding: 5px;
	border-style:solid;
	border-color:#79A0D3;
	border-width: 0px 1px 1px 1px;
	background-color:#B7CEEE;
}

.tableRowHead {
	font-family: Tahoma;
	font-size: 11px;
	background-color: #CCCCCC;
	color: #333333;
	padding: 5px;
	border: 1px solid #999999;	
}


.tableRowColor_selected {
	font-family: Tahoma;
	font-size: 11px;
	background-color: #FFCC00;
	color: #333333;
	padding-left: 3px;
	border: 1px solid #999999;
	cursor: pointer;
        height:25px;
}

.tableBorder{
	border: 1px solid #999999;        

}
.tableCellBorder{
	padding-left: 3px;
	border-top-color: #666666;
	border-right-color: #666666;
	border-bottom-color: #666666;
	border-left-color: #666666;
}
.tableCellBorder a{
	padding-left: 3px;
	border-top-color: #666666;
	border-right-color: #666666;
	border-bottom-color: #666666;
	border-left-color: #666666;
	text-decoration: none;
	color: #000033;
}

.tableCellBorder a:hover{
	padding-left: 3px;
	border-top-color: #666666;
	border-right-color: #666666;
	border-bottom-color: #666666;
	border-left-color: #666666;
	text-decoration: none;
	color: #FF9900;
}

.tableHeaderImg{
	padding-left: 3px;
	background-image: url(../../image_cmm/table/tblHeader.jpg);
	background-repeat: repeat-x;
	font-family: Tahoma;
	font-size: 11px;
	color: #000000;
        height:22px;
        text-align: left;

}
.tableHeaderTR
{
     height:22px;
    text-align: left;
    padding-left:3px;
}
.tableBottomRows
{
	background-color: #999999;
}

.lblNewAdd {
	font-family:  Verdana, Arial;
	font-size: 11px;
	color: #000053;
	width: 130px;
	position:absolute;
	left: 10px;
	font-weight: bold;
	text-decoration: none;
	padding-top: 5px;
}

.lblNewAdd a {
	font-family:  Verdana, Arial;
	font-size: 11px;
	color: #000053;
	width: 130px;
	position:absolute;
	left: 1px;
	font-weight: bold;
	text-decoration: none;
	padding-top: 5px;
	top: -43px;
}

.lblNewAdd a:hover {
	font-family:  Verdana, Arial;
	font-size: 11px;
	color: #FF0000;
	width: 130px;
	position:absolute;
	left: 10px;
	font-weight: bold;
	text-decoration: none;
	padding-top: 5px;
}

.table2 {
	font-family: Verdana;
	font-size: 11px;
	color: #000000;
	height: 25px;
	border: none;
	font-weight: bold;

}
.table {
	font-family: Verdana;
	font-size: 11px;
	color: #000000;
	height: 25px;
	border: none;
	font-weight: bold;
	background-color: #FFFFFF;
}
.textGrey {
	font-family: Verdana;
	font-size: 11px;
	color: #666666;
}
.table3 {
	font-family: Verdana;
	font-size: 10px;
	color: #666666;
	height: 20px;
	border: none;
	font-weight: bold;
	background-color: #F0F0F0;
}

.statementText3 {
	font-family: Verdana;
	font-size: 9px;
	height: 20px;
	border: none;
	background-color: #FFFFFF;
}

.Msg_Header {
	background-color:#CCEDFF;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	font-weight:bold;
	color:#004FB6;
	text-align: left;
	padding: 5px;
	vertical-align:top;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-top-color: #CCCCCC;
	border-right-color: #CCCCCC;
	border-bottom-color: #CCCCCC;
}

.Msg_Header_img {
	background-color:#CCEDFF;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	font-weight:bold;
	color:#004FB6;
	text-align: left;
	padding: 5px;
	vertical-align:top;
	border-top-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #CCCCCC;
	border-bottom-color: #CCCCCC;
	border-left-color: #CCCCCC;
}
.Msg_margin {
	vertical-align:top;
	padding-left: 20px;
	padding-right: 10px;
}

.Msg_Cont{
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11px;
	color: #999999;
	padding: 10px 0px 5px 5px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #CCCCCC;
}

.Msg_Cont span{
	font-family: Arial, Helvetica, sans-serif;
	font-size: 10px;
	color: #004fb6;
}

.txtfield {
	border: 1px solid #999999;
	width:93%;
}



label {
  font-size:10px;
  font-family:Verdana, Arial, Helvetica, sans-serif;
  color:#666666;
}

input, select, textarea, file {
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size: 10px;
	color: #666666;
	border: 1px solid #999999;
	width:80px;
	z-index:1;
	/*float:left*/
}

input.calander {
	background-image: url(../../image/dashboard/spacer.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	height: 16px;
	width: 1px;
	border-color: #999999;
	border-width: 1px 1px 1px 0;
	border-style: none none none none;	/*float:left*/
	
}

table.field tr {
	height:20px;
}

.chkBox
{
	width: 30px;
	text-align: left;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
}

.btnFB {
	border:#999999 1px solid;
	background-image: url(fb_form/but_mid.jpg);
	background-repeat: repeat-x;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:bold;
	height:22px;
	cursor:pointer;
	color:#666666;
}


.tbl_row_red {
	padding:2px 5px 2px 5px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	height:22px;
	color:#333333;
	background-color: #FFAEAE;
}
.tbl_row_red td{
	padding:2px 5px 2px 5px;
}

.tbl_row_red td a, a:hover{
	text-decoration:none;
	color:#333333;
}

.tbl_row_pink {
	padding: 2px 5px 2px 5px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11px;
	vertical-align: middle;
	height: 22px;
	color: #333333;
	background-color: #E1A8E1;
}

.tbl_row_yellow {
	padding:2px 5px 2px 5px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	height:22px;
	color:#333333;
	background-color: #F7EF95;
}

.tbl_row_yellow td{
	padding:2px 5px 2px 5px;
}

.tbl_row_yellow td a, a:hover{
	text-decoration:none;
	color:#333333;
}

.tbl_row_green {
	padding:2px 5px 2px 5px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	height:22px;
	color:#333333;
	background-color: #C2FFAE;
}
.tbl_row_green td{
	padding:2px 5px 2px 5px;
}

.tbl_row_green td a, a:hover{
	text-decoration:none;
	color:#333333;
}

.tbl_row_blue {
	padding:2px 5px 2px 5px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	height:22px;
	color:#333333;
	background-color: #BBC9FF;
}
.tbl_row_blue td{
	padding:2px 5px 2px 5px;
}

.tbl_row_blue td a, a:hover{
	text-decoration:none;
	color:#333333;
}


.tbl_row_brown {
	padding:2px 5px 2px 5px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	vertical-align: middle;
	height:22px;
	color:#333333;
	background-color: #E68860;
}
.tbl_row_brown td{
	padding:2px 5px 2px 5px;
}

.tbl_row_brown td a, a:hover{
	text-decoration:none;
	color:#333333;
}
