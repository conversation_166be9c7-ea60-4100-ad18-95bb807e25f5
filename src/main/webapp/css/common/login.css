body {background-color: #CCCCCC}
header.navbar {height: 60px;}
/*.login-container {position: relative;}*/
.login-card {position: absolute; top:50%; left: 18%; transform: translate(-50%,-50%); width: 324px;}
.login-header {background-color: #015aaa;padding: 10px 0;margin-top: -60px;border-radius: 5px; box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgb(0, 90, 170);}
.login-header .card-title {padding: 10px;}
.login-card .form-group {margin-bottom: 10px;}
.card-header{background-color: #ffffff!important;border-bottom: 4px solid rgb(0, 92, 171)!important;}
.callcolor{
	color: #6d6d6d;
}

@media only screen
and (min-device-width : 768px)
and (max-device-width : 1024px)
and (orientation : portrait) {
	.login-card {position: absolute; top:50%; left: 50%; transform: translate(-50%,-50%); width: 324px;}
}

@media (max-width: 767.98px) {
	.login-card {width: 400px;  transform: none; margin-bottom: 60px; left: 20%; top:30%;}
	}


@media (max-width: 575.98px) {
	.login-card {position: inherit;width: 100%; transform: none; margin: 50px auto;}
	}