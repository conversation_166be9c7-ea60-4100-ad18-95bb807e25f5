/* Menu CSS */#cssmenu,
#cssmenu > ul {
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    position: relative;
}
#cssmenu:before,
#cssmenu:after,
#cssmenu > ul:before,
#cssmenu > ul:after {
    content: '';
    display: table;
}
#cssmenu:after,
#cssmenu > ul:after {
    clear: both;
}
#cssmenu {
    width: auto;
    zoom: 1;
}
#cssmenu > ul {
    background: #c1c1c1;
/*    background: -moz-linear-gradient(#f5f5f5, #c1c1c1);
    background: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), to(#c1c1c1));
    background: -webkit-linear-gradient(#f5f5f5, #c1c1c1);
    background: -o-linear-gradient(#f5f5f5, #c1c1c1);
    background: -ms-linear-gradient(#f5f5f5, #c1c1c1);
    background: linear-gradient(#f5f5f5, #c1c1c1);*/
    border-width: 0px;
    border-style: solid;
    border-color: #fff #ccc #999 #eee;
    margin: 0;
    padding: 0;
    position: relative;
}
#cssmenu > ul li {
    margin: 0;
    padding: 0;
    list-style: none;
}
#cssmenu > ul > li {
    float: left;
    position: relative;
}
#cssmenu > ul > li > a {
    cursor:pointer;
    padding: 6px 26px;
    display: block;
    color: #555;
    font: bold 10px Arial, Helvetica, sans-serif;
    text-decoration: none;
    letter-spacing: 1px;
    text-transform: uppercase;
    border-width: 1px;
    border-style: solid;
    border-color: #fff #ccc #999 #eee;
    border-width: 0px 1px 0px 1px;
    background: #c1c1c1;
/*    background: -moz-linear-gradient(#f5f5f5, #c1c1c1);
    background: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), to(#c1c1c1));
    background: -webkit-linear-gradient(#f5f5f5, #c1c1c1);
    background: -o-linear-gradient(#f5f5f5, #c1c1c1);
    background: -ms-linear-gradient(#f5f5f5, #c1c1c1);
    background: linear-gradient(#f5f5f5, #c1c1c1);*/
}
#cssmenu > ul > li:hover > a {
    cursor:pointer;
    outline: 0;
    color: #fff;
    font: bold 10px Arial, Helvetica, sans-serif;
    text-shadow: 0 1px 0 rgba(0,0,0,.2);
    background: #d31145;
/*    background: -moz-linear-gradient(#d31145, #A40D36); 
    background: -webkit-gradient(linear, left top, left bottom, from(#d31145), to(#A40D36));
    background: -webkit-linear-gradient(#d31145, #A40D36);
    background: -o-linear-gradient(#d31145, #A40D36);
    background: -ms-linear-gradient(#d31145, #A40D36);
    background: linear-gradient(#d31145, #A40D36);*/
}
#cssmenu > ul > li > a > span {
    line-height: 18px;
}

/* Childs */
#cssmenu > ul ul {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    top: 120px;
    background: #d31145;
/*    background: -moz-linear-gradient(#d31145, #A40D36);
    background: -webkit-gradient(linear, left top, left bottom, from(#d31145), to(#A40D36));
    background: -webkit-linear-gradient(#d31145, #A40D36);
    background: -o-linear-gradient(#d31145, #A40D36);
    background: -ms-linear-gradient(#d31145, #A40D36);
    background: linear-gradient(#d31145, #A40D36);*/
    margin: 0;
    padding: 0;
    z-index: -1;
}
#cssmenu > ul li:hover ul {
    opacity: 1;
    visibility: visible;
    margin: 0;
    color: #000;
    z-index: 2;
    top: 30px;
    left: 0;
}
#cssmenu > ul ul:before {
    content: '';
    position: absolute;
    top: -10px;
    width: 100%;
    height: 20px;
    background: transparent;
}
#cssmenu > ul ul li {
    list-style: none;

    padding: 0;
    margin: 0;
    width: 100%;
}
#cssmenu > ul ul li a {
    cursor:pointer;
    padding: 8px 26px;
    display: block;
    color: #FFF;
    font: bold 10px Arial, Helvetica, sans-serif;
    text-decoration: none;
    text-transform: uppercase;
    width: 150px;
    border-left: 4px solid transparent;
    -webkit-transition: all 0.35s ease-in-out;
    -moz-transition: all 0.35s ease-in-out;
    -ms-transition: all 0.35s ease-in-out;
    transition: all 0.35s ease-in-out;
}
#cssmenu > ul ul li a:hover {
    cursor:pointer;
    border-left: 4px solid #de553b;
    background: url(images/hover.png) repeat;
    color: white;
    text-shadow: 0 1px 0 black;
}
#cssmenu > ul ul li a:active {
    background: url(images/menu-bg.png) repeat;
}



