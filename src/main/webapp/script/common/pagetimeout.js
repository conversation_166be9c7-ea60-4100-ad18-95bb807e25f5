// JavaScript Document


var c=0;
var t;
var timer_is_on=0;
var url="";
var tmpcnt=0;

function timedCount()
{
//document.getElementById('txt').value=c;
c=c-1;
t=setTimeout("timedCount()",1000);
if(c==0) 
{
	alert("your session is time out "+tmpcnt+" sec");
	clearTimeout(t);
	document.frmMnu.action=url;
	parent.location.href = url;
	document.frmMnu.submit();
	
	
}
}

function doTimer(cnt,url)
{
this.url=url;
c=cnt;
tmpcnt=c;
if (!timer_is_on)
  {
  timer_is_on=1;
  timedCount();
  }
}

function stopCount(cnt)
{
//clearTimeout(t);
timer_is_on=0;
c=cnt;
//alert("-----");
}
