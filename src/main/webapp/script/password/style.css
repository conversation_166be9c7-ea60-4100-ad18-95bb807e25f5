
/*  ADVANCED STYLES */
.top_testresult{
	font-weight: bold;
	font-size:13px;
	font-family: arail,helvetica,san-serif;
	color:#666;
	padding:0;
	margin:0 0 2px 0;
}
.top_testresult span{
	padding:6px ;
	margin:0;
}
.top_shortPass{
	background:#edabab;
	border:1px solid #bc0000;
	display:block;
	height: 20px;
	width: 158px;
}
.top_shortPass span{
	
}
.top_badPass{
	background:#edabab;
	border:1px solid #bc0000;
	display:block;
	height: 20px;
	width: 158px;
}
.top_badPass span{
	
}
.top_goodPass{
	background:#ede3ab;
	border:1px solid #bc9f00;
	display:block;
	height: 20px;
	width: 158px;
}
.top_goodPass span{

}
.top_strongPass{
	background:#d3edab;
	border:1px solid #73bc00;
	display:block;
	height: 20px;
	width: 158px;
}
.top_strongPass span{

}


/* 	RESULT STYLE  */
.testresult{
	font-weight: bold;
	font-size:13px;
	font-family: arial,helvetica,san-serif;
	color:#666;
	padding:0px 0px 12px 10px;
	margin-left:10px;
	display: block;
	height:28px;
	float:left;
}
.testresult span{
	padding:10px 20px 12px 10px;
	margin: 0px 0px 0px 20px;
	display:block;
	float:right;
	white-space: nowrap;
}
.shortPass{
	background:url(images/red.png) no-repeat 0 0;
}
.shortPass span{
	background:url(images/red.png) no-repeat top right;
}
.badPass{
	background:url(images/red.png) no-repeat 0 0;
}
.badPass span{
	background:url(images/red.png) no-repeat top right;
}
.goodPass{
	background:url(images/yellow.png) no-repeat 0 0;
}
.goodPass span{
	background:url(images/yellow.png) no-repeat top right;
}
.strongPass{
	background:url(images/green.png) no-repeat 0 0;
}
.strongPass span{
	background:url(images/green.png) no-repeat top right;
}
