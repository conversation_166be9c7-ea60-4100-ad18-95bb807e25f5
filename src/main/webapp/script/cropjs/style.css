BODY {
	PADDING-BOTTOM: 0px; BACKGROUND-COLOR: #ccc2ad; MARGIN: 0px auto; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
#main {
	POSITION: relative; WIDTH: 100%; TOP: 0px; LEFT: 0px
}
#page-background-glare {
	POSITION: absolute; WIDTH: 100%; HEIGHT: 245px; TOP: 0px; LEFT: 0px
}
#page-background-glare-image {
	BACKGROUND-IMAGE: url(../images/Page-BgGlare.png); MARGIN: 0px; WIDTH: 366px; BACKGROUND-REPEAT: no-repeat; HEIGHT: 245px
}
HTML:first-child #page-background-glare {
	BORDER-BOTTOM: transparent 1px solid; BORDER-LEFT: transparent 1px solid; BORDER-TOP: transparent 1px solid; BORDER-RIGHT: transparent 1px solid
}
#page-background-simple-gradient {
	BACKGROUND-IMAGE: url(../images/Page-BgSimpleGradient.jpg); POSITION: absolute; WIDTH: 100%; BACKGROUND-REPEAT: repeat-x; HEIGHT: 44px; TOP: 0px
}
.cleared {
	BORDER-BOTTOM-STYLE: none; PADDING-BOTTOM: 0px; BORDER-RIGHT-STYLE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; BORDER-TOP-STYLE: none; FLOAT: none; CLEAR: both; FONT-SIZE: 1px; BORDER-LEFT-STYLE: none; PADDING-TOP: 0px
}
FORM {
	PADDING-BOTTOM: 0px !important; MARGIN: 0px; PADDING-LEFT: 0px !important; PADDING-RIGHT: 0px !important; PADDING-TOP: 0px !important
}
TABLE.position {
	POSITION: relative; WIDTH: 100%; TABLE-LAYOUT: fixed
}
.Sheet {
	Z-INDEX: 0; POSITION: relative; MIN-WIDTH: 23px; MARGIN: 0px auto; MIN-HEIGHT: 23px; WIDTH: 900px
}
.Sheet-body {
	Z-INDEX: 1; POSITION: relative; PADDING-BOTTOM: 1px; PADDING-LEFT: 1px; PADDING-RIGHT: 1px; PADDING-TOP: 1px
}
.Sheet-tr {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-tl {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-br {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-bl {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-tc {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-bc {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-cr {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-cl {
	Z-INDEX: -1; POSITION: absolute
}
.Sheet-tr {
	BACKGROUND-IMAGE: url(../images/Sheet-s.png); WIDTH: 22px; HEIGHT: 22px
}
.Sheet-tl {
	BACKGROUND-IMAGE: url(../images/Sheet-s.png); WIDTH: 22px; HEIGHT: 22px
}
.Sheet-br {
	BACKGROUND-IMAGE: url(../images/Sheet-s.png); WIDTH: 22px; HEIGHT: 22px
}
.Sheet-bl {
	BACKGROUND-IMAGE: url(../images/Sheet-s.png); WIDTH: 22px; HEIGHT: 22px
}
.Sheet-tl {
	TOP: 0px; LEFT: 0px
}
.Sheet-tr {
	TOP: 0px; RIGHT: 0px
}
.Sheet-bl {
	BOTTOM: 0px; LEFT: 0px
}
.Sheet-br {
	BOTTOM: 0px; RIGHT: 0px
}
.Sheet-tc {
	BACKGROUND-IMAGE: url(../images/Sheet-h.png); HEIGHT: 22px; RIGHT: 11px; LEFT: 11px
}
.Sheet-bc {
	BACKGROUND-IMAGE: url(../images/Sheet-h.png); HEIGHT: 22px; RIGHT: 11px; LEFT: 11px
}
.Sheet-tc {
	TOP: 0px
}
.Sheet-bc {
	BOTTOM: 0px
}
.Sheet-cr {
	BACKGROUND-IMAGE: url(../images/Sheet-v.png); WIDTH: 22px; BOTTOM: 11px; TOP: 11px
}
.Sheet-cl {
	BACKGROUND-IMAGE: url(../images/Sheet-v.png); WIDTH: 22px; BOTTOM: 11px; TOP: 11px
}
.Sheet-cr {
	RIGHT: 0px
}
.Sheet-cl {
	LEFT: 0px
}
.Sheet-cc {
	Z-INDEX: -1; POSITION: absolute; BACKGROUND-COLOR: #fbfaf9; BOTTOM: 11px; TOP: 11px; RIGHT: 11px; LEFT: 11px
}
.Sheet {
	MARGIN-TOP: 10px !important
}
#page-background-simple-gradient {
	MIN-WIDTH: 900px
}
#page-background-gradient {
	MIN-WIDTH: 900px
}
#page-background-glare {
	MIN-WIDTH: 900px
}
.menu A {
	TEXT-ALIGN: left; OUTLINE-STYLE: none; LETTER-SPACING: normal; WORD-SPACING: normal; TEXT-DECORATION: none
}
.menu A:link {
	TEXT-ALIGN: left; OUTLINE-STYLE: none; LETTER-SPACING: normal; WORD-SPACING: normal; TEXT-DECORATION: none
}
.menu A:visited {
	TEXT-ALIGN: left; OUTLINE-STYLE: none; LETTER-SPACING: normal; WORD-SPACING: normal; TEXT-DECORATION: none
}
.menu A:hover {
	TEXT-ALIGN: left; OUTLINE-STYLE: none; LETTER-SPACING: normal; WORD-SPACING: normal; TEXT-DECORATION: none
}
.menu {
	PADDING-BOTTOM: 0px; BORDER-RIGHT-WIDTH: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; DISPLAY: block; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 0px
}
.menu UL {
	PADDING-BOTTOM: 0px; BORDER-RIGHT-WIDTH: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; DISPLAY: block; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 0px
}
.menu LI {
	Z-INDEX: 5; POSITION: relative; PADDING-BOTTOM: 0px; BORDER-RIGHT-WIDTH: 0px; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; DISPLAY: block; BACKGROUND: none transparent scroll repeat 0% 0%; FLOAT: left; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 0px
}
.menu LI:hover {
	Z-INDEX: 10000; WHITE-SPACE: normal
}
.menu LI LI {
	FLOAT: none
}
.menu UL {
	Z-INDEX: 10; POSITION: absolute; BACKGROUND: none transparent scroll repeat 0% 0%; VISIBILITY: hidden; TOP: 0px; LEFT: 0px
}
.menu LI:hover > UL {
	VISIBILITY: visible; TOP: 100%
}
.menu LI LI:hover > UL {
	TOP: 0px; LEFT: 100%
}
.menu:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; OVERFLOW: hidden; CONTENT: "."
}
.menu UL:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; OVERFLOW: hidden; CONTENT: "."
}
.menu {
	MIN-HEIGHT: 0px
}
.menu UL {
	MIN-HEIGHT: 0px
}
.menu UL {
	BACKGROUND-IMAGE: url(../images/spacer.gif); PADDING-BOTTOM: 30px; MARGIN: -10px 0px 0px -30px; PADDING-LEFT: 30px; PADDING-RIGHT: 30px; PADDING-TOP: 10px
}
.menu UL UL {
	PADDING-BOTTOM: 30px; MARGIN: -30px 0px 0px -10px; PADDING-LEFT: 10px; PADDING-RIGHT: 30px; PADDING-TOP: 30px
}
.menu {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 10px; PADDING-RIGHT: 10px; PADDING-TOP: 8px
}
.nav {
	Z-INDEX: 100; POSITION: relative; HEIGHT: 33px
}
.nav .l {
	BACKGROUND-IMAGE: url(../images/nav.png); Z-INDEX: -1; POSITION: absolute; HEIGHT: 33px; TOP: 0px
}
.nav .r {
	BACKGROUND-IMAGE: url(../images/nav.png); Z-INDEX: -1; POSITION: absolute; HEIGHT: 33px; TOP: 0px
}
.nav .l {
	RIGHT: 10px; LEFT: 0px
}
.nav .r {
	WIDTH: 898px; RIGHT: 0px
}
.menu UL LI {
	CLEAR: both
}
.menu A {
	POSITION: relative; DISPLAY: block; HEIGHT: 25px; MARGIN-LEFT: 3px; OVERFLOW: hidden; CURSOR: pointer; MARGIN-RIGHT: 3px; TEXT-DECORATION: none
}
.menu A .r {
	BACKGROUND-IMAGE: url(../images/MenuItem.png); Z-INDEX: -1; POSITION: absolute; DISPLAY: block; HEIGHT: 75px; TOP: 0px
}
.menu A .l {
	BACKGROUND-IMAGE: url(../images/MenuItem.png); Z-INDEX: -1; POSITION: absolute; DISPLAY: block; HEIGHT: 75px; TOP: 0px
}
.menu A .l {
	RIGHT: 3px; LEFT: 0px
}
.menu A .r {
	WIDTH: 406px; RIGHT: 0px
}
.menu A .t {
	TEXT-ALIGN: center; PADDING-BOTTOM: 0px; LINE-HEIGHT: 25px; TEXT-TRANSFORM: uppercase; FONT-STYLE: normal; MARGIN: 0px 3px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #1f1c14; FONT-SIZE: 11px; FONT-WEIGHT: bold; PADDING-TOP: 0px
}
.menu A:hover .l {
	TOP: -25px
}
.menu A:hover .r {
	TOP: -25px
}
.menu LI:hover > A .l {
	TOP: -25px
}
.menu LI:hover > A .r {
	TOP: -25px
}
.menu LI:hover A .l {
	TOP: -25px
}
.menu LI:hover A .r {
	TOP: -25px
}
.menu A:hover .t {
	COLOR: #f7f6f2
}
.menu LI:hover A .t {
	COLOR: #f7f6f2
}
.menu LI:hover > A .t {
	COLOR: #f7f6f2
}
.menu A.active .l {
	TOP: -50px
}
.menu A.active .r {
	TOP: -50px
}
.menu A.active .t {
	COLOR: #64583f
}
.menu UL A {
	BACKGROUND-IMAGE: url(../images/subitem-bg.png); BORDER-BOTTOM: 0px solid; TEXT-ALIGN: center; BORDER-LEFT: 0px solid; LINE-HEIGHT: 20px; WIDTH: 180px; DISPLAY: block; BACKGROUND-REPEAT: repeat-x; WHITE-SPACE: nowrap; BACKGROUND-POSITION: left top; HEIGHT: 20px; OVERFLOW: hidden; BORDER-TOP: 0px solid; MARGIN-RIGHT: auto; BORDER-RIGHT: 0px solid
}
.nav UL.menu UL SPAN {
	BACKGROUND-IMAGE: none; DISPLAY: inline; FLOAT: none
}
.nav UL.menu UL SPAN SPAN {
	BACKGROUND-IMAGE: none; DISPLAY: inline; FLOAT: none
}
.menu UL A {
	TEXT-ALIGN: left; LINE-HEIGHT: 20px; TEXT-INDENT: 12px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #000000; FONT-SIZE: 11px; TEXT-DECORATION: none
}
.menu UL A:link {
	TEXT-ALIGN: left; LINE-HEIGHT: 20px; TEXT-INDENT: 12px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #000000; FONT-SIZE: 11px; TEXT-DECORATION: none
}
.menu UL A:visited {
	TEXT-ALIGN: left; LINE-HEIGHT: 20px; TEXT-INDENT: 12px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #000000; FONT-SIZE: 11px; TEXT-DECORATION: none
}
.menu UL A:hover {
	TEXT-ALIGN: left; LINE-HEIGHT: 20px; TEXT-INDENT: 12px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #000000; FONT-SIZE: 11px; TEXT-DECORATION: none
}
.menu UL A:active {
	TEXT-ALIGN: left; LINE-HEIGHT: 20px; TEXT-INDENT: 12px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #000000; FONT-SIZE: 11px; TEXT-DECORATION: none
}
.nav UL.menu UL SPAN {
	TEXT-ALIGN: left; LINE-HEIGHT: 20px; TEXT-INDENT: 12px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #000000; FONT-SIZE: 11px; TEXT-DECORATION: none
}
.nav UL.menu UL SPAN SPAN {
	TEXT-ALIGN: left; LINE-HEIGHT: 20px; TEXT-INDENT: 12px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #000000; FONT-SIZE: 11px; TEXT-DECORATION: none
}
.menu UL UL A {
	MARGIN-LEFT: auto
}
.menu UL LI A:hover {
	BACKGROUND-POSITION: 0px -20px; COLOR: #000000
}
.menu UL LI:hover > A {
	BACKGROUND-POSITION: 0px -20px; COLOR: #000000
}
.nav .menu UL LI A:hover SPAN {
	COLOR: #000000
}
.nav .menu UL LI A:hover SPAN SPAN {
	COLOR: #000000
}
.nav .menu UL LI:hover > A SPAN {
	COLOR: #000000
}
.nav .menu UL LI:hover > A SPAN SPAN {
	COLOR: #000000
}
.contentLayout {
	POSITION: relative; WIDTH: 898px; MARGIN-BOTTOM: 0px
}
.Block {
	Z-INDEX: 0; POSITION: relative; MIN-WIDTH: 1px; MARGIN: 0px auto; MIN-HEIGHT: 1px
}
.Block-body {
	Z-INDEX: 1; POSITION: relative; PADDING-BOTTOM: 7px; PADDING-LEFT: 7px; PADDING-RIGHT: 7px; PADDING-TOP: 7px
}
.Block {
	MARGIN: 7px
}
.BlockContent {
	Z-INDEX: 0; POSITION: relative; MIN-WIDTH: 1px; MARGIN: 0px auto; MIN-HEIGHT: 1px
}
.BlockContent-body {
	Z-INDEX: 1; POSITION: relative; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
.BlockContent-body {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #7d6f4f; FONT-SIZE: 11px
}
.BlockContent-body A:link {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #a66c07; TEXT-DECORATION: underline
}
.BlockContent-body A:visited {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #9b8b87; TEXT-DECORATION: none
}
.BlockContent-body A.visited {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #9b8b87; TEXT-DECORATION: none
}
.BlockContent-body A:hover {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #dc8f09; TEXT-DECORATION: none
}
.BlockContent-body A.hover {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #dc8f09; TEXT-DECORATION: none
}
.BlockContent-body UL {
	PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; COLOR: #322c1f; PADDING-TOP: 0px
}
.BlockContent-body LI {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; MARGIN-LEFT: 12px; FONT-SIZE: 11px
}
.BlockContent-body UL LI {
	BACKGROUND-IMAGE: url(../images/BlockContentBullets.png); PADDING-BOTTOM: 2px; LINE-HEIGHT: 1.2em; MARGIN: 0.5em 0px; PADDING-LEFT: 22px; PADDING-RIGHT: 0px; BACKGROUND-REPEAT: no-repeat; PADDING-TOP: 2px
}
.Post {
	Z-INDEX: 0; POSITION: relative; MIN-WIDTH: 1px; MARGIN: 0px auto; MIN-HEIGHT: 1px
}
.Post-body {
	Z-INDEX: 1; POSITION: relative; PADDING-BOTTOM: 15px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; PADDING-TOP: 15px
}
.Post {
	MARGIN: 7px
}
A IMG {
	BORDER-RIGHT-WIDTH: 0px; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px
}
.article IMG {
	BORDER-BOTTOM: #ccc2ad 1px solid; BORDER-LEFT: #ccc2ad 1px solid; MARGIN: 1em; BORDER-TOP: #ccc2ad 1px solid; BORDER-RIGHT: #ccc2ad 1px solid
}
IMG.article {
	BORDER-BOTTOM: #ccc2ad 1px solid; BORDER-LEFT: #ccc2ad 1px solid; MARGIN: 1em; BORDER-TOP: #ccc2ad 1px solid; BORDER-RIGHT: #ccc2ad 1px solid
}
.metadata-icons IMG {
	BORDER-BOTTOM-STYLE: none; BORDER-RIGHT-STYLE: none; MARGIN: 2px; BORDER-TOP-STYLE: none; VERTICAL-ALIGN: middle; BORDER-LEFT-STYLE: none
}
.article TABLE {
	MARGIN: 1px; WIDTH: auto; BORDER-COLLAPSE: collapse
}
TABLE.article {
	MARGIN: 1px; WIDTH: auto; BORDER-COLLAPSE: collapse
}
.article TABLE {
	BACKGROUND-COLOR: transparent
}
TABLE.article .article TR {
	BACKGROUND-COLOR: transparent
}
.article TH {
	BACKGROUND-COLOR: transparent
}
.article TD {
	BACKGROUND-COLOR: transparent
}
.article TH {
	BORDER-BOTTOM: #978582 1px solid; TEXT-ALIGN: left; BORDER-LEFT: #978582 1px solid; PADDING-BOTTOM: 2px; PADDING-LEFT: 2px; PADDING-RIGHT: 2px; VERTICAL-ALIGN: top; BORDER-TOP: #978582 1px solid; BORDER-RIGHT: #978582 1px solid; PADDING-TOP: 2px
}
.article TD {
	BORDER-BOTTOM: #978582 1px solid; TEXT-ALIGN: left; BORDER-LEFT: #978582 1px solid; PADDING-BOTTOM: 2px; PADDING-LEFT: 2px; PADDING-RIGHT: 2px; VERTICAL-ALIGN: top; BORDER-TOP: #978582 1px solid; BORDER-RIGHT: #978582 1px solid; PADDING-TOP: 2px
}
.article TH {
	TEXT-ALIGN: center; PADDING-BOTTOM: 7px; PADDING-LEFT: 7px; PADDING-RIGHT: 7px; VERTICAL-ALIGN: middle; PADDING-TOP: 7px
}
PRE {
	PADDING-BOTTOM: 0.1em; PADDING-LEFT: 0.1em; PADDING-RIGHT: 0.1em; OVERFLOW: auto; PADDING-TOP: 0.1em
}
.PostHeader {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; TEXT-TRANSFORM: none; FONT-VARIANT: normal; FONT-STYLE: normal; TEXT-INDENT: 0px; MARGIN: 12px 0px 8px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #9d6607; FONT-SIZE: 18px; FONT-WEIGHT: bold; WORD-SPACING: normal; TEXT-DECORATION: none; PADDING-TOP: 0px
}
.PostHeader A {
	TEXT-ALIGN: left; FONT-STYLE: normal; MARGIN: 0px; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #9d6607; FONT-SIZE: 18px; FONT-WEIGHT: bold
}
.PostHeader A:link {
	TEXT-ALIGN: left; FONT-STYLE: normal; MARGIN: 0px; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #9d6607; FONT-SIZE: 18px; FONT-WEIGHT: bold
}
.PostHeader A:visited {
	TEXT-ALIGN: left; FONT-STYLE: normal; MARGIN: 0px; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #9d6607; FONT-SIZE: 18px; FONT-WEIGHT: bold
}
.PostHeader A:hover {
	TEXT-ALIGN: left; FONT-STYLE: normal; MARGIN: 0px; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #9d6607; FONT-SIZE: 18px; FONT-WEIGHT: bold
}
.PostHeader A:link {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; COLOR: #ba7908; TEXT-DECORATION: none
}
.PostHeader A:visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; COLOR: #514643; TEXT-DECORATION: none
}
.PostHeader A.visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; COLOR: #514643; TEXT-DECORATION: none
}
.PostHeader A:hover {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; COLOR: #a66c07; TEXT-DECORATION: underline
}
.PostHeader A.hovered {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; COLOR: #a66c07; TEXT-DECORATION: underline
}
BODY {
	FONT-STYLE: normal; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #7d6f4f; FONT-SIZE: 11px; FONT-WEIGHT: normal
}
.PostContent P {
	MARGIN: 0.5em 0px
}
.PostContent {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #7d6f4f
}
.PostContent P {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #7d6f4f
}
.PostContent {
	MARGIN: 0px
}
.PostContent TABLE {
	TEXT-ALIGN: center
}
.PostContent TH {
	BACKGROUND-COLOR: #c0c0c0; COLOR: #fff
}
A {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #dc8f09; TEXT-DECORATION: underline
}
A:link {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #dc8f09; TEXT-DECORATION: underline
}
A:visited {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #5f514f; TEXT-DECORATION: underline
}
A.visited {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #5f514f; TEXT-DECORATION: underline
}
A:hover {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #a66c07; TEXT-DECORATION: none
}
A.hover {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #a66c07; TEXT-DECORATION: none
}
H1 {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H2 {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H3 {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H4 {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H5 {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H6 {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H1 A {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H2 A {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H3 A {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H4 A {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H5 A {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H6 A H1 A:hover {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H2 A:hover {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H3 A:hover {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H4 A:hover {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H5 A:hover {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H6 A:hover H1 A:visited {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H2 A:visited {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H3 A:visited {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H4 A:visited {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H5 A:visited {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H6 A:visited {
	FONT-STYLE: normal; FONT-WEIGHT: normal; TEXT-DECORATION: none
}
H1 {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 3px; COLOR: #6b5e43; FONT-SIZE: 20px
}
H1 A {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 3px; COLOR: #6b5e43; FONT-SIZE: 20px
}
H1 A:link {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 3px; COLOR: #6b5e43; FONT-SIZE: 20px
}
H1 A:visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 3px; COLOR: #6b5e43; FONT-SIZE: 20px
}
H1 A:hover {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 3px; COLOR: #6b5e43; FONT-SIZE: 20px
}
H2 {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #8d7c58; FONT-SIZE: 18px
}
H2 A {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #8d7c58; FONT-SIZE: 18px
}
H2 A:link {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #8d7c58; FONT-SIZE: 18px
}
H2 A:visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #8d7c58; FONT-SIZE: 18px
}
H2 A:hover {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #8d7c58; FONT-SIZE: 18px
}
H3 {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6c68; FONT-SIZE: 16px
}
H3 A {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6c68; FONT-SIZE: 16px
}
H3 A:link {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6c68; FONT-SIZE: 16px
}
H3 A:visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6c68; FONT-SIZE: 16px
}
H3 A:hover {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6c68; FONT-SIZE: 16px
}
H4 {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 13px
}
H4 A {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 13px
}
H4 A:link {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 13px
}
H4 A:visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 13px
}
H4 A:hover {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 13px
}
H5 {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H5 A {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H5 A:link {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H5 A:visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H5 A:hover {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H6 {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H6 A {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H6 A:link {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H6 A:visited {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
H6 A:hover {
	TEXT-ALIGN: left; FONT-FAMILY: Verdana, Geneva, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 2px; COLOR: #7d6f4f; FONT-SIZE: 11px
}
OL {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; MARGIN: 1em 0px 1em 2em; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #191610; FONT-SIZE: 11px; PADDING-TOP: 0px
}
UL {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; MARGIN: 1em 0px 1em 2em; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #191610; FONT-SIZE: 11px; PADDING-TOP: 0px
}
LI OL {
	PADDING-BOTTOM: 0px; MARGIN: 0.5em 0px 0.5em 2em; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
LI UL {
	PADDING-BOTTOM: 0px; MARGIN: 0.5em 0px 0.5em 2em; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
LI {
	PADDING-BOTTOM: 0px; MARGIN: 0.2em 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
UL {
	LIST-STYLE-TYPE: none
}
OL {
	LIST-STYLE-POSITION: inside
}
.Post LI {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 1.2em; PADDING-LEFT: 11px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
.Post OL LI {
	PADDING-LEFT: 0px; BACKGROUND: none transparent scroll repeat 0% 0%
}
.Post UL OL LI {
	PADDING-LEFT: 0px; BACKGROUND: none transparent scroll repeat 0% 0%
}
.Post UL LI {
	BACKGROUND-IMAGE: url(../images/PostBullets.png); PADDING-LEFT: 11px; BACKGROUND-REPEAT: no-repeat
}
.Post OL UL LI {
	BACKGROUND-IMAGE: url(../images/PostBullets.png); PADDING-LEFT: 11px; BACKGROUND-REPEAT: no-repeat
}
BLOCKQUOTE {
	TEXT-ALIGN: left; FONT-STYLE: italic; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #13110c; FONT-WEIGHT: normal
}
BLOCKQUOTE P {
	TEXT-ALIGN: left; FONT-STYLE: italic; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #13110c; FONT-WEIGHT: normal
}
.PostContent BLOCKQUOTE P {
	TEXT-ALIGN: left; FONT-STYLE: italic; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #13110c; FONT-WEIGHT: normal
}
BLOCKQUOTE {
	BACKGROUND-IMAGE: url(../images/PostQuote.png); PADDING-BOTTOM: 5px; BACKGROUND-COLOR: #e4ded3; MARGIN: 10px 10px 10px 50px; PADDING-LEFT: 32px; PADDING-RIGHT: 5px; BACKGROUND-REPEAT: no-repeat; BACKGROUND-POSITION: left top; PADDING-TOP: 5px
}
.PostContent BLOCKQUOTE {
	BACKGROUND-IMAGE: url(../images/PostQuote.png); PADDING-BOTTOM: 5px; BACKGROUND-COLOR: #e4ded3; MARGIN: 10px 10px 10px 50px; PADDING-LEFT: 32px; PADDING-RIGHT: 5px; BACKGROUND-REPEAT: no-repeat; BACKGROUND-POSITION: left top; PADDING-TOP: 5px
}
.button-wrapper .button {
	Z-INDEX: 0; BORDER-BOTTOM-STYLE: none; PADDING-BOTTOM: 0px !important; LINE-HEIGHT: 25px; BORDER-RIGHT-STYLE: none; MARGIN: 0px; OUTLINE-STYLE: none; PADDING-LEFT: 0px !important; WIDTH: auto; PADDING-RIGHT: 0px !important; DISPLAY: inline-block; BORDER-TOP-STYLE: none; BACKGROUND: none transparent scroll repeat 0% 0%; BORDER-LEFT-STYLE: none; OVERFLOW: visible; CURSOR: default; TEXT-DECORATION: none !important; PADDING-TOP: 0px !important
}
.button-wrapper {
	Z-INDEX: 0; POSITION: relative; WIDTH: auto; DISPLAY: inline-block; WHITE-SPACE: nowrap; HEIGHT: 25px; OVERFLOW: hidden
}
.firefox2 .button-wrapper {
	DISPLAY: block; FLOAT: left
}
.button-wrapper .button {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px !important; LINE-HEIGHT: 25px; FONT-STYLE: normal; PADDING-LEFT: 10px !important; PADDING-RIGHT: 10px !important; DISPLAY: block; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; WHITE-SPACE: nowrap; LETTER-SPACING: 1px; HEIGHT: 25px; COLOR: #000000 !important; FONT-SIZE: 11px; FONT-WEIGHT: bold; TEXT-DECORATION: none !important; PADDING-TOP: 0px !important
}
INPUT {
	FONT-STYLE: normal; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; FONT-SIZE: 11px; FONT-WEIGHT: bold
}
SELECT {
	FONT-STYLE: normal; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; FONT-SIZE: 11px; FONT-WEIGHT: bold
}
.hover.button-wrapper .button {
	COLOR: #f3f1ec !important; TEXT-DECORATION: none !important
}
.button:hover {
	COLOR: #f3f1ec !important; TEXT-DECORATION: none !important
}
.active.button-wrapper .button {
	COLOR: #f6f4f4 !important
}
.button-wrapper .l {
	BACKGROUND-IMAGE: url(../images/Button.png); Z-INDEX: -1; POSITION: absolute; DISPLAY: block; HEIGHT: 75px
}
.button-wrapper .r {
	BACKGROUND-IMAGE: url(../images/Button.png); Z-INDEX: -1; POSITION: absolute; DISPLAY: block; HEIGHT: 75px
}
.button-wrapper .l {
	RIGHT: 1px; LEFT: 0px
}
.button-wrapper .r {
	WIDTH: 403px; RIGHT: 0px
}
.hover.button-wrapper .l {
	TOP: -25px
}
.hover.button-wrapper .r {
	TOP: -25px
}
.active.button-wrapper .l {
	TOP: -50px
}
.active.button-wrapper .r {
	TOP: -50px
}
.Footer {
	Z-INDEX: 0; POSITION: relative; MARGIN: 5px auto 0px; WIDTH: 898px; OVERFLOW: hidden
}
.Footer .Footer-inner {
	Z-INDEX: 0; POSITION: relative; TEXT-ALIGN: center; PADDING-BOTTOM: 8px; PADDING-LEFT: 8px; PADDING-RIGHT: 8px; HEIGHT: 1%; PADDING-TOP: 8px
}
.Footer .Footer-background {
	BACKGROUND-IMAGE: url(../images/Footer.png); Z-INDEX: -1; POSITION: absolute; WIDTH: 898px; BOTTOM: 0px; BACKGROUND-REPEAT: no-repeat; HEIGHT: 150px; LEFT: 0px
}
.Footer .Footer-text P {
	MARGIN: 0px
}
.Footer .Footer-text {
	DISPLAY: inline-block; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; LETTER-SPACING: 1px; COLOR: #271902; FONT-SIZE: 10px
}
.Footer .Footer-text A:link {
	FONT-STYLE: normal; FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #624004; FONT-WEIGHT: bold; TEXT-DECORATION: none
}
.Footer .Footer-text A:visited {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #534846; TEXT-DECORATION: none
}
.Footer .Footer-text A:hover {
	FONT-FAMILY: Tahoma, Arial, Helvetica, Sans-Serif; COLOR: #312002; TEXT-DECORATION: underline
}
.page-footer {
	FONT-STYLE: normal; FONT-FAMILY: Arial; LETTER-SPACING: normal; COLOR: #7d6f4f; FONT-SIZE: 8px; FONT-WEIGHT: normal; WORD-SPACING: normal; TEXT-DECORATION: underline
}
.page-footer A {
	FONT-STYLE: normal; FONT-FAMILY: Arial; LETTER-SPACING: normal; COLOR: #7d6f4f; FONT-SIZE: 8px; FONT-WEIGHT: normal; WORD-SPACING: normal; TEXT-DECORATION: underline
}
.page-footer A:link {
	FONT-STYLE: normal; FONT-FAMILY: Arial; LETTER-SPACING: normal; COLOR: #7d6f4f; FONT-SIZE: 8px; FONT-WEIGHT: normal; WORD-SPACING: normal; TEXT-DECORATION: underline
}
.page-footer A:visited {
	FONT-STYLE: normal; FONT-FAMILY: Arial; LETTER-SPACING: normal; COLOR: #7d6f4f; FONT-SIZE: 8px; FONT-WEIGHT: normal; WORD-SPACING: normal; TEXT-DECORATION: underline
}
.page-footer A:hover {
	FONT-STYLE: normal; FONT-FAMILY: Arial; LETTER-SPACING: normal; COLOR: #7d6f4f; FONT-SIZE: 8px; FONT-WEIGHT: normal; WORD-SPACING: normal; TEXT-DECORATION: underline
}
.page-footer {
	TEXT-ALIGN: center; MARGIN: 1em; COLOR: #6f605d; TEXT-DECORATION: none
}
.contentLayout .content {
	POSITION: relative; PADDING-BOTTOM: 0px; BORDER-RIGHT-WIDTH: 0px; MARGIN: 0px; PADDING-LEFT: 0px; WIDTH: 897px; PADDING-RIGHT: 0px; FLOAT: left; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; OVERFLOW: hidden; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 0px
}
#crop_container {
	FLOAT: left
}
#crop_container2 {
	FLOAT: left
}
.result {
	BORDER-BOTTOM: #333 1px solid; BORDER-LEFT: #333 1px solid; MARGIN: 0px 0px 0px 10px; WIDTH: 400px; FLOAT: left; HEIGHT: 300px; BORDER-TOP: #333 1px solid; BORDER-RIGHT: #333 1px solid
}
.result2 {
	BORDER-BOTTOM: #333 1px solid; BORDER-LEFT: #333 1px solid; MARGIN: 0px 0px 0px 10px; WIDTH: 400px; FLOAT: left; HEIGHT: 300px; BORDER-TOP: #333 1px solid; BORDER-RIGHT: #333 1px solid
}
.txt {
	MARGIN: 75px auto 0px; WIDTH: 230px
}
CODE {
	FONT-FAMILY: monospace; COLOR: #808080; FONT-SIZE: 12px
}
PRE {
	FONT-FAMILY: monospace; COLOR: #808080; FONT-SIZE: 12px
}
INS {
	MARGIN: 10px 0px 0px 10px
}
