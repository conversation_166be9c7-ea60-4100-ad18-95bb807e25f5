.vertical#zoomMin {
	TEXT-ALIGN: center; MARGIN: auto; WIDTH: 100%; FONT: 700 14px Arial; COLOR: #000
}
.vertical#zoomMax {
	TEXT-ALIGN: center; MARGIN: auto; WIDTH: 100%; FONT: 700 14px Arial; COLOR: #000
}
.vertical#zoomSlider {
	POSITION: relative; MARGIN: 7px auto; WIDTH: 6px; HEIGHT: 150px
}
.vertical#zoomContainer {
	Z-INDEX: 3; BACKGROUND-COLOR: #fff; WIDTH: 31px; HEIGHT: 200px
}
.vertical#rotationMin {
	TEXT-ALIGN: center; MARGIN: auto; WIDTH: 100%; FONT: 700 14px Arial; COLOR: #000
}
.vertical#rotationMax {
	TEXT-ALIGN: center; MARGIN: auto; WIDTH: 100%; FONT: 700 14px Arial; COLOR: #000
}
.vertical#rotationSlider {
	POSITION: relative; MARGIN: 7px auto; WIDTH: 6px; HEIGHT: 150px
}
.vertical#rotationContainer {
	Z-INDEX: 3; BACKGROUND-COLOR: #fff; WIDTH: 31px; HEIGHT: 200px
}
.horizontal#zoomMin {
	TEXT-ALIGN: center; PADDING-BOTTOM: 0px; MARGIN: 2px 3px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; FONT: 300 12px Arial; FLOAT: left; COLOR: #000; PADDING-TOP: 0px
}
.horizontal#zoomMax {
	TEXT-ALIGN: center; PADDING-BOTTOM: 0px; MARGIN: 2px 3px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; FONT: 300 12px Arial; FLOAT: left; COLOR: #000; PADDING-TOP: 0px
}
.horizontal#zoomSlider {
	POSITION: relative; MARGIN: 7px auto; WIDTH: 300px; FLOAT: left; HEIGHT: 6px
}
.horizontal#zoomContainer {
	Z-INDEX: 3; BACKGROUND-COLOR: #fff; WIDTH: auto
}
.horizontal#rotationMin {
	TEXT-ALIGN: center; MARGIN: 2px 4px; FONT: 300 12px Arial; FLOAT: left; COLOR: #000
}
.horizontal#rotationMax {
	TEXT-ALIGN: center; MARGIN: 2px 4px; FONT: 300 12px Arial; FLOAT: left; COLOR: #000
}
.horizontal#rotationSlider {
	POSITION: relative; MARGIN: 7px 5px; WIDTH: 300px; FLOAT: left; HEIGHT: 6px
}
.horizontal#rotationContainer {
	Z-INDEX: 3; BACKGROUND-COLOR: #fff; WIDTH: auto
}
.mvn {
	WIDTH: 21px; BACKGROUND: url(../images/movement.png) no-repeat; HEIGHT: 21px
}
.mvn:hover {
	CURSOR: pointer
}
.mvn_no {
	BACKGROUND-POSITION: 0px 0px
}
.mvn_n {
	BACKGROUND-POSITION: 0px -21px
}
.mvn_ne {
	BACKGROUND-POSITION: 0px -42px
}
.mvn_o {
	BACKGROUND-POSITION: 0px -63px
}
.mvn_c {
	
}
.mvn_e {
	BACKGROUND-POSITION: 0px -84px
}
.mvn_so {
	BACKGROUND-POSITION: 0px -105px
}
.mvn_s {
	BACKGROUND-POSITION: 0px -126px
}
.mvn_se {
	BACKGROUND-POSITION: 0px -147px
}
.mvn_no:hover {
	BACKGROUND-POSITION: 100% 0px
}
.mvn_n:hover {
	BACKGROUND-POSITION: 100% -21px
}
.mvn_ne:hover {
	BACKGROUND-POSITION: 100% -42px
}
.mvn_o:hover {
	BACKGROUND-POSITION: 100% -63px
}
.mvn_c:hover {
	
}
.mvn_e:hover {
	BACKGROUND-POSITION: 100% -84px
}
.mvn_so:hover {
	BACKGROUND-POSITION: 100% -105px
}
.mvn_s:hover {
	BACKGROUND-POSITION: 100% -126px
}
.mvn_se:hover {
	BACKGROUND-POSITION: 100% -147px
}
