.ui-helper-hidden {
	DISPLAY: none
	}
.ui-helper-hidden-accessible {
	POSITION: absolute; LEFT: -1342177.28px
	}
.ui-helper-reset {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 1.3; BORDER-RIGHT-WIDTH: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; OUTLINE-WIDTH: 0px; PADDING-RIGHT: 0px; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; FONT-SIZE: 100%; BORDER-LEFT-WIDTH: 0px; TEXT-DECORATION: none; PADDING-TOP: 0px
	}
.ui-helper-clearfix:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; CONTENT: "."
	}
.ui-helper-clearfix {
	DISPLAY: inline-block
	}
* HTML .ui-helper-clearfix {
	HEIGHT: 1%
	}
.ui-helper-clearfix {
	DISPLAY: block
	}
.ui-helper-zfix {
	POSITION: absolute; FILTER: Alpha(Opacity=0); WIDTH: 100%; HEIGHT: 100%; TOP: 0px; LEFT: 0px; opacity: 0
	}
.ui-state-disabled {
	CURSOR: default !important
	}
.ui-icon {
	TEXT-INDENT: -99999px; DISPLAY: block; BACKGROUND-REPEAT: no-repeat; OVERFLOW: hidden
	}
.ui-widget-overlay {
	POSITION: absolute; WIDTH: 100%; HEIGHT: 100%; TOP: 0px; LEFT: 0px
	}
.ui-widget {
	FONT-FAMILY: Segoe UI, Arial, sans-serif; FONT-SIZE: 1.1em
	}
.ui-widget INPUT {
	FONT-FAMILY: Segoe UI, Arial, sans-serif; FONT-SIZE: 1em
	}
.ui-widget SELECT {
	FONT-FAMILY: Segoe UI, Arial, sans-serif; FONT-SIZE: 1em
	}
.ui-widget TEXTAREA {
	FONT-FAMILY: Segoe UI, Arial, sans-serif; FONT-SIZE: 1em
	}
.ui-widget BUTTON {
	FONT-FAMILY: Segoe UI, Arial, sans-serif; FONT-SIZE: 1em
	}
.ui-widget-content {
	BORDER-BOTTOM: #8e846b 1px solid; BORDER-LEFT: #8e846b 1px solid; BACKGROUND: url(images/ui-bg_highlight-soft_100_feeebd_1x100.png) #feeebd repeat-x 50% top; COLOR: #383838; BORDER-TOP: #8e846b 1px solid; BORDER-RIGHT: #8e846b 1px solid
	}
.ui-widget-content A {
	COLOR: #383838
	}
.ui-widget-header {
	BORDER-BOTTOM: #494437 1px solid; BORDER-LEFT: #494437 1px solid; BACKGROUND: url(images/ui-bg_gloss-wave_45_817865_500x100.png) #817865 repeat-x 50% 50%; COLOR: #ffffff; BORDER-TOP: #494437 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #494437 1px solid
	}
.ui-widget-header A {
	COLOR: #ffffff
	}
.ui-state-default {
	BORDER-BOTTOM: #d19405 1px solid; BORDER-LEFT: #d19405 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_gloss-wave_60_fece2f_500x100.png) #fece2f repeat-x 50% 50%; COLOR: #4c3000; BORDER-TOP: #d19405 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #d19405 1px solid
	}
.ui-widget-content .ui-state-default {
	BORDER-BOTTOM: #d19405 1px solid; BORDER-LEFT: #d19405 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_gloss-wave_60_fece2f_500x100.png) #fece2f repeat-x 50% 50%; COLOR: #4c3000; BORDER-TOP: #d19405 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #d19405 1px solid
	}
.ui-state-default A {
	OUTLINE-STYLE: none; COLOR: #4c3000; TEXT-DECORATION: none
	}
.ui-state-default A:link {
	OUTLINE-STYLE: none; COLOR: #4c3000; TEXT-DECORATION: none
	}
.ui-state-default A:visited {
	OUTLINE-STYLE: none; COLOR: #4c3000; TEXT-DECORATION: none
	}
.ui-state-hover {
	BORDER-BOTTOM: #a45b13 1px solid; BORDER-LEFT: #a45b13 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_gloss-wave_70_ffdd57_500x100.png) #ffdd57 repeat-x 50% 50%; COLOR: #381f00; BORDER-TOP: #a45b13 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #a45b13 1px solid
	}
.ui-widget-content .ui-state-hover {
	BORDER-BOTTOM: #a45b13 1px solid; BORDER-LEFT: #a45b13 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_gloss-wave_70_ffdd57_500x100.png) #ffdd57 repeat-x 50% 50%; COLOR: #381f00; BORDER-TOP: #a45b13 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #a45b13 1px solid
	}
.ui-state-focus {
	BORDER-BOTTOM: #a45b13 1px solid; BORDER-LEFT: #a45b13 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_gloss-wave_70_ffdd57_500x100.png) #ffdd57 repeat-x 50% 50%; COLOR: #381f00; BORDER-TOP: #a45b13 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #a45b13 1px solid
	}
.ui-widget-content .ui-state-focus {
	BORDER-BOTTOM: #a45b13 1px solid; BORDER-LEFT: #a45b13 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_gloss-wave_70_ffdd57_500x100.png) #ffdd57 repeat-x 50% 50%; COLOR: #381f00; BORDER-TOP: #a45b13 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #a45b13 1px solid
	}
.ui-state-hover A {
	OUTLINE-STYLE: none; COLOR: #381f00; TEXT-DECORATION: none
	}
.ui-state-hover A:hover {
	OUTLINE-STYLE: none; COLOR: #381f00; TEXT-DECORATION: none
	}
.ui-state-active {
	BORDER-BOTTOM: #655e4e 1px solid; BORDER-LEFT: #655e4e 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_inset-soft_30_ffffff_1x100.png) #ffffff repeat-x 50% 50%; COLOR: #0074c7; BORDER-TOP: #655e4e 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #655e4e 1px solid
	}
.ui-widget-content .ui-state-active {
	BORDER-BOTTOM: #655e4e 1px solid; BORDER-LEFT: #655e4e 1px solid; OUTLINE-STYLE: none; BACKGROUND: url(images/ui-bg_inset-soft_30_ffffff_1x100.png) #ffffff repeat-x 50% 50%; COLOR: #0074c7; BORDER-TOP: #655e4e 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #655e4e 1px solid
	}
.ui-state-active A {
	OUTLINE-STYLE: none; COLOR: #0074c7; TEXT-DECORATION: none
	}
.ui-state-active A:link {
	OUTLINE-STYLE: none; COLOR: #0074c7; TEXT-DECORATION: none
	}
.ui-state-active A:visited {
	OUTLINE-STYLE: none; COLOR: #0074c7; TEXT-DECORATION: none
	}
.ui-state-highlight {
	BORDER-BOTTOM: #eeb420 1px solid; BORDER-LEFT: #eeb420 1px solid; BACKGROUND: url(images/ui-bg_gloss-wave_90_fff9e5_500x100.png) #fff9e5 repeat-x 50% top; COLOR: #1f1f1f; BORDER-TOP: #eeb420 1px solid; BORDER-RIGHT: #eeb420 1px solid
	}
.ui-widget-content .ui-state-highlight {
	BORDER-BOTTOM: #eeb420 1px solid; BORDER-LEFT: #eeb420 1px solid; BACKGROUND: url(images/ui-bg_gloss-wave_90_fff9e5_500x100.png) #fff9e5 repeat-x 50% top; COLOR: #1f1f1f; BORDER-TOP: #eeb420 1px solid; BORDER-RIGHT: #eeb420 1px solid
	}
.ui-state-highlight A {
	COLOR: #1f1f1f
	}
.ui-widget-content .ui-state-highlight A {
	COLOR: #1f1f1f
	}
.ui-state-error {
	BORDER-BOTTOM: #ffb73d 1px solid; BORDER-LEFT: #ffb73d 1px solid; BACKGROUND: url(images/ui-bg_diagonals-medium_20_d34d17_40x40.png) #d34d17 50% 50%; COLOR: #ffffff; BORDER-TOP: #ffb73d 1px solid; BORDER-RIGHT: #ffb73d 1px solid
	}
.ui-widget-content .ui-state-error {
	BORDER-BOTTOM: #ffb73d 1px solid; BORDER-LEFT: #ffb73d 1px solid; BACKGROUND: url(images/ui-bg_diagonals-medium_20_d34d17_40x40.png) #d34d17 50% 50%; COLOR: #ffffff; BORDER-TOP: #ffb73d 1px solid; BORDER-RIGHT: #ffb73d 1px solid
	}
.ui-state-error A {
	COLOR: #ffffff
	}
.ui-widget-content .ui-state-error A {
	COLOR: #ffffff
	}
.ui-state-error-text {
	COLOR: #ffffff
	}
.ui-widget-content .ui-state-error-text {
	COLOR: #ffffff
	}
.ui-state-disabled {
	BACKGROUND-IMAGE: none; FILTER: Alpha(Opacity=35); opacity: .35
	}
.ui-widget-content .ui-state-disabled {
	BACKGROUND-IMAGE: none; FILTER: Alpha(Opacity=35); opacity: .35
	}
.ui-priority-primary {
	FONT-WEIGHT: bold
	}
.ui-widget-content .ui-priority-primary {
	FONT-WEIGHT: bold
	}
.ui-priority-secondary {
	FILTER: Alpha(Opacity=70); FONT-WEIGHT: normal; opacity: .7
	}
.ui-widget-content .ui-priority-secondary {
	FILTER: Alpha(Opacity=70); FONT-WEIGHT: normal; opacity: .7
	}
.ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_d19405_256x240.png); WIDTH: 16px; HEIGHT: 16px
	}
.ui-widget-content .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_d19405_256x240.png)
	}
.ui-widget-header .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_fadc7a_256x240.png)
	}
.ui-state-default .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_3d3d3d_256x240.png)
	}
.ui-state-hover .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_bd7b00_256x240.png)
	}
.ui-state-focus .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_bd7b00_256x240.png)
	}
.ui-state-active .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_eb990f_256x240.png)
	}
.ui-state-highlight .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_ed9f26_256x240.png)
	}
.ui-state-error .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_ffe180_256x240.png)
	}
.ui-state-error-text .ui-icon {
	BACKGROUND-IMAGE: url(images/ui-icons_ffe180_256x240.png)
	}
.ui-icon-carat-1-n {
	BACKGROUND-POSITION: 0px 0px
	}
.ui-icon-carat-1-ne {
	BACKGROUND-POSITION: -16px 0px
	}
.ui-icon-carat-1-e {
	BACKGROUND-POSITION: -32px 0px
	}
.ui-icon-carat-1-se {
	BACKGROUND-POSITION: -48px 0px
	}
.ui-icon-carat-1-s {
	BACKGROUND-POSITION: -64px 0px
	}
.ui-icon-carat-1-sw {
	BACKGROUND-POSITION: -80px 0px
	}
.ui-icon-carat-1-w {
	BACKGROUND-POSITION: -96px 0px
	}
.ui-icon-carat-1-nw {
	BACKGROUND-POSITION: -112px 0px
	}
.ui-icon-carat-2-n-s {
	BACKGROUND-POSITION: -128px 0px
	}
.ui-icon-carat-2-e-w {
	BACKGROUND-POSITION: -144px 0px
	}
.ui-icon-triangle-1-n {
	BACKGROUND-POSITION: 0px -16px
	}
.ui-icon-triangle-1-ne {
	BACKGROUND-POSITION: -16px -16px
	}
.ui-icon-triangle-1-e {
	BACKGROUND-POSITION: -32px -16px
	}
.ui-icon-triangle-1-se {
	BACKGROUND-POSITION: -48px -16px
	}
.ui-icon-triangle-1-s {
	BACKGROUND-POSITION: -64px -16px
	}
.ui-icon-triangle-1-sw {
	BACKGROUND-POSITION: -80px -16px
	}
.ui-icon-triangle-1-w {
	BACKGROUND-POSITION: -96px -16px
	}
.ui-icon-triangle-1-nw {
	BACKGROUND-POSITION: -112px -16px
	}
.ui-icon-triangle-2-n-s {
	BACKGROUND-POSITION: -128px -16px
	}
.ui-icon-triangle-2-e-w {
	BACKGROUND-POSITION: -144px -16px
	}
.ui-icon-arrow-1-n {
	BACKGROUND-POSITION: 0px -32px
	}
.ui-icon-arrow-1-ne {
	BACKGROUND-POSITION: -16px -32px
	}
.ui-icon-arrow-1-e {
	BACKGROUND-POSITION: -32px -32px
	}
.ui-icon-arrow-1-se {
	BACKGROUND-POSITION: -48px -32px
	}
.ui-icon-arrow-1-s {
	BACKGROUND-POSITION: -64px -32px
	}
.ui-icon-arrow-1-sw {
	BACKGROUND-POSITION: -80px -32px
	}
.ui-icon-arrow-1-w {
	BACKGROUND-POSITION: -96px -32px
	}
.ui-icon-arrow-1-nw {
	BACKGROUND-POSITION: -112px -32px
	}
.ui-icon-arrow-2-n-s {
	BACKGROUND-POSITION: -128px -32px
	}
.ui-icon-arrow-2-ne-sw {
	BACKGROUND-POSITION: -144px -32px
	}
.ui-icon-arrow-2-e-w {
	BACKGROUND-POSITION: -160px -32px
	}
.ui-icon-arrow-2-se-nw {
	BACKGROUND-POSITION: -176px -32px
	}
.ui-icon-arrowstop-1-n {
	BACKGROUND-POSITION: -192px -32px
	}
.ui-icon-arrowstop-1-e {
	BACKGROUND-POSITION: -208px -32px
	}
.ui-icon-arrowstop-1-s {
	BACKGROUND-POSITION: -224px -32px
	}
.ui-icon-arrowstop-1-w {
	BACKGROUND-POSITION: -240px -32px
	}
.ui-icon-arrowthick-1-n {
	BACKGROUND-POSITION: 0px -48px
	}
.ui-icon-arrowthick-1-ne {
	BACKGROUND-POSITION: -16px -48px
	}
.ui-icon-arrowthick-1-e {
	BACKGROUND-POSITION: -32px -48px
	}
.ui-icon-arrowthick-1-se {
	BACKGROUND-POSITION: -48px -48px
	}
.ui-icon-arrowthick-1-s {
	BACKGROUND-POSITION: -64px -48px
	}
.ui-icon-arrowthick-1-sw {
	BACKGROUND-POSITION: -80px -48px
	}
.ui-icon-arrowthick-1-w {
	BACKGROUND-POSITION: -96px -48px
	}
.ui-icon-arrowthick-1-nw {
	BACKGROUND-POSITION: -112px -48px
	}
.ui-icon-arrowthick-2-n-s {
	BACKGROUND-POSITION: -128px -48px
	}
.ui-icon-arrowthick-2-ne-sw {
	BACKGROUND-POSITION: -144px -48px
	}
.ui-icon-arrowthick-2-e-w {
	BACKGROUND-POSITION: -160px -48px
	}
.ui-icon-arrowthick-2-se-nw {
	BACKGROUND-POSITION: -176px -48px
	}
.ui-icon-arrowthickstop-1-n {
	BACKGROUND-POSITION: -192px -48px
	}
.ui-icon-arrowthickstop-1-e {
	BACKGROUND-POSITION: -208px -48px
	}
.ui-icon-arrowthickstop-1-s {
	BACKGROUND-POSITION: -224px -48px
	}
.ui-icon-arrowthickstop-1-w {
	BACKGROUND-POSITION: -240px -48px
	}
.ui-icon-arrowreturnthick-1-w {
	BACKGROUND-POSITION: 0px -64px
	}
.ui-icon-arrowreturnthick-1-n {
	BACKGROUND-POSITION: -16px -64px
	}
.ui-icon-arrowreturnthick-1-e {
	BACKGROUND-POSITION: -32px -64px
	}
.ui-icon-arrowreturnthick-1-s {
	BACKGROUND-POSITION: -48px -64px
	}
.ui-icon-arrowreturn-1-w {
	BACKGROUND-POSITION: -64px -64px
	}
.ui-icon-arrowreturn-1-n {
	BACKGROUND-POSITION: -80px -64px
	}
.ui-icon-arrowreturn-1-e {
	BACKGROUND-POSITION: -96px -64px
	}
.ui-icon-arrowreturn-1-s {
	BACKGROUND-POSITION: -112px -64px
	}
.ui-icon-arrowrefresh-1-w {
	BACKGROUND-POSITION: -128px -64px
	}
.ui-icon-arrowrefresh-1-n {
	BACKGROUND-POSITION: -144px -64px
	}
.ui-icon-arrowrefresh-1-e {
	BACKGROUND-POSITION: -160px -64px
	}
.ui-icon-arrowrefresh-1-s {
	BACKGROUND-POSITION: -176px -64px
	}
.ui-icon-arrow-4 {
	BACKGROUND-POSITION: 0px -80px
	}
.ui-icon-arrow-4-diag {
	BACKGROUND-POSITION: -16px -80px
	}
.ui-icon-extlink {
	BACKGROUND-POSITION: -32px -80px
	}
.ui-icon-newwin {
	BACKGROUND-POSITION: -48px -80px
	}
.ui-icon-refresh {
	BACKGROUND-POSITION: -64px -80px
	}
.ui-icon-shuffle {
	BACKGROUND-POSITION: -80px -80px
	}
.ui-icon-transfer-e-w {
	BACKGROUND-POSITION: -96px -80px
	}
.ui-icon-transferthick-e-w {
	BACKGROUND-POSITION: -112px -80px
	}
.ui-icon-folder-collapsed {
	BACKGROUND-POSITION: 0px -96px
	}
.ui-icon-folder-open {
	BACKGROUND-POSITION: -16px -96px
	}
.ui-icon-document {
	BACKGROUND-POSITION: -32px -96px
	}
.ui-icon-document-b {
	BACKGROUND-POSITION: -48px -96px
	}
.ui-icon-note {
	BACKGROUND-POSITION: -64px -96px
	}
.ui-icon-mail-closed {
	BACKGROUND-POSITION: -80px -96px
	}
.ui-icon-mail-open {
	BACKGROUND-POSITION: -96px -96px
	}
.ui-icon-suitcase {
	BACKGROUND-POSITION: -112px -96px
	}
.ui-icon-comment {
	BACKGROUND-POSITION: -128px -96px
	}
.ui-icon-person {
	BACKGROUND-POSITION: -144px -96px
	}
.ui-icon-print {
	BACKGROUND-POSITION: -160px -96px
	}
.ui-icon-trash {
	BACKGROUND-POSITION: -176px -96px
	}
.ui-icon-locked {
	BACKGROUND-POSITION: -192px -96px
	}
.ui-icon-unlocked {
	BACKGROUND-POSITION: -208px -96px
	}
.ui-icon-bookmark {
	BACKGROUND-POSITION: -224px -96px
	}
.ui-icon-tag {
	BACKGROUND-POSITION: -240px -96px
	}
.ui-icon-home {
	BACKGROUND-POSITION: 0px -112px
	}
.ui-icon-flag {
	BACKGROUND-POSITION: -16px -112px
	}
.ui-icon-calendar {
	BACKGROUND-POSITION: -32px -112px
	}
.ui-icon-cart {
	BACKGROUND-POSITION: -48px -112px
	}
.ui-icon-pencil {
	BACKGROUND-POSITION: -64px -112px
	}
.ui-icon-clock {
	BACKGROUND-POSITION: -80px -112px
	}
.ui-icon-disk {
	BACKGROUND-POSITION: -96px -112px
	}
.ui-icon-calculator {
	BACKGROUND-POSITION: -112px -112px
	}
.ui-icon-zoomin {
	BACKGROUND-POSITION: -128px -112px
	}
.ui-icon-zoomout {
	BACKGROUND-POSITION: -144px -112px
	}
.ui-icon-search {
	BACKGROUND-POSITION: -160px -112px
	}
.ui-icon-wrench {
	BACKGROUND-POSITION: -176px -112px
	}
.ui-icon-gear {
	BACKGROUND-POSITION: -192px -112px
	}
.ui-icon-heart {
	BACKGROUND-POSITION: -208px -112px
	}
.ui-icon-star {
	BACKGROUND-POSITION: -224px -112px
	}
.ui-icon-link {
	BACKGROUND-POSITION: -240px -112px
	}
.ui-icon-cancel {
	BACKGROUND-POSITION: 0px -128px
	}
.ui-icon-plus {
	BACKGROUND-POSITION: -16px -128px
	}
.ui-icon-plusthick {
	BACKGROUND-POSITION: -32px -128px
	}
.ui-icon-minus {
	BACKGROUND-POSITION: -48px -128px
	}
.ui-icon-minusthick {
	BACKGROUND-POSITION: -64px -128px
	}
.ui-icon-close {
	BACKGROUND-POSITION: -80px -128px
	}
.ui-icon-closethick {
	BACKGROUND-POSITION: -96px -128px
	}
.ui-icon-key {
	BACKGROUND-POSITION: -112px -128px
	}
.ui-icon-lightbulb {
	BACKGROUND-POSITION: -128px -128px
	}
.ui-icon-scissors {
	BACKGROUND-POSITION: -144px -128px
	}
.ui-icon-clipboard {
	BACKGROUND-POSITION: -160px -128px
	}
.ui-icon-copy {
	BACKGROUND-POSITION: -176px -128px
	}
.ui-icon-contact {
	BACKGROUND-POSITION: -192px -128px
	}
.ui-icon-image {
	BACKGROUND-POSITION: -208px -128px
	}
.ui-icon-video {
	BACKGROUND-POSITION: -224px -128px
	}
.ui-icon-script {
	BACKGROUND-POSITION: -240px -128px
	}
.ui-icon-alert {
	BACKGROUND-POSITION: 0px -144px
	}
.ui-icon-info {
	BACKGROUND-POSITION: -16px -144px
	}
.ui-icon-notice {
	BACKGROUND-POSITION: -32px -144px
	}
.ui-icon-help {
	BACKGROUND-POSITION: -48px -144px
	}
.ui-icon-check {
	BACKGROUND-POSITION: -64px -144px
	}
.ui-icon-bullet {
	BACKGROUND-POSITION: -80px -144px
	}
.ui-icon-radio-off {
	BACKGROUND-POSITION: -96px -144px
	}
.ui-icon-radio-on {
	BACKGROUND-POSITION: -112px -144px
	}
.ui-icon-pin-w {
	BACKGROUND-POSITION: -128px -144px
	}
.ui-icon-pin-s {
	BACKGROUND-POSITION: -144px -144px
	}
.ui-icon-play {
	BACKGROUND-POSITION: 0px -160px
	}
.ui-icon-pause {
	BACKGROUND-POSITION: -16px -160px
	}
.ui-icon-seek-next {
	BACKGROUND-POSITION: -32px -160px
	}
.ui-icon-seek-prev {
	BACKGROUND-POSITION: -48px -160px
	}
.ui-icon-seek-end {
	BACKGROUND-POSITION: -64px -160px
	}
.ui-icon-seek-first {
	BACKGROUND-POSITION: -80px -160px
	}
.ui-icon-stop {
	BACKGROUND-POSITION: -96px -160px
	}
.ui-icon-eject {
	BACKGROUND-POSITION: -112px -160px
	}
.ui-icon-volume-off {
	BACKGROUND-POSITION: -128px -160px
	}
.ui-icon-volume-on {
	BACKGROUND-POSITION: -144px -160px
	}
.ui-icon-power {
	BACKGROUND-POSITION: 0px -176px
	}
.ui-icon-signal-diag {
	BACKGROUND-POSITION: -16px -176px
	}
.ui-icon-signal {
	BACKGROUND-POSITION: -32px -176px
	}
.ui-icon-battery-0 {
	BACKGROUND-POSITION: -48px -176px
	}
.ui-icon-battery-1 {
	BACKGROUND-POSITION: -64px -176px
	}
.ui-icon-battery-2 {
	BACKGROUND-POSITION: -80px -176px
	}
.ui-icon-battery-3 {
	BACKGROUND-POSITION: -96px -176px
	}
.ui-icon-circle-plus {
	BACKGROUND-POSITION: 0px -192px
	}
.ui-icon-circle-minus {
	BACKGROUND-POSITION: -16px -192px
	}
.ui-icon-circle-close {
	BACKGROUND-POSITION: -32px -192px
	}
.ui-icon-circle-triangle-e {
	BACKGROUND-POSITION: -48px -192px
	}
.ui-icon-circle-triangle-s {
	BACKGROUND-POSITION: -64px -192px
	}
.ui-icon-circle-triangle-w {
	BACKGROUND-POSITION: -80px -192px
	}
.ui-icon-circle-triangle-n {
	BACKGROUND-POSITION: -96px -192px
	}
.ui-icon-circle-arrow-e {
	BACKGROUND-POSITION: -112px -192px
	}
.ui-icon-circle-arrow-s {
	BACKGROUND-POSITION: -128px -192px
	}
.ui-icon-circle-arrow-w {
	BACKGROUND-POSITION: -144px -192px
	}
.ui-icon-circle-arrow-n {
	BACKGROUND-POSITION: -160px -192px
	}
.ui-icon-circle-zoomin {
	BACKGROUND-POSITION: -176px -192px
	}
.ui-icon-circle-zoomout {
	BACKGROUND-POSITION: -192px -192px
	}
.ui-icon-circle-check {
	BACKGROUND-POSITION: -208px -192px
	}
.ui-icon-circlesmall-plus {
	BACKGROUND-POSITION: 0px -208px
	}
.ui-icon-circlesmall-minus {
	BACKGROUND-POSITION: -16px -208px
	}
.ui-icon-circlesmall-close {
	BACKGROUND-POSITION: -32px -208px
	}
.ui-icon-squaresmall-plus {
	BACKGROUND-POSITION: -48px -208px
	}
.ui-icon-squaresmall-minus {
	BACKGROUND-POSITION: -64px -208px
	}
.ui-icon-squaresmall-close {
	BACKGROUND-POSITION: -80px -208px
	}
.ui-icon-grip-dotted-vertical {
	BACKGROUND-POSITION: 0px -224px
	}
.ui-icon-grip-dotted-horizontal {
	BACKGROUND-POSITION: -16px -224px
	}
.ui-icon-grip-solid-vertical {
	BACKGROUND-POSITION: -32px -224px
	}
.ui-icon-grip-solid-horizontal {
	BACKGROUND-POSITION: -48px -224px
	}
.ui-icon-gripsmall-diagonal-se {
	BACKGROUND-POSITION: -64px -224px
	}
.ui-icon-grip-diagonal-se {
	BACKGROUND-POSITION: -80px -224px
	}
.ui-corner-tl {
	-moz-border-radius-topleft: 8px; -webkit-border-top-left-radius: 8px
	}
.ui-corner-tr {
	-moz-border-radius-topright: 8px; -webkit-border-top-right-radius: 8px
	}
.ui-corner-bl {
	-moz-border-radius-bottomleft: 8px; -webkit-border-bottom-left-radius: 8px
	}
.ui-corner-br {
	-moz-border-radius-bottomright: 8px; -webkit-border-bottom-right-radius: 8px
	}
.ui-corner-top {
	-moz-border-radius-topleft: 8px; -webkit-border-top-left-radius: 8px; -moz-border-radius-topright: 8px; -webkit-border-top-right-radius: 8px
	}
.ui-corner-bottom {
	-moz-border-radius-bottomleft: 8px; -webkit-border-bottom-left-radius: 8px; -moz-border-radius-bottomright: 8px; -webkit-border-bottom-right-radius: 8px
	}
.ui-corner-right {
	-moz-border-radius-topright: 8px; -webkit-border-top-right-radius: 8px; -moz-border-radius-bottomright: 8px; -webkit-border-bottom-right-radius: 8px
	}
.ui-corner-left {
	-moz-border-radius-topleft: 8px; -webkit-border-top-left-radius: 8px; -moz-border-radius-bottomleft: 8px; -webkit-border-bottom-left-radius: 8px
	}
.ui-corner-all {
	-moz-border-radius: 8px; -webkit-border-radius: 8px
	}
.ui-widget-overlay {
	FILTER: Alpha(Opacity=80); BACKGROUND: url(images/ui-bg_flat_50_5c5c5c_40x100.png) #5c5c5c repeat-x 50% 50%; opacity: .80
	}
.ui-widget-shadow {
	FILTER: Alpha(Opacity=60); PADDING-BOTTOM: 7px; MARGIN: -7px 0px 0px -7px; PADDING-LEFT: 7px; PADDING-RIGHT: 7px; BACKGROUND: url(images/ui-bg_flat_30_cccccc_40x100.png) #cccccc repeat-x 50% 50%; PADDING-TOP: 7px; opacity: .60; -moz-border-radius: 8px; -webkit-border-radius: 8px
	}
.ui-accordion .ui-accordion-header {
	POSITION: relative; MARGIN-TOP: 1px; ZOOM: 1; CURSOR: pointer
	}
.ui-accordion .ui-accordion-li-fix {
	DISPLAY: inline
	}
.ui-accordion .ui-accordion-header-active {
	BORDER-BOTTOM-WIDTH: 0px !important
	}
.ui-accordion .ui-accordion-header A {
	PADDING-BOTTOM: 0.5em; PADDING-LEFT: 2.2em; PADDING-RIGHT: 0.5em; DISPLAY: block; FONT-SIZE: 1em; PADDING-TOP: 0.5em
	}
.ui-accordion .ui-accordion-header .ui-icon {
	POSITION: absolute; MARGIN-TOP: -8px; TOP: 50%; LEFT: 0.5em
	}
.ui-accordion .ui-accordion-content {
	POSITION: relative; PADDING-BOTTOM: 1em; MARGIN-TOP: -2px; PADDING-LEFT: 2.2em; PADDING-RIGHT: 2.2em; DISPLAY: none; MARGIN-BOTTOM: 2px; BORDER-TOP-WIDTH: 0px; OVERFLOW: auto; TOP: 1px; PADDING-TOP: 1em
	}
.ui-accordion .ui-accordion-content-active {
	DISPLAY: block
	}
.ui-datepicker {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 0.2em; WIDTH: 17em; PADDING-RIGHT: 0.2em; PADDING-TOP: 0.2em
	}
.ui-datepicker .ui-datepicker-header {
	POSITION: relative; PADDING-BOTTOM: 0.2em; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0.2em
	}
.ui-datepicker .ui-datepicker-prev {
	POSITION: absolute; WIDTH: 1.8em; HEIGHT: 1.8em; TOP: 2px
	}
.ui-datepicker .ui-datepicker-next {
	POSITION: absolute; WIDTH: 1.8em; HEIGHT: 1.8em; TOP: 2px
	}
.ui-datepicker .ui-datepicker-prev-hover {
	TOP: 1px
	}
.ui-datepicker .ui-datepicker-next-hover {
	TOP: 1px
	}
.ui-datepicker .ui-datepicker-prev {
	LEFT: 2px
	}
.ui-datepicker .ui-datepicker-next {
	RIGHT: 2px
	}
.ui-datepicker .ui-datepicker-prev-hover {
	LEFT: 1px
	}
.ui-datepicker .ui-datepicker-next-hover {
	RIGHT: 1px
	}
.ui-datepicker .ui-datepicker-prev SPAN {
	POSITION: absolute; MARGIN-TOP: -8px; DISPLAY: block; MARGIN-LEFT: -8px; TOP: 50%; LEFT: 50%
	}
.ui-datepicker .ui-datepicker-next SPAN {
	POSITION: absolute; MARGIN-TOP: -8px; DISPLAY: block; MARGIN-LEFT: -8px; TOP: 50%; LEFT: 50%
	}
.ui-datepicker .ui-datepicker-title {
	TEXT-ALIGN: center; LINE-HEIGHT: 1.8em; MARGIN: 0px 2.3em
	}
.ui-datepicker .ui-datepicker-title SELECT {
	MARGIN: 1px 0px; FLOAT: left; FONT-SIZE: 1em
	}
.ui-datepicker SELECT.ui-datepicker-month-year {
	WIDTH: 100%
	}
.ui-datepicker SELECT.ui-datepicker-month {
	WIDTH: 49%
	}
.ui-datepicker SELECT.ui-datepicker-year {
	WIDTH: 49%
	}
.ui-datepicker .ui-datepicker-title SELECT.ui-datepicker-year {
	FLOAT: right
	}
.ui-datepicker TABLE {
	MARGIN: 0px 0px 0.4em; WIDTH: 100%; BORDER-COLLAPSE: collapse; FONT-SIZE: 0.9em
	}
.ui-datepicker TH {
	TEXT-ALIGN: center; PADDING-BOTTOM: 0.7em; BORDER-RIGHT-WIDTH: 0px; PADDING-LEFT: 0.3em; PADDING-RIGHT: 0.3em; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; FONT-WEIGHT: bold; PADDING-TOP: 0.7em
	}
.ui-datepicker TD {
	PADDING-BOTTOM: 1px; BORDER-RIGHT-WIDTH: 0px; PADDING-LEFT: 1px; PADDING-RIGHT: 1px; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 1px
	}
.ui-datepicker TD SPAN {
	TEXT-ALIGN: right; PADDING-BOTTOM: 0.2em; PADDING-LEFT: 0.2em; PADDING-RIGHT: 0.2em; DISPLAY: block; TEXT-DECORATION: none; PADDING-TOP: 0.2em
	}
.ui-datepicker TD A {
	TEXT-ALIGN: right; PADDING-BOTTOM: 0.2em; PADDING-LEFT: 0.2em; PADDING-RIGHT: 0.2em; DISPLAY: block; TEXT-DECORATION: none; PADDING-TOP: 0.2em
	}
.ui-datepicker .ui-datepicker-buttonpane {
	BACKGROUND-IMAGE: none; PADDING-BOTTOM: 0px; BORDER-RIGHT-WIDTH: 0px; MARGIN: 0.7em 0px 0px; PADDING-LEFT: 0.2em; PADDING-RIGHT: 0.2em; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 0px
	}
.ui-datepicker .ui-datepicker-buttonpane BUTTON {
	PADDING-BOTTOM: 0.3em; MARGIN: 0.5em 0.2em 0.4em; PADDING-LEFT: 0.6em; WIDTH: auto; PADDING-RIGHT: 0.6em; FLOAT: right; OVERFLOW: visible; CURSOR: pointer; PADDING-TOP: 0.2em
	}
.ui-datepicker .ui-datepicker-buttonpane BUTTON.ui-datepicker-current {
	FLOAT: left
	}
.ui-datepicker-multi.ui-datepicker {
	WIDTH: auto
	}
.ui-datepicker-multi .ui-datepicker-group {
	FLOAT: left
	}
.ui-datepicker-multi .ui-datepicker-group TABLE {
	MARGIN: 0px auto 0.4em; WIDTH: 95%
	}
.ui-datepicker-multi-2 .ui-datepicker-group {
	WIDTH: 50%
	}
.ui-datepicker-multi-3 .ui-datepicker-group {
	WIDTH: 33.3%
	}
.ui-datepicker-multi-4 .ui-datepicker-group {
	WIDTH: 25%
	}
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header {
	BORDER-LEFT-WIDTH: 0px
	}
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
	BORDER-LEFT-WIDTH: 0px
	}
.ui-datepicker-multi .ui-datepicker-buttonpane {
	CLEAR: left
	}
.ui-datepicker-row-break {
	WIDTH: 100%; CLEAR: both
	}
.ui-datepicker-rtl {
	DIRECTION: rtl
	}
.ui-datepicker-rtl .ui-datepicker-prev {
	RIGHT: 2px; LEFT: auto
	}
.ui-datepicker-rtl .ui-datepicker-next {
	RIGHT: auto; LEFT: 2px
	}
.ui-datepicker-rtl .ui-datepicker-prev:hover {
	RIGHT: 1px; LEFT: auto
	}
.ui-datepicker-rtl .ui-datepicker-next:hover {
	RIGHT: auto; LEFT: 1px
	}
.ui-datepicker-rtl .ui-datepicker-buttonpane {
	CLEAR: right
	}
.ui-datepicker-rtl .ui-datepicker-buttonpane BUTTON {
	FLOAT: left
	}
.ui-datepicker-rtl .ui-datepicker-buttonpane BUTTON.ui-datepicker-current {
	FLOAT: right
	}
.ui-datepicker-rtl .ui-datepicker-group {
	FLOAT: right
	}
.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header {
	BORDER-RIGHT-WIDTH: 0px; BORDER-LEFT-WIDTH: 1px
	}
.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
	BORDER-RIGHT-WIDTH: 0px; BORDER-LEFT-WIDTH: 1px
	}
.ui-datepicker-cover {
	Z-INDEX: -1; POSITION: absolute; FILTER: mask(); WIDTH: 200px; DISPLAY: block; HEIGHT: 200px; TOP: -4px; LEFT: -4px
	}
.ui-dialog {
	POSITION: relative; PADDING-BOTTOM: 0.2em; PADDING-LEFT: 0.2em; WIDTH: 300px; PADDING-RIGHT: 0.2em; PADDING-TOP: 0.2em
	}
.ui-dialog .ui-dialog-titlebar {
	POSITION: relative; PADDING-BOTTOM: 0.3em; PADDING-LEFT: 1em; PADDING-RIGHT: 0.3em; PADDING-TOP: 0.5em
	}
.ui-dialog .ui-dialog-title {
	MARGIN: 0.1em 0px 0.2em; FLOAT: left
	}
.ui-dialog .ui-dialog-titlebar-close {
	POSITION: absolute; PADDING-BOTTOM: 1px; MARGIN: -10px 0px 0px; PADDING-LEFT: 1px; WIDTH: 19px; PADDING-RIGHT: 1px; HEIGHT: 18px; TOP: 50%; RIGHT: 0.3em; PADDING-TOP: 1px
	}
.ui-dialog .ui-dialog-titlebar-close SPAN {
	MARGIN: 1px; DISPLAY: block
	}
.ui-dialog .ui-dialog-titlebar-close:hover {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
	}
.ui-dialog .ui-dialog-titlebar-close:focus {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
	}
.ui-dialog .ui-dialog-content {
	PADDING-BOTTOM: 0.5em; BORDER-RIGHT-WIDTH: 0px; PADDING-LEFT: 1em; PADDING-RIGHT: 1em; ZOOM: 1; BACKGROUND: none transparent scroll repeat 0% 0%; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; OVERFLOW: auto; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 0.5em
	}
.ui-dialog .ui-dialog-buttonpane {
	BACKGROUND-IMAGE: none; TEXT-ALIGN: left; PADDING-BOTTOM: 0.5em; BORDER-RIGHT-WIDTH: 0px; MARGIN: 0.5em 0px 0px; PADDING-LEFT: 0.4em; PADDING-RIGHT: 1em; BORDER-TOP-WIDTH: 1px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 0.3em
	}
.ui-dialog .ui-dialog-buttonpane BUTTON {
	PADDING-BOTTOM: 0.3em; LINE-HEIGHT: 1.4em; MARGIN: 0.5em 0.4em 0.5em 0px; PADDING-LEFT: 0.6em; WIDTH: auto; PADDING-RIGHT: 0.6em; FLOAT: right; OVERFLOW: visible; CURSOR: pointer; PADDING-TOP: 0.2em
	}
.ui-dialog .ui-resizable-se {
	WIDTH: 14px; BOTTOM: 3px; HEIGHT: 14px; RIGHT: 3px
	}
.ui-draggable .ui-dialog-titlebar {
	CURSOR: move
	}
.ui-progressbar {
	TEXT-ALIGN: left; HEIGHT: 2em
	}
.ui-progressbar .ui-progressbar-value {
	MARGIN: -1px; HEIGHT: 100%
	}
.ui-resizable {
	POSITION: relative
	}
.ui-resizable-handle {
	Z-INDEX: 99999; POSITION: absolute; DISPLAY: block; FONT-SIZE: 0px
	}
.ui-resizable-disabled .ui-resizable-handle {
	DISPLAY: none
	}
.ui-resizable-autohide .ui-resizable-handle {
	DISPLAY: none
	}
.ui-resizable-n {
	WIDTH: 100%; HEIGHT: 7px; TOP: -5px; CURSOR: n-resize; LEFT: 0px
	}
.ui-resizable-s {
	WIDTH: 100%; BOTTOM: -5px; HEIGHT: 7px; CURSOR: s-resize; LEFT: 0px
	}
.ui-resizable-e {
	WIDTH: 7px; HEIGHT: 100%; TOP: 0px; CURSOR: e-resize; RIGHT: -5px
	}
.ui-resizable-w {
	WIDTH: 7px; HEIGHT: 100%; TOP: 0px; CURSOR: w-resize; LEFT: -5px
	}
.ui-resizable-se {
	WIDTH: 12px; BOTTOM: 1px; HEIGHT: 12px; CURSOR: se-resize; RIGHT: 1px
	}
.ui-resizable-sw {
	WIDTH: 9px; BOTTOM: -5px; HEIGHT: 9px; CURSOR: sw-resize; LEFT: -5px
	}
.ui-resizable-nw {
	WIDTH: 9px; HEIGHT: 9px; TOP: -5px; CURSOR: nw-resize; LEFT: -5px
	}
.ui-resizable-ne {
	WIDTH: 9px; HEIGHT: 9px; TOP: -5px; CURSOR: ne-resize; RIGHT: -5px
	}
.ui-slider {
	POSITION: relative; TEXT-ALIGN: left
	}
.ui-slider .ui-slider-handle {
	Z-INDEX: 2; POSITION: absolute; WIDTH: 13px; HEIGHT: 11px; CURSOR: default
	}
.ui-slider .ui-slider-range {
	Z-INDEX: 1; POSITION: absolute; BORDER-RIGHT-WIDTH: 0px; DISPLAY: block; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; FONT-SIZE: 0.7em; BORDER-LEFT-WIDTH: 0px
	}
.ui-slider-horizontal {
	HEIGHT: 0.8em
	}
.ui-slider-horizontal .ui-slider-handle {
	MARGIN-LEFT: -0.6em; TOP: -0.3em
	}
.ui-slider-horizontal .ui-slider-range {
	HEIGHT: 100%; TOP: 0px
	}
.ui-slider-horizontal .ui-slider-range-min {
	LEFT: 0px
	}
.ui-slider-horizontal .ui-slider-range-max {
	RIGHT: 0px
	}
.ui-slider-vertical {
	WIDTH: 0.8em; HEIGHT: 100px
	}
.ui-slider-vertical .ui-slider-handle {
	MARGIN-BOTTOM: -0.6em; MARGIN-LEFT: 0px; LEFT: -4px
	}
.ui-slider-vertical .ui-slider-range {
	WIDTH: 100%; LEFT: 0px
	}
.ui-slider-vertical .ui-slider-range-min {
	BOTTOM: 9px
	}
.ui-slider-vertical .ui-slider-range-max {
	TOP: 0px
	}
.ui-tabs {
	PADDING-BOTTOM: 0.2em; PADDING-LEFT: 0.2em; PADDING-RIGHT: 0.2em; ZOOM: 1; PADDING-TOP: 0.2em
	}
.ui-tabs .ui-tabs-nav {
	POSITION: relative; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; PADDING-LEFT: 0.2em; PADDING-RIGHT: 0.2em; PADDING-TOP: 0.2em
	}
.ui-tabs .ui-tabs-nav LI {
	POSITION: relative; PADDING-BOTTOM: 0px; MARGIN: 0px 0.2em -1px 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; FLOAT: left; BORDER-BOTTOM-WIDTH: 0px !important; PADDING-TOP: 0px
	}
.ui-tabs .ui-tabs-nav LI A {
	PADDING-BOTTOM: 0.5em; PADDING-LEFT: 1em; PADDING-RIGHT: 1em; FLOAT: left; TEXT-DECORATION: none; PADDING-TOP: 0.5em
	}
.ui-tabs .ui-tabs-nav LI.ui-tabs-selected {
	PADDING-BOTTOM: 1px; BORDER-BOTTOM-WIDTH: 0px
	}
.ui-tabs .ui-tabs-nav LI.ui-tabs-selected A {
	CURSOR: text
	}
.ui-tabs .ui-tabs-nav LI.ui-state-disabled A {
	CURSOR: text
	}
.ui-tabs .ui-tabs-nav LI.ui-state-processing A {
	CURSOR: text
	}
.ui-tabs .ui-tabs-nav LI A {
	CURSOR: pointer
	}
.ui-tabs-collapsible.ui-tabs .ui-tabs-nav LI.ui-tabs-selected A {
	CURSOR: pointer
	}
.ui-tabs .ui-tabs-panel {
	PADDING-BOTTOM: 1em; BORDER-RIGHT-WIDTH: 0px; PADDING-LEFT: 1.4em; PADDING-RIGHT: 1.4em; DISPLAY: block; BACKGROUND: none transparent scroll repeat 0% 0%; BORDER-TOP-WIDTH: 0px; BORDER-BOTTOM-WIDTH: 0px; BORDER-LEFT-WIDTH: 0px; PADDING-TOP: 1em
	}
.ui-tabs .ui-tabs-hide {
	DISPLAY: none !important
	}
