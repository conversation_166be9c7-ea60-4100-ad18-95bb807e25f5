html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	font-weight: inherit;
	font-style: inherit;
	font-size: 100%;
	font-family: inherit;
	vertical-align: baseline;
}
fieldset {
	border: 1px solid black; padding: 8px; margin: 8px 0;
}
/* remember to define focus styles! */
:focus {
	outline: 0;
}
body {
	line-height: 1;
	color: black;
	background: white;
}

body, div { font-family: 'lucida grande', helvetica, verdana, arial, sans-serif }
body { margin: 0; padding: 0; font-size: small; color: #333 }
h1, h2 { font-family: 'trebuchet ms', verdana, arial; padding: 10px; margin: 0 }
h1 { font-size: large }
#main { padding: 1em; }
#banner { padding: 15px; background-color: #06b; color: white; font-size: large; border-bottom: 1px solid #ccc;
    background: url(bg.gif) repeat-x; text-align: center }
#banner a { color: white; }
legend { font-weight: bold; }

button { padding: 0 6px; margin: 0; }

pre, code { white-space: pre; font-family: "Courier New"; }
pre { margin: 8px 0; }
h3 {
	font-size: 110%;
	font-weight: bold;
	margin: .2em 0 .5em 0;
}
p { margin: 1em 0; }
strong { font-weight: bolder; }
em { font-style: italic; }

.jscom, .mix htcom   { color: #4040c2; }
.com      { color: green; }
.regexp   { color: maroon; }
.string   { color: teal; }
.keywords { color: blue; }
.global   { color: #008; }
.numbers  { color: #880; }
.comm     { color: green; }
.tag      { color: blue; }
.entity   { color: blue; }
.string   { color: teal; }
.aname    { color: maroon; }
.avalue   { color: maroon; }
.jquery   { color: #00a; }
.plugin   { color: red; }

#tooltip.pretty {
	font-family: Arial;
	border: none;
	width: 210px;
	padding:20px;
	height: 135px;
	opacity: 0.8;
	background: url('shadow.png');
}
#tooltip.pretty h3 {
	margin-bottom: 0.75em;
	font-size: 12pt;
	width: 220px;
	text-align: center;
}
#tooltip.pretty div { width: 220px; text-align: left; }

#tooltip.fancy {
	background: url('shadow2.png');
	padding-top: 5em;
	height: 100px;
}
#tooltip.fancy.viewport-right {
	background: url('shadow2-reverse.png');
}

#extended { margin: 2em 0; }
#extended label { text-decoration: underline; }
#yahoo { width: 7em; }
#right, #right2 { text-align: right; }
#tooltip.right { width: 250px; }
#fancy2 { float: right; }