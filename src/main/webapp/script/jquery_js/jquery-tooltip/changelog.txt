1.3
---

* Added fade option (duration in ms) for fading in/out tooltips; IE <= 6 is excluded when bgiframe plugin is included
* Fixed imagemaps in IE, added back example
* Added positionLeft-option - positions the tooltip to the left of the cursor
* Remove deprecated $.fn.Tooltip in favor of $.fn.tooltip

1.2
---

* Improved bodyHandler option to accept HTML strings, DOM elements and jQuery objects as the return value
* Fixed bug in Safari 3 where to tooltip is initially visible, by first appending to DOM then hiding it
* Improvement for viewport-border-positioning: Add the classes "viewport-right" and "viewport-bottom" when the element is moved at the viewport border.
* Moved and enhanced documentation to docs.jquery.com
* Added examples for bodyHandler: footnote-tooltip and thumbnail
* Added id option, defaults to "tooltip", override to use a different id in your stylesheet
* Moved demo tooltip style to screen.css
* Moved demo files to demo folder and dependencies to lib folder
* Dropped image map example - completely incompatible with IE; image maps aren't supported anymore

1.1
---

* Use bgiframe-plugin if available
* Use dimensions-plugin to calculate viewport
* Expose global blocked-property via $.Tooltip.blocked to programmatically disable all tooltips
* Fixed image maps in IE by setting the alt-attribute to an empty string
* Removed event-option (only hover-tooltips now)
* Simplified event-handling (using hover instead of mouseover und mouseout)
* Added another "pretty" example
* Added top and left options to specify tooltip offset
* Reworked example page: New layout, code examples

1.0
---

* first release considered stable