<%--
    Document   : applicationItemResult
    Created on : Dec 24, 2010, 6:46:15 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.MainMenuItem" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="MainMenuItemManagerBean" class="com.misyn.mcms.admin.MainMenuItemManager" scope="application"/>

<%
    long timeURL = System.currentTimeMillis();
    int n_prgid = 1;
    MainMenuItem mainMenuItem = new MainMenuItem();


    try {
        mainMenuItem.setN_prgid(n_prgid);
    } catch (Exception e) {
    }
    try {
        mainMenuItem.setN_mnuid(Integer.parseInt(request.getParameter("txtN_mnuid")));
    } catch (Exception e) {
    }

    try {
        mainMenuItem.setV_mnuname(request.getParameter("txtV_mnuname"));
    } catch (Exception e) {
    }
    try {
        mainMenuItem.setV_apptype(request.getParameter("txtV_apptype"));
    } catch (Exception e) {
    }

    try {

        mainMenuItem.setN_itm_seq_no(0);
        mainMenuItem.setV_inpuser(user.getV_usrid());
    } catch (Exception e) {
    }


    int result = MainMenuItemManagerBean.saveMainMenuItem(mainMenuItem);

    if (result > 0) {
        response.sendRedirect("mainItemList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
    } else {
        response.sendRedirect("mainItemList.jsp?" + timeURL + "&P_ERROR=Can not be save");
    }

%>


