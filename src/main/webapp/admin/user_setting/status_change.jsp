<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>
<%
    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    String readOnlyClass = "";

    long timeURL = System.currentTimeMillis();
    String URL = "password_status_result.jsp?" + timeURL + "&TYPE=2";
    String ERROR = request.getParameter("ERROR");

    if (ERROR == null) {
        ERROR = "";
    }
    User u = new User();

    try {
        u.setN_comid(Integer.parseInt(request.getParameter("txtN_comid")));
        u.setV_usrid(request.getParameter("txtV_usrid"));
    } catch (Exception e) {
    }

    boolean b = UserManagerBean.isValiedUser(u);
    session.removeAttribute("STAUS_CHANGE_USER");
    session.setAttribute("STAUS_CHANGE_USER", u);

    if (!b) {
        response.sendRedirect("validateUsers.jsp?TYPE=2&ERROR=Invalid User ID");
    }







    /*  if (list != null) {
    if (list.size() != 0) {
    find_user = list.get(0);
    }
    }*/
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript">
        /* $(function() {
                            //
                            //buttonImage: '/image/common/calendar.gif',
                            var d1=document.frmForm.txtDOB.value;
                            $("#txtDOB").datepicker({
                                showOn: 'button',
                                buttonImage: '/css/common/fb_form/dtpic.gif',
                                buttonImageOnly: true,
                                changeMonth: true,
                                changeYear: true,
                                yearRange: '1940:2099' ,
                                minDate: '-70y',
                                maxDate: '0d'

                            });
                            $("#txtDOB").datepicker('option', {dateFormat: "yy-mm-dd"});
                            document.frmForm.txtDOB.value=d1;

                        });*/


        var timeUrl = new Date().getDate();


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //if($("select#txtN_comid").val()=="-1"){$("select#txtN_comid").focus();return;}
                            if ($("input#txtN_mnuname").val() == "") {
                                $("input#txtN_mnuname").focus();
                                return;
                            }
                            if ($("select#txtV_apptype").val() == "0") {
                                $("select#txtV_apptype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            if ($("select#txtV_title").val() == "") {
                                $("select#txtV_title").focus();
                                return;
                            }
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--
        function init() {

            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.PasswordChange.txtV_usrstatus.focus();
        }


        function pageSubmit(type) {

            if (type == 'Password') {
                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("B1").style.cursor = 'wait';
                document.PasswordChange.B1.disabled = true;
                document.PasswordChange.B3.disabled = true;
                document.PasswordChange.action = "<%=URL%>"
                document.PasswordChange.submit();
            }

            else if (type == 'Close') {
                window.location = "<%=HOME_PAGE%>";

            }

        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }
    </script>
    <style>
        .fbSelectBox {
            border: 1px solid #0C0;
            padding: 3px;
            }
        .selectbox {
            margin: 0px 5px 10px 0px;
            padding-left: 2px;
            font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
            font-size: 1em; /* Resize Font*/
            width: 190px; /* Resize Width */
            display: block;
            text-align: left;
            background: url('bg_select.png') right;
            cursor: pointer;
            border: 1px solid #D1E4F6;
            color: #333;
            }
    </style>
    <%--<link href="../../SpryAssets/SpryValidationPassword.css" rel="stylesheet" type="text/css"/>--%>
</head>
<body onload="init();">
<div class="container-fluid">
    <div class="row header-bg mb-2 py-2 bg-dark align-items-center" id="status">
        <div class="col-sm-7">
            <h5 class="hide-sm m-0">User Status Reset</h5>
            <div class="clearfix"></div>
        </div>
    </div>
    <form name="PasswordChange" action="" method="post">
        <div style="font-size:12px;color:#F00"><%=ERROR%>
        </div>
        <input type="hidden" name="txtN_comid" id="txtN_comid" value="<%=u.getN_comid()%>"/>
        <input type="hidden" name="txt1" id="txt1"/>
        <div class="col_half" style="width:95%;">
            <div class="row">
                <div class="col-12">
                    <div id="Note" class="noteDivClass text-right text-danger">Note : <span class="text-muted font-weight-bold">Fields marked with  <span class="text-danger font-weight-bold"> *</span> are mandatory.</span>
                    </div>
                </div>
                <hr>
                <div class="col-md-6 offset-md-3">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">User ID :</label>
                        <div class="col-sm-8">
                            <p><%=u.getV_usrid()%>
                            </p>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">User Status :</label>
                        <div class="col-sm-8">
                            <select name="txtV_usrstatus" id="txtV_usrstatus" class="form-control form-control-sm">
                                <option value="A">Active</option>
                                <option value="D">Disabled</option>
                                <option value="L">Locked</option>
                            </select>
                        </div>
                    </div>
                    <script type="text/javascript">
                        document.getElementById("txtV_usrstatus").value = "<%=u.getV_usrstatus()%>";
                    </script>
                    <div class="text-right">
                        <hr>
                        <input type="button" name="B1" id="B1" value="Change Password" onclick="pageSubmit('Password')"
                                class="btn btn-primary"/>
                        <input type="button" name="B3" id="B3" value="Close" onclick="pageSubmit('Close')"
                                class="btn btn-secondary"/>
                    </div>
                </div>
                <div id="dialog" style="display:none;" title="${CompanyTitle}">
                    <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
                    <p id="dialog-email" class="textGrey"></p>
                </div>
            </div>
        </div>
    </form>
</div>
</body>
</html>
