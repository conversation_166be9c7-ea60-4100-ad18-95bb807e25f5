<%--
    Document   : password_change_result
    Created on : Jan 31, 2011, 3:33:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@include file="/common/ValidateUser.jsp" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>

<%

    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("TYPE"));
    } catch (Exception e) {
    }
    User u = null;
    try {
        u = (User) session.getAttribute("STAUS_CHANGE_USER");
    } catch (Exception e) {
    }


    String URL = "";


    boolean result = false;
    if (TYPE == 1) {
        try {
            u.setV_password(request.getParameter("NewPassword"));
        } catch (Exception e) {
        }
        result = UserManagerBean.isUpadeteUserSetting(u, 1);
        if (result) {
            UserManagerBean.setMsg("Password reset successfully");
        } else {
            UserManagerBean.setMsg("Can not be reset password");
        }
        response.sendRedirect("validateUsers.jsp?TYPE=1&ERROR=" + UserManagerBean.getMsg());

    } else if (TYPE == 2) {
        try {
            u.setV_usrstatus(request.getParameter("txtV_usrstatus"));
        } catch (Exception e) {
        }
        result = UserManagerBean.isUpadeteUserSetting(u, 2);
        if (result) {
            UserManagerBean.setMsg("User status change successfully");
        } else {
            UserManagerBean.setMsg("Can not be changed User status");
        }
        response.sendRedirect("validateUsers.jsp?TYPE=2&ERROR=" + UserManagerBean.getMsg());
    }


%>

