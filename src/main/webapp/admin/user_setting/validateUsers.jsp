<%--
    Document   : validateUsers
    Created on : Feb 3, 2011, 11:28:23 AM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : <PERSON><PERSON> Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%
    long timeURL = System.currentTimeMillis();
    String URL = "";
    String ERROR = request.getParameter("ERROR");

    int TYPE = 0;
    String title = "";

    try {
        TYPE = Integer.parseInt(request.getParameter("TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {
        title = "Password Reset";
        URL = "password_change.jsp?" + timeURL + "&TYPE=1";
    } else if (TYPE == 2) {
        title = "User Status Reset";
        URL = "status_change.jsp?" + timeURL + "&TYPE=2";
    }
    session.removeAttribute("P_TYPE");
    session.setAttribute("P_TYPE", TYPE);

    //TYPE=1 ---> password reset
    //TYPE=2 ---> change user status

    if (ERROR == null) {
        ERROR = "";
    }
    String str_n_comid_popList = "";

    if (user.getN_accessusrtype() == 1) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "", "");


    } else if (user.getN_accessusrtype() == 2) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "", "");


    } else if (user.getN_accessusrtype() == 3) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + user.getN_comid(), "");


    } else if (user.getN_accessusrtype() == 4) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");


    } else if (user.getN_accessusrtype() == 5) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");


    } else {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=-1", "");
    }
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript">
        var timeUrl = new Date().getDate();


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //if($("select#txtN_comid").val()=="-1"){$("select#txtN_comid").focus();return;}
                            if ($("input#txtN_mnuname").val() == "") {
                                $("input#txtN_mnuname").focus();
                                return;
                            }
                            if ($("select#txtV_apptype").val() == "0") {
                                $("select#txtV_apptype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            if ($("select#txtV_title").val() == "") {
                                $("select#txtV_title").focus();
                                return;
                            }
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--


        function pageSubmit(type) {

            if (type == 'Validate') {

                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }

            else if (type == 'Close') {
                window.location = "<%=HOME_PAGE%>";

            }

        }

        function init() {

            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.frmForm.txtN_comid.focus();
        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }
    </script>
    <title></title>
</head>
<body onload="init();">
<div class="container-fluid">
    <div class="row header-bg mb-2 py-2 bg-dark align-items-center bg-dark">
        <div class="col-sm-7">
            <h5 class="hide-sm m-0">Validate User for <%=title%>
            </h5>
            <div class="clearfix"></div>
        </div>
    </div>
    <form name="frmForm" action="" method="post">
        <div style="font-size:12px;color:#F00"><%=ERROR%>
        </div>
        <input type="hidden" name="txt1" id="txt1"/>
        <div class="row">
            <div class="col-12">
                <div id="Note" class="noteDivClass text-right text-danger">Note : <span class="text-muted font-weight-bold">Fields marked with  <span class="text-danger font-weight-bold"> *</span> are mandatory.</span>
                </div>
            </div>
            <hr>
            <div class="col-md-6 offset-md-3">
                <input name="txtN_mnuid" id="txtN_mnuid" type="hidden" value=""/>
                <div class="form-group row">
                    <label for="txtN_comid" class="col-sm-4 col-form-label">Company <span style="color:#D50000">*</span> :</label>
                    <div class="col-sm-8">
                        <select name="txtN_comid" id="txtN_comid" class="form-control form-control-sm">
                            <%out.print(str_n_comid_popList);%>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="password" class="col-sm-4 col-form-label">User ID <span style="color:#D50000">*</span> :</label>
                    <div class="col-sm-8">
                        <input name="txtV_usrid" type="txtV_usrid" class="form-control form-control-sm" id="password" size="20"
                                maxlength="15" tabindex="1"/>
                    </div>
                </div>
                <div class=" text-right">
                    <hr>
                    <input type="button" name="cmdSave" id="cmdSave" value="Validate User" onclick="pageSubmit('Validate')"
                            class="btn btn-primary"/>
                    <input type="button" name="cmdClose" id="cmdClose" value="Close" onclick="pageSubmit('Close')"
                            class="btn btn-secondary"/>
                </div>
            </div>
            <div id="dialog" style="display:none;" title="${CompanyTitle}">
                <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
                <p id="dialog-email" class="textGrey"></p>
            </div>
        </div>
    </form>
    <div class="spacer"> &nbsp;</div>
</div>
</body>
</html>
