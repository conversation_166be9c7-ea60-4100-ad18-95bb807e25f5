<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@page import="com.misyn.mcms.admin.SubMenuItem" %>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@page import="java.util.List" %>
<jsp:useBean id="SubMenuItemManagerBean" class="com.misyn.mcms.admin.SubMenuItemManager" scope="application"/>
<%
    String ADD_RIGHT = "disabled='disabled'";
    String DELETE_RIGHT = "disabled='disabled'";
    if (((String) session.getAttribute("RIGHT_D")).equals("checked")) {
        DELETE_RIGHT = "";
    }

    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    String readOnlyClass = "";

    long timeURL = System.currentTimeMillis();
    String URL = "applicationItemResult.jsp?" + timeURL;


    int n_prgid = 1;
    int n_mnuid = -1;
    int n_itmid = -1;

    String str_n_mnuid_popList = "";


    List<SubMenuItem> list = null;
    SubMenuItem subMenuItem = null;
    String searchKey = "";

    boolean isNewRecord = true;
    try {
        isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
    } catch (Exception e) {
    }

    try {
        n_prgid = Integer.parseInt(request.getParameter("P_N_PRGID"));
    } catch (Exception e) {
    }

    try {
        n_mnuid = Integer.parseInt(request.getParameter("P_N_MNUID"));
    } catch (Exception e) {
    }

    try {
        n_itmid = Integer.parseInt(request.getParameter("P_N_ITMID"));
    } catch (Exception e) {
    }


    if (isNewRecord) {
        if (((String) session.getAttribute("RIGHT_I")).equals("checked")) {
            ADD_RIGHT = "";
        }
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        str_n_mnuid_popList = "<option value='-1' >Please select one</option>\n" + DbRecordCommonFunction.getInstance().
                getPopupList("mnu_mst ", "n_mnuid", "v_mnuname", "", "");

        subMenuItem = new SubMenuItem();


    } else {
        if (((String) session.getAttribute("RIGHT_M")).equals("checked")) {
            ADD_RIGHT = "";
        }
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        str_n_mnuid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("mnu_mst ", "n_mnuid", "v_mnuname", "n_prgid=" + n_prgid + " AND n_mnuid=" + n_mnuid, "");

        readOnlyClass = " readonly class=\"textReadOnly\" ";
        searchKey = "n_prgid=" + n_prgid + " AND n_mnuid=" + n_mnuid + " AND n_itmid=" + n_itmid;

        if (user.getN_accessusrtype() == 1) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 2) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 3) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 4) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        }


    }

            /*  if (list != null) {
            if (list.size() != 0) {
            find_user = list.get(0);
            }
            }*/


%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <script type="text/javascript" src="/script/common/ListBox.js"></script>

    <link href="/css/common/fb_form/fb_form_col.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/css/common/fb_form/custom-form-elements.js"></script>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript">
        /* $(function() {
                    //
                    //buttonImage: '/image/common/calendar.gif',
                    var d1=document.frmForm.txtDOB.value;
                    $("#txtDOB").datepicker({
                        showOn: 'button',
                        buttonImage: '/css/common/fb_form/dtpic.gif',
                        buttonImageOnly: true,
                        changeMonth: true,
                        changeYear: true,
                        yearRange: '1940:2099' ,
                        minDate: '-70y',
                        maxDate: '0d'

                    });
                    $("#txtDOB").datepicker('option', {dateFormat: "yy-mm-dd"});
                    document.frmForm.txtDOB.value=d1;

                });*/


        var timeUrl = new Date().getDate();


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //if($("select#txtN_comid").val()=="-1"){$("select#txtN_comid").focus();return;}
                            if ($("input#txtV_usrid").val() == "") {
                                $("input#txtV_usrid").focus();
                                return;
                            }
                            if ($("select#txtN_accessusrtype").val() == "-1") {
                                $("select#txtN_accessusrtype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            if ($("select#txtV_title").val() == "") {
                                $("select#txtV_title").focus();
                                return;
                            }
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--
        function init() {
            CustomInit();

        }

        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN        _comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew) document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        function pageSubmit(type) {

            if (type == 'Save') {
                if (document.getElementById("txtN_mnuid").value == "-1") {
                    showDialogbox("Please Select Main Menu Name");
                    return;
                }
                else if (Trim(document.getElementById("txtV_itmname").value) == "") {
                    showDialogbox("Please Enter Sub Menu Name");
                    return;
                }

                else if (document.getElementById("txtV_url").value == "") {
                    showDialogbox("Please Enter Application URL");
                    return;
                }

                else if (Trim(document.getElementById("txtV_target").value) == "-1") {
                    showDialogbox("Please Select Window Open Location");
                    return;
                }
                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }
            else if (type == 'Delete') {
                document.frmForm.action = "applicationItemDelete.jsp?<%=timeURL%>&P_TYPE=1";
                document.frmForm.submit();

            }
            else if (type == 'Close') {
                window.location = "applicationItemList.jsp?<%=timeURL%>";

            }

        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

    </script>
    <style>
        .fbSelectBox {
            border: 1px solid #0C0;
            padding: 3px;
        }

        .selectbox {
            margin: 0px 5px 10px 0px;
            padding-left: 2px;
            font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
            font-size: 1em; /* Resize Font*/
            width: 190px; /* Resize Width */
            display: block;
            text-align: left;
            background: url('bg_select.png') right;
            cursor: pointer;
            border: 1px solid #D1E4F6;
            color: #333;
        }
    </style>
</head>
<body onload="init();">
<div class="form_header">System Application Details</div>
<div class="container">
    <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are mandatory.
    </div>
    <form name="frmForm" action="" method="post">

        <div class="col_half" style="width:95%;">
            <fieldset>
                <div class="row"><span class="label">Main Menu Name <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
                                <select name="txtN_mnuid" id="txtN_mnuid" class="styled1">
                                    <%out.print(str_n_mnuid_popList);%>
                                </select>
                            </span></div>
                <div class="row"><span class="label">Sub Menu Name <span style="color:#D50000">*</span> :</span> <span
                        class="txt_cont">
                                <input name="txtN_itmid" id="txtN_itmid" type="hidden"
                                       value="<%=subMenuItem.getN_itmid()%>"/>
                                <input name="txtV_itmname" id="txtV_itmname" title="Item Name" type="text"
                                       value="<%=subMenuItem.getV_itmname()%>"/>
                            </span></div>
                <div class="row"><span class="label">URL <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
                                <input name="txtV_url" id="txtV_url" style="width:400px;" title="URL" type="text"
                                       value="<%=subMenuItem.getV_url()%>"/>
                            </span></div>
                <div class="row"><span class="label">Window Open Location <span
                        style="color:#D50000">*</span> :</span><span class="txt_cont">
                                <select name="txtV_target" id="txtV_target" class="styled1">
                                    <option value="-1">Please select one</option>
                                    <option value="imain_frm">Main Window</option>
                                    <option value="_blank">New Window</option>
                                </select>
                            </span></div>
            </fieldset>
            <fieldset>
                <div id="paraListView"></div>
            </fieldset>
            <div class="but_container">
                <input type="button" name="cmdSave" id="cmdSave" value="Save Changes" <%=ADD_RIGHT%>
                       onclick="pageSubmit('Save')" class="button"/>
                <input type="button" name="cmdDelete" id="cmdDelete" value="Delete" <%=DELETE_RIGHT%>
                       onclick="pageSubmit('Delete')" class="button"/>
                <input type="button" name="cmdClose" id="cmdClose" value="Close" onclick="pageSubmit('Close')"
                       class="button"/>
            </div>
        </div>
        <% if (!isNewRecord) {
        %>
        <script type="text/javascript">
            document.getElementById("txtN_mnuid").value =<%=subMenuItem.getN_mnuid()%>;
            document.getElementById("txtV_target").value = "<%=subMenuItem.getV_target()%>";
        </script>
        <%}%>
        <div id="dialog" style="display:none;" title="${CompanyTitle}">
            <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
            <p id="dialog-email" class="textGrey"></p>
        </div>
    </form>
    <div class="spacer"> &nbsp;</div>
</div>
</body>
</html>
