<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@page import="com.misyn.mcms.admin.UserGroup" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserGroupManagerBean" class="com.misyn.mcms.admin.UserGroupManager" scope="application"/>
<%
    String ADD_RIGHT = "disabled='disabled'";
    String DELETE_RIGHT = "disabled='disabled'";
    if (((String) session.getAttribute("RIGHT_D")).equals("checked")) {
        DELETE_RIGHT = "";
    }


    long timeURL = System.currentTimeMillis();
    String URL = "userGroupResult.jsp?" + timeURL;


    int n_group_id = 1;


    List<UserGroup> list = null;
    UserGroup userGroup = null;
    String searchKey = "";

    boolean isNewRecord = true;
    try {
        isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
    } catch (Exception e) {
    }

    try {
        n_group_id = Integer.parseInt(request.getParameter("P_N_GROUP_ID"));
    } catch (Exception e) {
    }

    //out.print(n_group_id);

    if (isNewRecord) {
        if (((String) session.getAttribute("RIGHT_I")).equals("checked")) {
            ADD_RIGHT = "";
        }
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);


        userGroup = new UserGroup();


    } else {
        if (((String) session.getAttribute("RIGHT_M")).equals("checked")) {
            ADD_RIGHT = "";
        }
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);


        searchKey = "AND n_group_id=" + n_group_id;

        if (user.getN_accessusrtype() == 1) {
            userGroup = UserGroupManagerBean.getUserGroupList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 2) {
            userGroup = UserGroupManagerBean.getUserGroupList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 3) {
            userGroup = UserGroupManagerBean.getUserGroupList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 4) {
            userGroup = UserGroupManagerBean.getUserGroupList(searchKey).get(0);

        }


    }

            /*  if (list != null) {
            if (list.size() != 0) {
            find_user = list.get(0);
            }
            }*/


%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <script type="text/javascript" src="/script/common/ListBox.js"></script>
    <link href="/css/common/fb_form/fb_form_col.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/css/common/fb_form/custom-form-elements.js"></script>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript">
        /* $(function() {
                    //
                    //buttonImage: '/image/common/calendar.gif',
                    var d1=document.frmForm.txtDOB.value;
                    $("#txtDOB").datepicker({
                        showOn: 'button',
                        buttonImage: '/css/common/fb_form/dtpic.gif',
                        buttonImageOnly: true,
                        changeMonth: true,
                        changeYear: true,
                        yearRange: '1940:2099' ,
                        minDate: '-70y',
                        maxDate: '0d'

                    });
                    $("#txtDOB").datepicker('option', {dateFormat: "yy-mm-dd"});
                    document.frmForm.txtDOB.value=d1;

                });*/


        var timeUrl = new Date().getDate();


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //if($("select#txtN_comid").val()=="-1"){$("select#txtN_comid").focus();return;}
                            if ($("input#txtV_group_name").val() == "") {
                                $("input#txtV_group_name").focus();
                                return;
                            }
                            if ($("input#txtV_group_desc").val() == "") {
                                $("input#txtV_group_desc").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            if ($("select#txtV_title").val() == "") {
                                $("select#txtV_title").focus();
                                return;
                            }
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--
        function init() {
            CustomInit();

        }

        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN_comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew) document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        function pageSubmit(type) {

            if (type == 'Save') {
                if (Trim(document.getElementById("txtV_group_name").value) == "") {
                    showDialogbox("Please Enter User Group Name");
                    return;
                }
                else if (Trim(document.getElementById("txtV_group_desc").value) == "") {
                    showDialogbox("Please Enter User Group Description");
                    return;
                }


                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }
            else if (type == 'Delete') {
                document.frmForm.action = "userGroupDelete.jsp?<%=timeURL%>&P_TYPE=1";
                document.frmForm.submit();

            }
            else if (type == 'Close') {
                window.location = "userGroupList.jsp?<%=timeURL%>";

            }

        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

    </script>
    <style>
        .fbSelectBox {
            border: 1px solid #0C0;
            padding: 3px;
        }

        .selectbox {
            margin: 0px 5px 10px 0px;
            padding-left: 2px;
            font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
            font-size: 1em; /* Resize Font*/
            width: 190px; /* Resize Width */
            display: block;
            text-align: left;
            background: url('bg_select.png') right;
            cursor: pointer;
            border: 1px solid #D1E4F6;
            color: #333;
        }
    </style>
</head>
<body onload="init();">
<div class="form_header">User Group Details</div>
<div class="container">
    <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are mandatory.
    </div>
    <form name="frmForm" action="" method="post">
        <div class="col_half" style="width:95%;">
            <fieldset>
                <input name="txtN_group_id" id="txtN_group_id" type="hidden" value="<%=userGroup.getN_group_id()%>"/>
                <div class="row"><span class="label">User Group Name <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
          <input name="txtV_group_name" id="txtV_group_name" title="User Group name" type="text"
                 value="<%=userGroup.getV_group_name()%>"/>
          </span></div>
                <div class="row"><span class="label">User Group Description <span
                        style="color:#D50000">*</span> :</span> <span class="txt_cont">
         <input name="txtV_group_desc" id="txtV_group_desc" title="User Group description" type="text"
                value="<%=userGroup.getV_group_desc()%>"/>
          </span></div>
            </fieldset>

            <div class="but_container">
                <input type="button" name="cmdSave" id="cmdSave" value="Save Changes" <%=ADD_RIGHT%>
                       onclick="pageSubmit('Save')" class="button"/>
                <input type="button" name="cmdDelete" id="cmdDelete" value="Delete" <%=DELETE_RIGHT%>
                       onclick="pageSubmit('Delete')" class="button"/>
                <input type="button" name="cmdClose" id="cmdClose" value="Close" onclick="pageSubmit('Close')"
                       class="button"/>
            </div>
        </div>

        <div id="dialog" style="display:none;" title="${CompanyTitle}">
            <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
            <p id="dialog-email" class="textGrey"></p>
        </div>
    </form>
    <div class="spacer"> &nbsp;</div>
</div>
</body>
</html>
