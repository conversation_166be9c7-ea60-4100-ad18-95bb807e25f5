<%--
    Document   : applicationItemResult
    Created on : Dec 24, 2010, 6:46:15 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : <PERSON>lum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.UserGroup" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="UserGroupManagerBean" class="com.misyn.mcms.admin.UserGroupManager" scope="application"/>

<%
    long timeURL = System.currentTimeMillis();
    int n_prgid = 1;
    UserGroup userGroup = new UserGroup();


    try {
        userGroup.setN_group_id(Integer.parseInt(request.getParameter("txtN_group_id")));
    } catch (Exception e) {
    }

    try {
        userGroup.setV_group_name(request.getParameter("txtV_group_name"));
    } catch (Exception e) {
    }
    try {
        userGroup.setV_group_desc(request.getParameter("txtV_group_desc"));
    } catch (Exception e) {
    }


    int result = UserGroupManagerBean.saveUserGroup(userGroup);

    if (result > 0) {
        response.sendRedirect("userGroupList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
    } else {
        response.sendRedirect("userGroupList.jsp?" + timeURL + "&P_ERROR=Can not be save");
    }

%>


