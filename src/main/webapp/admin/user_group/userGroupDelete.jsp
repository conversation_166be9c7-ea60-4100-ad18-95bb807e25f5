<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.UserGroup" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserGroupManagerBean" class="com.misyn.mcms.admin.UserGroupManager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%

    boolean isSearchPage = false;
    try {
        isSearchPage = (Boolean) session.getAttribute("ISSEARCH_PAGE");
    } catch (Exception e) {
    }
    UserGroup m_UserGroup = null;
    List<UserGroup> userGroupList = new ArrayList<UserGroup>();
    String chkvalue = "";

    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) {
            chkvalue = "";
        }
        if (chkvalue.equalsIgnoreCase("checked")) {
            m_UserGroup = new UserGroup();

            try {
                m_UserGroup.setN_group_id(Integer.parseInt(request.getParameter("P_N_GROUP_ID" + x)));
            } catch (Exception e) {
            }


            userGroupList.add(m_UserGroup);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }

    //================Start Delete only one recode=================
    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("P_TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {
        m_UserGroup = new UserGroup();
        try {
            m_UserGroup.setN_group_id(Integer.parseInt(request.getParameter("txtN_group_id")));
        } catch (Exception e) {
        }


        userGroupList.add(m_UserGroup);
    }
    //================End Delete only one recode=================

    //out.print("Size "+rolePrivilegeList.size());
    if (userGroupList.size() > 0) {
        UserGroupManagerBean.deleteUserGroup(userGroupList);
    }
    if (isSearchPage != true) {
        response.sendRedirect("userGroupList.jsp?P_ERROR=" + UserGroupManagerBean.getMsg());
    } else {
        response.sendRedirect("userGroupList.jsp?P_ERROR=" + UserGroupManagerBean.getMsg());
    }
%>


