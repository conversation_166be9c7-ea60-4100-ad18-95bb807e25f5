<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.roleFacility.RolePrivilege" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="RolePrivilegeManagerBean" class="com.misyn.mcms.roleFacility.RolePrivilegeManager"
             scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%
    RolePrivilege rolePrivilege = null;
    List<RolePrivilege> rolePrivilegeList = new ArrayList<RolePrivilege>();
    String chkvalue = "";


    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) chkvalue = "";
        if (chkvalue.equalsIgnoreCase("checked")) {
            rolePrivilege = new RolePrivilege();
            rolePrivilege.setN_prgid(user.getN_prgid());
            try {
                rolePrivilege.setN_comid(Integer.parseInt(request.getParameter("P_N_COMID" + x)));
            } catch (Exception e) {
            }

            try {
                rolePrivilege.setN_usrtype(Integer.parseInt(request.getParameter("P_N_USRTYPE" + x)));
            } catch (Exception e) {
            }
            rolePrivilegeList.add(rolePrivilege);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }
    //out.print("Size "+rolePrivilegeList.size());
    if (rolePrivilegeList.size() > 0) {
        RolePrivilegeManagerBean.deleteRolePrivilege(rolePrivilegeList);
    }
    response.sendRedirect("rolePrevList.jsp?P_ERROR=" + RolePrivilegeManagerBean.getMsg());
%>


