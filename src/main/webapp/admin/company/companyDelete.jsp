<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.Company" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="CompanyManagerBean" class="com.misyn.mcms.admin.CompanyManager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%
    Company company = null;
    List<Company> companyList = new ArrayList<Company>();
    String chkvalue = "";


    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) chkvalue = "";
        if (chkvalue.equalsIgnoreCase("checked")) {
            company = new Company();

            try {
                company.setN_comid(Integer.parseInt(request.getParameter("P_N_COMID" + x)));
            } catch (Exception e) {
            }


            companyList.add(company);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }
    //out.print("Size "+rolePrivilegeList.size());
    if (companyList.size() > 0) {
        CompanyManagerBean.deleteCompany(companyList);
    }
    response.sendRedirect("companyList.jsp?P_ERROR=" + CompanyManagerBean.getMsg());
%>


