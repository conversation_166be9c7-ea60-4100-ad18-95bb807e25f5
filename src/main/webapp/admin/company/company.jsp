<%--
    Document   : rolePrev
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 17, 2010, 10:02:58 AM
    Author     : <PERSON><PERSON>
--%>

<%@page import="com.misyn.mcms.admin.Company" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="com.misyn.mcms.roleFacility.RolePrivilege" %>
<%@page import="java.util.List" %>

<jsp:useBean id="CompanyPrivilegManagerBean" class="com.misyn.mcms.roleFacility.CompanyPrivilegeManager"
             scope="application"/>
<jsp:useBean id="CompanyManagerBean" class="com.misyn.mcms.admin.CompanyManager" scope="application"/>

<%@page contentType="text/html" pageEncoding="UTF-8" %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>


    <%!String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";%>

    <%

        String ADD_RIGHT = "disabled='disabled'";
        String DELETE_RIGHT = "disabled='disabled'";
        if (((String) session.getAttribute("RIGHT_D")).equals("checked")) {
            DELETE_RIGHT = "";
        }

        String readonly = "";
        long timeURL = System.currentTimeMillis();
        int n_usrtype = -1;
        int n_comid = -1;
        RolePrivilege rolePrivilege = null;
        Company company = null;
        int tempMenuId = -1;
        int divId = -1;
        boolean isNextRecLast = false;
        String URL = "companyResult.jsp?" + timeURL;


        try {
            n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
        } catch (Exception e) {
        }


        boolean isNewRecord = true;
        try {
            isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
        } catch (Exception e) {
        }


        int size = 0;
        List<RolePrivilege> list = null;

        try {
            if (isNewRecord) {
                if (((String) session.getAttribute("RIGHT_I")).equals("checked")) ADD_RIGHT = "";
                if (user.getN_accessusrtype() == 1) {//SupAdmin
                } else if (user.getN_accessusrtype() == 2) {    //Admin
                } else if (user.getN_accessusrtype() == 3) { //AgentAdmin
                    n_comid = user.getN_comid();


                } else if (user.getN_accessusrtype() == 4) { //InternalAdmin
                    n_comid = user.getN_comid();


                }
                company = new Company();
                list = CompanyPrivilegManagerBean.getRolePrivilege_New_List(user);
                size = list.size();
                //out.println("New------->");

            } else {
                if (((String) session.getAttribute("RIGHT_M")).equals("checked")) ADD_RIGHT = "";
                readonly = "readonly";
                try {
                    n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
                } catch (Exception e) {
                }


                company = CompanyManagerBean.getCompanyList(user, n_comid).get(0);
                list = CompanyPrivilegManagerBean.getRolePrivilege_Modify_List(user, n_comid);
                size = list.size();
                //out.println("Modify------->");
            }
        } catch (Exception e) {
        }


    %>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">

    <link href="../../css/common/accordion_form.css" rel="stylesheet" type="text/css"/>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>


    <script type="text/javascript">
        var timeUrl = new Date().getDate();

        $(function () {
            $("#accordion").accordion({
                //icons: {header: "ui-icon-circle-arrow-e",headerSelected: "ui-icon-circle-arrow-s"},
                autoHeight: false,
                active: false,
                active: 0,
                alwaysOpen: false
            });
        });

        function setAccordion(idno) {

            $(function () {
                $("#accordion" + idno).accordion({
                    //icons: {header: "ui-icon-circle-arrow-e",headerSelected: "ui-icon-circle-arrow-s"},
                    autoHeight: false,
                    collapsible: true,
                    active: false,
                    alwaysOpen: false
                });
            });
        }


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            if ($("input#txtN_comid").val() == "") {
                                $("input#txtN_comid").focus();
                                return;
                            }
                            if ($("input#txtV_description").val() == "") {
                                $("input#txtV_description").focus();
                                return;
                            }
                            if ($("input#txtV_add1").val() == "") {
                                $("input#txtV_add1").focus();
                                return;
                            }
                            if ($("input#txtV_telno").val() == "") {
                                $("input#txtV_telno").focus();
                                return;
                            }
                            if ($("input#txtV_contperson").val() == "") {
                                $("input#txtV_contperson").focus();
                                return;
                            }
                            if ($("input#txtV_contemail").val() == "") {
                                $("input#txtV_contemail").focus();
                                return;
                            }

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--


        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN_comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew) document.frmForm.action = "rolePrev.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else document.frmForm.action = "rolePrev.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        var ld = (document.all);

        function pageSubmit(type) {

            if (type == 'Save') {
                if (document.getElementById("txtN_comid").value == "") {
                    showDialogbox("Please Enter Company Code");
                    return;
                }
                else if (document.getElementById("txtV_description").value == "") {
                    showDialogbox("Please Enter Description");
                    return;
                }
                else if (document.getElementById("txtV_add1").value == "") {
                    showDialogbox("Please Enter Address");
                    return;
                }
                else if (document.getElementById("txtV_telno").value == "") {
                    showDialogbox("Please Enter Contact Number");
                    return;
                }
                else if (document.getElementById("txtV_contperson").value == "") {
                    showDialogbox("Please Enter Contact Person");
                    return;
                }
                else if (document.getElementById("txtV_contemail").value == "") {
                    showDialogbox("Please Enter Email");
                    return;
                }

                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }
            else if (type == 'Close') {
                window.location = "companyList.jsp?<%=timeURL%>";
            }

        }

        function init() {

            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";

        }


    </script>


    <title>${CompanyTitle}</title>
    <style type="text/css">
        <!--
        body {
            margin-top: 0px;
        }

        -->
    </style>
</head>
<body onload="init();">


<form name="frmForm" action="" method="post">
    <div id="frmheader">
        Company Details
    </div>

    <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are mandatory.
    </div>

    <div id="accordion" style="left:5px; width:100%;">
        <h3><a href="#">Company Details</a></h3>
        <div style="height:220px;">
            <div id="DIV_LABEL" class="lableDiv">
                <div id="lbl" class="label1">Company Code<span style="color:#FF0000">*</span> :</div>
                <div id="txt" class="label2">
                    <input name="txtN_comid" id="txtN_comid" type="hidden" class="textFields"
                           value="<%=company.getN_comid()%>"/>
                    <input name="txtV_comcode" id="txtV_comcode" type="text" class="textFields" <%=readonly%>
                           value="<%=company.getV_comCode()%>"/>

                </div>

            </div>

            <div id="DIV_LABEL" class="lableDiv">
                <div id="lbl" class="label1">Description <span style="color:#FF0000">*</span> :</div>
                <div id="txt" class="label2">
                    <input name="txtV_description" id="txtV_description" type="text" class="textFields"
                           style="width:200px;" value="<%=company.getV_description()%>"/>
                </div>
            </div>
            <div id="DIV_LABEL" class="lableDiv">
                <div id="lbl" class="label1">Address <span style="color:#FF0000">*</span> :</div>
                <div id="txt" class="label2">
                    <input name="txtV_add1" id="txtV_add1" type="text" class="textFields" style="width:200px;"
                           value="<%=company.getV_add1()%>"/>
                </div>
            </div>
            <div id="DIV_LABEL" class="lableDiv">
                <div id="lbl" class="label1"></div>
                <div id="txt" class="label2">
                    <input name="txtV_add2" id="txtV_add2" type="text" class="textFields" style="width:200px;"
                           value="<%=company.getV_add2()%>"/>
                </div>
            </div>
            <div id="DIV_LABEL" class="lableDiv">
                <div id="lbl" class="label1">Phone<span style="color:#FF0000">*</span> :</div>
                <div id="txt" class="label2">
                    <input name="txtV_telno" id="txtV_telno" type="text" class="textFields"
                           value="<%=company.getV_telno()%>"/>
                </div>
            </div>
            <div id="DIV_LABEL" class="lableDiv">
                <div id="lbl" class="label1">Contact person<span style="color:#FF0000">*</span> :</div>
                <div id="txt" class="label2">
                    <input name="txtV_contperson" id="txtV_contperson" type="text" class="textFields"
                           value="<%=company.getV_contperson()%>"/>
                </div>
            </div>
            <div id="DIV_LABEL" class="lableDiv">
                <div id="lbl" class="label1">Email<span style="color:#FF0000">*</span> :</div>
                <div id="txt" class="label2">
                    <input name="txtV_contemail" id="txtV_contemail" type="text" class="textFields" style="width:200px;"
                           value="<%=company.getV_contemail()%>"/>
                </div>
            </div>

            <div style="position:absolute; width:60px; left:435px; top:10px;">
                <input name="cmdSave" id="cmdSave" type="button" <%=butClass%> value="Save" <%=ADD_RIGHT%>
                       onclick="pageSubmit('Save')">
                <div style="padding-top:2px;">
                    <input name="cmdClose" id="cmdClose" type="button" <%=butClass%> value="Close"
                           onclick="pageSubmit('Close')">
                </div>

            </div>
            <div></div>

        </div>
    </div>
    <div id="spacer" style="height:5px;">&nbsp;</div>
    <input name="hideRecCount" type="hidden" value="<%=size%>"/>
    <% for (int i = 0; i < size; i++) {
        rolePrivilege = list.get(i);

        if (tempMenuId != rolePrivilege.getN_mnuid()) {
            divId = rolePrivilege.getN_mnuid();
    %>

    <div id="accordion<%=divId%>" style="left:5px; width:100%; ">

        <h3><a href="#"><%=rolePrivilege.getV_mnuname()%>
        </a></h3>
        <div>
            <table width="600" border="0" cellspacing="1">
                <thead>
                <tr>
                    <th width="200" class="MainMenu_Heading">Name</th>
                    <th width="150" class="MainMenu_Heading">View</th>
                    <th width="150" class="MainMenu_Heading">Add</th>
                    <th width="150" class="MainMenu_Heading">Modify</th>
                    <th width="150" class="MainMenu_Heading">Delete</th>
                    <th width="150" class="MainMenu_Heading">Grant</th>
                </tr>
                </thead>

                <tbody>
                <% }%>
                <tr>
                    <input name="hide_n_itmid<%=i%>" type="hidden" value="<%=rolePrivilege.getN_itmid()%>"/>
                    <td class="SubMenu_Heading"><%=rolePrivilege.getV_itmname()%>
                    </td>
                    <td class="SubMenu_Heading"><input name="chkboxView<%=i%>" <%=rolePrivilege.getV_view()%>
                                                       type="checkbox" value="checked"></td>
                    <td class="SubMenu_Heading"><input name="chkboxAdd<%=i%>"  <%=rolePrivilege.getV_input()%>
                                                       type="checkbox" value="checked"></td>
                    <td class="SubMenu_Heading"><input name="chkboxModify<%=i%>" <%=rolePrivilege.getV_modify()%>
                                                       type="checkbox" value="checked"></td>
                    <td class="SubMenu_Heading"><input name="chkboxDelete<%=i%>" <%=rolePrivilege.getV_delete()%>
                                                       type="checkbox" value="checked"></td>
                    <td class="SubMenu_Heading"><input name="chkboxGrant<%=i%>" <%=rolePrivilege.getV_grant()%>
                                                       type="checkbox" value="checked"></td>
                </tr>
                <%
                    try {
                        if (rolePrivilege.getN_mnuid() != list.get(i + 1).getN_mnuid()) {
                            isNextRecLast = true;
                        }
                    } catch (Exception e) {
                        isNextRecLast = true;
                    }
                    if (isNextRecLast) {
                        isNextRecLast = false;
                %>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        setAccordion(<%=divId%>);
    </script>
    <%
        }
        tempMenuId = rolePrivilege.getN_mnuid();
    %>
    <input name="hide_n_mnuid<%=i%>" type="hidden" value="<%=rolePrivilege.getN_mnuid()%>"/>
    <%
        }
    %>


</form>
<div id="dialog" style="display:none;" title="${CompanyTitle}">
    <p><span class="ui-icon ui-icon-info" style="float:left; margin:8px 7px 0 0;"></span></p>
    <p id="dialog-email" class="textGrey" style="padding-top:10px; font-size:12px"></p>
</div>
</body>
</html>
