<%--
    Document   : rolePrevResult
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 19, 2010, 2:34:45 PM
    Author     : <PERSON><PERSON>
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="com.misyn.mcms.admin.Company" %>
<%@page import="com.misyn.mcms.roleFacility.RolePrivilege" %>
<%@page import="java.util.ArrayList" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@page import="java.util.List" %>
<jsp:useBean id="RolePrivilegeManagerBean" class="com.misyn.mcms.roleFacility.RolePrivilegeManager"
             scope="application"/>
<jsp:useBean id="CompanyManagerBean" class="com.misyn.mcms.admin.CompanyManager" scope="application"/>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">

<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="/css/common/loding.css" rel="stylesheet" type="text/css"/>
    <title>${CompanyTitle}</title>
</head>
<body>
<div id="loading" class="lodingImage" style="position:absolute;top:120px;">
    <img style="z-index:1002" alt="Loading..." src="/image/common/ajax-loader6.gif" border=0>
    <div class="lodingText">RECORD IS IN PROGRESS ...</div>
    <div class="labelText" align="center"><p>Don't close this browser window or navigate until the progress is
        complete</p></div>
</div>
<div id="cell1" class="innerDivBg"></div>

<%
    long timeURL = System.currentTimeMillis();
    Company company = new Company();
    RolePrivilege rolePrivilege = null;
    List<RolePrivilege> rolePrivilegeList = new ArrayList<RolePrivilege>();


    int recCount = 0;
    try {
        recCount = Integer.parseInt(request.getParameter("hideRecCount"));
    } catch (Exception e) {
    }

    int n_comid = 0;
    try {
        n_comid = Integer.parseInt(request.getParameter("txtN_comid"));
        company.setN_comid(n_comid);
    } catch (Exception e) {
    }

    try {
        company.setV_comCode(request.getParameter("txtV_comcode"));
        company.setV_description(request.getParameter("txtV_description"));
        company.setV_add1(request.getParameter("txtV_add1"));
        company.setV_add2(request.getParameter("txtV_add2"));
        company.setV_telno(request.getParameter("txtV_telno"));
        company.setV_contperson(request.getParameter("txtV_contperson"));
        company.setV_contemail(request.getParameter("txtV_contemail"));
        company.setV_comstatus("Y");
        company.setV_inpuser(user.getV_usrid());

    } catch (Exception e) {
    }


    for (int i = 0; i < recCount; i++) {
        rolePrivilege = new RolePrivilege();
        rolePrivilege.setN_comid(n_comid);
        rolePrivilege.setN_prgid(1);
        rolePrivilege.setV_inpuser(user.getV_usrid());

        try {
            rolePrivilege.setN_mnuid(Integer.parseInt(request.getParameter("hide_n_mnuid" + i)));
        } catch (Exception e) {
        }
        try {
            rolePrivilege.setN_itmid(Integer.parseInt(request.getParameter("hide_n_itmid" + i)));
        } catch (Exception e) {
        }


        rolePrivilege.setV_view(request.getParameter("chkboxView" + i));
        rolePrivilege.setV_input(request.getParameter("chkboxAdd" + i));
        rolePrivilege.setV_modify(request.getParameter("chkboxModify" + i));
        rolePrivilege.setV_delete(request.getParameter("chkboxDelete" + i));
        rolePrivilege.setV_grant(request.getParameter("chkboxGrant" + i));


        rolePrivilegeList.add(rolePrivilege);

                        /* String mnid=request.getParameter("hide_n_mnuid"+i);
                        String itid=request.getParameter("hide_n_itmid"+i);
                        String view=request.getParameter("chkboxView" + i);
                        out.print("</br> Menu id "+mnid+ " Item Id :"+itid+ "View "+view);*/
    }
    // if (rolePrivilegeList.size() > 0) {
    int r = CompanyManagerBean.saveCompany(company, rolePrivilegeList);
    if (r > 0) {
        response.sendRedirect("companyList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
    } else {
        response.sendRedirect("companyList.jsp?" + timeURL + "&P_ERROR=Can not be save " + company.getErrorMessage());
    }
    //  }
%>
</body>
</html>
