<%--
    Document   : applicationItemResult
    Created on : Dec 24, 2010, 6:46:15 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : <PERSON>lum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.Parameter" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="ParameterManagerBean" class="com.misyn.mcms.admin.ParameterManager" scope="application"/>

<%
    long timeURL = System.currentTimeMillis();
    int n_prgid = 1;
    Parameter parameter = new Parameter();


    try {
        parameter.setParaCode(request.getParameter("txtParaCode"));
    } catch (Exception e) {
    }
    try {
        parameter.setDescription(request.getParameter("txtDescription"));
    } catch (Exception e) {
    }
    int result = ParameterManagerBean.saveParameter(parameter, user);
    if (result == 0) {
        response.sendRedirect("parameterItemList.jsp?" + timeURL + "&P_ERROR=Can not be save, " + parameter.getErrorMessage());
    } else if (result > 0) {
        response.sendRedirect("parameterItemList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
    } else {
        response.sendRedirect("parameterItemList.jsp?" + timeURL + "&P_ERROR=Can not be save");
    }

%>


