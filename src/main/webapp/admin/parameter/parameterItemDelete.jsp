<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.Parameter" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="ParameterManagerBean" class="com.misyn.mcms.admin.ParameterManager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%
    Parameter m_Parameter = null;
    List<Parameter> parameterList = new ArrayList<Parameter>();
    String chkvalue = "";


    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) chkvalue = "";
        if (chkvalue.equalsIgnoreCase("checked")) {
            m_Parameter = new Parameter();

            try {
                m_Parameter.setParaCode(request.getParameter("P_PARACODE" + x));
            } catch (Exception e) {
            }

            parameterList.add(m_Parameter);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }


    //================Start Delete only one recode=================
    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("P_TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {
        m_Parameter = new Parameter();
        try {
            m_Parameter.setParaCode(request.getParameter("txtParaCode"));
        } catch (Exception e) {
        }

        parameterList.add(m_Parameter);
    }
    //================End Delete only one recode=================
    //out.print("Size "+rolePrivilegeList.size());
    if (parameterList.size() > 0) {
        ParameterManagerBean.deleteParameter(parameterList);
    }
    response.sendRedirect("parameterItemList.jsp?P_ERROR=" + ParameterManagerBean.getMsg());
%>


