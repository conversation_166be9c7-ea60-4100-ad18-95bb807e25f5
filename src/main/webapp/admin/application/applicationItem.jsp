<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>
<%@page import="com.misyn.mcms.admin.ApplicationParameter" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@page import="com.misyn.mcms.admin.SubMenuItem" %>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@page import="java.util.List" %>
<jsp:useBean id="SubMenuItemManagerBean" class="com.misyn.mcms.admin.SubMenuItemManager" scope="application"/>
<%
    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    String readOnlyClass = "";

    long timeURL = System.currentTimeMillis();
    String URL = "applicationItemResult.jsp?" + timeURL;


    int n_prgid = 1;
    int n_mnuid = -1;
    int n_itmid = -1;

    String str_n_mnuid_popList = "";
    List<SubMenuItem> list = null;
    SubMenuItem subMenuItem = null;

    List<ApplicationParameter> appParameteList = null;


    String searchKey = "";

    boolean isNewRecord = true;
    try {
        isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
    } catch (Exception e) {
    }

    try {
        n_prgid = Integer.parseInt(request.getParameter("P_N_PRGID"));
    } catch (Exception e) {
    }

    try {
        n_mnuid = Integer.parseInt(request.getParameter("P_N_MNUID"));
    } catch (Exception e) {
    }

    try {
        n_itmid = Integer.parseInt(request.getParameter("P_N_ITMID"));
    } catch (Exception e) {
    }


    if (isNewRecord) {

        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        str_n_mnuid_popList = "<option value='-1' >Please select one</option>\n" + DbRecordCommonFunction.getInstance().
                getPopupList("mnu_mst ", "n_mnuid", "v_mnuname", "v_apptype='Y'", "");

        subMenuItem = new SubMenuItem();
        appParameteList = SubMenuItemManagerBean.getApplicationParameterList_New("");

    } else {
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        str_n_mnuid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("mnu_mst ", "n_mnuid", "v_mnuname", "n_prgid=" + n_prgid + " AND n_mnuid=" + n_mnuid + " AND v_apptype='Y'", "");

        readOnlyClass = " readonly class=\"textReadOnly\" ";
        searchKey = "n_prgid=" + n_prgid + " AND n_mnuid=" + n_mnuid + " AND n_itmid=" + n_itmid;

        if (user.getN_accessusrtype() == 1) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 2) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 3) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        } else if (user.getN_accessusrtype() == 4) {
            subMenuItem = SubMenuItemManagerBean.getSubMenuItemList(searchKey).get(0);

        }
        appParameteList = SubMenuItemManagerBean.getApplicationParameterList_Modify(n_prgid, n_mnuid, n_itmid, "");

    }


%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/common/ListBox.js"></script>
    <link href="${pageContext.request.contextPath}/css/common/fb_form/fb_form_col.css" rel="stylesheet"
          type="text/css"/>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/css/common/fb_form/custom-form-elements.js"></script>
    <link href="${pageContext.request.contextPath}/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css"
          rel="stylesheet" type="text/css"/>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript">
        /* $(function() {
             //
             //buttonImage: '/image/common/calendar.gif',
             var d1=document.frmForm.txtDOB.value;
             $("#txtDOB").datepicker({
                 showOn: 'button',
                 buttonImage: '/css/common/fb_form/dtpic.gif',
                 buttonImageOnly: true,
                 changeMonth: true,
                 changeYear: true,
                 yearRange: '1940:2099' ,
                 minDate: '-70y',
                 maxDate: '0d'

             });
             $("#txtDOB").datepicker('option', {dateFormat: "yy-mm-dd"});
             document.frmForm.txtDOB.value=d1;

         });*/


        var timeUrl = new Date().getDate();


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //if($("select#txtN_comid").val()=="-1"){$("select#txtN_comid").focus();return;}
                            if ($("select#txtN_mnuid").val() == "-1") {
                                $("select#txtN_mnuid").focus();
                                return;
                            }
                            if ($("input#txtV_itmname").val() == "") {
                                $("input#txtV_itmname").focus();
                                return;
                            }
                            if ($("input#txtV_url").val() == "") {
                                $("input#txtV_url").focus();
                                return;
                            }
                            if ($("select#txtV_target").val() == "-1") {
                                $("select#txtV_target").focus();
                                return;
                            }

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--
        function init() {
            CustomInit();  //Select BOX Style
        }

        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN_comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew) document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        function pageSubmit(type) {

            if (type == 'Save') {
                if (document.getElementById("txtN_mnuid").value == "-1") {
                    showDialogbox("Please Select Main Menu Name");
                    return;
                }
                else if (Trim(document.getElementById("txtV_itmname").value) == "") {
                    showDialogbox("Please Enter Sub Menu Name");
                    return;
                }

                else if (document.getElementById("txtV_url").value == "") {
                    showDialogbox("Please Enter Application URL");
                    return;
                }

                else if (Trim(document.getElementById("txtV_target").value) == "-1") {
                    showDialogbox("Please Select Window Open Location");
                    return;
                }


                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }
            else if (type == 'Delete') {
                document.frmForm.action = "applicationItemDelete.jsp?<%=timeURL%>&P_TYPE=1";
                document.frmForm.submit();

            }
            else if (type == 'Close') {
                window.location = "applicationItemList.jsp?<%=timeURL%>";

            }

        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

        //=======================Application Parameter List============================

        function AddItemList(cmbname, Text, Value) {
            // Create an Option object
            var opt = document.createElement("option");

            // Add an Option object to Drop Down/List Box
            document.getElementById(cmbname).options.add(opt);        // Assign text and value to Option object
            opt.text = Text;
            opt.value = Value;

        }

        function removeItemsList(selectbox) {
            var i;
            for (i = selectbox.options.length - 1; i >= 0; i--) {
                //selectbox.options.remove(i);
                selectbox.remove(i);
            }
        }


        function ParaFieldValidate(cnt) {
            var b = true;
            var x1 = null;
            var s1 = null;
            var x2 = null;
            var s2 = null;
            var x3 = null;
            var s3 = null;
            var cmbBoxName1 = "";
            var cmbBoxName2 = "";
            var cmbBoxName3 = "";
            var txt = "";

            for (i = 1; i <= cnt; i++) {
                cmbBoxName1 = "col" + i + "2";
                cmbBoxName2 = "col" + i + "3";
                cmbBoxName3 = "col" + i + "4";
                txt = "col" + i + "5";

                x1 = document.getElementById(cmbBoxName1);
                s1 = x1.options[x1.selectedIndex].text;

                x2 = document.getElementById(cmbBoxName2);
                s2 = x2.options[x2.selectedIndex].text;

                x3 = document.getElementById(cmbBoxName3);
                s3 = x3.options[x3.selectedIndex].text;

                if (s1 == 'Static Field') {
                    if (document.getElementById(txt).value == "") {
                        b = false;
                        document.getElementById(txt).focus();
                        break;
                    }
                }
                else if (s1 == 'Dynamic Field') {
                    if (s2 == 'Please select one') {
                        b = false;
                        x2.focus();
                        break;
                    }
                    else {
                        if (s3 == 'Please select one') {
                            b = false;
                            x3.focus();
                            break;
                        }
                    }
                }

            }
            //alert(b);
            return b;
        }

        function checkEqualItems(cnt) {
            var b = true;
            var x1 = null;
            var s1 = null;
            var x2 = null;
            var s2 = null;
            var cmbBoxName1 = "";
            var cmbBoxName2 = "";
            for (i = 1; i < cnt; i++) {
                cmbBoxName1 = "col" + i + "4";
                x1 = document.getElementById(cmbBoxName1);
                s1 = x1.options[x1.selectedIndex].text;

                for (j = (i + 1); j <= cnt; j++) {
                    cmbBoxName2 = "col" + j + "4";
                    x2 = document.getElementById(cmbBoxName2);
                    s2 = x2.options[x2.selectedIndex].text;
                    if (s1 != 'Please select one' && s1 != 'paravalue') {
                        if (s1 == s2) {
                            //x1.focus();
                            b = false;
                            x1.focus();
                            break;
                        }
                    }
                }
            }
            //alert(b);
            return b;
        }


        function clickTxtSelectType(id) {
            var val = document.getElementById("txtSelectType" + id).value;
            document.getElementById("txtTableName" + id).value = "Please select one";

            //removeItemsList(document.getElementById("txtFieldNames"+id));
            //AddItemList("txtFieldNames"+id,'Please select one','Please select one');

            document.getElementById("txtFieldNames" + id).value = "Please select one";
            document.getElementById("txtStaticValue" + id).value = "";

            if (val == "Static Field") {
                document.getElementById("txtStaticValue" + id).style.display = "block";
                document.getElementById("txtTableName" + id).style.display = "none";
                document.getElementById("txtFieldNames" + id).style.display = "none";
            }
            else if (val == "Dynamic Field") {
                document.getElementById("txtTableName" + id).style.display = "block";
                document.getElementById("txtFieldNames" + id).style.display = "block";
                document.getElementById("txtStaticValue" + id).style.display = "none";
            }
            else {
                document.getElementById("txtStaticValue" + id).style.display = "none";
                document.getElementById("txtTableName" + id).style.display = "none";
                document.getElementById("txtFieldNames" + id).style.display = "none";
            }
        }


        function setListValues(cmbBoxName1, cmbBoxName2) {
            var x1 = document.getElementById(cmbBoxName1);
            var selTbl = x1.options[x1.selectedIndex].text;
            var a = null;

            if (selTbl == 'User Master') {
                a = ['Please select one', 'n_usrcode', 'n_comid', 'v_usrid', 'v_password', 'v_oldpassword', 'v_usrtypes', 'n_accessusrtype', 'v_title', 'v_firstname', 'v_lastname', 'v_address1', 'v_address2', 'v_email', 'v_land_phone', 'v_mobile', 'v_fax', 'v_nic', 'v_emp_no', 'n_brid', 'd_activedate', 'd_expirydate', 'v_usrstatus', 'v_oldusrstatus', 'd_lastlogindate', 'd_lastlogintime', 'n_atmptno', 'd_pwchgdate', 'd_pwprtdate', 'v_firstlogin', 'd_uidlockdate', 'd_uidlocktime', 'v_anymodify', 'v_group_ids', 'v_group_ids_desc', 'v_usrtype_desc', 'v_inpstat', 'v_inpuser', 'd_inptime'];
                removeItemsList(document.getElementById(cmbBoxName2));
                for (i = 0; i < a.length; i++)
                    AddItemList(cmbBoxName2, a[i], a[i]);
            }
            else if (selTbl == 'User Parameter') {
                //usrcode, paraCode, paravalue, status, inpstat, inpuser, inptime, auth1stat, auth1user, auth1time, auth2stat, auth2user, auth2time
                a = ['Please select one', 'paravalue'];
                removeItemsList(document.getElementById(cmbBoxName2));
                for (i = 0; i < a.length; i++)
                    AddItemList(cmbBoxName2, a[i], a[i]);
            }
            else {
                a = ['Please select one'];
                removeItemsList(document.getElementById(cmbBoxName2));
                for (i = 0; i < a.length; i++)
                    AddItemList(cmbBoxName2, a[i], a[i]);
            }


        }

        function setTableNameValues(id, tableNameValue) {
            document.getElementById("txtTableName" + id).value = tableNameValue;

        }

        function setFieldNameValues(id, fieldNameValue) {
            document.getElementById("txtFieldNames" + id).value = fieldNameValue;
        }

        function setVisibleSelectBox(id) {
            var val = document.getElementById("txtSelectType" + id).value;


            if (val == "Static Field") {
                document.getElementById("txtStaticValue" + id).style.display = "block";
                document.getElementById("txtTableName" + id).style.display = "none";
                document.getElementById("txtFieldNames" + id).style.display = "none";
            }
            else if (val == "Dynamic Field") {
                document.getElementById("txtTableName" + id).style.display = "block";
                document.getElementById("txtFieldNames" + id).style.display = "block";
                document.getElementById("txtStaticValue" + id).style.display = "none";
            }
            else {
                document.getElementById("txtStaticValue" + id).style.display = "none";
                document.getElementById("txtTableName" + id).style.display = "none";
                document.getElementById("txtFieldNames" + id).style.display = "none";
            }
        }


    </script>
    <style>
        .fbSelectBox {
            border: 1px solid #0C0;
            padding: 3px;
        }

        .selectbox {
            margin: 0px 5px 10px 0px;
            padding-left: 2px;
            font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
            font-size: 1em; /* Resize Font*/
            width: 190px; /* Resize Width */
            display: block;
            text-align: left;
            background: url('bg_select.png') right;
            cursor: pointer;
            border: 1px solid #D1E4F6;
            color: #333;
        }
    </style>
</head>
<body onload="init();">
<div class="form_header">Application Details</div>
<div class="container">
    <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are mandatory.
    </div>
    <form name="frmForm" action="" method="post">
        <div class="col_half" style="width:95%;">
            <fieldset>
                <div class="row"><span class="label">Main Menu Name <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
          <select name="txtN_mnuid" id="txtN_mnuid" class="styled1">
            <%out.print(str_n_mnuid_popList);%>
          </select>
          </span></div>
                <div class="row"><span class="label">Sub Menu Name <span style="color:#D50000">*</span> :</span> <span
                        class="txt_cont">
          <input name="txtN_itmid" id="txtN_itmid" type="hidden" value="<%=subMenuItem.getN_itmid() %>"/>
          <input name="txtV_itmname" id="txtV_itmname" title="Item Name" type="text"
                 value="<%=subMenuItem.getV_itmname()%>"/>
          </span></div>
                <div class="row"><span class="label">URL <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
          <input name="txtV_url" id="txtV_url" style="width:400px;" title="URL" type="text"
                 value="<%=subMenuItem.getV_url()%>"/>
          </span></div>
                <div class="row"><span class="label">Window Open Location <span
                        style="color:#D50000">*</span> :</span><span class="txt_cont">
          <select name="txtV_target" id="txtV_target" class="styled1">
            <option value="-1">Please select one</option>
            <option value="imain_frm">Main Window</option>
            <option value="_blank">New Window</option>
          </select>
          </span></div>
            </fieldset>
            <fieldset>
                <div id="paraListView">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td style="font-size:11px;font-family:Arial, Helvetica, sans-serif;font-weight:bold;color:#333;width:50px;">
                                No .
                            </td>
                            <td style="font-size:11px;font-family:Arial, Helvetica, sans-serif;font-weight:bold;color:#333;width:150px;">
                                Para. code
                            </td>
                            <td style="font-size:11px;font-family:Arial, Helvetica, sans-serif;font-weight:bold;color:#333;width:200px;padding-left:10px;">
                                Select type
                            </td>
                            <td style="font-size:11px;font-family:Arial, Helvetica, sans-serif;font-weight:bold;color:#333;width:200px;padding-left:10px;">
                                Table name
                            </td>
                            <td style="font-size:11px;font-family:Arial, Helvetica, sans-serif;font-weight:bold;color:#333;width:200px;padding-left:10px;">
                                Field name
                            </td>
                            <td style="font-size:11px;font-family:Arial, Helvetica, sans-serif;font-weight:bold;color:#333;width:200px;padding-left:10px;">
                                Para. Value
                            </td>
                        </tr>
                        <tr>
                            <input name="hideRecCount" type="hidden" value="<%=appParameteList.size()%>"/>
                            <%
                                int cnt = 1;
                                for (ApplicationParameter apapplictionParameter : appParameteList) {
                            %>
                            <input type="hidden" name="txtParaCode<%=cnt%>"
                                   value="<%=apapplictionParameter.getParaCode()%>"/>
                            <td><%=cnt%>
                            </td>
                            <td>
                                <div class="row"><span class="label"
                                                       style="width:80px;text-align:left;"><%=apapplictionParameter.getParaCode()%></span>
                                </div>
                            </td>
                            <td style="padding-left:10px;"><select name="txtSelectType<%=cnt%>"
                                                                   id="txtSelectType<%=cnt%>"
                                                                   onchange="clickTxtSelectType(<%=cnt%>)">
                                <option value="Please select one">Please select one</option>
                                <option value="Static Field">Static Field</option>
                                <option value="Dynamic Field">Dynamic Field</option>
                            </select></td>
                            <td style="padding-left:10px;"><select name="txtTableName<%=cnt%>" id="txtTableName<%=cnt%>"
                                                                   style="display:none;"
                                                                   onchange="setListValues('txtTableName<%=cnt%>','txtFieldNames<%=cnt%>')">
                                <option value="Please select one">Please select one</option>
                                <option value="User Parameter">User Parameter</option>
                                <option value="User Master">User Master</option>
                            </select></td>
                            <td style="padding-left:10px;"><select name="txtFieldNames<%=cnt%>"
                                                                   id="txtFieldNames<%=cnt%>" style="display:none;">
                                <option value="Please select one">Please select one</option>
                            </select></td>
                            <td>
                                <div class="row"> <span class="txt_cont">
                  <input name="txtStaticValue<%=cnt%>" id="txtStaticValue<%=cnt%>" style="width:200px;display:none;"
                         type="text" value=""/>
                  </span></div>
                            </td>
                        </tr>
                        <script type="text/javascript">
                            document.getElementById("txtSelectType<%=cnt%>").value = '<%=apapplictionParameter.getSelType()%>';
                            setTableNameValues('<%=cnt%>', '<%=apapplictionParameter.getTableName()%>');
                            setListValues("txtTableName<%=cnt%>", "txtFieldNames<%=cnt%>");
                            setFieldNameValues('<%=cnt%>', '<%=apapplictionParameter.getFieldname()%>');
                            document.getElementById("txtStaticValue<%=cnt%>").value = '<%=apapplictionParameter.getValue()%>';
                            setVisibleSelectBox('<%=cnt%>');
                        </script>
                        <%
                                cnt++;
                            }
                        %>
                    </table>
                </div>
            </fieldset>
            <div class="but_container">
                <input type="button" name="cmdSave" id="cmdSave" value="Save Changes" onclick="pageSubmit('Save')"
                       class="button"/>
                <input type="button" name="cmdDelete" id="cmdDelete" value="Delete" onclick="pageSubmit('Delete')"
                       class="button"/>
                <input type="button" name="cmdClose" id="cmdClose" value="Close" onclick="pageSubmit('Close')"
                       class="button"/>
            </div>
        </div>
        <% if (!isNewRecord) {
        %>
        <script type="text/javascript">
            document.getElementById("txtN_mnuid").value =<%=subMenuItem.getN_mnuid() %>;
            document.getElementById("txtV_target").value = "<%=subMenuItem.getV_target() %>";
        </script>
        <%}%>
        <div id="dialog" style="display:none;" title="${CompanyTitle}">
            <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
            <p id="dialog-email" class="textGrey"></p>
        </div>
    </form>
    <div class="spacer"> &nbsp;</div>
</div>
</body>
</html>
