<!-------------------------- Start Import -------------------------------------------------->
<%@ page import="com.misyn.mcms.utility.AJax_XML_Writer" %>
<%@ page import="com.misyn.mcms.utility.DataValidate" %>
<!-------------------------- End Import ---------------------------------------------------->

<%@ page import="com.misyn.mcms.utility.Utility" %>
<%@ page import="java.io.PrintWriter" %>

<%
    response.setHeader("Cache-Control", "no-cache, must-revalidate");
    response.setHeader("Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
    session = request.getSession();
    String reply = "";
    PrintWriter pr = null;
    String searchKey1 = request.getParameter("searchKey1").trim();
    String searchKey2 = request.getParameter("searchKey2").trim();


    try {

        out.clear();
        response.setContentType("text/xml");
        pr = new PrintWriter(response.getOutputStream()); //description like 'user Login%'
        AJax_XML_Writer ajaxXML = new AJax_XML_Writer();
        //ajaxXML.writeXML("error_apply_mst","error_ref_id=? AND company=?",new String[]{"2","1"});
        if (DataValidate.isNumeric(searchKey1)) {
            try {
                searchKey1 = Utility.formatNumber(searchKey1);
                searchKey2 = Utility.formatNumber(searchKey2);
            } catch (Exception e) {
                searchKey1 = "";
            }
            ajaxXML.writeXMLAll("mnu", "mnuid", "mnuname", "mnuid like '" + searchKey1 + "%' AND prgid='" + searchKey2 + "'", 0, true);
        } else {
            ajaxXML.writeXMLAll("mnu", "mnuid", "mnuname", "mnuname like '" + searchKey1 + "%' AND prgid=" + searchKey2, 0, true);
        }

	/*reply = "<?xml version='1.0' encoding='ISO-8859-1'?>";
	reply += "<reply>";
	out.print("ok");
	reply += "<inputtext1>"+ usrData[0] +"</inputtext1>";
	reply += "<inputtext1>"+ usrData[1]+"</inputtext1>";
	reply += "</reply>";*/
        //output the response

        pr.write(ajaxXML.getXMLReply());
        //pr.write(reply);
    } catch (Exception e) {
    } finally {
        pr.flush();
        pr.close();

    }
//response.sendRedirect("user/misynerror/errorApply.jsp");
%>

