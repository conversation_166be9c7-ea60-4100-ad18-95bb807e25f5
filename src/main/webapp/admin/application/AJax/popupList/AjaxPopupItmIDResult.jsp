<!-------------------------- Start Import -------------------------------------------------->
<%@ page import="com.misyn.mcms.utility.AJax_XML_Writer" %>
<%@ page import="com.misyn.mcms.utility.DataValidate" %>
<!-------------------------- End Import ---------------------------------------------------->

<%@ page import="com.misyn.mcms.utility.Utility" %>
<%@ page import="java.io.PrintWriter" %>

<%
    response.setHeader("Cache-Control", "no-cache, must-revalidate");
    response.setHeader("Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
    session = request.getSession();
    String reply = "";
    PrintWriter pr = null;
    String usrData[] = new String[15];
    String searchKey1 = request.getParameter("searchKey1").trim();

    String searchKey2 = request.getParameter("searchKey2").trim();
    String searchKey3 = request.getParameter("searchKey3").trim();

    try {

        out.clear();
        response.setContentType("text/xml");
        pr = new PrintWriter(response.getOutputStream()); //description like 'user Login%'
        AJax_XML_Writer ajaxXML = new AJax_XML_Writer();
        //ajaxXML.writeXML("error_apply_mst","error_ref_id=? AND company=?",new String[]{"2","1"});


        if (DataValidate.isNumeric(searchKey1)) {
            try {
                searchKey1 = Utility.formatNumber(searchKey1);
                searchKey2 = Utility.formatNumber(searchKey2);
                searchKey3 = Utility.formatNumber(searchKey3);
            } catch (Exception e) {
                searchKey1 = "";
            }

            ajaxXML.writeXMLAll("itm", "itmid", "itmname", "itmid like '" + searchKey1 + "%' AND prgid=" + searchKey2 + " AND mnuid=" + searchKey3 + " AND apptype='Y'", 0, false);
        } else {
            ajaxXML.writeXMLAll("itm", "itmid", "itmname", "itmname like '" + searchKey1 + "%' AND prgid=" + searchKey2 + " AND mnuid=" + searchKey3 + " AND apptype='Y'", 0, false);
        }


        pr.write(ajaxXML.getXMLReply());

    } catch (Exception e) {
    } finally {
        pr.flush();
        pr.close();

    }
//response.sendRedirect("user/misynerror/errorApply.jsp");
%>

