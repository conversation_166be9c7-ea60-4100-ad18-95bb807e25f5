// JavaScript Document

var xmlHttp=null;

var xmlHttp1=null;
var xmlHttp2=null;


var tblName=null;

var frmName=null;
var txtName=null;
var lstName=null;

var id_value=null;

var id_valueArray = new Array();
var txt_valueArray = new Array();
var arrayList=new Array();
 

var isPutId=false;


var XMLHttpFactories = [
	function () {return new XMLHttpRequest()},
	function () {return new ActiveXObject("Msxml2.XMLHTTP")},
	function () {return new ActiveXObject("Msxml3.XMLHTTP")},
	function () {return new ActiveXObject("Microsoft.XMLHTTP")}
];

//=====================================Start ShowPopUp List PrgID==============================================================================

function showPopupList_PrgID(e,m_frmName,m_txtName,m_lstName,m_isPutId) {
	
	frmName=m_frmName;
	txtName=m_txtName;
	lstName=m_lstName;
	
	isPutId=m_isPutId;
	
	if(e.keyCode==40)
	{
		focusLst(e);
		//alert(e.keyCode);
		return;
	}
	removeItems(document.getElementById(lstName));
	document.getElementById(lstName).style.display="none";
	
	//document.all.item('mytableheader').classname='TH_New';
   
	
	document.getElementById(frmName).style.cursor='wait';
	//document.getElementById("btn1").style.cursor='wait';
	document.getElementById(txtName).style.cursor='wait';
	
    var paraKey1=document.getElementById(txtName).value;
	//var paraKey2=document.getElementById("txt1").value;
	
	//document.getElementById(txtName).className = 'txt_readonly'; 
	//alert(document.getElementById(txtName).className);
	if(document.getElementById(txtName).className=="txt_readonly")
	{
		return;
		//document.getElementById(txtName).className = 'txt_readonly';  
	}
	
	
	if(paraKey1=="")
	{
		document.getElementById(frmName).style.cursor='default';
		//document.getElementById("btn1").style.cursor='default';
		document.getElementById(txtName).style.cursor='default';
	    document.getElementById(txtName).className = 'txt'; 
		return;
	}
	
	document.getElementById(txtName).className = 'txtProgress';   
	
	xmlHttp=GetXmlHttpObject();
	xmlHttp=createXMLHTTPObject();
	if (xmlHttp==null) 
	{
  		alert ("Your browser does not support AJAX!");
  		return;
  	}
	var url;
	//alert ("Your browser does not support AJAX!");
	
	url="AJax/popupList/AjaxPopupPrgIDResult.jsp?searchKey1="+paraKey1;
	xmlHttp.open("GET",url,true);
	//alert("test"+ paraKey1);
	xmlHttp.onreadystatechange=getPopList_PrgId;
	xmlHttp.send(null);
	//showLblDisp_PrgName();
	allTextID_Validate();
	return;
	
}

//**************************************************************************************

function getPopList_PrgId() { 
//document.getElementById("span1").innerHTML=document.getElementById("file").value;

 
if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
		
		var xmlDoc=xmlHttp.responseXML.documentElement;
		var txtId="";
		var rec_count=0;
		if (xmlDoc!=null) {
			
			if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
			{
				rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
				rec_count=rec_count;
			}
			
			//document.getElementById(txtName).className = 'txtProgress';   
			for (var i=0;i<rec_count;i++)
			{
				//t=setTimeout("getPopList()",1000);
				//if(i==5) break;
				txtId="inputtext"+(i+1);
				if (xmlDoc.getElementsByTagName(txtId)[0].childNodes[0]!=null)
				{
				
				   var items="";// =xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   id_valueArray[i]=xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   txt_valueArray[i]=xmlDoc.getElementsByTagName(txtId)[1].childNodes[0].nodeValue;
				   
				   if(id_valueArray[i].length==1)
				   {					   
				   		items="0"+id_valueArray[i]+"  "+txt_valueArray[i];	
				   }
				   else
				   {
					   items=id_valueArray[i]+"  "+txt_valueArray[i];	
				   }
				   arrayList[i]=items;				   
				   document.getElementById(lstName).style.display="block";	
				   
				   //AddItem(items,0);			
								
				}
			}
			
			for (var x=0;x<rec_count;x++)
			{
				//document.getElementById(lstName).style.display="block";					   
				AddItem(arrayList[x],0);
				//alert(arrayList[x]);
			}
						
		}
		//document.getElementById("btn1").style.display="block";
			document.getElementById(frmName).style.cursor='default';
			//document.getElementById("btn1").style.cursor='default';
			document.getElementById(txtName).style.cursor='default';
	        document.getElementById(txtName).className = 'txt'; 
	}

}
//====================================End ShowPopUp List PrgID==============================================================

//================================Start ShowPopUp List MnuID================================================================

function showPopupList_MnuID(e,m_frmName,m_txtName,m_lstName,m_isPutId) {
	
	frmName=m_frmName;
	txtName=m_txtName;
	lstName=m_lstName;
	
	isPutId=m_isPutId;
	
	if(e.keyCode==40)
	{
		focusLst(e);
		//alert(e.keyCode);
		return;
	}
	removeItems(document.getElementById(lstName));
	document.getElementById(lstName).style.display="none";
	
	//document.all.item('mytableheader').classname='TH_New';
   
	
	document.getElementById(frmName).style.cursor='wait';
	//document.getElementById("btn1").style.cursor='wait';
	document.getElementById(txtName).style.cursor='wait';
	
    var paraKey1=document.getElementById(txtName).value;
	var paraKey2=document.getElementById("txt1").value;
	
	//alert("P1: "+paraKey1+ " P2 : "+paraKey2);
	
	//document.getElementById(txtName).className = 'txt_readonly'; 
	//alert(document.getElementById(txtName).className);
	if(document.getElementById(txtName).className=="txt_readonly")
	{
		return;
		//document.getElementById(txtName).className = 'txt_readonly';  
	}
	
	
	if(paraKey1=="" && paraKey2=="")
	{
		document.getElementById(frmName).style.cursor='default';
		//document.getElementById("btn1").style.cursor='default';
		document.getElementById(txtName).style.cursor='default';
	    document.getElementById(txtName).className = 'txt'; 
		return;
	}
	
	document.getElementById(txtName).className = 'txtProgress';   
	
	xmlHttp=GetXmlHttpObject();
	xmlHttp=createXMLHTTPObject();
	if (xmlHttp==null) 
	{
  		alert ("Your browser does not support AJAX!");
  		return;
  	}
	var url;
	//alert ("Your browser does not support AJAX!");
	
	url="AJax/popupList/AjaxPopupMnuIDResult.jsp?searchKey1="+paraKey1+"&searchKey2="+paraKey2;
	xmlHttp.open("GET",url,true);
	//alert("test"+ paraKey1);
	xmlHttp.onreadystatechange=getPopList_MnuId;
	xmlHttp.send(null);
	//showLblDisp_MnuID();
	allTextID_Validate();
	return;
	
}

function getPopList_MnuId() { 
//document.getElementById("span1").innerHTML=document.getElementById("file").value;

 
if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
		
		var xmlDoc=xmlHttp.responseXML.documentElement;
		var txtId="";
		var rec_count=0;
		if (xmlDoc!=null) {
			
			if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
			{
				rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
				rec_count=rec_count;
			}
			
			//document.getElementById(txtName).className = 'txtProgress';   
			for (var i=0;i<rec_count;i++)
			{
				
				txtId="inputtext"+(i+1);
				if (xmlDoc.getElementsByTagName(txtId)[0].childNodes[0]!=null)
				{
				
				   var items="";// =xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   id_valueArray[i]=xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   txt_valueArray[i]=xmlDoc.getElementsByTagName(txtId)[1].childNodes[0].nodeValue;
				   
				   if(id_valueArray[i].length==1)
				   {					   
				   		items="0"+id_valueArray[i]+"  "+txt_valueArray[i];	
				   }
				   else
				   {
					   items=id_valueArray[i]+"  "+txt_valueArray[i];	
				   }
				   arrayList[i]=items;				   
				   document.getElementById(lstName).style.display="block";	
				   
				   //AddItem(items,0);			
								
				}
			}
			//alert(id_valueArray[0].length);
			//id_valueArray.sort();
			//arrayList.sort();
			for (var x=0;x<rec_count;x++)
			{
				//document.getElementById(lstName).style.display="block";					   
				AddItem(arrayList[x],0);
				//alert(arrayList[x]);
			}
					
		}
		//document.getElementById("btn1").style.display="block";
			document.getElementById(frmName).style.cursor='default';
			//document.getElementById("btn1").style.cursor='default';
			document.getElementById(txtName).style.cursor='default';
	        document.getElementById(txtName).className = 'txt'; 
	}

}
//=====================================End ShowPopUp List MnuID==============================================================================

//=====================================Start ShowPopUp List ItemID==============================================================================
function showPopupList_ItmID(e,m_frmName,m_txtName,m_lstName,m_isPutId) {
	
	frmName=m_frmName;
	txtName=m_txtName;
	lstName=m_lstName;
	
	isPutId=m_isPutId;
	
	if(e.keyCode==40)
	{
		focusLst(e);
		//alert(e.keyCode);
		return;
	}
	removeItems(document.getElementById(lstName));
	document.getElementById(lstName).style.display="none";
	
	//document.all.item('mytableheader').classname='TH_New';
   
	
	document.getElementById(frmName).style.cursor='wait';
	//document.getElementById("btn1").style.cursor='wait';
	document.getElementById(txtName).style.cursor='wait';
	
    var paraKey1=document.getElementById(txtName).value;
	
	var paraKey2=document.getElementById("txt1").value;
	var paraKey3=document.getElementById("txt2").value;
	
	//document.getElementById(txtName).className = 'txt_readonly'; 
	//alert(document.getElementById(txtName).className);
	if(document.getElementById(txtName).className=="txt_readonly")
	{
		return;
		//document.getElementById(txtName).className = 'txt_readonly';  
	}
	
	
	if(paraKey1=="" && paraKey2=="" && paraKey3=="")
	{
		document.getElementById(frmName).style.cursor='default';
		//document.getElementById("btn1").style.cursor='default';
		document.getElementById(txtName).style.cursor='default';
	    document.getElementById(txtName).className = 'txt'; 
		return;
	}
	
	document.getElementById(txtName).className = 'txtProgress';   
	
	xmlHttp=GetXmlHttpObject();
	xmlHttp=createXMLHTTPObject();
	if (xmlHttp==null) 
	{
  		alert ("Your browser does not support AJAX!");
  		return;
  	}
	var url;
	//alert ("Your browser does not support AJAX!");
	
	url="AJax/popupList/AjaxPopupItmIDResult.jsp?searchKey1="+paraKey1+"&searchKey2="+paraKey2+"&searchKey3="+paraKey3;
	xmlHttp.open("GET",url,true);
	//alert("test"+ paraKey1);
	xmlHttp.onreadystatechange=getPopList_ItmID;
	xmlHttp.send(null);
	
	return;
	
}

//***********************************************************************************************************

function getPopList_ItmID() { 
//document.getElementById("span1").innerHTML=document.getElementById("file").value;

 try
 {
if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
		
		var xmlDoc=xmlHttp.responseXML.documentElement;
		var txtId="";
		var rec_count=0;
		
		if (xmlDoc!=null) {
			
			if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
			{
				rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
				rec_count=rec_count;
			}
			
			//document.getElementById(txtName).className = 'txtProgress';   
			for (var i=0;i<rec_count;i++)
			{
				//t=setTimeout("getPopList()",1000);
				//if(i==5) break;
				txtId="inputtext"+(i+1);
				if (xmlDoc.getElementsByTagName(txtId)[0].childNodes[0]!=null)
				{
				
				   var items="";// =xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   id_valueArray[i]=xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   txt_valueArray[i]=xmlDoc.getElementsByTagName(txtId)[1].childNodes[0].nodeValue;
				   
				   if(id_valueArray[i].length==1)
				   {					   
				   		items="0"+id_valueArray[i]+"  "+txt_valueArray[i];	
				   }
				   else
				   {
					   items=id_valueArray[i]+"  "+txt_valueArray[i];	
				   }
				   arrayList[i]=items;				   
				   document.getElementById(lstName).style.display="block";	
				   
				   //AddItem(items,0);			
								
				}
			}
			//alert(id_valueArray[0].length);
			//id_valueArray.sort();
			//arrayList.sort();
			for (var x=0;x<rec_count;x++)
			{
				//document.getElementById(lstName).style.display="block";					   
				AddItem(arrayList[x],0);
				//alert(arrayList[x]);
			}			
			
		}
		
		//document.getElementById("btn1").style.display="block";
			document.getElementById(frmName).style.cursor='default';
			//document.getElementById("btn1").style.cursor='default';
			document.getElementById(txtName).style.cursor='default';
	        document.getElementById(txtName).className = 'txt'; 
		
	}
	
 }
 catch(err)
 {
	 alert("error :"+err);
 }

}
//=====================================End ShowPopUp List ItemID==============================================================================


function createXMLHTTPObject() {
	var xmlhttp = null;
	for (var i=0;i<XMLHttpFactories.length;i++) {
		try {
			xmlhttp = XMLHttpFactories[i]();
		} catch (e) {
			continue;
		}
		break;
	}
	return xmlhttp;
}

function GetXmlHttpObject() {
	var xmlHttp=null;
	try {
  		// Firefox, Opera 8.0+, Safari
  		xmlHttp=new XMLHttpRequest();
  	} catch (e) {
  		// Internet Explorer
  		try {
    		xmlHttp=new ActiveXObject("Msxml2.XMLHTTP");
    	} catch (e) {
    		xmlHttp=new ActiveXObject("Microsoft.XMLHTTP");
    	}
  	}
	return xmlHttp;
}
var t;







//===================================Start label display PrgName==========================================================================
function showLblDisp_PrgName() {
	
    var paraKey1=document.getElementById("txt1").value;
	//var paraKey2=document.getElementById("txt1").value;
	document.getElementById("lblDis1").innerHTML="";
		
	xmlHttp1=GetXmlHttpObject();
	xmlHttp1=createXMLHTTPObject();
	if (xmlHttp1==null) 
	{
  		alert ("Your browser does not support AJAX!");
  		return;
  	}
	var url;
	//alert ("Your browser does not support AJAX!");
	
	url="AJax/lblDisp/AjaxLblDispPrgIDResult.jsp?searchKey1="+paraKey1;
	xmlHttp1.open("GET",url,true);
	//alert("test"+ paraKey1);
	xmlHttp1.onreadystatechange=getLblDisp_PrgName;
	xmlHttp1.send(null);
	
	return;
	
}

var t;
function getLblDisp_PrgName() { 
//document.getElementById("span1").innerHTML=document.getElementById("file").value;

 
if ( (xmlHttp1.readyState==4) && (xmlHttp1.status==200) ) {
		
		var xmlDoc=xmlHttp1.responseXML.documentElement;
		var txtId="";
		var rec_count=0;
		if (xmlDoc!=null) {
			
			if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
			{
				rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
				rec_count=rec_count;
			}
			
			//document.getElementById(txtName).className = 'txtProgress';   
			for (var i=0;i<1;i++)
			{
				//t=setTimeout("getPopList()",1000);
				//if(i==5) break;
				txtId="inputtext"+(i+1);
				if (xmlDoc.getElementsByTagName(txtId)[0].childNodes[0]!=null)
				{
				
				   var disName="";// =xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   disName=xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				    //alert(disName);
				   document.getElementById("lblDis1").innerHTML=disName;
					 
					if(disName=="Invalid")
					{	
						document.getElementById("txt1").className = 'txt_Invidate';						
					}				   								
				}
			}
			 
		}
	}

}
//===================================End label Display==================================================================================

//===================================Start label display MnuName==========================================================================
function showLblDisp_MnuID() {
	
    var paraKey1=document.getElementById("txt2").value;
	var paraKey2=document.getElementById("txt1").value;
	
	document.getElementById("lblDis2").innerHTML="";
		
	xmlHttp2=GetXmlHttpObject();
	xmlHttp2=createXMLHTTPObject();
	if (xmlHttp2==null) 
	{
  		alert ("Your browser does not support AJAX!");
  		return;
  	}
	var url;
	//alert ("Your browser does not support AJAX!");
	
	url="AJax/lblDisp/AjaxLblDispMnuIDResult.jsp?searchKey1="+paraKey1+"&searchKey2="+paraKey2;
	xmlHttp2.open("GET",url,true);
	//alert("test"+ paraKey1);
	xmlHttp2.onreadystatechange=getLblDisp_MnuId;
	xmlHttp2.send(null);
	return;
	
}

var t;
function getLblDisp_MnuId() { 
//document.getElementById("span1").innerHTML=document.getElementById("file").value;

 
if ( (xmlHttp2.readyState==4) && (xmlHttp2.status==200) ) {
		
		var xmlDoc=xmlHttp2.responseXML.documentElement;
		var txtId="";
		var rec_count=0;
		if (xmlDoc!=null) {
			
			if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
			{
				rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
				rec_count=rec_count;
			}
			
			//document.getElementById(txtName).className = 'txtProgress';   
			for (var i=0;i<1;i++)
			{
				//t=setTimeout("getPopList()",1000);
				//if(i==5) break;
				txtId="inputtext"+(i+1);
				if (xmlDoc.getElementsByTagName(txtId)[0].childNodes[0]!=null)
				{
				
				   var disName="";// =xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   disName=xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;;
				    //alert(disName);
					 document.getElementById("lblDis2").innerHTML=disName;
					 
					if(disName=="Invalid")
					{	
						document.getElementById("txt2").className = 'txt_Invidate';						
					}
				   								
				}
			}
			 
		}
	}

}
//===================================End label Display==================================================================================

function allTextID_Validate()
{
	
	
	if(document.getElementById("txt2").value!="")
	{
			
			showLblDisp_MnuID();
			//alert("***2");
	}
	else
	{
		document.getElementById("lblDis2").innerHTML="";
	}
}


function AddItem(Text,Value)
    {
        // Create an Option object               
		 var opt = document.createElement("option");

        // Add an Option object to Drop Down/List Box
        document.getElementById(lstName).options.add(opt);        // Assign text and value to Option object
        opt.text = Text;
        opt.value = Value;

    }
	
function removeItems(selectbox)
{
	var i;
	for(i=selectbox.options.length-1;i>=0;i--)
	{
	//selectbox.options.remove(i);
		selectbox.remove(i);
	}
}

function selectLstBox()
{
	var x2=document.getElementById(lstName);
	var s2=x2.options[x2.selectedIndex].text;
	if(isPutId==true)
	{
		//document.getElementById(txtName).value= id_valueArray[x2.selectedIndex];//s2;
		document.getElementById(txtName).value=arrayList[x2.selectedIndex].substring(0,2);
		
		if(lstName=="lst1")showLblDisp_PrgName();
		if(lstName=="lst2")showLblDisp_MnuID();
		
	}
	else
	{
		//document.getElementById(txtName).value= txt_valueArray[x2.selectedIndex];
		document.getElementById(txtName).value=Trim(arrayList[x2.selectedIndex].substring(2));
	}
	
}

function onhide(m_lstName)
{
		document.getElementById(m_lstName).style.display="none";
}

function focusLst(e)
{
	
	if (!e) e=window.event;  // Block the user of digits.  
	var code; 
	//if     code = e.charCode  else        code = e.keyCode;
    if ((e.charCode) && (e.keyCode==0)) 
	{
		code = e.charCode 
		//alert(code);
	}
	else     
	{
		code = e.keyCode;
		//alert(code);
	}
	if(code==40)
  	{
  		//alert(code);
		document.getElementById(lstName).focus();
		document.getElementById(lstName).selectedIndex=0;
	}
}

function EnterKeyLst(e)
{
	
	if (!e) e=window.event;  // Block the user of digits.  
	var code; 
	//if     code = e.charCode  else        code = e.keyCode;
    if ((e.charCode) && (e.keyCode==0)) 
	{
		code = e.charCode 
		//alert(code);
	}
	else     
	{
		code = e.keyCode;
		//alert(code);
	}
	if(code==13)
  	{
  		//alert(code);
		selectLstBox();
		document.getElementById(txtName).focus();
		
	}
}

function Trim(str)
{
    while (str.substring(0,1) == ' ') // check for white spaces from beginning
    {
        str = str.substring(1, str.length);
    }
    while (str.substring(str.length-1, str.length) == ' ') // check white space from end
    {
        str = str.substring(0,str.length-1);
    }
   
    return str;
}
