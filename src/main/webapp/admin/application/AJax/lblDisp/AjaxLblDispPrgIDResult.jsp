<!-------------------------- Start Import -------------------------------------------------->
<%@ page import="com.misyn.mcms.utility.AJax_XML_Writer" %>
<%@ page import="com.misyn.mcms.utility.DataValidate" %>
<!-------------------------- End Import ---------------------------------------------------->

<%@ page import="java.io.PrintWriter" %>

<%
    response.setHeader("Cache-Control", "no-cache, must-revalidate");
    response.setHeader("Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
    session = request.getSession();
    String reply = "";
    PrintWriter pr = null;
    String usrData[] = new String[15];
    String searchKey1 = (request.getParameter("searchKey1")).trim();
    //String searchKey2=request.getParameter("searchKey2");
    try {
        String p1 = "1";
        usrData[0] = "Kelum Lakmal Sepala";
        usrData[1] = "MISynergy Pvt(Ltd).";
        out.clear();
        response.setContentType("text/xml");
        pr = new PrintWriter(response.getOutputStream()); //description like 'user Login%'
        AJax_XML_Writer ajaxXML = new AJax_XML_Writer();
        //ajaxXML.writeXML("error_apply_mst","error_ref_id=? AND company=?",new String[]{"2","1"});
        ajaxXML.writeXMLLable("prgs", "prgName", "prgid=" + searchKey1);

        if (DataValidate.isNumeric(searchKey1)) {
            ajaxXML.writeXMLLable("prgs", "prgName", "prgid=" + searchKey1);
            //ajaxXML.writeXMLAll("itm","itmid","itmid like '"+searchKey1+"%' AND prgid="+searchKey2+" AND mnuid="+searchKey3,0);
        } else {
            if (!searchKey1.equals(""))
                ajaxXML.writeXMLLable("prgs", "prgName", "prgid like '" + searchKey1 + "'");
            //ajaxXML.writeXMLLable("mnu","mnuName","mnuId like '"+searchKey1+"%' AND prgid="+searchKey2 );
            //ajaxXML.writeXMLAll("itm","itmid","itmname like '"+searchKey1+"%' AND prgid="+searchKey2+" AND mnuid="+searchKey3,0);
        }

        pr.write(ajaxXML.getXMLReply());
        //pr.write(reply);
    } catch (Exception e) {
    } finally {
        pr.flush();
        pr.close();

    }
//response.sendRedirect("user/misynerror/errorApply.jsp");
%>

