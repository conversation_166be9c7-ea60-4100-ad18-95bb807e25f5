// JavaScript Document


var tblName=null;

var frmName=null;
var txtName=null;
var lstName=null;

var id_value=null;

var id_valueArray = new Array();
var txt_valueArray = new Array();
var arrayList=new Array();
 

var isPutId=false;

function showLblDisp_MnuID(e,m_frmName,m_txtName,m_lstName,m_isPutId) {
	
	frmName=m_frmName;
	txtName=m_txtName;
	lstName=m_lstName;
	
	isPutId=m_isPutId;	
	
	
	
    var paraKey1=document.getElementById(txtName).value;
	var paraKey2=document.getElementById("txt1").value;
		
	xmlHttp=GetXmlHttpObject();
	xmlHttp=createXMLHTTPObject();
	if (xmlHttp==null) 
	{
  		alert ("Your browser does not support AJAX!");
  		return;
  	}
	var url;
	//alert ("Your browser does not support AJAX!");
	
	url="AJax/lblDisp/AjaxLblDispMnuIDResult.jsp?searchKey1="+paraKey1+"&searchKey2="+paraKey2;
	xmlHttp.open("GET",url,true);
	//alert("test"+ paraKey1);
	xmlHttp.onreadystatechange=getLblDisp_MnuId;
	xmlHttp.send(null);
	return;
	
}

var t;
function getLblDisp_MnuId() { 
//document.getElementById("span1").innerHTML=document.getElementById("file").value;

 
if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
		
		var xmlDoc=xmlHttp.responseXML.documentElement;
		var txtId="";
		var rec_count=0;
		if (xmlDoc!=null) {
			
			if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
			{
				rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
				rec_count=rec_count;
			}
			
			//document.getElementById(txtName).className = 'txtProgress';   
			for (var i=0;i<1;i++)
			{
				//t=setTimeout("getPopList()",1000);
				//if(i==5) break;
				txtId="inputtext"+(i+1);
				if (xmlDoc.getElementsByTagName(txtId)[0].childNodes[0]!=null)
				{
				
				   var disName="";// =xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;
				   disName=xmlDoc.getElementsByTagName(txtId)[0].childNodes[0].nodeValue;;
				    //alert(disName);
					 document.getElementById("lblDis2").innerHTML=disName;
				   								
				}
			}
			 
		}
	}

}


