<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@page import="com.misyn.mcms.admin.SubMenuItem" %>
<%@page import="java.util.Iterator" %>
<%@page import="java.util.List" %>
<jsp:useBean id="SubMenuItemManagerBean" class="com.misyn.mcms.admin.SubMenuItemManager" scope="application"/>
<%

    String tdClass1 = "class=\"tbl_row\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row';\"";
    String tdClass2 = "class=\"tbl_row2\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row2';\"";

    //*********Start Sort Fields ***************
    boolean IS_ASC = true;
    String SORT_FIELD = request.getParameter("P_SORT_FIELD");
    String temp_SortField = "";

    String ASC_DESC_STATUS = request.getParameter("P_ASC_DESC_STATUS");
    temp_SortField = (String) session.getAttribute("FIELD");


    if (SORT_FIELD != null) {
        session.removeAttribute("FIELD");
        session.setAttribute("FIELD", SORT_FIELD);
    }
    if (ASC_DESC_STATUS != null) {

        session.removeAttribute("ASC_DESC_STATUS");
        session.setAttribute("ASC_DESC_STATUS", ASC_DESC_STATUS);
    }

    try {
        IS_ASC = Boolean.parseBoolean(request.getParameter("P_IS_ASC"));
    } catch (Exception e) {
    }


    if (session.getAttribute("IS_ASC") == null) {
        // IS_ASC = true;
        session.removeAttribute("IS_ASC");
        session.setAttribute("IS_ASC", IS_ASC);
    } else {
        IS_ASC = (Boolean) session.getAttribute("IS_ASC");
    }

    int ISBODY_LOAD = 0;
    try {
        ISBODY_LOAD = Integer.parseInt(request.getParameter("P_ISBODY_LOAD"));
    } catch (Exception e) {
    }

    if (temp_SortField != null && SORT_FIELD != null && ISBODY_LOAD == 0) {
        if (temp_SortField.toLowerCase().equalsIgnoreCase(SORT_FIELD.toLowerCase())) {
            if (IS_ASC) {
                IS_ASC = false;
                ASC_DESC_STATUS = " DESC ";
            } else {
                IS_ASC = true;
                ASC_DESC_STATUS = " ASC ";
            }

        } else {
            if (IS_ASC) {
                IS_ASC = false;
                ASC_DESC_STATUS = " DESC ";
            } else {
                IS_ASC = true;
                ASC_DESC_STATUS = " ASC ";
            }

        }
        session.removeAttribute("ASC_DESC_STATUS");
        session.setAttribute("ASC_DESC_STATUS", ASC_DESC_STATUS);

        session.removeAttribute("IS_ASC");
        session.setAttribute("IS_ASC", IS_ASC);
    }

    SORT_FIELD = (String) session.getAttribute("FIELD") + " " + (String) session.getAttribute("ASC_DESC_STATUS");

    if (SORT_FIELD != null) {
        session.removeAttribute("STR_FIELD");
        session.setAttribute("STR_FIELD", SORT_FIELD);
    }
    String str_sort_field = (String) session.getAttribute("STR_FIELD");
    //out.print("str_sort_field " + str_sort_field + " IS_ASC: " + IS_ASC);
    //*********End Sort Fields ***************

    int m_clickPos = 1;
    int m_pagecount = com.misyn.mcms.utility.Parameters.getInstance().getNoRecordPerPage();

    boolean isClickNavigat = true;
    try {
        m_clickPos = Integer.parseInt(request.getParameter("P_Click_Pos"));
    } catch (Exception e) {
        isClickNavigat = false;
    }

    try {
        if (!isClickNavigat) {
            m_clickPos = (Integer) session.getAttribute("CLICK_PAGE_NUMBER");
            //out.println("XXXXXXXXXXXXXXXX"+m_clickPos);
        }
    } catch (Exception e) {
    }

    session.removeAttribute("CLICK_PAGE_NUMBER");
    session.setAttribute("CLICK_PAGE_NUMBER", m_clickPos);
    int m_start = 0;
    int m_end = m_pagecount * m_clickPos;
    m_start = m_end - m_pagecount;


    //---------------------------
    int TYPE = 0;
    long timeURL = System.currentTimeMillis();
    String destinationURL = "";
    String URL = "applicationItem.jsp?" + timeURL;

    int tmp_mnuid = -1;

    List<SubMenuItem> list = null;
    SubMenuItem m_SubMenuItem = null;

    try {
        if (user.getN_accessusrtype() == 1) {
            list = SubMenuItemManagerBean.getSubMenuItemViewList("AND t2.v_apptype='Y' "
                    + "ORDER BY " + str_sort_field + " LIMIT " + m_start + "," + m_pagecount);
        } else if (user.getN_accessusrtype() == 2) {
            list = SubMenuItemManagerBean.getSubMenuItemViewList("AND t2.v_apptype='Y' "
                    + "ORDER BY  " + str_sort_field + " LIMIT " + m_start + "," + m_pagecount);
        } else if (user.getN_accessusrtype() == 3) {
            list = SubMenuItemManagerBean.getSubMenuItemViewList("AND t2.v_apptype='Y' "
                    + "ORDER BY  " + str_sort_field + " LIMIT " + m_start + "," + m_pagecount);
        } else if (user.getN_accessusrtype() == 4) {
            list = SubMenuItemManagerBean.getSubMenuItemViewList("AND t2.v_apptype='Y' "
                    + "ORDER BY  " + str_sort_field + " LIMIT " + m_start + "," + m_pagecount);
        }


    } catch (Exception e) {
    }
    //----------------------------

%>

<table width="100%" cellpadding="0" cellspacing="1" bgcolor="#999999">
    <tr>
        <td class="tbl_row_header"
            onclick="ViewGrid('applicationItemListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t2.n_mnuid,t2.n_itm_seq_no,t2.n_itmid',<%=m_clickPos%>)">
            No.
        </td>
        <td class="tbl_row_header"></td>
        <td class="tbl_row_header"
            onclick="ViewGrid('applicationItemListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t2.n_mnuid',<%=m_clickPos%>)">
            Menu ID
        </td>
        <td class="tbl_row_header"
            onclick="ViewGrid('applicationItemListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t2.n_mnuid,t2.n_itmid',<%=m_clickPos%>)">
            Sub Menu ID
        </td>
        <td class="tbl_row_header"
            onclick="ViewGrid('applicationItemListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t2.n_mnuid,t2.v_itmname',<%=m_clickPos%>)">
            Sub Menu Name
        </td>
        <td class="tbl_row_header"
            onclick="ViewGrid('applicationItemListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t2.n_mnuid,t2.v_url',<%=m_clickPos%>)">
            URL
        </td>
    </tr>


    <%
        int cnt = 1;

        if (list != null) {
            Iterator<SubMenuItem> it = list.iterator();
            while (it.hasNext()) {
                m_SubMenuItem = it.next();
                request.setAttribute("SubMenuItemBean", m_SubMenuItem);

                destinationURL = URL + ""
                        + "&P_N_PRGID=" + m_SubMenuItem.getN_prgid()
                        + "&P_N_MNUID=" + m_SubMenuItem.getN_mnuid()
                        + "&P_N_ITMID=" + m_SubMenuItem.getN_itmid()
                        + "&P_ISNEWRECORD=false";

                if (tmp_mnuid != m_SubMenuItem.getN_mnuid()) {
    %>
    <tr>
        <td colspan="6"
            style="font-size:12px; font-family:Arial, Helvetica, sans-serif;padding-top:5px;padding-bottom:5px; background-color:#CCC;">
            &nbsp;&nbsp;<%=m_SubMenuItem.getV_mnuname()%>
        </td>
    </tr>

    <% }
        tmp_mnuid = m_SubMenuItem.getN_mnuid();

    %>
    <jsp:useBean id="SubMenuItemBean" scope="request" type="com.misyn.mcms.admin.SubMenuItem"/>
    <input name="P_N_PRGID<%=(cnt - 1)%>" type="hidden" value="<%=m_SubMenuItem.getN_prgid()%>"/>
    <input name="P_N_MNUID<%=(cnt - 1)%>" type="hidden" value="<%=m_SubMenuItem.getN_mnuid()%>"/>
    <input name="P_N_ITMID<%=(cnt - 1)%>" type="hidden" value="<%=m_SubMenuItem.getN_itmid()%>"/>

    <%
        if ((cnt % 2) == 0) {
    %>
    <tr  <%=tdClass1%> >
        <td>&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            <input name="chkboxDelete<%=(cnt - 1)%>" id="chkboxDelete<%=(cnt - 1)%>" class="chkBox" type="checkbox"
                   value="checked"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getN_mnuid()%>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getN_itmid()%>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getV_itmname()%>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getV_url()%>
        </td>

    </tr>
    <%
    } else {
    %>
    <tr  <%=tdClass2%> >
        <td>&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            <input name="chkboxDelete<%=(cnt - 1)%>" id="chkboxDelete<%=(cnt - 1)%>" class="chkBox" type="checkbox"
                   value="checked"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getN_mnuid()%>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getN_itmid()%>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getV_itmname()%>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <%=m_SubMenuItem.getV_url()%>
        </td>

    </tr>
    <% }
        cnt++;
    }
    }
    %>
    <input name="P_END_POS" type="hidden" value="<%=(cnt - 1)%>"/>
</table>
