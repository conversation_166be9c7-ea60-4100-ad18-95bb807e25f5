<%--
    Document   : applicationItemResult
    Created on : Dec 24, 2010, 6:46:15 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : <PERSON>lum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.ApplicationParameter" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="com.misyn.mcms.admin.SubMenuItem" %>
<%@page import="java.util.LinkedList" %>
<%@page import="java.util.List" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="SubMenuItemManagerBean" class="com.misyn.mcms.admin.SubMenuItemManager" scope="application"/>

<%
    long timeURL = System.currentTimeMillis();
    int n_prgid = 1;
    SubMenuItem subMenuItem = new SubMenuItem();
    List<ApplicationParameter> applicationParametersList = new LinkedList<ApplicationParameter>();
    ApplicationParameter applicationParameter = null;


    try {
        subMenuItem.setN_prgid(n_prgid);
    } catch (Exception e) {
    }
    try {
        subMenuItem.setN_mnuid(Integer.parseInt(request.getParameter("txtN_mnuid")));
    } catch (Exception e) {
    }
    try {
        subMenuItem.setN_itmid(Integer.parseInt(request.getParameter("txtN_itmid")));
    } catch (Exception e) {
    }
    try {
        subMenuItem.setV_itmname(request.getParameter("txtV_itmname"));
    } catch (Exception e) {
    }
    try {
        subMenuItem.setV_url(request.getParameter("txtV_url"));
    } catch (Exception e) {
    }
    try {
        subMenuItem.setV_target(request.getParameter("txtV_target"));
    } catch (Exception e) {
    }
    try {
        subMenuItem.setV_apptype("Y");
        subMenuItem.setN_itm_seq_no(1000);
        subMenuItem.setV_inpuser(user.getV_usrid());
    } catch (Exception e) {
    }

    int paraCnt = 0;
    try {
        paraCnt = Integer.parseInt(request.getParameter("hideRecCount"));
    } catch (Exception e) {
    }

    for (int x = 1; x <= paraCnt; x++) {
        applicationParameter = new ApplicationParameter();
        applicationParameter.setParaCode(request.getParameter("txtParaCode" + x));
        applicationParameter.setSelType(request.getParameter("txtSelectType" + x));
        applicationParameter.setTableName(request.getParameter("txtTableName" + x));
        applicationParameter.setFieldname(request.getParameter("txtFieldNames" + x));
        applicationParameter.setValue(request.getParameter("txtStaticValue" + x));
        applicationParametersList.add(applicationParameter);

    }
    int result = SubMenuItemManagerBean.saveApplicationParameter(applicationParametersList, subMenuItem);

    if (result > 0) {
        response.sendRedirect("applicationItemList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
    } else {
        response.sendRedirect("applicationItemList.jsp?" + timeURL + "&P_ERROR=Can not be save");
    }

%>


