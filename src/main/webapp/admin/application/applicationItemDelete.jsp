<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.SubMenuItem" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="SubMenuItemManagerBean" class="com.misyn.mcms.admin.SubMenuItemManager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%

    boolean isSearchPage = false;
    try {
        isSearchPage = (Boolean) session.getAttribute("ISSEARCH_PAGE");
    } catch (Exception e) {
    }
    SubMenuItem m_subMenuItem = null;
    List<SubMenuItem> subMenuItemList = new ArrayList<SubMenuItem>();
    String chkvalue = "";

    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) {
            chkvalue = "";
        }
        if (chkvalue.equalsIgnoreCase("checked")) {
            m_subMenuItem = new SubMenuItem();

            try {
                m_subMenuItem.setN_prgid(Integer.parseInt(request.getParameter("P_N_PRGID" + x)));
            } catch (Exception e) {
            }
            try {
                m_subMenuItem.setN_mnuid(Integer.parseInt(request.getParameter("P_N_MNUID" + x)));
            } catch (Exception e) {
            }
            try {
                m_subMenuItem.setN_itmid(Integer.parseInt(request.getParameter("P_N_ITMID" + x)));
            } catch (Exception e) {
            }

            subMenuItemList.add(m_subMenuItem);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }

    //================Start Delete only one recode=================
    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("P_TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {
        m_subMenuItem = new SubMenuItem();
        try {
            m_subMenuItem.setN_prgid(user.getN_prgid());
        } catch (Exception e) {
        }
        try {
            m_subMenuItem.setN_mnuid(Integer.parseInt(request.getParameter("txtN_mnuid")));
        } catch (Exception e) {
        }
        try {
            m_subMenuItem.setN_itmid(Integer.parseInt(request.getParameter("txtN_itmid")));
        } catch (Exception e) {
        }

        subMenuItemList.add(m_subMenuItem);
    }
    //================End Delete only one recode=================

    //out.print("Size "+rolePrivilegeList.size());
    if (subMenuItemList.size() > 0) {
        SubMenuItemManagerBean.deleteAubMenuItem(subMenuItemList);
    }
    if (isSearchPage != true) {
        response.sendRedirect("applicationItemList.jsp?P_ERROR=" + SubMenuItemManagerBean.getMsg());
    } else {
        response.sendRedirect("applicationItemList.jsp?P_ERROR=" + SubMenuItemManagerBean.getMsg());
    }
%>


