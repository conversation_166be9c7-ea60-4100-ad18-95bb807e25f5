<%--
    Document   : applicationItemResult
    Created on : Dec 24, 2010, 6:46:15 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="UserPasswordParaManagerBean" class="com.misyn.mcms.admin.UserPasswordParaManager" scope="application"/>

<%
    long timeURL = System.currentTimeMillis();
    int n_prgid = 1;
    UserPasswordPara userPasswordPara = new UserPasswordPara();


    try {
        userPasswordPara.setN_accessusrtype(Integer.parseInt(request.getParameter("txtN_accessusrtype")));
    } catch (Exception e) {
    }
    try {
        userPasswordPara.setN_attempt(Integer.parseInt(request.getParameter("txtN_attempt")));
    } catch (Exception e) {
    }
    try {
        userPasswordPara.setN_timeout(Integer.parseInt(request.getParameter("txtN_timeout")));
    } catch (Exception e) {
    }
    try {
        userPasswordPara.setN_pswexpiryNodays(Integer.parseInt(request.getParameter("txtN_pswexpiryNodays")));
    } catch (Exception e) {
    }
    try {
        userPasswordPara.setN_lstloginexpirydays(Integer.parseInt(request.getParameter("txtN_lstloginexpirydays")));
    } catch (Exception e) {
    }
    try {
        userPasswordPara.setV_home_page_url(request.getParameter("txtV_home_page_url"));
    } catch (Exception e) {
    }
    try {

        userPasswordPara.setV_inpuser(user.getV_usrid());
    } catch (Exception e) {
    }


    int result = UserPasswordParaManagerBean.saveUserPasswordPara(userPasswordPara);

    if (result > 0) {
        response.sendRedirect("passwordParameterList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
    } else {
        response.sendRedirect("passwordParameterList.jsp?" + timeURL + "&P_ERROR=Can not be save");
    }

%>


