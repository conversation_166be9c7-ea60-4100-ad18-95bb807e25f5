<%--
    Document   : rolePrev
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 17, 2010, 10:02:58 AM
    Author     : <PERSON><PERSON>
--%>

<%@include file="/common/ValidateUser.jsp" %>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@page import="com.misyn.mcms.roleFacility.RolePrivilege" %>
<%@page import="java.util.List" %>

<jsp:useBean id="UserPrivilegeManagerBean" class="com.misyn.mcms.roleFacility.UserPrivilegeManager"
             scope="application"/>

<%@page contentType="text/html" pageEncoding="UTF-8" %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>


    <%!String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";%>

    <% String ADD_RIGHT = "disabled='disabled'";
        String DELETE_RIGHT = "disabled='disabled'";
        if (((String) session.getAttribute("RIGHT_D")).equals("checked")) {
            DELETE_RIGHT = "";
        }

        long timeURL = System.currentTimeMillis();
        int n_usrcode = -1;
        int n_comid = -1;
        RolePrivilege rolePrivilege = null;
        int tempMenuId = -1;
        int divId = -1;
        boolean isNextRecLast = false;
        String URL = "userPrevResult.jsp?" + timeURL;

        String str_n_comid_popList = "";
        String str_n_usrcode_popList = "";

        try {
            n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
        } catch (Exception e) {
        }

        try {
            n_usrcode = Integer.parseInt(request.getParameter("P_N_USRCODE"));
        } catch (Exception e) {
        }

        boolean isNewRecord = true;
        try {
            isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
        } catch (Exception e) {
        }

        int size = 0;
        List<RolePrivilege> list = null;

        try {
            if (isNewRecord) {
                if (((String) session.getAttribute("RIGHT_I")).equals("checked")) {
                    ADD_RIGHT = "";
                }
                if (user.getN_accessusrtype() == 1) {//SupAdmin
                    str_n_comid_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("company_mst ", "n_comid", "v_comcode", "", "");

                    str_n_usrcode_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("usrtype_mst ", "n_usrtype", "v_name", ""
                                    + "n_comid IN(-1," + n_comid + ") "
                                    + "AND "
                                    + "n_usrtype NOT IN(SELECT n_usrtype FROM prev_mst GROUP BY n_usrtype)"
                                    + "", "");

                } else if (user.getN_accessusrtype() == 2) {    //Admin
                    str_n_comid_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("company_mst ", "n_comid", "v_comcode", "", "");
                    str_n_usrcode_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("usr_mst ", "n_usrcode", "v_usrid",
                                    "n_accessusrtype NOT IN(1,2) "
                                            + "AND "
                                            + "n_usrcode NOT IN(SELECT n_usrcode FROM userprev_mst GROUP BY n_usrcode) "
                                            + "AND "
                                            + "n_comid=" + n_comid, "");

                } else if (user.getN_accessusrtype() == 3) { //AgentAdmin
                    n_comid = user.getN_comid();
                    str_n_comid_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");
                    str_n_usrcode_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("usr_mst ", "n_usrcode", "v_usrid",
                                    "n_accessusrtype IN(6) "
                                            + "AND "
                                            + "n_usrcode NOT IN(SELECT n_usrcode FROM userprev_mst GROUP BY n_usrcode) "
                                            + "AND "
                                            + "n_comid=" + n_comid, "");

                } else if (user.getN_accessusrtype() == 4) { //InternalAdmin
                    n_comid = user.getN_comid();
                    str_n_comid_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");
                    str_n_usrcode_popList = DbRecordCommonFunction.getInstance().
                            getPopupList("usr_mst ", "n_usrcode", "v_usrid",
                                    "n_accessusrtype IN(7) "
                                            + "AND "
                                            + "n_usrcode NOT IN(SELECT n_usrcode FROM userprev_mst GROUP BY n_usrcode) "
                                            + "AND "
                                            + "n_comid=" + n_comid, "");

                }
            } else {
                if (((String) session.getAttribute("RIGHT_M")).equals("checked")) {
                    ADD_RIGHT = "";
                }
                try {
                    n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
                } catch (Exception e) {
                }

                try {
                    n_usrcode = Integer.parseInt(request.getParameter("P_N_USRCODE"));
                } catch (Exception e) {
                }

                str_n_comid_popList = DbRecordCommonFunction.getInstance().
                        getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + n_comid, "");
                str_n_usrcode_popList = DbRecordCommonFunction.getInstance().
                        getPopupList("usr_mst ", "n_usrcode", "v_usrid",
                                "n_usrcode=" + n_usrcode, "");

                list = UserPrivilegeManagerBean.getUserPrivilege_List(user, n_usrcode);
                size = list.size();
            }
        } catch (Exception e) {
        }


    %>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>


    <script type="text/javascript">
        var timeUrl = new Date().getDate();

        $(function () {
            $("#accordion").accordion({
                //icons: {header: "ui-icon-circle-arrow-e",headerSelected: "ui-icon-circle-arrow-s"},
                autoHeight: false,
                active: false,
                active: 0,
                alwaysOpen: false
            });
        });

        function setAccordion(idno) {

            $(function () {
                $("#accordion" + idno).accordion({
                    //icons: {header: "ui-icon-circle-arrow-e",headerSelected: "ui-icon-circle-arrow-s"},
                    autoHeight: false,
                    collapsible: true,
                    active: false,
                    alwaysOpen: false
                });
            });
        }


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            if ($("select#txtN_comid").val() == "-1") {
                                $("select#txtN_comid").focus();
                                return;
                            }
                            // if($("select#txtN_usrtype").val()=="-1"){$("select#txtN_usrtype").focus();return;}
                            if ($("input#txtD_CAMP_EFCT_DATE").val() == "") {
                                $("input#txtD_CAMP_EFCT_DATE").focus();
                                return;
                            }
                            if ($("input#txtV_CAMP_OWNER").val() == "") {
                                $("input#txtV_CAMP_OWNER").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--


        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN_comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew)
                document.frmForm.action = "rolePrev.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else
                document.frmForm.action = "rolePrev.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        var ld = (document.all);

        function pageSubmit(type) {

            if (type == 'Save') {
                if (document.getElementById("txtN_comid").value == "-1") {
                    showDialogbox("Please Select Company Code");
                    return;
                }


                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }
            else if (type == 'Close') {
                window.location = "userPrevList.jsp?<%=timeURL%>";
            }

        }

        function init() {

            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";

        }


    </script>


    <title>${CompanyTitle}</title>
    <style type="text/css">
        <!--
        body {
            margin-top: 0px;
        }

        -->
    </style>
</head>
<body onload="init();" class="scroll">
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h6 class="float-left text-dark hide-for-small">User Role Privileges Details</h6>
        </div>
    </div>
<form name="frmForm" action="" method="post">

    <div class="row">
        <div class="col-lg-5 mt-2">
            <label> User Role Privileges Details : </label>
            <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are
                mandatory.
            </div>
        </div>
    </div>


    <div class="row ">
        <div class="col-lg-12 mt-3 ">
            <div class="row ">
                <div class="form-group col-lg-4">
                    <label>Company</label>
                    <select class="form-control lstBox" name="txtN_comid" id="txtN_comid" onchange="refresh_n_comid();">
                        <%out.print(str_n_comid_popList);%>
                    </select>
                </div>
                <div class="form-group col-lg-4">
                    <label>User ID </label>
                    <select id="txtN_usrcode" name="txtN_usrcode" class="lstBox form-control">
                        <%out.print(str_n_usrcode_popList);%>
                    </select>
                </div>
                <div class="form-group col-lg-4 " style="margin-top: 33px;">

                    <input name="cmdSave" id="cmdSave" class="btn btn-primary" type="button" value="Save" <%=ADD_RIGHT%>
                           onclick="pageSubmit('Save')">

                    <input name="cmdClose" class="btn btn-primary" id="cmdClose" type="button" value="Close"
                           onclick="pageSubmit('Close')">

                </div>
            </div>


        </div>
    </div>


    <div id="spacer" style="height:5px;">&nbsp;</div>
    <input name="hideRecCount" type="hidden" value="<%=size%>"/>
    <%
        String title1 = "Name";
        String title2 = "View";
        String title3 = "Add";
        String title4 = "Modify";
        String title5 = "Delete";
        String title6 = "Grant";

        for (int i = 0; i < size; i++) {

            rolePrivilege = list.get(i);

            if (tempMenuId != rolePrivilege.getN_mnuid()) {
                divId = rolePrivilege.getN_mnuid();
                if (divId == 4) {
                    title3 = "Intimation FW";
                    title4 = "Assign Assessor";
                    title5 = "Reject";
                    title6 = "Grant";
                } else if (divId == 6) {
                    title3 = "Inspection Save";
                    title4 = "Inspection Submit";
                    title5 = "File Upload";
                    title6 = "Grant";
                }
    %>

    <div class="card mt-2">
        <div class="card-header padding5px" style="padding: 5px!important;" id="heading<%=divId%>">
            <h5 class="mb-0">
                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse<%=divId%>" aria-expanded="true"
                   aria-controls="collapse<%=divId%>">
                    <%=rolePrivilege.getV_mnuname()%>
                </a>
            </h5>
        </div>
        <div id="collapse<%=divId%>" class="collapse" aria-labelledby="heading<%=divId%>" data-parent="#accordion">
            <div class="card-body">
                <div class="col-sm-12">
                    <table id="demo-dt-basic" class="table table-sm table-hover dataTable no-footer" cellspacing="0"
                           style="cursor: pointer; width: 80%; margin: 0 auto;">
                        <thead>
                        <tr>
                            <th width="200" class="MainMenu_Heading"><%=title1%>
                            </th>
                            <th width="150" class="MainMenu_Heading"><%=title2%>
                            </th>
                            <th width="200" class="MainMenu_Heading"><%=title3%>
                            </th>
                            <th width="230" class="MainMenu_Heading"><%=title4%>
                            </th>
                            <th width="150" class="MainMenu_Heading"><%=title5%>
                            </th>
                            <th width="150" class="MainMenu_Heading"><%=title6%>
                            </th>
                        </tr>
                        </thead>

                        <tbody>
                        <% }%>
                        <tr>
                            <input name="hide_n_usrtype<%=i%>" type="hidden" value="<%=rolePrivilege.getN_usrtype()%>"/>
                            <input name="hide_n_itmid<%=i%>" type="hidden" value="<%=rolePrivilege.getN_itmid()%>"/>
                            <td class="SubMenu_Heading"><%=rolePrivilege.getV_itmname()%>
                            </td>
                            <td class="SubMenu_Heading"><input name="chkboxView<%=i%>" <%=rolePrivilege.getV_view()%>
                                                               type="checkbox" value="checked"></td>
                            <td class="SubMenu_Heading"><input name="chkboxAdd<%=i%>"  <%=rolePrivilege.getV_input()%>
                                                               type="checkbox" value="checked"></td>
                            <td class="SubMenu_Heading"><input
                                    name="chkboxModify<%=i%>" <%=rolePrivilege.getV_modify()%>
                                    type="checkbox" value="checked"></td>
                            <td class="SubMenu_Heading"><input
                                    name="chkboxDelete<%=i%>" <%=rolePrivilege.getV_delete()%>
                                    type="checkbox" value="checked"></td>
                            <td class="SubMenu_Heading"><input name="chkboxGrant<%=i%>" <%=rolePrivilege.getV_grant()%>
                                                               type="checkbox" value="checked"></td>
                        </tr>
                        <%
                            try {
                                if (rolePrivilege.getN_mnuid() != list.get(i + 1).getN_mnuid()) {
                                    isNextRecLast = true;
                                }
                            } catch (Exception e) {
                                isNextRecLast = true;
                            }
                            if (isNextRecLast) {
                                isNextRecLast = false;
                        %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <script>
        setAccordion(<%=divId%>);
    </script>
    <%
        }
        tempMenuId = rolePrivilege.getN_mnuid();
        title1 = "Name";
        title2 = "View";
        title3 = "Add";
        title4 = "Modify";
        title5 = "Delete";
        title6 = "Grant";
    %>
    <input name="hide_n_mnuid<%=i%>" type="hidden" value="<%=rolePrivilege.getN_mnuid()%>"/>
    <%
        }
    %>
    <script type="text/javascript">
        document.getElementById("txtN_comid").value = "<%=n_comid%>";
        document.getElementById("txtN_usrcode").value = "<%=n_usrcode%>";
    </script>
</form>
</div>
</div>
</body>
</html>
