<%--
    Document   : rolePrevResult
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 19, 2010, 2:34:45 PM
    Author     : <PERSON><PERSON>
--%>

<%@page import="com.misyn.mcms.roleFacility.RolePrivilege" %>
<%@page import="java.util.ArrayList" %>
<%@page import="java.util.List" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="UserPrivilegeManagerBean" class="com.misyn.mcms.roleFacility.UserPrivilegeManager"
             scope="application"/>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">

<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="/css/common/loding.css" rel="stylesheet" type="text/css"/>
    <title>${CompanyTitle}</title>
</head>
<body>
<div id="loading" class="lodingImage" style="position:absolute;top:120px;">
    <img style="z-index:1002" alt="Loading..." src="/image/common/ajax-loader6.gif" border=0>
    <div class="lodingText">RECORD IS IN PROGRESS ...</div>
    <div class="labelText" align="center"><p>Don't close this browser window or navigate until the progress is
        complete</p></div>
</div>
<div id="cell1" class="innerDivBg"></div>
<%
    long timeURL = System.currentTimeMillis();
    RolePrivilege rolePrivilege = null;
    List<RolePrivilege> rolePrivilegeList = new ArrayList<RolePrivilege>();
    int recCount = 0;
    try {
        recCount = Integer.parseInt(request.getParameter("hideRecCount"));
    } catch (Exception e) {
    }

    int n_comid = 0;
    try {
        n_comid = Integer.parseInt(request.getParameter("txtN_comid"));
    } catch (Exception e) {
    }
    int n_usrcode = 0;
    try {
        n_usrcode = Integer.parseInt(request.getParameter("txtN_usrcode"));
    } catch (Exception e) {
    }


    for (int i = 0; i < recCount; i++) {
        rolePrivilege = new RolePrivilege();
        rolePrivilege.setN_comid(n_comid);
        rolePrivilege.setN_prgid(1);
        rolePrivilege.setN_usrcode(n_usrcode);
        rolePrivilege.setN_homepage(0);
        try {
            rolePrivilege.setN_usrtype(Integer.parseInt(request.getParameter("hide_n_usrtype" + i)));
        } catch (Exception e) {
        }
        try {
            rolePrivilege.setN_mnuid(Integer.parseInt(request.getParameter("hide_n_mnuid" + i)));
        } catch (Exception e) {
        }
        try {
            rolePrivilege.setN_itmid(Integer.parseInt(request.getParameter("hide_n_itmid" + i)));
        } catch (Exception e) {
        }
        rolePrivilege.setV_view(request.getParameter("chkboxView" + i));
        rolePrivilege.setV_input(request.getParameter("chkboxAdd" + i));
        rolePrivilege.setV_modify(request.getParameter("chkboxModify" + i));
        rolePrivilege.setV_delete(request.getParameter("chkboxDelete" + i));
        rolePrivilege.setV_grant(request.getParameter("chkboxGrant" + i));
        rolePrivilegeList.add(rolePrivilege);
    }
    if (rolePrivilegeList.size() > 0) {
        int r = UserPrivilegeManagerBean.saveUserPrivilege(rolePrivilegeList);
        if (r > -1) {
            response.sendRedirect("userPrevList.jsp?" + timeURL + "&P_ERROR=Record successfully saved");
        } else {
            response.sendRedirect("userPrevList.jsp?" + timeURL + "&P_ERROR=Unable to save record");
        }
    }
%>
</body>
</html>
