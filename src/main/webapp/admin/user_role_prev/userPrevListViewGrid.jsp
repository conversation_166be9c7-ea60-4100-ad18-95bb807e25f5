<%@page import="com.misyn.mcms.roleFacility.RolePrivilege" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@page import="java.util.Iterator" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserPrivilegeManagerBean" class="com.misyn.mcms.roleFacility.UserPrivilegeManager"
             scope="application"/>

<%

    String tdClass1 = "class=\"tbl_row\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row';\"";
    String tdClass2 = "class=\"tbl_row2\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row2';\"";

    //*********Start Sort Fields ***************
    boolean IS_ASC = true;
    String SORT_FIELD = request.getParameter("P_SORT_FIELD");
    String temp_SortField = "";

    String ASC_DESC_STATUS = request.getParameter("P_ASC_DESC_STATUS");
    temp_SortField = (String) session.getAttribute("FIELD");


    if (SORT_FIELD != null) {
        session.removeAttribute("FIELD");
        session.setAttribute("FIELD", SORT_FIELD);
    }
    if (ASC_DESC_STATUS != null) {

        session.removeAttribute("ASC_DESC_STATUS");
        session.setAttribute("ASC_DESC_STATUS", ASC_DESC_STATUS);
    }

    try {
        IS_ASC = Boolean.parseBoolean(request.getParameter("P_IS_ASC"));
    } catch (Exception e) {
    }


    if (session.getAttribute("IS_ASC") == null) {
        // IS_ASC = true;
        session.removeAttribute("IS_ASC");
        session.setAttribute("IS_ASC", IS_ASC);
    } else {
        IS_ASC = (Boolean) session.getAttribute("IS_ASC");
    }

    int ISBODY_LOAD = 0;
    try {
        ISBODY_LOAD = Integer.parseInt(request.getParameter("P_ISBODY_LOAD"));
    } catch (Exception e) {
    }

    if (temp_SortField != null && SORT_FIELD != null && ISBODY_LOAD == 0) {
        if (temp_SortField.toLowerCase().equalsIgnoreCase(SORT_FIELD.toLowerCase())) {
            if (IS_ASC) {
                IS_ASC = false;
                ASC_DESC_STATUS = " DESC ";
            } else {
                IS_ASC = true;
                ASC_DESC_STATUS = " ASC ";
            }

        } else {
            if (IS_ASC) {
                IS_ASC = false;
                ASC_DESC_STATUS = " DESC ";
            } else {
                IS_ASC = true;
                ASC_DESC_STATUS = " ASC ";
            }

        }
        session.removeAttribute("ASC_DESC_STATUS");
        session.setAttribute("ASC_DESC_STATUS", ASC_DESC_STATUS);

        session.removeAttribute("IS_ASC");
        session.setAttribute("IS_ASC", IS_ASC);
    }

    SORT_FIELD = (String) session.getAttribute("FIELD") + " " + (String) session.getAttribute("ASC_DESC_STATUS");

    if (SORT_FIELD != null) {
        session.removeAttribute("STR_FIELD");
        session.setAttribute("STR_FIELD", SORT_FIELD);
    }
    String str_sort_field = (String) session.getAttribute("STR_FIELD");
    //out.print("str_sort_field " + str_sort_field + " IS_ASC: " + IS_ASC);
    //*********End Sort Fields ***************

    int m_clickPos = 1;
    int m_pagecount = com.misyn.mcms.utility.Parameters.getInstance().getNoRecordPerPage();

    boolean isClickNavigat = true;
    try {
        m_clickPos = Integer.parseInt(request.getParameter("P_Click_Pos"));
    } catch (Exception e) {
        isClickNavigat = false;
    }

    try {
        if (!isClickNavigat) {
            m_clickPos = (Integer) session.getAttribute("CLICK_PAGE_NUMBER");
            //out.println("XXXXXXXXXXXXXXXX"+m_clickPos);
        }
    } catch (Exception e) {
    }

    session.removeAttribute("CLICK_PAGE_NUMBER");
    session.setAttribute("CLICK_PAGE_NUMBER", m_clickPos);
    int m_start = 0;
    int m_end = m_pagecount * m_clickPos;
    m_start = m_end - m_pagecount;


    //---------------------------
    int TYPE = 0;
    long timeURL = System.currentTimeMillis();
    String URL = "userPrev.jsp?" + timeURL;


    List<RolePrivilege> list = null;

    try {
        list = UserPrivilegeManagerBean.getUserPrivilegeViewList(user,
                "",
                "ORDER BY " + str_sort_field + " LIMIT " + m_start + "," + m_pagecount);

    } catch (Exception e) {
    }
    //----------------------------

%>

<table width="100%" cellpadding="0" cellspacing="1" bgcolor="#999999">
    <thead class="blueheader">
    <tr>
        <td class="tbl_row_header"
            onclick="ViewGrid('userPrevListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t1.n_usrcode',<%=m_clickPos%>)">No.
        </td>
        <td class="tbl_row_header"
            onclick="ViewGrid('userPrevListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t3.v_usrid',<%=m_clickPos%>)">User Id
        </td>
        <td class="tbl_row_header"
            onclick="ViewGrid('userPrevListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t3.v_usrtype_desc',<%=m_clickPos%>)">
            User Roles
        </td>
        <td class="tbl_row_header"
            onclick="ViewGrid('userPrevListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=t2.v_comcode',<%=m_clickPos%>)">
            Company
        </td>
    </tr>
    </thead>
    <tbody>
    <%
        int cnt = 1;

        if (list != null) {
            Iterator<RolePrivilege> it = list.iterator();
            while (it.hasNext()) {
                request.setAttribute("UserPrivilegeBean", it.next());

    %>
    <jsp:useBean id="UserPrivilegeBean" scope="request" type="com.misyn.mcms.roleFacility.RolePrivilege"/>

    <%
        if ((cnt % 2) == 0) {
    %>
    <tr onClick="window.location='<%=URL%>&P_N_COMID=<%=UserPrivilegeBean.getN_comid()%>&P_N_USRCODE=<%=UserPrivilegeBean.getN_usrcode()%>&P_ISNEWRECORD=false'"  <%=tdClass1%> >
        <td>&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            <%=UserPrivilegeBean.getV_usrid()%>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="UserPrivilegeBean" property="v_usrtype_name"/>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="UserPrivilegeBean" property="v_comCode"/>
        </td>

    </tr>
    <%
    } else {
    %>
    <tr onClick="window.location='<%=URL%>&P_N_COMID=<%=UserPrivilegeBean.getN_comid()%>&P_N_USRCODE=<%=UserPrivilegeBean.getN_usrcode()%>&P_ISNEWRECORD=false'"  <%=tdClass2%> >
        <td>&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            <%=UserPrivilegeBean.getV_usrid()%>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="UserPrivilegeBean" property="v_usrtype_name"/>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="UserPrivilegeBean" property="v_comCode"/>
        </td>

    </tr>
    <% }
        cnt++;
    }
    }
    %>
    </tbody>
</table>
