<%--
  Created by IntelliJ IDEA.
  User: madhushanka
  Date: 12/12/2019
  Time: 12:18 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmBranchDetailList" id="frmBranchDetailList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Branch Details</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <div class="row">
                    <div class="col-md-12">
                        <div id="accordion" class="accordion">
                            <div class="card">
                                <div class="card-header" id="headingOne">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                           data-target="#collapseOne"
                                           aria-expanded="true" aria-controls="collapseOne">
                                            Search Here <i class="fa fa-search"></i>
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                                     data-parent="#accordion">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="branchCode" class="col-md-4 col-form-label">Branch
                                                        Code</label>
                                                    <div class="col-md-8">
                                                        <input name="branchCode" id="branchCode"
                                                               class="form-control form-control-sm"
                                                               placeholder="Branch Code">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label for="branchCity" class="col-md-4 col-form-label">Branch
                                                        City</label>
                                                    <div class="col-md-8">
                                                        <input name="branchCity" id="branchCity"
                                                               class="form-control form-control-sm"
                                                               placeholder="Branch City">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="branchName" class="col-md-4 col-form-label">Branch
                                                        Name</label>
                                                    <div class="col-md-8">
                                                        <input name="branchName" id="branchName"
                                                               class="form-control form-control-sm"
                                                               placeholder="Branch Name">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 text-right">
                                                <button class="btn btn-primary" type="button" name="cmdSearch"
                                                        id="cmdSearch"
                                                        onclick="search()">Search
                                                </button>
                                                <a class="btn btn-secondary" type="button" name="cmdClose"
                                                   id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                                </a>
                                                <hr>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <div class="row float-right">
                            <button type="button" class="btn btn-primary mr-1" id="btnAdd"
                                    onclick="viewBranchDetails('','Save')">Add Branch Details
                            </button>
                            <button type="button" class="btn btn-danger mr-3" id="btnDelete"
                                    onclick="getSelectedBranchesCode(document.getElementsByName('checkBox'))" disabled>
                                Delete Branches
                            </button>

                        </div>
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th class=""><input type="checkbox" id="selectAll"
                                                    onclick="selectAllCheckBoxes();"/></th>
                                <th>No</th>
                                <th>Branch Code</th>
                                <th>Branch Name</th>
                                <th>Branch City</th>
                                <th>Input User</th>
                                <th>Input Date Time</th>
                                <th class="min-mobile">Edit</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </form>

    <input type="hidden" id="selectedBranchesCode" name="selectedBranchesCode">

    <form name="frmBranchDetails" id="frmBranchDetails" method="post">
        <!-- Modal -->
        <div class="modal fade animated fadeInDown" id="panelBranch" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> Branch Code</label>
                            <div class="col-sm-8">
                                <c:choose>
                                    <c:when test="${ACTION=='UPDATE'}">
                                        <input id="pnlBranchCode" name="branchCode" autocomplete="off"
                                               class="form-control form-control-sm"
                                               readonly/>
                                    </c:when>
                                    <c:otherwise>
                                        <input id="pnlBranchCode" name="branchCode"
                                               class="form-control form-control-sm" autocomplete="off"/>
                                    </c:otherwise>
                                </c:choose>
                                <div class="text-danger" id="errorDiv" style="display: none">
                                    Branch code cannot be duplicated
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> Branch Name</label>
                            <div class="col-sm-8">
                                <input id="pnlBranchName" name="branchName" class="form-control form-control-sm"/>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> Branch City</label>
                            <div class="col-sm-8">
                                <input id="pnlBranchCity" name="branchCity" class="form-control form-control-sm"/>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="submit" id="btnSubmit" class="btn btn-primary">
                        <input type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()"
                               value="Close">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/admin/branch-management-datatables.js?v3"></script>
<script type="text/javascript">

    $("#frmBranchDetails").submit(function (e) {
        e.preventDefault();
    });

    function updateBranchDetails() {

        var formData = $('#frmBranchDetails').serialize();
        if ($('#btnSubmit').val() == 'Update') {
            $.ajax({
                async: false,
                url: contextPath + "/UserManagementController/updateBranchDetails",
                type: 'POST',
                data: formData,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != null) {
                        if (obj == "SUCCESS") {
                            notify("Successfully Updated", "success");
                            closeModal();
                        } else {
                            notify("Can not be updated", "danger");
                        }

                    }
                }
            });
        } else {
            $.ajax({
                async: false,
                url: contextPath + "/UserManagementController/saveBranchDetails",
                type: 'POST',
                data: formData,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != null) {
                        if (obj == "SUCCESS") {
                            notify("Successfully Saved", "success");
                            closeModal();
                        } else if ('DUPLICATE_CODE') {
                            notify("Branch code cannot be duplicated", "danger");
                        } else {
                            notify("Can not be saved", "danger");
                        }

                    }
                }
            });
        }
    }

    function closeModal() {
        $('#panelBranch').modal('hide');
        $('#panelBranch').modal('hide');
        destroyValidation();
        search();
        $('#errorDiv').hide();
        $('#pnlBranchName').attr('readOnly', false);
        $('#pnlBranchCity').attr('readOnly', false);
    }

    function getSelectedBranchesCode(checkBoxElements) {
        var selectedBranches = "";
        for (var i = 0; i < checkBoxElements.length; i++) {
            var code = checkBoxElements[i].getAttribute('id');

            if (document.getElementById(code).checked) {
                var valCode = code;
                selectedBranches += valCode + ",";
            }
        }
        if (selectedBranches != '') {
            deleteSelectedBranches(selectedBranches);
        } else {
            notify('Please Select One', "danger");
            $("#btnDelete").prop("disabled", true);
        }
    }

    function deleteSelectedBranches(selectedBranchesCode) {
        $.ajax({
            async: false,
            url: contextPath + "/UserManagementController/deleteSelectedBranches",
            type: 'POST',
            data: {
                "selectedBranchesCode": selectedBranchesCode
            },
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    if (obj == "SUCCESS") {
                        notify("Successfully Deleted", "success");
                        search();
                    } else {
                        notify("Can not be deleted", "danger");
                        search();
                    }

                }
            }
        });
    };

</script>
</body>
</html>
