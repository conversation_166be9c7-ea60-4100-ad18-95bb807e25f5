<%--
  Created by IntelliJ IDEA.
  User: Thanura
  Date: 12/5/2019
  Time: 3:28 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script>
        function init() {
            hideLoader();
        }
    </script>
</head>
<body class="scroll" onload="init()">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input type="hidden" id="teamId" name="teamId"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Handler Team List</h5>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body table-bg">
                <div class="row">
                    <div class="col-md-12">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <h6 style="float: left;">Claim Handler Teams</h6>
                                            <div class="but_container" style="float: right">
                                                <button class="btn btn-primary" type="button" id="btnAdd"
                                                        onclick="createTeam()">Create Team
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <table id="team-table" class="table table-sm table-hover" cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th width="40px">No</th>
                                                <th>Team Name</th>
                                                <th>Channel Count</th>
                                                <th>Users Count</th>
                                                <th>Created Date</th>
                                                <th>Last Updated Date</th>
                                                <th class="min-mobile" width="40px"></th>
                                            </tr>
                                            <c:if test="${teamList == null}">
                                                <tr><i class="badge-warning">No Teams Found</i></tr>
                                            </c:if>
                                            </thead>
                                            <tbody>
                                            <c:forEach var="team" items="${teamList}">
                                                <tr>
                                                    <td>${team.index}</td>
                                                    <td>${team.teamName}</td>
                                                    <td>${team.channelCount}</td>
                                                    <td>${team.memberCount}</td>
                                                    <td>${team.createdDate}</td>
                                                    <td>${team.updatedDate}</td>
                                                    <td>
                                                        <button class='btn-primary btn' type='button' title='View Team'
                                                                onclick='viewTeam(${team.teamId})'><i
                                                                class='fa fa-eye'></i></button>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script>
    function createTeam() {
        document.getElementById('frmForm').action = contextPath + "/UserManagementController/newTeam";
        document.getElementById('frmForm').submit();
    }

    function viewTeam(teamId) {
        $("#teamId").val(teamId);
        document.getElementById('frmForm').action = contextPath + "/UserManagementController/viewTeam";
        document.getElementById('frmForm').submit();
    }
</script>
</body>
</html>
