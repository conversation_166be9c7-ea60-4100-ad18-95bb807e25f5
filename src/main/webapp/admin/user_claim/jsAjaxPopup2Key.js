/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

function AjaxPopup(txtID,hideValue,lstID,m_url)
{
    this.id_valueArray = null;
    this.txt_valueArray =null;
    this.selected_value="";

    this.lstID=lstID;
    this.txtID=txtID;
    this.hideValue=hideValue;
    this.m_url=m_url;
    var TimeURL=new Date().getTime();


    var XMLHttpFactories = [
    function () {
        return new XMLHttpRequest()
    },
    function () {
        return new ActiveXObject("Msxml2.XMLHTTP")
    },
    function () {
        return new ActiveXObject("Msxml3.XMLHTTP")
    },
    function () {
        return new ActiveXObject("Microsoft.XMLHTTP")
    }
    ];

    function GetXmlHttpObject() {
        var xmlHttp=null;
        try {
            // Firefox, Opera 8.0+, Safari
            xmlHttp = new XMLHttpRequest();
        } catch (e) {
            // Internet Explorer
            try {
                xmlHttp=new ActiveXObject("Msxml2.XMLHTTP");
            } catch (e) {
                xmlHttp=new ActiveXObject("Microsoft.XMLHTTP");
            }
        }
        return xmlHttp;
    }

    function createXMLHTTPObject() {
        var xmlhttp = null;
        for (var i=0;i<XMLHttpFactories.length;i++) {
            try {
                xmlhttp = XMLHttpFactories[i]();
            } catch (e) {
                continue;
            }
            break;
        }
        return xmlhttp;
    }





    this.showPopupListMenu=function(e,isPopBtn)
    {
        var url="";
        var xmlHttp =  GetXmlHttpObject();
        this.e=e;
        id_valueArray = new Array();
        txt_valueArray = new Array();

        if (xmlHttp==null)
        {
            alert ("Your browser does not support AJAX!");
            return;
        }

        var paraKey1=document.getElementById(txtID).value;
        var isEnter=false;
        url=m_url+"?"+TimeURL+"&searchKey1="+paraKey1;
        //alert(url);
        if(e.keyCode==40)
        {
            focusLst(this.e);
            isEnter=true;
        //alert("Key :"+e.keyCode);
        //return;
        }

        xmlHttp.onreadystatechange = function() {
            var xmlDoc=null;
            var rec_count=0;
            var txtTagId="inputtext";
            var tempTxtID="";

            document.getElementById(hideValue).value="";
            document.getElementById(lstID).style.display="none";
            document.getElementById(txtID).style.cursor='wait';


            removeItems();

            //alert("Value----> :"+xmlHttp.status);
            if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {

                xmlDoc=xmlHttp.responseXML.documentElement;
                if (xmlDoc!=null) {
                    if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
                    {
                        rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
                    }
                    for (var i=0;i<rec_count;i++)
                    {
                        txtTagId="inputtext"+(i+1);

                        if (xmlDoc.getElementsByTagName(txtTagId)[0].childNodes[0]!=null)
                        {
                            selected_value="";
                            document.getElementById(hideValue).value=selected_value;
                            id_valueArray[i]=xmlDoc.getElementsByTagName(txtTagId)[0].childNodes[0].nodeValue;
                            txt_valueArray[i]=xmlDoc.getElementsByTagName(txtTagId)[1].childNodes[0].nodeValue;
                            if(rec_count==1){
                                selected_value=id_valueArray[i];
                              //  alert("Value----> :"+selected_value)
                                tempTxtID=txt_valueArray[i];
                                if((document.getElementById(txtID).value).toLowerCase()!=tempTxtID.toLowerCase())selected_value="";
                                document.getElementById(hideValue).value=selected_value;                                
                                document.getElementById("lblV_asscode").innerHTML=document.getElementById(hideValue).value;
                                if(Trim(document.getElementById(hideValue).value)=="")
                                {
                                    // alert("Value<<<---->>> :"+selected_value)
                                      document.getElementById("lblV_asscode").innerHTML="Invalid Assessor Code";  
                                }
                            }

                            AddItem(txt_valueArray[i],id_valueArray[i]);
                            document.getElementById(lstID).style.display="block";
                        }
                    }
                    if(rec_count>0)
                    {
                        if(isPopBtn==true)
                        {
                            document.getElementById(lstID).focus();
                            document.getElementById(lstID).selectedIndex=0;
                        }
                    }
                    else
                    {
                        if(isPopBtn==true)
                        {
                            alert("Record Not Found");

                        }
                        else
                        {
                            alert(document.getElementById(txtID).value+"  is Invalid Record");
                        }
                        //showDialogbox("Record Not Found");
                        selected_value="";
                        document.getElementById(txtID).value="";
                        document.getElementById(hideValue).value="";
                        document.getElementById("lblV_asscode").innerHTML="Invalid Assessor Code"; 
                        document.getElementById(txtID).focus();
                    }
                    if(isEnter==true)
                    {
                        isEnter=false;
                        document.getElementById(lstID).focus();
                        document.getElementById(lstID).selectedIndex=0;
                    }
                    document.getElementById(txtID).style.cursor='default';

                }
            }
        }

        xmlHttp.open("POST", url, true);
        xmlHttp.send(null);
    }

    this.selectLstBox=function()
    {
        var x2=document.getElementById(lstID);
        var s2=x2.options[x2.selectedIndex].text;
        selected_value=id_valueArray[x2.selectedIndex];
        document.getElementById(txtID).value=txt_valueArray[x2.selectedIndex];
        document.getElementById(hideValue).value= selected_value;
        document.getElementById("lblV_asscode").innerHTML=document.getElementById(hideValue).value;
        document.getElementById(lstID).style.display="none";
    //alert("**********");
    }

    this.onhide=function()
    {
        document.getElementById(lstID).style.display="none";
    }

    this.EnterKeyLst=function (e)
    {

        if (!e) e=window.event;  // Block the user of digits.
        var code;
        //alert(e.keyCode);
        //if     code = e.charCode  else        code = e.keyCode;
        if ((e.charCode) && (e.keyCode==0))
        {
            code = e.charCode
        //alert(code);
        }
        else
        {
            code = e.keyCode;
        //alert(code);
        }
        if(code==13)
        {
            //alert(code);
            this.selectLstBox();
            document.getElementById(txtID).focus();

        }
    }


    function focusLst(e)
    {

        if (!e) e=window.event;  // Block the user of digits.
        var code;
        //if     code = e.charCode  else        code = e.keyCode;
        if ((e.charCode) && (e.keyCode==0))
        {
            code = e.charCode
        //alert(code);
        }
        else
        {
            code = e.keyCode;
        //alert(code);
        }
        if(code==40)
        {
            //alert(code);
            document.getElementById(lstID).focus();
            document.getElementById(lstID).selectedIndex=0;
        }
    }



    function AddItem(Text,Value)
    {
        // Create an Option object
        var opt = document.createElement("option");

        // Add an Option object to Drop Down/List Box
        document.getElementById(lstID).options.add(opt);        // Assign text and value to Option object
        opt.text = Text;
        opt.value = Value;

    }

    function removeItems()
    {
        var i;
        var selectbox=document.getElementById(lstID);
        for(i=selectbox.options.length-1;i>=0;i--)
        {
            selectbox.remove(i);
        }
    }



    function Trim(str)
    {
        while (str.substring(0,1) == ' ') // check for white spaces from beginning
        {
            str = str.substring(1, str.length);
        }
        while (str.substring(str.length-1, str.length) == ' ') // check white space from end
        {
            str = str.substring(0,str.length-1);
        }

        return str;
    }



}
