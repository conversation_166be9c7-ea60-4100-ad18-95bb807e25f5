<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
<%--    <link href="${pageContext.request.contextPath}/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>--%>
</head>
<body>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">Create User</h5>
        </div>
        <div class="col-md-12">
            <div class="error-mg">
            </div>
            <div class="mt-3 float-right mr-3">
                <button class="btn btn-primary mb-3" data-toggle="modal" data-target="#userModal" onclick="resetUserForm()">
                    + Add User
                </button>
            </div>
        </div>

        <div class="col-sm-12 py-2 ">
            <div id="accordion" class="accordion">
                <div class="card">
                    <div class="card-header" id="headingOne">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                               aria-expanded="false" aria-controls="collapseOne">
                                Search Here <i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapseOne" class="collapse " aria-labelledby="headingOne"
                         data-parent="#accordion">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group row">
                                        <label for="txtUserId" class="col-sm-4 col-form-label"> User ID
                                        </label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control form-control-sm"
                                                   placeholder="User ID"
                                                   name="txtUserId"
                                                   id="txtUserId">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtUserStatus" class="col-sm-4 col-form-label">
                                            Status </label>
                                        <div class="col-sm-8">
                                            <select name="txtPolicyStatus" id="txtUserStatus"
                                                    class="form-control form-control-sm">
                                                <option value="">All</option>
                                                <option value="D">Disable</option>
                                                <option value="A">Active</option>
                                                <option value="L">Locked</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtUserMobile" class="col-sm-4 col-form-label"> User Mobile Number
                                        </label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control form-control-sm"
                                                   placeholder="User Mobile Number"
                                                   name="txtUserMobile"
                                                   id="txtUserMobile">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">

                                    <div class="form-group row">
                                        <label for="txtUserRole" class="col-sm-4 col-form-label">
                                            User Role </label>
                                        <div class="col-sm-8">
                                            <select name="txtPolicyStatus" id="txtUserRole"
                                                    class="form-control form-control-sm">
                                                <c:forEach var="role" items="${accessUserTypeListMain}">
                                                    <option value="${role.id}">${role.name}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtUserFirstName" class="col-sm-4 col-form-label"> User First Name
                                        </label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control form-control-sm"
                                                   placeholder="User First Name"
                                                   name="txtUserFirstName"
                                                   id="txtUserFirstName">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12 text-right">

                                            <button class="btn btn-primary" type="button" name="cmdSearch"
                                                    id="cmdSearch" onclick="search()">
                                                Search
                                            </button>

                                                <a class="btn btn-secondary" type="button" name="cmdClose"
                                                   id="cmdClose"
                                                   href="${pageContext.request.contextPath}/welcome.do">Close
                                                </a>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <!-- Data Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card mt-3">
                <div class="card-body table-bg">
                    <div class="row">
                        <div class="col-md-12">
                            <table id="user-privileges-table" class="table table-sm table-hover" cellspacing="0" style="cursor:pointer" width="100%">
                                <thead class="blueheader">
                                <tr>
                                    <th>No.</th>
                                    <th>User Id</th>
                                    <th>User Role</th>
                                    <th>First Name</th>
                                    <th>Last Name</th>
                                    <th>Mobile Number</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="max-width: 75%;">
        <form class="modal-content" id="userForm" novalidate>
            <div class="modal-header">
                <h5 class="modal-title" id="userModalLabel">User Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userId" class="form-label">User Name<span class="text-danger">*</span></label>
                                <input
                                        type="text"
                                        id="userId"
                                        name="userId"
                                        class="form-control"
                                        required
                                        pattern="^[a-zA-Z0-9_]{4,20}$"
                                        title="Username must be 4–20 characters long, only letters, numbers, or underscores allowed." autocomplete="off">
                            </div>
                            <div class="mb-3">
                                <label for="department" class="form-label">Department<span class="text-danger">*</span></label>
                                <select
                                        name="department"
                                        id="department"
                                        class="form-control form-control-sm"
                                        required autocomplete="off">
                                    <option value="">-- Select Department --</option>
                                    <c:forEach var="department" items="${accessUserTypeListMain}">
                                        <option value="${department.id}">${department.name}</option>
                                    </c:forEach>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a department.
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="userRole" class="form-label">User Role<span class="text-danger">*</span></label>
                                <select name="userRole[]" id="userRole" class="form-control form-control-sm" required autocomplete="off" multiple>
                                    <c:forEach var="userRole" items="${userTypeListMain}">
                                        <option value="${userRole.id}">${userRole.name}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="userPassword" class="form-label">Password<span class="text-danger">*</span></label>
                                <input type="password" id="userPassword" name="userPassword" class="form-control"
                                       autocomplete="off" required minlength="8"
                                       pattern="^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$"
                                       title="Password must be at least 8 characters long, include an uppercase letter, a number, and a symbol.">
                                <div class="invalid-feedback">
                                    Password must be at least 8 characters long, include an uppercase letter, a number, and a symbol.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password<span class="text-danger">*</span></label>
                                <input type="password" id="confirmPassword" name="confirmPassword" class="form-control"
                                       autocomplete="off" required minlength="8"
                                       title="Confirm password must match the password.">
                                <div class="invalid-feedback">Passwords must match.</div>
                            </div>

                            <div class="mb-3">
                                <label for="userStatus" class="form-label">User Status<span class="text-danger">*</span></label>
                                <select name="userStatus" id="userStatus" class="form-control form-control-sm" required autocomplete="off">
                                    <option value="">Select Status</option>
                                    <option value="D">Disable</option>
                                    <option value="A">Active</option>
                                    <option value="L">Locked</option>
                                </select>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title<span class="text-danger">*</span></label>
                                <select name="title" id="title" class="form-control form-control-sm" required autocomplete="off">
                                    <option value="">Select Title</option>
                                    <option value="Mr">Mr.</option>
                                    <option value="Ms">Ms.</option>
                                    <option value="Mrs">Mrs.</option>
                                    <option value="Dr">Dr.</option>
                                </select>
                            </div>
                            <!-- Reserve Limit (shown for Regional roles only) -->
                            <div id="reserveLimitOnly" class="mb-3" style="display: none;">
                                <label for="reserveLimitOnlyInput" class="form-label">Reserve Limit<span class="text-danger">*</span></label>
                                <input type="text" id="reserveLimitOnlyInput" name="reserveLimitOnlyInput"
                                       class="form-control form-control-sm" autocomplete="off"
                                       pattern="^\d+(\.\d{1,2})?$"
                                       title="Reserve limit must be a positive number with up to two decimal places.">
                                <div class="invalid-feedback">Please enter a valid reserve limit (e.g., 1000 or 1000.00).</div>
                            </div>
                            <!-- Full Assessor-like Card (for Claim Handler / Payment roles) -->
                            <div id="teamFields" class="card mt-3" style="display: none;">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="teamId" class="form-label">Team ID<span class="text-danger">*</span></label>
                                        <select id="teamId" name="teamId" class="form-control form-control-sm" required autocomplete="off">
                                            <option value="">Select Team ID</option>
                                            <option value="team-1">Team 1</option>
                                            <option value="team-2">Team 2</option>
                                            <option value="team-3">Team 3</option>
                                            <!-- Add more as needed -->
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="liabilityLimit" class="form-label">Liability Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="liabilityLimit" name="liabilityLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Liability limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid liability limit (e.g., 1000 or 1000.00).</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentLimit" class="form-label">Payment Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="paymentLimit" name="paymentLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Payment limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid payment limit (e.g., 1000 or 1000.00).</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentAuthLimit" class="form-label">Payment Auth Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="paymentAuthLimit" name="paymentAuthLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Payment auth limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid payment auth limit (e.g., 1000 or 1000.00).</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="reserveLimit" class="form-label">Reserve Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="reserveLimit" name="reserveLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Reserve limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid reserve limit (e.g., 1000 or 1000.00).</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="reportingTo" class="form-label">Reporting To<span class="text-danger">*</span></label>
                                        <input type="text" id="reportingTo" name="reportingTo"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^[a-zA-Z\s]{2,50}$"
                                               title="Reporting To must be 2–50 alphabetic characters.">
                                        <div class="invalid-feedback">Please enter a valid reporting person name.</div>
                                    </div>
                                </div>
                            </div>

                            <div id="assessorFields" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        Assessor Details
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="assessorCode" class="form-label">Assessor Code<span class="text-danger">*</span></label>
                                            <select name="assessorCode" id="assessorCode" class="form-control form-control-sm" required autocomplete="off">
                                                <option value="">Select Assessor Code</option>
                                                <option value="AC001">AC001</option>
                                                <option value="AC002">AC002</option>
                                            </select>
                                            <div class="invalid-feedback">Please select an assessor code.</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="assessorType" class="form-label">Assessor Type<span class="text-danger">*</span></label>
                                            <select name="assessorType" id="assessorType" class="form-control form-control-sm" required autocomplete="off">
                                                <option value="">Select Type</option>
                                                <option value="internal">Internal</option>
                                                <option value="external">External</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="assignDistrict" class="form-label">Assign District<span class="text-danger">*</span></label>
                                            <select name="assignDistrict" id="assignDistrict" class="form-control form-control-sm" required autocomplete="off">
                                                <option value="">Select District</option>
                                                <c:forEach var="district" items="${districtListMain}">
                                                    <option value="${district.districtCode}">${district.districtName}</option>
                                                </c:forEach>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="reportingToRte" class="form-label">Reporting To<span class="text-danger">*</span></label>
                                            <select name="reportingToRte" id="reportingToRte" class="form-control form-control-sm" required autocomplete="off">
                                                <option value="">Select Reporting Engineer</option>
                                                <c:forEach var="rte" items="${rteListMain}">
                                                    <option value="${rte.userCode}">${rte.userId}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="firstName" class="form-label">First Name<span class="text-danger">*</span></label>
                                <input type="text" id="firstName" name="firstName" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="lastName" class="form-label">Last Name<span class="text-danger">*</span></label>
                                <input type="text" id="lastName" name="lastName" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="empCode" class="form-label">Emp Code<span class="text-danger">*</span></label>
                                <input type="text" id="empCode" name="empCode" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email<span class="text-danger">*</span></label>
                                <input type="email" id="email" name="email" class="form-control" required>
                                <div class="invalid-feedback">Please enter a valid email address.</div>
                            </div>

                            <div class="mb-3">
                                <label for="address1" class="form-label">Address Line 1<span class="text-danger">*</span></label>
                                <input type="text" id="address1" name="address1" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="address2" class="form-label">Address Line 2</label>
                                <input type="text" id="address2" name="address2" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label for="landPhone" class="form-label">Land Phone</label>
                                <input type="tel" id="landPhone" name="landPhone" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label for="mobilePhone" class="form-label">Mobile Phone<span class="text-danger">*</span></label>
                                <input type="tel" id="mobilePhone" name="mobilePhone" class="form-control"
                                       pattern="^(?:\+94|0)?7[0,1,2,4-8]\d{7}$"
                                       title="Enter a valid Sri Lankan mobile number (e.g., 0771234567 or +94771234567)">
                                <div class="invalid-feedback">Please enter a valid Sri Lankan mobile number.</div>
                            </div>

                            <div class="mb-3">
                                <label for="nic" class="form-label">NIC/Passport<span class="text-danger">*</span></label>
                                <input type="text" id="nic" name="nic" class="form-control"
                                       pattern="^([0-9]{9}[vVxX]|[0-9]{12}|[A-Za-z0-9]{6,10})$"
                                       title="Enter a valid NIC (e.g., 123456789V or 200012345678) or Passport number.">
                                <div class="invalid-feedback">Please enter a valid NIC or Passport number.</div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" id="saveUserBtn" class="btn btn-primary">Save Changes</button>
                <button type="submit" class="btn btn-danger">Delete</button>
                <button class="btn btn-secondary" data-toggle="modal" data-target="#userModal" onclick="resetUserForm()">Close</button>
            </div>
        </form>
    </div>
</div>



</body>
<script>
    hideLoader()
</script>
<script>
        document.addEventListener('DOMContentLoaded', function () {
        const departmentSelect = document.getElementById('department');
        const reserveLimitOnly = document.getElementById('reserveLimitOnly');
        const teamFields = document.getElementById('teamFields');
        const assessorFields = document.getElementById('assessorFields');

        const reserveOnlyRoles = [
        '22',
        '23',
        '24'
        ];

        const teamCardRoles = [
        '41',
        '42',
        '43',
        '44',
        '62',
        '63'
        ];

        const assessorRoles = [
        '20'
        ];

        departmentSelect.addEventListener('change', function () {
        const selectedRole = this.value.trim();

        // Reset all conditional sections
        reserveLimitOnly.style.display = 'none';
        teamFields.style.display = 'none';
        assessorFields.style.display = 'none';

        // Show based on department value
        if (reserveOnlyRoles.includes(selectedRole)) {
        reserveLimitOnly.style.display = 'block';
    }

        if (teamCardRoles.includes(selectedRole)) {
        teamFields.style.display = 'block';
    }

        if (assessorRoles.includes(selectedRole)) {
        assessorFields.style.display = 'block';
    }
    });
    });

</script>

<script>

    let table;
    function setData(obj) {
        console.log("data loading called");
        console.log(obj);

        document.getElementById('userId').setAttribute('disabled', 'disabled');
        document.getElementById('empCode').setAttribute('disabled', 'disabled');
        // Basic fields
        $('#userId').val(obj.userId);
        $('#company').val(obj.companyCode);
        $('#department').val(obj.accessUserType);

        let rawValue = obj.userTypes;
        rawValue = rawValue.replace(/,+$/, '');  // "1"
        let selectedRoles = rawValue ? rawValue.split(',') : [];  // ["1"]
        $('#userRole').val(selectedRoles).trigger('change');

        $('#confirmPassword').val(obj.realPassword);
        $('#userPassword').val(obj.realPassword);
        $('#userStatus').val(obj.userStatus);
        $('#title').val(obj.title);
        $('#firstName').val(obj.firstName);
        $('#lastName').val(obj.lastName);
        $('#empCode').val(obj.employeeNumber);
        $('#email').val(obj.email);
        $('#address1').val(obj.address2);
        $('#address2').val(obj.address2);
        $('#landPhone').val(obj.landPhone);
        $('#mobilePhone').val(obj.mobilePhone);
        $('#nic').val(obj.nic);

       // Get Assessor Detail
        $('#assessorCode').val(obj.assessorCode);



        // Optional / Visible fields
        if ($('#reserveLimitOnly').is(':visible')) {
            $('#reserveLimitOnlyInput').val(obj.reserveLimitLevel || 0);
        }

        if ($('#teamId').is(':visible')) {
            $('#teamId').val(obj.teamId);
        }

        if ($('#liabilityLimit').is(':visible')) {
            $('#liabilityLimit').val(obj.liabilityLimit);
        }

        if ($('#paymentLimit').is(':visible')) {
            $('#paymentLimit').val(obj.paymentLimit);
        }

        if ($('#paymentAuthLimit').is(':visible')) {
            $('#paymentAuthLimit').val(obj.paymentAuthLimit);
        }

        if ($('#reserveLimit').is(':visible')) {
            $('#reserveLimit').val(obj.reserveLimit);
        }

        if ($('#reportingTo').is(':visible')) {
            $('#reportingTo').val(obj.reportingTo);
        }

        if ($('#assessorCode').is(':visible')) {
            $('#assessorCode').val(obj.assessorName);
        }

        if ($('#assessorType').is(':visible')) {
            $('#assessorType').val(obj.assessorType);
        }

        if ($('#assignDistrict').is(':visible')) {
            $('#assignDistrict').val(obj.districtCode);
        }

        if ($('#assessorFields #reportingTo').is(':visible')) {
            $('#assessorFields #reportingTo').val(obj.reportingTo);
        }
    }

    function resetUserForm() {
        document.getElementById('userId').disabled = false;
        document.getElementById('empCode').disabled = false;
        $('#userId').val('');
        $('#company').val('');
        $('#department').val('');
        $('#userRole').val([]).trigger('change');
        $('#confirmPassword').val('');
        $('#userPassword').val('');
        $('#userStatus').val('');
        $('#title').val('');
        $('#firstName').val('');
        $('#lastName').val('');
        $('#empCode').val('');
        $('#email').val('');
        $('#address1').val('');
        $('#address2').val('');
        $('#landPhone').val('');
        $('#mobilePhone').val('');
        $('#nic').val('');
        $('#assessorCode').val('');

        if ($('#reserveLimitOnly').is(':visible')) {
            $('#reserveLimitOnlyInput').val(0);
        }

        if ($('#teamId').is(':visible')) {
            $('#teamId').val('');
        }

        if ($('#liabilityLimit').is(':visible')) {
            $('#liabilityLimit').val('');
        }

        if ($('#paymentLimit').is(':visible')) {
            $('#paymentLimit').val('');
        }

        if ($('#paymentAuthLimit').is(':visible')) {
            $('#paymentAuthLimit').val('');
        }

        if ($('#reserveLimit').is(':visible')) {
            $('#reserveLimit').val('');
        }

        if ($('#reportingTo').is(':visible')) {
            $('#reportingTo').val('');
        }

        if ($('#assessorCode').is(':visible')) {
            $('#assessorCode').val('');
        }

        if ($('#assessorType').is(':visible')) {
            $('#assessorType').val('');
        }

        if ($('#assignDistrict').is(':visible')) {
            $('#assignDistrict').val('');
        }

        if ($('#assessorFields #reportingTo').is(':visible')) {
            $('#assessorFields #reportingTo').val('');
        }
    }


    $('#saveUserBtn').on('click', function () {


        const form = document.getElementById('userForm'); // your <form> must have id="userForm"

        // Remove `required` from hidden fields
        $('#userForm').find('input, select, textarea').each(function () {
            if (!$(this).is(':visible')) {
                $(this).prop('required', false);
            }
        });

        // Native validation check
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }


        // Collect only visible input values
        const formData = {
            company: $('#company').val(),
            loginName: $('#userId').val(),
            department: $('#department').val(),
            userRole: $('#userRole').val(),
            password: $('#userPassword').val(),
            confirmPassword: $('#confirmPassword').val(),
            userStatus: $('#userStatus').val(),
            title: $('#title').val(),

            // Optional/visible fields
            reserveLimitOnly: $('#reserveLimitOnly').is(':visible') ? $('#reserveLimitOnlyInput').val() : null,

            teamId: $('#teamId').is(':visible') ? $('#teamId').val() : null,
            liabilityLimit: $('#liabilityLimit').is(':visible') ? $('#liabilityLimit').val() : null,
            paymentLimit: $('#paymentLimit').is(':visible') ? $('#paymentLimit').val() : null,
            paymentAuthLimit: $('#paymentAuthLimit').is(':visible') ? $('#paymentAuthLimit').val() : null,
            reserveLimit: $('#reserveLimit').is(':visible') ? $('#reserveLimit').val() : null,
            reportingTo: $('#reportingTo').is(':visible') ? $('#reportingTo').val() : null,

            assessorCode: $('#assessorCode').is(':visible') ? $('#assessorCode').val() : null,
            assessorType: $('#assessorType').is(':visible') ? $('#assessorType').val() : null,
            assignDistrict: $('#assignDistrict').is(':visible') ? $('#assignDistrict').val() : null,
            assessorReportingTo: $('#assessorFields #reportingTo').is(':visible') ? $('#assessorFields #reportingTo').val() : null
        };

        $.ajax({
            url: '<%= request.getContextPath() %>/ClaimUserLeaveController/save',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function (response) {
                alert('Saved successfully.');
                // Close modal or do any other action
                $('#userModal').modal('hide');
            },
            error: function (xhr, status, error) {
                alert('Save failed. Please try again.');
                console.error('Error:', error);
            }
        });
    });

    $(document).ready(function () {
        $.fn.DataTable.ext.pager.numbers_length = 5;

        table = $('#user-privileges-table').DataTable({
            "lengthMenu": [25, 50, 100, 200, 400],
            "processing": true,
            "searching": false,
            "serverSide": true,
            "ajax": {
                "url": "<%= request.getContextPath() %>/ClaimUserLeaveController/getAll",
                "type": "GET",
                "data": function (d) {
                    console.log(d)
                    d.userId = $("#txtUserId").val();
                    d.userStatus = $("#txtUserStatus").val();
                    d.userRole = $("#txtUserRole").val();
                    d.userMobile = $("#txtUserMobile").val();
                    d.userFirstName = $("#txtUserFirstName").val();
                }
            },
            "error": function (xhr, error, thrown) {
                notify("Failed to load data from server. Please try again later." ,"danger");
            },
            "columns": [
                {"data": "userCode"},
                {"data": "userId"},
                {"data": "userTypeDescription"},
                {"data": "firstName"},
                {"data": "lastName"},
                {"data": "mobile"},
                {
                    "data": "userStatus",
                    "render": function (data) {
                        if(data == "A"){
                            return `<span class="badge-success">ACTIVE</span>`;
                        }else if(data == "D"){
                            return `<span class="badge-dark">DISABLED</span>`;
                        }else if(data == "L"){
                            return `<span class="badge-info">LOCKED</span>`;
                        }else if(data == "C"){
                            return `<span class="badge-danger">DELETED</span>`;
                        }else{
                            return `<span class="badge-warning">N/A</span>`;
                        }

                    }
                },
                {
                    "render": function (data, type, obj, meta) {
                        if (data !== "C") {
                            return '<button class="btn btn-primary"  data-toggle="modal" data-target="#userModal" type="button" onclick=\'setData(' + JSON.stringify(obj) + ')\'>'
                                + '<i class="fa fa-edit"></i></button>';
                        } else {
                            return '<button class="btn btn-primary">'
                                + '<i class="fa fa-edit"></i></button>';
                        }
                    },
                    "orderable": false
                },
            ]
        });
    });

    function search() {
        table.ajax.reload();
    }
    hideLoader();
</script>

</html>
