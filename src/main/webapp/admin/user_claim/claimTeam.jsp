<%--
  Created by IntelliJ IDEA.
  User: Thanura
  Date: 12/6/2019
  Time: 4:28 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script>
        function init() {
            hideLoader();
            $("#teamId").val(${channelTeam.teamId});
            loadUserDiv();
            loadChannelDiv();
        }
    </script>
</head>
<body class="scroll" onload="init()">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input type="hidden" id="teamId" name="teamId"/>
        <input type="hidden" id="selectedIds" name="selectedIds"/>
        <input type="hidden" id="successCode" name="successCode"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Handler Team</h5>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body table-bg">
                <div class="row">
                    <div class="col-md-12">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <h6 style="float: left">${channelTeam.teamName}</h6>
                                            <div class="but_container" style="float: right">
                                                <button class="btn btn-info" type="button" id="btnUpdateTeam"
                                                        onclick="updateTeam()">Update Team
                                                </button>
                                                <button class="btn btn-primary" type="button" id="btnAdd"
                                                        onclick="addChannels()">Add Channels
                                                </button>
                                                <button class="btn btn-danger" type="button" id="btnBack"
                                                        onclick="back()">Back
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="mt-2 ml-5" id="channelDiv" style="width: 45%">
                                        </div>
                                        <div class="mt-2 ml-5" id="userDiv" style="width: 45%">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade bd-example-modal-md" tabindex="-1" role="dialog"
             id="teamUpdateModal" aria-hidden="true" style="    background: #333333c2;">
            <div class="modal-dialog modal-md modal-dialog-centered">
                <div class="modal-content p-2" style="overflow: hidden">
                    <div class="modal-header  p-2">
                        <h5 class="modal-title"
                            id="modalLabel">Update Team Details</h5><br>
                    </div>
                    <div class=" mt-4">
                        <div class="col-sm-8 offset-2">
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Team Name
                                </label>
                                <div class="col-sm-8">
                                    <input class="form-control-sm" id="txtTeamNameUpdate" name="teamNameUpdate"
                                           value="${channelTeam.teamName}" onclick="validateUpdateBtn()"
                                           onkeyup="validateUpdateBtn()"/>
                                </div>
                                <div class="text-danger" id="errorDiv" style="display: none">
                                    Team Name Already Exists
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Channel Description
                                </label>
                                <div class="col-sm-8">
                                    <input class="form-control-sm" id="txtChannelDescUpdate" name="channelDescUpdate"
                                           value="${channelTeam.channelDesc}" onclick="validateUpdateBtn()"
                                           onkeyup="validateUpdateBtn()"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer p-1">
                        <button type="button" class="btn btn-danger" data-dismiss="modal" id="btnClose"
                                onclick="closeModal()">
                            Close
                        </button>
                        <button type="button" class="btn btn-secondary" id="btnTeamUpdate"
                                onclick="updateTeamDetails()" disabled>
                            Update
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<script>

    var channelAdd = false;

    function reloadPage() {
        document.getElementById('frmForm').action = contextPath + "/UserManagementController/viewTeam";
        document.getElementById('frmForm').submit();
    }

    function loadUserDiv() {
        $("#userDiv").load(contextPath + "/UserManagementController/userList?teamId=" + $("#teamId").val());
    }

    function loadChannelDiv() {
        $("#channelDiv").load(contextPath + "/UserManagementController/channelList?teamId=" + $("#teamId").val());
    }

    function loadAddChannelDiv() {
        $("#userDiv").load(contextPath + "/UserManagementController/channelView");
    }

    function addChannels() {
        if (!channelAdd) {
            loadAddChannelDiv();
            $("#btnAdd").prop("disabled", true);
            channelAdd = true;
        } else {
            var elements = document.getElementsByName('checkBox');

            var selectedIds = "";
            for (var i = 0; i < elements.length; i++) {
                var id = elements[i].getAttribute('id');

                if (document.getElementById(id).checked) {
                    selectedIds += id + ",";
                }
            }
            $("#selectedIds").val(selectedIds);

            if ($("#selectedIds").val() == "") {
                notify('Please Select Channels to Add', "danger");
                return;
            }
            bootbox.confirm({
                message: "Do you want to add the selected Channels for Team : " + $("#teamId").val() + " ?",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    }

                },
                callback: function (result) {
                    if (result == true) {
                        var formData = $("#frmForm").serialize();
                        $.ajax({
                            url: contextPath + "/UserManagementController/addChannelsForTeam",
                            method: "POST",
                            data: formData,
                            success: function (respo) {
                                var resp = JSON.parse(respo);
                                if (resp == "SUCCESS") {
                                    notify("Channels Added Successfully", "success");
                                    loadChannelDiv();
                                    loadAddChannelDiv();
                                } else {
                                    notify("Failed to add Channels", "danger");
                                }
                            }
                        })
                    }
                }
            });
        }
    }

    function updateTeam() {
        $("#teamUpdateModal").modal('show');
    }

    function viewTeam(teamId) {
        $("#teamId").val(teamId);
        document.getElementById('frmForm').action = contextPath + "/UserManagementController/viewTeam";
        document.getElementById('frmForm').submit();
    }

    function updateTeamDetails() {
        var teamName = $("#txtTeamNameUpdate").val();
        var channelDesc = $("txtChannelDescUpdate").val();
        if (teamName == '' && channelDesc == '') {
            notify("Please Provide Required Data", "danger");
        } else {
            var formData = $("#frmForm").serialize();
            $.ajax({
                url: contextPath + "/UserManagementController/updateTeam",
                method: "POST",
                data: formData,
                success: function (respo) {
                    var resp = JSON.parse(respo);
                    if (resp == "SUCCESS") {
                        $("#successCode").val(resp);
                        viewTeam($("#teamId").val());
                    } else if (resp == "DUPLICATE") {
                        $("#errorDiv").show();
                    } else {
                        notify("Failed to Update Team", "danger");
                    }
                }
            })
        }
    }

    function validateUpdateBtn() {
        $("#errorDiv").hide();
        var teamName = $("#txtTeamNameUpdate").val();
        var channelDesc = $("#txtChannelDescUpdate").val();
        if (teamName == '' || channelDesc == '') {
            $('#btnTeamUpdate').prop('disabled', true);
        } else {
            $('#btnTeamUpdate').prop('disabled', false);
        }
    }

    function closeModal() {
        $("#teamUpdateModal").modal('hide');
    }

    function back() {
        if (channelAdd) {
            $("#btnAdd").prop("disabled", false);
            $("#userDiv").load(contextPath + "/UserManagementController/userList?teamId=" + $("#teamId").val());
            channelAdd = false;
        } else {
            document.getElementById('frmForm').action = contextPath + "/UserManagementController/viewTeams?TYPE=1";
            document.getElementById('frmForm').submit();
        }
    }
</script>
</body>
</html>
