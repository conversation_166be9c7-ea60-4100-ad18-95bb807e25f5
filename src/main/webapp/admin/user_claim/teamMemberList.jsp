<%--
  Created by IntelliJ IDEA.
  User: Thanura
  Date: 12/8/2019
  Time: 7:27 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div>
    <h6>Members</h6>
    <table id="user-table" class="table table-sm table-hover" cellspacing="0"
           style="cursor:pointer">
        <thead>
        <tr>
            <th width="40px">User Code</th>
            <th>User Id</th>
            <th>Firstname</th>
            <th>Lastname</th>
            <th class="min-mobile"></th>
        </tr>
        <c:if test="${channelUserList == null}">
            <tr><i class="badge-warning">No Users Found</i></tr>
        </c:if>
        </thead>
        <tbody>
        <c:forEach var="listDto" items="${channelUserList}">
            <tr>
                <td>${listDto.userCode}</td>
                <td>${listDto.userId}</td>
                <td>${listDto.firstName}</td>
                <td>${listDto.lastName}</td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
</div>