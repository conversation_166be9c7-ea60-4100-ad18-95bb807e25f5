<%--
    Document   : viewUserParameterList
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 27, 2010, 8:23:53 PM
    Author     : <PERSON><PERSON>
--%>
<%@page import="com.misyn.mcms.admin.UserParameters" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserParametersManagerBean" class="com.misyn.mcms.admin.UserParametersManager" scope="application"/>
<%
    //UserParameters userParameters = null;
    List<UserParameters> list = null;


    int n_comid = -1;

    String v_groupids = "0";
    try {
        n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
    } catch (Exception e) {
    }
    int n_usrcode = -1;
    try {
        n_usrcode = Integer.parseInt(request.getParameter("P_N_USRCODE"));
    } catch (Exception e) {
    }

    try {
        v_groupids = request.getParameter("P_V_GROUPIDS");
    } catch (Exception e) {
    }

    boolean isNewRec = true;
    try {
        isNewRec = (Boolean) session.getAttribute("IS_NEW_RECORD");
    } catch (Exception e) {
    }
    if (isNewRec) {

        list = UserParametersManagerBean.getUserParameterList_New(n_comid, v_groupids);

    } else {

        list = UserParametersManagerBean.getUserParameterList_Modify(n_usrcode, n_comid, v_groupids);

    }


    int index = 0;
    for (UserParameters userParameters : list) {


%>


<div class="row">
    <span class="label"><%=userParameters.getV_lblDesc()%> :</span>
    <span class="txt_cont">
          <input name="txtParaValue<%=index%>" id="txtParaValue<%=index%>" type="text"
                 value="<%=userParameters.getV_txtValue()%>"/>
          <input name="txtParaCode<%=index%>" id="txtParaCode<%=index%>" type="hidden"
                 value="<%=userParameters.getV_paracode()%>"/>
	</span>
</div>

<% index++;
}%>
<input name="txtParaCount" id="txtParaCount" type="hidden" value="<%=list.size()%>"/>
