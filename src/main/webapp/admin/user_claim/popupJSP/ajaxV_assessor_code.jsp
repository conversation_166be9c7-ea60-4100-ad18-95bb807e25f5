<jsp:useBean id="AJaxXMLWriterBean" class="com.misyn.mcms.dbconfig.AJaxXMLWriter" scope="request"/>

<%@page import="com.misyn.mcms.utility.DataValidate" %>
<%@page import="java.io.PrintWriter" %>

<%
    response.setHeader("Cache-Control", "no-cache, must-revalidate");
    response.setHeader("Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
    session = request.getSession();

    PrintWriter pr = null;
    String searchKey1 = "";
    String searchKey2 = "";

    try {
        searchKey1 = request.getParameter("searchKey1");
        searchKey2 = request.getParameter("searchKey2");
        if (searchKey1 != null) {
            searchKey1 = searchKey1.trim();
        }
        if (searchKey2 != null) {
            searchKey2 = searchKey2.trim();
        }
    } catch (Exception e) {
    }
    try {
        out.clear();
        response.setContentType("text/xml");
        pr = new PrintWriter(response.getOutputStream()); //description like 'user Login%'


        if (DataValidate.isNumeric(searchKey1)) {
            AJaxXMLWriterBean.writeXMLAll_2Key("claim_assessor", "V_CODE", "V_NAME", "V_CODE like '" + searchKey1 + "%'", 100, true);
        } else {
            AJaxXMLWriterBean.writeXMLAll_2Key("claim_assessor", "V_CODE", "V_NAME", "V_NAME like '%" + searchKey1 + "%'", 100, true);
        }


        pr.write(AJaxXMLWriterBean.getXmlReply());
    } catch (Exception e) {
    } finally {
        pr.flush();
        pr.close();

    }
%>

