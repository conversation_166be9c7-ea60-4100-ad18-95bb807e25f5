<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%--
  Created by IntelliJ IDEA.
  User: Thanura
  Date: 12/8/2019
  Time: 7:28 PM
  To change this template use File | Settings | File Templates.
--%>
<html xmlns="http://www.w3.org/1999/xhtml">
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<div>
    <h6>Add Channels</h6>
    <table id="add-channel-table" class="table table-sm table-hover" cellspacing="0"
           style="cursor:pointer">
        <thead>
        <tr>
            <th width="40px">Ref No</th>
            <th>Channel Id</th>
            <th>Channel Code</th>
        </tr>
        <c:if test="${newChannelList == null}">
            <tr><i class="badge-warning">No Channels Found</i></tr>
        </c:if>
        </thead>
        <tbody>
        <c:forEach var="newChannels" items="${newChannelList}">
            <tr>
                <td><input type='checkbox' class='checkBtn' id=${newChannels.refNo} name='checkBox'
                           onclick='enableSaveBtn()'/></td>
                <td>${newChannels.refNo}</td>
                <td>${newChannels.channelDesc}</td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
</div>
<script>
    function enableSaveBtn() {
        var elements = document.getElementsByName('checkBox');

        for (var i = 0; i < elements.length; i++) {
            var id = elements[i].getAttribute('id');

            if (document.getElementById(id).checked) {
                $("#btnAdd").prop("disabled", false);
                $("#btnSave").prop("disabled", false);
                return;
            } else {
                $("#btnAdd").prop("disabled", true);
                $("#btnSave").prop("disabled", true);
            }
        }
    }

</script>
</html>