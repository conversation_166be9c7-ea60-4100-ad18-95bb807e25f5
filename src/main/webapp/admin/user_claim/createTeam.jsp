<%--
  Created by IntelliJ IDEA.
  User: Thanura
  Date: 12/9/2019
  Time: 12:14 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script>
        function init() {
            hideLoader();
            $("#teamId").val(${channelTeam.teamId});
            loadChannelsDiv();
        }
    </script>
</head>
<body class="scroll" onload="init()">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input type="hidden" id="teamId" name="teamId"/>
        <input type="hidden" id="selectedIds" name="selectedIds"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Handler Team</h5>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body table-bg">
                <div class="row">
                    <div class="col-md-12">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <h6 style="float: left">Create Team</h6>
                                            <div class="but_container" style="float: right">
                                                <button class="btn btn-info" type="button" id="btnSave" disabled
                                                        onclick="saveTeam()">Save
                                                </button>
                                                <button class="btn btn-danger" type="button" id="btnBack"
                                                        onclick="back()">Back
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="mt-2 ml-5" id="teamDetailsDiv" style="width: 45%">
                                            <div>
                                                <h6>Team Description</h6>
                                                <div class="row mt-3">
                                                    <div class="col-sm-3" style="float: left">
                                                        <span class="label" style="float: right;">Team Name : </span>
                                                    </div>
                                                    <div class="col-sm-6 mb-2" style="float: right">
                                                        <input name="txtTeamName" id="txtTeamName" maxlength="20"
                                                               title="test" type="text"
                                                               class="form-control"
                                                               value=""
                                                               onchange="validateSaveBtn()"
                                                               onkeyup="validateSaveBtn()"/>
                                                        <div class="text-danger" id="errorDiv" style="display: none">
                                                            Team Name Already Exists
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-sm-3" style="float: left">
                                                        <span class="label"
                                                              style="float: right;">Channel Description : </span>
                                                    </div>
                                                    <div class="col-sm-6 mb-2" style="float: right">
                                                        <input name="txtChannelDesc" id="txtChannelDesc" maxlength="20"
                                                               title="test" type="text"
                                                               class="form-control"
                                                               value=""
                                                               onchange="validateSaveBtn()"
                                                               onkeyup="validateSaveBtn()"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-2 ml-5" id="ChannelsDiv" style="width: 45%">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script>

    function validateSaveBtn() {
        $("#errorDiv").hide();
        if ($("#txtTeamName").val() != '' && $("#txtChannelDesc").val() != '') {
            $("#btnSave").prop("disabled", false);
        } else {
            $("#btnSave").prop("disabled", true);
        }
    }

    function back() {
        document.getElementById('frmForm').action = contextPath + "/UserManagementController/viewTeams?TYPE=1";
        document.getElementById('frmForm').submit();
    }

    function saveTeam() {
        var selectedIds = "";
        var elements = document.getElementsByName('checkBox');

        for (var i = 0; i < elements.length; i++) {
            var id = elements[i].getAttribute('id');

            if (document.getElementById(id).checked) {
                var val = id;
                selectedIds += val + ",";
            }
        }
        $('#selectedIds').val(selectedIds);
        if ($("#txtTeamName").val() != '' && $("#txtChannelDesc").val() != '') {
            save();
        } else {
            notify('Please Submit All Required Data', "danger");
        }
    }

    function save() {
        var teamName = $("#txtTeamName").val();
        var channelDesc = $("#txtChannelDesc").val();
        var ids = $('#selectedIds').val();
        $.ajax({
            url: contextPath + "/UserManagementController/saveNewTeam",
            type: 'POST',
            data: {
                "selectedIds": ids,
                teamName,
                channelDesc
            },
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "New Team Saved Successfully!";
                    notify(message, "success");
                    loadChannelsDiv();
                    $("#txtTeamName").val('');
                    $("#txtChannelDesc").val('')
                } else if (messageType == "DUPLICATE") {
                    $("#errorDiv").show();
                    $("#txtTeamName").focus();
                } else {
                    message = "Failed to Save new Team";
                    notify(message, "danger");
                }
            }
        });
    }

    function loadChannelsDiv() {
        $("#ChannelsDiv").load(contextPath + "/UserManagementController/channelView");
    }
</script>
</body>
</html>
