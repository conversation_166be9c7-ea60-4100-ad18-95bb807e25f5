<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>
<%

    String ADD_RIGHT = "disabled='disabled'";
    String DELETE_RIGHT = "disabled='disabled'";
    if (((String) session.getAttribute("RIGHT_D")).equals("checked")) DELETE_RIGHT = "";

    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    String readOnlyClass = "";


    boolean isSearchPage = false;

    try {
        isSearchPage = Boolean.parseBoolean(request.getParameter("P_ISSEARCH_PAGE"));
    } catch (Exception e) {
        isSearchPage = false;
    }

    session.removeAttribute("ISSEARCH_PAGE");
    session.setAttribute("ISSEARCH_PAGE", isSearchPage);

    long timeURL = System.currentTimeMillis();
    String URL = "userAllResult.jsp?" + timeURL;

    int n_accessusrtype = 7;

    int n_comid = -1;

    try {
        n_comid = user.getN_comid();//Integer.parseInt(request.getParameter("P_N_COMID"));
    } catch (Exception e) {
    }

    int n_usrcode = -1;
    String str_n_comid_popList = "";
    String str_n_accessusrtype_popList = "";


    int size = 0;
    List<User> list = null;
    User find_user = null;
    String searchKey = "";

    boolean isNewRecord = true;
    try {
        isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
    } catch (Exception e) {
    }

    try {
        n_usrcode = Integer.parseInt(request.getParameter("P_N_USRCODE"));
    } catch (Exception e) {
    }

    if (isNewRecord) {
        if (((String) session.getAttribute("RIGHT_I")).equals("checked")) ADD_RIGHT = "";
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        find_user = new User();
        if (user.getN_accessusrtype() == 1) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "", "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "", "");


        } else if (user.getN_accessusrtype() == 2) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "", "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype NOT IN(1,2)", "");


        } else if (user.getN_accessusrtype() == 3) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(6)", "");


        } else if (user.getN_accessusrtype() == 4) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(7)", "");


        } else if (user.getN_accessusrtype() == 5) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(0)", "");


        } else {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=-1", "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(-1)", "");

        }

    } else {
        if (((String) session.getAttribute("RIGHT_M")).equals("checked")) ADD_RIGHT = "";
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        readOnlyClass = " readonly class=\"textReadOnly\" ";
        if (user.getN_accessusrtype() == 1) {
            searchKey = " AND n_usrcode=" + n_usrcode;
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "", "");


        } else if (user.getN_accessusrtype() == 2) {
            searchKey = "AND n_usrcode=" + n_usrcode + " AND n_accessusrtype NOT IN(1,2)";
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype NOT IN(1,2)", "");


        } else if (user.getN_accessusrtype() == 3) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(6) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);


            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(6)", "");

        } else if (user.getN_accessusrtype() == 4) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(7) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(7)", "");


        } else if (user.getN_accessusrtype() == 5) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(8) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(0)", "");

        }

        n_comid = find_user.getN_comid();
    }

            /*  if (list != null) {
            if (list.size() != 0) {
            find_user = list.get(0);
            }
            }*/


%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <script type="text/javascript" src="/script/common/ListBox.js"></script>
    <script type="text/javascript" src="jsAjaxMultPopup2Key.js"></script>
    <link href="/css/common/fb_form/fb_form_col.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/css/common/fb_form/custom-form-elements.js"></script>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript">
        /* $(function() {
             //
             //buttonImage: '/image/common/calendar.gif',
             var d1=document.frmForm.txtDOB.value;
             $("#txtDOB").datepicker({
                 showOn: 'button',
                 buttonImage: '/css/common/fb_form/dtpic.gif',
                 buttonImageOnly: true,
                 changeMonth: true,
                 changeYear: true,
                 yearRange: '1940:2099' ,
                 minDate: '-70y',
                 maxDate: '0d'

             });
             $("#txtDOB").datepicker('option', {dateFormat: "yy-mm-dd"});
             document.frmForm.txtDOB.value=d1;

         });*/


        var timeUrl = new Date().getDate();
        var ajaxMultiSelectPopup1 = new AjaxMultiSelectPopup('txtSearchV_usrtypes', 'hidV_usrtypes', 'lstV_usrtypes', "popupJSP/ajaxV_USRTYPES.jsp");
        var ajaxMultiSelectPopup2 = new AjaxMultiSelectPopup('txtSearchV_group_ids', 'hidV_group_ids', 'lstV_group_ids', "popupJSP/ajaxV_GROUP_IDs.jsp");


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //if($("select#txtN_comid").val()=="-1"){$("select#txtN_comid").focus();return;}
                            if ($("input#txtV_usrid").val() == "") {
                                $("input#txtV_usrid").focus();
                                return;
                            }
                            if ($("select#txtN_accessusrtype").val() == "-1") {
                                $("select#txtN_accessusrtype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            // if($("select#txtV_title").val()==""){$("select#txtV_title").focus();return;}
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            if ($("input#txtV_emp_no").val() == "") {
                                $("input#txtV_emp_no").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--
        function init() {
            CustomInit();
            var isNew =<%=isNewRecord%>;
            if (isNew) {
                ajaxMultiSelectPopup2.ViewParameterList('viewUserParameterList.jsp', 0);
            }
            else {
                ajaxMultiSelectPopup2.ViewParameterList('viewUserParameterList.jsp', "<%= find_user.getV_group_ids()%>");
            }

        }

        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN_comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew) document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        function pageSubmit(type) {

            if (type == 'Save') {
                if (document.getElementById("txtN_comid").value == "-1") {
                    showDialogbox("Please Select Company Code");
                    return;
                }
                else if (Trim(document.getElementById("txtV_usrid").value) == "") {
                    showDialogbox("Please Enter User Id");
                    return;
                }

                else if (document.getElementById("txtN_accessusrtype").value == "-1") {
                    showDialogbox("Please Select Access User Level");
                    return;
                }

                else if (Trim(document.getElementById("txtV_usrtypes").value) == "") {
                    showDialogbox("Please Select User Role");
                    return;
                }

                else if (Trim(document.getElementById("txtV_password").value) == "") {
                    showDialogbox("Please Enter Password");
                    return;
                }
                else if (Trim(document.getElementById("txtV_confirm_password").value) == "") {
                    showDialogbox("Please Enter Confirm Password");
                    return;
                }
                else if (Trim(document.getElementById("txtV_confirm_password").value) != Trim(document.getElementById("txtV_password").value)) {
                    document.getElementById("txtV_confirm_password").value = "";
                    document.getElementById("txtV_password").value = "";
                    showDialogbox("The Password You Typed Do Not Match.Please Retype the New Password in Both Boxes.");
                    return;
                }

                /*  else if(document.getElementById("txtV_title").value=="")
                  {
                      showDialogbox("Please Select Title");
                      return;
                  }*/

                else if (document.getElementById("txtV_firstname").value == "") {
                    showDialogbox("Please Enter First Name");
                    return;
                }
                else if (document.getElementById("txtV_lastname").value == "") {
                    showDialogbox("Please Enter Last Name");
                    return;
                }
                else if (document.getElementById("txtV_email").value == "") {
                    showDialogbox("Please Enter Email Address");
                    return;
                }
                else if (document.getElementById("txtV_emp_no").value == "") {
                    showDialogbox("Please Enter Employee No");
                    return;
                }


                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }
            else if (type == 'Delete') {
                document.frmForm.action = "userDelete.jsp?<%=timeURL%>&P_TYPE=1";
                document.frmForm.submit();

            }
            else if (type == 'Close') {
                var isSearchPage =<%=isSearchPage%>;
                if (isSearchPage == true) window.location = "searchUser.jsp?<%=timeURL%>";
                else window.location = "userAllList.jsp?<%=timeURL%>";

            }

        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

    </script>

    <style>
        .fbSelectBox {
            border: 1px solid #0C0;
            padding: 3px;
        }

        .selectbox {
            margin: 0px 5px 10px 0px;
            padding-left: 2px;
            font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
            font-size: 1em; /* Resize Font*/
            width: 190px; /* Resize Width */
            display: block;
            text-align: left;
            background: url('bg_select.png') right;
            cursor: pointer;
            border: 1px solid #D1E4F6;
            color: #333;
        }
    </style>
</head>
<body onload="init();">
<div class="form_header">UAL User Details</div>

<div class="container">
    <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are mandatory.
    </div>
    <form name="frmForm" action="" method="post">
        <input name="txtN_usrcode" id="txtN_usrcode" value="<%=find_user.getN_usrcode()%>" type="hidden"/>
        <input name="txtN_comid" id="txtN_comid" value="<%=n_comid%>" type="hidden"/>
        <input name="txtN_accessusrtype" id="txtN_accessusrtype" value="<%=n_accessusrtype%>" type="hidden"/>

        <input name="txtV_address1" id="txtV_address1" type="hidden" value="<%=find_user.getV_address1()%>"/>
        <input name="txtV_address2" id="txtV_address2" type="hidden" value="<%=find_user.getV_address2()%>"/>
        <input name="txtV_land_phone" id="txtV_land_phone" type="hidden" value="<%=find_user.getV_land_phone()%>"/>
        <input name="txtV_nic" id="txtV_nic" type="hidden" value="<%=find_user.getV_nic()%>"/>


        <div class="col_half" style="width:55%;">
            <fieldset>
                <div class="row"><span class="label">User ID <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
          <input name="txtV_usrid" id="txtV_usrid" maxlength="20" title="User Id" type="text"
                 value="<%=find_user.getV_usrid()%>"/>
          </span></div>

                <div class="row"><span class="label">User Role <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
          <input class="dropdown" size="25" name="txtV_usrtypes" readonly="readonly"
                 value="<%= UserManagerBean.getTextValue(find_user.getV_usrtype_desc())%>" id="txtV_usrtypes"
                 type="text"/>
          <input type="button" class="drop_down_btn" name="cmdV_usrtypes" id="cmdV_usrtypes" value=""
                 onclick="ajaxMultiSelectPopup1.onClick_ToggleButton(event,true)"/>
          <div class="multiDivTag" name="lstV_usrtypes" id="lstV_usrtypes">
            <input class="listtxt" name="txtSearchV_usrtypes" id="txtSearchV_usrtypes" style="display:none;" type="text"
                   onkeyup="ajaxMultiSelectPopup1.showPopupListMenu(event,false)"/>
          </div>
          <input name="hidV_usrtypes" id="hidV_usrtypes" type="hidden" value="<%= find_user.getV_usrtypes()%>"/>
          </span></div>
                <div class="row"><span class="label">User Group :</span><span class="txt_cont">
          <input class="dropdown" size="25" name="txtV_group_ids" readonly="readonly"
                 value="<%= UserManagerBean.getTextValue(find_user.getV_group_ids_desc())%>" id="txtV_group_ids"
                 type="text"/>
          <input type="button" class="drop_down_btn" name="cmdV_group_ids" id="cmdV_group_ids" value=""
                 onclick="ajaxMultiSelectPopup2.onClick_ToggleButton(event,true)"/>
          <div class="multiDivTag" name="lstV_group_ids" id="lstV_group_ids">
            <input class="listtxt" name="txtSearchV_group_ids" id="txtSearchV_group_ids" style="display:none;"
                   type="text" onkeyup="ajaxMultiSelectPopup2.showPopupListMenu(event,false)"/>
          </div>
          <input name="hidV_group_ids" id="hidV_group_ids" type="hidden" value="<%= find_user.getV_group_ids()%>"/>
          </span></div>
                <div class="row"><span class="label">Password <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
          <input name="txtV_password" id="txtV_password" maxlength="15" type="password"
                 value="<%=find_user.getV_real_password()%>"/>
          </span></div>
                <div class="row"><span class="label">Confirm Password <span style="color:#D50000">*</span> :</span><span
                        class="txt_cont"> <span class="txt_cont" style="padding-left:0px;width:150px;">
          <input name="txtV_confirm_password" id="txtV_confirm_password" maxlength="15" title="test" type="password"
                 value="<%=find_user.getV_real_password()%>"/>
          </span></span></div>
                <div class="row"><span class="label">User Status<span style="color:#D50000">*</span> :</span><span
                        class="txt_cont">
          <%if (isNewRecord) {%>
          <select name="txtV_usrstatus" id="txtV_usrstatus" class="styled1">
            <option value="X">Active</option>
            <option value="D">Disabled</option>
            <option value="L">Locked</option>
            <option value="C">Delete</option>
          </select>
          <%
          } else {

              if (find_user.getV_usrstatus().equalsIgnoreCase("A")) {
          %>
              <select name="txtV_usrstatus" id="txtV_usrstatus" class="styled1">
                <option value="A">Active</option>
                <option value="D">Disabled</option>
                <option value="L">Locked</option>
                <option value="C">Delete</option>
              </select>
          <%
          } else {%>
                <select name="txtV_usrstatus" id="txtV_usrstatus" class="styled1">
            	<option value="X">Active</option>
            	<option value="D">Disabled</option>
            	<option value="L">Locked</option>
           	    <option value="C">Delete</option>
          		</select>
               <%
                       }
                   }%>
          </span></div>
            </fieldset>
            <fieldset>
                <div id="paraListView"> Parameter List</div>
            </fieldset>
            <div class="but_container">
                <input type="button" name="cmdSave" id="cmdSave" value="Save Changes" <%=ADD_RIGHT%>
                       onclick="pageSubmit('Save')" class="button"/>
                <input type="button" name="cmdDelete" id="cmdDelete" value="Delete" <%=DELETE_RIGHT%>
                       onclick="pageSubmit('Delete')" class="button"/>
                <input type="button" name="cmdClose" id="cmdClose" value="Close" onclick="pageSubmit('Close')"
                       class="button"/>
            </div>
        </div>
        <div class="col_half" style="width:42%;">
            <fieldset>
                <div class="row"><span class="label" style="text-align:right; width:80px;">Title <span
                        style="color:#D50000"></span> :</span> <span class="txt_cont" style="width:150px;">
          <select name="txtV_title" id="txtV_title" class="styled1">
            <option value="">Please select one</option>
            <option value="Rev">Rev</option>
            <option value="Dr">Dr</option>
            <option value="Mr">Mr</option>
            <option value="Mrs">Mrs</option>
            <option value="Ms">Ms</option>
            <option value="M/S">M/S</option>
            <option value="Other">Other</option>
          </select>
          </span></div>
                <div class="row"><span class="label" style="text-align:right; width:80px;">First Name <span
                        style="color:#D50000">*</span> :</span> <span class="txt_cont" style="width:200px;">
          <input name="txtV_firstname" id="txtV_firstname" maxlength="20" title="test" type="text"
                 value="<%=find_user.getV_firstname()%>"/>
          </span></div>
                <div class="row"><span class="label" style="text-align:right; width:80px;">Last Name <span
                        style="color:#D50000">*</span> :</span> <span class="txt_cont" style="width:200px;">
          <input name="txtV_lastname" id="txtV_lastname" maxlength="20" title="test" type="text"
                 value="<%=find_user.getV_lastname()%>"/>
          </span></div>

                <div class="row"><span class="label" style="text-align:right; width:80px;">Email <span
                        style="color:#D50000">*</span> :</span> <span class="txt_cont" style="width:200px;">
          <input name="txtV_email" id="txtV_email" type="text" value="<%=find_user.getV_email()%>"/>
          </span></div>
                <div class="row"><span class="label" style="text-align:right; width:80px;">Emp. No <span
                        style="color:#D50000">*</span> :</span> <span class="txt_cont" style="width:200px;">
          <input name="txtV_emp_no" id="txtV_emp_no" maxlength="20" title="test" type="text"
                 value="<%=find_user.getV_emp_no()%>"/>
          </span></div>
            </fieldset>

        </div>
        <% if (!isNewRecord) {

        %>
        <script type="text/javascript">
            document.getElementById("txtN_accessusrtype").value =<%=find_user.getN_accessusrtype()%>;
            document.getElementById("txtV_title").value = "<%=find_user.getV_title()%>";
            document.getElementById("txtV_usrstatus").value = "<%=find_user.getV_usrstatus()%>";

        </script>
        <%} else {%>
        <script type="text/javascript">
            document.getElementById("txtN_comid").value =<%=n_comid%>;
        </script>
        <%}%>
        <div id="dialog" style="display:none;" title="${CompanyTitle}">
            <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
            <p id="dialog-email" class="textGrey"></p>
        </div>
    </form>
    <div class="spacer"> &nbsp;</div>
</div>
</body>
</html>
