<%@include file="/common/ValidateUser.jsp" %>
<jsp:useBean id="AJaxXMLWriterBean" class="com.misyn.mcms.dbconfig.AJaxXMLWriter" scope="request"/>


<%@page import="com.misyn.mcms.utility.DataValidate" %>
<%@page import="java.io.PrintWriter" %>

<%
    response.setHeader("Cache-Control", "no-cache, must-revalidate");
    response.setHeader("Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
    session = request.getSession();

    String sqlStr = "";
    PrintWriter pr = null;
    String searchKey1 = "";
    String searchKey2 = "";

    try {
        searchKey1 = request.getParameter("searchKey1");
        searchKey2 = request.getParameter("searchKey2");
        if (searchKey1 != null) {
            searchKey1 = searchKey1.trim();
        }
        if (searchKey2 != null) {
            searchKey2 = searchKey2.trim();
        }
    } catch (Exception e) {
    }

    try {
        if (user.getN_accessusrtype() == 1) {
            sqlStr = " AND n_comid=" + searchKey2
                    + " AND "
                    + "n_accessusrtype NOT IN(-1) "
                    + "AND "
                    + "n_usrtype IN(SELECT DISTINCT(n_usrtype) FROM prev_mst)";
        } else if (user.getN_accessusrtype() == 2) {
            sqlStr = " AND n_comid=" + searchKey2
                    + " AND "
                    + "n_accessusrtype NOT IN(-1,1,2) "
                    + "AND "
                    + "n_usrtype IN(SELECT DISTINCT(n_usrtype) FROM prev_mst)";
        } else if (user.getN_accessusrtype() == 3) {
            sqlStr = " AND n_comid=" + searchKey2
                    + " AND "
                    + "n_accessusrtype IN(6) "
                    + "AND "
                    + "n_usrtype IN(SELECT DISTINCT(n_usrtype) FROM prev_mst)";
        } else if (user.getN_accessusrtype() == 4) {
            sqlStr = " AND n_comid=" + searchKey2
                    + " AND "
                    + "n_accessusrtype IN(7) "
                    + "AND "
                    + "n_usrtype IN(SELECT DISTINCT(n_usrtype) FROM prev_mst)";
        } else if (user.getN_accessusrtype() == 5) {
            sqlStr = " AND n_comid=" + searchKey2
                    + " AND "
                    + "n_accessusrtype IN(8) "
                    + "AND "
                    + "n_usrtype IN(SELECT DISTINCT(n_usrtype) FROM prev_mst)";
        }

    } catch (Exception e) {
    }

    try {
        out.clear();
        response.setContentType("text/xml");
        pr = new PrintWriter(response.getOutputStream()); //description like 'user Login%'

        if (user.getN_accessusrtype() == 1 ||
                user.getN_accessusrtype() == 2 ||
                user.getN_accessusrtype() == 3 ||
                user.getN_accessusrtype() == 4 ||
                user.getN_accessusrtype() == 5) {
            if (DataValidate.isNumeric(searchKey1)) {
                AJaxXMLWriterBean.writeXMLAll_2Key("usrtype_mst", "n_usrtype", "v_name", "n_usrtype like '" + searchKey1 + "%'" + sqlStr, 100, true);
            } else {
                AJaxXMLWriterBean.writeXMLAll_2Key("usrtype_mst", "n_usrtype", "v_name", "v_name like '" + searchKey1 + "%'" + sqlStr, 100, true);
            }

        }

        pr.write(AJaxXMLWriterBean.getXmlReply());
    } catch (Exception e) {
    } finally {
        pr.flush();
        pr.close();

    }
%>

