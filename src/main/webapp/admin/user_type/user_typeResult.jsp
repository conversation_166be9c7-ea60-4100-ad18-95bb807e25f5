<%--
    Document   : applicationItemResult
    Created on : Dec 24, 2010, 6:46:15 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : <PERSON><PERSON> Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.UserType" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="UserTypeManagerBean" class="com.misyn.mcms.admin.UserTypeManager" scope="application"/>

<%
    long timeURL = System.currentTimeMillis();
    int n_prgid = 1;
    UserType userType = new UserType();


    try {
        userType.setN_comid(Integer.parseInt(request.getParameter("txtN_comid")));
    } catch (Exception e) {
    }
    try {
        userType.setN_usrtype(Integer.parseInt(request.getParameter("txtN_usrtype")));
    } catch (Exception e) {
    }

    try {
        userType.setV_name(request.getParameter("txtV_name"));
    } catch (Exception e) {
    }
    try {
        userType.setN_accessusrtype(Integer.parseInt(request.getParameter("txtN_accessusrtype")));
    } catch (Exception e) {
    }
    try {
        userType.setV_description(request.getParameter("txtV_description"));
    } catch (Exception e) {
    }


    try {
        int result = UserTypeManagerBean.saveUserType(userType);
        if (result == 0) {
            response.sendRedirect("user_typeList.jsp?" + timeURL + "&P_ERROR=Can not be save, " + userType.getErrorMessage());
        } else if (result > 0) {
            response.sendRedirect("user_typeList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
        }
    } catch (Exception e) {
    }

%>


