<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.UserType" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserTypeManagerBean" class="com.misyn.mcms.admin.UserTypeManager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%
    UserType m_UserType = null;
    List<UserType> userTypeList = new ArrayList<UserType>();
    String chkvalue = "";


    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) {
            chkvalue = "";
        }
        if (chkvalue.equalsIgnoreCase("checked")) {
            m_UserType = new UserType();

            try {
                m_UserType.setN_usrtype(Integer.parseInt(request.getParameter("P_N_USERTYPE" + x)));
            } catch (Exception e) {
            }

            userTypeList.add(m_UserType);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }

    //================Start Delete only one recode=================
    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("P_TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {
        m_UserType = new UserType();
        try {
            m_UserType.setN_usrtype(Integer.parseInt(request.getParameter("txtN_usrtype")));
        } catch (Exception e) {
        }

        userTypeList.add(m_UserType);
    }
    //================End Delete only one recode=================

    //out.print("Size "+rolePrivilegeList.size());
    if (userTypeList.size() > 0) {
        UserTypeManagerBean.deleteUserType(userTypeList);
    }
    response.sendRedirect("user_typeList.jsp?P_ERROR=" + UserTypeManagerBean.getMsg());
%>


