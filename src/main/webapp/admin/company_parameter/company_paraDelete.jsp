<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.CompanyParameter" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="CompanyParameterManager" class="com.misyn.mcms.admin.CompanyParameterManager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%
    CompanyParameter m_CompanyParameter = null;
    List<CompanyParameter> companyParameterList = new ArrayList<CompanyParameter>();
    String chkvalue = "";


    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) chkvalue = "";
        if (chkvalue.equalsIgnoreCase("checked")) {
            m_CompanyParameter = new CompanyParameter();

            try {
                m_CompanyParameter.setComid(Integer.parseInt(request.getParameter("P_N_COMID" + x)));
            } catch (Exception e) {
            }

            companyParameterList.add(m_CompanyParameter);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }


    //================Start Delete only one recode=================
    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("P_TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {
        m_CompanyParameter = new CompanyParameter();
        try {
            m_CompanyParameter.setComid(Integer.parseInt(request.getParameter("txtN_comid")));
        } catch (Exception e) {
        }

        companyParameterList.add(m_CompanyParameter);
    }
    //================End Delete only one recode=================
    //out.print("Size "+rolePrivilegeList.size());
    if (companyParameterList.size() > 0) {
        CompanyParameterManager.deleteCompanyParameter(companyParameterList);
    }
    response.sendRedirect("company_paraList.jsp?P_ERROR=" + CompanyParameterManager.getMsg());
%>


