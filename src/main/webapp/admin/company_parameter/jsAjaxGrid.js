/* 
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */


/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */


//<!--
var XMLHttpFactories = [
function () {
    return new XMLHttpRequest()
},
function () {
    return new ActiveXObject("Msxml2.XMLHTTP")
},
function () {
    return new ActiveXObject("Msxml3.XMLHTTP")
},
function () {
    return new ActiveXObject("Microsoft.XMLHTTP")
}
];

function GetXmlHttpObject() {
    var xmlHttp=null;
    try {
        // Firefox, Opera 8.0+, Safari
        xmlHttp = new XMLHttpRequest();
    } catch (e) {
        // Internet Explorer
        try {
            xmlHttp=new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
            xmlHttp=new ActiveXObject("Microsoft.XMLHTTP");
        }
    }
    return xmlHttp;
}

function createXMLHTTPObject() {
    var xmlhttp = null;
    for (var i=0;i<XMLHttpFactories.length;i++) {
        try {
            xmlhttp = XMLHttpFactories[i]();
        } catch (e) {
            continue;
        }
        break;
    }
    return xmlhttp;
}





function ViewGrid(url,clickposno){
    myRand = parseInt(Math.random()*999999999999999);
    document.getElementById("txtClickPos").value=clickposno;
    CmdAdd_onclick();
    var xmlHttp =  GetXmlHttpObject() ;
    // alert("hello............ : "+url);
	document.getElementById("apDiv1").style.display="block";
	//alert("ok...");
    try
    {
        xmlHttp.onreadystatechange = function() {
            if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
                document.getElementById("gridview").innerHTML = xmlHttp.responseText;
		document.getElementById("apDiv1").style.display="none";
            }
        }
        //alert(url);
        xmlHttp.open("GET", url+"&"+myRand, true);
        xmlHttp.send(null);
    }
    catch(e)
    {
        alert("Error : jsAjax->ViewCustDetails "+e);
    }
}

function ViewCustLifeGrid(url,clickposno){
    document.getElementById("txtClickPos").value=clickposno;
    CmdAdd_onclick();
    var xmlHttp =  GetXmlHttpObject() ;
    // alert("hello............ : "+url);
    try
    {
        xmlHttp.onreadystatechange = function() {
            if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
                document.getElementById("gridview").innerHTML = xmlHttp.responseText;
            }
        }
        //alert(url);
        xmlHttp.open("POST", url, true);
        xmlHttp.send(null);
    }
    catch(e)
    {
        alert("Error : jsAjax->ViewCustDetails "+e);
    }
}
function ViewAgentLifeGrid(url,clickposno){
    document.getElementById("txtClickPos").value=clickposno;
    CmdAdd_onclick();
    var xmlHttp =  GetXmlHttpObject() ;
    // alert("hello............ : "+url);
	document.getElementById("apDiv1").style.display="block";
    try
    {
        xmlHttp.onreadystatechange = function() {
            if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
                document.getElementById("gridview").innerHTML = xmlHttp.responseText;
				document.getElementById("apDiv1").style.display="none";
            }
        }
        //alert(url);
        xmlHttp.open("POST", url, true);
        xmlHttp.send(null);
    }
    catch(e)
    {
        alert("Error : jsAjax->ViewCustDetails "+e);
    }
}

            // -->



