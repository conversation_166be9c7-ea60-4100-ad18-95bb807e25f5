<%--
    Document   : rolePrevResult
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 19, 2010, 2:34:45 PM
    Author     : <PERSON><PERSON>
--%>
<%@page import="com.misyn.mcms.admin.CompanyParameter" %>
<%@page import="java.util.ArrayList" %>
<%@page import="java.util.List" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="CompanyParameterManagerBean" class="com.misyn.mcms.admin.CompanyParameterManager" scope="application"/>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">

<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="/css/common/loding.css" rel="stylesheet" type="text/css"/>
    <title>${CompanyTitle}</title>
</head>
<body>
<div id="loading" class="lodingImage" style="position:absolute;top:120px;">
    <img style="z-index:1002" alt="Loading..." src="/image/common/ajax-loader6.gif" border=0>
    <div class="lodingText">RECORD IS IN PROGRESS ...</div>
    <div class="labelText" align="center"><p>Don't close this browser window or navigate until the progress is
        complete</p></div>
</div>
<div id="cell1" class="innerDivBg"></div>

<%
    long timeURL = System.currentTimeMillis();
    CompanyParameter companyParameter = null;
    List<CompanyParameter> companyParameterpList = new ArrayList<CompanyParameter>();
    int recCount = 0;
    try {
        recCount = Integer.parseInt(request.getParameter("hideRecCount"));
    } catch (Exception e) {
    }

    int n_comid = -1;
    try {
        n_comid = Integer.parseInt(request.getParameter("txtN_comid"));
    } catch (Exception e) {
    }


    String chkValue = "";
    for (int i = 0; i < recCount; i++) {

        chkValue = "";
        chkValue = request.getParameter("chkboxView" + i);

        companyParameter = new CompanyParameter();
        companyParameter.setComid(n_comid);


        if (chkValue != null) {
            companyParameter.setChkvalue(chkValue);
        }

        //out.print("Para value "+request.getParameter("hideParaCode" + i)+"</br>");
        //out.print("chkboxViewvalue "+request.getParameter("chkboxView" + i)+"</br>");


        try {
            companyParameter.setParaCode(request.getParameter("hideParaCode" + i));
        } catch (Exception e) {
        }
        companyParameterpList.add(companyParameter);

    }
    if (companyParameterpList.size() > 0) {
        int r = CompanyParameterManagerBean.saveCompanyParamerers(companyParameterpList, n_comid);
        if (r > -1) {
            response.sendRedirect("company_paraList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
        } else {
            response.sendRedirect("company_paraList.jsp?" + timeURL + "&P_ERROR=Can not be save");
        }
    }
%>
</body>
</html>
