<%--
    Document   : searchUser
    Created on : Jan 17, 2011, 11:11:27 AM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : <PERSON>lum Sepala
    version 2.0
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@include file="/common/ValidateUser.jsp" %>

<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>
<%
    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    String readOnlyClass = "";


    long timeURL = System.currentTimeMillis();
    String ERROR = "";
    try {

        ERROR = request.getParameter("P_ERROR");
        if (ERROR == null) {
            ERROR = "";
        }

    } catch (Exception e) {
    }

    String URL = "searchUserAllListViewGrid.jsp?" + timeURL;
    String str_n_comid_popList = "";
    if (user.getN_accessusrtype() == 1) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "", "");


    } else if (user.getN_accessusrtype() == 2) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "", "");


    } else if (user.getN_accessusrtype() == 3) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + user.getN_comid(), "");


    } else if (user.getN_accessusrtype() == 4) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");


    } else if (user.getN_accessusrtype() == 5) {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");


    } else {
        str_n_comid_popList = DbRecordCommonFunction.getInstance().
                getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=-1", "");
    }



            /*  if (list != null) {
            if (list.size() != 0) {
            find_user = list.get(0);
            }
            }*/


%>


<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="/css/common/fb_form/fb_form_col.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jsAjaxGrid.js"></script>
    <script type="text/javascript" src="/css/common/fb_form/custom-form-elements.js"></script>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>

    <script type="text/javascript">
        var timeUrl = new Date().getDate();

        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            if ($("select#txtN_comid").val() == "-1") {
                                $("select#txtN_comid").focus();
                                return;
                            }
                            if ($("input#txtV_usrid").val() == "") {
                                $("input#txtV_usrid").focus();
                                return;
                            }
                            if ($("select#txtN_accessusrtype").val() == "-1") {
                                $("select#txtN_accessusrtype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            if ($("select#txtV_title").val() == "") {
                                $("select#txtV_title").focus();
                                return;
                            }
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            if ($("input#txtV_emp_no").val() == "") {
                                $("input#txtV_emp_no").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function pageSubmit(type) {
            var n_comid = document.getElementById("txtN_comid").value;
            var n_usrid = document.getElementById("txtV_usrid").value;

            if (type == 'Search') {
                if ((document.getElementById("txtN_comid").value == "-1") && (document.getElementById("txtV_usrid").value == "")) {
                    showDialogbox("Can not be blank");
                    return;
                }
                //parent.document.getElementById("loading").style.display="block";
                //parent.document.getElementById("cell1").style.display="block";

                document.getElementById("cmdSearch").style.cursor = 'wait';

                document.frmForm.cmdSearch.disabled = true;
                document.frmForm.cmdClose.disabled = true;

                ViewSearchGrid("<%=URL%>&P_N_COMID=" + n_comid + "&P_USRID=" + n_usrid);

            }

            else if (type == 'Close') {
                window.location = "<%=HOME_PAGE%>";

            }

        }


        function init() {
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
        }
    </script>

    <title>Aviva NDB PLC</title>
</head>
<body onLoad="init();">

<div class="form_header" style="width:740px;">Search User Details</div>
<div class="container" style="width:740px;">

    <form name="frmForm" action="" method="post">
        <input name="txtN_usrcode" id="txtN_usrcode" value="" type="hidden"/>
        <div class="col_half" style="width:55%;">
            <div class="ErrorNote"><%=ERROR%>
            </div>
            <fieldset>
                <div class="row"><span class="label">Company :</span><span class="txt_cont">
                                <select name="txtN_comid" id="txtN_comid" style="width:200px;" class="styled1">
                                    <%out.print(str_n_comid_popList);%>
                                </select>
                            </span></div>
                <div class="row"><span class="label">User ID  :</span><span class="txt_cont">
                                <input name="txtV_usrid" id="txtV_usrid" maxlength="20" title="User Id" type="text"
                                       value=""/>
                            </span></div>

                <div class="but_container">
                    <input type="button" name="cmdSearch" id="cmdSearch" value="Search" onclick="pageSubmit('Search')"
                           class="button"/>
                    <input type="button" name="cmdClose" id="cmdClose" value="Close" onclick="pageSubmit('Close')"
                           class="button"/>
                </div>
            </fieldset>


        </div>

        <div id="dialog" style="display:none;" title="Aviva NDB PLC">
            <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
            <p id="dialog-email" class="textGrey"></p>
        </div>

        <div id="gridview" style="clear:both;"></div>


    </form>
    <div class="spacer"> &nbsp;</div>
</div>

</body>
</html>
