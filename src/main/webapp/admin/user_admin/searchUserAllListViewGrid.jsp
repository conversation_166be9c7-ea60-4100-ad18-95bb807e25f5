<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@page import="java.util.Iterator" %>
<%@page import="java.util.List" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>
<%

    String tdClass1 = "class=\"tbl_row\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row';\"";
    String tdClass2 = "class=\"tbl_row2\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row2';\"";

    int m_clickPos = 1;
    int m_pagecount = com.misyn.mcms.utility.Parameters.getInstance().getNoRecordPerPage();

    try {
        m_clickPos = Integer.parseInt(request.getParameter("P_Click_Pos"));
    } catch (Exception e) {
    }
    int m_start = 0;
    int m_end = m_pagecount * m_clickPos;
    m_start = m_end - m_pagecount;


    //---------------------------
    int TYPE = 0;
    long timeURL = System.currentTimeMillis();
    String destinationURL = "";
    String URL = "userAll.jsp?" + timeURL;

    int n_comid = 0;
    try {
        n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
    } catch (Exception e) {
    }
    String v_usrid = "";
    try {
        v_usrid = request.getParameter("P_USRID");
    } catch (Exception e) {
    }
    String searchKey = "";

    if (n_comid != -1 && !v_usrid.trim().equalsIgnoreCase("")) {
        searchKey = " u.n_comid=" + n_comid + " AND u.v_usrid LIKE '" + v_usrid + "%' AND ";
    } else if (n_comid != -1 && v_usrid.trim().equalsIgnoreCase("")) {
        searchKey = " u.n_comid=" + n_comid + " AND ";
    }
    if (n_comid == -1 && !v_usrid.trim().equalsIgnoreCase("")) {
        searchKey = " u.v_usrid LIKE '" + v_usrid + "%' AND ";
    }

    List<User> list = null;
    User m_user = null;

    try {
        if (user.getN_accessusrtype() == 1) {
            list = UserManagerBean.getUserViewList("AND " + searchKey + " u.n_usrcode>0 ORDER BY u.n_usrcode");
        } else if (user.getN_accessusrtype() == 2) {
            list = UserManagerBean.getUserViewList("AND " + searchKey + " u.n_accessusrtype NOT IN(1,2) "
                    + "ORDER BY u.n_usrcode");
        } else if (user.getN_accessusrtype() == 3) {
            list = UserManagerBean.getUserViewList("AND " + searchKey + " u.n_accessusrtype IN(6) "
                    + "AND "
                    + "u.n_comid=" + user.getN_comid() + " "
                    + "ORDER BY u.n_usrcode");
        } else if (user.getN_accessusrtype() == 4) {
            list = UserManagerBean.getUserViewList("AND " + searchKey + " u.n_accessusrtype IN(7) "
                    + "AND "
                    + "u.n_comid=" + user.getN_comid() + " "
                    + "ORDER BY u.n_usrcode");
        } else if (user.getN_accessusrtype() == 5) {
            list = UserManagerBean.getUserViewList("AND " + searchKey + " u.n_accessusrtype IN(8) "
                    + "AND "
                    + "u.n_comid=" + user.getN_comid() + " "
                    + "ORDER BY u.n_usrcode");
        }


    } catch (Exception e) {
    }
    //----------------------------

%>

<table width="100%" cellpadding="0" cellspacing="1" bgcolor="#999999">
    <tr>
        <td class="tbl_row_header">No.</td>
        <td class="tbl_row_header">All</td>
        <td class="tbl_row_header">User Id</td>
        <td class="tbl_row_header">Company</td>
        <td class="tbl_row_header">User Role</td>
        <td class="tbl_row_header">First Name</td>
        <td class="tbl_row_header">Last Name</td>
        <td class="tbl_row_header">Status</td>
    </tr>
    <%
        int cnt = 1;

        if (list != null) {
            Iterator<User> it = list.iterator();
            while (it.hasNext()) {
                m_user = it.next();
                request.setAttribute("UserBean", m_user);

                destinationURL = URL + ""
                        + "&P_N_USRCODE=" + m_user.getN_usrcode()
                        + "&P_ISSEARCH_PAGE=true";

    %>
    <jsp:useBean id="UserBean" scope="request" type="com.misyn.mcms.admin.User"/>
    <input name="P_N_USRCODE<%=(cnt - 1)%>" type="hidden" value="<%=m_user.getN_usrcode()%>"/>

    <%
        if ((cnt % 2) == 0) {
    %>
    <tr  <%=tdClass1%> >
        <td>&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            <input name="chkboxDelete<%=(cnt - 1)%>" id=="chkboxDelete<%=(cnt - 1)%>" class="chkBox" type="checkbox"
                   value="checked"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_usrid"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_comCode"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_usrtype_desc"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_firstname"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_lastname"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_usrstatus"/>
        </td>

    </tr>
    <%
    } else {
    %>
    <tr <%=tdClass2%> >
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            <input name="chkboxDelete<%=(cnt - 1)%>" id=="chkboxDelete<%=(cnt - 1)%>" class="chkBox" type="checkbox"
                   value="checked"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_usrid"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_comCode"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_usrtype_desc"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_firstname"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_lastname"/>
        </td>
        <td onClick="window.location='<%=destinationURL%>'">&nbsp;&nbsp;
            <jsp:getProperty name="UserBean" property="v_usrstatus"/>
        </td>

    </tr>
    <% }
        cnt++;
    }
    }
    %>
    <input name="P_END_POS" type="hidden" value="<%=(cnt - 1)%>"/>
</table>
