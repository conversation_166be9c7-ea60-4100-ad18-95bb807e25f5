<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<jsp:useBean id="RolePrivilegeManagerBean" class="com.misyn.mcms.roleFacility.RolePrivilegeManager"
             scope="application"/>
<%@include file="/common/ValidateUser.jsp" %>
<%
    String ADD_RIGHT = "disabled='disabled'";
    if (((String) session.getAttribute("RIGHT_I")).equals("checked")) {
        ADD_RIGHT = "";
    }
    String DELETE_RIGHT = "disabled='disabled'";
    if (((String) session.getAttribute("RIGHT_D")).equals("checked")) {
        DELETE_RIGHT = "";
    }

    int click_page = 1;
    //*********** Sorting Fields**********************
    String SORTFIELDNAME = "u.n_usrcode";

    if (session.getAttribute("FIELD") != null) {
        SORTFIELDNAME = (String) session.getAttribute("FIELD");
    }
    String ASC_DESC_STATUS = " ASC ";
    if (session.getAttribute("ASC_DESC_STATUS") != null) {
        ASC_DESC_STATUS = (String) session.getAttribute("ASC_DESC_STATUS");
    }
    boolean IS_ASC = true;
    if (session.getAttribute("IS_ASC") != null) {
        IS_ASC = (Boolean) session.getAttribute("IS_ASC");
    }
    //*********** Sorting Fields**********************


    // out.println("SORT "+session.getAttribute("FIELD")+"  -->"+session.getAttribute("ASC_DESC_STATUS")+" -->"+IS_ASC);

    try {
        click_page = (Integer) session.getAttribute("CLICK_PAGE_NUMBER");
    } catch (Exception e) {
    }

    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    int recCnt = 0;
    int noRecPerPage = 5;

    long timeURL = System.currentTimeMillis();

    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = new Integer(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

    String ERROR = "";
    try {

        ERROR = request.getParameter("P_ERROR");
        if (ERROR == null) {
            ERROR = "";
        }

    } catch (Exception e) {
    }

    String strSQL = "";


    try {

        if (user.getN_accessusrtype() == 1) {
            strSQL = "SELECT u.n_usrcode  FROM usr_mst u "
                    + "WHERE "
                    + "u.v_usrstatus<>'C'";

        } else if (user.getN_accessusrtype() == 2) {
            strSQL = "SELECT u.n_usrcode  FROM usr_mst u "
                    + "WHERE "
                    + "u.v_usrstatus<>'C' "
                    + "AND "
                    + "u.n_accessusrtype NOT IN(1,2)";

        } else if (user.getN_accessusrtype() == 3) {
            strSQL = "SELECT u.n_usrcode  FROM usr_mst u "
                    + "WHERE "
                    + "u.v_usrstatus<>'C' "
                    + "AND "
                    + "u.n_accessusrtype IN(6) AND u.n_comid=" + user.getN_comid();

        } else if (user.getN_accessusrtype() == 4) {
            strSQL = "SELECT u.n_usrcode  FROM usr_mst u "
                    + "WHERE "
                    + "u.v_usrstatus<>'C' "
                    + "AND "
                    + "u.n_accessusrtype IN(7) AND u.n_comid=" + user.getN_comid();

        } else if (user.getN_accessusrtype() == 5) {
            strSQL = "SELECT u.n_usrcode  FROM usr_mst u "
                    + "WHERE "
                    + "u.v_usrstatus<>'C' "
                    + "AND "
                    + "u.n_accessusrtype IN(8) AND u.n_comid=" + user.getN_comid();

        }
        recCnt = DbRecordCommonFunction.getInstance().getRecordCount(strSQL);


        noRecPerPage = com.misyn.mcms.utility.Parameters.getInstance().getNoRecordPerPage();
    } catch (Exception e) {
    }

    long noOfrecord = recCnt;
    double noPage = recCnt % noRecPerPage;
    if (noPage != 0) {
        recCnt = (recCnt / noRecPerPage) + 1;
    } else {
        recCnt = (recCnt / noRecPerPage);
    }

    // out.print("SSS"+recCnt);


%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript" src="jsAjaxGrid.js"></script>
    <script language="javascript" type="text/javascript">

        // <!CDATA[
        var clickPos = 0;
        var forStart = 0;
        var forEnd = 10;

        function CmdAdd_onclick() {

            var newTable, startTag, endTag;
            var TABLE_WIDTH = 0;
            var cnt = "<%=recCnt%>";
            var lastClickPos = parseInt(clickPos);
            clickPos = parseInt(document.getElementById("txtClickPos").value);

            var m_cnt = parseInt(cnt);
            var m_lastClickPos = parseInt(lastClickPos);
            var TOLERENCE = 5;


            if (cnt <= 10) {
                forStart = 0;
                forEnd = parseInt(cnt);
            }
            else {
                //if(lastClickPos<clickPos)
                //{
                forStart = clickPos - TOLERENCE;
                forEnd = clickPos + TOLERENCE;

                if (forEnd > m_cnt) forEnd = m_cnt;
                if (m_cnt > 10 && parseInt(clickPos) < 10) {
                    forStart = 0;
                    forEnd = 10;
                }
                if (parseInt(clickPos) > (m_cnt - 10) && m_cnt > 10) {
                    forStart = m_cnt - 10;
                    forEnd = m_cnt;
                }
                if (parseInt(clickPos) == (m_cnt - 9) && m_cnt > 10) {
                    forStart = m_cnt - 15;
                    forEnd = m_cnt - 5;
                }
                if (forStart < 0) forStart = 0;
                if (forEnd > 5 && forEnd < 10) forEnd = 10;

            }


            for (i = (forStart + 1); i <= forEnd; i++) {
                //alert(m_lastClickPos);
                TABLE_WIDTH = TABLE_WIDTH + 20;
            }
            TABLE_WIDTH = TABLE_WIDTH + 200;

            /*startTag="<table width=\""+TABLE_WIDTH+"\" border=\"1\" cellspacing=\"0\" cellpadding=\"0\">"+*/
            startTag = "<table width=\"" + "100%" + "\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">" +
                "<tr>" +
                "<td width=\"" + "50%" + "\"align=\"right\" class=\"trClick\" onClick=\"ViewGrid('userAllListViewGrid.jsp?<%=timeURL%>&P_Click_Pos=" + (0 + 1) + "'," + (0 + 1) + ")\" >First&nbsp;</td>";
            // if((i+1)==clickPos)
            for (i = forStart; i < forEnd; i++) {
                if ((i + 1) == clickPos) {
                    startTag = startTag + "<td align=\"center\" class=\"trClickPos\"  onClick=\"ViewGrid('userAllListViewGrid.jsp?<%=timeURL%>&P_Click_Pos=" + (i + 1) + "'," + (i + 1) + ")\" >" + (i + 1) + "</td>";
                }
                else {
                    startTag = startTag + "<td align=\"center\" class=\"trClick\"  onClick=\"ViewGrid('userAllListViewGrid.jsp?<%=timeURL%>&P_Click_Pos=" + (i + 1) + "'," + (i + 1) + ")\" >" + (i + 1) + "</td>";
                }

            }

            if (forEnd > 1) {
                endTag = "<td width=\"" + "50%" + "\"align=\"left\" class=\"trClick\" onClick=\"ViewGrid('userAllListViewGrid.jsp?<%=timeURL%>&P_Click_Pos=" + (<%=recCnt%>) + "'," + (<%=recCnt%>) + ")\" >Last&nbsp;</td>" +
                    "</tr>" +
                    "</table>";
            } else {
                endTag = "<td width=\"" + "50%" + "\"align=\"center\">&nbsp;</td>" +
                    "</tr>" +
                    "</table>";
            }

            newTable = startTag + endTag;

            document.getElementById('tableDiv').innerHTML = newTable;
            if (forStart == 0) forStart = 1;
        }

        // ]]>

        //------------Start JQuery Script---------------------

        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            if ($("select#txtV_CAMP_SOURCE").val() == "0") {
                                $("select#txtV_CAMP_SOURCE").focus();
                                return;
                            }
                            if ($("input#txtV_CAMP_NAME").val() == "") {
                                $("input#txtV_CAMP_NAME").focus();
                                return;
                            }
                            if ($("input#txtD_CAMP_EFCT_DATE").val() == "") {
                                $("input#txtD_CAMP_EFCT_DATE").focus();
                                return;
                            }
                            if ($("input#txtV_CAMP_OWNER").val() == "") {
                                $("input#txtV_CAMP_OWNER").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }, "Cancel": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--

        //------------End JQuery Script---------------------


        //================Others=========================================================
        function setNextButtonDisable() {

        }

        function init() {
            setNextButtonDisable();
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
        }

        function pageSubmit(type) {
            if (type == "Add") {
                document.getElementById("cmdAdd").style.cursor = 'wait';
                document.frmForm.cmdAdd.disabled = true;
                document.frmForm.cmdDelete.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "userAll.jsp?&P_ISNEWRECORD=true";
                document.frmForm.submit();
            }
            if (type == "Delete") {
                document.getElementById("cmdDelete").style.cursor = 'wait';
                document.frmForm.cmdAdd.disabled = true;
                document.frmForm.cmdDelete.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "userDelete.jsp";
                document.frmForm.submit();
            }
            else if (type == 'Close') {
                window.location = "<%=HOME_PAGE%>";

            }


        }

        //===============================================================================

    </script>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>
</head>
<body onload="ViewGrid('userAllListViewGrid.jsp?<%=timeURL%>&P_SORT_FIELD=<%=SORTFIELDNAME%>&P_ASC_DESC_STATUS=<%=ASC_DESC_STATUS%>&P_IS_ASC=<%=IS_ASC%>&P_ISBODY_LOAD=1',<%=click_page%>);init();">
<form name="frmForm" method="post" action="">
    <div id="frmheader"> User List</div>
    <table class="tableWidth" border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td class="tbl_sub_header">
                <div id='progressview' style="color:#333333; font-size:12px;">Number of records : <%=noOfrecord%>
                </div>
                <div class="ErrorNote"><%=ERROR%>
                </div>
                <div id="but_cont" style="float:right">
                    <input type="button" name="cmdAdd" id="cmdAdd" value="Add" <%=ADD_RIGHT%>
                           onclick="pageSubmit('Add')"  <%=butClass%> />
                    <input type="button" name="cmdDelete" id="cmdDelete" value="Delete" <%=DELETE_RIGHT%>
                           onclick="pageSubmit('Delete')" <%=butClass%> />
                    <input type="button" name="cmdClose" id="cmdClose" value="Close"
                           onclick="pageSubmit('Close')" <%=butClass%> />
                    <input type="hidden" name="txtClickPos" id="txtClickPos"/>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="gridview" style="width:100%; height:100%;"></div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="apDiv1"><img src="/image/common/prgBar.gif"/></div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="tableDiv" style="width:100%; height:100%;"></div>
            </td>
        </tr>
        <tr>
            <td></td>
        </tr>
        <tr>
            <div id="dialog" style="display:none;" title="AVIVA NDB PLC">
                <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
                <p id="dialog-email" class="textGrey"></p>
            </div>
        </tr>
    </table>
</form>
</body>
</html>
