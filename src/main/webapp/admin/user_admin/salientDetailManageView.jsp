<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

%>
<%! int fontSize; %>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script>
        var isPriority = false;

        function init() {
            hideLoader();
        }
    </script>
    <style>
        .grab {
            cursor: -webkit-grab;
            cursor: grab;
        }

        .prevent-select {
            -webkit-user-select: none; /* Safari */
            -ms-user-select: none; /* IE 10 and IE 11 */
            user-select: none; /* Standard syntax */
        }

        .right-side-draggable-table tbody tr td:nth-child(1),
        .benefitTable tbody tr td:nth-child(4),
        .coverTable tbody tr td:nth-child(4),
        .specialPackagesTable tbody tr td:nth-child(4),
        .conditionSection tbody tr td:nth-child(4),
        .serviceFactorTable tbody tr td:nth-child(4)
        {
            display: none;
        }

        .card-custom-padding {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        .card-padding-top {
            padding-top: 8px !important;
        }

        .save-btn {
            width: 100px;
            height: 35px;
            float: right;
        }

        #productTable #tblProduct tr.selected {
            background: #d7d7d7 !important;
            color: #1b1e21 !important;
        }
    </style>
</head>
<body class="scroll" onload="init()">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input type="hidden" id="serviceFactorData" name="serviceFactorData">
        <input type="hidden" id="benefitData" name="benefitData">
        <input type="hidden" id="coverData" name="coverData">
        <input type="hidden" id="conditionData" name="conditionData">
        <input type="hidden" id="parentProduct" name="parentProduct">

        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Manage Product And Covers</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-6" id="productContainer">
                <div class="card mt-3 product-container-card">
                    <div class="card-body table-bg" style="padding:  0 0 14px 0;">
                        <div class="row prevent-select">
                            <div class="col-lg-12" style="    overflow: hidden;">
                                <ul class="nav nav-tabs" id="mySectionTab" role="i">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="serviceFactor-tab" data-toggle="tab"
                                           href="#serviceFactor" role="tab" aria-controls="home" aria-selected="true">Service
                                            Factors</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="cover-tab" data-toggle="tab" href="#cover" role="tab"
                                           aria-controls="profile" aria-selected="false">Covers</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="Benefit-tab" data-toggle="tab" href="#benefit"
                                           role="tab" aria-controls="contact" aria-selected="false">Benefits</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="conditionAndExclusion-tab" data-toggle="tab"
                                           href="#conditionAndExclusion" role="tab" aria-controls="contact"
                                           aria-selected="false">Condition and Exclusions</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="specialPackage-tab" data-toggle="tab"
                                           href="#specialPackage" role="tab" aria-controls="contact"
                                           aria-selected="false">Special Packages</a>
                                    </li>
                                </ul>
                                <div class="tab-content" id="mySectionTabContent">
                                    <div class="tab-pane fade show active" id="serviceFactor" role="tabpanel"
                                         aria-labelledby="serviceFactor-tab">

                                            <div class="card mt-3">
                                                <div class="col-lg-12 card-custom-padding" id="serviceFactorSection">
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <table id="serviceFactorTable"
                                                                   class="col-lg-12 mb-3 table table-sm draggable-table left-side-draggable-table serviceFactorTable">
                                                                <thead style="text-align: center;font-size: 14px;">
                                                                <tr>
                                                                    <th>Index</th>
                                                                    <th>Code</th>
                                                                    <th>Name</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody id="serviceFactorTableBody" class="grab"
                                                                       style="min-height: 400px;">
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                    </div>
                                    <div class="tab-pane fade show " id="benefit" role="tabpanel"
                                         aria-labelledby="serviceFactor-tab">

                                            <div class="card mt-3">
                                                <div class="col-lg-12 card-custom-padding" id="benefitSection">
                                                    <div class="row">
                                                        <div class="col-lg-12 mb-3">
                                                            <table id="benefitTable"
                                                                   class="col-lg-12 mb-3 table table-sm draggable-table left-side-draggable-table benefitTable">
                                                                <thead style="text-align: center;font-size: 14px;">
                                                                <tr>
                                                                    <th>Index</th>
                                                                    <th>Code</th>
                                                                    <th>Name</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody id="benefitTableTableBody" class="grab"
                                                                       style="min-height: 400px;">
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                    </div>
                                    <div class="tab-pane fade show " id="cover" role="tabpanel"
                                         aria-labelledby="serviceFactor-tab">
                                        <div class="card mt-3">
                                            <div class="col-lg-12 card-custom-padding" id="coverSection">
                                                <div class="row">
                                                    <div class="col-lg-12 mb-3">
                                                        <table id="coverTable"
                                                               class="col-lg-12 mb-3 table table-sm draggable-table left-side-draggable-table coverTable">
                                                            <thead style="text-align: center;font-size: 14px;">
                                                            <tr>
                                                                <th>Index</th>
                                                                <th>Code</th>
                                                                <th>Name</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody id="coverTableBody" class="grab">
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade show " id="conditionAndExclusion" role="tabpanel"
                                         aria-labelledby="serviceFactor-tab">
                                        <div class="card mt-3">
                                            <div class="col-lg-12 card-custom-padding"
                                                 id="conditionSection"><%--conditionSection--%>
                                                <div class="row">
                                                    <div class="col-lg-12 mb-3" draggable="true">
                                                        <table id="cAndETable"
                                                               class="col-lg-12 mb-3 table table-sm draggable-table left-side-draggable-table conditionSection">
                                                            <thead style="text-align: center;font-size: 14px;">
                                                            <tr>
                                                                <th>Index</th>
                                                                <th>Code</th>
                                                                <th>Name</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody id="cAndETableBody" class="grab">
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade show " id="specialPackage" role="tabpanel"
                                         aria-labelledby="serviceFactor-tab">
                                        <div class="card mt-3">
                                            <div class="col-lg-12 card-custom-padding"
                                                 id="specialPackagesSection"><%--conditionSection--%>
                                                <div class="row">
                                                    <div class="col-lg-12 mb-3" draggable="true">
                                                        <table id="specialPackagesTable"
                                                               class="col-lg-12 mb-3 table table-sm draggable-table left-side-draggable-table conditionSection">
                                                            <thead style="text-align: center;font-size: 14px;">
                                                            <tr>
                                                                <th>Index</th>
                                                                <th>Code</th>
                                                                <th>Name</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody id="specialPackagesTableBody" class="grab">
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" id="coversContainer">
                <div class="card card mt-3 covers-container-card">
                    <div >

                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal" style="position: absolute; right: 16px; top: 12px; z-index: 10;">Search </button>
                        <button type="button" class="btn btn-primary"  style="position: absolute; right: 16px; top: 45px; z-index: 10;" onclick="searchMainTableDetail()">Refresh</button>

                        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-sm" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="exampleModalLabel">Search</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">

                                        <form>
                                            <div class="form-group">
                                                <label for="1">Code</label>
                                                <input id="codeSearch" type="text" class="form-control form-control-sm" id="1">
                                            </div>
                                            <div class="form-group">
                                                <label for="2">Name</label>
                                                <input id="nameSearch" type="text" class="form-control form-control-sm" id="2">
                                            </div>
                                        </form>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" ID="btnMainSearch" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                        <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="searchMainTableDetail()">Search</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Modal -->

                    <div class="col-md-12 mt-3">
                        <ul class="nav nav-tabs" id="myTab" role="i">
                            <li class="nav-item">
                                <a class="nav-link active" id="home-tab" data-toggle="tab" href="#benefitMain" role="tab"
                                   aria-controls="home" aria-selected="true">Benefit / Cover / Loading Details</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="profile-tab" data-toggle="tab" href="#cweMain" role="tab"
                                   aria-controls="profile" aria-selected="false">CWE Detail</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="contact-tab" data-toggle="tab" href="#chargesMain" role="tab"
                                   aria-controls="contact" aria-selected="false">Charges / Discount</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="otherSec-tab" data-toggle="tab" href="#otherSecMain" role="tab"
                                   aria-controls="contact" aria-selected="false">Additional Details</a>
                            </li>
                        </ul>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="benefitMain" role="tabpanel" aria-labelledby="home-tab">

                                <div class="mt-2">
                                    <div class="card">
                                        <div class="card-body">
                                            <table id="benefitCoverLoadingTable"
                                                   class="table table-sm  table-hover draggable-table right-side-draggable-table"><%--cover detail table--%>
                                                <thead>
                                                <tr>
                                                    <th width="40px">Code</th>
                                                    <th>Name</th>
                                                    <th>Last Update Datetime</th>
                                                </tr>
                                                <c:if test="${benefitCoverLoadingDetailList == null}">
                                                    <tr><i class="badge-warning">No Covers Found</i></tr>
                                                </c:if>
                                                </thead>
                                                <tbody id="benefitCoverLoadingDetailTableBody">
                                                <c:forEach var="benefitCoverLoadingDetail"
                                                           items="${benefitCoverLoadingDetailList}">
                                                    <tr id="${benefitCoverLoadingDetail.id}"
                                                        class="draggable_tr">
                                                        <td class="hidden"></td>
                                                        <td>${benefitCoverLoadingDetail.code}</td>
                                                        <td>${benefitCoverLoadingDetail.coverName}</td>
                                                        <td>${benefitCoverLoadingDetail.updateDate}</td>
                                                    </tr>
                                                </c:forEach>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="tab-pane fade" id="cweMain" role="tabpanel" aria-labelledby="profile-tab">

                                <div class="mt-2">
                                    <div class="card">
                                        <div class="card-body">
                                            <table id="cweDetail"
                                                   class="table table-sm table-hover draggable-table right-side-draggable-table"><%--cover detail table--%>
                                                <thead>
                                                <tr>
                                                    <th width="40px">Code</th>
                                                    <th>Name</th>
                                                    <th>Last Update Datetime</th>
                                                </tr>
                                                <c:if test="${cweDetailList == null}">
                                                    <tr><i class="badge-warning">No Covers Found</i></tr>
                                                </c:if>
                                                </thead>
                                                <tbody id="cweMasterTableBody">
                                                <c:forEach var="cwe" items="${cweDetailList}">
                                                    <tr id="${cwe.id}"
                                                        class="draggable_tr">
                                                        <td class="hidden"></td>
                                                        <td>${cwe.code}</td>
                                                        <td>${cwe.coverName}</td>
                                                        <td>${cwe.updateDate}</td>
                                                    </tr>
                                                </c:forEach>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="tab-pane fade" id="chargesMain" role="tabpanel" aria-labelledby="contact-tab">

                                <div class="mt-2">
                                    <div class="card">
                                        <div class="card-body">
                                            <table id="cover-table"
                                                   class="table table-sm table-hover draggable-table right-side-draggable-table"><%--cover detail table--%>
                                                <thead>
                                                <tr>
                                                    <th width="40px">Code</th>
                                                    <th>Name</th>
                                                    <th>Last Update Datetime</th>
                                                </tr>
                                                <c:if test="${chargesAndDiscountList == null}">
                                                    <tr><i class="badge-warning">No Covers Found</i></tr>
                                                </c:if>
                                                </thead>
                                                <tbody id="chargesMasterTableBody">
                                                <c:forEach var="charges" items="${chargesAndDiscountList}">
                                                    <tr id="${charges.id}"
                                                        class="draggable_tr">
                                                        <td class="hidden"></td>
                                                        <td>${charges.code}</td>
                                                        <td>${charges.coverName}</td>
                                                        <td>${charges.updateDate}</td>
                                                    </tr>
                                                </c:forEach>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="tab-pane fade" id="otherSecMain" role="tabpanel" aria-labelledby="contact-tab">

                                <div class="mt-2">
                                    <div class="card">
                                        <div class="card-body">
                                            <table id="otherSec-table"
                                                   class="table table-sm table-hover draggable-table right-side-draggable-table"><%--cover detail table--%>
                                                <thead>
                                                <tr>
                                                    <th width="40px">Code</th>
                                                    <th>Name</th>
                                                    <th>Last Update Datetime</th>
                                                </tr>
                                                <c:if test="${chargesAndDiscountList == null}">
                                                    <tr><i class="badge-warning">No Data Found</i></tr>
                                                </c:if>
                                                </thead>
                                                <tbody id="otherSecTableBody">
                                                <c:forEach var="other" items="${getSrccTcDetailList}">
                                                    <tr id="${other.id}"
                                                        class="draggable_tr">
                                                        <td class="hidden"></td>
                                                        <td>${other.code}</td>
                                                        <td>${other.coverName}</td>
                                                        <td>${other.updateDate}</td>
                                                    </tr>
                                                </c:forEach>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12 mt-3">
                <button class='btn-primary btn save-btn'
                        type="button"
                        title='View Team' onclick="submitChanges()">SAVE
                </button>
            </div>
        </div>

    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/admin/product-management-datatables.js?v3"></script>
</body>
</html>
