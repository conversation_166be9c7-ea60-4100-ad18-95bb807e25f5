<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Untitled Document</title>
    <link href="/cmmCommon/style/fb_form/fb_form_col.css" rel="stylesheet" type="text/css"/>
    <
    <link href="/css/common/fb_form/fb_form_col.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/css/common/fb_form/custom-form-elements.js"></script>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript">
        $(function () {
            //
            //buttonImage: '/image/common/calendar.gif',
            var d1 = document.frmLead.txtDOB.value;
            $("#txtDOB").datepicker({
                showOn: 'button',
                buttonImage: '/cmmCommon/style/fb_form/dtpic.gif',
                buttonImageOnly: true,
                changeMonth: true,
                changeYear: true,
                yearRange: '1940:2099',
                minDate: '-70y',
                maxDate: '0d'

            });
            $("#txtDOB").datepicker('option', {dateFormat: "yy-mm-dd"});
            document.frmLead.txtDOB.value = d1;

        });

        function init() {
            CustomInit();


        }

    </script>
</head>
<body onload="init()">
<div class="container">
    <form id="frmLead" name="frmLead">
        <div class="col_half">
            <fieldset>
                <div class="row"><span class="label">Current _City:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
                <div class="row"><span class="label">Age:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Shoe---> size:</span><span class="txt_cont">
      
       <select name="txtV_title" id="txtV_title" class="styled">
            <option value="">Please select one</option>
            <option value="Rev">Rev</option>
            <option value="Dr">Dr</option>
            <option value="Mr">Mr</option>
            <option value="Mrs">Mrs</option>
            <option value="Ms">Ms</option>
            <option value="M/S">M/S</option>
            <option value="Other">Other</option>
          </select>
      
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn_up" value=""/>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn" value=""/>
      </span></div>
                <div class="row"><span class="label">Date of Birth:</span><span class="txt_cont">
      <input type="text" id="txtDOB" size="25" class="dtpicker"/>
      </span></div>
                <div class="row"><span class="label">Comments:</span><span class="txt_cont">
      <textarea cols="25" rows="8"> Go ahead - write something...   </textarea>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt1" name="opt1" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt1" name="opt1" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt2" name="opt2" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt2" name="opt2" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <input name="" type="checkbox" value="" class="styled" checked="checked" alt="4666"/>
      <input name="" type="checkbox" value="" class="styled" alt="asdasd"/>
      </span></div>
            </fieldset>
            <div class="but_container">
                <input class="button_sel" type="button" value="Save Changes"/>
                <input class="button" name="" type="button" value="Cancel"/>
            </div>
        </div>
        <div class="col_half">
            <fieldset>
                <div class="row"><span class="label">Current City:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
                <div class="row"><span class="label">Age:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <select name="d" class="styled">
        <option value="1">Option 1</option>
        <option value="2">Option 2</option>
        <option value="3">Option 3</option>
      </select>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn_up" value=""/>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn" value=""/>
      </span></div>
                <div class="row"><span class="label">Date of Birth:</span><span class="txt_cont">
      <input type="text" id="txtDOB" size="25" class="dtpicker"/>
      </span></div>
                <div class="row"><span class="label">Comments:</span><span class="txt_cont">
      <textarea cols="25" rows="8"> Go ahead - write something...   </textarea>
      </span></div>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt1" name="opt1" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt1" name="opt1" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt2" name="opt2" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt2" name="opt2" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <input name="" type="checkbox" value="" class="styled" checked="checked" alt="4666"/>
      <input name="" type="checkbox" value="" class="styled" alt="asdasd"/>
      </span></div>
            </fieldset>
            <div class="but_container">
                <input class="button_sel" type="button" value="Save Changes"/>
                <input class="button" name="" type="button" value="Cancel"/>
            </div>
        </div>
        <div class="col_full">
            <fieldset>
                <div class="row"><span class="label">Current City:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
                <div class="row"><span class="label">Age:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <select name="d" class="styled">
        <option value="1">Option 1</option>
        <option value="2">Option 2</option>
        <option value="3">Option 3</option>
      </select>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn_up" value=""/>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn" value=""/>
      </span></div>
                <div class="row"><span class="label">Date of Birth:</span><span class="txt_cont">
      <input type="text" id="txtDOB" size="25" class="dtpicker"/>
      </span></div>
                <div class="row"><span class="label">Comments:</span><span class="txt_cont">
      <textarea cols="25" rows="8"> Go ahead - write something...   </textarea>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt1" name="opt1" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt1" name="opt1" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt2" name="opt2" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt2" name="opt2" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <input name="" type="checkbox" value="" class="styled" checked="checked" alt="4666"/>
      <input name="" type="checkbox" value="" class="styled" alt="asdasd"/>
      </span></div>
            </fieldset>
            <div class="but_container">
                <input class="button_sel" type="button" value="Save Changes"/>
                <input class="button" name="" type="button" value="Cancel"/>
            </div>
        </div>
        <div class="col_half">
            <fieldset>
                <div class="row"><span class="label">Current City:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
                <div class="row"><span class="label">Age:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <select name="d" class="styled">
        <option value="1">Option 1</option>
        <option value="2">Option 2</option>
        <option value="3">Option 3</option>
      </select>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn_up" value=""/>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn" value=""/>
      </span></div>
                <div class="row"><span class="label">Date of Birth:</span><span class="txt_cont">
      <input type="text" id="txtDOB" size="25" class="dtpicker"/>
      </span></div>
                <div class="row"><span class="label">Comments:</span><span class="txt_cont">
      <textarea cols="25" rows="8"> Go ahead - write something...   </textarea>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt1" name="opt1" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt1" name="opt1" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt2" name="opt2" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt2" name="opt2" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <input name="" type="checkbox" value="" class="styled" checked="checked" alt="4666"/>
      <input name="" type="checkbox" value="" class="styled" alt="asdasd"/>
      </span></div>
            </fieldset>
            <div class="but_container">
                <input class="button_sel" type="button" value="Save Changes"/>
                <input class="button" name="" type="button" value="Cancel"/>
            </div>
        </div>
        <div class="col_half">
            <fieldset>
                <div class="row"><span class="label">Current City:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
                <div class="row"><span class="label">Age:</span><span class="txt_cont">
      <input type="text" size="25"/>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <select name="d" class="styled">
        <option value="1">Option 1</option>
        <option value="2">Option 2</option>
        <option value="3">Option 3</option>
      </select>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn_up" value=""/>
      </span></div>
                <div class="row"><span class="label">Shoe size:</span><span class="txt_cont">
      <input type="text" size="25" class="dropdown"/>
      <input type="button" class="drop_down_btn" value=""/>
      </span></div>
                <div class="row"><span class="label">Date of Birth:</span><span class="txt_cont">
      <input type="text" id="txtDOB" size="25" class="dtpicker"/>
      </span></div>
                <div class="row"><span class="label">Comments:</span><span class="txt_cont">
      <textarea cols="25" rows="8"> Go ahead - write something...   </textarea>
      </span></div>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt1" name="opt1" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt1" name="opt1" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <div>
        <input id="opt2" name="opt2" type="radio" class="styled" checked="checked" alt="Maasdasdasdle"/>
        <input id="opt2" name="opt2" type="radio" class="styled" alt="Female"/>
      </div>
      </span></div>
            </fieldset>
            <fieldset>
                <div class="row"><span class="label">Gender:</span><span class="opt_cont">
      <input name="" type="checkbox" value="" class="styled" checked="checked" alt="4666"/>
      <input name="" type="checkbox" value="" class="styled" alt="asdasd"/>
      </span></div>
            </fieldset>
            <div class="but_container">
                <input class="button_sel" type="button" value="Save Changes"/>
                <input class="button" name="" type="button" value="Cancel"/>
            </div>
        </div>
    </form>
    <div class="spacer"> &nbsp;</div>
</div>
</body>
</html>
