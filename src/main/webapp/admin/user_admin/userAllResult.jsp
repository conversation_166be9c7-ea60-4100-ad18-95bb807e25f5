<%--
    Document   : userAllResult
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 23, 2010, 1:50:45 PM
    Author     : <PERSON><PERSON>
--%>
<%@page import="com.misyn.mcms.admin.UserParameters" %>
<%@page import="java.util.ArrayList" %>
<%@page import="java.util.List" %>
<%@ page import="com.misyn.mcms.utility.AppConstant" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>

<%
    long timeURL = System.currentTimeMillis();
    User saveUser = new User();

    boolean isSearchPage = false;
    try {
        isSearchPage = (Boolean) session.getAttribute("ISSEARCH_PAGE");
    } catch (Exception e) {
    }
    int n_usrcode = 0;
    int result = -1;

    try {
        n_usrcode = Integer.parseInt(request.getParameter("txtN_usrcode"));
    } catch (Exception e) {
    }
    saveUser.setN_usrcode(n_usrcode);

    try {
        saveUser.setN_comid(Integer.parseInt(request.getParameter("txtN_comid")));
    } catch (Exception e) {
    }
    saveUser.setV_usrid(request.getParameter("txtV_usrid"));
    try {
        saveUser.setN_accessusrtype(Integer.parseInt(request.getParameter("txtN_accessusrtype")));
    } catch (Exception e) {
    }


    saveUser.setV_usrtypes(request.getParameter("hidV_usrtypes"));
    saveUser.setV_usrtype_desc(request.getParameter("txtV_usrtypes"));

    saveUser.setV_group_ids(request.getParameter("hidV_group_ids"));
    saveUser.setV_group_ids_desc(request.getParameter("txtV_group_ids"));

    saveUser.setV_password(request.getParameter("txtV_password"));
    saveUser.setV_usrstatus(request.getParameter("txtV_usrstatus"));

    if (request.getParameter("txtV_title") != null) {
        saveUser.setV_title(request.getParameter("txtV_title"));
    }


    saveUser.setV_firstname(request.getParameter("txtV_firstname"));
    saveUser.setV_lastname(request.getParameter("txtV_lastname"));

    saveUser.setV_address1(request.getParameter("txtV_address1"));
    saveUser.setV_address2(request.getParameter("txtV_address2"));
    saveUser.setV_email(request.getParameter("txtV_email"));
    saveUser.setV_land_phone(request.getParameter("txtV_land_phone"));
    saveUser.setV_mobile(request.getParameter("txtV_mobile"));
    saveUser.setV_nic(request.getParameter("txtV_nic"));
    saveUser.setV_emp_no(request.getParameter("txtV_emp_no"));

    saveUser.setV_inpuser(user.getV_usrid());

    //-------------------User Parameter Save Method Start ----------------------------
    UserParameters userParameters = null;
    List<UserParameters> userParametersList = new ArrayList<UserParameters>();
    int paraRecCount = 0;
    try {
        paraRecCount = Integer.parseInt(request.getParameter("txtParaCount"));
    } catch (Exception e) {
    }
    try {

        for (int i = 0; i < paraRecCount; i++) {
            userParameters = new UserParameters();

            try {
                userParameters.setV_paracode(request.getParameter("txtParaCode" + i));
            } catch (Exception e) {
            }
            try {
                userParameters.setV_txtValue(request.getParameter("txtParaValue" + i));
            } catch (Exception e) {
            }
            userParametersList.add(userParameters);
        }
    } catch (Exception e) {
    }
    //-------------------User Parameter Save Method End ----------------------------

    try {
        User sessionUser = (User) session.getAttribute(AppConstant.SESSION_USER);
        result = UserManagerBean.saveUser(saveUser,userParametersList,sessionUser);
        if (result == 0) {
            if (isSearchPage != true) {
                response.sendRedirect("userAllList.jsp?" + timeURL + "&P_ERROR=Can not be save, " + saveUser.getErrorMessage());
            } else {
                response.sendRedirect("searchUser.jsp?" + timeURL + "&P_ERROR=Can not be save, " + saveUser.getErrorMessage());
            }

        } else if (result > 0) {
            if (isSearchPage != true) {
                response.sendRedirect("userAllList.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
            } else {
                response.sendRedirect("searchUser.jsp?" + timeURL + "&P_ERROR=Record Save Successful");
            }

        }
    } catch (Exception e) {
    }


%>


<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">

<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>${CompanyTitle}</title>
</head>
<body>

</body>
</html>
