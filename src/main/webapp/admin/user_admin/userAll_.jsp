<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>

<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>

<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@page import="java.util.List" %>

<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>


<%
    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    String readOnlyClass = "";

    long timeURL = System.currentTimeMillis();
    String URL = "userAllResult.jsp?" + timeURL;

    int n_comid = -1;

    try {
        n_comid = Integer.parseInt(request.getParameter("P_N_COMID"));
    } catch (Exception e) {
    }

    int n_usrcode = -1;
    String str_n_comid_popList = "";
    String str_n_accessusrtype_popList = "";

    int size = 0;
    List<User> list = null;
    User find_user = null;
    String searchKey = "";

    boolean isNewRecord = true;
    try {
        isNewRecord = Boolean.parseBoolean(request.getParameter("P_ISNEWRECORD"));
    } catch (Exception e) {
    }

    try {
        n_usrcode = Integer.parseInt(request.getParameter("P_N_USRCODE"));
    } catch (Exception e) {
    }

    if (isNewRecord) {

        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        find_user = new User();
        if (user.getN_accessusrtype() == 1) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "", "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "", "");


        } else if (user.getN_accessusrtype() == 2) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "", "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype NOT IN(1,2)", "");


        } else if (user.getN_accessusrtype() == 3) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(6)", "");


        } else if (user.getN_accessusrtype() == 4) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(7)", "");


        } else if (user.getN_accessusrtype() == 5) {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + user.getN_comid(), "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(0)", "");


        } else {
            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=-1", "");
            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(-1)", "");

        }

    } else {
        session.removeAttribute("IS_NEW_RECORD");
        session.setAttribute("IS_NEW_RECORD", isNewRecord);

        readOnlyClass = " readonly class=\"textReadOnly\" ";
        if (user.getN_accessusrtype() == 1) {
            searchKey = " AND n_usrcode=" + n_usrcode;
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "", "");


        } else if (user.getN_accessusrtype() == 2) {
            searchKey = "AND n_usrcode=" + n_usrcode + " AND n_accessusrtype NOT IN(1,2)";
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype NOT IN(1,2)", "");


        } else if (user.getN_accessusrtype() == 3) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(6) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);


            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid =" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(6)", "");

        } else if (user.getN_accessusrtype() == 4) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(7) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(7)", "");


        } else if (user.getN_accessusrtype() == 5) {
            searchKey = "AND u.n_usrcode=" + n_usrcode + " AND u.n_accessusrtype IN(8) AND u.n_comid=" + user.getN_comid();
            list = UserManagerBean.getUserList(searchKey);
            find_user = list.get(0);

            str_n_comid_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("company_mst ", "n_comid", "v_comcode", "n_comid=" + find_user.getN_comid(), "");

            str_n_accessusrtype_popList = DbRecordCommonFunction.getInstance().
                    getPopupList("accessusrtype_mst ", "n_accessusrtype", "v_accessusrtype", "n_accessusrtype IN(0)", "");

        }
    }

            /*  if (list != null) {
            if (list.size() != 0) {
            find_user = list.get(0);
            }
            }*/


%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="../../css/common/forms_new.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/common/ListBox.js"></script>
    <script type="text/javascript" src="jsAjaxMultPopup2Key.js"></script>

    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>


    <title>${CompanyTitle}</title>
    <script type="text/javascript">
        var timeUrl = new Date().getDate();
        var ajaxMultiSelectPopup1 = new AjaxMultiSelectPopup('txtSearchV_usrtypes', 'hidV_usrtypes', 'lstV_usrtypes', "popupJSP/ajaxV_USRTYPES.jsp");
        var ajaxMultiSelectPopup2 = new AjaxMultiSelectPopup('txtSearchV_group_ids', 'hidV_group_ids', 'lstV_group_ids', "popupJSP/ajaxV_GROUP_IDs.jsp");


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            if ($("select#txtN_comid").val() == "-1") {
                                $("select#txtN_comid").focus();
                                return;
                            }
                            if ($("input#txtV_usrid").val() == "") {
                                $("input#txtV_usrid").focus();
                                return;
                            }
                            if ($("select#txtN_accessusrtype").val() == "-1") {
                                $("select#txtN_accessusrtype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            if ($("select#txtV_title").val() == "") {
                                $("select#txtV_title").focus();
                                return;
                            }
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--
        function init() {
            var isNew =<%=isNewRecord%>;
            if (isNew) {
                ajaxMultiSelectPopup2.ViewParameterList('viewUserParameterList.jsp', 0);
            }
            else {
                ajaxMultiSelectPopup2.ViewParameterList('viewUserParameterList.jsp', "<%= find_user.getV_group_ids()%>");
            }

        }

        function refresh_n_comid() {
            var p_n_comid = document.getElementById("txtN_comid").value;
            var isNew =<%=isNewRecord%>;
            if (isNew) document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid + "&P_ISNEWRECORD=true";
            else document.frmForm.action = "userAll.jsp?" + timeUrl + "&P_N_COMID=" + p_n_comid;

            document.frmForm.submit();
        }

        function pageSubmit(type) {

            if (type == 'Save') {
                if (document.getElementById("txtN_comid").value == "-1") {
                    showDialogbox("Please Select Company Code");
                    return;
                }
                else if (Trim(document.getElementById("txtV_usrid").value) == "") {
                    showDialogbox("Please Enter User Id");
                    return;
                }

                else if (document.getElementById("txtN_accessusrtype").value == "-1") {
                    showDialogbox("Please Select Access User Level");
                    return;
                }

                else if (Trim(document.getElementById("txtV_usrtypes").value) == "") {
                    showDialogbox("Please Select User Role");
                    return;
                }

                else if (Trim(document.getElementById("txtV_password").value) == "") {
                    showDialogbox("Please Enter Password");
                    return;
                }
                else if (Trim(document.getElementById("txtV_confirm_password").value) == "") {
                    showDialogbox("Please Enter Confirm Password");
                    return;
                }
                else if (Trim(document.getElementById("txtV_confirm_password").value) != Trim(document.getElementById("txtV_password").value)) {
                    document.getElementById("txtV_confirm_password").value = "";
                    document.getElementById("txtV_password").value = "";
                    showDialogbox("The Password You Typed Do Not Match.Please Retype the New Password in Both Boxes.");
                    return;
                }

                else if (document.getElementById("txtV_title").value == "") {
                    toggleSub('cont2');
                    showDialogbox("Please Select Title");
                    return;
                }

                else if (document.getElementById("txtV_firstname").value == "") {
                    toggleSub('cont2');
                    showDialogbox("Please Enter First Name");
                    return;
                }
                else if (document.getElementById("txtV_lastname").value == "") {
                    toggleSub('cont2');
                    showDialogbox("Please Enter Last Name");
                    return;
                }
                else if (document.getElementById("txtV_email").value == "") {
                    toggleSub('cont2');
                    showDialogbox("Please Enter Email Address");
                    return;
                }


                parent.document.getElementById("loading").style.display = "block";
                parent.document.getElementById("cell1").style.display = "block";
                document.getElementById("cmdSave").style.cursor = 'wait';
                document.frmForm.cmdSave.disabled = true;
                document.frmForm.cmdClose.disabled = true;
                document.frmForm.action = "<%=URL%>"
                document.frmForm.submit();
            }
            else if (type == 'Close') {
                //window.location="userAllList.jsp?<%=timeURL%>";
                ajaxMultiSelectPopup2.ViewParameterList('viewUserParameterList.jsp', 0);
            }

        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

        function toggleSub(submenu) {

            if (document.getElementById(submenu).style.display == 'none') {
                document.getElementById(submenu).style.display = 'block'
                //document.getElementById(submenu+'_top').className = 'Sect_Open'
                document.getElementById(submenu + '_img').src = "/image/forms/list_close.png"
            }
            else {
                document.getElementById(submenu).style.display = 'none'
                //document.getElementById(submenu+'_top').className='Sect_Close'
                document.getElementById(submenu + '_img').src = "/image/forms/list_open.jpg"
            }
        }
    </script>

</head>
<body onload="init();">
<form name="frmForm" action="" method="post">
    <div id="frmheader">
        <div id="caption"><span></span> User Details</div>
    </div>
    <div id="spacer" style="height:5px;">&nbsp;</div>
    <div id="Note" class="tbl_Note" style="padding-left:5px;width:250px;">Note : Fields marked with <span
            style="color:#FF0000">*</span> are mandatory.
    </div>
    <input name="txtN_usrcode" id="txtN_usrcode" value="<%=find_user.getN_usrcode()%>" type="hidden"/>

    <fieldset>
        <legend onClick="toggleSub('cont1');">General Information&nbsp;<img id="cont1_img"
                                                                            src="/image/forms/list_close.jpg"
                                                                            border="0"/></legend>
        <div id="cont1" style="width:550px;display:block;">
            <div class="required">
                <label>Company <span style="color:#D50000">*</span> : </label>
                <select name="txtN_comid" id="txtN_comid" <%=readOnlyClass%> style="width:160px;"
                        onchange="refresh_n_comid();">
                    <%out.print(str_n_comid_popList);%>
                </select>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">User ID <span style="color:#D50000">*</span> (Max 20 Characters):</label>
                <input name="txtV_usrid" id="txtV_usrid" <%=readOnlyClass%> maxlength="20" style="width:200px;"
                       title="User Id" type="text" value="<%=find_user.getV_usrid()%>"/>
            </div>
            <div class="required">
                <label>Access User Level <span style="color:#D50000">*</span> :</label>
                <select name="txtN_accessusrtype" id="txtN_accessusrtype" style="width:160px;">
                    <%out.print(str_n_accessusrtype_popList);%>
                </select>
            </div>
            <div class="required">
                <label>User Role <span style="color:#D50000">*</span> :</label>

                <input class="listtxt" name="txtV_usrtypes" readonly="readonly"
                       value="<%= UserManagerBean.getTextValue(find_user.getV_usrtype_desc())%>" id="txtV_usrtypes"
                       type="text"/>
                <input type="button" class="drop_down_btn" name="cmdV_usrtypes" id="cmdV_usrtypes" value=""
                       onclick="ajaxMultiSelectPopup1.onClick_ToggleButton(event,true)"/>


                <div class="multiDivTag" name="lstV_usrtypes" id="lstV_usrtypes">
                    <input class="listtxt" name="txtSearchV_usrtypes" id="txtSearchV_usrtypes" type="text"
                           onkeyup="ajaxMultiSelectPopup1.showPopupListMenu(event,false)"/>
                </div>
                <input name="hidV_usrtypes" id="hidV_usrtypes" type="hidden" value="<%= find_user.getV_usrtypes()%>"/>

            </div>
            <div class="required">
                <label>User Group :</label>

                <input class="listtxt" name="txtV_group_ids" readonly="readonly"
                       value="<%= UserManagerBean.getTextValue(find_user.getV_group_ids_desc())%>" id="txtV_group_ids"
                       type="text"/>
                <input type="button" class="drop_down_btn" name="cmdV_group_ids" id="cmdV_group_ids" value=""
                       onclick="ajaxMultiSelectPopup2.onClick_ToggleButton(event,true)"/>
                <div class="multiDivTag" name="lstV_group_ids" id="lstV_group_ids">
                    <input class="listtxt" name="txtSearchV_group_ids" id="txtSearchV_group_ids" type="text"
                           onkeyup="ajaxMultiSelectPopup2.showPopupListMenu(event,false)"/>
                </div>
                <input name="hidV_group_ids" id="hidV_group_ids" type="hidden"
                       value="<%= find_user.getV_group_ids()%>"/>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">Password <span style="color:#D50000">*</span> (Max 15 Characters):</label>
                <input name="txtV_password" id="txtV_password" maxlength="15" title="test" type="password"
                       value="<%=find_user.getV_real_password()%>"/>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">Confirm Password <span style="color:#D50000">*</span> (Max 15
                    Characters):</label>
                <input name="txtV_confirm_password" id="txtV_confirm_password" maxlength="15" title="test"
                       type="password" value="<%=find_user.getV_real_password()%>"/>
            </div>
            <div class="required">
                <label>User Status :</label>
                <select name="txtV_usrstatus" id="txtV_usrstatus" style="width:160px;">
                    <option value="X">Active</option>
                    <option value="D">Disabled</option>
                    <option value="L">Locked</option>
                    <option value="C">Delete</option>
                </select>
            </div>
        </div>
    </fieldset>

    <fieldset>
        <legend onClick="toggleSub('cont2');">User Contact Information&nbsp;<img id="cont2_img"
                                                                                 src="/image/forms/list_close.jpg"
                                                                                 border="0"/></legend>
        <div id="cont2" style="width:550px; display:block;">
            <div class="required">

                <label>Title <span style="color:#D50000">*</span> : </label>
                <select name="txtV_title" id="txtV_title" style="width:160px;">
                    <option value="">Please select one</option>
                    <option value="Rev">Rev</option>
                    <option value="Dr">Dr</option>
                    <option value="Mr">Mr</option>
                    <option value="Mrs">Mrs</option>
                    <option value="Ms">Ms</option>
                    <option value="M/S">M/S</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">First Name <span style="color:#D50000">*</span> (Max 20
                    Characters):</label>
                <input name="txtV_firstname" id="txtV_firstname" maxlength="20" title="test" type="text"
                       value="<%=find_user.getV_firstname()%>"/>
            </div>
            <div class="required">
                <label>Last Name <span style="color:#D50000">*</span> :</label>
                <input name="txtV_lastname" id="txtV_lastname" maxlength="20" title="test" type="text"
                       value="<%=find_user.getV_lastname()%>"/>

            </div>
            <div class="required">
                <label>Address :</label>
                <input name="txtV_address1" id="txtV_address1" type="text" value="<%=find_user.getV_address1()%>"/>
                <div></div>
                <input name="txtV_address2" id="txtV_address2" type="text" value="<%=find_user.getV_address2()%>"/>
            </div>
            <div class="required">
                <label>Email <span style="color:#D50000">*</span> :</label>
                <input name="txtV_email" id="txtV_email" type="text" value="<%=find_user.getV_email()%>"/>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">Land Phone</label>
                <input name="txtV_land_phone" id="txtV_land_phone" maxlength="20" title="test" type="text"
                       value="<%=find_user.getV_land_phone()%>"/>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">Mobile Phone</label>
                <input name="txtV_mobile" id="txtV_mobile" maxlength="20" title="test" type="text"
                       value="<%=find_user.getV_mobile()%>"/>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">NIC/Passport</label>
                <input name="txtV_nic" id="txtV_nic" maxlength="20" title="test" type="text"
                       value="<%=find_user.getV_nic()%>"/>
            </div>
            <div class="required">
                <label class="lbl" style="top:100px;">Employee No</label>
                <input name="txtV_emp_no" id="txtV_emp_no" maxlength="20" title="test" type="text"
                       value="<%=find_user.getV_emp_no()%>"/>
            </div>
        </div>
    </fieldset>

    <fieldset>
        <legend onClick="toggleSub('cont3');">User Parameters&nbsp;<img id="cont3_img" src="/image/forms/list_close.jpg"
                                                                        border="0"/></legend>
        <div id="cont3" style="width:550px;display:none;">
            <div id="paraListView">
                Parameter List
            </div>
        </div>
    </fieldset>

    <fieldset>
        <div class="submit" style="width:400px;">
            <div>
                <input type="button" name="cmdSave" id="cmdSave" value="Save"
                       onclick="pageSubmit('Save')" <%=butClass%>/>
                <input type="button" name="cmdDelete" id="cmdDelete" value="Delete"
                       onclick="pageSubmit('Delete')" <%=butClass%>/>
                <input type="button" name="cmdClose" id="cmdClose" value="Close"
                       onclick="pageSubmit('Close')" <%=butClass%>/>
            </div>
        </div>
    </fieldset>

    <% if (!isNewRecord) {

    %>
    <script type="text/javascript">
        document.getElementById("txtN_accessusrtype").value =<%=find_user.getN_accessusrtype()%>;
        document.getElementById("txtV_title").value = "<%=find_user.getV_title()%>";
        document.getElementById("txtV_usrstatus").value = "<%=find_user.getV_usrstatus()%>";
        <% if (find_user.getV_usrstatus().equalsIgnoreCase("A")) {
        %>
        document.getElementById("txtV_usrstatus").value = "X";
        <% }%>

    </script>

    <%} else {%>
    <script type="text/javascript">
        document.getElementById("txtN_comid").value =<%=n_comid%>;
    </script>
    <%}%>

    <div id="dialog" style="display:none;" title="Aviva NDB PLC">
        <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
        <p id="dialog-email" class="textGrey"></p>
    </div>
</form>
</body>
</html>
