/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

function AjaxMultiSelectPopup(txtID,hideValue,lstID,m_url)
{
    this.oListBox=null;
    this.id_valueArray = null;
    this.txt_valueArray =null;
    this.selected_value="";



    this.lstID=lstID;
    this.txtID=txtID;
    this.hideValue=hideValue;
    this.m_url=m_url;
    var TimeURL=new Date().getTime();
    var cmdPopBtnID= "cmd"+hideValue.substring(3);





    var XMLHttpFactories = [
    function () {
        return new XMLHttpRequest()
    },
    function () {
        return new ActiveXObject("Msxml2.XMLHTTP")
    },
    function () {
        return new ActiveXObject("Msxml3.XMLHTTP")
    },
    function () {
        return new ActiveXObject("Microsoft.XMLHTTP")
    }
    ];

    function GetXmlHttpObject() {
        var xmlHttp=null;
        try {
            // Firefox, Opera 8.0+, Safari
            xmlHttp = new XMLHttpRequest();
        } catch (e) {
            // Internet Explorer
            try {
                xmlHttp=new ActiveXObject("Msxml2.XMLHTTP");
            } catch (e) {
                xmlHttp=new ActiveXObject("Microsoft.XMLHTTP");
            }
        }
        return xmlHttp;
    }

    function createXMLHTTPObject() {
        var xmlhttp = null;
        for (var i=0;i<XMLHttpFactories.length;i++) {
            try {
                xmlhttp = XMLHttpFactories[i]();
            } catch (e) {
                continue;
            }
            break;
        }
        return xmlhttp;
    }


    var isClick=false;
    this.onClick_ToggleButton=function(e,isPopBtn)
    {
		
        document.getElementById(txtID).value="";
        document.getElementById(txtID).style.display="none";
        //alert(isClick);
        if(isClick==false)
        {
			
			
            this.showPopupListMenu(e,isPopBtn);
            isClick=true;

        }
        else
        {
            try{
                document.getElementById(cmdPopBtnID).className = 'drop_down_btn';
            }
            catch(e){}
            if(isClick==true && oListBox.getStyle()==false)
            {				
				
                this.showPopupListMenu(e,isPopBtn);
                isClick=true;

            }else
            {
                HideListBox();
                isClick=false;

            }
        }



    }



    this.showPopupListMenu=function(e,isPopBtn)
    {
        var url="";
        var xmlHttp =  GetXmlHttpObject();
        this.e=e;
        id_valueArray = new Array();
        txt_valueArray = new Array();

        if (xmlHttp==null)
        {
            alert ("Your browser does not support AJAX!");
            return;
        }

        var paraKey1=document.getElementById(txtID).value;
       
        url=m_url+"?"+TimeURL+"&searchKey1="+paraKey1;

        try
        {  
            if(txtID=="txtSearchV_usrtypes" || txtID=="txtSearchV_group_ids")
            {
                var paraKey2=document.getElementById("txtN_comid").value;
                url=m_url+"?"+TimeURL+"&searchKey1="+paraKey1+"&searchKey2="+paraKey2;
                
            }
        }
        catch(e1)
        {

        }


        xmlHttp.onreadystatechange = function() {
            var xmlDoc=null;
            var rec_count=0;
            var txtTagId="inputtext";
            var tempTxtID="";

            var hideTextValues= document.getElementById(hideValue).value;
            document.getElementById(txtID).style.cursor='wait';
            try{
                document.getElementById(cmdPopBtnID).style.cursor='wait';
            }
            catch(e){}

            try
            {
                DisposeListBox();
            }catch(e1)
            {
            }

            ListBoxInit();
            setVisible(false);
            document.getElementById(txtID).style.display="block";
			
            if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {

                xmlDoc=xmlHttp.responseXML.documentElement;
                if (xmlDoc!=null) {
                    if (xmlDoc.getElementsByTagName("recCount")[0].childNodes[0]!=null)
                    {
                        rec_count=xmlDoc.getElementsByTagName("recCount")[0].childNodes[0].nodeValue;
                        if(rec_count>0)
                        {
                            try{
                                document.getElementById(cmdPopBtnID).className = 'drop_down_btn_up';
                            }
                            catch(e){}
                        }
                    }
                    for (var i=0;i<rec_count;i++)
                    {
                        txtTagId="inputtext"+(i+1);

                        if (xmlDoc.getElementsByTagName(txtTagId)[0].childNodes[0]!=null)
                        {
                            id_valueArray[i]=xmlDoc.getElementsByTagName(txtTagId)[0].childNodes[0].nodeValue;
                            txt_valueArray[i]=xmlDoc.getElementsByTagName(txtTagId)[1].childNodes[0].nodeValue;
                            if(rec_count==1){
                                selected_value=id_valueArray[i];
                                tempTxtID=txt_valueArray[i];
                            }

                            var s_val="";
                            try
                            {
                                var tmp_s_val=hideTextValues.split(",");
                                for(x = 0;x < tmp_s_val.length; x++){
                                    if(id_valueArray[i]==tmp_s_val[x])
                                    {
                                        s_val=tmp_s_val[x];
                                        break;
                                    }
                                }


                            }
                            catch(e2)
                            {
                            //alert(e2);
                            }                            
                            try
                            {
                                if(txtID=="txtSearchV_usrtypes" || txtID=="txtSearchV_group_ids") 
                                {
                                    AddItem(txt_valueArray[i],id_valueArray[i],s_val);
                                }
                                else
                                {
                                    AddItem(txt_valueArray[i]+" ("+id_valueArray[i]+")",id_valueArray[i],s_val);
                                }
                            }
                            catch(e)
                            {
                            }
                            
                            setVisible(true);
                            isClick=true;
                        }
                    }
                    if(rec_count>0)
                    {
                        if(isPopBtn==true)
                        {

                    }
                    }
                    else
                    {
                        isClick=false;
                        if(isPopBtn==true)
                        {
                            //alert("Record Not Found");
                            showDialogbox("Record Not Found");
                        }
                        else
                        {
                            //alert(document.getElementById(txtID).value+"  is Invalid Record");
                            setVisible(true);
                            isClick=true;
                        }
                    //showDialogbox("Record Not Found");
                    // selected_value="";
                    //document.getElementById(txtID).value="";

                    }
                    document.getElementById(txtID).style.cursor='default';
                    try{
                        document.getElementById(cmdPopBtnID).style.cursor='default';
                    }
                    catch(e){}

                }
            }
        }
        xmlHttp.open("POST", url, true);
        xmlHttp.send(null);
    }

 	
	
    this.ViewParameterList=function(url,v_groupIds){

        var myRand = parseInt(Math.random()*999999999999999);
        var n_comid=document.getElementById("txtN_comid").value;
        var n_usrcode=document.getElementById("txtN_usrcode").value;

        var xmlHttp =  GetXmlHttpObject() ;
        if(v_groupIds=="" || v_groupIds==null)v_groupIds="0";
        //alert("hello............ : "+v_groupIds);

        url=url+"?"+myRand+"&P_N_COMID="+n_comid+"&P_V_GROUPIDS="+v_groupIds+"&P_N_USRCODE="+n_usrcode;
        try
        {
            xmlHttp.onreadystatechange = function() {
                if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
                    document.getElementById("paraListView").innerHTML = xmlHttp.responseText;
                }
            }
            //alert(url);
            xmlHttp.open("POST", url, true);
            xmlHttp.send(null);
        }
        catch(e)
        {
            alert("Error : jsAjax->ViewParameterList "+e);
        }
    }

    function ViewParameterList_1(url,v_groupIds){
        var myRand = parseInt(Math.random()*999999999999999);
        var n_comid=document.getElementById("txtN_comid").value;
        var n_usrcode=document.getElementById("txtN_usrcode").value;

        var xmlHttp =  GetXmlHttpObject() ;
        if(v_groupIds=="" || v_groupIds==null)v_groupIds="0";
        //alert("hello............ : "+v_groupIds);

        url=url+"?"+myRand+"&P_N_COMID="+n_comid+"&P_V_GROUPIDS="+v_groupIds+"&P_N_USRCODE="+n_usrcode;
        try
        {
            xmlHttp.onreadystatechange = function() {
                if ( (xmlHttp.readyState==4) && (xmlHttp.status==200) ) {
                    document.getElementById("paraListView").innerHTML = xmlHttp.responseText;
                }
            }
            //alert(url);
            xmlHttp.open("POST", url, true);
            xmlHttp.send(null);
        }
        catch(e)
        {
            alert("Error : jsAjax->ViewParameterList "+e);
        }
    }


    function focusLst(e)
    {

        if (!e) e=window.event;  // Block the user of digits.
        var code;
        //if     code = e.charCode  else        code = e.keyCode;
        if ((e.charCode) && (e.keyCode==0))
        {
            code = e.charCode
        //alert(code);
        }
        else
        {
            code = e.keyCode;
        //alert(code);
        }
        if(code==40)
        {
    //alert(code);
    // document.getElementById(lstID).focus();
    //document.getElementById(lstID).selectedIndex=0;
    }
    }







    function Trim(str)
    {
        while (str.substring(0,1) == ' ') // check for white spaces from beginning
        {
            str = str.substring(1, str.length);
        }
        while (str.substring(str.length-1, str.length) == ' ') // check white space from end
        {
            str = str.substring(0,str.length-1);
        }

        return str;
    }


    //var oListBox;
    function ListBoxInit()
    {
        var Arguments = {
            Base: document.getElementById(lstID),
            Rows: 3,
            Width: 600,
            NormalItemColor: null,
            NormalItemBackColor: null,
            AlternateItemColor: null,
            AlternateItemBackColor: null,
            SelectedItemColor: null,
            SelectedIItemBackColor: null,
            HoverItemColor: null,
            HoverItemBackColor: null,
            HoverBorderdColor: null,
            ClickEventHandler: OnClick
        };

        oListBox = new ListBox(Arguments);

    }
    function setVisible(isVisible)
    {
        if(isVisible)
        {
            document.getElementById(lstID).style.display="block";
            oListBox.setVisible(true);
            try
            {
                document.getElementById(txtID).focus();
            }catch(e)
            {
            }
            
        }
        else
        {
            document.getElementById(lstID).style.display="none";
            oListBox.setVisible(false);

        //oListBox.Dispose();
        }

    }

    function AddItem(Text,Value,SelectedValue)
    {
        oListBox.AddItem(Text,Value,SelectedValue);
    }


    var OnClick = function(Sender, EventArgs)
    {
		
        var selectedValues=document.getElementById(hideValue).value;
        var selectedText=document.getElementById("txt"+hideValue.substring(3)).value;
		
		
		
        var Message = new Array();
        if(Sender.checked)
        {
            //selectedValues=selectedValues.replace(EventArgs.Value+",","");
            selectedValues=selectedValues+EventArgs.Value+",";
            selectedText=selectedText+EventArgs.Text+",";
        //alert("OK"+EventArgs.Text);
        }
        else
        {
            selectedValues=selectedValues.replace(EventArgs.Value+",","");
            selectedText=selectedText.replace(EventArgs.Text+",","");
			
        }
		

        document.getElementById(hideValue).value=selectedValues;
        if(hideValue=="hidV_group_ids")ViewParameterList_1("viewUserParameterList.jsp",selectedValues);

        try
        {//txtSearchV_BRANCH_CODE
            var txtTextid="txt"+hideValue.substring(3);
            document.getElementById(txtTextid).value=selectedText;//selectedValues;
        }catch(ex)
        {

        }

	


    //document.write("The first element is " + mySplitResult[0]);
    //document.write("<br /> The second element is  " + mySplitResult[1]);

    //  Message.push('IsSelected: ' + Sender.checked.toString());
    // Message.push('Text: ' + EventArgs.Text);
    // Message.push('Value: ' + EventArgs.Value);
    //  Message.push('Index: ' + EventArgs.ItemIndex);

    // document.getElementById('DivMessage').innerHTML = Message.join('<br />');
    }




    function DisposeListBox()
    {
        try
        {
            isClick=false;
			
            if(oListBox!=null)
            {
				
                oListBox.Dispose();

            }
            onhideAllLstBox(txtID);
        }
        catch(E)
        {
        }

    }

    function HideListBox ()
    {

        try
        {
            isClick=false;
            oListBox.setVisible(false);			
            document.getElementById(lstID).style.display="none";
            try{
                document.getElementById(cmdPopBtnID).className = 'drop_down_btn';
            }
            catch(e){}
            if(oListBox!=null)
            {
                oListBox.Dispose();
            }
            onhideAllLstBox(txtID);
        }
        catch(E)
        {
        }
    }
	
	
// window.onunload = function(){oListBox.Dispose(); }



}
