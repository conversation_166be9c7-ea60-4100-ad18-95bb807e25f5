<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="java.util.ArrayList" %>
<%@page import="java.util.List" %>
<%@ page import="com.misyn.mcms.utility.AppConstant" %>
<%@include file="/common/ValidateUser.jsp" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%
    User m_user = null;
    List<User> userList = new ArrayList<User>();
    String chkvalue = "";


    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) chkvalue = "";
        if (chkvalue.equalsIgnoreCase("checked")) {
            m_user = new User();

            try {
                m_user.setN_usrcode(Integer.parseInt(request.getParameter("P_N_USRCODE" + x)));
            } catch (Exception e) {
            }

            userList.add(m_user);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }


    //================Start Delete only one recode=================
    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("P_TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {
        m_user = new User();
        try {
            m_user.setN_usrcode(Integer.parseInt(request.getParameter("txtN_usrcode")));
        } catch (Exception e) {
        }

        userList.add(m_user);
    }
    //================End Delete only one recode=================
    //out.print("Size "+rolePrivilegeList.size());
    if (userList.size() > 0) {
        User sessionUser = (User) session.getAttribute(AppConstant.SESSION_USER);
        UserManagerBean.deleteUser(userList,sessionUser);
    }
    response.sendRedirect("userAllList.jsp?P_ERROR=" + UserManagerBean.getMsg());
%>


