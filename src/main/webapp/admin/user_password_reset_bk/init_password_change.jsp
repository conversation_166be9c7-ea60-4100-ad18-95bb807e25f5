<%--
    Document   : user_all
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 22, 2010, 2:32:34 PM
    Author     : <PERSON><PERSON>
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="session"/>
<%


    String butClass = "class=\"button\" onmouseover=\"className='button_dwn';\" onmouseout=\"className='button';\"";
    String readOnlyClass = "";

    long timeURL = System.currentTimeMillis();
    String URL = "init_password_change_result.jsp?" + timeURL;
    String ERROR = request.getParameter("ERROR");

    if (ERROR == null) {
        ERROR = "";
    }






            /*  if (list != null) {
            if (list.size() != 0) {
            find_user = list.get(0);
            }
            }*/


%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>

    <link href="/css/common/fb_form/fb_form_col.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/css/common/fb_form/custom-form-elements.js"></script>
    <link href="/css/jquery_css/theme_orange/jquery-ui-1.8.2.custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript" src="/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>


    <script type="text/javascript" src="../../script/password/jquery-1.3.2.min.js"></script>
    <script type="text/javascript" src="../../script/password/password_strength_plugin.js"></script>
    <script src="../../SpryAssets/SpryValidationPassword.js" type="text/javascript"></script>
    <link href="../../script/password/style.css" rel="stylesheet" type="text/css"/>


    <script type="text/javascript">

        $(document).ready(function () {

            //BASIC


            //ADVANCED
            $(".inputBox").passStrength({
                shortPass: "top_shortPass",
                badPass: "top_badPass",
                goodPass: "top_goodPass",
                strongPass: "top_strongPass",
                baseStyle: "top_testresult",
                messageloc: 0
            });
        });

        function checkPassword() {
            var newpass = document.PasswordChange.NewPassword.value;


            if ((newpass.length < 6) || (newpass.length > 15)) {
                alert("The New Password length must be between 6 and 15")
                document.PasswordChange.NewPassword.focus();
                return false;
            }
            for (i = 0; i < newpass.length; i++) {
                ch = newpass.charAt(i);
                if (ch == ' ') {
                    alert("Password cannot have spaces");
                    return false;
                }
            }
            if (document.PasswordChange.ReNewPassword.value != newpass) {
                alert("Please Re-enter the same New Password")
                document.PasswordChange.ReNewPassword.focus();
                return false;
            }
            document.PasswordChange.B1.disabled = true;
            document.PasswordChange.B2.disabled = true;
            document.PasswordChange.B3.disabled = true;
            return true;
        }

        /* $(function() {
                    //
                    //buttonImage: '/image/common/calendar.gif',
                    var d1=document.frmForm.txtDOB.value;
                    $("#txtDOB").datepicker({
                        showOn: 'button',
                        buttonImage: '/css/common/fb_form/dtpic.gif',
                        buttonImageOnly: true,
                        changeMonth: true,
                        changeYear: true,
                        yearRange: '1940:2099' ,
                        minDate: '-70y',
                        maxDate: '0d'

                    });
                    $("#txtDOB").datepicker('option', {dateFormat: "yy-mm-dd"});
                    document.frmForm.txtDOB.value=d1;

                });*/


        var timeUrl = new Date().getDate();


        //--> For Dialog Box <--
        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').dialog({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //if($("select#txtN_comid").val()=="-1"){$("select#txtN_comid").focus();return;}
                            if ($("input#txtN_mnuname").val() == "") {
                                $("input#txtN_mnuname").focus();
                                return;
                            }
                            if ($("select#txtV_apptype").val() == "0") {
                                $("select#txtV_apptype").focus();
                                return;
                            }
                            if ($("input#txtV_usrtypes").val() == "") {
                                $("input#txtV_usrtypes").focus();
                                return;
                            }
                            if ($("input#txtV_password").val() == "") {
                                $("input#txtV_password").focus();
                                return;
                            }
                            if ($("input#txtV_confirm_password").val() == "") {
                                $("input#txtV_confirm_password").focus();
                                return;
                            }
                            if ($("select#txtV_title").val() == "") {
                                $("select#txtV_title").focus();
                                return;
                            }
                            if ($("input#txtV_firstname").val() == "") {
                                $("input#txtV_firstname").focus();
                                return;
                            }
                            if ($("input#txtV_lastname").val() == "") {
                                $("input#txtV_lastname").focus();
                                return;
                            }
                            if ($("input#txtV_email").val() == "") {
                                $("input#txtV_email").focus();
                                return;
                            }
                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        //--> End Dialog Box <--
        function init() {
            // parent.document.getElementById("cell1").style.display="none";
            //parent.document.getElementById("loading").style.display="none";
            document.PasswordChange.NewPassword.focus();
        }


        function pageSubmit(type) {

            if (type == 'Password') {
                if (!checkPassword()) return;

                //parent.document.getElementById("loading").style.display="block";
                //parent.document.getElementById("cell1").style.display="block";
                document.getElementById("B1").style.cursor = 'wait';
                document.PasswordChange.B1.disabled = true;
                document.PasswordChange.B2.disabled = true;
                document.PasswordChange.action = "<%=URL%>"
                document.PasswordChange.submit();
            }

            else if (type == 'Close') {
                history.go(-2);

                // window.location="<%=HOME_PAGE%>?<%=timeURL%>";

            }

        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

    </script>
    <style>
        .fbSelectBox {
            border: 1px solid #0C0;
            padding: 3px;
        }

        .selectbox {
            margin: 0px 5px 10px 0px;
            padding-left: 2px;
            font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
            font-size: 1em; /* Resize Font*/
            width: 190px; /* Resize Width */
            display: block;
            text-align: left;
            background: url('bg_select.png') right;
            cursor: pointer;
            border: 1px solid #D1E4F6;
            color: #333;
        }
    </style>
    <link href="../../SpryAssets/SpryValidationPassword.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        <!--
        body {
            background-color: #CCC;
        }

        -->
    </style>
</head>
<body onload="init();">
<div style="position:absolute; left: 219px; top: 41px;">
    <div class="form_header">User Password Change</div>
    <div class="container" style="position:relative;">
        <div id="Note" class="noteDivClass">Note : Fields marked with <span style="color:#FF0000">*</span> are
            mandatory.
        </div>
        <form name="PasswordChange" action="" method="post">
            <div style="font-size:12px;color:#F00"><%=ERROR%>
            </div>
            <input type="hidden" name="txt1" id="txt1"/>
            <div class="col_half" style="width:95%;">
                <fieldset>
                    <div class="row">
                        <span class="label">User ID :</span>
                        <span class="label" style="width:200px;text-align:left;padding-left:15px;color:#060">
          <%=user.getV_usrid()%>
          </span>
                    </div>

                    <div class="row"><span class="label">New Password <span style="color:#D50000">*</span> :</span>
                        <span class="txt_cont">
         <input name="NewPassword" type="password" class="inputBox" id="password" size="20" maxlength="15"
                tabindex="2"/>
          </span></div>
                    <div class="row"><span class="label">Repeat New Password <span
                            style="color:#D50000">*</span> :</span> <span class="txt_cont">
          <input name="ReNewPassword" type="password" class="inputBox" id="password" size="20" maxlength="15"
                 tabindex="3"/>
          </span></div>
                </fieldset>

                <div class="but_container">
                    <input type="button" name="B1" id="B1" value="Change Password" onclick="pageSubmit('Password')"
                           class="button"/>
                    <input type="reset" name="B2" id="B2" value="Reset" onclick="pageSubmit('Delete')" class="button"/>
                    <input type="button" name="B3" id="B3" value="Close" onclick="pageSubmit('Close')" class="button"/>
                </div>
            </div>

            <div id="dialog" style="display:none;" title="${CompanyTitle}">
                <p><span class="ui-icon ui-icon-info" style="float:left; margin:0 7px 0 0;"></span></p>
                <p id="dialog-email" class="textGrey"></p>
            </div>
        </form>
        <div class="spacer"> &nbsp;</div>
    </div>
</div>
</body>
</html>
