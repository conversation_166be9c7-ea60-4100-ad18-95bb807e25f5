<%--
    Document   : password_change_result
    Created on : Jan 31, 2011, 3:33:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@include file="/common/ValidateUser.jsp" %>
<jsp:useBean id="UserManagerBean" class="com.misyn.mcms.admin.UserManager" scope="session"/>

<%
    int usrcode = user.getN_usrcode();
    String oldpassword = request.getParameter("Password");
    String password = request.getParameter("NewPassword");
    String complexWord = request.getParameter("txt1");
    long timeURL = 0;
    String message = "";
    if (complexWord.equals("Too short")) {
        message = "Password strength is to very too short";
        response.sendRedirect(response.encodeRedirectURL("password_change.jsp?ERROR=" + message));
        return;
    } else if (complexWord.equals("Weak")) {
        message = "password Strength is too Weak";
        response.sendRedirect(response.encodeRedirectURL("password_change.jsp?ERROR=" + message));
        return;
    }

    try {
        password = password.trim();
    } catch (Exception e) {
    }


    boolean result = UserManagerBean.changeUserPassword(usrcode, oldpassword, password, user.getV_usrid(), request.getRemoteAddr());


    user = UserManagerBean.getInstance().getUserList("AND u.n_usrcode=" + usrcode).get(0);
    user.setIpAddress(request.getRemoteAddr());
    user.setSessionID(session.getId().trim());
    user.setN_prgid(1);

    session.removeAttribute("G_USER");
    session.setAttribute("G_USER", user);

    String URL = "password_change.jsp?" + timeURL;
            /*if(user.getN_accessusrtype()==4
                    ||user.getN_accessusrtype()==7
                    ||user.getN_accessusrtype()==1) //INTERNAL USER MAIN PAGE
            {
                URL = "/main_internal.jsp?" + timeURL;
            }
            else if(user.getN_accessusrtype()==3
                    ||user.getN_accessusrtype()==6
                    ||user.getN_accessusrtype()==2) //AGENT MAIN PAGE
            {
                URL = "/main_agent.jsp?" + timeURL;
            }
            else if(user.getN_accessusrtype()==0
                    ||user.getN_accessusrtype()==5) //External MAIN PAGE
            {
                URL = "/main_external.jsp?" + timeURL;
            }*/

    response.sendRedirect(URL + "&ERROR=" + UserManagerBean.getMsg());

%>

