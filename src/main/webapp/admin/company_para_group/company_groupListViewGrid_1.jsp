<%@page import="com.misyn.mcms.admin.Company_Group" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@page import="java.util.Iterator" %>
<%@page import="java.util.List" %>
<jsp:useBean id="Company_GroupManagerBean" class="com.misyn.mcms.admin.Company_Group_Manager" scope="application"/>

<%

    String tdClass1 = "class=\"tbl_row\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row';\"";
    String tdClass2 = "class=\"tbl_row2\" onmouseover=\"className='tbl_row_selected';\" onmouseout=\"className='tbl_row2';\"";

    int m_clickPos = 1;
    int m_pagecount = com.misyn.mcms.utility.Parameters.getInstance().getNoRecordPerPage();

    try {
        m_clickPos = Integer.parseInt(request.getParameter("P_Click_Pos"));
    } catch (Exception e) {
    }
    int m_start = 0;
    int m_end = m_pagecount * m_clickPos;
    m_start = m_end - m_pagecount;


    //---------------------------
    int TYPE = 0;
    long timeURL = System.currentTimeMillis();
    String URL = "company_group.jsp?" + timeURL;


    List<Company_Group> list = null;

    try {
        list = Company_GroupManagerBean.getCompany_GroupViewList(user,
                "",
                "ORDER BY t1.n_comid,t1.n_group_id LIMIT " + m_start + "," + m_pagecount);

    } catch (Exception e) {
    }
    //----------------------------

%>

<table width="100%" cellpadding="0" cellspacing="1" bgcolor="#999999">
    <tr>
        <td class="tbl_row_header">No.</td>
        <td class="tbl_row_header"></td>
        <td class="tbl_row_header">Group ID</td>
        <td class="tbl_row_header">Group Name</td>
        <td class="tbl_row_header">Company</td>
    </tr>
    <%
        int cnt = 1;

        if (list != null) {
            Iterator<Company_Group> it = list.iterator();
            while (it.hasNext()) {
                request.setAttribute("Company_GroupBean", it.next());

    %>
    <jsp:useBean id="Company_GroupBean" scope="request" type="com.misyn.mcms.admin.Company_Group"/>

    <%
        if ((cnt % 2) == 0) {
    %>
    <tr onClick="window.location='<%=URL%>&P_N_COMID=<%=Company_GroupBean.getN_comid()%>&P_N_GROUPID=<%=Company_GroupBean.getN_group_id()%>&P_ISNEWRECORD=false'"  <%=tdClass1%> >
        <td>&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            -
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="Company_GroupBean" property="n_group_id"/>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="Company_GroupBean" property="v_groupName"/>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="Company_GroupBean" property="v_comCode"/>
        </td>

    </tr>
    <%
    } else {
    %>
    <tr onClick="window.location='<%=URL%>&P_N_COMID=<%=Company_GroupBean.getN_comid()%>&P_N_GROUPID=<%=Company_GroupBean.getN_group_id()%>&P_ISNEWRECORD=false'"  <%=tdClass2%> >
        <td>&nbsp;<%=(cnt + m_start)%>
        </td>
        <td>&nbsp;&nbsp;
            -
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="Company_GroupBean" property="n_group_id"/>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="Company_GroupBean" property="v_groupName"/>
        </td>
        <td>&nbsp;&nbsp;
            <jsp:getProperty name="Company_GroupBean" property="v_comCode"/>
        </td>

    </tr>
    <% }
        cnt++;
    }
    }
    %>
</table>
