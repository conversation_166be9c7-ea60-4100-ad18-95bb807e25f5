<%--
    Document   : rolePrivDelete
    Created on : Dec 18, 2010, 7:35:34 PM
    Product    : Intranet - UA Intranet & Common Auth. System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.admin.Company_Group" %>
<%@page import="java.util.ArrayList" %>
<%@include file="/common/ValidateUser.jsp" %>
<%@page import="java.util.List" %>
<jsp:useBean id="CompanyGroupParameterManager" class="com.misyn.mcms.admin.Company_Group_Manager" scope="application"/>
<%@page contentType="text/html" pageEncoding="UTF-8" %>


<%
    Company_Group m_Company_Group = null;
    List<Company_Group> company_GroupList = new ArrayList<Company_Group>();
    String chkvalue = "";


    int endPos = 0;
    try {
        endPos = Integer.parseInt(request.getParameter("P_END_POS"));
    } catch (Exception e) {
    }

    //out.print("End Pos : "+endPos);

    for (int x = 0; x < endPos; x++) {
        chkvalue = request.getParameter("chkboxDelete" + x);
        if (chkvalue == null) chkvalue = "";
        if (chkvalue.equalsIgnoreCase("checked")) {
            m_Company_Group = new Company_Group();

            try {
                m_Company_Group.setN_comid(Integer.parseInt(request.getParameter("P_N_COMID" + x)));
            } catch (Exception e) {
            }
            try {
                m_Company_Group.setN_group_id(Integer.parseInt(request.getParameter("P_N_GROUP_ID" + x)));
            } catch (Exception e) {
            }

            company_GroupList.add(m_Company_Group);
            // out.print("User type "+rolePrivilege.getN_usrtype() +" </br>");

        }
    }


    //================Start Delete only one recode=================
    int TYPE = 0;
    try {
        TYPE = Integer.parseInt(request.getParameter("P_TYPE"));
    } catch (Exception e) {
    }
    if (TYPE == 1) {

        m_Company_Group = new Company_Group();

        try {
            m_Company_Group.setN_comid(Integer.parseInt(request.getParameter("txtN_comid")));
        } catch (Exception e) {
        }
        try {
            m_Company_Group.setN_group_id(Integer.parseInt(request.getParameter("txtN_group_id")));
        } catch (Exception e) {
        }

        company_GroupList.add(m_Company_Group);
    }
    //================End Delete only one recode=================
    out.print("Size " + company_GroupList.size());
    if (company_GroupList.size() > 0) {
        CompanyGroupParameterManager.deleteCompany_GroupParameter(company_GroupList);
    }
    response.sendRedirect("company_groupList.jsp?P_ERROR=" + CompanyGroupParameterManager.getMsg());
%>


