<%--
    Document   : LoginValidate
    Product    : Intranet - UA Intranet & Common Auth. System.
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Created on : Nov 14, 2010, 11:14:55 AM
    Author     : <PERSON><PERSON>
--%>


<%@page import="com.misyn.mcms.admin.User" %>
<jsp:useBean id="LoginManagerBean" class="com.misyn.mcms.admin.LoginManager" scope="application"/>
<jsp:useBean id="UtilityBean" class="com.misyn.mcms.utility.Utility" scope="application"/>
<%@page import="com.misyn.mcms.admin.UserPasswordPara" %>
<%@ page import="com.misyn.mcms.claim.dto.UserDto" %>
<%
    String systemDate = null;
    String HOME_PAGE = "";
    int PAGE_TIMEOUT = 0;
    User user = null;
    UserPasswordPara m_userPasswordPara = null;
    try {
        systemDate = UtilityBean.sysDate();
        user = (User) session.getAttribute("G_USER");
        m_userPasswordPara = (UserPasswordPara) session.getAttribute("G_USER_PASSWORD_PARAMETER");
        HOME_PAGE =m_userPasswordPara.getV_home_page_url();
        PAGE_TIMEOUT = m_userPasswordPara.getN_timeout();
    } catch (Exception e) {
        e.printStackTrace();
    }



%>
