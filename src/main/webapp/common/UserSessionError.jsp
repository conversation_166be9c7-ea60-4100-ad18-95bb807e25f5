<html>
<head>
    <title>${CompanyTitle} - SESSION ERROR</title>

    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="/common/Anchor.css" type="text/css">
    <link href="/common/main.css" rel="stylesheet" type="text/css">
    <SCRIPT LANGUAGE="JavaScript" type="text/javascript" SRC="/common/disable.js"></SCRIPT>

    <script type="text/javascript">
        function logOut() {
            var loginType = 2;
            try {
                loginType = parent.document.getElementById("LOGINTYPE").value;
            } catch (e) {
            }
            parent.location.href = "/Logout.jsp?P_LOGIN_TYPE=" + loginType;

        }
    </script>

    <style type="text/css">
        <!--
        .style1 {
            color: #333333;
            font-weight: bold;
            }

        .style2 {
            color: #FF6600;
            font-weight: bold;
            }

        .style3 {
            color: #000066
            }

        .urllink {
            color: #000066;
            font-weight: bold;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 13px;
            text-decoration: none;
            cursor: pointer;
            }

        .urllink a {
            color: #000066;
            font-weight: bold;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 13px;
            text-decoration: none;
            cursor: pointer;
            }

        .urllink_sel {
            color: #FF6633;
            font-weight: bold;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 13px;
            text-decoration: none;
            cursor: pointer;
            }

        -->
    </style>
</head>
<body bgcolor="#FFFFFF">
<form name="Form1" method="post" action="">
    <table width="100%">
        <tr>
            <td colspan="3" height="30"></td>
        </tr>
        <tr>
            <td height="30">&nbsp;</td>
            <td class="table2" valign="middle" align="center"><img src="../image/LOGOwhite.png"/></td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td colspan="3" height="40"></td>
        </tr>
        <tr>
            <td height="10"></td>
            <td width="60%">
                <table width="100%" class="BdrThin">
                    <tr>
                        <td height="30">
                            <div align="center">
                                <p class="style1">S E S S I O N &nbsp;&nbsp;&nbsp;E R R O R</p>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="center">
                            <span class="style2" style="color:#093"><font face="Arial" size="3">Your session has expired, </font></span>
                        </td>
                    </tr>
                    <tr>
                        <td height="30" align="center">
                            <span class="style2" style="color:#093"><font face="Arial"
                                    size="3">please login agian.</font></span>
                        </td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td align="center">
                            <div class="urllink" onClick="logOut()" onMouseOver="this.className='urllink_sel';"
                                    onMouseOut="this.className='urllink';">Go
                                to Login Page
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                    </tr>
                </table>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td colspan="3" height="28"></td>
        </tr>
    </table>
</form>
<script type="text/javascript">
    try {
        parent.document.getElementById("cell1").style.display = "none";
        parent.document.getElementById("loading").style.display = "none";
    } catch (e) {

    }
</script>
</body>

</html>
