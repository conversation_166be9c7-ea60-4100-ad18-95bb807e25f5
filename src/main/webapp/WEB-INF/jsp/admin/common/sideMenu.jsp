<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery-1.4.2.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript">
        $(function () {
            $("#accordion").accordion({
                //icons: {header: "ui-icon-circle-arrow-e",headerSelected: "ui-icon-circle-arrow-s"},
                autoHeight: false,
                collapsible: true,
                active: 0,
                alwaysOpen: false
            });
        });
    </script>
    <title>${CompanyTitle}</title>
    <style>
        .hide {
            display: none;
        }
        .active-menu {
            background-color: #fbad19 !important;
            color: #fff !important;
        }
    </style>


</head>
<body style="background: #172554" class="scroll">
<div style="width:100%;">
    <c:set var="index" value="0"/>
    <c:set var="menuId" value="0"/>
    <c:set var="size" value="${userRightList.size()}"/>
    <div id="accordion" class="Sidenav">
        <c:forEach var="userRight" items="${userRightList}">
        <c:set var="url"
               value="${pageContext.request.contextPath}/submit.do?P_MENUID=${userRight.n_mnuid}&P_ITEMID=${userRight.n_itmid}&P_AUTHTYPE=I"/>
        <c:if test="${(menuId!=userRight.n_mnuid) }">
        <c:if test="${index>0}">
    </div>
    </c:if>
    <c:set var="menuId" value="${userRight.n_mnuid}"/>
    <p class="m-0"><a href="#"><i class="fa fa-edit p-2" style="padding-left: 5px!important;"></i>
        <span class="menutitle"> ${userRight.v_mnuname}</span><i class="fa fa-angle-down float-right"
                                                                 style="margin-top: 10px;"></i></a></p>
    <div>
        </c:if>
        <a href="${url}" target="${userRight.v_target}">${userRight.v_itmname}</a>
        <c:if test="${index==(size-1)}">
    </div>
    </c:if>
    <c:set var="index" value="${index+1}"/>

    </c:forEach>
</div>
<script type="">
    $('#accordion>div>a').click(function () {

        $('#accordion').find('.active').removeClass('active');
        $(this).addClass('active');
        $('.active').addClass('hide');
        $(window.parent.document.getElementById('navChange')).removeClass('nav-lg');
        $('#accordion').removeClass('navLg')
    });

    $(document).ready(function () {
        // Handle clicks on accordion headers (Main Menu)
        $('#accordion p.m-0').click(function () {
            // Remove highlight from all headers
            $('#accordion p.m-0').removeClass('active-menu');

            // Add highlight to the clicked header
            $(this).addClass('active-menu');
        });

        // Handle clicks on menu items inside accordion
        $('#accordion div a').click(function (e) {


            // Find the parent header and highlight it
            let parentHeader = $(this).closest('div').prev('p.m-0');

            // Remove active class from all headers
            $('#accordion p.m-0').removeClass('active-menu');

            // Add active class to the corresponding header
            parentHeader.addClass('active-menu');
        });
    });
</script>
</body>
</html>
