<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css?v1" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/css/common/login.css?v1" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css?v1" rel="stylesheet" type="text/css"/>

    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery-ui-1.8.2.custom.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript">
        var validList = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_-";
        $(document).ready(function () {

            // jQuery UI Dialog

            $('#dialog').dialog({
                autoOpen: false,
                width: 400,
                modal: true,
                bgiframe: false,
                resizable: false,
                //closeOnEscape: false ,
                //dialogClass: 'alert',
                //position: [400,200],
                //show: 'bounce',
                //dragStop: function(event, ui) { alert("drag"+ui); },
                buttons: {
                    "Ok": function () {
                        $(this).dialog("close");
                        //$(this).dialog({show: 'explode'});

                        if ($("input#username").val() == "") {
                            $("input#username").focus();
                            return;
                        }
                        else if ($("input#password").val() == "") {
                            $("input#password").focus();
                            return;
                        }
                        //document.testconfirmJQ.submit();
                    }
                }
            });

            $('form#testconfirmJQ').submit(function () {
                // $("p#dialog-email").html($("input#emailJQ2").val());
                //$('#dialog').dialog('open');
                return false;
            });

        });

        function msgbox(str) {

            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');

        }
    </script>
    <script Language="JavaScript">
        var validList = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_-";


        function check() {
            document.Login.B1.disabled = false;
            document.Login.B2.disabled = false;

            if (document.Login.username.value == "") {
                //alert("\"User ID\" cannot be blank.");
                msgbox("\"User ID\" cannot be blank.");
                document.Login.username.focus();
                return false;
            } else if (!validateText(document.Login.username.value)) {
                //alert("Invalid \"User ID\"");
                msgbox("Invalid \"User ID\"");
                document.Login.username.focus();
                return false;
            } else if (document.Login.password.value == "") {
                //alert("\"Password\" Cannot be blank");
                msgbox("\"Password\" Cannot be blank");
                document.Login.password.focus();
                return false;
            } else {


                document.Login.B1.disabled = true;
                document.Login.B2.disabled = true;
                return true;
            }
        }

        function validateText(str) {
            str = str.toUpperCase();
            for (var n = 0; n < str.length; n++) {
                if (validList.indexOf(str.charAt(n)) == -1)
                    return false;
            }
            return true;
        }


        function viewHelpPopup() {
            var screenWith = 700;
            var wihe = 'width=' + screenWith + ',height=' + (screen.availHeight - 85);
            window.open("passwordResetHelp.html", "helpwindow", "scrollbars=1,status=1,screenX=1,screenY=1,left=1,top=1," + wihe);
            //window.open("Login.jsp", "mywindow", "fullscreen=1," + wihe);

            //opener.parent.window.close();
            //window.opener='x';
            //window.close();
        }

        function init() {
            $("input#username").focus();
        }
    </script>

    <style>
        html {
            display: none;
        }
    </style>
    <script>
        if (self == top) {
            document.documentElement.style.display = 'block';
        } else {
            top.location = self.location;
        }
    </script>

    <title>${CompanyTitle}</title>
</head>
<body onload="init()" style="background: url('${pageContext.request.contextPath}/image/back.jpg') no-repeat top center fixed;  -webkit-background-size: cover;
        -moz-background-size: cover;
        -o-background-size: cover;
        background-size: cover;">
<!-- Content here -->
<header class="navbar navbar-expand navbar-light">

</header>
<div class="container">
    <form name="Login" id="Login" method="post" action="${pageContext.request.contextPath}/login.do"
          enctype="multipart/form-data" autocomplete="off" onSubmit="return check()">
        <input type="hidden" name="txtScreenHeight" id="txtScreenHeight"/>
        <input type="hidden" name="returnUrl" value="${RETURN_URL}"/>
        <input type="hidden" name="csrfToken" value="${csrfToken}"/>
        <div class="login-container">
            <div>
                <div class="card login-card">
                    <div class="card-header">
                        <img src="image/LOGOwhite.png" style="height: 40px; ">
                    </div>

                    <div class="card-body mb-2">

                        <p class="text-danger">${errorMessage}
                        </p>
                        <div class="form-group">
                            <label for="username"> User Name</label>
                            <input name="username" id="username" type="text" class="form-control" autocomplete="off"/>
                        </div>
                        <div class="form-group">
                            <label for="username"> Password</label>
                            <input name="password" type="password" class="form-control" id="password"
                                   autocomplete="off"/>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12 col-sm-12 col-lg-12 text-center">
                                <button name="B1" class="btn btn-primary col-md-6 col-lg-6 col-sm-12 " type="submit" value="Login">Login</button>
                                <%--<button name="B2" class="btn btn-primary " type="reset" value="Clear">Clear</button>--%>
                            </div>
                            <h6 class="col-md-12">
                                <small id="emailHelp" class="form-text text-muted float-right"></small>
                            </h6>
                        </div>
                        <div class="row mt-2">
                            <div class="col-lg-12 text-center">
                                <strong class="callcolor">Please Call for Assistance</strong>
                                <p class="text-danger">0115 880 880 </p>
                            </div>

                        </div>
                    </div>
                </div>
                <div id="dialog" title="${CompanyTitle}"><p><span class="ui-icon ui-icon-info"></span>
                    <p id="dialog-email" class="textGrey"></p>
                </div>
            </div>
        </div>
    </form>
    <div class="fixed-bottom p-1 ">
        <div class="footerNote text-center">
            <img src="resources/images/browsers.png" class="img-responsive" height="30px" style="width: auto"
                 title="This web site is best viewed using  Microsoft Edge Ver 40 & above and or Google Chrome Ver 65 & above and or Mozilla Firefox Ver 46 & above and or Safari ver 10.1.2 or above. You may experience compatibility issues when accessing through Mobile devices">
        </div>
        <div class="row text-center text-light">
            <div class="col-md-12">
                Copyright ©  2018 <a href="http://www.mi-synergy.com"
                                                                                        class="text-white"
                                                                                        target="_blank">M I Synergy
                (Pvt) Ltd </a>. All rights Reserved.
            </div>
        </div>
    </div>
</div>
</body>
</html>
