<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>${CompanyTitle}</title>
    <meta content="text/html; charset=utf-8" http-equiv=Content-Type>
    <meta name="viewport" content="user-scalable=no">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name=language content=en>
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/common/page-loader.js"></script>
    <meta name=GENERATOR content="MSHTML 8.00.6001.18999">
    <script type="text/javascript">
        var WIDTH = 0;
        var HEIGHT = 0;

        ///////////IFrame Size Change///////////////////
        function doIframe() {
            var screenW = 640;
            var screenH = 400;

            var winHeihgt = 0;

            if (parseInt(navigator.appVersion) > 3) {
                screenW = screen.width;
                screenH = screen.height;

                WIDTH = screenW;
                HEIGHT = screenH;
            }

            if (navigator.userAgent.indexOf("Firefox") > -1) {
                screenH = screenH - 280;

            } else if (navigator.userAgent.indexOf("Chrome") > -1) {
                screenH = screenH - 297;
            } else {//IE
                winHeihgt = window.screen.availHeight
                screenH = screenH - 340;
            }
            document.getElementById('imain_frm').height = screenH - 40 + 54 + 84;
            document.getElementById("cell1").style.display = "none";
            document.getElementById("loading").style.display = "none";

            try {
                opener.opener = 'as';
            }
            catch (e) {
            }
        }


        var addEvent = function (elem, type, eventHandle) {
            if (elem == null || elem == undefined) return;
            if (elem.addEventListener) {
                elem.addEventListener(type, eventHandle, false);
            } else if (elem.attachEvent) {
                elem.attachEvent("on" + type, eventHandle);
            }
        };
        addEvent(window, "resize", function () {

            var winW = 630, winH = 460;

            if (parseInt(navigator.appVersion) > 3) {
                if (navigator.appName == "Netscape") {
                    winW = window.innerWidth;
                    winH = window.innerHeight;
                }
                if (navigator.appName.indexOf("Microsoft") != -1) {
                    winW = document.body.offsetWidth;
                    winH = document.body.offsetHeight;
                }
            }

        });


        var notificationTimer;
        var isNotificationTimerOn = 0;
        var isShowNotify = false;
        var currentNotificationCount = 0;
        var previousNotificationCount = 0;

        function showNotification() {
            document.getElementById("DIV_NOTIFICATION").style.right = 8;
            document.getElementById("DIV_NOTIFICATION").style.top = 50;
            document.getElementById("DIV_NOTIFICATION").style.bottom = 8;

            if (!isShowNotify) {
                isShowNotify = true;
                document.getElementById("DIV_NOTIFICATION").style.display = 'block';
                $("#DIV_NOTIFICATION").animate({width: (450)}, "slow");
            } else {
                isShowNotify = false;
                $("#DIV_NOTIFICATION").animate({width: (0)}, "slow");
                isNotificationTimerOn = 0;
                $("#DIV_NOTIFICATION").hide(100);
            }
        }

        function decrementNotification() {
            var unReadCount = $("#unreadNotificationCount").val();
            if (unReadCount > 0) {
                $("#spanNotificationCount").text(unReadCount - 1);
            }

        }

        function doNotificationTimer() {
            if (!isNotificationTimerOn) {
                isNotificationTimerOn = 1;
                showNotification();
            } else {
                clearTimeout(notificationTimer);
                isNotificationTimerOn = 0;
            }
        }

        function refreshNotificationPanel() {
            var TimeURL = new Date().getTime();
            $.ajax({
                url: '${pageContext.request.contextPath}/NotificationController/viewNotification?' + TimeURL,
                cache: false,
                success: function (data) {
                    $('#DIV_NOTIFICATION').html(data);
                    currentNotificationCount = $("#notificationCount").val();
                    $("#spanNotificationCount").text($("#unreadNotificationCount").val());
                    if (currentNotificationCount > previousNotificationCount) {
                        //  doNotificationTimer();
                    }
                    previousNotificationCount = currentNotificationCount;
                    // notificationTimer = setTimeout("callNotificationTimer()", 300000);
                }
            });
        }

        function callNotificationTimer() {
            var TimeURL = new Date().getTime();
            $.ajax({
                url: '${pageContext.request.contextPath}/NotificationController/viewNotification?' + TimeURL,
                cache: false,
                success: function (data) {
                    $('#DIV_NOTIFICATION').html(data);
                    currentNotificationCount = $("#notificationCount").val();
                    $("#spanNotificationCount").text($("#unreadNotificationCount").val());
                    if (currentNotificationCount > previousNotificationCount) {
                        doNotificationTimer();
                    }
                    previousNotificationCount = currentNotificationCount;
                    notificationTimer = setTimeout("callNotificationTimer()", 300000);
                }
            });
        }

        window.onbeforeunload = function () {
            return 'Close claim window!';
        }
    </script>
</head>
<body onLoad=" hideLoader();doIframe();callNotificationTimer();" id="navChange" class="">
<div id="loading" class="lodingImage"
     style="position:absolute;top:50%; transform:translateY(-50%) ; z-index:10000;     text-align: center; width: 100%; color: #333333;">
    <div>
        <img alt="Loading..." src="${pageContext.request.contextPath}/image/common/ajax-loader6.gif" border=0></div>
    <div class="labelText mt-3">
        <h5 class="lodingText">RECORD IS IN PROGRESS ...</h5>
        <h6>Don't close this browser window or navigate until the progress is complete</h6>
    </div>
</div>
<div id="cell1" class="innerDivBg"
     style="z-index:1000; height:100vh; padding-left:0px; width:100%; position: fixed; top: 0; left: 0; background: rgba(255, 255, 255, 0.87);"></div>
<div id="DIV_NOTIFICATION" class="notification"></div>
<div id="topmenu">
    <nav class="navbar navbar-expand-sm navbar-light bg-light py-0" style="background-color: white!important;    border-bottom: 2px solid #fbad19;">
        <a class="navbar-brand p-0" href="#">
            <img src="image/logo.png" alt="logo" style="width: 134px; height: 48px">
            <div id="aviva_logo"></div>
        </a>
        <a href="javascript:void(0)" class="anglrRight text-dark" id="anglrRight" onclick="showHideNav()"><i
                class="fa fa-bars"></i></a>
        <div class="navbar-collapse text-right" id="navbarNav">
            <ul class="navbar-nav ml-auto">
            </ul>
            <div class="navi-menu" style="text-align: left">
                <h5 style="margin: 0!important;">MiSyn MCMS</h5>
                <span id="navigationMenuDetails"></span>
            </div>
            <div class="ml-auto mr-3">
                <a href="#" onClick="showNotification();refreshNotificationPanel();" class="mr-3"
                   style="position: relative;"><i class="fa fa-bell"
                                                  style="font-size: 20px;"></i><span
                        class="badge badge-pill badge-danger" style="position: absolute;top: -12px;left: 15px;"
                        id="spanNotificationCount"></span></a></a>


            </div>
            <div>
                <div class="btn-group">
                    <div id="DIV_AS400_STATUS"
                         style="position: absolute; right: -3px; top: -3px; font-size: 14px;font-weight: bold;z-index: 10001;text-align:right;">
                    </div>
                    <h6 class="mr-2 mt-2 d-none d-sm-block">${G_USER.firstName}
                    <c:out value=" "/>
                    ${G_USER.firstName}
                    </h6>
                    <a class="dropdown-toggle userPic" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true"
                       aria-expanded="false"></a>
                    <div class="dropdown-menu dropdown-menu-right" style="right: 0; left: auto;" id="dropdownMenu">
                        <h6 class="mr-2 mt-1 d-sm-none dropdown-item bg-warning"><i
                                class="fa fa-user"></i> ${G_USER.lastName} ${G_USER.lastName}
                        </h6>
                        <div class="dropdown-divider d-sm-none"></div>
                        <a href="${pageContext.request.contextPath}/welcome.do" target="imain_frm"
                           class="dropdown-item"><span>Home</span></a>
                        <a href="${pageContext.request.contextPath}/changepsw.do" target="imain_frm"
                           class="dropdown-item"><span>Change password</span></a>
                        <%--<a href="${pageContext.request.contextPath}/FAQ/FAQ_CALL_CENTER.html" target="new" class="dropdown-item"><span>FAQ</span></a>--%>
                        <div class="dropdown-divider"></div>
                        <a href="${pageContext.request.contextPath}/logout" target="_parent"
                           class="dropdown-item"><span>Log out</span></a>
                    </div>
                </div>
            </div>
        </div>
    </nav>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="left-menu" id="Sidenav">
            <div style="width: 280px;">
                <%--<a href="javascript:void(0)" class="pinbtn text-light p-2" onclick="pinNav()"><i class="fa fa-map-pin"></i> </a>--%>
                <div class=content>
                    <%-- --%>
                    <div>
                    </div>
                    <iframe id="menu" frameborder="0" src="${pageContext.request.contextPath}/sidemenu.do" width="100%">
                        <p>Your browser does not support iframes.</p>
                    </iframe>
                </div>
            </div>
        </div>
        <div id="mainContent" class="right-content">
            <div class="ui-layout-center">
                <div class="ui-layout-content">
                    <div style="">
                        <%--<c:choose>
                            <c:when test="${RETURN_URL!=''}">
                                <iframe id="imain_frm" name="imain_frm" frameborder="0"
                                        src="${pageContext.request.contextPath}/${RETURN_URL}" width="100px"></iframe>
                            </c:when>
                            <c:otherwise>
                                <iframe id="imain_frm" name="imain_frm" frameborder="0"
                                        src="${pageContext.request.contextPath}/welcome.do" width="100px"></iframe>
                            </c:otherwise>
                        </c:choose>--%>

                            <iframe id="imain_frm" name="imain_frm" frameborder="0"
                                    src="${pageContext.request.contextPath}/welcome.do" width="100px"></iframe>
                    </div>
                </div>
                <div class="footer text-white hide-sm"
                     style="vertical-align:bottom;padding-bottom:0px;font-family:Arial, Helvetica, sans-serif;float:left;width:100%; height: 28px; background: #ffffff;">
                    <div class="text-center pt-1 " style="color: #7C7C7C;">
                        Copyright &copy; 2024 <a
                            href="http://www.mi-synergy.com" target="_blank">M I Synergy (Pvt)
                        Ltd </a>. All rights Reserved.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

    var body = $('body');

    function showHideNav() {
        body.toggleClass('nav-lg nav-pin');
//        body.toggleClass();
        if (body.hasClass('nav-lg')) {
            $("#menu").contents().find('#accordion').addClass('navLg');
        }
        else {
            $("#menu").contents().find('#accordion').removeClass('navLg');
        }
    }

    $("#Sidenav").mouseenter(function () {
        $("body").addClass('nav-lg');
        $("#menu").contents().find('#accordion').addClass('navLg');
    }).mouseout(function () {
        if (body.hasClass('nav-pin')) {
            body.addClass('nav-lg');
            $("#menu").contents().find('#accordion').addClass('navLg');
        }
        else {
            body.removeClass('nav-lg');
            $("#menu").contents().find('#accordion').removeClass('navLg');
        }
    });

    $("#dropdownMenu2").click(function () {
        $("#dropdownMenu").toggle();
    });
</script>
</body>
</html>

