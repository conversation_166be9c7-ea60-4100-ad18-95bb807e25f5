<%--
  Created by IntelliJ IDEA.
  User: HP
  Date: 6/19/2024
  Time: 11:40 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
</head>
<body style="padding: 10px" onload="hideLoader()">

<div id="accordion" class="accordion">
    <div class="card">
        <div class="card-header" id="headingOne">
            <h5 class="mb-0">
                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                   aria-expanded="true" aria-controls="collapseOne">
                    Search Here <i class="fa fa-search"></i>
                </a>
            </h5>
        </div>
        <div id="collapseOne" class="collapse show p-3" aria-labelledby="headingOne"
             data-parent="#accordion">

            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                            data-target="#addModal" id="addBtn"><i class="fa fa-plus"></i> Add Reserve Adjustment
                    </button>
                </div>
            </div>


            <div class="row mt-3">
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Category</label>
                        <div class="col-sm-8">
                            <select id="categoryIdSelectionSearch" name="categoryId" class="form-control form-select form-select-sm w-30" required>
                            </select>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" id="searchBtn">Search</button>
                    <button type="button" class="btn btn-secondary float-right mr-1" id="clearBtn">Clear</button>
                </div>
            </div>

            <!-- Update Modal -->
            <div class="modal fade overflow-scroll" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editModalLabel">Edit Reserve Adjustment</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editForm">
                                <div class="form-group">
                                    <label>Category</label>
                                    <input type="text" class="form-control"
                                           id="categoryId"
                                           name="categoryId"
                                           readonly
                                    >
                                </div>

                                <div class="form-group">
                                    <label>Min. Amount</label>
                                    <input type="text" class="form-control"
                                           id="amountMin"
                                           name="amountMin"
                                           placeholder="Min. Amount"
                                           oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                           required
                                           readonly
                                    >
                                </div>

                                <div class="form-group">
                                    <label>Max. Amount</label>
                                    <input type="text" class="form-control"
                                           id="amountMax"
                                           name="amountMax"
                                           placeholder="Max. Amount"
                                           oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                           required
                                           readonly
                                    >
                                </div>
                            </form>
                            <form id="periodForm">
                            </form>

                            <button type="button" class="btn btn-primary float-right" id="updateButton">
                                Update
                            </button>
                            <button type="button" class="btn btn-secondary float-right mr-1" id="cancelButtonEditModal">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Modal -->
            <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addModalLabel">Add Reserve Adjustment Policy</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="addForm">
                                <div class="form-group">
                                    <label>Category</label>
                                    <select id="categoryIdSelection" name="categoryId" class="form-control form-select form-select-sm w-30" required>

                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>Period</label>
                                    <select id="periodIdSelection" name="periodId" class="form-control form-select form-select-sm w-30" required>

                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>Amount</label>
                                    <input type="text" class="form-control"
                                           id="amount"
                                           name="amount"
                                           placeholder="Amount"
                                           required
                                    >
                                </div>
                                <div class="form-group">
                                    <div style="display: flex;gap: 10px">
                                        <div style="margin-top: 3px">
                                            <input class="form-control"
                                                   type="checkbox"
                                                   id="recordStatusInAddForm"
                                                   value="A"
                                                   checked
                                            >
                                        </div>
                                        <div>
                                            <label for="recordStatus">Record Status</label>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveButton">Save</button>
                                <button type="button" class="btn btn-secondary float-right mr-1" id="cancelButtonAddModal">
                                    Cancel
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card mt-3">
        <div class="card-body table-bg">
            <div class="row">
                <div class="col-lg-12 pl-0 pr-0">
                    <table id="example" class="table table-sm table-striped table-bordered" style="width:100%">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Category</th>
                            <th>1 Month</th>
                            <th>2 Month</th>
                            <th>3 Month</th>
                            <th>4 Month</th>
                            <th>5 Month</th>
                            <th>6 Month</th>
                            <th>Above 06 Months</th>
                            <th>More than one year</th>
                            <th>Min. Amount</th>
                            <th>Max. Amount</th>
                            <th>Edit</th>
                        </tr>
                        </thead>
                        <tbody id="tableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    let isSearch = 0;

    $(document).ready(function () {

        fetchAllClaimReserveAdjustmentDetails();
        setSearchCategoryOptions();

        $('#example').on('click', '.edit-btn', function () {
            const currentRow = $(this).closest('tr');
            const taskName = currentRow.find('td:eq(4)').text();
            $('#editModal #taskName').val(taskName);
        });

        $('#periodForm').on('change', '.recordStatus', function () {
            const isChecked = $(this).is(':checked');
            const amountInput = $(this).closest('.form-group').parent().find('.amount');

            if (!isChecked) {
                amountInput.val('');
            }
        });


        // Save button click event
        $('#saveButton').click(function () {
            if (validateForm()) {
                const jsonData = handleAddFormData();

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/ClaimReserveAdjustmentController/save',
                    data: jsonData,
                    success: function (response) {
                        if (typeof response === 'string' && response?.trim() === '"SUCCESS"') {
                            notify('Claim exceptional case details saved successfully.', "success");
                            $('#addModal').modal('hide');
                            fetchAllClaimReserveAdjustmentDetails();
                        } else {
                            notify(response.message, "danger");
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        notify('An error occurred while saving claim exceptional case details: ' + error, "danger");
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }

        });


        // Update button click event
        $('#updateButton').click(function () {
            removeRequiredValidationInEditModal()
            if (validateEditForm() && validatePeriodForm()) {
                var jsonData = handleUpdateFormData();
                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/ClaimReserveAdjustmentController/save',
                    data: jsonData,
                    success: function (response) {
                        if (typeof response === 'string' && response?.trim() === '"SUCCESS"') {
                            notify('Claim reserver adjustment details updated successfully.', "success");
                            $('#editModal').modal('hide');
                            fetchAllClaimReserveAdjustmentDetails();
                        } else {
                            notify(response.message, "danger");
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        alert('An error occurred while updating reserve adjustment details: ' + error);
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }
        });
    });

    function removeRequiredValidationInEditModal() {
        $('#periodForm .adjustment-row').each(function () {
            const amount = $(this).find('#amount');
            const recordStatus = $(this).find('.recordStatus').is(':checked') ? 'A' : 'D';
            console.log(recordStatus)

            if (recordStatus === "D") {
                console.log("ready to remove")
                amount.removeAttr("required");
                console.log("removed")
            } else {
                console.log("ready to add")
                amount.attr("required", "required");
            }
        });
    }
    
    function handleUpdateFormData() {
        const dataToSend = [];

        $('#periodForm .adjustment-row').each(function () {
            const recordStatus = $(this).find('.recordStatus').is(':checked') ? 'A' : 'D';
            let periodId = $(this).find('#periodId').val();
            let categoryId = $(this).find('#categoryId').val();
            let claimReserveAdjustmentId = $(this).find('#claimReserveAdjustmentId').val();
            const amount = $(this).find('#amount').val();

            periodId = parseInt(periodId);
            categoryId = parseInt(categoryId);
            claimReserveAdjustmentId = parseInt(claimReserveAdjustmentId);

                const item = {
                    claimReserveAdjustmentId: claimReserveAdjustmentId,
                    periodId: periodId,
                    categoryId: categoryId,
                    amount: amount,
                    recordStatus: recordStatus
                };

                dataToSend.push(item);
        });

        return JSON.stringify(dataToSend);
    }

    function handleAddFormData() {
        var rawArray = $('#addForm').serializeArray();
        let recordStatus = $('#recordStatusInAddForm').is(':checked') ? 'A' : 'D';

        // Convert to object
        let formObject = {};
        rawArray.forEach(function(field) {
            formObject[field.name] = field.value;
        });

        formObject.recordStatus = recordStatus;

        formObject.periodId = parseInt(formObject.periodId);
        formObject.categoryId = parseInt(formObject.categoryId);
        formObject.amountMin = parseFloat(formObject.amountMin);
        formObject.amountMax = parseFloat(formObject.amountMax);

        var jsonData = JSON.stringify([formObject]);

        return jsonData;
    }

    async function setDetailsInSearchAction(reserveAdjustmentId) {
        try {
            const response = await getReserveAdjustmentById(reserveAdjustmentId);
            const data = response?.data?.[0];

            if (!data) {
                throw new Error("No adjustment data found.");
            }

            addTableData(response)
        } catch (error) {
            console.error(error);
            alert('An error occurred while fetching reserve adjustment details: ' + (error?.responseText || error.message));
        }
    }

    function getReserveAdjustmentById(reserveAdjustmentId) {
        return $.ajax({
            type: 'GET',
            url: '${pageContext.request.contextPath}/ClaimReserveAdjustmentController/searchAll?categoryId=' + reserveAdjustmentId,
        });
    }

    async function setDetailsOnEditModal(reserveAdjustmentId) {
        try {
            const response = await getReserveAdjustmentById(reserveAdjustmentId);
            const data = response?.data?.[0];

            if (!data) {
                throw new Error("No adjustment data found.");
            }

            $('#categoryId').val(data.categoryLabel);
            $('#amountMin').val(data.amountMin ?? 0);
            $('#amountMax').val(data.amountMax ?? 0);
            $('#recordStatus').prop('checked', data?.periods?.[0]?.recordStatus === "A");

            generateAdjustmentRows(data);
            removeValidateClass();
        } catch (error) {
            console.error(error);
            alert('An error occurred while fetching reserve adjustment details: ' + (error?.responseText || error.message));
        }
    }

    function createAdjustmentRow(data) {
        const div = document.createElement('div');
        div.className = 'form-group adjustment-row';
        div.dataset.periodId = data.periodId;

        div.innerHTML = `
        <input type="hidden" name="claimReserveAdjustmentId" id="claimReserveAdjustmentId" class="claimReserveAdjustmentId">
        <input type="hidden" name="periodId" id="periodId" class="periodId">
        <input type="hidden" name="categoryId" id="categoryId" class="categoryId">

        <div class="form-group mb-2">
        <div style="margin-left: 20px">
        <div>
        <input class="form-check-input recordStatus" type="checkbox" name="recordStatus" value="A"  checked>
<!--<label class="switch">-->
<!--  <input class="form-check-input recordStatus" type="checkbox" name="recordStatus" value="A"  checked>-->
<!--  <span class="slider round"></span>-->
<!--</label>-->

        </div>
        <div>
        <label id="periodLabel" class="mb-0">Period</label>
        </div>
        </div>

        </div>

        <div class="form-group">
        <input type="text" class="form-control amount"
        id="amount"
        name="amount"
        placeholder="Amount"
        required>
        </div>
        `;

        return div;
    }

    function generateAdjustmentRows(data) {
        const form = document.getElementById('periodForm');

        form.innerHTML = '';

        for (let i = 0; i < data.periods.length; i++) {
            const row = createAdjustmentRow(data.periods[i]);

            const claimInput = row.querySelector('input[name="claimReserveAdjustmentId"]');
            const periodInput = row.querySelector('input[name="periodId"]');
            const categoryInput = row.querySelector('input[name="categoryId"]');
            const amountInput = row.querySelector('input[name="amount"]');
            const checkboxInput = row.querySelector('input[name="recordStatus"]');
            const label = row.querySelector('label[id="periodLabel"]');

            claimInput.value = data.periods[i].claimReserveAdjustmentId;
            periodInput.value = data.periods[i].periodId;
            categoryInput.value = data.periods[i].categoryId;
            amountInput.value = data.periods[i].amount ?? "0";
            checkboxInput.checked = data.periods[i].recordStatus === "A";
            label.textContent = data.periods[i].periodLabel;

            form.appendChild(row);
        }
    }

    function fetchAllClaimReserveAdjustmentDetails() {
        $.ajax({
            url: '${pageContext.request.contextPath}/ClaimReserveAdjustmentController/searchAll',
            method: 'POST',
            data: {
                id: $('#txtId').val(),
                claimNo: $('#txtClaimNo').val(),
                remark: $('#txtRemark').val()
            },
            success: function (data) {
                addTableData(data);
            },
            error: function (xhr, status, error) {
                console.error("AJAX Error:", status, error);
                alert("Failed to load data.");
            }
        });
    }

    function addTableData(data) {
        const tableBody = document.getElementById('tableBody');
        tableBody.innerHTML = '';

        data.data.forEach(item => {
            const row = document.createElement('tr');

            const tdCatId = document.createElement('td');
            tdCatId.textContent = item.categoryId ?? '-';
            row.appendChild(tdCatId);

            const tdLabel = document.createElement('td');
            tdLabel.textContent = item.categoryLabel ?? '-';
            row.appendChild(tdLabel);

            for (let i = 0; i < item.periods.length; i++) {
                const td = document.createElement('td');
                const amount = item.periods?.[i]?.amount;
                if (amount === "" || amount === "0") {
                    td.textContent = "-";
                } else if (amount === "Closed" || amount === "close") {
                    td.textContent = "Closed";
                } else {
                    td.textContent = amount + "%";
                }
                row.appendChild(td);
            }

            const tdMinAmount = document.createElement('td');
            tdMinAmount.textContent = item.amountMin == null ? '-':item.amountMin;
            row.appendChild(tdMinAmount);

            const tdMaxAmount = document.createElement('td');
            tdMaxAmount.textContent =item.amountMax == null ? '-':item.amountMax;
            row.appendChild(tdMaxAmount);

            const tdEditButton = document.createElement('td');
            const button = document.createElement('button');

            button.className = "btn btn-primary btn-sm edit-btn";
            button.id = 'editBtn';
            button.setAttribute('data-toggle', "modal");
            button.setAttribute('data-target', "#editModal");
            button.setAttribute('data-id', item.categoryId);
            button.innerHTML = '<i class="fa fa-edit"></i>';

            button.addEventListener('click', function () {
                setDetailsOnEditModal(item.categoryId);
            });

            tdEditButton.appendChild(button);
            row.appendChild(tdEditButton);

            tableBody.appendChild(row);
        });
    }

    function formatDate(dateString) {
        let date = new Date(dateString);
        let formattedDate = date.getFullYear() + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + ('0' + date.getDate()).slice(-2);
        let formattedTime = ('0' + date.getHours()).slice(-2) + ':' + ('0' + date.getMinutes()).slice(-2) + ':' + ('0' + date.getSeconds()).slice(-2);
        return formattedDate + ' ' + formattedTime;
    }

    $('#searchBtn').click(function () {
        const categoryId = document.getElementById("categoryIdSelectionSearch").value;
        setDetailsInSearchAction(categoryId);
    });

    $('#clearBtn').click(function () {
        $('#categoryIdSelectionSearch').val('');

        fetchAllClaimReserveAdjustmentDetails();
    });

    $('#categoryIdSelection').on('change',async function () {
        const categoryId = document.getElementById("categoryIdSelection").value;
        const periodId = document.getElementById("periodIdSelection").value;
        try {
            if (categoryId != null && periodId != null) {
                const res = await fetchCheckActiveAdjustment(categoryId, periodId)
                if (res.exists){
                    $('#categoryIdSelection').val('');
                    $('#periodIdSelection').val('');
                    notify("This record already exists", "danger")
                }
            }
        } catch (e) {
            notify("Error Occurred in fetch active adjustment", "danger")
        }
    });

    $('#periodIdSelection').on('change', async function () {
        const categoryId = document.getElementById("categoryIdSelection").value;
        const periodId = document.getElementById("periodIdSelection").value;

        try {
            if (categoryId != null && periodId != null) {
                const res = await fetchCheckActiveAdjustment(categoryId, periodId);

                if (res.exists) {
                    $('#categoryIdSelection').val('');
                    $('#periodIdSelection').val('');
                    notify("This record already exists", "danger");
                }
            }
        } catch (e) {
            notify("Error Occurred in fetch active adjustment", "danger");
        }
    });

    $('#addBtn').click(function () {
        setCategoryOptions()
        setPeriodOptions();

        $('#categoryId').val('');
        $('#periodId').val('');
        $('#amount').val('');
        $('#amountMin').val('');
        $('#amountMax').val('');
        $('#saveButton').show();
        $('#addModalLabel').text("Add Reserve Adjustment Policy");

        removeValidateClass();
    });

    $('#cancelButtonEditModal').click(function () {
        $('#editModal').modal('hide');
        removeValidateClass();
    });

    $('#cancelButtonAddModal').click(function () {
        $('#addModal').modal('hide');
        removeValidateClass();
    });

    async function setCategoryOptions() {
        try {
            const response = await fetchReserveCategories();
            if (!response) {
                throw new Error("No adjustment category data found.");
            }

            const categorySelect = document.getElementById("categoryIdSelection");
            categorySelect.innerHTML = '<option value="" selected disabled>---- Select a Category ----</option>'

            response.forEach(function (optData) {
                const option = document.createElement("option");
                option.value = optData.categoryId;
                option.text = optData.description;

                categorySelect.appendChild(option);
            });
        } catch (error) {
            console.error(error);
            alert('An error occurred while fetching reserve adjustment category details: ' + (error?.responseText || error.message));
        }
    }

    async function setSearchCategoryOptions() {
        try {
            const response = await fetchReserveCategories();
            if (!response) {
                throw new Error("No adjustment category data found.");
            }

            const categorySelectSearch = document.getElementById("categoryIdSelectionSearch");
            categorySelectSearch.innerHTML = '<option value="" selected disabled>---- Select a Category ----</option>'

            response.forEach(function (optData) {
                const option = document.createElement("option");
                option.value = optData.categoryId;
                option.text = optData.description;

                categorySelectSearch.appendChild(option);
            });
        } catch (error) {
            console.error(error);
            alert('An error occurred while fetching reserve adjustment category details: ' + (error?.responseText || error.message));
        }
    }

    async function setPeriodOptions() {
        try {
            const response = await fetchReservePeriods();
            if (!response) {
                throw new Error("No adjustment data found.");
            }

            const periodSelect = document.getElementById("periodIdSelection");
            periodSelect.innerHTML = '<option value="" selected disabled>---- Select a Category ----</option>'

            response.forEach(function (optData) {
                const option = document.createElement("option");
                option.value = optData.periodId;
                option.text = optData.label;

                periodSelect.appendChild(option);
            });
        } catch (error) {
            console.error(error);
            alert('An error occurred while fetching reserve adjustment period details: ' + (error?.responseText || error.message));
        }
    }

    function fetchReservePeriods() {
        return $.ajax({
            url: '${pageContext.request.contextPath}/ClaimReserveAdjustmentController/periodType',
            method: 'GET',
        });
    }

    function fetchReserveCategories(){
        return $.ajax({
            url: '${pageContext.request.contextPath}/ClaimReserveAdjustmentController/categoryType',
            method: 'GET',
        });
    }

    function fetchCheckActiveAdjustment(categoryId, periodId){
        return $.ajax({
            url: '${pageContext.request.contextPath}/ClaimReserveAdjustmentController/checkActiveAdjustment?periodId='+periodId+'&categoryId='+categoryId,
            method: 'GET',
        });
    }

    // Validate form fields before save or update
    function validateForm() {
        let isValid = true;
        $('#addForm').find('input, textarea, select').each(function () {
            if ($(this).prop('required') && $(this).val() === '') {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        return isValid;
    }

    function validateEditForm() {
        let isValid = true;
        $('#editForm').find('input').each(function () {
            if ($.trim($(this).val()) === "") {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        return isValid;
    }

    function validatePeriodForm() {
        let isValid = true;
        $('#periodForm').find('input').each(function () {
            if ($(this).prop('required') && $(this).val() === '') {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        return isValid;
    }

    function removeValidateClass() {
        $('#addForm').find('input, textarea').each(function () {
            $(this).removeClass('is-invalid');
        });
    }

    function validateMaxLength(element, maxLength) {
        const errorElement = document.getElementById(element.id + "Error");
        if (element.value.length > maxLength) {
            errorElement.style.display = "block";
        } else {
            errorElement.style.display = "none";
        }
    }

</script>
</body>
</html>
