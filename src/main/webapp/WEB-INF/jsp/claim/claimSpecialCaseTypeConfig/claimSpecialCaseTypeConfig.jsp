<%--
  Created by IntelliJ IDEA.
  User: HP
  Date: 6/19/2024
  Time: 11:40 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
</head>
<body style="padding: 10px" onload="hideLoader()">

<div id="accordion" class="accordion">
    <div class="card">
        <div class="card-header" id="headingOne">
            <h5 class="mb-0">
                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                   aria-expanded="true" aria-controls="collapseOne">
                    Search Here <i class="fa fa-search"></i>
                </a>
            </h5>
        </div>
        <div id="collapseOne" class="collapse show p-3" aria-labelledby="headingOne"
             data-parent="#accordion">

            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                            data-target="#addModal" id="addBtn"><i class="fa fa-plus"></i> Add Claim Exceptional
                    </button>
                </div>
            </div>


            <div class="row mt-3">
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">ID</label>
                        <div class="col-sm-8">
                            <input type="number" class="form-control form-control-sm "
                                   id="txtId"
                                   placeholder="ID"
                                   min="1"
                                   name="txtId">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Claim No</label>
                        <div class="col-sm-8">
                            <textarea class="form-control form-control-sm "
                                      id="txtClaimNo"
                                      placeholder="Claim No"
                                      name="txtClaimNo"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Remark</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm "
                                   id="txtRemark"
                                   placeholder="Remark"
                                   name="txtRemark">
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" id="searchBtn">Search</button>
                    <button type="button" class="btn btn-secondary float-right mr-1" id="clearBtn">Clear</button>
                </div>
            </div>

            <!-- Edit Modal -->
            <%--<div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editModalLabel">Edit Holiday Type</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editForm">
                                <div class="form-group">
                                    <label>Holiday Type Name</label>
                                    <input type="text" class="form-control">
                                </div>

                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control"></textarea>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveChanges">Update
                                </button>
                                <button type="button" class="btn btn-secondary float-right mr-1">Cancel
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>--%>

            <!-- Add Modal -->
            <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addModalLabel">Add Claim Exceptional</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="addForm">
                                <input type="hidden" class="form-control" id="specialCaseTypeId" name="specialCaseTypeId">
                                <div class="form-group">
                                    <label>Claim No</label>
                                    <input type="text" class="form-control"
                                           id="claimNo"
                                           name="claimNo"
                                           placeholder="Claim No"
                                           required
                                    <%--                                           maxlength="225"--%>
                                    <%--                                           oninput="validateMaxLength(this, 255)"--%>
                                    >
                                    <small id="holidayTypeNameError" class="text-danger" style="display: none;">Maximum
                                        length is 255 characters.</small>
                                </div>

                                <div class="form-group">
                                    <label>Claim Type</label>
                                    <textarea class="form-control"
                                              id="claimType"
                                              name="claimType"
                                              placeholder="Claim Type"
                                              required
                                    <%--                                              maxlength="225"--%>
                                    <%--                                              oninput="validateMaxLength(this, 255)"--%>
                                    >
                                    </textarea>
                                    <small id="claimTypeError" class="text-danger" style="display: none;">Maximum
                                        length is 255 characters.</small>
                                </div>

                                <div class="form-group">
                                    <label>Remark</label>
                                    <textarea class="form-control"
                                              id="remark"
                                              name="remark"
                                              placeholder="Remark"
                                              required
                                    <%--                                              maxlength="225"--%>
                                    <%--                                              oninput="validateMaxLength(this, 255)"--%>
                                    >
                                    </textarea>
                                    <small id="holidayTypeDescError" class="text-danger" style="display: none;">Maximum
                                        length is 255 characters.</small>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveButton">Save</button>
                                <button type="button" class="btn btn-primary float-right" id="updateButton"
                                        style="display: none;">Update
                                </button>
                                <button type="button" class="btn btn-secondary float-right mr-1" id="cancelButton">
                                    Cancel
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card mt-3">
        <div class="card-body table-bg">
            <div class="row">
                <div class="col-lg-12 pl-0 pr-0">
                    <table id="example" class="table table-sm table-striped table-bordered" style="width:100%">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Claim No</th>
                            <th>Remark</th>
                            <th>Claim Type</th>
                            <th>Created User</th>
                            <th>Created Date/Time</th>
                            <th>Modified User</th>
                            <th>Modified Date/Time</th>
                            <th>Delete</th>
                            <th>Edit</th>
                        </tr>
                        </thead>
                        <%-- <tbody>
                         <tr>
                             <td>HD001</td>
                             <td>Poya</td>
                             <td>Holiday</td>
                             <td style="text-align: center">
                                 <button class="btn btn-danger btn-sm delete-btn" style="width: auto">
                                     <i class="fa fa-trash"></i>
                                 </button>
                             </td>
                             <td style="text-align: center">
                                 <button class="btn btn-primary btn-sm edit-btn" data-toggle="modal"
                                         data-target="#editModal" style="width: auto">
                                     <i class="fa fa-edit"></i>
                                 </button>
                             </td>

                         </tr>
                         </tbody>--%>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    let isSearch = 0;

    $(document).ready(function () {

        fetchAllClaimSpecialCaseTypeDetails();

        $('#example').on('click', '.edit-btn', function () {
            const currentRow = $(this).closest('tr');
            const taskName = currentRow.find('td:eq(4)').text();
            $('#editModal #taskName').val(taskName);
        });

        $('#example').on('click', '.delete-btn', function () {
            const specialCaseTypeId = $(this).data('id');
            bootbox.confirm({
                // message: "<p>Are you sure you want to delete this holiday Type?</p><textarea id='delete-reason' placeholder='Delete Reason' rows='4' style='width: 100%'></textarea>",
                message: "<p>Are you sure you want to delete this holiday Type?</p>",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-danger'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary'
                    }
                },
                callback: function (result) {
                    if (result) {
                        const reason = $('#delete-reason').val();
                        // Perform the delete action here, optionally using the reason
                        deleteClaimExceptionalCaseById(specialCaseTypeId, reason)
                    } else {
                        console.log('Delete canceled');
                    }
                }
            });
        });

        // Save button click event
        $('#saveButton').click(function () {
            if (validateForm()) {
                const claimNo = $('#claimNo').val();
                const remark = $('#remark').val();
                const claimType = $('#claimType').val();

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/claimSpecialCaseTypeController/saveClaimSpecialCaseType',
                    data: {
                        claimNo: claimNo,
                        claimType: claimType,
                        remark: remark
                    },
                    success: function (response) {
                        if (typeof response === 'string' && response?.trim() === '"Saved Successfully"') {
                            notify('Claim exceptional case details saved successfully.', "success");
                            $('#addModal').modal('hide');
                            fetchAllClaimSpecialCaseTypeDetails();
                        } else {
                            // notify('Cannot be saved. ', "danger");
                            notify(response.message, "danger");
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        notify('An error occurred while saving claim exceptional case details: ' + error, "danger");
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }

        });


        // Update button click event
        $('#updateButton').click(function () {
            if (validateForm()) {
                const claimNo = $('#claimNo').val();
                const remark = $('#remark').val();
                const claimType = $('#claimType').val();
                const specialCaseTypeId = $('#specialCaseTypeId').val();

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/claimSpecialCaseTypeController/updateClaimSpecialCaseType',
                    data: {
                        id: specialCaseTypeId,
                        claimNo: claimNo,
                        claimType: claimType,
                        remark: remark
                    },
                    success: function (response) {
                        if (typeof response === 'string' && response?.trim() === '"Saved Successfully"') {
                            notify('Claim exceptional case details updated successfully.', "success");
                            $('#addModal').modal('hide');
                            fetchAllClaimSpecialCaseTypeDetails();
                        } else {
                            // notify('Cannot be saved. ', "danger");
                            notify(response.message, "danger");
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        alert('An error occurred while updating claim exceptional case details: ' + error);
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }
        });

     
        function deleteClaimExceptionalCaseById(specialCaseTypeId, deleteReason) {
            $.ajax({
                type: 'post',
                url: '${pageContext.request.contextPath}/claimSpecialCaseTypeController/deleteClaimSpecialCaseType',
                data: {
                    id: specialCaseTypeId
                },
                success: function (response) {
                    if (typeof response === 'string' && response?.trim() === '"SUCCESS"') {
                        notify('Claim exceptional case details deleted successfully.', "success");
                        $('#addModal').modal('hide');
                        fetchAllClaimSpecialCaseTypeDetails();
                    } else {
                        notify(response.message, "danger");
                    }
                },
                error: function (error) {
                    console.log(error);
                    alert('An error occurred while fetching claim exceptional case details: ' + error);
                }
            });
        }

    });

    function getClaimExceptionalCaseById(specialCaseTypeId) {
        $.ajax({
            type: 'GET',
            url: '${pageContext.request.contextPath}/claimSpecialCaseTypeController/viewClaimSpecialCaseType',
            data: {id: specialCaseTypeId},
            success: function (response) {
                $('#specialCaseTypeId').val(response.id);
                $('#claimNo').val(response.claimNo);
                $('#claimType').val(response.claimType);
                $('#remark').val(response.remark);
                $('#saveButton').hide();
                $('#updateButton').show();
                $('#addModal').modal('show');
                $('#addModalLabel').text("Update Claim Exceptional");

                removeValidateClass();
            },
            error: function (error) {
                console.log(error);
                alert('An error occurred while fetching claim exceptional case details: ' + error);
            }
        });
    }

    function fetchAllClaimSpecialCaseTypeDetails() {
        $('#example').DataTable({
            serverSide: true,
            ajax: {
                url: '${pageContext.request.contextPath}/claimSpecialCaseTypeController/viewClaimSpecialCaseTypeList',
                type: 'POST',
                "data": function (d) {
                    d.id = $("#txtId").val();
                    d.claimNo = $("#txtClaimNo").val();
                    d.remark = $("#txtRemark").val();
                }
            },
            "bDestroy": true,
            destroy: true,
            searching: false,
            columns: [
                {data: 'id'},
                {data: 'claimNo'},
                {
                    data: 'remark',
                    defaultContent: ""
                },
                {
                    data: 'claimType',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'inputUser',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'inputDateTime',
                    orderable: false,
                    render: function (data, type, row) {
                        // Format inputDateTime from '7/8/2024, 12:09:27 PM' to '2024-07-08 12:09:27'
                        return data ? formatDate(data) : "";
                    }
                    /*render: function (data, type, row) {
                        // Format inputDateTime from '2024-07-08T12:09:27' to '2024-07-08 12:09:27'
                        return data ? new Date(data).toLocaleString() : ""; // Adjust locale as needed
                    }*/
                },
                {
                    data: 'lastModifiedUser',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'lastModifiedDateTime',
                    orderable: false,
                    render: function (data, type, row) {
                        // Format inputDateTime from '7/8/2024, 12:09:27 PM' to '2024-07-08 12:09:27'
                        return data ? formatDate(data) : "";
                    }
                },
                {
                    data: 'id',
                    orderable: false,
                    render: function (data, type, row) {
                        return '<button class="btn btn-danger btn-sm delete-btn" data-id="' + data + '"> <i class="fa fa-trash"></i></button>';
                    }
                },
                {
                    data: 'id',
                    orderable: false,
                    render: function (data, type, row) {
                        return '<button class="btn btn-primary btn-sm edit-btn" onclick="getClaimExceptionalCaseById(' + data + ')" data-id="' + data + '" ><i class="fa fa-edit"></i></button>';
                    }
                }
            ],
            order: [[0, 'asc']]
        });
    }

    function formatDate(dateString) {
        let date = new Date(dateString);
        let formattedDate = date.getFullYear() + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + ('0' + date.getDate()).slice(-2);
        let formattedTime = ('0' + date.getHours()).slice(-2) + ':' + ('0' + date.getMinutes()).slice(-2) + ':' + ('0' + date.getSeconds()).slice(-2);
        return formattedDate + ' ' + formattedTime;
    }

    $('#searchBtn').click(function () {
        fetchAllClaimSpecialCaseTypeDetails();
    });

    $('#clearBtn').click(function () {
        $('#txtId').val('');
        $('#txtRemark').val('');
        $('#txtClaimNo').val('');

        fetchAllClaimSpecialCaseTypeDetails();
    });

    $('#addBtn').click(function () {
        $('#specialCaseTypeId').val('');
        $('#claimNo').val('');
        $('#claimType').val('');
        $('#remark').val('');
        $('#saveButton').show();
        $('#updateButton').hide();
        $('#addModalLabel').text("Add Claim Exceptional");

        removeValidateClass();
    });

    $('#cancelButton').click(function () {
        $('#addModal').modal('hide');
        removeValidateClass();
    });

    // Validate form fields before save or update
    function validateForm() {
        let isValid = true;
        $('#addForm').find('input, textarea').each(function () {
            if ($(this).prop('required') && $(this).val() === '') {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        return isValid;
    }

    function removeValidateClass() {
        $('#addForm').find('input, textarea').each(function () {
            $(this).removeClass('is-invalid');
        });
    }

    function validateMaxLength(element, maxLength) {
        const errorElement = document.getElementById(element.id + "Error");
        if (element.value.length > maxLength) {
            errorElement.style.display = "block";
        } else {
            errorElement.style.display = "none";
        }
    }

</script>
</body>
</html>
