<%--
  Created by IntelliJ IDEA.
  User: HP
  Date: 6/19/2024
  Time: 11:40 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
</head>
<body style="padding: 10px" onload="hideLoader()">

<div id="accordion" class="accordion">
    <div class="card">
        <div class="card-header" id="headingOne">
            <h5 class="mb-0">
                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                   aria-expanded="true" aria-controls="collapseOne">
                    Search Here <i class="fa fa-search"></i>
                </a>
            </h5>
        </div>
        <div id="collapseOne" class="collapse show p-3" aria-labelledby="headingOne"
             data-parent="#accordion">

            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                            data-target="#addModal" id="addBtn"><i class="fa fa-plus"></i> Add Holiday Type
                    </button>
                </div>
            </div>


            <div class="row mt-3">
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Holiday Type ID</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm "
                                   id="txtHolidayTypeId"
                                   placeholder="Holiday Type ID"
                                   name="txtHolidayTypeId">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Description</label>
                        <div class="col-sm-8">
                            <textarea class="form-control form-control-sm "
                                      id="txtHolidayTypeDesc"
                                      placeholder="Description"
                                      name="txtHolidayTypeDesc"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Holiday Type Name</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm "
                                   id="txtHolidayTypeName"
                                   placeholder="Holiday Type Name"
                                   name="txtHolidayTypeName">
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" id="searchBtn">Search</button>
                    <button type="button" class="btn btn-secondary float-right mr-1" id="clearBtn">Clear</button>
                </div>
            </div>

            <!-- Edit Modal -->
            <%--<div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editModalLabel">Edit Holiday Type</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editForm">
                                <div class="form-group">
                                    <label>Holiday Type Name</label>
                                    <input type="text" class="form-control">
                                </div>

                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control"></textarea>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveChanges">Update
                                </button>
                                <button type="button" class="btn btn-secondary float-right mr-1">Cancel
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>--%>

            <!-- Add Modal -->
            <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addModalLabel">Add Holiday Type </h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="addForm">
                                <input type="hidden" class="form-control" id="holidayTypeId" name="holidayTypeId">
                                <div class="form-group">
                                    <label>Holiday Type Name</label>
                                    <input type="text" class="form-control"
                                           id="holidayTypeName"
                                           name="holidayTypeName"
                                           placeholder="Holiday Type Name"
                                           required>
                                </div>

                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control"
                                              id="holidayTypeDesc"
                                              name="holidayTypeDesc"
                                              placeholder="Description"
                                              required>

                                    </textarea>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveButton">Save</button>
                                <button type="button" class="btn btn-primary float-right" id="updateButton"
                                        style="display: none;">Update
                                </button>
                                <button type="button" class="btn btn-secondary float-right mr-1">Cancel</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card mt-3">
        <div class="card-body table-bg">
            <div class="row">
                <div class="col-lg-12 pl-0 pr-0">
                    <table id="example" class="table table-sm table-striped table-bordered" style="width:100%">
                        <thead>
                        <tr>
                            <th>Holiday ID</th>
                            <th>Holiday Type Name</th>
                            <th>Description</th>
                            <th>Delete</th>
                            <th>Edit</th>
                        </tr>
                        </thead>
                        <%-- <tbody>
                         <tr>
                             <td>HD001</td>
                             <td>Poya</td>
                             <td>Holiday</td>
                             <td style="text-align: center">
                                 <button class="btn btn-danger btn-sm delete-btn" style="width: auto">
                                     <i class="fa fa-trash"></i>
                                 </button>
                             </td>
                             <td style="text-align: center">
                                 <button class="btn btn-primary btn-sm edit-btn" data-toggle="modal"
                                         data-target="#editModal" style="width: auto">
                                     <i class="fa fa-edit"></i>
                                 </button>
                             </td>

                         </tr>
                         </tbody>--%>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    let isSearch = 0;

    $(document).ready(function () {

        fetchAllHolidayTypeDetails();

        $('#example').on('click', '.edit-btn', function () {
            const currentRow = $(this).closest('tr');
            const taskName = currentRow.find('td:eq(4)').text();
            $('#editModal #taskName').val(taskName);
        });

        $('#example').on('click', '.delete-btn', function () {
            const holidayTypeId = $(this).data('id');
            bootbox.confirm({
                message: "<p>Are you sure you want to delete this holiday Type?</p><textarea id='delete-reason' placeholder='Delete Reason' rows='4' style='width: 100%'></textarea>",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-danger'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary'
                    }
                },
                callback: function (result) {
                    if (result) {
                        const reason = $('#delete-reason').val();
                        // Perform the delete action here, optionally using the reason
                        deleteHolidayTypeById(holidayTypeId, reason)
                    } else {
                        console.log('Delete canceled');
                    }
                }
            });
        });

        // Save button click event
        $('#saveButton').click(function () {
            if (validateForm()) {
                const holidayTypeName = $('#holidayTypeName').val();
                const holidayTypeDesc = $('#holidayTypeDesc').val();

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/HolidayTypeDetailController/saveHolidayTypeDetail',
                    data: {
                        holidayTypeName: holidayTypeName,
                        holidayTypeDesc: holidayTypeDesc
                    },
                    success: function (response) {
                        notify('Holiday type details saved successfully.', "success");
                        $('#addModal').modal('hide');
                        fetchAllHolidayTypeDetails();
                    },
                    error: function (error) {
                        console.log(error);
                        notify('An error occurred while saving holiday type details: ' + error, "danger");
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }

        });


        // Update button click event
        $('#updateButton').click(function () {
            if (validateForm()) {
                const holidayTypeName = $('#holidayTypeName').val();
                const holidayTypeDesc = $('#holidayTypeDesc').val();
                const holidayTypeId = $('#holidayTypeId').val();

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/HolidayTypeDetailController/updateHolidayTypeDetail',
                    data: {
                        holidayTypeId: holidayTypeId,
                        holidayTypeName: holidayTypeName,
                        holidayTypeDesc: holidayTypeDesc
                    },
                    success: function (response) {
                        notify('Holiday type details updated successfully.', "success");
                        $('#addModal').modal('hide');
                        fetchAllHolidayTypeDetails();
                    },
                    error: function (error) {
                        console.log(error);
                        alert('An error occurred while updating holiday type details: ' + error);
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }
        });

        // Function to delete holiday type details by ID
        function deleteHolidayTypeById(holidayTypeId, deleteReason) {
            $.ajax({
                type: 'post',
                url: '${pageContext.request.contextPath}/HolidayTypeDetailController/deleteHolidayTypeDetail',
                data: {
                    holidayTypeId: holidayTypeId,
                    deleteReason: deleteReason
                },
                success: function (response) {
                    notify('Holiday type details deleted successfully.', "success");
                    $('#addModal').modal('hide');
                    fetchAllHolidayTypeDetails();
                },
                error: function (error) {
                    console.log(error);
                    alert('An error occurred while fetching holiday type details: ' + error);
                }
            });
        }

    });

    // Function to fetch holiday type details by ID
    function getHolidayTypeById(holidayTypeId) {
        $.ajax({
            type: 'GET',
            url: '${pageContext.request.contextPath}/HolidayTypeDetailController/viewHolidayTypeDetail',
            data: {holidayTypeId: holidayTypeId},
            success: function (response) {
                $('#holidayTypeId').val(response.holidayTypeId);
                $('#holidayTypeName').val(response.holidayTypeName);
                $('#holidayTypeDesc').val(response.holidayTypeDesc);
                $('#saveButton').hide();
                $('#updateButton').show();
                $('#addModal').modal('show');
                $('#addModalLabel').text("Update Holiday Type");

                removeValidateClass();
            },
            error: function (error) {
                console.log(error);
                alert('An error occurred while fetching holiday type details: ' + error);
            }
        });
    }

    function fetchAllHolidayTypeDetails() {
        $('#example').DataTable({
            serverSide: true,
            ajax: {
                url: '${pageContext.request.contextPath}/HolidayTypeDetailController/viewHolidayTypeDetailList',
                type: 'POST',
                "data": function (d) {
                    d.holidayTypeId = $("#txtHolidayTypeId").val();
                    d.holidayTypeName = $("#txtHolidayTypeName").val();
                    d.holidayTypeDesc = $("#txtHolidayTypeDesc").val();
                }
            },
            "bDestroy": true,
            destroy: true,
            searching: false,
            columns: [
                {data: 'holidayTypeId'},
                {data: 'holidayTypeName'},
                // {data: 'holidayTypeDesc'},
                {
                    data: 'holidayTypeDesc',
                    defaultContent: ""  // Provide default content if holidayTypeDesc is null or undefined
                },
                {
                    data: 'holidayTypeId',
                    render: function (data, type, row) {
                        return '<button class="btn btn-danger btn-sm delete-btn" data-id="' + data + '"> <i class="fa fa-trash"></i></button>';
                    }
                },
                {
                    data: 'holidayTypeId',
                    render: function (data, type, row) {
                        return '<button class="btn btn-primary btn-sm edit-btn" onclick="getHolidayTypeById(' + data + ')" data-id="' + data + '" ><i class="fa fa-edit"></i></button>';
                    }
                }
            ],
            order: [[0, 'asc']]
        });
    }

    $('#searchBtn').click(function () {
        fetchAllHolidayTypeDetails();
    });

    $('#clearBtn').click(function () {
        $('#txtHolidayTypeId').val('');
        $('#txtHolidayTypeName').val('');
        $('#txtHolidayTypeDesc').val('');

        fetchAllHolidayTypeDetails();
    });

    $('#addBtn').click(function () {
        $('#holidayTypeId').val('');
        $('#holidayTypeName').val('');
        $('#holidayTypeDesc').val('');
        $('#saveButton').show();
        $('#updateButton').hide();
        $('#addModalLabel').text("Add Holiday Type");

        removeValidateClass();
    });

    // Validate form fields before save or update
    function validateForm() {
        let isValid = true;
        $('#addForm').find('input, textarea').each(function () {
            if ($(this).prop('required') && $(this).val() === '') {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        return isValid;
    }

    function removeValidateClass() {
        $('#addForm').find('input, textarea').each(function () {
            $(this).removeClass('is-invalid');
        });
    }

</script>
</body>
</html>
