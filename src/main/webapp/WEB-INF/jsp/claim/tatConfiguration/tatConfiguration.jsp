<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 6/22/2024
  Time: 11:40 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
</head>
<body style="padding: 10px">

<div id="accordion" class="accordion">
    <div class="card">
        <div class="card-header" id="headingOne">
            <h5 class="mb-0">
                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                   aria-expanded="true" aria-controls="collapseOne">
                    Search Here <i class="fa fa-search"></i>
                </a>
            </h5>
        </div>
        <div id="collapseOne" class="collapse show p-3" aria-labelledby="headingOne"
             data-parent="#accordion">

            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                            data-target="#addModal"><i class="fa fa-plus"></i> Add Task
                    </button>
                </div>
            </div>


            <div class="row mt-3">
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Task ID</label>
                        <div class="col-sm-8">
                            <input type="text" id="taskIdInput" class="form-control form-control-sm ">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Task Name</label>
                        <div class="col-sm-8">
                            <input type="text" id="taskNameInput" class="form-control form-control-sm ">
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" id="searchBtn">Search</button>
                    <button type="button" class="btn btn-secondary float-right mr-1" id="clearBtn">Clear</button>
                </div>
            </div>

            <!-- Edit Modal -->
            <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editModalLabel">Edit TAT Configuration</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editForm">
                                <div class="form-group">
                                    <label>Task Name</label>
                                    <input type="text" class="form-control" id="taskName" name="taskName">
                                </div>
                                <div class="form-group">
                                    <label>Minimum TAT</label>
                                    <input type="text" class="form-control" id="minTime" name="minTime">
                                </div>
                                <div class="form-group">
                                    <label>Maximum TAT</label>
                                    <input type="text" class="form-control" id="maxTime" name="maxTime">
                                </div>
                                <div class="form-group">
                                    <label for="taskStatus">Visibility</label>
                                    <select class="form-control" id="taskStatus" name="visibility">
                                        <option value="Not Started">Not Started</option>
                                        <option value="In Progress">In Progress</option>
                                        <option value="Completed">Completed</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="updateChanges">Update
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>


            <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteModalLabel">Deleting this record will permanently remove
                                it from the system. Do you wish to continue? </h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="deleteForm">
                                <div class="form-group">
                                    <label>Delete Reason</label>
                                    <textarea class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
                                </div>
                                <button type="button" class="btn btn-danger float-right">Yes, Delete
                                </button>
                                <button type="button" class="btn btn-secondary float-right mr-3">Cancel
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Modal -->
            <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addModalLabel">Add TAT Configuration</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="addForm">
                                <div class="form-group">
                                    <label>Task Name</label>
                                    <input type="text" class="form-control" id="taskNameSave" name="taskName">
                                </div>
                                <div class="form-group">
                                    <label>Minimum TAT</label>
                                    <input type="text" class="form-control" id="minTimeSave" name="minTime">
                                </div>
                                <div class="form-group">
                                    <label>Maximum TAT</label>
                                    <input type="text" class="form-control" id="maxTimeSave" name="maxTime">
                                </div>
                                <div class="form-group">
                                    <label for="taskStatus">Visibility</label>
                                    <select class="form-control" id="taskStatusSave" name="visibility">
                                        <option value="Not Started">Not Started</option>
                                        <option value="In Progress">In Progress</option>
                                        <option value="Completed">Completed</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveChanges">Save
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-body table-bg">
            <div class="row">
                <div class="col-lg-12 pl-0 pr-0">
                    <table id="tatTable" class="table table-sm table-striped table-bordered" style="width:100%">
                        <thead>
                        <tr>
                            <th>TAT ID</th>
                            <th>Task name</th>
                            <th>Visibility</th>
                            <th>Min TAT</th>
                            <th>Max TAT</th>
                            <th>Delete</th>
                            <th>Edit</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    $(document).ready(function () {
        fetchAllTATDetails();

        function fetchAllTATDetails() {
            $('#tatTable').DataTable({
                serverSide: true,
                ajax: {
                    url: '${pageContext.request.contextPath}/TATConfigController/getAllTATDetails',
                    type: 'POST'
                },
                "bDestroy": true,
                destroy: true,
                columns: [
                    {data: 'tatId'},
                    {data: 'taskName'},
                    {data: 'visibility'},
                    {data: 'minTime'},
                    {data: 'maxTime'},
                    {
                        data: 'tatId',
                        render: function (data, type, row) {
                            return '<button class="btn btn-danger btn-sm delete-btn" data-id="' + data + '"> <i class="fa fa-trash"></i></button>';
                        }
                    },
                    {
                        data: 'tatId',
                        render: function (data, type, row) {
                            return '<button class="btn btn-primary btn-sm edit-btn" data-id="' + data + '" data-toggle="modal" data-target="#editModal"><i class="fa fa-edit"></i></button>';
                        }
                    }
                ],
                order: [[1, 'asc']]
            });
        }

        $(document).ready(function () {

            $('#updateChanges').click(function () {
                const tatId = $('#editModal').data('tatId');
                const taskName = $('#editModal #taskName').val().trim();
                const minTime = $('#editModal #minTime').val().trim();
                const maxTime = $('#editModal #maxTime').val().trim();
                const visibility = $('#editModal #taskStatus').val().trim();

                let valid = true;

                if (!taskName) {
                    $('#editModal #taskName').addClass('is-invalid');
                    $('#editModal #taskName').next('.invalid-feedback').remove();
                    $('#editModal #taskName').after('<div class="invalid-feedback">Task Name is required</div>');
                    valid = false;
                } else {
                    $('#editModal #taskName').removeClass('is-invalid');
                    $('#editModal #taskName').next('.invalid-feedback').remove();
                }

                if (!minTime) {
                    $('#editModal #minTime').addClass('is-invalid');
                    $('#editModal #minTime').next('.invalid-feedback').remove();
                    $('#editModal #minTime').after('<div class="invalid-feedback">Minimum TAT is required</div>');
                    valid = false;
                } else {
                    $('#editModal #minTime').removeClass('is-invalid');
                    $('#editModal #minTime').next('.invalid-feedback').remove();
                }

                if (!maxTime) {
                    $('#editModal #maxTime').addClass('is-invalid');
                    $('#editModal #maxTime').next('.invalid-feedback').remove();
                    $('#editModal #maxTime').after('<div class="invalid-feedback">Maximum TAT is required</div>');
                    valid = false;
                } else {
                    $('#editModal #maxTime').removeClass('is-invalid');
                    $('#editModal #maxTime').next('.invalid-feedback').remove();
                }

                if (valid) {
                    $.ajax({
                        type: 'POST',
                        url: '${pageContext.request.contextPath}/TATConfigController/updateTATDetails',
                        data: {
                            tatId: tatId,
                            taskName: taskName,
                            minTime: minTime,
                            maxTime: maxTime,
                            visibility: visibility
                        },
                        success: function (response) {
                            notify('TAT details updated successfully', "success");
                            $('#editModal').modal('hide');
                            fetchAllTATDetails();
                        },
                        error: function (xhr, status, error) {
                            notify('An error occurred while updating TAT details: ' + error, "error");
                        }
                    });
                }
            });

            $('#tatTable').on('click', '.edit-btn', function () {
                const tatId = $(this).data('id');

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/TATConfigController/searchTATDetail',
                    data: {tatId: tatId},
                    success: function (response) {
                        const tatDetail = JSON.parse(response);

                        $('#editModal').data('tatId', tatDetail.tatId);
                        $('#editModal #taskName').val(tatDetail.taskName);
                        $('#editModal #minTime').val(tatDetail.minTime);
                        $('#editModal #maxTime').val(tatDetail.maxTime);
                        $('#editModal #taskStatus').val(tatDetail.visibility);
                        $('#editModal').modal('show');
                    },
                    error: function (xhr, status, error) {
                        notify('An error occurred while fetching TAT details: ' + error, "error");
                    }
                });
            });
        });

        $('#saveChanges').click(function () {
            const taskName = $('#addForm #taskNameSave').val().trim();
            const minTime = $('#addForm #minTimeSave').val().trim();
            const maxTime = $('#addForm #maxTimeSave').val().trim();
            const visibility = $('#addForm #taskStatusSave').val().trim();

            let valid = true;

            if (!taskName) {
                $('#addForm #taskNameSave').addClass('is-invalid');
                $('#addForm #taskNameSave').next('.invalid-feedback').remove();
                $('#addForm #taskNameSave').after('<div class="invalid-feedback">Task Name is required</div>');
                valid = false;
            } else {
                $('#addForm #taskNameSave').removeClass('is-invalid');
                $('#addForm #taskNameSave').next('.invalid-feedback').remove();
            }

            if (!minTime) {
                $('#addForm #minTimeSave').addClass('is-invalid');
                $('#addForm #minTimeSave').next('.invalid-feedback').remove();
                $('#addForm #minTimeSave').after('<div class="invalid-feedback">Minimum TAT is required</div>');
                valid = false;
            } else {
                $('#addForm #minTimeSave').removeClass('is-invalid');
                $('#addForm #minTimeSave').next('.invalid-feedback').remove();
            }

            if (!maxTime) {
                $('#addForm #maxTimeSave').addClass('is-invalid');
                $('#addForm #maxTimeSave').next('.invalid-feedback').remove();
                $('#addForm #maxTimeSave').after('<div class="invalid-feedback">Maximum TAT is required</div>');
                valid = false;
            } else {
                $('#addForm #maxTimeSave').removeClass('is-invalid');
                $('#addForm #maxTimeSave').next('.invalid-feedback').remove();
            }

            if (valid) {
                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/TATConfigController/saveTATDetails',
                    data: {
                        taskName: taskName,
                        minTime: minTime,
                        maxTime: maxTime,
                        visibility: visibility
                    },
                    success: function (response) {
                        notify('TAT details saved successfully.', "success");
                        $('#addForm #taskNameSave').val('');
                        $('#addForm #minTimeSave').val('');
                        $('#addForm #maxTimeSave').val('');
                        $('#addForm #taskStatusSave').val('');
                        $('#addModal').modal('hide');
                        fetchAllTATDetails();
                    },
                    error: function (error) {
                        notify('An error occurred while saving TAT details: ' + error, "error");
                    }
                });
            }
        });

        $('#tatTable').on('click', '.delete-btn', function () {
            const tatId = $(this).data('id');

            bootbox.confirm({
                message: "<p>Are you sure you want to delete this TAT?</p>" +
                    "<textarea style='width: 100%' id='delete-reason' placeholder='Delete Reason' rows='4' cols='50'></textarea>",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-danger'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary'
                    }
                },
                callback: function (result) {
                    if (result) {
                        const deleteReason = $('#delete-reason').val().trim();
                        let valid = true;

                        if (!deleteReason) {
                            $('#delete-reason').addClass('is-invalid');
                            $('#delete-reason').next('.invalid-feedback').remove();
                            $('#delete-reason').after('<div class="invalid-feedback">Task Name is required</div>');
                            valid = false;
                        } else {
                            $('#delete-reason').removeClass('is-invalid');
                            $('#delete-reason').next('.invalid-feedback').remove();
                        }

                        if (valid) {
                            $.ajax({
                                type: 'POST',
                                url: '${pageContext.request.contextPath}/TATConfigController/deleteTATDetail',
                                data: {
                                    tatId: tatId,
                                    deleteReason: deleteReason
                                },
                                success: function (response) {
                                    notify('TAT detail deleted successfully', "success");
                                    fetchAllTATDetails();

                                },
                                error: function (xhr, status, error) {
                                    notify('An error occurred while deleting TAT detail: ' + error, "error");
                                }
                            });
                        }
                    }
                }
            });
        });

        function searchFilters() {
            const tatId = $('#taskIdInput').val();
            const taskName = $('#taskNameInput').val();

            $('#tatTable').DataTable({
                serverSide: true,
                ajax: {
                    url: '${pageContext.request.contextPath}/TATConfigController/filterTatDetails',
                    type: 'POST',
                    data: function (d) {
                        d.tatId = tatId;
                        d.taskName = taskName;
                    },
                    dataSrc: function (json) {
                        if (json.error) {
                            notify(json.error, "error");
                            return [];
                        }
                        return json.data;
                    }
                },
                "bDestroy": true,
                destroy: true,
                columns: [
                    {data: 'tatId'},
                    {data: 'taskName'},
                    {data: 'visibility'},
                    {data: 'minTime'},
                    {data: 'maxTime'},
                    {
                        data: 'tatId',
                        render: function (data, type, row) {
                            return '<button class="btn btn-danger btn-sm delete-btn" data-id="' + data + '"> <i class="fa fa-trash"></i></button>';
                        }
                    },
                    {
                        data: 'tatId',
                        render: function (data, type, row) {
                            return '<button class="btn btn-primary btn-sm edit-btn" data-id="' + data + '" data-toggle="modal" data-target="#editModal"><i class="fa fa-edit"></i></button>';
                        }
                    }
                ],
                order: [[1, 'asc']]
            });
        }

        $('#searchBtn').click(function () {
            searchFilters();
        });

        $('#clearBtn').click(function () {
            $('#taskIdInput').val('');
            $('#taskNameInput').val('');
            fetchAllTATDetails();
        });
    });
    hideLoader();


</script>
</body>
</html>

