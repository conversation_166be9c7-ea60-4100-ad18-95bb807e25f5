<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE HTML>
<!--
/*
* jQuery File Upload Plugin Basic Demo
* https://github.com/blueimp/jQuery-File-Upload
*
* Copyright 2013, <PERSON>
* https://blueimp.net
*
* Licensed under the MIT license:
* https://opensource.org/licenses/MIT
*/
-->
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Document Upload</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <!-- Generic page styles -->
    <%--<link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/style.css">--%>
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/ari-form-validations.js"></script>
</head>
<body onload="hideLoader()">
<div class="container-fluid">
    <%--<div class="row header-bg">--%>
    <%--<div class="col-sm-12 py-2 bg-dark">--%>
    <%--<h6 class="float-left text-dark hide-for-small"> Inspection Report Details</h6>--%>
    <%--</div>--%>
    <%--</div>--%>
    <div class="row">
        <div class="offset-md-3 col-md-6">
            <form id="frmMain" name="frmMain" method="post">
                <input type="hidden" id="claimNo" name="claimNo" value="${claimNo}" >
                    <fieldset class="border p-2 mt-3">
                        <h6>Customer Details </h6>
                        <hr class="my-2">
                        <div class="col-md-12">
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Customer Name :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <input type="text" class="form-control form-control-sm" name="customerName"
                                               id="customerName">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <leabel class="col-sm-4 col-form-label">Address :</leabel>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <input type="text" class="form-control form-control-sm" placeholder="Address 1"
                                               name="address1" id="address1"/>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"></label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <input type="text" class="form-control form-control-sm mt-1"
                                               placeholder="Address 2" name="address2" id="address2">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"></label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <input type="text" class="form-control form-control-sm mt-1"
                                               placeholder="Address 3" id="address3" name="address3">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Contact Number :</label>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <input type="text" class="form-control form-control-sm" name="contactNo"
                                               id="contactNo">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col text-right p-0">
                                    <button type="submit" class="btn btn-primary" id="btnSubmit">Submit</button>
                                </div>
                            </div>
                        </div>
                    </fieldset>
            </form>
            <c:if test="${successMessage!=null && successMessage!=''}">
                <script type="text/javascript">
                    window.parent.notify('${successMessage}', "success");
                </script>
            </c:if>
            <c:if test="${errorMessage!=null && errorMessage!=''}">
                <script type="text/javascript">
                    window.parent.notify('${errorMessage}', "danger");
                </script>
            </c:if>


        </div>
    </div>
</div>
</body>
<script type="text/javascript">

    function hideValidation() {
        $('#customerName').val("");
        $('#address1').val("");
        $('#address2').val("");
        $('#contactNo').val("");
        $('#address3').val("");
        $('#frmMain').data('formValidation').enableFieldValidators('customerName', false);
        $('#frmMain').data('formValidation').enableFieldValidators('address1', false);
        $('#frmMain').data('formValidation').enableFieldValidators('address2', false);
        $('#frmMain').data('formValidation').enableFieldValidators('contactNo', false);
        $('#customerName').attr('disabled', 'disabled');
        $('#address1').attr('disabled', 'disabled');
        $('#address2').attr('disabled', 'disabled');
        $('#contactNo').attr('disabled', 'disabled');
        $('#address3').attr('disabled', 'disabled');
        $('#btnSubmit').hide();

    }

    function showValidation() {
        $('#customerName').removeAttr('disabled');
        $('#address1').removeAttr('disabled');
        $('#address2').removeAttr('disabled');
        $('#contactNo').removeAttr('disabled');
        $('#address3').removeAttr('disabled');
        $('#btnSubmit').show();
        $('#frmMain').data('formValidation').enableFieldValidators('customerName', true);
        $('#frmMain').data('formValidation').enableFieldValidators('address1', true);
        $('#frmMain').data('formValidation').enableFieldValidators('address2', true);
        $('#frmMain').data('formValidation').enableFieldValidators('contactNo', true);


    }


    <%--function pageSubmit() {--%>

    <%--document.frmMain.action = "${pageContext.request.contextPath}/InspectionDetailsController/saveAri";--%>
    <%--document.frmMain.submit();--%>

    <%--}--%>
</script>
</html>
