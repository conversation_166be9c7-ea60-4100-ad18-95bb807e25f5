<div class="">
    <fieldset class="border p-2 my-2">
        <h6> Offer</h6>
        <hr class="my-1">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Provide Offer?
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.provideOffer eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.provideOffer" type="radio" value="Yes"
                                class="align-middle provideoffer" onclick="addValidation('offerType','','frmMain')"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.provideOffer eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.provideOffer" type="radio" value="No"
                                class="align-middle provideoffer" onclick="removeValidation('offerType','','frmMain')"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Offer Type
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8">
                <select name="desktopInspectionDetailsDto.offerType" class="form-control form-control-sm offertype "
                        id="offerType">
                    <option value="0">Please Select</option>
                    <option value="1">On site Offer</option>
                    <option value="3">Call Estimate</option>
                </select>
            </div>
            <script>document.getElementById("offerType").value = "${inspectionDetailsDto.desktopInspectionDetailsDto.offerType}";</script>
        </div>
    </fieldset>
    <fieldset class="border p-2 mt-2">
        <h6>Desktop Review</h6>
        <hr class="my-1">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Approximate Cost Report :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.appCostReport " placeholder="Approximate Cost Report"
                       value="${isEmptyValue? '' :inspectionDetailsDto.desktopInspectionDetailsDto.appCostReport}"
                       class="form-control form-control-sm cospart" id=" ">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Pre Accident Value (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.preAccidentValue" placeholder="Pre Accident Value"
                       value="${isEmptyValue? '' :inspectionDetailsDto.desktopInspectionDetailsDto.preAccidentValue}"
                       class="form-control form-control-sm preaccidentvalue text-right" id=" ">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.acr" placeholder="ACR"
                       value="${inspectionDetailsDto.desktopInspectionDetailsDto.acr}"
                       class="form-control form-control-sm acr text-right " id=" ">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Excess (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.excess" placeholder="Excess"
                       value="${inspectionDetailsDto.desktopInspectionDetailsDto.excess}"
                       class="form-control form-control-sm  text-right" id=" " readonly>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Bald Tyre Penalty
                :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.boldTyrePenaltyAmount" placeholder="Bald Tyre Penalty"
                       value="${inspectionDetailsDto.desktopInspectionDetailsDto.boldTyrePenaltyAmount}"
                       class="form-control form-control-sm underpenaltyamount text-right" id=" ">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Under Insurance Penalty:</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.underInsurancePenaltyAmount"
                       placeholder="Under Insurance Penalty"
                       value="${inspectionDetailsDto.desktopInspectionDetailsDto.underInsurancePenaltyAmount}"
                       class="form-control form-control-sm underpenaltyamount text-right" id=" ">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Payable Amount (Rs.) :</label>
            <div class="col-sm-8">
                <input name="desktopInspectionDetailsDto.payableAmount" placeholder="Payable Amount"
                       value="${isEmptyValue? '' :inspectionDetailsDto.desktopInspectionDetailsDto.payableAmount}"
                       class="form-control form-control-sm payableamount text-right" id=" ">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Desktop Offer
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.desktopOffer eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.desktopOffer" type="radio" value="Yes"
                                class="align-middle desktopoffer"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.desktopOffer eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.desktopOffer" type="radio" value="No"
                                class="align-middle desktopoffer"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">ARI Salvage
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.ariSalvage eq 'Yes' and not isEmptyValue  or isRequested =='Y' ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.ariSalvage" type="radio" value="Yes"
                                class="align-middle arisalvage"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.ariSalvage eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.ariSalvage" type="radio" value="No"
                                class="align-middle arisalvage"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
    </fieldset>
    <fieldset class="border p-2 mt-2">
        <h6> Assessor Remarks</h6>
        <hr class="my-1">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Settlement Methods
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8">
                <select name="desktopInspectionDetailsDto.settlementMethod"
                        class="form-control form-control-sm settlementMethod"
                        id="settlementMethod">
                    <option value="0">-- Please Select --</option>
                    <option value="1">Partial Loss</option>
                    <option value="2">Total Loss</option>
                    <option value="3">Cash In Lieu</option>
                </select>
            </div>
            <script>document.getElementById("settlementMethod").value = "${inspectionDetailsDto.desktopInspectionDetailsDto.settlementMethod}";</script>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Inspection remarks :</label>
            <div class="col-sm-8">
                              <textarea name="desktopInspectionDetailsDto.inspectionRemark"
                                        class="form-control form-control-sm "
                                        id=" ">${inspectionDetailsDto.desktopInspectionDetailsDto.inspectionRemark}</textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Police Report Request<span
                    class="text-danger font-weight-bold">  *</span> :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.policeReportRequested eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.policeReportRequested" type="radio" value="Yes"
                                class="align-middle policerequested"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.policeReportRequested eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.policeReportRequested" type="radio" value="No"
                                class="align-middle policerequested"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Special remarks :</label>
            <div class="col-sm-8">
                <textarea name="desktopInspectionDetailsDto.specialRemark" class="form-control form-control-sm "
                          id=" ">${inspectionDetailsDto.desktopInspectionDetailsDto.specialRemark}</textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Investigate Claim<span
                    class="text-danger font-weight-bold">  *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.investigaedClaim eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.investigaedClaim" type="radio" value="Yes"
                                class="align-middle investigate"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input ${inspectionDetailsDto.desktopInspectionDetailsDto.investigaedClaim eq 'No' and not isEmptyValue ? 'checked' : ''}
                                name="desktopInspectionDetailsDto.investigaedClaim" type="radio" value="No"
                                class="align-middle investigate"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <!--        <div class="mt-3">
                    <div class="float-right">
                        <button type="button" name="cmdReject" id="cmdAuth"
                                value="Authorize"
                                class="btn btn-primary">
                            Authorize
                        </button>
                    </div>
                </div>-->
    </fieldset>
    <fieldset class="border p-2 mt-2 my-2">
        <h6> Assessor Payment</h6>
        <hr class="my-1">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Job Type
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8">
                <select name="jobType" class="form-control form-control-sm jobtype"
                        id="jobType">
                    <option value="0">-- Please Select --</option>
                    <option value="1">DAY</option>
                    <option value="2">NIGHT</option>
                </select>
                <script>document.getElementById("jobType").value = "${inspectionDetailsDto.jobType}";</script>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Current Location
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8">
                <input name="currentLocation" class="form-control form-control-sm"
                       value="${inspectionDetailsDto.assessorAllocationDto.currentLocation}"
                       readonly>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Place of Inspection
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8">
                <input name="placeOfInspection" class="form-control form-control-sm"
                       value="${inspectionDetailsDto.assessorAllocationDto.placeOfinspection}"
                       readonly>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Mileage
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
            <div class="col-sm-8">
                <input class="form-control form-control-sm mileage miles" id="mileage"
                       name="mileage" value="${isEmptyValue? '' :inspectionDetailsDto.mileage}"/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Other Fee
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8">
                <input class="form-control form-control-sm text-right"
                       id="otherFee"
                       name="otherFee" value="${isEmptyValue? '' :inspectionDetailsDto.otherFee}">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Total Fee
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8">
                <input class="form-control form-control-sm otherchage text-right"
                       id="totalAssessorFee"
                       name="totalAssessorFee"
                       value="${isEmptyValue? '':inspectionDetailsDto.totalAssessorFee}" readonly>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Description
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8">
                    <textarea name="feeDesc" class="form-control form-control-sm"
                              id=" ">${inspectionDetailsDto.feeDesc}</textarea>
            </div>
        </div>
    </fieldset>
</div>
<script>
    $(document).ready(function () {

        if (${inspectionDetailsDto.onSiteInspectionDetailsDto.provideOffer eq 'Yes'}) {
            addValidation('offerType', '', 'frmMain');
        } else {
            removeValidation('offerType', '', 'frmMain');
        }

    });
</script>
