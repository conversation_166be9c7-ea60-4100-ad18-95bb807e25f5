<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="">
    <c:if test="${inspectionDetailsDto.isVehicleAvailable ne 'N'}">
        <fieldset class="border p-2 mt-2">
            <h6>Garage Assessment</h6>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
                <div class="col-sm-8">
                    <input value="${inspectionDetailsDto.garageInspectionDetailsDto.acr}" placeholder="ACR"
                           class="form-control form-control-sm acr text-right" id="acr"
                           name="garageInspectionDetailsDto.acr">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Excess (Rs.) :</label>
                <div class="col-sm-8">
                    <fmt:formatNumber var="policyDtoExcess"
                                      value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                      pattern="###,##0.00;(###,##0.00)" type="number"/>
                    <input readonly value="${policyDtoExcess}" placeholder=""
                           class="form-control form-control-sm text-right" id="excess1"
                           name="garageInspectionDetailsDto.excess1">
                    <input type="hidden"
                           value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                           id="excess" name="garageInspectionDetailsDto.excess">
                </div>
            </div>
            <input type="hidden" id="boldPercent"/>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Bald Tyre Penalty (Rs.)
                    :</label>
                <div class="col-sm-8">
                    <input value="${inspectionDetailsDto.garageInspectionDetailsDto.boldTyrePenaltyAmount}"
                           placeholder="Bald Tyre Penalty" class="form-control form-control-sm excess text-right"
                           id="boldTyrePenaltyAmount" name="garageInspectionDetailsDto.boldTyrePenaltyAmount">
                </div>
            </div>
            <input type="hidden" id="underPenaltyPercent"/>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Under Insurance Penalty (Rs.) :</label>
                <div class="col-sm-8">
                    <input value="${inspectionDetailsDto.garageInspectionDetailsDto.underInsurancePenaltyAmount}"
                           placeholder="Under Insurance Penalty"
                           class="form-control form-control-sm underpenaltyamount text-right"
                           id="underInsurancePenaltyAmount"
                           name="garageInspectionDetailsDto.underInsurancePenaltyAmount">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Payable Amount (Rs.) :</label>
                <div class="col-sm-8">
                    <input value="${isEmptyValue? '':inspectionDetailsDto.garageInspectionDetailsDto.payableAmount}"
                           placeholder="Payable Amount" class="form-control form-control-sm payableamount text-right"
                           id="payableAmount" name="garageInspectionDetailsDto.payableAmount">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Advance Amount (Rs.) <span
                        class="text-danger font-weight-bold">  *</span>:</label>
                <div class="col-sm-8">
                    <input value="${isEmptyValue? '':inspectionDetailsDto.garageInspectionDetailsDto.advancedAmount}"
                           placeholder="Advance Amount" class="form-control form-control-sm advancedamount text-right"
                           id="advancedAmount" name="garageInspectionDetailsDto.advancedAmount">
                </div>
            </div>
        </fieldset>
        <fieldset class="border p-2 mt-2">
            <h6> Assessor Remarks</h6>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Settlement Methods
                    <span class="text-danger font-weight-bold"> *</span>
                    :</label>
                <div class="col-sm-8">
                    <select name="garageInspectionDetailsDto.settlementMethod"
                            class="form-control form-control-sm settlementMethod"
                            id="settlementMethod">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_settlement_method WHERE settlement_id NOT IN (6)", "settlement_id", "value")}
                    </select>
                </div>
                <script>document.getElementById("settlementMethod").value = "${inspectionDetailsDto.garageInspectionDetailsDto.settlementMethod}";</script>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Offer Amount (Rs.) :</label>
                <div class="col-sm-8">
                    <input value="${isEmptyValue? '':inspectionDetailsDto.garageInspectionDetailsDto.offerAmount}"
                           placeholder="Offer Amount" class="form-control form-control-sm offeramount text-right" id=" "
                           name="garageInspectionDetailsDto.offerAmount">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Inspection Remarks :</label>
                <div class="col-sm-8">
                <textarea name="garageInspectionDetailsDto.inspectionRemark" class="form-control form-control-sm "
                          id=" ">${inspectionDetailsDto.garageInspectionDetailsDto.inspectionRemark}</textarea>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ARI and Salvage<span
                        class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${inspectionDetailsDto.garageInspectionDetailsDto.ariAndSalvage eq 'Yes' and not isEmptyValue or isRequested =='Y' ? 'checked' : ''}
                                    name="garageInspectionDetailsDto.ariAndSalvage" type="radio" value="Yes"
                                    class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${inspectionDetailsDto.garageInspectionDetailsDto.ariAndSalvage eq 'No' and not isEmptyValue ? 'checked' : ''}
                                    name="garageInspectionDetailsDto.ariAndSalvage" type="radio" value="No"
                                    class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>

        </fieldset>
    </c:if>
    <fieldset class="border p-2 mt-2 my-2">
        <jsp:include page="../assessor/assessorPayment.jsp"/>
    </fieldset>
    <fieldset class="border p-2 mt-2 my-2">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Special Remarks :</label>
            <div class="col-sm-8">
                <textarea name="garageInspectionDetailsDto.specialRemark" class="form-control form-control-sm "
                          id="RemarkText">${inspectionDetailsDto.garageInspectionDetailsDto.specialRemark}</textarea>
            </div>
        </div>
        <div class="float-right mt-1">
            <button type="submit" name="addGarageRemark"
                    id="addGarageRemark"
                    class="btn btn-primary float-right"
                    style="margin-left: 10px">
                Add Special Remarks
            </button>
        </div>
    </fieldset>
</div>

<script>
    function calculateBaldTyrePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            boldPercent: $("#boldPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=BaldTyrePenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#boldTyrePenaltyAmount").val(result);
                calculatePayableAmount();
            }
        });
    }

    function calculateUnderInsurancePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            underPenaltyPercent: $("#underPenaltyPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=UnderPenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underInsurancePenaltyAmount").val(result.trim());
                calculatePayableAmount();
            }
        });
    }

    function calculatePayableAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            excess: $("#excess").val(),
            underPenaltyAmount: $("#underInsurancePenaltyAmount").val(),
            boldTirePenaltyAmount: $("#boldTyrePenaltyAmount").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=PayableAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#payableAmount").val(result);
            }
        });
    }

    function calculateUnderInsPenaltyPerc() {
        var dataObj = {
            pav: $("#pav").val(),
            sumInsured: $("#sumInsuredVal").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateUnderInsPenaltyPerc";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underPenaltyPercent").val(parseFloat(result.trim()) < 0 ? '0.00' : result.trim());
                calculateUnderInsurancePenaltyAmount();
            }
        });
    }

    $("#pav").change(function () {
        calculateUnderInsPenaltyPerc();
    });

    // $(document).ready(function () {
    //     calculateUnderInsPenaltyPerc();
    // });

    $("#acr").change(function () {
        calculateBoldTyerPenaltyPerc();
        calculateUnderInsPenaltyPerc();
    });

    $("#boldTyrePenaltyAmount,#underInsurancePenaltyAmount").change(function () {
        calculatePayableAmount();
    });

    function calculateBoldTyerPenaltyPerc() {
        var wheelCount = 0;
        var boldWheelCount = 0;
        var boldTyrePercentage = "0.00";
        for (var i = 0; i < 7; i++) {
            var val = $("#cot_0_" + i).val();
            if ("N/A" != val) {
                wheelCount++;
            }
            if ("Bald" == val) {
                boldWheelCount++;
            }
        }
        switch (wheelCount) {
            case 2:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "20.00";
                        break;
                    case 2:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 3:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "10.00";
                        break;
                    case 2:
                        boldTyrePercentage = "30.00";
                        break;
                    case 3:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 4:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "20.00";
                        break;
                    case 3:
                        boldTyrePercentage = "30.00";
                        break;
                    case 4:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 6:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "10.00";
                        break;
                    case 3:
                        boldTyrePercentage = "20.00";
                        break;
                    case 4:
                        boldTyrePercentage = "30.00";
                        break;
                    case 5:
                        boldTyrePercentage = "40.00";
                        break;
                    case 6:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
        }
        $("#boldPercent").val(parseFloat(boldTyrePercentage) < 0 ? '0.00' : boldTyrePercentage);
        calculateBaldTyrePenaltyAmount();
    }

    $(".cot_0").change(function () {
        calculateBoldTyerPenaltyPerc();
    });

</script>
