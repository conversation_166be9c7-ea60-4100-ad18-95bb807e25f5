<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Vehicle Image</title>
    <!-- Bootstrap -->
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
   <%-- <link href="https://cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">--%>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://cdn.bootcss.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://cdn.bootcss.com/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script>
        function selectImage(id) {
            $("[id^=vehicleImage]").removeClass( "img-mag-selected" )
            $( "#vehicleImage"+id ).addClass( "img-mag-selected" );
        }

        $(document).ready(function(){
            $('[data-toggle="tooltip"]').tooltip({
                html:true,
                template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
            });
        });
    </script>

    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="viewerClaimImageList" value="${viewerClaimImageList}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="viewerClaimImageList" value="${historyViewerClaimImageList}" scope="request"/>
        </c:when>
    </c:choose>
</head>

<body>
<div class="container">
    <hr>
    <div class="image-set">
        <c:set var="index" value="1"/>
        <c:forEach var="claimImageDto" items="${viewerClaimImageList}">
            <figure>
            <a data-magnify="gallery" data-caption="Vehicle Photo" href="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}" data-toggle="tooltip" title="${claimImageDto.toolTip}">
                <img class="magnify-thumb" id="vehicleImage${claimImageDto.refNo}"  src="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}" alt="" width="100" height="95" alt="" border="0" onclick="selectImage(${claimImageDto.refNo})">
            </a>
                <figcaption>${index}</figcaption>
            </figure>
            <c:set var="index" value="${index=index+1}"/>
        </c:forEach>
    </div>
    <hr>
</div>
<%--<!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->--%>
<%--<script src="https://cdn.bootcss.com/jquery/1.12.4/jquery.min.js"></script>--%>
<%--<script src="https://cdn.bootcss.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>--%>
<!-- Include all compiled plugins (below), or include individual files as needed -->
<script src="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.js"></script>
<script>
    $('[data-magnify]').magnify({
        fixedContent: false,
        initMaximized: true,
        fixedModalPos: true
    });

</script>
</body>

</html>
