<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="">
    <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
        <fieldset class="border p-2 mt-2">
            <div class="justify-content-between">
                <h6 class="float-left">ARI</h6>
                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                    <span class="custom-control-description"></span>
                    <input id="isAriCheckbox" onclick="setAriInOrder()" type="checkbox" class="align-middle"
                        ${inspectionDetailsDto.ariInspectionDetailsDto.isAri eq 'Yes' and not isEmptyValue ? 'checked' : ''} />
                    <input type="hidden" name="ariInspectionDetailsDto.isAri" id="isAri"
                           value="${inspectionDetailsDto.ariInspectionDetailsDto.isAri}"/>
                    <span class="Checkmark" style="    top: -1px;left: 20px;"></span>
                </label>
            </div>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ARI in Order
                    <span class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="Yes" ${inspectionDetailsDto.ariInspectionDetailsDto.ariOrder eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                   name="ariInspectionDetailsDto.ariOrder" type="radio"
                                   class="align-middle ariinorder"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="No" ${inspectionDetailsDto.ariInspectionDetailsDto.ariOrder eq 'No' and not isEmptyValue ? 'checked' : ''}
                                   name="ariInspectionDetailsDto.ariOrder" type="radio"
                                   class="align-middle ariinorder"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>
            <h6 class="float-left">
                Salvage
            </h6>
            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                <span class="custom-control-description"></span>
                <input id="isSalvageCheckbox" onclick="setSalvageInOrder()" type="checkbox" class="align-middle"
                    ${inspectionDetailsDto.ariInspectionDetailsDto.isSalvage eq 'Yes' and not isEmptyValue ? 'checked' : ''} />
                <input type="hidden" name="ariInspectionDetailsDto.isSalvage" id="isSalvage"
                       value="${inspectionDetailsDto.ariInspectionDetailsDto.isSalvage}"/>
                <span class="Checkmark" style="    top: -1px;left: 50px;"></span>
            </label>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Salvage in Order
                    <span class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="Yes" ${inspectionDetailsDto.ariInspectionDetailsDto.salvageOrder eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                   name="ariInspectionDetailsDto.salvageOrder" type="radio"
                                   class="align-middle salvageinorder"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="No" ${inspectionDetailsDto.ariInspectionDetailsDto.salvageOrder eq 'No' and not isEmptyValue ? 'checked' : ''}
                                   name="ariInspectionDetailsDto.salvageOrder" type="radio"
                                   class="align-middle salvageinorder"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Assessor Special Remarks :</label>
                <div class="col-sm-8">
                    <textarea name="ariInspectionDetailsDto.assessorSpecialRemark" class="form-control form-control-sm "
                              id=" ">${inspectionDetailsDto.ariInspectionDetailsDto.assessorSpecialRemark}</textarea>
                </div>
            </div>
        </fieldset>
    </c:if>
    <fieldset class="border p-2 mt-2 my-2">
        <jsp:include page="../assessor/assessorPayment.jsp"/>
    </fieldset>

</div>
<script type="text/javascript">
    function setAriInOrder() {
        if ($('#isAriCheckbox').is(':checked')) {
            $("#isAri").val("Yes");
            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('disabled', false);
        } else {
            $("#isAri").val("No");
            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('checked', false);
            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('disabled', true);
        }
    }

    function setSalvageInOrder() {
        if ($('#isSalvageCheckbox').is(':checked')) {
            $("#isSalvage").val("Yes");
            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('disabled', false);
        } else {
            $("#isSalvage").val("No");
            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('checked', false);
            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('disabled', true);
        }
    }

    $(document).ready(function () {
        setAriInOrder();
        setSalvageInOrder();
    });
</script>