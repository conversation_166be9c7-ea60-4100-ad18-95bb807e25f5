<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmClaimPanelUserList" id="frmClaimPanelUserList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Mobile Application Details</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mt-2">
                            <h6 class="float-left">Assessor Application Inspection Details</h6>
                            <!-- Button trigger modal -->
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div id="accordion" class="accordion">
                            <div class="card">
                                <div class="card-header" id="headingOne">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                           data-target="#collapseOne"
                                           aria-expanded="true" aria-controls="collapseOne">
                                            Search Here <i class="fa fa-search"></i>
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                                     data-parent="#accordion">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                        Date/Time </label>
                                                    <div class="col-sm-8">
                                                        <input name="txtFromDate" class="form-control form-control-sm"
                                                               placeholder="From Date" id="txtFromDate" type="text"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label for="txtAssignUser" class="col-sm-4 col-form-label"> Assign
                                                        User </label>
                                                    <div class="col-sm-8">
                                                        <input name="txtAssignUser" id="txtAssignUser" type="text"
                                                               class="form-control form-control-sm"
                                                               placeholder="Assign User">
                                                    </div>
                                                </div>
                                                <div class="form-group row" id="jobNumberDiv">
                                                    <label for="txtJobNumber" class="col-sm-4 col-form-label"> Job
                                                        Number</label>
                                                    <div class="col-sm-8">
                                                        <input name="txtJobNumber" id="txtJobNumber"
                                                               class="form-control form-control-sm"
                                                               placeholder="Job Number">
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                        Date/Time </label>
                                                    <div class="col-sm-8">
                                                        <input name="txtToDate" id="txtToDate" type="text"
                                                               class="form-control form-control-sm"
                                                               placeholder="To Date">
                                                    </div>
                                                </div>
                                                <div class="form-group row" id="claimNoDiv">
                                                    <label for="txtClaimNo" class="col-sm-4 col-form-label"> Claim
                                                        No </label>
                                                    <div class="col-sm-8">
                                                        <input name="txtClaimNo" class="form-control form-control-sm"
                                                               placeholder="Claim No" id="txtClaimNo" type="text"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="form-group row" id="inspectionTypeDiv">
                                                    <label for="txtInspectionType" class="col-sm-4 col-form-label">
                                                        Inspection Type </label>
                                                    <div class="col-sm-8">
                                                        <select name="txtInspectionType" id="txtInspectionType"
                                                                class="form-control form-control-sm">

                                                            <option value="">Please Select One</option>
                                                            <option value="1">On site Inspection</option>
                                                            <option value="2">Off site Inspection</option>
                                                            <option value="3">Underwiting Inspection</option>
                                                            <option value="4">Garage Inspection</option>
                                                            <option value="5">DR Insepection</option>
                                                            <option value="6">Supplimantary Inspection</option>
                                                            <option value="7">After Repair inspection</option>
                                                            <option value="8">Desktop Assesment</option>
                                                            <option value="9">Salvage Inspection</option>
                                                            <option value="10">Investigation</option>
                                                            <option value="10">Investigation</option>
                                                            <option value="11">Call Estimate</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 text-right">
                                                <button class="btn btn-primary" type="button" name="cmdSearch"
                                                        id="cmdSearch"
                                                        onclick="search()">Search
                                                </button>
                                                <a class="btn btn-secondary" type="button" name="cmdClose"
                                                   id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                                </a>
                                                <hr>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>Ref No</th>
                                <th></th>
                                <th>Claim No</th>
                                <th>Inspection Type</th>
                                <th>Job Number</th>
                                <th>Assign User</th>
                                <th>Vehicle No</th>
                                <th>Accident Date/Time</th>
                                <th>MCMS Notification Received Date/Time</th>
                                <th>App Notification Read Date/Time</th>
                                <th>App Notification Read Status</th>
                                <%--<th class="min-mobile"></th>--%>

                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/mobileapplicationinspectionview-datatables.js?v5"></script>
<script type="text/javascript">

    function myFunction() {
        $('#inputUser,#inputDateTime ,#userStatus, #userId ').val('').attr('disabled', false);
        $('#userPanelId').val('');
        $('#userPanelId').multiselect('enable');
        $('#userPanelId').multiselect('refresh');
    }

    function CloseReload() {
        location.reload();
    }

</script>
<script type="text/javascript">

    $("#txtFromDate").datetimepicker({
        sideBySide: true,
        format: 'YYYY-MM-DD HH:mm',
        //  maxDate:new Date(currentDate),
        icons: {
            time: "fa fa-clock-o",
            date: "fa fa-calendar",
            up: "fa fa-arrow-up",
            down: "fa fa-arrow-down"
        }
    });

    $("#txtToDate").datetimepicker({
        sideBySide: true,
        format: 'YYYY-MM-DD HH:mm',
        icons: {
            time: "fa fa-clock-o",
            date: "fa fa-calendar",
            up: "fa fa-arrow-up",
            down: "fa fa-arrow-down"
        }
    });

    $('#addClaimUserbtn').click(function (e) {
        // e.preventDefault();
        var userPanelId = $('#userPanelId').val();
        $('#userPanelIds').val(userPanelId);
        var formData = $('#frmClaimPanelUser').serialize();
        $.ajax({
            url: contextPath + "/ClaimPanelUserController/save",
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    notify(obj, "success");
                }
                else {
                    notify("Fail to Add User", "fail");
                }
            }
        });
    });
    $("#frmClaimPanelUser").submit(function (e) {
        e.preventDefault();
    });
    $(document).ready(function () {

        // $('#userPanelId').multiselect({
        //     onChange: function () {
        //         $('#frmClaimPanelUser').formValidation('revalidateField', 'userPanelId');
        //     }
        // });

        $('#frmClaimPanelUser')
            .formValidation({
                framework: 'bootstrap',
                excluded: 'disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {

                    userId: {
                        // object with has 'myClass' class.
                        validators: {
                            notEmpty: {
                                message: 'Please Select'
                            }
                        }
                    },
                    // userPanelIds: {
                    //     validators: {
                    //         choice: {
                    //             min: 1,
                    //             max: 4,
                    //             message: 'Please Select'
                    //         },
                    //     }
                    // },
                    userStatus: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select Status'
                            }
                        }
                    }
                }
            })
            .on('success.form.fv', function (e) {
                $(e.target).data('formValidation').disableSubmitButtons(false);

            });
    });


</script>
</body>
</html>
