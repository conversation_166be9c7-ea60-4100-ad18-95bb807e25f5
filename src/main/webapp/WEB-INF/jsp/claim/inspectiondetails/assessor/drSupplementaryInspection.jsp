<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="">
    <c:if test="${inspectionDetailsDto.isVehicleAvailable ne 'N'}">
        <fieldset class="border p-2 mt-2">
                <%--<h6> DR & Supplementary</h6>--%>
                <%--<hr class="my-1">--%>
            <div class="form-group row" id="SumInsuredRemove">
                <label class="col-sm-4 col-form-label">Sum Insured (Rs) :</label>
                <div class="col-sm-8">
                    <input name="drSuppInspectionDetailsDto.sumInsured" placeholder="Sum Insured"
                           value="${isEmptyValue? '':inspectionDetailsDto.drSuppInspectionDetailsDto.sumInsured}"
                           class="form-control form-control-sm  suminsured text-right" id=" ">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
                <div class="col-sm-8">
                    <input name="drSuppInspectionDetailsDto.acr" placeholder="ACR"
                           value="${inspectionDetailsDto.drSuppInspectionDetailsDto.acr}"
                           class="form-control form-control-sm acr text-right" id=" ">
                </div>
            </div>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Assessor Remarks :</label>
                <div class="col-sm-8">
                    <textarea name="drSuppInspectionDetailsDto.assessorRemark" class="form-control form-control-sm "
                              id=" ">${inspectionDetailsDto.drSuppInspectionDetailsDto.assessorRemark}</textarea>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ARI and Salvage<span
                        class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="Yes" ${inspectionDetailsDto.drSuppInspectionDetailsDto.ariAndSalvage eq 'Yes' and not isEmptyValue or isRequested =='Y' ? 'checked' : ''}
                                   name="drSuppInspectionDetailsDto.ariAndSalvage" type="radio"
                                   class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="No" ${inspectionDetailsDto.drSuppInspectionDetailsDto.ariAndSalvage eq 'No' and not isEmptyValue ? 'checked' : ''}
                                   name="drSuppInspectionDetailsDto.ariAndSalvage" type="radio"
                                   class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>
        </fieldset>
    </c:if>
    <fieldset class="border p-2 mt-2 my-2">
        <jsp:include page="../assessor/assessorPayment.jsp"/>
    </fieldset>

</div>