<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

    String ERROR = "";
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script language="javascript" type="text/javascript">

        var currentDate = '${Current_Date}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);

        });
        //         $(function () {
        //             //
        //             //buttonImage: '/image/common/calendar.gif',
        //             var d1 = document.frmForm.txtFromDate.value;
        //             $("#txtFromDate").datepicker({
        //                 changeMonth: true,
        //                 changeYear: true,
        //                 yearRange: '1940:2099',
        //                 minDate: '-70y',
        //                 maxDate: '0d',
        //                 onClose: function (dateText, inst) {
        //                     document.getElementById("txtToDate").focus();
        //                 }
        //
        //             });
        //             $("#txtFromDate").datepicker('option', {dateFormat: "yy-mm-dd"});
        //             document.frmForm.txtFromDate.value = d1;
        //
        //         });
        //         $(function () {
        //             //
        //             //buttonImage: '/image/common/calendar.gif',
        //             var d1 = document.frmForm.txtToDate.value;
        //             $("#txtToDate").datepicker({
        //                 changeMonth: true,
        //                 changeYear: true,
        //                 yearRange: '1940:2099',
        //                 minDate: '-70y',
        //                 maxDate: '0d',
        //                 onClose: function (dateText, inst) {
        // //                    document.getElementById("txtToDate").focus();
        //                 }
        //
        //             });
        //             $("#txtToDate").datepicker('option', {dateFormat: "yy-mm-dd"});
        //             document.frmForm.txtToDate.value = d1;
        //
        //         });

        $(document).ready(function () {
            selectAssignedByList();
        });

        //------------Start JQuery Script---------------------

        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }, "Cancel": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }


        //------------End JQuery Script---------------------


        //================Others=========================================================
        function setNextButtonDisable() {

        }

        function init() {
            setNextButtonDisable();
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }


        function setLoading() {
            parent.document.getElementById("cell1").style.display = "block";
            parent.document.getElementById("loading").style.display = "block";

        }
    </script>
    <style>
        #demo-dt-basic tbody tr :nth-child(1){
           display: flex;
            align-items: center;
            /*justify-content: space-evenly;*/

        }
        .badge{
            display: flex;
            justify-content: center;
        }
.text_center_btn{
    justify-content: center;
}
    </style>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid" style="width: 100%; overflow: hidden">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="P_N_JOB_NO" id="P_N_JOB_NO" type="hidden"/>
        <input name="P_N_REF_NO" id="P_N_REF_NO" type="hidden"/>
        <div class="row">
            <div class="col-md-12 bg-dark py-2">
                <h5> Allocated Inspections List</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 py-1 mt-3">
                <div class="ErrorNote"><%=ERROR%>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-md-4 col-form-label"> From Date </label>
                                            <div class="col-md-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-md-4 col-form-label"> Vehicle
                                                Number</label>
                                            <div class="col-md-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-md-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-md-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-md-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-md-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number">
                                            </div>
                                        </div>

                                        <c:if test="${TYPE == 2}">
                                            <div class="form-group row">
                                                <label for="txtRte" class="col-md-4 col-form-label">
                                                    RTE Name</label>
                                                <div class="col-md-8">
                                                    <select name="txtRte" id="txtRte"
                                                            class="form-control form-control-sm" onchange="selectAssessorList(this.value)">
                                                        <option value="">Please Select One</option>
                                                        <c:forEach var="listDto" items="${rteList}">
                                                            <option value="${listDto.userId}">${listDto.userId}</option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label for="txtInspectionType" class="col-md-4 col-form-label">
                                                    Inspection Type</label>
                                                <div class="col-md-8">
                                                    <select name="txtInspectionType" id="txtInspectionType"
                                                            class="form-control form-control-sm">
                                                            ${DbRecordCommonFunctionBean.getPopupList("claim_inspection_type", "inspection_type_id", "inspection_type_desc")}
                                                    </select>
                                                </div>
                                            </div>
                                        </c:if>

                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-md-4 col-form-label"> To Date </label>
                                            <div class="col-md-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-md-4 col-form-label"> Cover Note
                                                Number </label>
                                            <div class="col-md-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-md-4 col-form-label"> Status</label>
                                            <div class="col-md-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <c:if test="${TYPE != 3}">
                                                        <option value="">ALL</option>
                                                        <option value="9">APPROVED</option>
                                                        <option value="29">ASSIGNED</option>
                                                        <option value="7">ATTENDED</option>
                                                        <option value="10">CLAIM CHANGE REQUESTED</option>
                                                        <option value="8">SUBMITTED</option>
                                                        <option value="69">REVOKED</option>
                                                    </c:if>
                                                    <c:if test="${TYPE == 3}">
                                                        <option value="29">ASSIGNED</option>
                                                    </c:if>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtJobNumber" class="col-md-4 col-form-label"> Job
                                                Number</label>
                                            <div class="col-md-8">
                                                <input name="txtJobNumber" id="txtJobNumber"
                                                       class="form-control form-control-sm" placeholder="Job Number">
                                            </div>
                                        </div>

                                        <c:if test="${TYPE == 2}">
                                            <div class="form-group row">
                                                <label for="txtAssessor" class="col-md-4 col-form-label">
                                                    Assessor</label>
                                                <div class="col-md-8">
                                                    <select name="txtAssessor" id="txtAssessor"
                                                            class="form-control form-control-sm">
                                                        <option value="">Please Select One</option>
                                                        <c:forEach var="listDto" items="${assessorList}">
                                                            <option value="${listDto.code}">${listDto.firstName}
                                                                ( ${listDto.name} )
                                                            </option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </c:if>

                                        <c:if test="${TYPE == 2}">
                                            <div class="form-group row">
                                                <label for="txtAssignedBy" class="col-md-4 col-form-label">
                                                    CC Assigned By</label>
                                                <div class="col-md-8">
                                                    <select name="txtAssignedBy" id="txtAssignedBy"
                                                            class="form-control form-control-sm">
                                                        <option value="">Please Select One</option>
                                                        <c:forEach var="listDto" items="${assignedByList}">
                                                            <option value="${listDto.inp_userid}">${listDto.inp_userid}</option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </c:if>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                                <%--<div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-md-4 col-form-label"> 3<sup>rd</sup>
                                                Party Vehicle Number</label>
                                            <div class="col-md-8">
                                                <input name="txt3rdVehicleNumber" id="txt3rdVehicleNumber"
                                                        class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                    </div>
                                </div>--%>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <input type="hidden" value="${PENDING_INSPECTION_CLAIM_NO}" name="type" id="pendingInspectionClaimNo">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-danger"></i>&nbsp;&nbsp;Late
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-warning"></i> &nbsp;&nbsp; On
                                    Site</p>
                                <%--<h6>Policy Suspend &nbsp;&nbsp;<span class="badge badge-pill badge-secondary border">&nbsp;</span></h6>--%>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-history text-warning"></i>&nbsp;&nbsp;Exceed
                                    24 Hours </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-history text-danger"></i> &nbsp;&nbsp;
                                    Exceed 48 Hours</p>
                                <%--<h6>Policy Suspend &nbsp;&nbsp;<span class="badge badge-pill badge-secondary border">&nbsp;</span></h6>--%>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-history text-info"></i> &nbsp;&nbsp;
                                    Online Inspection</p>
                            </div>
                        </div>
                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim Result</h6>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th>ref no</th>
                                                <th width="40px">No</th>
                                                <th>Job No</th>
                                                <th>Claim No</th>
                                                <th>Vehicle No</th>
                                                <th>Inspection Type</th>
                                                <th>CC Assigned By</th>
                                                <th>Assigned Assessor</th>
                                                <th>Assigned Date / Time</th>
                                                <c:if test="${TYPE ==2}">
                                                    <th>Assigned Rte</th>
                                                    <th>Assigned Date / Time</th>
                                                </c:if>
                                                <th>Job Status</th>
                                                <th>Claim Status</th>
                                                <c:if test="${TYPE !=2}">
                                                    <th class="min-mobile"></th>
                                                </c:if>

                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <c:if test="${successMessage!=null && successMessage!=''}">
        <script type="text/javascript">
            notify('${successMessage}', "success");
        </script>
    </c:if>
    <c:if test="${errorMessage!=null && errorMessage!=''}">
        <script type="text/javascript">
            notify('${errorMessage}', "danger");
        </script>
    </c:if>
    <script>
        $('table#demo-dt-basic').parent().addClass('scroll')

        winresize();

        function winresize() {
            var width = $(window.parent.$('body')).width();
            $('body').width(width - 40);
        }

        $(window.parent).resize(function () {
            winresize();
        });

        function selectAssessorList(rte) {
            $("#txtAssessor option").remove();
            $("#txtAssessor").append('<option value="" >Please Select One</option>');
            $.ajax({
                url: contextPath + "/AssessorAllocationController/assessorListByRte",
                data: {
                    rteCode: rte
                },
                type: 'POST',
                success: function (result) {
                    var response = JSON.parse(result);
                    var options = '';
                    for (var i = 0; i < response.length; i++) {
                        options += "<option value='" + response[i].code + "'>" + response[i].firstName.concat(' (' + response[i].name + ')') + "</option>";
                    }
                    $("#txtAssessor").append(options).trigger("chosen:updated");
                }
            })
        }

        function selectAssignedByList() {
            $("#txtAssignedBy option").remove();
            $("#txtAssignedBy").append('<option value="" >Please Select One</option>');
            $.ajax({
                url: contextPath + "/InspectionDetailsController/assignedByList",
                type: 'GET',
                dataType: "json",
                success: function (result) {
                    var options = '';
                    for (var i = 0; i < result.length; i++) {
                        options += "<option value='" + result[i] + "'>" + result[i] + "</option>";
                    }
                    $("#txtAssignedBy").append(options).trigger("chosen:updated");
                }
            })
        }
    </script>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/assessor-job-datatables.js?v4"></script>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });
</script>
</body>
</html>
