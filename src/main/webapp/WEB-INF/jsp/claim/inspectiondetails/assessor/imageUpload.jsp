<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<div class="scroll">
    <script src="${pageContext.request.contextPath}/resources/js/imagePre.js"></script>
    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="claimImageDtoList" value="${claimImageDtoList}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="claimImageDtoList" value="${historyClaimImageDtoList}" scope="request"/>
        </c:when>
    </c:choose>
    <form name="frmDocumentUpload" id="frmDocumentUpload" method="post">
        <input type="hidden" name="claimNo" id="claimNo" value="${inspectionDetailsDto.claimNo}">
        <input type="hidden" name="jobRefNo" id="jobRefNo" value="${inspectionDetailsDto.refNo}">
        <div class="container-fluid">
            <div class="row">
                <fieldset class="col-md-12 border mt-2">
                    <div class="row">
                        <div class="col-sm-12 col-form-label">
                            <h6 class="float-left">Vehicle Image</h6>
                            <c:if test="${PREVIOUS_INSPECTION != 'Y' && (G_USER.userId == inspectionDetailsDto.assessorAllocationDto.assessorDto.userName or G_USER.accessUserType eq 1)}">
                                <c:if test="${(inspectionDetailsDto.assessorAllocationDto.recordStatus==0
                                    || inspectionDetailsDto.assessorAllocationDto.recordStatus==29)}">
                                    <button id="cmdDelete" type="button" name="cmdUpload"
                                            class="btn btn-red text-danger pull-right ml-2"
                                            onclick="deleteSelectedImages()">Delete
                                    </button>
                                </c:if>
                                <button id="cmdUpload" type="button" name="cmdUpload"
                                        class="btn btn-primary pull-right ml-2"
                                        onclick="docUpload()">Upload
                                    Here
                                </button>

                            </c:if>

                            <input type="hidden" id="deleteImages" name="deleteImages"/>
                        </div>
                        <div class="col-md-12">
                            <hr class="my-2">
                            <c:set var="cnt" value="1"/>
                            <c:forEach var="claimImageDto" items="${claimImageDtoList}">
                                <div class="uploadfile-delet imgupload">
                                    <figure>
                                        <figcaption>${cnt}</figcaption>
                                        <c:set var="cnt" value="${cnt=cnt+1}"/>
                                    </figure>
                                    <a data-magnify="gallery" data-caption="Vehicle Photo"
                                       href="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}"
                                       data-toggle="tooltip" title="" class="preview float-none">
                                        <img class="magnify-thumb" id="vehicleImage${claimImageDto.refNo}"
                                             src="${pageContext.request.contextPath}/ImageThumbViewController?refNo=${claimImageDto.refNo}"
                                             alt="" width="100%" height="95" alt="" border="0">

                                    </a>
                                    <div class="row">
                                        <div class="col">
                                            <label class="custom-control custom-checkbox float-left col-form-label check-container"
                                                   style="right: 8%; bottom: auto;">
                                                <input id="intendClaim_N" name="intendClaim"
                                                       onclick="setDeleteImages('${claimImageDto.refNo}')"
                                                       type="checkbox"
                                                       class="align-middle"/>
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                        <div class="col">
                                            <a href="${pageContext.request.contextPath}/DocumentDownloadController?refNo=${claimImageDto.refNo}"
                                               class="mt-2 pointer" title="Download" style="width: auto; height: auto;"><i
                                                    class="fa fa-download text-success"></i></a>
                                        </div>
                                    </div>


                                </div>
                            </c:forEach>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </form>
    <script src="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.js"></script>
    <script>

        $('[data-magnify]').magnify({
            fixedContent: false,
            initMaximized: false,
            fixedModalPos: true
        });

        function reloadImageUploadPage() {
            document.frmDocumentUpload.action = "${pageContext.request.contextPath}/InspectionDetailsController/imageUpload";
            document.frmDocumentUpload.submit();
        }

        function docUpload() {
            totalImages = 0;
            uploadedImages = 0;
            $('#imageCount').html("");
            $(document.getElementById('imgUploadModal')).modal({
                backdrop: 'static',
                keyboard: false,
                refresh: true,
                show: true
            });
        }

        function setDeleteImages(refId) {
            var images = $('#deleteImages').val();
            if (images == '') {
                images = refId;
            } else {
                images = images + ',' + refId;
            }

            $('#deleteImages').val(images);

        }

        function deleteSelectedImages() {

            var dataObj = {
                deleteImages: $("#deleteImages").val(),
                jobRefNo: $("#jobRefNo").val(),
                claimNo: $("#claimNo").val()
            };

            var URL = "${pageContext.request.contextPath}/InspectionDetailsController/deleteImages";
            $.ajax({
                url: URL,
                type: 'POST',
                data: dataObj,
                success: function (result) {
                    var messageType = JSON.parse(result);
                    var message = "";
                    if (messageType == "SUCCESS") {
                        message = "Successfully deleted";
                        notify(message, "success");
                    } else {
                        message = "Can not be deleted";
                        notify(message, "danger");
                    }
                    loadImageUploadViewer();

                }
            });

        }
    </script>
</div>

