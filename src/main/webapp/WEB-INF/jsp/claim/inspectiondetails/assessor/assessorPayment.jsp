<%@taglib prefix="c" uri="jakarta.tags.core" %>
<h6> Assessor Payment</h6>
<hr class="my-1">
<div class="form-group row">
    <label class="col-sm-4 col-form-label">Job Type
        <span class="text-danger font-weight-bold"> *</span>
        :</label>
    <div class="col-sm-8">
        <select name="jobType" class="form-control form-control-sm jobtype"
                id="jobType">
            <option value="0">-- Please Select --</option>
<%--            <option value="1">DAY</option>--%>
<%--            <option value="2">NIGHT</option>--%>
            <c:forEach var="item" items="${dayTypeList}">
                <option value="${item.value}">${item.label}</option>
            </c:forEach>
        </select>
        <script>document.getElementById("jobType").value = "${inspectionDetailsDto.jobType}";</script>
    </div>
</div>

<div class="form-group row">
    <label class="col-sm-4 col-form-label">Inspection Time
        <span class="text-danger font-weight-bold"> *</span>
        :</label>
    <div class="col-sm-8">
        <select name="assessorFeeDetailId" class="form-control form-control-sm jobtype"
                id="assessorFeeDetailId">
            <option value="0">-- Please Select --</option>
<%--            <option value="0">-- Please Select --</option>--%>
<%--            <c:forEach var="item" items="${inspectionTimeList}">--%>
<%--                <option value="${item.value}">${item.label}</option>--%>
<%--            </c:forEach>--%>
        </select>
<%--        <script>document.getElementById("jobType").value = "${inspectionDetailsDto.jobType}";</script>--%>
    </div>
</div>

<%--<div class="form-group">
    <label for="jobType">Day Type</label>
    <select class="form-control" id="jobType" name="jobType" required>
        <option value="">-- Select Day Type --</option>
        <c:forEach var="item" items="${dayTypeList}">
            <option value="${item.value}">${item.label}</option>
        </c:forEach>
    </select>
</div>--%>

<%--<div class="form-group">
    <label for="inspectionTypeId">Inspection Type</label>
    <select class="form-control" id="inspectionTypeId" name="inspectionTypeId" required>
        <option value="">-- Select Inspection Type --</option>

        <c:forEach var="item" items="${inspectionTypeList}">
            <option value="${item.value}">${item.label}</option>
        </c:forEach>
    </select>
</div>--%>

<div class="form-group row">
    <label class="col-sm-4 col-form-label">Assigned Location
        <%--<span class="text-danger font-weight-bold"> *</span> --%>
        :</label>
    <div class="col-sm-8">
        <input name="assignedLocation" class="form-control form-control-sm"
               value="${ACTION eq 'SAVE' ? inspectionDetailsDto.assessorAllocationDto.placeOfinspection : inspectionDetailsDto.assignedLocation}"
               readonly>
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-4 col-form-label">Place of Inspection
        <%--<span class="text-danger font-weight-bold"> *</span> --%>
        :</label>
    <div class="col-sm-8">
        <input name="placeOfInspection" class="form-control form-control-sm"
               value="${ACTION eq 'SAVE' ? inspectionDetailsDto.assessorAllocationDto.placeOfinspection : inspectionDetailsDto.placeOfInspection}"/>
    </div>
</div>

<div class="form-group row">
    <label class="col-sm-4 col-form-label">Commence Assessment Location
        <%--<span class="text-danger font-weight-bold"> *</span> --%>
        :</label>
    <div class="col-sm-8">
        <input name="currentLocation" class="form-control form-control-sm"
               value="${ACTION eq 'SAVE' ? inspectionDetailsDto.assessorAllocationDto.currentLocation : inspectionDetailsDto.currentLocation}"/>
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-4 col-form-label">Mileage
        <span class="text-danger font-weight-bold"> *</span>
        :</label>
    <div class="col-sm-8">
        <input class="form-control form-control-sm miles" id="mileage"
               name="mileage" value="${isEmptyValue? '':inspectionDetailsDto.mileage}"/>
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-4 col-form-label">Cost of Call (Rs)
        <%--<span class="text-danger font-weight-bold"> *</span> --%>
        :</label>
    <div class="col-sm-8">
        <input class="form-control form-control-sm costofcall text-right"
               id="costOfCall"
               name="costOfCall" value="${isEmptyValue?'50.00':inspectionDetailsDto.costOfCall}">
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-4 col-form-label">Other Fee (Rs)
        <%--<span class="text-danger font-weight-bold"> *</span> --%>
        :</label>
    <div class="col-sm-8">
        <input class="form-control form-control-sm otherchage text-right"
               id="otherFee"
               name="otherFee" value="${isEmptyValue? '':inspectionDetailsDto.otherFee}">
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-4 col-form-label">Total Fee (Rs)
        <%--<span class="text-danger font-weight-bold"> *</span> --%>
        :</label>
    <div class="col-sm-8">
        <input class="form-control form-control-sm text-right"
               id="totalAssessorFee"
               name="totalAssessorFee"
               value="${isEmptyValue? '':inspectionDetailsDto.totalAssessorFee}" readonly>
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-4 col-form-label">Description
        <%--<span class="text-danger font-weight-bold"> *</span> --%>
        :</label>
    <div class="col-sm-8">
        <textarea name="feeDesc" class="form-control form-control-sm"
                  id=" ">${inspectionDetailsDto.feeDesc}</textarea>
    </div>
</div>