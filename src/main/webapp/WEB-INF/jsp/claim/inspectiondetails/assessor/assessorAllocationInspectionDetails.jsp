<%--
  Created by IntelliJ IDEA.
  User: Asiri
  Date: 3/13/2018
  Time: 4:34 PM
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<html>
<head>
    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="inspectionDetailsDto" value="${inspectionDetailsDto}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="inspectionDetailsDto" value="${previousInspectionDto}" scope="request"/>
        </c:when>
    </c:choose>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">

    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/inspectiondetails-form-validations.js?v5"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/assessor.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>

    <!-- Generic page styles -->
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>

    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>

    <script>
        showLoader();
        $(document).ready(function () {
            loadImageUploadViewer();
        });

        function loadImageUploadViewer() {
            $("#imageUploadContainer").load("${pageContext.request.contextPath}/InspectionDetailsController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${inspectionDetailsDto.assessorAllocationDto.refNo}");
        }

        function resizeIframe(obj) {

            obj.style.height = obj.contentWindow.document.body.scrollHeight + 'px';
        }

        var documentUploadIds = [];


        $(document).ready(function () {
            loadSpecialRemarks();

        });

    </script>
    <script type="text/javascript">
        var val = $('#assessorRemark').val();
        if (val !== "") {
            $('#addSpecialRemark').prop('disabled', true);
        } else {
            $('#addSpecialRemark').prop('disabled', false);
            console.log("sdsds");
        }
    </script>
</head>

<body class="scroll" onload="hideLoader()">
<div class="container-fluid scroll">
    <div class="row header-bg bg-dark">
        <div class="col-sm-12 py-2" >
            <h6 class="float-left text-dark hide-for-small"> Inspection Report Details</h6>


            <c:if test="${inspectionDetailsDto.isVehicleAvailable eq 'N'}">

                <div id="claimStampContainer" class="stamp-container3">
                    <img src="${pageContext.request.contextPath}/resources/stamps/vehicle_not_available.png"
                         class="ml-3" width="100" height="100">
                </div>

            </c:if>

            <c:if test="${inspectionDetailsDto.assessorAllocationDto.recordStatus eq '23'}">

                <div id="claimStampContainer" class="stamp-container3" style="left: 5vw">
                    <img src="${pageContext.request.contextPath}/resources/stamps/finalrejected.png"
                         class="ml-3" width="100" height="100">
                </div>

            </c:if>


            <%--<c:if test="${inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo ne '' }">--%>
            <%--<h6 class="text-dark float-right">Vehicle No--%>
            <%--: ${inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}</h6><br>--%>
            <%--</c:if>--%>

            <c:if test="${inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo eq '' }">
                <h6 class="text-dark float-right">Cover Note No
                    : ${inspectionDetailsDto.assessorAllocationDto.claimsDto.coverNoteNo}</h6><br>
            </c:if>
            <%--<h6 class="text-dark float-right" style="margin-right: -162px">Claim No--%>
            <%--: ${inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}</h6>--%>
            <p class="text-danger float-right mr-3">ISF Claim No
                : ${not empty inspectionDetailsDto.assessorAllocationDto.claimsDto.isfClaimNo  ? inspectionDetailsDto.assessorAllocationDto.claimsDto.isfClaimNo:'PENDING'}
            </p>
        </div>
<%--        <div class="col-sm-12 py-2" style="--%>
<%--            display: flex;--%>
<%--            flex-direction: row;--%>
<%--            justify-content: space-around;--%>
<%--            align-items: center;--%>
<%--            background: #a7d1d6;--%>
<%--        ">--%>
<%--            <h6 style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                ${(inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product eq "") || (null eq inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product) ? "N/A" : inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product }--%>
<%--            </h6>--%>
<%--            <h6 id="service-factor-header" style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                Service Factors--%>
<%--            </h6>--%>
<%--        </div>--%>
    </div>
    <div class="row">
        <div class="col-sm-12">

            <div class="stamp-container">
                <c:if test="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.categoryDescription eq 'VIP'}">
                    <img src="${pageContext.request.contextPath}/resources/stamps/vip.png"
                         class="stamp-container-vip"
                    >
                </c:if>
            </div>
            <div class="f1-steps">
                <div class="f1-progress">
                    <div class="f1-progress-line" data-now-value="10" data-number-of-steps="5"
                         style="width: 70%;"></div>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimStatus>=1?"active":""}">
                        1
                    </div>
                    <p>Call Center</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">2</div>
                    <p>Assessor Coordinator</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">3</div>
                    <p>Assessor</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon">4</div>
                    <p>Motor Engineer</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon">5</div>
                    <p>Claim Handler</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">

        <fieldset class="col-md-12">
            <%--<h6> Inspection Details</h6>--%>
            <%--<hr class="my-1">--%>
            <form id="frmMain" name="frmMain" onsubmit="removeDisableFormInputs('#frmMain');">
                <input type="hidden" name="ACTION" value="${ACTION}"/>
                <input type="hidden" value="${TYPE}" name="TYPE"/>
                <input type="hidden" value="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}"
                       name="inspectionType" id="inspectionType"/>

                <c:if test="${ACTION eq 'SAVE'}">
                    <c:set var="isEmptyValue" value="true" scope="request"/>
                </c:if>
                <c:if test="${ACTION eq 'UPDATE'}">
                    <c:set var="isEmptyValue" value="false" scope="request"/>
                </c:if>
                <c:if test="${inspectionDetailsDto.recordStatus eq 8}">
                    <c:set var="ACTION_TYPE" value="SUBMITTED" scope="request"/>
                </c:if>
                <c:if test="${inspectionDetailsDto.recordStatus ne 8}">
                    <c:set var="ACTION_TYPE" value="DRAFT" scope="request"/>
                </c:if>
                <c:if test="${IS_GARAGEINSPECTION == 'Y'}">
                    <c:set var="isEmptyValue" value="false" scope="request"/>
                </c:if>
                <div class="row">

                    <fieldset class="col-md-7 border scroll" style="height: calc(100vh - 160px);"
                              id="InspectionReportDetails">
                        <div id="accordionOne">
                            <div class="card mt-3">
                                <div class="card-header p-0" id="heading1">
                                    <h5 class="mb-0">
                                        <input type="hidden" id="secName" name="secName"
                                               value="Inspection Report Details-${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse1"
                                           aria-expanded="false" aria-controls="collapse1">
                                            Inspection Report
                                            Details-${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse1" class="collapse show" aria-labelledby="heading1"
                                     data-parent="#accordionOne">
                                    <input type="hidden" name="claimNo" id="claimNo"
                                           value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}">
                                    <div class="card-body p-lg-3 p-2">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Code of Assessor
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                    <div class="col-sm-8">
                                                                <span class="label_Value input-view"
                                                                > ${inspectionDetailsDto.assessorAllocationDto.assessorDto.name} &nbsp; ${inspectionDetailsDto.assessorAllocationDto.assessorDto.assessorContactNo}</span>
                                                    </div>
                                                </div>

                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Job No
                                                        :${IS_GARAGEINSPECTION}</label>
                                                    <div class="col-sm-8">

                                                                <span class="label_Value input-view"
                                                                >${inspectionDetailsDto.assessorAllocationDto.jobId}</span>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Registration No. :</label>
                                                    <div class="col-sm-8">
                                                                <span class="label_Value input-view"
                                                                >${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleNumber}</span>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Make<span
                                                            class="text-danger font-weight-bold">  *</span> :</label>
                                                    <div class="col-sm-8 input-group">
                                                                <span class="label_Value input-view"
                                                                >${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleMake}</span>
                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.makeConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                        name="makeConfirm" type="radio"
                                                                        class="align-middle" value="Confirm"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Correct</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.makeConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                        name="makeConfirm" type="radio"
                                                                        class="align-middle" value="Wrong"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Wrong</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.makeConfirm eq 'Not_Checked' and not isEmptyValue ? 'checked' : ''}
                                                                        name="makeConfirm" type="radio"
                                                                        class="align-middle" value="Not_Checked"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Not Checked</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Model<span
                                                            class="text-danger font-weight-bold">  *</span> :</label>
                                                    <div class="col-sm-8 input-group">
                                                                <span class="label_Value input-view"
                                                                >${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleModel}</span>
                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.modelConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio" name="modelConfirm"
                                                                        class="align-middle" value="Confirm"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Correct</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.modelConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio" name="modelConfirm"
                                                                        class="align-middle" value="Wrong"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Wrong</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.modelConfirm eq 'Not_Checked' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio" name="modelConfirm"
                                                                        class="align-middle" value="Not_Checked"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Not Checked</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Year of Make <span
                                                            class="text-danger font-weight-bold">  *</span>:</label>
                                                    <div class="col-sm-8 input-group">
                                                                <span class="label_Value input-view"
                                                                >${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.manufactYear}</span>
                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.yearMakeConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio"
                                                                        class="align-middle" name="yearMakeConfirm"
                                                                        value="Confirm"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Correct</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.yearMakeConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio"
                                                                        class="align-middle" name="yearMakeConfirm"
                                                                        value="Wrong"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Wrong</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.yearMakeConfirm eq 'Not_Checked' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio"
                                                                        class="align-middle" name="yearMakeConfirm"
                                                                        value="Not_Checked"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Not Checked</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Engine No :</label>
                                                    <div class="col-sm-8 input-group">
                                                                <span class="label_Value input-view"
                                                                >${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.engineNo}</span>
                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.engNoConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio"
                                                                        class="align-middle" name="engNoConfirm"
                                                                        value="Confirm"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Correct</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.engNoConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio" name="engNoConfirm"
                                                                        class="align-middle" value="Wrong"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Wrong</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.engNoConfirm eq 'Not_Checked' ? 'checked' : ''}
                                                                        type="radio" name="engNoConfirm"
                                                                        class="align-middle" value="Not_Checked"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Not Checked</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Chassis No in ISF *</label>
                                                    <div class="col-sm-8">
                                                        <span class="label_Value input-view">${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}</span>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Chassis No :</label>
                                                    <div class="col-sm-8 input-group">

                                                        <div class="row">
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.chassisNoConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio"
                                                                        class="align-middle" name="chassisNoConfirm"
                                                                        value="Confirm" onclick="correctSelect()"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Correct</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.chassisNoConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                        type="radio" name="chassisNoConfirm"
                                                                        class="align-middle" value="Wrong"
                                                                        onclick="wrongSelect()"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Wrong</span>
                                                            </label>
                                                            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                <input ${inspectionDetailsDto.chassisNoConfirm eq 'Not_Checked' ? 'checked' : ''}
                                                                        type="radio" name="chassisNoConfirm"
                                                                        class="align-middle" value="Not_Checked"
                                                                        onclick="notCheckSelect()"/>
                                                                <span class="radiomark"></span>
                                                                <span class="custom-control-description">Not Checked</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <script>
                                                    $("#typeOnlineInspection").click(function (){
                                                        if(document.getElementById("typeOnlineInspection").val === 'N'){
                                                            document.getElementById("typeOnlineInspection").checked = true
                                                        }else if(document.getElementById("typeOnlineInspection").val === 'Y'){
                                                            document.getElementById("typeOnlineInspection").checked = false
                                                        }
                                                    });
                                                </script>
                                                <%--<div class="form-group row" id="chassisDiv" style="display: none">--%>
                                                <%--<label class="col-sm-4 col-form-label">Chassis No<span--%>
                                                <%--class="text-danger font-weight-bold">  *</span> :</label>--%>
                                                <%--<div class="col-sm-8">--%>
                                                <%--<input name="chassisNo" id="chassisNo" placeholder="Chassis No"--%>
                                                <%--value="${inspectionDetailsDto.chassisNo}"--%>
                                                <%--class="lstBox form-control form-control-sm">--%>
                                                <%--</div>--%>
                                                <%--</div>--%>
                                                <div class="form-group row " id="notCheckDiv" style="display: none;">
                                                    <label class="col-sm-4 col-form-label"><span
                                                            class="text-danger font-weight-bold"> Not Checked Reason
                                                        * :</span>
                                                    </label>
                                                    <div class="col-sm-8">
                                                        <select name="notCheckedReason" id="notCheckedReason"
                                                                class="form-control form-control-sm "
                                                                data-fv-field="">
                                                            ${DbRecordCommonFunctionBean.getPopupList("not_check_reason_mst", "reason_id", "value")}
                                                        </select>
                                                        <script type="text/javascript">
                                                            $('#notCheckedReason').val('${inspectionDetailsDto.notCheckedReason}');
                                                        </script>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Date of Registration
                                                        :</label>
                                                    <div class="col-sm-8">
                                                                <span class="label_Value input-view"
                                                                >${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.registDate}</span>
                                                    </div>
                                                </div>
                                                <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 7
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 9
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 5
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 6}">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Excess (Rs) :</label>
                                                        <div class="col-sm-8">
                                                                      <span class="label_Value input-view"
                                                                      >
                                                                          <fmt:formatNumber
                                                                                  value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                                                                  pattern="###,##0.00;(###,##0.00)"
                                                                                  type="number"/>
                                                                      </span>
                                                        </div>
                                                    </div>
                                                </c:if>
                                                <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 1
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 2
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 4
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 5
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 6
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 9
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 7}">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">NCB (Rs) :</label>
                                                        <div class="col-sm-8">
                                                                      <span class="label_Value input-view"
                                                                      >
                                                                          <fmt:formatNumber
                                                                                  value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbAmount}"
                                                                                  pattern="#,##0.00;-#,##0.00"
                                                                                  type="number"/></span>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">NCB Percentage :</label>
                                                        <div class="col-sm-8">
                                                                      <span class="label_Value input-view"
                                                                      >${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbRate} %</span>
                                                        </div>
                                                    </div>
                                                </c:if>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Date of Inspection<span
                                                            class="text-danger font-weight-bold">  *</span> :</label>
                                                    <div class="col-sm-8">
                                                        <input name="inspectDateTime"
                                                               id="inspectDateTime"
                                                               class="form-control form-control-sm"
                                                               title=""
                                                               type="text"
                                                               value="${inspectionDetailsDto.inspectDateTime == '1980-01-01 00:00:00'
                                                                                ? inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime == '1980-01-01 00:00:00'
                                                                                ? inspectionDetailsDto.assessorAllocationDto.assignDatetime
                                                                                : inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime:inspectionDetailsDto.inspectDateTime}"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Sum Insured (Rs) :</label>
                                                    <div class="col-sm-8">
                                                                <span class="label_Value input-view"
                                                                >
                                                                    <input id="sumInsuredVal" name="sumInsuredVal"
                                                                           type="hidden"
                                                                           value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.sumInsured}"/>
                                                                    <input id="policyCoverNoteNo"
                                                                           name="policyCoverNoteNo"
                                                                           type="hidden"
                                                                           value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"/>
                                                                    <fmt:formatNumber
                                                                            value="${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.sumInsured}"
                                                                            pattern="###,##0.00;(###,##0.00)"
                                                                            type="number"/></span>
                                                    </div>
                                                </div>
                                                <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 5
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 6
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 9
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 7}">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">PAV (Rs.)<span
                                                                class="text-danger font-weight-bold">  *</span>
                                                            :</label>
                                                        <div class="col-sm-8">
                                                            <input id="pav" name="pav" value="${isEmptyValue
                                                                                                          ? not empty PREVIOUS_PAV && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId eq 4
                                                                                                          ? PREVIOUS_PAV : ''
                                                                                                          : inspectionDetailsDto.pav}"
                                                                   class="lstBox form-control form-control-sm text-right">
                                                        </div>
                                                    </div>
                                                </c:if>
                                                <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 7 && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 9}">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Details of Damages
                                                            :</label>
                                                        <div class="col-sm-8">
                                                                    <textarea name="damageDetails"
                                                                              class="lstBox form-control form-control-sm">${inspectionDetailsDto.damageDetails}</textarea>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">PAD :</label>
                                                        <div class="col-sm-8">
                                                                    <textarea name="pad"
                                                                              class="lstBox form-control form-control-sm">${inspectionDetailsDto.pad}</textarea>
                                                        </div>
                                                    </div>
                                                </c:if>
                                                <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 5
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 6
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 9
                                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 7}">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Genuineness of the
                                                            Accident
                                                            <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 4}">
                                                                <span class="text-danger font-weight-bold">  *</span>
                                                                <script>
                                                                    //TODO - Remove Validation
                                                                </script>
                                                            </c:if>
                                                            :</label>
                                                        <div class="col-sm-8">
                                                            <div class="row">
                                                                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container first-container">
                                                                    <input ${inspectionDetailsDto.genuineOfAccident eq 'Consistent' and not isEmptyValue ? 'checked' : ''}
                                                                            name="genuineOfAccident" type="radio"
                                                                            class="align-middle" value="Consistent"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">Consistent</span>
                                                                </label>
                                                                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container ml-0">
                                                                    <input ${inspectionDetailsDto.genuineOfAccident eq 'Non_Consistence' and not isEmptyValue ? 'checked' : ''}
                                                                            name="genuineOfAccident" type="radio"
                                                                            class="align-middle"
                                                                            value="Non_Consistence"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">Non Consistent</span>
                                                                </label>
                                                                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col-3 col-form-label check-container ml-0">
                                                                    <input ${inspectionDetailsDto.genuineOfAccident eq 'Doubtful' and not isEmptyValue ? 'checked' : ''}
                                                                            name="genuineOfAccident" type="radio"
                                                                            class="align-middle" value="Doubtful"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">Doubtful</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">1st statement required
                                                            for
                                                            Own Damage Settlement<span
                                                                    class="text-danger font-weight-bold">  *</span>
                                                            :</label>
                                                        <div class="col-sm-8 input-group">
                                                            <div class="row">
                                                                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                    <input ${inspectionDetailsDto.firstStatementReq eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                                                            name="firstStatementReq" type="radio"
                                                                            class="align-middle" value="Yes"
                                                                            onclick="enableDisableFirstStatement('Y');"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">Yes</span>
                                                                </label>
                                                                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                    <input ${inspectionDetailsDto.firstStatementReq eq 'No' and not isEmptyValue ? 'checked' : ''}
                                                                            name="firstStatementReq" type="radio"
                                                                            class="align-middle" value="No"
                                                                            onclick="enableDisableFirstStatement('N');"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">No</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Reason <span
                                                                class="text-danger font-weight-bold">  *</span>
                                                            :</label>
                                                        <div class="col-sm-8">
                                                            <select class=" form-control form-control-sm "
                                                                    id="firstStatementReqReason"
                                                                    name="firstStatementReqReason" disabled="true">
                                                                    ${DbRecordCommonFunctionBean.getPopupList("claim_first_statement_reason", "N_ID", "V_REASON")}
                                                            </select>
                                                        </div>
                                                        <script>
                                                            $("#firstStatementReqReason").val("${inspectionDetailsDto.firstStatementReqReason}");
                                                        </script>
                                                    </div>

                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Investigation
                                                            Required<span
                                                                    class="text-danger font-weight-bold">  *</span>
                                                            :</label>
                                                        <div class="col-sm-8 input-group">
                                                            <div class="row">
                                                                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                    <input ${inspectionDetailsDto.investReq eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                                                            name="investReq" type="radio"
                                                                            class="align-middle" value="Yes"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">Yes</span>
                                                                </label>
                                                                <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                                    <input ${inspectionDetailsDto.investReq eq 'No' and not isEmptyValue ? 'checked' : ''}
                                                                            name="investReq" type="radio"
                                                                            class="align-middle" value="No"/>
                                                                    <span class="radiomark"></span>
                                                                    <span class="custom-control-description">No</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:if>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Assessor Remarks
                                                        :</label>
                                                    <div class="col-sm-8">
                                                                <textarea name="assessorSpecialRemark"
                                                                          class="lstBox form-control form-control-sm">${inspectionDetailsDto.assessorSpecialRemark}</textarea>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Assessor Special Remarks
                                                        :</label>
                                                    <div class="col-sm-8">
                                                                <textarea name="assessorRemark"
                                                                          id="assessorRemark"
                                                                          class="lstBox form-control form-control-sm"></textarea>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <div>
                                                        <button type="submit" name="addSpecialRemark"
                                                                id="addSpecialRemark"
                                                                class="btn btn-primary float-right"
                                                                style="margin-left: 10px">
                                                            Add Special Remarks
                                                        </button>

                                                        <a href="${pageContext.request.contextPath}/InspectionDetailsController/viewClaimHistory?P_N_CLIM_NO=${inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}"
                                                           class="claimView">
                                                            <button type="button" name="cmdViewAccident"
                                                                    class="btn btn-primary float-right">
                                                                View Accidents
                                                            </button>
                                                        </a>
                                                    </div>
                                                    <script type="text/javascript">
                                                        $('.claimView').popupWindow({
                                                            height: screen.height,
                                                            width: screen.width,
                                                            resizable: 1,
                                                            centerScreen: 1,
                                                            scrollbars: 1,
                                                            windowName: 'swip'
                                                        });

                                                    </script>
                                                </div>

                                            </div>
                                            <div class="clearfix"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 5
                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 6
                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 7
                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 9}">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="heading2">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse2"
                                               aria-expanded="false" aria-controls="collapse2">
                                                Condition of Tyres
                                            </a>
                                            <input type="hidden" id="sectName" value="Condition of Tyres">
                                        </h5>
                                    </div>
                                    <div id="collapse2" class="collapse " aria-labelledby="heading2"
                                         data-parent="#accordion">
                                        <div class="card-body p-lg-3 p-2">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <table class="table table-responsive tire-con-table">
                                                        <tbody>
                                                        <tr>
                                                            <th>Position</th>
                                                            <td class="text-center">RF</td>
                                                            <td class="text-center">LF</td>
                                                            <td class="text-center">RR</td>
                                                            <td class="text-center">RL</td>
                                                            <td class="text-center">RRI</td>
                                                            <td class="text-center">LRI</td>
                                                            <td class="text-center">Other</td>
                                                        </tr>
                                                        <tr>
                                                            <th>Condition</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <select name="cot_0_0"
                                                                                class="form-control form-control-sm  cot_0"
                                                                                id="cot_0_0">
                                                                            <option value="Good">Good</option>
                                                                            <option value="Fair">Fair</option>
                                                                            <option value="Bald">Bald</option>
                                                                            <option value="N/A">N/A</option>
                                                                        </select>
                                                                        <script>document.getElementById("cot_0_0").value = "${inspectionDetailsDto.tireCondtionDtoList[0].rf}";</script>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <select name="cot_0_1"
                                                                                class="form-control form-control-sm  cot_0"
                                                                                id="cot_0_1">
                                                                            <option value="Good">Good</option>
                                                                            <option value="Fair">Fair</option>
                                                                            <option value="Bald">Bald</option>
                                                                            <option value="N/A">N/A</option>
                                                                        </select>
                                                                        <script>document.getElementById("cot_0_1").value = "${inspectionDetailsDto.tireCondtionDtoList[0].lf}";
                                                                        </script>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <select name="cot_0_2"
                                                                                class="form-control form-control-sm  cot_0"
                                                                                id="cot_0_2">
                                                                            <option value="Good">Good</option>
                                                                            <option value="Fair">Fair</option>
                                                                            <option value="Bald">Bald</option>
                                                                            <option value="N/A">N/A</option>
                                                                        </select>
                                                                        <script>document.getElementById("cot_0_2").value = "${inspectionDetailsDto.tireCondtionDtoList[0].rr}";</script>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <select name="cot_0_3"
                                                                                class="form-control form-control-sm  cot_0"
                                                                                id="cot_0_3">
                                                                            <option value="Good">Good</option>
                                                                            <option value="Fair">Fair</option>
                                                                            <option value="Bald">Bald</option>
                                                                            <option value="N/A">N/A</option>
                                                                        </select>
                                                                        <script>document.getElementById("cot_0_3").value = "${inspectionDetailsDto.tireCondtionDtoList[0].rl}";</script>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <select name="cot_0_4"
                                                                                class="form-control form-control-sm  cot_0"
                                                                                id="cot_0_4">
                                                                            <option value="Good">Good</option>
                                                                            <option value="Fair">Fair</option>
                                                                            <option value="Bald">Bald</option>
                                                                            <option value="N/A">N/A</option>
                                                                        </select>
                                                                        <script>document.getElementById("cot_0_4").value = "${inspectionDetailsDto.tireCondtionDtoList[0].rri}";</script>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <select name="cot_0_5"
                                                                                class="form-control form-control-sm  cot_0"
                                                                                id="cot_0_5">
                                                                            <option value="Good">Good</option>
                                                                            <option value="Fair">Fair</option>
                                                                            <option value="Bald">Bald</option>
                                                                            <option value="N/A">N/A</option>
                                                                        </select>
                                                                        <script>document.getElementById("cot_0_5").value = "${inspectionDetailsDto.tireCondtionDtoList[0].lri}";</script>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <select name="cot_0_6"
                                                                                class="form-control form-control-sm  cot_0"
                                                                                id="cot_0_6">
                                                                            <option value="Good">Good</option>
                                                                            <option value="Fair">Fair</option>
                                                                            <option value="Bald">Bald</option>
                                                                            <option value="N/A">N/A</option>
                                                                        </select>
                                                                        <script>document.getElementById("cot_0_6").value = "${inspectionDetailsDto.tireCondtionDtoList[0].other}";</script>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                            <%--and inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 4--%>
                                                        <c:if test="${ACTION eq 'SAVE' and inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 4}">
                                                            <script>
                                                                for (var i = 0; i < 7; i++) {
                                                                    document.getElementById("cot_0_" + i).value = "N/A";
                                                                    // $('#cot_0_' + i).val('"N/A"');
                                                                }
                                                            </script>
                                                        </c:if>
                                                        <tr>
                                                            <th>Size</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[1].rf}"
                                                                               name="cot_1_0"
                                                                               class="form-control form-control-sm cot1">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[1].lf}"
                                                                               name="cot_1_1"
                                                                               class="form-control form-control-sm cot2 ">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[1].rr}"
                                                                               name="cot_1_2"
                                                                               class="form-control form-control-sm cot3">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[1].rl}"
                                                                               name="cot_1_3"
                                                                               class="form-control form-control-sm cot4 ">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[1].rri}"
                                                                               name="cot_1_4"
                                                                               class="form-control form-control-sm cot5 ">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[1].lri}"
                                                                               name="cot_1_5"
                                                                               class="form-control form-control-sm cot6">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[1].other}"
                                                                               name="cot_1_6"
                                                                               class="form-control form-control-sm cot7">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Make</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[2].rf}"
                                                                               name="cot_2_0"
                                                                               class="form-control form-control-sm cot1">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[2].lf}"
                                                                               name="cot_2_1"
                                                                               class="form-control form-control-sm cot2">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[2].rr}"
                                                                               name="cot_2_2"
                                                                               class="form-control form-control-sm cot3">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[2].rl}"
                                                                               name="cot_2_3"
                                                                               class="form-control form-control-sm cot4">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[2].rri}"
                                                                               name="cot_2_4"
                                                                               class="form-control form-control-sm cot5">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[2].lri}"
                                                                               name="cot_2_5"
                                                                               class="form-control form-control-sm cot6">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[2].other}"
                                                                               name="cot_2_6"
                                                                               class="form-control form-control-sm cot7">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>New / RB</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[3].rf}"
                                                                               name="cot_3_0"
                                                                               class="form-control form-control-sm cot1">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[3].lf}"
                                                                               name="cot_3_1"
                                                                               class="form-control form-control-sm cot2">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[3].rr}"
                                                                               name="cot_3_2"
                                                                               class="form-control form-control-sm cot3">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[3].rl}"
                                                                               name="cot_3_3"
                                                                               class="form-control form-control-sm cot4">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[3].rri}"
                                                                               name="cot_3_4"
                                                                               class="form-control form-control-sm cot5">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[3].lri}"
                                                                               name="cot_3_5"
                                                                               class="form-control form-control-sm cot6">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${inspectionDetailsDto.tireCondtionDtoList[3].other}"
                                                                               name="cot_3_6"
                                                                               class="form-control form-control-sm cot7">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-group row">
                                                                <label class="col-sm-4 col-form-label">Special
                                                                    remarks
                                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                                    :</label>
                                                                <div class="col-sm-8">
                                                                        <textarea name="inspectionSpecialRemark"
                                                                                  id="inspectionSpecialRemark"
                                                                                  class="form-control form-control-sm "></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="float-right">
                                                                <button type="submit" name="AddRemarks"
                                                                        id="AddRemarks"
                                                                        class="btn btn-primary float-right">
                                                                    Add Special Remarks
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </c:if>
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading3">
                                    <h5 class="mb-0">
                                        <input type="hidden" id="sectiName"
                                               value="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue} Calculation Summary">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse3"
                                           style="word-break: break-all;"
                                           aria-expanded="false" aria-controls="collapse3">
                                            ${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}
                                            Calculation Summary
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse3" class="collapse" aria-labelledby="heading3"
                                     data-parent="#accordionOne">
                                    <div class="card-body p-lg-3 p-2">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label">Inspection Type
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                    <div class="col-sm-8">
                                                                    <span class="label_Value input-view"
                                                                    >${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}</span>
                                                    </div>
                                                </div>
                                                <c:choose>
                                                    <c:when test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 4}">
                                                        <jsp:include
                                                                page="/WEB-INF/jsp/claim/inspectiondetails/assessor/garageInspection.jsp"></jsp:include>
                                                    </c:when>
                                                    <c:when test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 5 || inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 6 }">
                                                        <jsp:include
                                                                page="/WEB-INF/jsp/claim/inspectiondetails/assessor/drSupplementaryInspection.jsp"></jsp:include>
                                                    </c:when>
                                                    <c:when test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 7||inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 9}">
                                                        <jsp:include
                                                                page="/WEB-INF/jsp/claim/inspectiondetails/assessor/ariInspection.jsp"></jsp:include>
                                                    </c:when>
                                                    <c:when test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 8}">
                                                        <jsp:include
                                                                page="/WEB-INF/jsp/claim/inspectiondetails/assessor/desktopAssesment.jsp"></jsp:include>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <jsp:include
                                                                page="/WEB-INF/jsp/claim/inspectiondetails/assessor/onSiteInspection.jsp"></jsp:include>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading4">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse4"
                                           aria-expanded="false" aria-controls="collapse4">
                                            Previous Inspection
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse4" class="collapse " aria-labelledby="heading4"
                                     data-parent="#accordionOne">
                                    <div class="card-body p-lg-3 p-2">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <table width="100%" cellpadding="0" cellspacing="1"
                                                       class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                                    <thead>
                                                    <tr>
                                                        <th class="tbl_row_header">Job No</th>
                                                        <th class="tbl_row_header">Inspection Type</th>
                                                        <th class="tbl_row_header">Assign Assessor</th>
                                                        <th class="tbl_row_header">Assign RTE</th>
                                                        <th class="tbl_row_header">Approve Date Time</th>
                                                        <th class="tbl_row_header">Date Of Accident</th>
                                                        <th class="tbl_row_header">Status</th>
                                                        <th class="tbl_row_header"></th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <c:forEach var="claim" items="${previousInspectionList}">
                                                        <c:forEach var="jobs" items="${claim.list}">
                                                            <tr>
                                                                <td>${jobs.jobNo}</td>
                                                                <td>${jobs.inspectionType}</td>
                                                                <td>${jobs.assignAssessor}</td>
                                                                <td>${jobs.assignRte}</td>
                                                                <td>${jobs.approveDateTime}</td>
                                                                <td>${jobs.dateOfAccident}</td>
                                                                <td>${jobs.statusDesc}</td>

                                                                <c:if test="${jobs.statusDesc ne 'ASSIGNED'}">
                                                                    <td>
                                                                        <div>
                                                                            <a href="${pageContext.request.contextPath}/InspectionDetailsController/viewEditPrevious?P_N_CLIM_NO=${claim.claimNo}&P_N_JOB_NO=${jobs.jobRefNo}&P_POL_N_REF_NO=${jobs.policyRefNo}"
                                                                               class="jobView">
                                                                                <button type="button"
                                                                                        name="cmdPrevInspec"
                                                                                        class="btn btn-primary">
                                                                                    <i class="fa fa-eye"></i>
                                                                                </button>
                                                                            </a>
                                                                        </div>
                                                                        <script type="text/javascript">
                                                                            $('.jobView').popupWindow({
                                                                                height: screen.height,
                                                                                width: screen.width,
                                                                                resizable: 1,
                                                                                centerScreen: 1,
                                                                                scrollbars: 1,
                                                                                windowName: 'swip'
                                                                            });
                                                                        </script>
                                                                    </td>
                                                                </c:if>

                                                            </tr>
                                                        </c:forEach>
                                                    </c:forEach>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading9">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse9"
                                           aria-expanded="false" aria-controls="collapse9">
                                            Previous Claims
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse9" class="collapse " aria-labelledby="heading9"
                                     data-parent="#accordionOne">
                                    <div class="card-body p-lg-3 p-2">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <table width="100%" cellpadding="0" cellspacing="1"
                                                       class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                                    <thead>
                                                    <tr>
                                                        <th class="tbl_row_header">Job No</th>
                                                        <th class="tbl_row_header">Inspection Type</th>
                                                        <th class="tbl_row_header">Vehicle No</th>
                                                        <th class="tbl_row_header">Policy No</th>
                                                        <th class="tbl_row_header">Date of Accident</th>
                                                        <th class="tbl_row_header"></th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <c:forEach var="claim" items="${previousClaimList}">
                                                        <tr class="bg-dark">
                                                            <td colspan="6">
                                                                <b> ${claim.claimNo}</b>
                                                            </td>
                                                        </tr>
                                                        <c:forEach var="jobs" items="${claim.list}">
                                                            <tr>
                                                                <td>${jobs.jobNo}</td>
                                                                <td>${jobs.inspectionType}</td>
                                                                <td>${jobs.vehicleNo}</td>
                                                                <td>${jobs.policyNo}</td>
                                                                <td>${jobs.dateOfAccident}</td>
                                                                <td>
                                                                    <div>
                                                                        <c:if test="${jobs.jobNo ne 'N/A'}">
                                                                            <a href="${pageContext.request.contextPath}/InspectionDetailsController/viewEditPrevious?P_N_CLIM_NO=${claim.claimNo}&P_N_JOB_NO=${jobs.refNo}&P_POL_N_REF_NO=${jobs.policyRefNo}"
                                                                               class="jobView">
                                                                                <button type="button" name="cmdViewPrev"
                                                                                        class="btn btn-primary">
                                                                                    <i class="fa fa-eye"></i>
                                                                                </button>
                                                                            </a>
                                                                        </c:if>
                                                                    </div>
                                                                    <script type="text/javascript">
                                                                        $('.jobView').popupWindow({
                                                                            height: screen.height,
                                                                            width: screen.width,
                                                                            resizable: 1,
                                                                            centerScreen: 1,
                                                                            scrollbars: 1,
                                                                            windowName: 'swip'
                                                                        });
                                                                    </script>
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                    </c:forEach>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <c:if test="${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 5
                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 6
                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 9
                                                      && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId ne 7}">
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="heading10">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse10"
                                               aria-expanded="false" aria-controls="collapse10">
                                                Third Party Details
                                            </a>
                                        </h5>
                                    </div>
                                    <div id="collapse10" class="collapse" aria-labelledby="heading0"
                                         data-parent="#accordionOne">
                                        <div class="card-body p-lg-3 p-2">
                                            <div class="row">
                                                <iframe width="100%" id="requestFrame" style="height: 100vh ;"
                                                        frameborder="0"
                                                        class="scroll"
                                                        src="${pageContext.request.contextPath}/InspectionDetailsController/thirdPartyDetails"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </c:if>
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading12">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse12"
                                           aria-expanded="false" aria-controls="collapse12">
                                            Log Details
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse12" class="collapse " aria-labelledby="heading12"
                                     data-parent="#accordionOne">
                                    <div class="card-body p-lg-3 p-2">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="form-group row">
                                                    <div class="col-sm-12">
                                                        <div class="w-100 scroll"
                                                             style="height: calc(100vh - 350px);">
                                                            <c:forEach var="logDetail"
                                                                       items="${inspectionDetailsDto.logList}">
                                                                <a href="#"
                                                                   class="list-group-item list-group-item-action flex-column align-items-start">
                                                                    <div class="font-bg log-left"
                                                                         style="width: 50px; height:50px; overflow: hidden; margin-top: 25px;">
                                                                        <h2 class="name text-white">${logDetail.userId}</h2>
                                                                    </div>
                                                                    <div class="float-left log-right"
                                                                         style="height: auto;">
                                                                        <div class="w-100">
                                                                                <%--<h5 class="mb-1">${logDetail.userId}</h5>--%>
                                                                            <p class="text-right mb-0">
                                                                                claim-${logDetail.claimNo}</p>
                                                                        </div>
                                                                        <hr class="m-0 mt-1 mb-1">
                                                                        <div class="row">
                                                                            <div class="col-md-6">
                                                                                <p class="mb-0">Changed Field</p>
                                                                                <p class="mb-1">
                                                                                    <b>${logDetail.fieldName}</b>
                                                                                </p>
                                                                            </div>
                                                                            <div class="col-md-6 border-left">
                                                                                <p class="mb-0">Changed Value</p>
                                                                                <p class="mb-1">
                                                                                    <b>${logDetail.fieldValue}</b>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                        <hr class="m-0 mt-1 mb-1">
                                                                        <h6 class="float-right">${logDetail.userId} </h6>
                                                                        <p class="float-left">${logDetail.inputDateTime}</p>
                                                                    </div>
                                                                    <div class="clearfix"></div>
                                                                </a>
                                                            </c:forEach>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading13">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse13"
                                           aria-expanded="false" aria-controls="collapse13">
                                            Special Remarks
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse13" class="collapse " aria-labelledby="heading13"
                                     data-parent="#accordionOne">
                                    <div class="card-body p-lg-3 p-2">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="form-group row">
                                                    <div class="col-sm-12">
                                                        <div class="w-100 scroll"
                                                             style="height: calc(100vh - 350px);"
                                                             id="specialRemarks">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset class="col-md-5 border scroll" style="height: calc(100vh - 210px);">
                        <div class="mt-3">
                            <div id="accordion">

                                <div class="card mt-2">
                                    <div class="card-header p-0" id="heading5">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse5"
                                               aria-expanded="false" aria-controls="collapse5">
                                                Documents
                                            </a>
                                        </h5>
                                    </div>
                                    <div id="collapse5" class="collapse " aria-labelledby="heading5"
                                         data-parent="#accordion">
                                        <div class="card-body p-lg-3 p-2">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <iframe width="100%" style="height: 100vh;" frameborder="0"
                                                            id="iframeDocumentUpload" name="iframeDocumentUpload"
                                                            height="90vh"
                                                            class="scroll"
                                                            src="${pageContext.request.contextPath}/InspectionDetailsController/documentUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${inspectionDetailsDto.assessorAllocationDto.refNo}"></iframe>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card mt-2">
                                    <div class="card-header p-0" id="heading6">
                                        <h5 class="mb-0">
                                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse6"
                                               aria-expanded="false" aria-controls="collapse6">
                                                Vehicle Image
                                            </a>
                                        </h5>
                                    </div>
                                    <div id="collapse6" class="collapse " aria-labelledby="heading6"
                                         data-parent="#accordion">
                                        <div class="card-body p-lg-3 p-2">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <div id="imageUploadContainer"></div>

                                                    <%--<iframe width="100%" style="height: 100vh;" frameborder="0"
                                                            id="iframeImageUpload" name="iframeImageUpload"
                                                            height="90vh"
                                                            class="scroll"
                                                            src="${pageContext.request.contextPath}/InspectionDetailsController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${inspectionDetailsDto.assessorAllocationDto.refNo}"></iframe>--%>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset class="col-md-12">
                        <div id="accordion3">
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading8">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse8"
                                           aria-expanded="false" aria-controls="collapse8">
                                            Photo Comparison
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse8" class="collapse " aria-labelledby="heading8"
                                     data-parent="#accordion3">
                                    <div class="card-body p-0">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <iframe width="50%" style="height: 100vh;" frameborder="0"
                                                        id="iframePhotoCom1" name="iframePhotoCom1"
                                                        height="90vh"
                                                        class="scroll float-left"
                                                        src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=1&policyNumber=${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                                <iframe width="50%" style="height: 100vh;" frameborder="0"
                                                        id="iframePhotoCom2" name="iframePhotoCom2"
                                                        height="90vh"
                                                        class="scroll float-left"
                                                        src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=2&policyNumber=${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <c:if test="${PREVIOUS_INSPECTION!='Y'}">
                                <div class="float-right mt-3 mb-3">
                                    <c:if test="${ACTION_TYPE eq 'DRAFT' && G_USER.userId == inspectionDetailsDto.assessorAllocationDto.assessorDto.userName && (inspectionDetailsDto.assessorAllocationDto.recordStatus==0
                                    || inspectionDetailsDto.assessorAllocationDto.recordStatus==29)}">
                                        <!--<button class="btn btn-primary" type="button" onclick="saveDetails();">Save</button>-->
                                        <button class="btn btn-primary" type="submit">Submit</button>
                                    </c:if>
                                    <button onclick="goBack()" class="btn btn-link" type="button"><b>Back</b></button>
                                </div>
                            </c:if>
                        </div>
                    </fieldset>
                </div>
            </form>
        </fieldset>
    </div>
</div>
<c:forEach var="claimDocumentTypeDto" items="${inspectionDetailsDto.claimDocumentTypeDtoList}">
    <form name="frmDocumentModal${claimDocumentTypeDto.documentTypeId}"
          id="frmDocumentModal${claimDocumentTypeDto.documentTypeId}">
        <input type="hidden" name="documentTypeId"
               value="${claimDocumentTypeDto.documentTypeId}">
        <input type="hidden" name="claimNo" value="${inspectionDetailsDto.claimNo}">
        <input type="hidden" name="jobRefNo" value="${inspectionDetailsDto.refNo}">
        <input type="hidden" name="departmentId" value="3">
        <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
             id="docUploadModal${claimDocumentTypeDto.documentTypeId}" aria-hidden="true"
             style="    background: #333333c2;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content p-2" style="overflow: hidden">
                    <div class="modal-header  p-2">
                        <h6 class="modal-title"
                            id="modalLabel${claimDocumentTypeDto.documentTypeId}">${claimDocumentTypeDto.documentTypeName} </h6>
                        <small class="text-danger pull-right"><b> .PNG / .JPG / .PDF File Formats Only.</b></small>
                    </div>
                    <p id="errorUpload${claimDocumentTypeDto.documentTypeId}"></p>
                    <div class=" mt-4">
                        <div class="col-sm-12">
                            <!-- The fileinput-button span is used to style the file input field as button -->
                            <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                <!-- The file input field used as target for the file upload widget -->
                                        <input id="fileUploadClaim${claimDocumentTypeDto.documentTypeId}" type="file"
                                               name="files[]" multiple>
                                    </span>
                            <!-- The global progress bar -->
                            <div id="progressClaim${claimDocumentTypeDto.documentTypeId}" class="progress">
                                <div class="progress-bar bg-success"></div>
                            </div>
                            <!-- The container for the uploaded files -->
                            <div id="filesClaim${claimDocumentTypeDto.documentTypeId}" class="files"></div>
                            <br>
                        </div>
                    </div>
                    <div class="modal-footer p-1">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                onclick="closeUploadWindow('${claimDocumentTypeDto.documentTypeId}');">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
            <%--<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
                 id="imgUploadModal" aria-hidden="true"
                 style="    background: #333333c2;">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content p-2" style="overflow: hidden">
                        <div class="modal-header  p-2">
                            <h6 class="modal-title"
                                id="imagemodalLabel">Image Upload</h6>
                        </div>
                        <p id="imageerrorUpload"></p>
                        <div class=" mt-4">
                            <div class="col-sm-12">
                                <!-- The fileinput-button span is used to style the file input field as button -->
                                <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                            <i class="fa fa-plus"></i>
                                            <span>Select files...</span>
                                    <!-- The file input field used as target for the file upload widget -->
                                            <input id="imageUpload" type="file" name="files[]" multiple>
                                        </span>
                                <!-- The global progress bar -->
                                <div id="imageProgress" class="progress">
                                    <div class="progress-bar bg-success"></div>
                                </div>
                                <!-- The container for the uploaded files -->
                                <div id="imageFiels" class="files"></div>
                                <br>
                                <div id="imageCount">Upload Count</div>
                            </div>
                        </div>
                        <div class="modal-footer p-1">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                    onclick="closeUploadImageWindow();">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                documentUploadIds.push('${claimDocumentTypeDto.documentTypeId}');
            </script>--%>
    </form>

    <form method="POST" ENCTYPE="multipart/form-data">
        <input type="hidden" name="documentTypeId"
               value="${claimDocumentTypeDto.documentTypeId}">
        <input type="hidden" name="claimNo" value="${inspectionDetailsDto.claimNo}">
        <input type="hidden" name="jobRefNo" value="${inspectionDetailsDto.refNo}">
        <input type="hidden" name="departmentId" value="3">
        <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
             id="imgUploadModal" aria-hidden="true"
             style="    background: #333333c2;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content p-2" style="overflow: hidden">
                    <div class="modal-header  p-2">
                        <h6 class="modal-title"
                            id="imagemodalLabel">Image Upload</h6>
                    </div>
                    <p id="imageerrorUpload"></p>
                    <div class=" mt-4">
                        <div class="col-sm-12">
                            <!-- The fileinput-button span is used to style the file input field as button -->
                            <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                <!-- The file input field used as target for the file upload widget -->
                                        <input id="imageUpload" type="file" name="files[]" multiple>
                                    </span>
                            <!-- The global progress bar -->
                            <div id="imageProgress" class="progress">
                                <div class="progress-bar bg-success"></div>
                            </div>
                            <!-- The container for the uploaded files -->
                            <div id="imageFiels" class="files"></div>
                            <br>
                            <div id="imageCount">Upload Count</div>
                        </div>
                    </div>
                    <div class="modal-footer p-1">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                onclick="closeUploadImageWindow();">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <script>
            documentUploadIds.push('${claimDocumentTypeDto.documentTypeId}');
        </script>
    </form>


    <form id="backForm"></form>


</c:forEach>
<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>
<script type="text/javascript">
    function initTyreCondition() {
        try {
            document.getElementById("cot_0_0").value = "${not empty inspectionDetailsDto.tireCondtionDtoList[0].rf ? inspectionDetailsDto.tireCondtionDtoList[0].rf :'N/A'}";
            document.getElementById("cot_0_1").value = "${not empty inspectionDetailsDto.tireCondtionDtoList[0].lf ? inspectionDetailsDto.tireCondtionDtoList[0].lf :'N/A'}";
            document.getElementById("cot_0_2").value = "${not empty inspectionDetailsDto.tireCondtionDtoList[0].rr ? inspectionDetailsDto.tireCondtionDtoList[0].rr :'N/A'}";
            document.getElementById("cot_0_5").value = "${not empty inspectionDetailsDto.tireCondtionDtoList[0].lri ? inspectionDetailsDto.tireCondtionDtoList[0].lri :'N/A'}";
            document.getElementById("cot_0_3").value = "${not empty inspectionDetailsDto.tireCondtionDtoList[0].rl ? inspectionDetailsDto.tireCondtionDtoList[0].rl :'N/A'}";
            document.getElementById("cot_0_4").value = "${not empty inspectionDetailsDto.tireCondtionDtoList[0].rri ? inspectionDetailsDto.tireCondtionDtoList[0].rri :'N/A'}";
            document.getElementById("cot_0_6").value = "${not empty inspectionDetailsDto.tireCondtionDtoList[0].other ? inspectionDetailsDto.tireCondtionDtoList[0].other :'N/A'}";
        } catch (Ex) {

        }
    }

    initTyreCondition();

    $("#cot_0_0").change(function () {
        var val = $("#cot_0_0 :selected").attr('value');
        if (val == "N/A") {
            $(".cot1").val("");
        }
    });
    $("#cot_0_1").change(function () {
        var val = $("#cot_0_1 :selected").attr('value');
        if (val == "N/A") {
            $(".cot2").val("");
        }
    });
    $("#cot_0_2").change(function () {
        var val = $("#cot_0_2 :selected").attr('value');
        if (val == "N/A") {
            $(".cot3").val("");
        }
    });
    $("#cot_0_3").change(function () {
        var val = $("#cot_0_3 :selected").attr('value');
        if (val == "N/A") {
            $(".cot4").val("");
        }
    });
    $("#cot_0_4").change(function () {
        var val = $("#cot_0_4 :selected").attr('value');
        if (val == "N/A") {
            $(".cot5").val("");
        }
    });
    $("#cot_0_5").change(function () {
        var val = $("#cot_0_5 :selected").attr('value');
        if (val == "N/A") {
            $(".cot6").val("");
        }
    });
    $("#cot_0_6").change(function () {
        var val = $("#cot_0_6 :selected").attr('value');
        if (val == "N/A") {
            $(".cot7").val("");
        }
    });

    winresize();

    function winresize() {
        var width = $(window.parent.$('body')).width();
        $('body').width(width - 40);
    }

    $(window.parent).resize(function () {
        winresize();
    });

    $(".datepicker").datepicker({dateFormat: 'yy-mm-dd'});
    $(".yearpicker").datepicker({dateFormat: 'yy-mm-dd'});

    function documentFileUploder(documentTypeId) {
        'use strict';
        var progress = 0;
        var url = '${pageContext.request.contextPath}/DocumentUploadController';
        $('#fileUploadClaim' + documentTypeId).fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {
                data.submit();
                return;
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<i class="fa fa-file-pdf-o fa-4x m-3"></i>').appendTo('#filesClaim' + documentTypeId);
                });
                $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorUpload' + documentTypeId).removeClass("bg-danger");
                $('#errorUpload' + documentTypeId).addClass("bg-success");
                $('#errorUpload' + documentTypeId).html("");
                $('#errorUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Document Uploaded Successfully!</span>').appendTo('#errorUpload' + documentTypeId);
                $('#errorUpload' + documentTypeId).fadeOut(4000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);

                $('#progressClaim' + documentTypeId + ' .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorUpload' + documentTypeId).removeClass("bg-success");
                $('#errorUpload' + documentTypeId).addClass("bg-danger");
                $('#errorUpload' + documentTypeId).html("");
                $('#errorUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center">Document Upload failed.</span>').appendTo('#errorUpload' + documentTypeId);
                $('#errorUpload' + documentTypeId).fadeOut(4000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {

                    $('#progressClaim' + documentTypeId + ' .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                    $('#errorUpload' + documentTypeId).removeClass("bg-danger");
                    $('#errorUpload' + documentTypeId).removeClass("bg-success");

                    $('#errorUpload' + documentTypeId).addClass("bg-primary");
                    $('#errorUpload' + documentTypeId).html("");
                    $('#errorUpload' + documentTypeId).fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#errorUpload' + documentTypeId);


                });
            }, drop: function (e, data) {
                e.preventDefault();
            }, dragover: function (e, data) {
                // return false;
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    }

    var totalImages = 0;
    var uploadedImages = 0;

    $(document).ready(function () {
        if ('${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 5 || inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId == 6 }' == "true") {
            $("#SumInsuredRemove").hide();
        } else {
            $("#SumInsuredRemove").show();
        }
        'use strict';
        // Change this to the location of your server-side upload handler:
        var url = '${pageContext.request.contextPath}/ImageUploadController';
        var progress = 0;

        $('#imageUpload').fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {
                data.submit()
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<img src="" alt="">').appendTo('#imageFiels');
                    $('#imageCount').html("Uploaded " + (++uploadedImages) + " of " + totalImages);
                });
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-danger");
                $('#imageerrorUpload').addClass("bg-success");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Image Uploaded Successfully!</span>').appendTo('#imageerrorUpload');

                $('#imageerrorUpload').fadeOut(9000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);
                // alert(progress);
                $('#imageProgress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-success");
                $('#imageerrorUpload').addClass("bg-danger");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center">Image Upload failed.</span>').appendTo('#imageerrorUpload');
                $('#imageerrorUpload').fadeOut(9000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {
                    totalImages = data.files.length;
                    //  uploadedImages=totalImages;
                    $('#imageProgress .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#imageerrorUpload').removeClass("bg-primary");
                    $('#imageerrorUpload').removeClass("bg-danger");
                    $('#imageerrorUpload').removeClass("bg-success");

                    $('#imageerrorUpload').addClass("bg-primary");
                    $('#imageerrorUpload').html("");
                    $('#imageerrorUpload').fadeIn();

                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#imageerrorUpload');


                });
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    });

    $(document).ready(function () {
        $('#frmMain')
            .formValidation({
                framework: 'bootstrap',
                excluded: 'disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {
                    assessorRemark: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select'
                            }
                        }
                    }
                }
            })
    });

    documentUploadIds.forEach(function (t) {
        documentFileUploder(t)
    });

    function closeUploadWindow(documentTypeId) {
        //        $('docUploadModal'+documentTypeId).hide();
        var iframeEl = document.getElementById("iframeDocumentUpload");
        iframeEl.contentWindow.reloadPage();
        $('#filesClaim' + documentTypeId).html('');
        //        $('#progressClaim .progress-bar').css('width',0);


    }

    function closeUploadImageWindow() {
        loadImageUploadViewer();
    }

    function saveDetails() {
        var type = '${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId }';
        var policyNo = $('#policyCoverNoteNo').val();
        // var covn = policyNo.substring(0, 4);
        // if ('COVN' == covn) {
        //
        // }
        if (type == 1 || type == 4) {
            if (sumInsuredVal >= acr) {
                document.frmMain.action = "${pageContext.request.contextPath}/InspectionDetailsController/saveDetails?ACTION_TYPE=DRAFT";
                document.frmMain.method = "POST";
                document.frmMain.submit();
            } else {
                bootbox.confirm({
                    message: "Are you sure you want to close this Page?",
                    buttons: {
                        // confirm: {
                        //     label: 'Yes',
                        //     className: 'btn-primary'
                        // },
                        cancel: {
                            label: 'Close',
                            className: 'btn-secondary float-right'
                        }
                    },
                });

            }
        } else {
            document.frmMain.action = "${pageContext.request.contextPath}/InspectionDetailsController/saveDetails?ACTION_TYPE=DRAFT";
            document.frmMain.method = "POST";
            document.frmMain.submit();
        }
    }

    // send refNo
    $("#jobType").change(function () {
        calAssessorFee();

    });
    // send refNo
    $("#assessorFeeDetailId").change(function () {
        calAssessorFee();

    });

    $("#mileage,#otherFee,#costOfCall").keyup(function () {
        calAssessorFee();
    });

    $("#jobType").on("change", function () {
        getTimeSlots();
    });

    function getTimeSlots() {
        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/fetchTimeSlotForInspection";

        $.ajax({
            url: URL,
            type: 'POST',
            data: {
                jobType: $("#jobType").val(),
                inspectionTypeId: "${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}"
            },
            async: false,
            success: function (result) {
                console.log("result ", result)
                // Ensure result is a proper object (in case it's a JSON string)
                var timeSlots = typeof result === "string" ? JSON.parse(result) : result;

                var select = $('#assessorFeeDetailId');
                select.empty(); // Clear existing options

                select.append('<option value="0">-- Please Select --</option>');

                // Append new options
                $.each(timeSlots, function(index, item) {
                    select.append(
                        $('<option></option>').val(item.value).text(item.label)
                    );
                });
            },
            error: function () {
                alert("Failed to load inspection time slots.");
            }
        });
    }

    function calAssessorFee() {
        var refNo = "${inspectionDetailsDto.assessorAllocationDto.refNo}";

        if ("" == $("#jobType").val()) {
            return false;
        }
        var dataObj = {
            refNo: refNo,
            jobType: $("#jobType").val(),
            otherFee: $("#otherFee").val(),
            mileage: $("#mileage").val(),
            costOfCall: $("#costOfCall").val(),
            assessorFeeDetailId: $("#assessorFeeDetailId").val(),
            inspectionTypeId: "${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}"
        };
        console.log('dataObj : ',dataObj)
        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateProfessionalFee";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            async: false,
            success: function (result) {
                $("#totalAssessorFee").val(result);
            }
        });
    }

    $('[data-toggle="collapse"]').click(function () {
        $('body').animate({
            scrollTop: 0
        });
    });

    $("#inspectDateTime").datetimepicker({
        sideBySide: true,
        format: 'YYYY-MM-DD HH:mm',
        icons: {
            time: "fa fa-clock-o",
            date: "fa fa-calendar",
            up: "fa fa-arrow-up",
            down: "fa fa-arrow-down"
        }
    });

    var currentDate = '${Current_Date}';
    var accidentDate = '${inspectionDetailsDto.assessorAllocationDto.claimsDto.accidDate}' + ' ' + '${inspectionDetailsDto.assessorAllocationDto.claimsDto.accidTime}';
    $("#inspectDateTime").data("DateTimePicker").maxDate(currentDate);
    $("#inspectDateTime").data("DateTimePicker").minDate(accidentDate);

    function enableDisableFirstStatement(stat) {
        if (stat == "Y") {
            $('#frmMain').formValidation('revalidateField', 'firstStatementReqReason');
            $("#firstStatementReqReason").prop('disabled', false).trigger("chosen:updated");
        } else {
            $('#frmMain').formValidation('revalidateField', 'firstStatementReqReason');
            $("#firstStatementReqReason").val('').prop('disabled', true).trigger("chosen:updated");
        }
    }


    $(document).ready(function () {
        if (isOnsiteOrOffsite('${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}')) {
            $("#jobType option[value='0']").prop('disabled', false);
            $("#jobType option[value='2']").prop('disabled', false);
        } else {
            $("#jobType option[value='0']").prop('disabled', false);
            $("#jobType option[value='1']").prop('selected', false);
            $("#jobType option[value='2']").prop('disabled', false);
        }

        if ('${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}' == '4') {
            $('#frmMain').data('formValidation').enableFieldValidators('genuineOfAccident', false);
        } else {
            $('#frmMain').data('formValidation').enableFieldValidators('genuineOfAccident', true);
        }

        if (${not empty PREVIOUS_PAV && inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId eq 4}) {
            calculateUnderInsPenaltyPerc();
        }

        if (${inspectionDetailsDto.chassisNoConfirm eq 'Wrong'}) {
            // $('#chassisDiv').show();
            wrongSelect();
        } else if (${inspectionDetailsDto.chassisNoConfirm eq 'Not_Checked'}) {
            $('#notCheckDiv').show();
        } else if (${inspectionDetailsDto.chassisNoConfirm eq 'Confirm'}) {
            correctSelect();
        }

    });

    function isOnsiteOrOffsite(typeId) {
        if ('1' == typeId || '2' == typeId) {
            return true;
        } else {
            return false;
        }
    }

    function goBack() {
        bootbox.confirm({
            message: "Are you sure you want to close this Page?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }
            },
            callback: function (result) {
                if (result == true) {
                    document.frmMain.action = contextPath + "/InspectionDetailsController/jobView";
                    document.frmMain.submit();
                }
            }
        });
    }

    $('.currencyMask').autoNumeric('init');

    $('#addSpecialRemark').click(function (e) {
        e.preventDefault();
        var val = $('#assessorRemark').val();
        if (val !== "") {
            var data = {
                claimId: $('#claimNo').val(),
                remark: $('#assessorRemark').val(),
                sectionName: $('#secName').val(),
            }
            $.ajax({
                type: 'POST',
                url: "${pageContext.request.contextPath}/InspectionDetailsController/addRemark",
                data: data,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != null) {
                        notify(obj, "success");
                        $('#assessorRemark').val('');

                    }
                    loadSpecialRemarks();
                }
            });
        }
    });


    $('#AddRemarks').click(function (e) {
        e.preventDefault();
        var val = $('#inspectionSpecialRemark').val();
        if (val !== "") {
            var data = {
                claimId: $('#claimNo').val(),
                remark: $('#inspectionSpecialRemark').val(),
                sectionName: $('#sectName').val(),
            }
            $.ajax({
                type: 'POST',
                url: "${pageContext.request.contextPath}/InspectionDetailsController/addRemark",
                data: data,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != null) {
                        notify(obj, "success");
                        $('#assessorRemark').val('');

                    }
                    loadSpecialRemarks();
                    $('#inspectionSpecialRemark').val("");
                }
            });
        }
    });

    $('#addSpecRemark').click(function (e) {
        e.preventDefault();
        var val = $('#specialRemark').val();
        if (val !== "") {
            var data = {
                claimId: $('#claimNo').val(),
                remark: $('#specialRemark').val(),
                sectionName: $('#sectiName').val(),
            }
            $.ajax({
                type: 'POST',
                url: "${pageContext.request.contextPath}/InspectionDetailsController/addRemark",
                data: data,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != null) {
                        notify(obj, "success");
                        $('#assessorRemark').val('');

                    }
                    loadSpecialRemarks();
                    $('#inspectionSpecialRemark').val("");
                }
            });
        }
    });

    $('#addGarageRemark').click(function (e) {
        e.preventDefault();
        var val = $('#RemarkText').val();
        if (val !== "") {
            var data = {
                claimId: $('#claimNo').val(),
                remark: $('#RemarkText').val(),
                sectionName: $('#sectiName').val(),
            }
            $.ajax({
                type: 'POST',
                url: "${pageContext.request.contextPath}/InspectionDetailsController/addRemark",
                data: data,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != null) {
                        notify(obj, "success");
                        $('#assessorRemark').val('');

                    }
                    loadSpecialRemarks();
                    $('#inspectionSpecialRemark').val("");
                }
            });
        }
    });

    function loadSpecialRemarks() {
        $("#specialRemarks").load("${pageContext.request.contextPath}/InspectionDetailsController/viewSpecialRemark?CLIM_NO=${inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
    }

    function correctSelect() {
        $('#frmMain').data('formValidation').enableFieldValidators('notCheckedReason', false);
        //$('#frmMain').data('formValidation').enableFieldValidators('chassisNo', false);
        $("#notCheckedReason").attr('disabled', 'disabled');
        // $('#chassisDiv').hide();
        $('#notCheckDiv').hide();
        // $('#chassisNo').val('${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}');
    }

    function wrongSelect() {
        //   $('#chassisDiv').show();
        // $('#chassisNo').val('${inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}');
        //$('#frmMain').data('formValidation').enableFieldValidators('chassisNo', true);
        $('#notCheckedReason').val('0');
        $("#notCheckedReason").attr('disabled', 'disabled');
        $('#frmMain').data('formValidation').enableFieldValidators('notCheckedReason', false);
        $('#notCheckDiv').hide();

    }

    function notCheckSelect() {
        // $('#chassisDiv').hide();
        //   $('#frmMain').data('formValidation').enableFieldValidators('chassisNo', false);
        //   $('#chassisNo').val('');
        $('#notCheckDiv').show();
        $("#notCheckedReason").removeAttr('disabled');
        $('#frmMain').data('formValidation').enableFieldValidators('notCheckedReason', true);
    }

    if(${((null == inspectionDetailsDto.assessorAllocationDto.typeOnlineInspection) ||
          ('' == inspectionDetailsDto.assessorAllocationDto.typeOnlineInspection)) ||
                inspectionDetailsDto.assessorAllocationDto.typeOnlineInspection == 'N'}){
        document.getElementById("typeOnlineInspection").checked = false
    }else{
        document.getElementById("typeOnlineInspection").checked = true
    }


</script>
<script>
    hideLoader();
</script>
</body>
</html>
