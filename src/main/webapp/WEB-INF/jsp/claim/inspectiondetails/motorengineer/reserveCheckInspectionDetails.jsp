<%--
  Created by IntelliJ IDEA.
  User: madhushanka
  Date: 12/4/2019
  Time: 3:24 PM
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">

    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">

    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/inspectiondetails-form-validations.js?v1"></script>

    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>

    <!-- Generic page styles -->
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>

    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>

    <script>

        showLoader();
        $(document).ready(function () {
            loadImageUploadViewer();
            loadDocumentUploadView();
            loadBillUploadView();
            logdetails();
            loadSuppyOrderView();
            loadPaymentOptionPage();
        });

        function loadPaymentOptionPage() {
            $("#col_paymentoption").load(contextPath + "/CalculationSheetController/loadPaymentOptionPage?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $("#col_RefPaymentoption").load(contextPath + "/ReferenceTwoCalculationSheetController/loadPaymentOptionPage?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&DOC_UPLOAD=${docUpload}");
        }

        function loadSuppyOrderView() {
            $("#claimSupplyOrderContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSupplyOrder?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
        }

        function logdetails() {
            $("#col_logdetails").load("${pageContext.request.contextPath}/ClaimHandlerController/viewLogTrail?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadImageUploadViewer() {
            $("#imageUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/viewBillImage?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
        }

        function loadDocumentUploadView() {
            $("#documentUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/viewDocumentUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadBillUploadView() {
            $("#billUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/viewBillUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        $(document).ready(function () {
            loadSpecialRemarks();

        });

        function resizeIframe(obj) {
            obj.style.height = obj.contentWindow.document.body.scrollHeight + 'px';
        }

        var documentUploadIds = [];
        var documentBillUploadIds = [];


        function loadSpecialRemarks() {
            $("#specialRemarks").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSpecialRemark?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
        }
    </script>
</head>
<c:set var="motorEngineerDto" value="${motorEngineerDto}" scope="request"/>

<body class="scroll" onload="hideLoader()">
<form name="frmForm" id="frmForm">
    <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"
           value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}"/>
    <input name="P_CAL_SHEET_NO" id="P_CAL_SHEET_NO" type="hidden" value="${calculationSheetNo}"/>
    <input name="TYPE" id="TYPE" type="hidden" value="${TYPE}"/>
</form>
<div class="container-fluid">
    <div class="row header-bg bg-dark">
        <div class="col-sm-12 py-2" >
            <h6 class="float-left text-dark hide-for-small"> Inspection Report
                Details-Motor Engineering</h6>

            <h6 class="text-dark float-right">Vehicle No
                : ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}</h6><br>
            <h6 class="text-dark float-right" style="margin-right: -162px">Claim No
                : ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}</h6>
        </div>
<%--        <div class="col-sm-12 py-2" style="--%>
<%--            display: flex;--%>
<%--            flex-direction: row;--%>
<%--            justify-content: space-around;--%>
<%--            align-items: center;--%>
<%--            background: #a7d1d6;--%>
<%--        ">--%>
<%--            <h6 style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                ${(motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product eq "") || (null eq motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product) ? "N/A" : motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product }--%>
<%--            </h6>--%>
<%--            <h6 id="service-factor-header" style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                Service Factors--%>
<%--            </h6>--%>
<%--        </div>--%>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="f1-steps">
                <div class="f1-progress">
                    <div class="f1-progress-line" data-now-value="10" data-number-of-steps="5"
                         style="width: 70%;"></div>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimStatus>=1?"active":""}">
                        1
                    </div>
                    <p>Call Center</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">2</div>
                    <p>Assessor Coordinator</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">3</div>
                    <p>Assessor</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">4</div>
                    <p>Motor Engineer</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon">5</div>
                    <p>Claim Handler</p>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" name="ACTION" value="${ACTION}"/>
    <c:if test="${ACTION eq 'SAVE'}">
        <c:set var="isEmptyValue" value="false" scope="request"/>
    </c:if>
    <c:if test="${ACTION eq 'UPDATE'}">
        <c:set var="isEmptyValue" value="false" scope="request"/>
    </c:if>
    <c:if test="${motorEngineerDto.inspectionDetailsDto.recordStatus eq 0}">
        <c:set var="isEmptyValue" value="true" scope="request"/>
    </c:if>
    <c:if test="${motorEngineerDto.inspectionDto.inspectionId eq 8}">
        <c:set var="isDesktopInspection" value="true" scope="request"/>
    </c:if>
    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 8}">
        <c:set var="isDesktopInspection" value="false" scope="request"/>
    </c:if>
    <div class="row">
        <fieldset class="col-md-6 border scroll" style="height: calc(100vh - 210px);">
            <div id="accordionOne">
                <div class="card mt-3">
                    <div class="card-header p-0" id="heading0">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse0"
                               aria-expanded="true" aria-controls="collapse0">
                                Underwriting Details
                            </a>
                        </h5>
                    </div>
                    <div id="collapse0" class="collapse" aria-labelledby="heading0"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div id="underWritingDetails" class="col-lg-12">
                                    <jsp:include
                                            page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/underwritingDetails.jsp"></jsp:include>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="inspectionDetailsDiv" class="card mt-2">
                    <div class="card-header p-0" id="heading1">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse1"
                               aria-expanded="true" aria-controls="collapse1">
                                Inspection Report
                                Details
                            </a>
                        </h5>
                    </div>
                    <input type="hidden" name="claimNo"
                           value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}">
                    <div id="collapse1" class="collapse" aria-labelledby="heading1"
                         data-parent="#accordionOne">
                        <div class="card-body p-1 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <c:if test="${not isDesktopInspection}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Code of Assessor
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assessorDto.name} &nbsp; ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assessorDto.assessorContactNo}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Job No :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobId}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Customer Name :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="custName">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custName}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Contact Address :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="contactAddress">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine1},</span>
                                            <span class="label_Value input-view"
                                                  id="contactAddress2">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine2},</span>
                                            <span class="label_Value input-view"
                                                  id="contactAddress3">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine3}.</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Customer Contact Number :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="contactNumber">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custMobileNo}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Registration No. :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleNumber}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Make :</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleMake}</span>
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.makeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.makeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.makeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.makeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                                <%--<label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">--%>
                                                <%--<input ${motorEngineerDto.makeConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}--%>
                                                <%--name="makeConfirm" type="radio"--%>
                                                <%--class="align-middle" value="Confirm"/>--%>
                                                <%--<span class="radiomark"></span>--%>
                                                <%--<span class="custom-control-description">Correct</span>--%>
                                                <%--</label>--%>
                                                <%--<label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">--%>
                                                <%--<input ${motorEngineerDto.makeConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}--%>
                                                <%--name="makeConfirm" type="radio"--%>
                                                <%--class="align-middle" value="Wrong"/>--%>
                                                <%--<span class="radiomark"></span>--%>
                                                <%--<span class="custom-control-description">Wrong</span>--%>
                                                <%--</label>--%>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Model<span
                                                class="text-danger font-weight-bold">  *</span> :</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleModel}</span>
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.modelConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.modelConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.modelConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.modelConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Year of Make <span
                                                class="text-danger font-weight-bold">  *</span>:</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.manufactYear}</span>
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.yearMakeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.yearMakeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.yearMakeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.yearMakeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Engine No :</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.engineNo}</span>

                                                <c:choose>

                                                    <c:when test="${motorEngineerDto.engNoConfirm eq 'Confirm'}">
                                                        <span class="label_Value assessor_value m-0 col-6 ">Confirm</span>
                                                    </c:when>
                                                    <c:when test="${motorEngineerDto.engNoConfirm eq 'Wrong'}">
                                                        <span class="label_Value assessor_value m-0 col-6 ">Wrong</span>
                                                    </c:when>
                                                    <c:when test="${motorEngineerDto.engNoConfirm eq 'Not_Checked'}">
                                                        <span class="label_Value assessor_value m-0 col-6 ">Not Checked</span>
                                                    </c:when>
                                                </c:choose>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.engNoConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.engNoConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Chassis No in ISF :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}</span>
                                        </div>
                                    </div>
                                    <c:if test="${not isDesktopInspection}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Chassis No:</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value assessor_value input-view">${motorEngineerDto.inspectionDetailsDto.chassisNo}</span>
                                                    <%--<div class="row">--%>
                                                    <%--<label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">--%>
                                                    <%--<input type="checkbox" name="engNoConfirm"--%>
                                                    <%--id="engNoConfirmbox"--%>
                                                    <%--class="align-middle" value="Y"--%>
                                                    <%--onclick="chassis_correct('Y');"/>--%>
                                                    <%--<span class="checkmark"></span>--%>
                                                    <%--<span class="custom-control-description">Incorrect Chassis No.</span>--%>
                                                    <%--</label>--%>
                                                    <%--</div>--%>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <c:if test="${not isDesktopInspection}">
                                            <label class="col-sm-4 col-form-label text-mute correctChaNo">Correct
                                                Chassis No :</label>
                                        </c:if>
                                        <c:if test="${isDesktopInspection}">
                                            <label class="col-sm-4 col-form-label text-mute correctChaNo">Chassis
                                                No :</label>
                                        </c:if>
                                        <div class="col-sm-8">
                                            <span class="label_Value assessor_value input-view">${motorEngineerDto.chassisNo}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Date of Registration
                                            :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.registDate}</span>
                                        </div>
                                    </div>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 7
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 6}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Excess (Rs) :</label>
                                            <div class="col-sm-8">
                                                          <span class="label_Value input-view">
                                                              <fmt:formatNumber
                                                                      value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                                                      pattern="###,##0.00;(###,##0.00)"
                                                                      type="number"/>
                                                          </span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 1
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 2
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 4
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 6
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">NCB (Rs) :</label>
                                            <div class="col-sm-8">
                                                          <span class="label_Value input-view">
                                                              <fmt:formatNumber
                                                                      value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbAmount}"
                                                                      pattern="#,##0.00;-#,##0.00"
                                                                      type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">NCB Percentage :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbRate} %</span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Date of Inspection :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view">${isEmptyValue ? '' : motorEngineerDto.inspectDateTime == '1980-01-01 00:00:00'
                                                            ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime == '1980-01-01 00:00:00'
                                                            ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assignDatetime
                                                            : motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime:motorEngineerDto.inspectDateTime}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Sum Insured (Rs) :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view" id="sumInsuredVal"
                                                          name="sumInsuredVal">
                                                        <fmt:formatNumber
                                                                value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.sumInsured}"
                                                                pattern="###,##0.00;(###,##0.00)"
                                                                type="number"/></span>
                                        </div>
                                    </div>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 6
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">PAV (Rs.) :</label>
                                            <div class="col-sm-8 input-group">
                                                          <span class="label_Value assessor_value input-view"
                                                          ><fmt:formatNumber
                                                                  value="${motorEngineerDto.inspectionDetailsDto.pav}"
                                                                  pattern="###,##0.00;"
                                                                  type="number"/></span>
                                                <span class="label_Value assessor_value input-view text-success"
                                                ><fmt:formatNumber
                                                        value="${isEmptyValue ? not empty PREVIOUS_PAV && motorEngineerDto.inspectionDto.inspectionId eq 8
                                                                                              ? PREVIOUS_PAV : ''
                                                                                              : motorEngineerDto.pav}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Details of Damages<span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.damageDetails}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.damageDetails}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">PAD :</label>
                                            <div class="col-sm-8  input-group">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.pad}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.pad}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 6
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Genuineness of the Accident<c:if
                                                    test="${motorEngineerDto.inspectionDto.inspectionId ne 4}">
                                                <span class="text-danger font-weight-bold">  *</span>
                                                <script>
                                                    //TODO - Remove Validation
                                                </script>
                                            </c:if>
                                                :</label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                              <span class="label_Value assessor_value col-md-12 input-view text-primary m-0 pl-3">
                                                                  <c:choose>
                                                                      <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Consistent'}">
                                                                          Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Non_Consistence'}">
                                                                          Non Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Doubtful'}">
                                                                          Doubtful
                                                                      </c:when>
                                                                  </c:choose>
                                                              </span>
                                                    <span class="label_Value assessor_value col-md-12 input-view text-primary m-0 pl-3">
                                                                  <c:choose>
                                                                      <c:when test="${motorEngineerDto.genuineOfAccident eq 'Consistent'}">
                                                                          Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.genuineOfAccident eq 'Non_Consistence'}">
                                                                          Non Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.genuineOfAccident eq 'Doubtful'}">
                                                                          Doubtful
                                                                      </c:when>
                                                                  </c:choose>
                                                              </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">1st statement required for
                                                Own Damage Settlement<span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8 input-group">
                                                          <span class="label_Value assessor_value input-view text-primary"
                                                          >${motorEngineerDto.inspectionDetailsDto.firstStatementReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <span class="label_Value assessor_value input-view text-primary"
                                                >${motorEngineerDto.firstStatementReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <div class="row">

                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Reason <span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8 input-group">
                                                    <%--<span class="label_Value assessor_value input-view text-primary"--%>
                                                    <%-->${DbRecordCommonFunctionBean.getValue("claim_first_statement_reason", "V_REASON", "N_ID", motorEngineerDto.inspectionDetailsDto.firstStatementReqReason)}</span>--%>
                                                <span class="label_Value assessor_value input-view text-primary"
                                                >${motorEngineerDto.firstStatementReqReason}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Investigation Required<span
                                                    class="text-danger font-weight-bold">  *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                          <span class="label_Value assessor_value input-view text-primary"
                                                          >${motorEngineerDto.inspectionDetailsDto.investReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <div class="row">
                                                            <span class="label_Value assessor_value input-view text-primary"
                                                            >${motorEngineerDto.investReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Assessor Remarks:</label>
                                        <div class="col-sm-8">
                                                        <span class="label_Value assessor_value input-view text-primary"
                                                        >${motorEngineerDto.inspectionDetailsDto.assessorSpecialRemark}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">RTE Remarks:</label>
                                        <div class="col-sm-8">
                                                         <span class="label_Value assessor_value input-view text-primary"
                                                         >${motorEngineerDto.assessorSpecialRemark}</span>

                                        </div>
                                    </div>
                                    <div class="my-3 clearfix">
                                        <a href="${pageContext.request.contextPath}/MotorEngineerController/viewClaimHistoryBillCheck?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}"
                                           class="claimView">
                                            <button type="button" name="cmdViewAccident"
                                                    value="Reject"
                                                    class="btn btn-primary float-right skipReadOnly">
                                                View Accidents
                                            </button>
                                        </a>
                                        <div class="float-right" style="padding-right:10px ">

                                        </div>
                                        <script type="text/javascript">
                                            $('.claimView').popupWindow({
                                                height: screen.height,
                                                width: screen.width,
                                                resizable: 1,
                                                centerScreen: 1,
                                                scrollbars: 1,
                                                windowName: 'swip'
                                            });
                                        </script>
                                    </div>

                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <jsp:include page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/allInspectionTypeDetails.jsp"/>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading4">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse4"
                               aria-expanded="true" aria-controls="collapse4">
                                Previous Inspection
                            </a>
                        </h5>
                    </div>
                    <div id="collapse4" class="collapse " aria-labelledby="heading4"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2" style="overflow: auto">
                            <div class="row">
                                <div class="col-md-12">
                                    <table width="100%" cellpadding="0" cellspacing="1"
                                           class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                        <thead>
                                        <tr>
                                            <th class="tbl_row_header">Job No</th>
                                            <th class="tbl_row_header">Inspection Type</th>
                                            <th class="tbl_row_header">Assign Assessor</th>
                                            <th class="tbl_row_header">Assign RTE</th>
                                            <th class="tbl_row_header">Forwarded RTE</th>
                                            <th class="tbl_row_header">Approve Date Time</th>
                                            <th class="tbl_row_header">Date of Accident</th>
                                            <th class="tbl_row_header">Status</th>
                                            <th class="tbl_row_header"></th>
                                            <th class="tbl_row_header">Forward</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:set var="cnt" value="1"/>
                                        <c:forEach var="claim" items="${previousInspectionList}">
                                            <c:forEach var="jobs" items="${claim.list}">
                                                <tr>
                                                    <td>${jobs.jobNo}</td>
                                                    <td>${jobs.inspectionType}</td>
                                                    <td>${jobs.assignAssessor}</td>
                                                    <td>${jobs.assignRte}</td>
                                                    <td>${jobs.approveAssignRte}</td>
                                                    <td>${jobs.approveDateTime}</td>
                                                    <td>${jobs.dateOfAccident}</td>
                                                    <td>${jobs.statusDesc}</td>

                                                    <c:if test="${jobs.statusDesc ne 'ASSIGNED'}">
                                                        <td>
                                                            <div>
                                                                <a href="${pageContext.request.contextPath}/MotorEngineerController/viewEditClaimPrevious?P_N_JOB_NO=${jobs.jobRefNo}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&P_POL_N_REF_NO=${jobs.policyRefNo}"
                                                                   class="jobView${cnt}">
                                                                    <button type="button" name="cmdReject"
                                                                            class="btn btn-primary">
                                                                        <i class="fa fa-eye"></i>
                                                                    </button>
                                                                </a>
                                                            </div>
                                                            <script type="text/javascript">
                                                                $('.jobView${cnt}').popupWindow({
                                                                    height: screen.height,
                                                                    width: screen.width,
                                                                    resizable: 1,
                                                                    centerScreen: 1,
                                                                    scrollbars: 1,
                                                                    windowName: 'swip${jobs.jobNo}'
                                                                });
                                                            </script>
                                                        </td>
                                                    </c:if>
                                                    <c:if test="${(G_USER.accessUserType eq 27 || G_USER.accessUserType eq 28) && jobs.statusDesc ne 'ASSIGNED'}">
                                                        <td>
                                                            <button type="button" id="${jobs.jobRefNo}" name="btnFwdRte"
                                                                    class="btn btn-primary ml-1" ${jobs.recordStatus eq 10 ? 'disabled' : ''}
                                                                    onclick="selectRte('${jobs.jobRefNo}')">Forward
                                                            </button>
                                                        </td>
                                                    </c:if>
                                                </tr>
                                                <c:set var="cnt" value="${cnt=cnt+1}"/>
                                            </c:forEach>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-2">
                    <div class="card-header p-0" id="heading14">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse14"
                               aria-expanded="true" aria-controls="collapse14">
                                Document
                            </a>
                        </h5>
                    </div>
                    <div id="collapse14" class="collapse " aria-labelledby="heading14"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                                 id="documentUploadContainer">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card mt-2">
                    <div class="card-header p-0" id="heading15">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse15"
                               aria-expanded="true" aria-controls="collapse15">
                                Finall Bills
                            </a>
                        </h5>
                    </div>
                    <div id="collapse15" class="collapse ${tabIndex==10?'show':''}" aria-labelledby="heading15"
                         data-parent="#accordionOne">
                        <div class="card-body p-2">
                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                 id="billUploadContainer">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading9">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse9"
                               aria-expanded="true" aria-controls="collapse9">
                                Previous Claims
                            </a>
                        </h5>
                    </div>
                    <div id="collapse9" class="collapse " aria-labelledby="heading9"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-md-12">
                                    <table width="100%" cellpadding="0" cellspacing="1"
                                           class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                        <thead>
                                        <tr>
                                            <th class="tbl_row_header">Job No</th>
                                            <th class="tbl_row_header">Inspection Type</th>
                                            <th class="tbl_row_header">Vehicle No</th>
                                            <th class="tbl_row_header">Policy No</th>
                                            <th class="tbl_row_header">Date of Accident</th>
                                            <th class="tbl_row_header"></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="claim" items="${previousClaimList}">
                                            <tr class="bg-dark">
                                                <td colspan="6">
                                                    <b> ${claim.claimNo}</b>
                                                </td>
                                            </tr>
                                            <c:forEach var="jobs" items="${claim.list}">
                                                <tr>
                                                    <td>${jobs.jobNo}</td>
                                                    <td>${jobs.inspectionType}</td>
                                                    <td>${jobs.vehicleNo}</td>
                                                    <td>${jobs.policyNo}</td>
                                                    <td>${jobs.dateOfAccident}</td>
                                                    <td>
                                                        <div>
                                                            <c:if test="${jobs.jobNo ne 'N/A'}">
                                                                <a href="${pageContext.request.contextPath}/MotorEngineerController/viewEditPrevious?P_N_JOB_NO=${jobs.refNo}"
                                                                   class="jobView${loop.index}">
                                                                    <button type="button" name="cmdPrev"
                                                                            class="btn btn-primary">
                                                                        <i class="fa fa-eye"></i>
                                                                    </button>
                                                                </a>
                                                            </c:if>
                                                        </div>
                                                        <script type="text/javascript">
                                                            $('.jobView${loop.index}').popupWindow({
                                                                height: screen.height,
                                                                width: screen.width,
                                                                resizable: 1,
                                                                centerScreen: 1,
                                                                scrollbars: 1,
                                                                windowName: 'swip${jobs.jobNo}'
                                                            });
                                                        </script>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="paymentoption">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_paymentoption"
                               aria-expanded="false" aria-controls="col_paymentoption">
                                Payment Option
                            </a>
                        </h5>
                    </div>
                    <div id="col_paymentoption" class="collapse ${tabIndex==9?'show':''}"
                         aria-labelledby="paymentoption"
                         data-parent="#accordionOne" style="overflow: auto">
                    </div>
                </div>
<%--                <div class="card mt-2">--%>
<%--                    <div class="card-header p-0" id="RefPaymentoption">--%>
<%--                        <h5 class="mb-0">--%>
<%--                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_RefPaymentoption"--%>
<%--                               aria-expanded="false" aria-controls="col_RefPaymentoption">--%>
<%--                                Cover Based Payments--%>
<%--                            </a>--%>
<%--                        </h5>--%>
<%--                    </div>--%>
<%--                    <div id="col_RefPaymentoption" class="collapse  ${tabIndex==15?'show':''}"--%>
<%--                         aria-labelledby="RefPaymentoption"--%>
<%--                         data-parent="#accordion2" style="overflow: auto">--%>
<%--                        <!-- Content for the new div goes here -->--%>
<%--                    </div>--%>
<%--                </div>--%>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading12">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_logdetails"
                               aria-expanded="true" aria-controls="col_logdetails">
                                Log Details
                            </a>
                        </h5>
                    </div>
                    <div id="col_logdetails" class="collapse" aria-labelledby="logdetails"
                         data-parent="#accordion2">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading13">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse13"
                               aria-expanded="true" aria-controls="collapse13">
                                Special Remarks
                            </a>
                        </h5>
                    </div>
                    <div id="collapse13" class="collapse " aria-labelledby="heading13"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                                 id="specialRemarks">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="col-md-6 border scroll" style="height: calc(100vh - 210px);">
            <%--<h6> Inspection Details</h6>--%>
            <%--<hr class="my-1">--%>
            <div class="mt-3">
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading6">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse6"
                               aria-expanded="true" aria-controls="collapse6">
                                Vehicle Image
                            </a>
                        </h5>
                    </div>
                    <div id="collapse6" class="collapse " aria-labelledby="heading6"
                         data-parent="#accordion">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div id="imageUploadContainer"></div>

                                    <%--<iframe width="100%" style="height: 100vh;" frameborder="0"
                                            id="iframeImageUpload" name="iframeImageUpload"
                                            height="90vh"
                                            class="scroll"
                                            src="${pageContext.request.contextPath}/MotorEngineerController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}"></iframe>--%>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="supplyorder">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_supplyorder"
                               aria-expanded="false" aria-controls="col_supplyorder">
                                Supply Order
                            </a>
                        </h5>
                    </div>
                    <div id="col_supplyorder" class="collapse ${tabIndex==6?'show':''}"
                         aria-labelledby="supplyorder"
                         data-parent="#accordion2">
                        <div id="claimSupplyOrderContainer"></div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="col-md-12">
            <div id="accordion3">
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading8">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse8"
                               aria-expanded="false" aria-controls="collapse8">
                                Photo Comparison
                            </a>
                        </h5>
                    </div>
                    <div id="collapse8" class="collapse " aria-labelledby="heading8"
                         data-parent="#accordion3">
                        <div class="card-body p-0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <iframe width="50%" style="height: 100vh;" frameborder="0"
                                            id="iframePhotoCom1" name="iframePhotoCom1"
                                            height="90vh"
                                            class="scroll float-left"
                                            src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=1&policyNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                    <iframe width="50%" style="height: 100vh;" frameborder="0"
                                            id="iframePhotoCom2" name="iframePhotoCom2"
                                            height="90vh"
                                            class="scroll float-left"
                                            src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=2&policyNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </fieldset>
    </div>

</div>

<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>
<script type="text/javascript">
    $(".datepicker").datepicker({dateFormat: 'yy-mm-dd'});
    $(".yearpicker").datepicker({dateFormat: 'yy-mm-dd'});


    $(document).ready(function () {
        'use strict';
        // Change this to the location of your server-side upload handler:
        var url = '${pageContext.request.contextPath}/ImageUploadController';
        var progress = 0;
        $('#imageUpload').fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {

                data.submit()
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<img src="" alt="">').appendTo('#imageFiels');
                });
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-danger");
                $('#imageerrorUpload').addClass("bg-success");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Image Uploaded Successfully!</span>').appendTo('#imageerrorUpload');
                $('#imageerrorUpload').fadeOut(9000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);
                // alert(progress);
                $('#imageProgress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-success");
                $('#imageerrorUpload').addClass("bg-danger");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center">Image Upload failed.</span>').appendTo('#imageerrorUpload');
                $('#imageerrorUpload').fadeOut(9000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {
                    $('#imageProgress .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#imageerrorUpload').removeClass("bg-primary");
                    $('#imageerrorUpload').removeClass("bg-danger");
                    $('#imageerrorUpload').removeClass("bg-success");

                    $('#imageerrorUpload').addClass("bg-primary");
                    $('#imageerrorUpload').html("");
                    $('#imageerrorUpload').fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#imageerrorUpload');


                });
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    });

    function viewClaimHistory(polRefNo, claimNo) {
        $("#" + claimNo).colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/CallCenter/viewClaimHistory?P_N_CLIM_NO=" + claimNo,
            onCleanup: function () {
            }
        });
    }

    documentUploadIds.forEach(function (t) {
        documentFileUploder(t)
    });

    documentBillUploadIds.forEach(function (t) {
        alert(t);
        documentBillFileUploder(t)
    });


    function closeUploadImageWindow() {
        loadImageUploadViewer();
    }


    $("#mileage,#otherFee,#jobType,#deductions").change(function () {
        calAssessorFee();
    });

    $("#jobType").on("change", function () {
        getTimeSlots();
    });

    function getTimeSlots() {
        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/fetchTimeSlotForInspection";

        $.ajax({
            url: URL,
            type: 'POST',
            data: {
                jobType: $("#jobType").val(),
                inspectionTypeId: "${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}"
            },
            async: false,
            success: function (result) {
                console.log("result ", result)
                // Ensure result is a proper object (in case it's a JSON string)
                var timeSlots = typeof result === "string" ? JSON.parse(result) : result;

                var select = $('#assessorFeeDetailId');
                select.empty(); // Clear existing options

                select.append('<option value="0">-- Please Select --</option>');

                // Append new options
                $.each(timeSlots, function(index, item) {
                    select.append(
                        $('<option></option>').val(item.value).text(item.label)
                    );
                });
            },
            error: function () {
                alert("Failed to load inspection time slots.");
            }
        });
    }

    function calAssessorFee() {
        if ("" == $("#jobType").val()) {
            return false;
        }
        var dataObj = {
            jobType: $("#jobType").val(),
            otherFee: $("#otherFee").val(),
            deductions: $("#deductions").val(),
            mileage: $("#mileage").val(),
            inspectionTypeId: "${motorEngineerDto.inspectionDto.inspectionId}"
        };

        var URL = "${pageContext.request.contextPath}/MotorEngineerController/calculateProfessionalFee";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            async: false,
            success: function (result) {
                if (0 > parseInt(result)) {
                    notify("Deductions can not be exceed total fee", "danger");
                    $("#deductions").val(0);
                    calAssessorFee();
                } else {
                    $("#totalAssessorFee").val(result);
                }
            }
        });
    }

    function setAsReadOnlyAllElements(selector) {
        $(selector).find(':input').not('.skipReadOnly').prop('readonly', true);
        $(selector).find('textarea').not('.skipReadOnly').prop('readonly', true);
        $(selector).find(':radio:not(:checked)').not('.skipReadOnly').prop('disabled', true);
        $(selector).find('select').not('.skipReadOnly').each(function () {
            var selectVal = $(this).val();
            $(this).children('option').each(function () {
                if (selectVal == $(this).val()) {
                    $(this).attr('disabled', false);
                    $(this).attr('selected', true);
                } else {
                    $(this).attr('disabled', true);
                }
            });
        });
        $(selector).find('button').not('.skipReadOnly').prop('disabled', true);
        $(selector).find(':checkbox[readonly="readonly"]').not('.skipReadOnly').click(function () {
            return false;
        });
    }

    $(document).ready(function () {
        $("#thirdPartyInvolved_N").prop("checked", true);
        disable_3rd_Party_details('N');

//                if (${(motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10))}) {
//                    $('#estimationDiv').find('fieldset').not(':has(>.assessor_pmt_div)').each(function () {
//                        setAsReadOnlyAllElements($(this));
//                    });
//                    setAsReadOnlyAllElements($('#tyreConditionDiv'));
//                    setAsReadOnlyAllElements($('#inspectionDetailsDiv'));
//                }
//                if (${motorEngineerDto.inspectionDetailsDto.assessorFeeAuthStatus eq 'A'}) {
//                    setAsReadOnlyAllElements($('#assessorPmtDiv'));
//                }
    });

    function processThirdParty(type, key) {

        if ("EDIT" == type) {
            disable_3rd_Party_details('Y');
            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=GET&KEY=" + key,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    $("#txnId").val(obj.txnId);
                    $("#ccTpdId").val(obj.ccTpdId);
                    $("#type").val(obj.type);
                    $("#lossType").val(obj.lossType);
                    $("#itemType").val(obj.itemType);
                    $("#vehicleNo").val(obj.vehicleNo);
                    $("#contactNo").val(obj.contactNo);
                    $("#insureDetails").val(obj.insureDetails);
                    $("#remark").val(obj.remark);
                    $("#key").val(key);

                    $("input[name=thirdPartyInvolved]").prop("checked", false);
                    $("input[name=intendClaim]").prop("checked", false);
                    $("#thirdPartyInvolved_" + obj.thirdPartyInvolved).prop("checked", true);
                    $("#intendClaim_" + obj.intendClaim).prop("checked", true);
                }
            });

        } else if ("DELETE" == type) {
            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=DELETE&KEY=" + key,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    $("#txnId").val(obj.type);
                    $("#type").val(obj.type);
                    $("#lossType").val(obj.lossType);
                    $("#itemType").val(obj.itemType);
                    $("#vehicleNo").val(obj.vehicleNo);
                    $("#contactNo").val(obj.contactNo);
                    $("#insureDetails").val(obj.insureDetails);
                    $("#remark").val(obj.remark);

                    $("#thirdPartyInvolved").val(obj.lossType);
                    $("#intendClaim").val(obj.lossType);
                }
            });
        } else if ("ADD" == type) {
            var dataObj = {
                txnId: $("#txnId").val(),
                ccTpdId: $("#ccTpdId").val(),
                type: $("#type").val(),
                lossType: $("#lossType option:selected").val(),
                itemType: $("#itemType option:selected").val(),
                vehicleNo: $("#vehicleNo").val(),
                contactNo: $("#contactNo").val(),
                insureDetails: $("#insureDetails").val(),
                remark: $("#remark").val(),
                thirdPartyInvolved: $('input[name=thirdPartyInvolved]:checked').val(),
                intendClaim: $('input[name=intendClaim]:checked').val()
            };

            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=ADD&KEY=" + key,
                type: 'POST',
                data: dataObj,
                success: function (result) {
                    $("#TPdata tbody").load(contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=LIST");
                    resetForm();
                }
            });
        }


    }

    $('#vehicleNo').focusout(function () {
        $.ajax({
            url: contextPath + "/CallCenter/searchVehicle?vehicleNo=" + this.value,
            type: 'POST',
            success: function (result) {
                if (result == 'YES') {
                    $('#vehicalAvailability').show()
                } else {
                    $('#vehicalAvailability').hide()
                }
            }
        });
    });

    function resetForm() {
        $("#txnId").val("");
        $("#ccTpdId").val("");
        $("#type").val("");
        $("#lossType").val("");
        $("#itemType").val("");
        $("#vehicleNo").val("");
        $("#contactNo").val("");
        $("#insureDetails").val("");
        $("#remark").val("");
        $("#key").val("");

        $("input[name=thirdPartyInvolved]").prop("checked", false);
        $("#thirdPartyInvolved_N").prop("checked", true);
        $("input[name=intendClaim]").prop("checked", false);
    }

    function disable_3rd_Party_details(stat) {
        if (stat == "N") {
            $(".disable").addClass("text-mute").prop('disabled', true);
            $("#addbtn").prop('disabled', true);
            resetForm()
        } else {
            $(".disable").removeClass("text-mute").attr('disabled', false);
            $("#addbtn").prop('disabled', false);
            vehicleNoFieldStatChange($('#itemType').val());

        }

    }


    function vehicleNoFieldStatChange(val) {
        if (1 == val) {
            $("#vehicleNo").prop('disabled', false);
        } else {
            $("#vehicleNo").prop('disabled', true).val("");
        }
    }

    $(function () {
        if ('${thirdPartyDto.thirdPartyInvolved}' == 'Y') {
            disable_3rd_Party_details('Y');
        }
    });


    $('[data-toggle="collapse"]').click(function () {
        $('body').animate({
            scrollTop: 0
        });
    });
    // $("#inspectDateTime").datetimepicker({
    //     sideBySide: true,
    //     format: 'YYYY-MM-DD HH:mm',
    //     icons: {
    //         time: "fa fa-clock-o",
    //         date: "fa fa-calendar",
    //         up: "fa fa-arrow-up",
    //         down: "fa fa-arrow-down"
    //     }
    // });

    var currentDate = '${Current_Date}';
    var accidentDate = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.accidDate}' + ' ' + '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.accidTime}';
    // $("#inspectDateTime").data("DateTimePicker").maxDate(currentDate);
    // $("#inspectDateTime").data("DateTimePicker").minDate(accidentDate);

    function enableDisableFirstStatement(stat) {
        if (stat == "Y") {
            $("#firstStatementReqReason").prop('disabled', false).trigger("chosen:updated");
        } else {
            $("#firstStatementReqReason").val('').prop('disabled', true).trigger("chosen:updated");
        }
    }


    $(document).ready(function () {
        if (isOnsiteOrOffsite('${motorEngineerDto.inspectionDto.inspectionId}')) {
            $("#jobType option[value='0']").prop('disabled', false);
            $("#jobType option[value='2']").prop('disabled', false);
        } else {
            $("#jobType option[value='0']").prop('disabled', true);
            $("#jobType option[value='1']").prop('selected', true);
            $("#jobType option[value='2']").prop('disabled', true);
        }

        if (${PREVIOUS_INSPECTION eq 'Y'}) {
            disableFormInputs('#frmForm');
            $('#changeSpecialRemark').prop("readonly", false);
        }


    });

    function isOnsiteOrOffsite(typeId) {
        if ('1' == typeId || '2' == typeId) {
            return true;
        } else {
            return false;
        }
    }

    function chassis_correct(stat) {
        if ($('#engNoConfirmbox').is(':checked')) {
            $(".correctChaNo").removeClass("text-mute").prop('disabled', false);
        } else {
            $(".correctChaNo").addClass("text-mute").prop('disabled', true);
        }
    }


    function goBack() {
        bootbox.confirm({
            message: "Are you sure you want to close this Page?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }

            },
            callback: function (result) {
                if (result == true) {
                    document.frmForm.action = contextPath + "/CalculationSheetController/claimCalList?TYPE=6";
                    document.frmForm.submit();
                }
            }
        });
    }


    function isRemark() {
        var remark = $("#changeSpecialRemark").val();
        if (remark != '') {
            $('#changeBtn').prop("disabled", false);
            $('#remarkDiv').hide();
        }
    }

    function updateAsChangeRequest() {
        var remark = $("#changeAsSpecialRemark").val();
        var refNo =${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo};

        if (remark != '') {
            $.ajax({
                url: contextPath + "/MotorEngineerController/updateAsChangeRequest?refNo=" + refNo + "&remark=" + remark,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != '') {
                        notify(obj, "success");
                        $('#asChangeBtn').prop("disabled", true);
                    } else {
                        notify("Can not be updated", "danger");
                    }

                }
            });
        } else {
            $('#asRemarkDiv').show();
            $('#asChangeBtn').prop("disabled", true);
        }

    }

    function isAsRemark() {
        var remark = $("#changeAsSpecialRemark").val();
        if (remark != '') {
            $('#asChangeBtn').prop("disabled", false);
            $('#asRemarkDiv').hide();
        }
    }

    $('#addRTERemarks').click(function (e) {
        var formData = $('#frmMain').serialize();
        $.ajax({
            type: 'POST',
            url: "${pageContext.request.contextPath}/MotorEngineerController/addRteSpecialRemarks",
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    // alert(obj);
                    document.getElementById('inspectionSpecialRemark').value = ''
                    notify(obj, "success");

                }
                loadSpecialRemarks();
            }
        });
    });

    let refNos = [];

    function selectRte(jobRefNo) {

        let rteArray = [];

        $.ajax({
            url: contextPath + "/MotorEngineerController/getReportingRteList",
            type: 'POST',
            success: function (result) {

                let option1 = {text: 'Please Select', value: ''};
                rteArray.push(option1);

                let rteList = JSON.parse(result);

                for (let i = 0; i < rteList.length; i++) {
                    let option = {text: rteList[i].userId, value: rteList[i].userId};
                    rteArray.push(option);
                }

                bootbox.prompt({
                    title: "Please select user to forward",
                    inputType: 'select',
                    inputOptions: rteArray,
                    callback: function (result) {
                        if (result === null) {
                            // Prompt dismissed
                        } else {
                            if (result === '') {
                                return false;
                            }
                            $.ajax({
                                type: 'POST',
                                url: "${pageContext.request.contextPath}/MotorEngineerController/forwardToRte",
                                data: {
                                    N_REF_NO: jobRefNo,
                                    V_ASSIGN_RTE: result,
                                    N_CLAIM_NO: ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}
                                },
                                success: function (result) {
                                    var obj = JSON.parse(result);
                                    if (obj != null) {
                                        notify(obj, "success");
                                        document.getElementById(jobRefNo).disabled = true;
                                        loadSuppyOrderView();
                                        loadPaymentOptionPage();
                                    }
                                }
                            });
                        }
                    }
                });
            }
        });
    }
</script>
<c:if test="${isDesktopInspection}">
    <script type="text/javascript">
        $('.assessor_value').hide();
        $(".correctChaNo").removeClass("text-mute").prop('disabled', false);
    </script>
</c:if>
</body>
<script>
    hideLoader();
</script>
</html>

