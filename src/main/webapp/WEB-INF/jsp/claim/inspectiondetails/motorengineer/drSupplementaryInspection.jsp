<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="">
    <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
        <fieldset class="border p-2 mt-2">
            <h6> DR & Supplementary</h6>
            <hr class="my-1">
                <%--<div class="form-group row">--%>
                <%--<label class="col-sm-4 col-form-label">Sum Insured (Rs) :</label>--%>
                <%--<div class="col-sm-8 input-group">--%>
                <%--<span class="label_Value input-view text-primary text-right">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.sumInsured}</span>--%>
                <%--<input name="drSuppInspectionDetailsDto.sumInsured" placeholder="Sum Insured" value="${isEmptyValue? '':motorEngineerDto.drSuppInspectionDetailsDto.sumInsured}" class="form-control form-control-sm  suminsured" id=" ">--%>
                <%--</div>--%>
                <%--</div>--%>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label text-danger font-weight-bold">Total Approve ACR (Rs.) :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-danger font-weight-bold"></span>
                    <span class="label_Value input-view text-danger font-weight-bold" id="totalAcr"><fmt:formatNumber
                            value="${motorEngineerDto.totalApproveAcrAmount}"
                            pattern="###,##0.00;"
                            type="number"/></span>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary text-right">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.acr}</span>
                    <input name="drSuppInspectionDetailsDto.acr" placeholder="ACR"
                           value="${isEmptyValue? '':motorEngineerDto.drSuppInspectionDetailsDto.acr}"
                           class="form-control form-control-sm acr" name="acr" id="acr" autocomplete="off">
                </div>
                <c:if test="${!(motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14))}">
                    <div class="col-sm-12 text-right text-danger">
                        <label>(This value will be added to the Total Approved ACR)</label>
                    </div>
                </c:if>
            </div>

            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Assessor Remarks :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.assessorRemark}</span>
                    <textarea name="drSuppInspectionDetailsDto.assessorRemark" class="form-control form-control-sm "
                              id=" ">${motorEngineerDto.drSuppInspectionDetailsDto.assessorRemark}</textarea>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ARI and Salvage<span
                        class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8  input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.ariAndSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="Yes" ${motorEngineerDto.drSuppInspectionDetailsDto.ariAndSalvage eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                   name="drSuppInspectionDetailsDto.ariAndSalvage" type="radio"
                                   class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input value="No" ${motorEngineerDto.drSuppInspectionDetailsDto.ariAndSalvage eq 'No' and not isEmptyValue ? 'checked' : ''}
                                   name="drSuppInspectionDetailsDto.ariAndSalvage" type="radio"
                                   class="align-middle arisalvage"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Special Remarks
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.specialRemark}</span>
                    <textarea name="drSuppInspectionDetailsDto.specialRemark" class="form-control form-control-sm "
                              id="specialRemark">${motorEngineerDto.drSuppInspectionDetailsDto.specialRemark}</textarea>
                </div>
            </div>
            <div class="mt-3">
                <c:if test="${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && (motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 14))  || (G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser && motorEngineerDto.inspectionDetailsDto.recordStatus ne 80) || G_USER.accessUserType eq 1 || G_USER.accessUserType eq 23 || G_USER.accessUserType eq 24}">
                    <div class="float-right" style="display: none" id="forwardDiv">
                        <button type="submit" name="cmdReject" id="forward"
                                value="forward"
                                class="btn btn-primary"  ${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or  (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 80 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14) ? 'disabled' : ''}>
                            Forward
                        </button>
                    </div>

                    <div class="float-right">
                        <c:if test="${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && motorEngineerDto.inspectionDetailsDto.recordStatus eq 80)}">
                            <button id="btnReturn" value="return"
                                    class=" btn btn-danger">
                                Return
                            </button>
                        </c:if>
                        <button type="submit" name="cmdReject" id="cmdAuth"
                                value="Authorize"
                                class="btn btn-primary"  ${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14) ? 'disabled' : ''}>
                            Authorize
                        </button>
                    </div>
                </c:if>
            </div>
        </fieldset>
    </c:if>
    <fieldset class="border p-2 mt-2 my-2">
        <jsp:include page="../motorengineer/assessorPayment.jsp"/>
    </fieldset>
</div>
