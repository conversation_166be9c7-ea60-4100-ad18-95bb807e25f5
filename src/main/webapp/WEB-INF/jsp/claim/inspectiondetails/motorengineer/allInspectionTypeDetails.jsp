<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<c:forEach var="motorEngineerDto" items="${motorEngineerDetailsList}" varStatus="status">
    <div class="card mt-2">
        <div class="card-header p-0" id="heading3${status.index}">
            <h5 class="mb-0">
                <a class="btn btn-link" data-toggle="collapse"
                   data-target="#collapse3${status.index}"
                   aria-expanded="true" aria-controls="collapse3${status.index}">
                        ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}
                    Review
                </a>
            </h5>
        </div>
        <div id="collapse3${status.index}" class="collapse"
             aria-labelledby="heading3${status.index}"
             data-parent="#accordionOne">
            <div class="card-body p-lg-3 p-2">
                <div class="row">
                    <div id="estimationDiv" class="col-lg-12">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Inspection Type
                                :</label>
                            <div class="col-sm-8">
                                <span class="label_Value input-view"
                                >${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}</span>
                            </div>
                        </div>
                        <c:choose>

                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 4}">
                                <div class="">
                                    <fieldset class="border p-2 mt-2">
                                        <h6>Garage Assessment</h6>
                                        <hr class="my-1">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.acr}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.acr}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Excess (Rs.) :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.excess}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.excess}</span>
                                            </div>
                                        </div>
                                        <input type="hidden" name="garageInspectionDetailsDto.boldPercent"/>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Bald Tyre Penalty (Rs.)
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.boldTyrePenaltyAmount}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.boldTyrePenaltyAmount}</span>
                                                    <%--<input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.boldTyrePenaltyAmount}" placeholder="Bald Tyre Penalty" class="form-control form-control-sm excess text-right" id="boldTyrePenaltyAmount" name="garageInspectionDetailsDto.boldTyrePenaltyAmount">--%>
                                            </div>
                                        </div>
                                        <input type="hidden" name="garageInspectionDetailsDto.underPenaltyPercent"/>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Under Insurance Penalty (Rs.)
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.underInsurancePenaltyAmount}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.underInsurancePenaltyAmount}</span>
                                                    <%--<input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.underInsurancePenaltyAmount}" placeholder="Under Insurance Penalty" class="form-control form-control-sm underpenaltyamount text-right" id="underInsurancePenaltyAmount" name="garageInspectionDetailsDto.underInsurancePenaltyAmount" >--%>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Payable Amount (Rs.) :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.payableAmount}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.payableAmount}</span>
                                                    <%--<input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.payableAmount}" placeholder="Payable Amount" class="form-control form-control-sm payableamount text-right" id="payableAmount" name="garageInspectionDetailsDto.payableAmount">--%>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Advance Amount (Rs.)
                                                <span class="text-danger font-weight-bold"> *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.advancedAmount}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.advancedAmount}</span>
                                                    <%--<input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.advancedAmount}" placeholder="Advance Amount" class="form-control form-control-sm advancedamount text-right" id="" name="garageInspectionDetailsDto.advancedAmount">--%>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2">
                                        <h6> Assessor Remarks</h6>
                                        <hr class="my-1">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Settlement Methods :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">
                                                        ${DbRecordCommonFunctionBean.getValue("claim_settlement_method", "value", "settlement_id", motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.settlementMethod)}
                                                </span>
                                                <span class="label_Value input-view text-success">
                                                        ${DbRecordCommonFunctionBean.getValue("claim_settlement_method", "value", "settlement_id", motorEngineerDto.garageInspectionDetailsDto.settlementMethod)}
                                                </span>
                                            </div>
                                                <%--<script>document.getElementById("settlementMethod").value = "${motorEngineerDto.garageInspectionDetailsDto.settlementMethod}";</script>--%>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Offer Amount (Rs.) :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.offerAmount}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.offerAmount}</span>
                                                    <%--<input value="${isEmptyValue? '':motorEngineerDto.garageInspectionDetailsDto.offerAmount}" placeholder="Offer Amount" class="form-control form-control-sm offeramount text-right" id=" " name="garageInspectionDetailsDto.offerAmount">--%>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Inspection Remarks :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.inspectionRemark}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.inspectionRemark}</span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">ARI and Salvage<span
                                                    class="text-danger font-weight-bold">  *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.garageInspectionDetailsDto.ariAndSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.garageInspectionDetailsDto.ariAndSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                                            </div>
                                        </div>

                                        <div class="mt-3">
                                            <div class="float-right">

                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2 my-2">
                                        <div class="assessor_pmt_div">
                                            <h6> Assessor Payment</h6>
                                            <hr class="my-1">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Job Type
                                                        <span class="text-danger font-weight-bold"> *</span>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">
                                                        <c:choose>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 0}">
                                                                -- Please Select --
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 1}">
                                                                DAY
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 2}">
                                                                NIGHT
                                                            </c:when>
                                                        </c:choose>
                                                    </span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">
                                                        <c:choose>
                                                            <c:when test="${motorEngineerDto.jobType eq 0}">
                                                                -- Please Select --
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.jobType eq 1}">
                                                                DAY
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.jobType eq 2}">
                                                                NIGHT
                                                            </c:when>
                                                        </c:choose>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Assigned Location
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.assignedLocation}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.assignedLocation}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Place of Inspection
                                                    :</label>
                                                </div>

                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.placeOfInspection}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.placeOfInspection}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Mileage
                                                        <span class="text-danger font-weight-bold"> *</span>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.mileage}</span>

                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.mileage}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Other Fee (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.otherFee}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col-sm-4">
                                                     <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                             value="${motorEngineerDto.otherFee}"
                                                             pattern="###,##0.00;"
                                                             type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Cost Of Call (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.costOfCall}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col-sm-4">
                                                     <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                             value="${motorEngineerDto.costOfCall}"
                                                             pattern="###,##0.00;"
                                                             type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Deductions (Rs)
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary"></span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.deductions}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class=" col-form-label">Reason of Deduction
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary"></span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.reasonOfDeduction}</span>
                                                </div>

                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Total Fee (Rs)
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.totalAssessorFee}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col sm-4">
                                                     <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                             value="${motorEngineerDto.totalAssessorFee}"
                                                             pattern="###,##0.00;"
                                                             type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Description
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.feeDesc}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.feeDesc}</span>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <div class="float-right">

                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2 my-2">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Special Remarks :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.garageInspectionDetailsDto.specialRemark}</span>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </c:when>
                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 5 || motorEngineerDto.inspectionDto.inspectionId == 6 }">
                                <div class="">
                                    <fieldset class="border p-2 mt-2">
                                        <h6> DR & Supplementary</h6>
                                        <hr class="my-1">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Sum Insured (Rs) :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary text-left">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.sumInsured}</span>
                                                <span class="label_Value input-view text-success text-left">${motorEngineerDto.drSuppInspectionDetailsDto.sumInsured}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary text-left">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.acr}</span>
                                                <span class="label_Value input-view text-success text-left">${motorEngineerDto.drSuppInspectionDetailsDto.acr}</span>
                                            </div>
                                        </div>

                                        <hr class="my-1">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Assessor Remarks :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.assessorRemark}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.drSuppInspectionDetailsDto.assessorRemark}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">ARI and Salvage<span
                                                    class="text-danger font-weight-bold">  *</span>
                                                :</label>
                                            <div class="col-sm-8  input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.drSuppInspectionDetailsDto.ariAndSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.drSuppInspectionDetailsDto.ariAndSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="float-right">

                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2 my-2">
                                        <div class="assessor_pmt_div">
                                            <h6> Assessor Payment</h6>
                                            <hr class="my-1">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Job Type
                                                        <span class="text-danger font-weight-bold"> *</span>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">
                                                        <c:choose>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 0}">
                                                                -- Please Select --
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 1}">
                                                                DAY
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 2}">
                                                                NIGHT
                                                            </c:when>
                                                        </c:choose>
                                                    </span>
                                                </div>
                                                <div class="col-sm-4">
                                                  <span class="label_Value input-view text-success">
                                                        <c:choose>
                                                            <c:when test="${motorEngineerDto.jobType eq 0}">
                                                                -- Please Select --
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.jobType eq 1}">
                                                                DAY
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.jobType eq 2}">
                                                                NIGHT
                                                            </c:when>
                                                        </c:choose>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Assigned Location
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.assignedLocation}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.assignedLocation}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Place of Inspection
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>

                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.placeOfInspection}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.placeOfInspection}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Mileage
                                                        <span class="text-danger font-weight-bold"> *</span>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.mileage}</span>

                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.mileage}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Other Fee (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.otherFee}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col-sm-4">
                                                 <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                         value="${motorEngineerDto.otherFee}"
                                                         pattern="###,##0.00;"
                                                         type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Cost of Call (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.costOfCall}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col-sm-4">
                                                 <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                         value="${motorEngineerDto.costOfCall}"
                                                         pattern="###,##0.00;"
                                                         type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Deductions (Rs)
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary"></span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.deductions}</span>

                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class=" col-form-label">Reason of Deduction
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary"></span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.reasonOfDeduction}</span>
                                                </div>

                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Total Fee (Rs)
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.totalAssessorFee}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col sm-4">
                                                     <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                             value="${motorEngineerDto.totalAssessorFee}"
                                                             pattern="###,##0.00;"
                                                             type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Description
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.feeDesc}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.feeDesc}</span>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <div class="float-right">

                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </c:when>
                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 7 || motorEngineerDto.inspectionDto.inspectionId == 9 }">
                                <fieldset class="border p-2 mt-2">
                                    <div class="row">
                                        <label class="col-sm-4 col-form-label">ARI</label>
                                        <span class="label_Value input-view text-primary col-1">${motorEngineerDto.inspectionDetailsDto.ariInspectionDetailsDto.isAri eq 'Yes' ? 'Yes' : 'No'}</span>
                                        <span class="label_Value input-view text-success col-1">${motorEngineerDto.ariInspectionDetailsDto.isAri eq 'Yes' ? 'Yes' : 'No'}</span>
                                    </div>
                                    <hr class="my-1">
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">ARI in Order</label>
                                        <span class="label_Value input-view text-primary col-1">${motorEngineerDto.inspectionDetailsDto.ariInspectionDetailsDto.ariOrder eq 'Yes' ? 'Yes' : 'No'}</span>
                                        <span class="label_Value input-view text-success col-1">${motorEngineerDto.ariInspectionDetailsDto.ariOrder eq 'Yes' ? 'Yes' : 'No'}</span>
                                    </div>
                                    <div class="row mt-3">
                                        <label class="col-sm-4 col-form-label">Salvage</label>
                                        <span class="label_Value input-view text-primary col-1">${motorEngineerDto.inspectionDetailsDto.ariInspectionDetailsDto.isSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                                        <span class="label_Value input-view text-success col-1">${motorEngineerDto.ariInspectionDetailsDto.isSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
                                    </div>
                                    <hr class="my-1">
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Salvage in Order</label>
                                        <span class="label_Value input-view text-primary col-1">${motorEngineerDto.inspectionDetailsDto.ariInspectionDetailsDto.salvageOrder eq 'Yes' ? 'Yes' : 'No'}</span>
                                        <span class="label_Value input-view text-success col-1">${motorEngineerDto.ariInspectionDetailsDto.salvageOrder eq 'Yes' ? 'Yes' : 'No'}</span>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Assessor Special Remarks :</label>
                                        <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.ariInspectionDetailsDto.assessorSpecialRemark}</span>
                                        <span class="label_Value input-view text-success">${motorEngineerDto.ariInspectionDetailsDto.assessorSpecialRemark}</span>
                                    </div>
                                    <div class="mt-3">
                                        <div class="float-right">

                                        </div>
                                    </div>
                                </fieldset>
                                <fieldset class="border p-2 mt-2 my-2">
                                    <div class="assessor_pmt_div">
                                        <h6> Assessor Payment</h6>
                                        <hr class="my-1">
                                        <div class="form-group row">
                                            <div class="col-sm-4">
                                                <label class="col-form-label">Job Type
                                                    :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary">
                                                    <c:choose>
                                                        <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 1}">
                                                            DAY
                                                        </c:when>
                                                        <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 2}">
                                                            NIGHT
                                                        </c:when>
                                                    </c:choose>
                                                </span>
                                            </div>
                                            <div class="col-sm-4">
                                                 <span class="label_Value input-view text-success">
                                                    <c:choose>
                                                        <c:when test="${motorEngineerDto.jobType eq 1}">
                                                            DAY
                                                        </c:when>
                                                        <c:when test="${motorEngineerDto.jobType eq 2}">
                                                            NIGHT
                                                        </c:when>
                                                    </c:choose>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <div class="col-sm-4">
                                                <label class="col-form-label">Assigned Location
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.assignedLocation}</span>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-success">${motorEngineerDto.assignedLocation}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4"><label class="col-form-label">Place of Inspection
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            </div>

                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.placeOfInspection}</span>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-success">${motorEngineerDto.placeOfInspection}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4">
                                                <label class="col-form-label">Mileage
                                                    <span class="text-danger font-weight-bold"> *</span>
                                                    :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.mileage}</span>

                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-success">${motorEngineerDto.mileage}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4"><label class="col-form-label">Other Fee (Rs)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.otherFee}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                            <div class="col-sm-4">
                                              <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                      value="${motorEngineerDto.otherFee}"
                                                      pattern="###,##0.00;"
                                                      type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4"><label class="col-form-label">Cost of Call (Rs)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.costOfCall}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                            <div class="col-sm-4">
                                              <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                      value="${motorEngineerDto.costOfCall}"
                                                      pattern="###,##0.00;"
                                                      type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4">
                                                <label class="col-form-label">Deductions (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary"></span>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-success">${motorEngineerDto.deductions}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4">
                                                <label class=" col-form-label">Reason of Deduction
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary"></span>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.reasonOfDeduction}</span>
                                            </div>

                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4">
                                                <label class="col-form-label">Total Fee (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.totalAssessorFee}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                            <div class="col sm-4">
                                                  <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                          value="${motorEngineerDto.totalAssessorFee}"
                                                          pattern="###,##0.00;"
                                                          type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-sm-4">
                                                <label class="col-form-label">Description
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.feeDesc}</span>
                                            </div>
                                            <div class="col-sm-4">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.feeDesc}</span>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="float-right">

                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                                <script type="text/javascript">
                                    function setAriInOrder() {
                                        if ($('#isAriCheckbox').is(':checked')) {
                                            $("#isAri").val("Yes");
                                            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('disabled', false);
                                        } else {
                                            $("#isAri").val("No");
                                            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('checked', false);
                                            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('disabled', true);
                                        }
                                    }

                                    function setSalvageInOrder() {
                                        if ($('#isSalvageCheckbox').is(':checked')) {
                                            $("#isSalvage").val("Yes");
                                            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('disabled', false);
                                        } else {
                                            $("#isSalvage").val("No");
                                            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('checked', false);
                                            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('disabled', true);
                                        }
                                    }
                                </script>
                            </c:when>
                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 8}">
                                <div class="">
                                    <fieldset class="border p-2 my-2">
                                        <h6> Offer</h6>
                                        <hr class="my-1">
                                        <div class="form-group row" style="display: none">
                                            <label class="col-sm-4 col-form-label">Provide Offer?
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <div class="row">
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.desktopInspectionDetailsDto.provideOffer eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                                                name="desktopInspectionDetailsDto.provideOffer"
                                                                type="radio" value="Yes"
                                                                class="align-middle provideoffer"
                                                                onclick="setOfferType(1)"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Yes</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.desktopInspectionDetailsDto.provideOffer eq 'No' and not isEmptyValue ? 'checked' : ''}
                                                                name="desktopInspectionDetailsDto.provideOffer"
                                                                type="radio" value="No"
                                                                class="align-middle provideoffer"
                                                                onclick="setOfferType(3)"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">No</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row" style="display: none">
                                            <label class="col-sm-4 col-form-label">Offer Type
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.offerType}</span>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2">
                                        <h6>Desktop Review</h6>
                                        <hr class="my-1">
                                        <div class="form-group row" style="display: none">
                                            <label class="col-sm-4 col-form-label">Approximate Cost Report :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.appCostReport}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Pre Accident Value (Rs.) :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.preAccidentValue}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">ACR (Rs.) :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.acr}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Excess (Rs.) :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.excess}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Bald Tyre Penalty
                                                :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.boldTyrePenaltyAmount}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Under Insurance Penalty: </label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.underInsurancePenaltyAmount}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Payable Amount (Rs.) :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.payableAmount}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Desktop Offer
                                                <span class="text-danger font-weight-bold"> *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <div class="row">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.desktopOffer}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">ARI Salvage
                                                <span class="text-danger font-weight-bold"> *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <div class="row">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.ariSalvage}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2">
                                        <h6> Assessor Remarks</h6>
                                        <hr class="my-1">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Settlement Methods :</label>
                                            <div class="col-sm-8">
                                                <c:choose>
                                                    <c:when test="${motorEngineerDto.desktopInspectionDetailsDto.settlementMethod eq 1}">
                                                        <span class="label_Value input-view text-primary">Partial Loss</span>
                                                    </c:when>
                                                    <c:when test="${motorEngineerDto.desktopInspectionDetailsDto.settlementMethod eq 2}">
                                                        <span class="label_Value input-view text-primary">Total Loss</span>
                                                    </c:when>
                                                    <c:when test="${motorEngineerDto.desktopInspectionDetailsDto.settlementMethod eq 3}">
                                                        <span class="label_Value input-view text-primary">Cash In Lieu</span>
                                                    </c:when>
                                                </c:choose>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Inspection Remarks :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.inspectionRemark}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Police Report Request<span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8 input-group">
                                                <div class="row">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.policeReportRequested}</span>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Special Remarks :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.specialRemark}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Investigate Claim
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <div class="row">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.investigaedClaim}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="float-right">
                                                <input type="hidden" name="forwardToInformDesktopUser"
                                                       id="forwardToInformDesktopUser"/>
                                            </div>
                                        </div>
                                    </fieldset>

                                    <fieldset class="border p-2 mt-2">
                                        <div class="mt-3">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Inform To Garage - Name :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.informToGarageName}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Inform To Garage - Contact
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.informToGarageContact}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label"><span
                                                        class="text-danger font-weight-bold">  </span>
                                                </label>
                                                <div class="col-sm-8 input-group">
                                                    <div class="row">
                                                        <span class="label_Value input-view text-primary">${motorEngineerDto.desktopInspectionDetailsDto.isAgreeGarage}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Inform to Customer - Name
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.desktopInspectionDetailsDto.informToCustomerName}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Inform to Customer - Contact
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.desktopInspectionDetailsDto.informToCustomerContact}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label"><span
                                                        class="text-danger font-weight-bold">  </span>
                                                </label>
                                                <div class="col-sm-8 input-group">
                                                    <div class="row">
                                                        <span class="label_Value input-view text-success">${motorEngineerDto.desktopInspectionDetailsDto.isAgreeCustomer}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Comment :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.desktopInspectionDetailsDto.desktopComment}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Reason :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.desktopInspectionDetailsDto.reasonForDisagree}</span>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <div class="float-right">

                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>

                            </c:when>
                            <c:otherwise>
                                <div class="">
                                    <fieldset class="border p-2 my-2">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Provide Offer?
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.provideOffer eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.onSiteInspectionDetailsDto.provideOffer eq 'Yes' ? 'Yes' : 'No'}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Offer Type
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">
                                                        ${DbRecordCommonFunctionBean.getValue("claim_offer_type", "V_OFFER_TYPE_DESC", "N_OFFER_TYPE_ID", motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.offerType)  eq 'Please Select' ==''}
                                                </span>
                                                <span class="label_Value input-view text-success">
                                                        ${DbRecordCommonFunctionBean.getValue("claim_offer_type", "V_OFFER_TYPE_DESC", "N_OFFER_TYPE_ID", motorEngineerDto.onSiteInspectionDetailsDto.offerType) eq 'Please Select' =='' }
                                                </span>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 my-2">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Cost of Parts * (Rs.)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary "><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.costPart}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                                <span class="label_Value input-view text-success "><fmt:formatNumber
                                                        value="${motorEngineerDto.onSiteInspectionDetailsDto.costPart}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Cost of Labour (Rs.)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary"><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.costLabour}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                                <span class="label_Value input-view text-success"><fmt:formatNumber
                                                        value="${motorEngineerDto.onSiteInspectionDetailsDto.costLabour}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Excess (Rs.)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <fmt:formatNumber var="policyDtoExcessLbl"
                                                                  value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                                                  pattern="###,##0.00;(###,##0.00)" type="number"/>
                                                <span class="label_Value input-view text-primary">${policyDtoExcessLbl}</span>
                                                <fmt:formatNumber var="policyDtoExcess"
                                                                  value="${motorEngineerDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                                                  pattern="###,##0.00;(###,##0.00)" type="number"/>
                                                <span class="label_Value input-view text-success">${policyDtoExcess}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">ACR (Rs.) *
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span
                                                        class="label_Value input-view text-primary"><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.acr}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                                <span
                                                        class="label_Value input-view text-success"><fmt:formatNumber
                                                        value="${motorEngineerDto.onSiteInspectionDetailsDto.acr}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Request ARI:</label>
                                            <div class="col-sm-8  input-group">

                                                <span class="label_Value input-view text-success"></span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.onSiteInspectionDetailsDto.requestAri eq 'Yes' ? 'Yes' : 'No'}</span>

                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2">
                                        <h6> Penalty</h6>
                                        <hr class="my-1">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Bald Tyre Penalty<span
                                                    class="text-danger font-weight-bold">  *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.boldTyrePenalty eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.onSiteInspectionDetailsDto.boldTyrePenalty eq 'Yes' ? 'Yes' : 'No'}</span>

                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Penalty Percentage
                                                (%)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.boldPercent}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.onSiteInspectionDetailsDto.boldPercent}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Penalty Amount (Rs.)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary"><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.boldTirePenaltyAmount}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                                <span class="label_Value input-view text-success"><fmt:formatNumber
                                                        value="${motorEngineerDto.onSiteInspectionDetailsDto.boldTirePenaltyAmount}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Under Insurance Penalty<span
                                                    class="text-danger font-weight-bold">  *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.underInsuradPenalty eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.onSiteInspectionDetailsDto.underInsuradPenalty eq 'Yes' ? 'Yes' : 'No'}</span>

                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Penalty Percentage
                                                (%)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.underPenaltyPercent}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.onSiteInspectionDetailsDto.underPenaltyPercent}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Penalty Amount (Rs.)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary"><fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.underPenaltyAmount}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                                <span class="label_Value input-view text-success"><fmt:formatNumber
                                                        value="${motorEngineerDto.onSiteInspectionDetailsDto.underPenaltyAmount}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Payable Amount (Rs.)
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary"> <fmt:formatNumber
                                                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.payableAmount}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                                <span class="label_Value input-view text-success"> <fmt:formatNumber
                                                        value="${motorEngineerDto.onSiteInspectionDetailsDto.payableAmount}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Special Remarks
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.specialRemark}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.onSiteInspectionDetailsDto.specialRemark}</span>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="float-right">

                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="border p-2 mt-2 my-2">
                                        <div id="assessorPmtDiv" class="assessor_pmt_div">
                                            <h6> Assessor Payment</h6>
                                            <hr class="my-1">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Job Type
                                                        <span class="text-danger font-weight-bold"> *</span>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">
                                                        <c:choose>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 1}">
                                                                DAY
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.inspectionDetailsDto.jobType eq 2}">
                                                                NIGHT
                                                            </c:when>
                                                        </c:choose>
                                                    </span>
                                                </div>
                                                <div class="col-sm-4">
                                                      <span class="label_Value input-view text-success">
                                                        <c:choose>
                                                            <c:when test="${motorEngineerDto.jobType eq 1}">
                                                                DAY
                                                            </c:when>
                                                            <c:when test="${motorEngineerDto.jobType eq 2}">
                                                                NIGHT
                                                            </c:when>
                                                        </c:choose>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Assigned Location
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.assignedLocation}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.assignedLocation}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Place of Inspection
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>

                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.placeOfInspection}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.placeOfInspection}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Mileage
                                                        <span class="text-danger font-weight-bold"> *</span>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.mileage}</span>

                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.mileage}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Other Fee (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.otherFee}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col-sm-4">
                                                  <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                          value="${motorEngineerDto.otherFee}"
                                                          pattern="###,##0.00;"
                                                          type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4"><label class="col-form-label">Cost Of Call (Rs)
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.costOfCall}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col-sm-4">
                                                  <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                          value="${motorEngineerDto.costOfCall}"
                                                          pattern="###,##0.00;"
                                                          type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Deductions (Rs)
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary"></span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.deductions}</span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class=" col-form-label">Reason of Deduction
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary"></span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.reasonOfDeduction}</span>
                                                </div>

                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Total Fee (Rs)
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary text-left"><fmt:formatNumber
                                                            value="${motorEngineerDto.inspectionDetailsDto.totalAssessorFee}"
                                                            pattern="###,##0.00;"
                                                            type="number"/></span>
                                                </div>
                                                <div class="col sm-4">
                                                         <span class="label_Value input-view text-success text-left"><fmt:formatNumber
                                                                 value="${motorEngineerDto.totalAssessorFee}"
                                                                 pattern="###,##0.00;"
                                                                 type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <label class="col-form-label">Description
                                                            <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                        :</label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.feeDesc}</span>
                                                </div>
                                                <div class="col-sm-4">
                                                    <span class="label_Value input-view text-success">${motorEngineerDto.feeDesc}</span>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <div class="float-right">

                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </div>
        </div>
    </div>
</c:forEach>