<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Document Viewer</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/resources/pdfviewer/web/viewer.css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/pdfviewer/web/viewer.js"></script>

    <script>
        function selectDocument(id) {
            $( "#documentView"+id ).addClass( "img-mag-selected" );
        }
    </script>

    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="claimDocumentDtoList" value="${claimDocumentDtoList}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="claimDocumentDtoList" value="${historyClaimDocumentDtoList}" scope="request"/>
        </c:when>
    </c:choose>

</head>
<body class="scroll" onload="selectDocument(${refNo});">
<form name="frmDocumentUpload" id="frmDocumentUpload" method="post">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 text-center mt-2 text-light">
                <span>Page: <span id="page_num"></span> / <span id="page_count"></span></span>
            </div>
            <div class="col-12 text-center mt-2">
                <canvas id="the-canvas" style="padding-bottom:70px; "></canvas>
            </div>
            <div class="col-12 text-center mt-2" style="display: none;">
                <button class="btn btn-light" id="prev"><i class="fa fa-angle-left"></i></button>
                <button class="btn btn-light" id="next"><i class="fa fa-angle-right"></i></button>
            </div>
            <div class="col-12 text-center mt-2 bg-light pdf-thumbnails" style="position: fixed; bottom: 0;">
                <c:forEach var="claimDocumentDto" items="${claimDocumentDtoList}">
                    <a href="${pageContext.request.contextPath}/InspectionDetailsController/documentViewer?refNo=${claimDocumentDto.refNo}&jobRefNo=${claimDocumentDto.jobRefNo}" class="">
                        <span><i class="fa fa-file-pdf-o fa-4x m-3 " id="documentView${claimDocumentDto.refNo}" ></i></span>
                    </a>
                </c:forEach>

            </div>
        </div>
    </div>
</form>
<script>
    // If absolute URL from the remote server is provided, configure the CORS
    // header on that server.
    var url = '${pageContext.request.contextPath}/DocumentViewController?refNo=${refNo}';

    // Loaded via <script> tag, create shortcut to access PDF.js exports.
    var pdfjsLib = window['pdfjs-dist/build/pdf'];

    // The workerSrc property shall be specified.
    pdfjsLib.GlobalWorkerOptions.workerSrc = '//mozilla.github.io/pdf.js/build/pdf.worker.js';

    var pdfDoc = null,
        pageNum = 1,
        pageRendering = false,
        pageNumPending = null,
        scale = 0.7,
        canvas = document.getElementById('the-canvas'),
        ctx = canvas.getContext('2d');

    /**
     * Get page info from document, resize canvas accordingly, and render page.
     * @param num Page number.
     */
    function renderPage(num) {
        pageRendering = true;
        // Using promise to fetch the page
        pdfDoc.getPage(num).then(function (page) {
            var viewport = page.getViewport(scale);
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render PDF page into canvas context
            var renderContext = {
                canvasContext: ctx,
                viewport: viewport
            };
            var renderTask = page.render(renderContext);

            // Wait for rendering to finish
            renderTask.promise.then(function () {
                pageRendering = false;
                if (pageNumPending !== null) {
                    // New page rendering is pending
                    renderPage(pageNumPending);
                    pageNumPending = null;
                }
            });
        });

        // Update page counters
        document.getElementById('page_num').textContent = num;
    }

    /**
     * If another page rendering in progress, waits until the rendering is
     * finised. Otherwise, executes rendering immediately.
     */
    function queueRenderPage(num) {
        if (pageRendering) {
            pageNumPending = num;
        } else {
            renderPage(num);
        }
    }

    /**
     * Displays previous page.
     */
    function onPrevPage() {
        if (pageNum <= 1) {
            return;
        }
        pageNum--;
        queueRenderPage(pageNum);
    }

    document.getElementById('prev').addEventListener('click', onPrevPage);

    /**
     * Displays next page.
     */
    function onNextPage() {
        if (pageNum >= pdfDoc.numPages) {
            return;
        }
        pageNum++;
        queueRenderPage(pageNum);
    }

    document.getElementById('next').addEventListener('click', onNextPage);

    /**
     * Asynchronously downloads PDF.
     */
    pdfjsLib.getDocument(url).then(function (pdfDoc_) {
        pdfDoc = pdfDoc_;
        document.getElementById('page_count').textContent = pdfDoc.numPages;

        // Initial/first page rendering
        renderPage(pageNum);
    });
</script>
</body>
</html>
