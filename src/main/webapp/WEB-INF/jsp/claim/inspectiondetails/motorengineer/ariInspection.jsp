<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<fieldset class="border p-2 mt-2">
    <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
        <div class="row">
            <h6 class="pl-3">ARI</h6>
            <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col-1 col-form-label check-container">
                <span class="custom-control-description"></span>
                <input id="isAriCheckbox" onclick="setAriInOrder()" type="checkbox" class="align-middle"
                    ${motorEngineerDto.ariInspectionDetailsDto.isAri eq 'Yes' and not isEmptyValue ? 'checked' : ''} />
                <input type="hidden" name="ariInspectionDetailsDto.isAri" id="isAri"
                       value="${motorEngineerDto.ariInspectionDetailsDto.isAri}"/>
                <span class="Checkmark" style="    top: -1px;left: 20px;"></span>
            </label>
            <span class="label_Value input-view text-primary col-1">${motorEngineerDto.ariInspectionDetailsDto.isAri eq 'Yes' ? 'Yes' : 'No'}</span>
        </div>
        <hr class="my-1">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">ARI in Order<span
                    class="text-danger font-weight-bold">  *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary">${motorEngineerDto.ariInspectionDetailsDto.ariOrder eq 'Yes' ? 'Yes' : 'No'}</span>
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input value="Yes" ${motorEngineerDto.ariInspectionDetailsDto.ariOrder eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                               name="ariInspectionDetailsDto.ariOrder" type="radio"
                               class="align-middle ariinorder"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input value="No" ${motorEngineerDto.ariInspectionDetailsDto.ariOrder eq 'No' and not isEmptyValue ? 'checked' : ''}
                               name="ariInspectionDetailsDto.ariOrder" type="radio"
                               class="align-middle ariinorder"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <h6 class="pl-3">Salvage</h6>
            <label class="custom-control custom-checkbox mb-sm-0 pl-3 col-1 col-form-label check-container">
                <span class="custom-control-description"></span>
                <input id="isSalvageCheckbox" onclick="setSalvageInOrder()" type="checkbox" class="align-middle"
                    ${motorEngineerDto.ariInspectionDetailsDto.isSalvage eq 'Yes' and not isEmptyValue ? 'checked' : ''} />
                <input type="hidden" name="ariInspectionDetailsDto.isSalvage" id="isSalvage"
                       value="${motorEngineerDto.ariInspectionDetailsDto.isSalvage}"/>
                <span class="Checkmark" style="    top: -1px;left: 20px;"></span>
            </label>
            <span class="label_Value input-view text-primary col-1">${motorEngineerDto.ariInspectionDetailsDto.isSalvage eq 'Yes' ? 'Yes' : 'No'}</span>
        </div>
        <hr class="my-1">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Salvage in Order<span
                    class="text-danger font-weight-bold">  *</span>
                :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary">${motorEngineerDto.ariInspectionDetailsDto.salvageOrder eq 'Yes' ? 'Yes' : 'No'}</span>
                <div class="row">
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input value="Yes" ${motorEngineerDto.ariInspectionDetailsDto.salvageOrder eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                               name="ariInspectionDetailsDto.salvageOrder" type="radio"
                               class="align-middle salvageinorder"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                        <input value="No" ${motorEngineerDto.ariInspectionDetailsDto.salvageOrder eq 'No' and not isEmptyValue ? 'checked' : ''}
                               name="ariInspectionDetailsDto.salvageOrder" type="radio"
                               class="align-middle salvageinorder"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Assessor Special Remarks :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary">${motorEngineerDto.ariInspectionDetailsDto.assessorSpecialRemark}</span>
                <textarea name="ariInspectionDetailsDto.assessorSpecialRemark" class="form-control form-control-sm "
                          id=" ">${motorEngineerDto.ariInspectionDetailsDto.assessorSpecialRemark}</textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Special Remarks
                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
            <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary">${motorEngineerDto.ariInspectionDetailsDto.specialRemark}</span>
                <textarea name="ariInspectionDetailsDto.specialRemark" class="form-control form-control-sm "
                          id="specialRemark">${motorEngineerDto.ariInspectionDetailsDto.specialRemark}</textarea>
            </div>
        </div>
        <div class="mt-3 pb-3">
            <div class="float-right">
                <span class="label_Value input-view text-primary" style="padding-right: 0"/>
                <label class="custom-control custom-checkbox mb-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container pull-right"
                       style="padding-right: 0; text-align: right">
                    <input name="ariInspectionDetailsDto.partiallyReview" title="Partially Review"
                           class="align-middle checkbox_check" type="checkbox" id="partiallyReview"
                           value="Y" ${motorEngineerDto.inspectionDetailsDto.ariInspectionDetailsDto.partiallyReview eq 'Y' ? 'checked' : ''}>
                    <span class="checkmark" style="left: 1vw"></span>
                    <span class="custom-control-description">Partially Review </span>
                </label>
            </div>
        </div>
        <br>
        <div class="mt-3">
            <c:if test="${G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser || (G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && motorEngineerDto.inspectionDetailsDto.recordStatus eq 14) || G_USER.accessUserType eq 1 || G_USER.accessUserType eq 23 || G_USER.accessUserType eq 24}">
                <div class="float-right">
                    <button type="submit" name="cmdReject" id="cmdAuth"
                            value="Authorize"
                            class="btn btn-primary"  ${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14) ? 'disabled' : ''}>
                        Authorize
                    </button>
                </div>
            </c:if>
        </div>
    </c:if>
</fieldset>
<fieldset class="border p-2 mt-2 my-2">
    <jsp:include page="../motorengineer/assessorPayment.jsp"/>
</fieldset>
<script type="text/javascript">
    function setAriInOrder() {
        if ($('#isAriCheckbox').is(':checked')) {
            $("#isAri").val("Yes");
            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('disabled', false);
        } else {
            $("#isAri").val("No");
            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('checked', false);
            $("input[name='ariInspectionDetailsDto.ariOrder']").prop('disabled', true);
        }
    }

    function setSalvageInOrder() {
        if ($('#isSalvageCheckbox').is(':checked')) {
            $("#isSalvage").val("Yes");
            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('disabled', false);
        } else {
            $("#isSalvage").val("No");
            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('checked', false);
            $("input[name='ariInspectionDetailsDto.salvageOrder']").prop('disabled', true);
        }
    }
</script>
