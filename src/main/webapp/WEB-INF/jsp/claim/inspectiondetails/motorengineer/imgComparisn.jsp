<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Image Upload</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <script>
        $(document).ready(function () {
            $('[data-toggle="tooltip"]').tooltip({
                html: true,
                template: '<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
            });
        });
    </script>
    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="claimImageDtoList" value="${claimImageDtoList}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="claimImageDtoList" value="${historyClaimImageDtoList}" scope="request"/>
        </c:when>
    </c:choose>
</head>
<body class="scroll">
<form name="frmDocumentUpload" id="frmDocumentUpload" method="post">
    <div class="container-fluid">
        <div class="row">
            <fieldset class="col-md-12 border">
                <div class="form-group row mt-2">
                    <label class="col-sm-4 col-form-label disable">Select Claim :</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable " name="">
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Select Compensation Type :</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable" id="type">
                            <option value="photo" selected>Photo</option>
                            <option value="doc">Document</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Select Photo Type :</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable ">
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Select Document :</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable ">
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                            <option value="">Sample_data</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="button" name="cmdViewAccident"
                                class="btn btn-secondary pull-right ml-1">
                            Cancel
                        </button>
                        <button type="button" name="cmdViewAccident"
                                class="btn btn-primary pull-right">
                            Search
                        </button>
                    </div>
                </div>
                <hr class="my-2">
                <div class="row">
                    <div class="col-12" id="photo">
                        <c:set var="cnt" value="1"/>
                        <c:forEach var="claimImageDto" items="${claimImageDtoList}">
                            <div class="uploadfile-delet imgupload">
                                <figure>
                                    <a data-magnify="gallery" data-caption="Vehicle Photo" href="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}" data-toggle="tooltip" title="${claimImageDto.toolTip}">
                                        <img class="magnify-thumb" id="vehicleImage${claimImageDto.refNo}" src="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}" alt="" width="100" height="95" alt="" border="0" onclick="selectImage(${claimImageDto.refNo})">
                                    </a>
                                    <figcaption>${index}</figcaption>
                                </figure>
                            </div>
                        </c:forEach>
                    </div>
                    <div class="col-12" id="docuent">
                        xxxxxxxxxxxxxxx
                        <c:forEach var="claimDocumentDto" items="${claimDocumentDtoList}">
                            <a href="${pageContext.request.contextPath}/InspectionDetailsController/documentViewer?refNo=${claimDocumentDto.refNo}&jobRefNo=${claimDocumentDto.jobRefNo}" class="">
                                <span><i class="fa fa-file-pdf-o fa-4x m-3 " id="documentView${claimDocumentDto.refNo}"></i></span>
                            </a>
                        </c:forEach>
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
</form>
<script src="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.js"></script>
<script>
    $('[data-magnify]').magnify({
        fixedContent: false,
        initMaximized: true
    });

    $(function () {
        $('#docuent').hide();
        $('#type').change(function () {
            if ($('#type').val() == 'photo') {
                $('#docuent').hide();
                $('#photo').show();
            }
            if ($('#type').val() == 'doc') {
                $('#photo').hide();
                $('#docuent').show();
            }
        });
    });
</script>
</body>
</html>
