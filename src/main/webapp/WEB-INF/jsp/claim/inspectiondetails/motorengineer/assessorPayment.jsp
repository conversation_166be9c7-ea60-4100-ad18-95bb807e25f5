<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div id="assessorPmtDiv" class="assessor_pmt_div">
    <h6> Assessor Payment</h6>
    <hr class="my-1">
    <div class="form-group row">
        <div class="col-sm-4">
            <label class="col-form-label">Job Type
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
        </div>
        <div class="col-sm-4">
        <span class="label_Value input-view text-primary" id="job_type_label">
            <c:forEach var="item" items="${dayTypeList}">
                <c:if test="${motorEngineerDto.jobType == item.value}">
                    ${item.label}
                </c:if>
            </c:forEach>
        </span>
        </div>
        <div class="col-sm-4">
            <select name="jobType" class="form-control form-control-sm jobtype"
                    id="jobType">
                <option value="0">-- Please Select --</option>
                <c:forEach var="item" items="${dayTypeList}">
                    <option value="${item.value}">${item.label}</option>
                </c:forEach>
            </select>
        </div>


        <script type="text/javascript">
            $(document).ready(function () {
                var inspectionTypeId = "${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}";
                if (inspectionTypeId == 4 || inspectionTypeId == 7 || inspectionTypeId == 9) {
                    $("#jobType option[value='2']").remove();
                }
            });

        </script>
        <script>
            document.getElementById("jobType").value = "${motorEngineerDto.jobType}";
        </script>
    </div>

    <div class="form-group row">
        <div class="col-sm-4">
            <label class="col-form-label">Time Slot
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
        </div>
        <div class="col-sm-4">
        <span class="label_Value input-view text-primary">
            <c:forEach var="item" items="${inspectionTimeList}">
                <c:if test="${motorEngineerDto.inspectionDetailsDto.assessorFeeDetailId == item.value}">
                    ${item.label}
                </c:if>
            </c:forEach>
        </span>
        </div>

        <div class="col-sm-4">
            <select name="assessorFeeDetailId" class="form-control form-control-sm jobtype"
                    value="${motorEngineerDto.inspectionDetailsDto.assessorFeeDetailId}"
                    id="assessorFeeDetailId">
                <option value="0">-- Please Select --</option>
                <c:forEach var="item" items="${inspectionTimeList}">
                    <option value="${item.value}">${item.label}</option>
                </c:forEach>
            </select>
        </div>


        <script type="text/javascript">
            $(document).ready(function () {
                <%--var inspectionTypeId = "${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}";--%>
                <%--if (inspectionTypeId == 4 || inspectionTypeId == 7 || inspectionTypeId == 9) {--%>
                <%--    $("#jobType option[value='2']").remove();--%>
                <%--}--%>

            });

        </script>
        <script>document.getElementById("assessorFeeDetailId").value = "${motorEngineerDto.inspectionDetailsDto.assessorFeeDetailId}";</script>
    </div>

    <div class="form-group row">
        <div class="col-sm-4">
            <label class="col-form-label">Assigned Location
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
        </div>
        <div class="col-sm-4">
            <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.assignedLocation}</span>
        </div>
        <div class="col-sm-4">
            <input name="assignedLocation" id="assignedLocation" class="form-control form-control-sm"
                   value="${motorEngineerDto.assignedLocation}"
                   readonly>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-4"><label class="col-form-label">Place of Inspection
            <%--<span class="text-danger font-weight-bold"> *</span> --%>
            :</label>
        </div>

        <div class="col-sm-4">
            <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.placeOfInspection}</span>
        </div>
        <div class="col-sm-4">
            <input name="placeOfInspection" id="placeOfInspection" class="form-control form-control-sm"
                   value="${motorEngineerDto.placeOfInspection}"/>
        </div>
    </div>

    <div class="form-group row">
        <div class="col-sm-4"><label class="col-form-label">Commence Assessment Location
            <%--<span class="text-danger font-weight-bold"> *</span> --%>
            :</label>
        </div>

        <div class="col-sm-4">
            <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.currentLocation}</span>
        </div>
        <div class="col-sm-4">
            <input name="placeOfInspection" id="currentLocation" class="form-control form-control-sm"
                   value="${motorEngineerDto.currentLocation}"/>
        </div>
    </div>

    <div class="form-group row">
        <div class="col-sm-4">
            <label class="col-form-label">Mileage
                <span class="text-danger font-weight-bold"> *</span>
                :</label>
        </div>
        <div class="col-sm-4">
            <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.mileage}</span>

        </div>
        <div class="col-sm-4">
            <input class="form-control form-control-sm miles" id="mileage"
                   name="mileage" value="${isEmptyValue? '':motorEngineerDto.mileage}"/>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-4"><label class="col-form-label">Cost Of Call (Rs)
            <%--<span class="text-danger font-weight-bold"> *</span> --%>
            :</label>
        </div>
        <div class="col-sm-4">
        <span class="label_Value input-view text-primary text-right"><fmt:formatNumber
                value="${motorEngineerDto.inspectionDetailsDto.costOfCall}"
                pattern="###,##0.00;"
                type="number"/></span>
        </div>
        <div class="col-sm-4">
            <input class="form-control form-control-sm costofcall text-right"
                   id="costOfCall"
                   name="costOfCall" value="${isEmptyValue? '50.00':motorEngineerDto.costOfCall}">
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-4"><label class="col-form-label">Other Fee (Rs)
            <%--<span class="text-danger font-weight-bold"> *</span> --%>
            :</label>
        </div>
        <div class="col-sm-4">
        <span class="label_Value input-view text-primary text-right"><fmt:formatNumber
                value="${motorEngineerDto.inspectionDetailsDto.otherFee}"
                pattern="###,##0.00;"
                type="number"/></span>
        </div>
        <div class="col-sm-4">
            <input class="form-control form-control-sm otherchage text-right"
                   id="otherFee"
                   name="otherFee" value="${isEmptyValue? '':motorEngineerDto.otherFee}">
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-4">
            <label class="col-form-label">Deductions (Rs)
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
        </div>
        <div class="col-sm-4">
            <span class="label_Value input-view text-primary"></span>
        </div>
        <div class="col-sm-4">
            <input class="form-control form-control-sm deductions text-right"
                   id="deductions"
                   name="deductions" value="${isEmptyValue? '':motorEngineerDto.deductions}"
            >
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-4">
            <label class=" col-form-label">Reason of Deduction
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
        </div>
        <div class="col-sm-4">
            <span class="label_Value input-view text-primary"></span>
        </div>
        <div class="col-sm-4">
            <input class="form-control form-control-sm reasonOfDeduction"
                   id="reasonOfDeduction"
                   name="reasonOfDeduction" value="${isEmptyValue? '':motorEngineerDto.reasonOfDeduction}">
        </div>

    </div>
    <div class="form-group row">
        <div class="col-sm-4">
            <label class="col-form-label">Total Fee (Rs)
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
        </div>
        <div class="col-sm-4">
        <span class="label_Value input-view text-primary text-right"><fmt:formatNumber
                value="${motorEngineerDto.inspectionDetailsDto.totalAssessorFee}"
                pattern="###,##0.00;"
                type="number"/></span>
        </div>
        <div class="col sm-4">
            <input class="form-control form-control-sm text-right"
                   id="totalAssessorFee"
                   name="totalAssessorFee"
                   value="${isEmptyValue? '':motorEngineerDto.totalAssessorFee}" readonly>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-4">
            <label class="col-form-label">Description
                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                :</label>
        </div>
        <div class="col-sm-4">
            <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.feeDesc}</span>
        </div>
        <div class="col-sm-4">
         <textarea name="feeDesc" class="form-control form-control-sm"
                   id=" ">${motorEngineerDto.feeDesc}</textarea>
        </div>
    </div>

    <div class="mt-6">
        <c:if test="${G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser || G_USER.accessUserType eq 1 || G_USER.accessUserType eq 22 ||  G_USER.accessUserType eq 23 || G_USER.accessUserType eq 24}">
            <div class="float-right">
                <c:choose>
                    <c:when test="${motorEngineerDto.inspectionDetailsDto.assessorFeeAuthStatus eq 'H' }">
                        <label class="warning-mg pr-2" id="lblHoldWarning">This is an already held payment</label>
                    </c:when>
                    <c:when test="${motorEngineerDto.inspectionDetailsDto.assessorFeeAuthStatus eq 'R' }">
                        <label class="warning-mg pr-2" id="lblHoldWarning">This is an already rejected payment</label>
                    </c:when>
                </c:choose>
                <button type="button"
                        class="btn btn-danger ${motorEngineerDto.inspectionDetailsDto.assessorFeeAuthStatus eq 'A' ? '' : 'disabled'}"
                        id="btnHold"
                        value="assessorPaymentHold">
                    Hold
                </button>
                <button type="submit" name="cmdReject" id="cmdAuthAssessorFee"
                        value="assessorAuthorize"
                        class="btn btn-primary" ${motorEngineerDto.inspectionDetailsDto.assessorFeeAuthStatus eq 'A' ? 'disabled' : ''}>
                    Authorize
                </button>

            </div>
        </c:if>

    </div>
</div>


