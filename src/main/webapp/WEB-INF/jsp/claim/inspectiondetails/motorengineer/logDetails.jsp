<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%--
  Created by IntelliJ IDEA.
  User: Tharaka
  Date: 8/10/2018
  Time: 2:35 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<div class="w-100 scroll" style="height: calc(100vh - 350px);">
    <c:forEach var="logDetail"
               items="${engineerDetailsDto.logList}">
        <a href="#"
           class="list-group-item list-group-item-action flex-column align-items-start">
            <div class="font-bg log-left"
                 style="width: 50px; height:50px; overflow: hidden; margin-top: 25px;">
                <h2 class="name text-white">${logDetail.userId}</h2>
            </div>
            <div class="float-left log-right" style="height: auto;">
                <div class="w-100">
                        <%--<h5 class="mb-1">${logDetail.userId}</h5>--%>
                    <p class="text-right mb-0">
                        claim-${logDetail.claimNo}</p>
                </div>
                <hr class="m-0 mt-1 mb-1">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-0">Changed Field</p>
                        <p class="mb-1">
                            <b>${logDetail.fieldName}</b></p>
                    </div>
                    <div class="col-md-6 border-left">
                        <p class="mb-0">Changed Value</p>
                        <p class="mb-1">
                            <b>${logDetail.fieldValue}</b></p>
                    </div>
                </div>
                <hr class="m-0 mt-1 mb-1">
                <h6 class="float-right">${logDetail.userId} </h6>
                <p class="float-left">${logDetail.inputDateTime}</p>
            </div>
            <div class="clearfix"></div>
        </a>
    </c:forEach>
</div>
