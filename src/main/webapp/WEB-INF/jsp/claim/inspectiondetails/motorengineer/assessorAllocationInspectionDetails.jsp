<%--
  Created by IntelliJ IDEA.
  User: Asiri
  Date: 3/13/2018
  Time: 4:34 PM
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">

    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">

    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/inspectiondetails-form-validations.js?v12"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>

    <!-- Generic page styles -->
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>

    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>

    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="motorEngineerDto" value="${motorEngineerDto}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="motorEngineerDto" value="${motorEngineerPreviousDto}" scope="request"/>
        </c:when>
    </c:choose>

    <script>

        showLoader();
        $(document).ready(function () {
            loadImageUploadViewer();
            loadLogDetails();
            loadLogDetails();
        });

        function loadImageUploadViewer() {

            var pRefNo = 0;
            pRefNo = '${prevOnOrOffSiteRefNo}'
            if (0 != pRefNo) {
                $("#imageUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${prevOnOrOffSiteRefNo}");
            } else {
                $("#imageUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}");
            }

            <%--$("#imageUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}");--%>
            loadLogDetails();
        }

        $(document).ready(function () {
            loadSpecialRemarks();

        });

        function resizeIframe(obj) {
            obj.style.height = obj.contentWindow.document.body.scrollHeight + 'px';
        }

        var documentUploadIds = [];


        function loadSpecialRemarks() {
            $("#specialRemarks").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSpecialRemark?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
        }

        function loadLogDetails() {
            $("#logDetails").load("${pageContext.request.contextPath}/MotorEngineerController/viewLogDetails?jobRefNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
        }



        function updateUser() {
            var txnId = $('#refNos').val();
            var assignUser = $('#assignUser').val();
            var claimN = $('#claimNo').val();
            document.getElementById("assignUsersBtnDiv").style.display = "none";

            bootbox.confirm({
                message: "Are you sure you want to assign this job?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'

                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {

                    if (result === true) {

                        $.ajax({
                            url: contextPath + "/ClaimAssignUserReassignController/updateSelfAssignRTEUser?refNo=" + txnId + "&assignUser=" + assignUser + "&claimNo=" + claimN,
                            type: 'POST',
                            success: function (result) {
                                var obj = JSON.parse(result);
                                if (obj === "SUCCESS") {
                                    notify("Claim assigned successfully.", "success");
                                    document.getElementById("assignUsersBtnDiv").style.display = "none";
                                } else if (obj === "ALREADY_ASSIGNED") {
                                    notify("This claim is already assigned.", "warning");
                                    document.getElementById("assignUsersBtnDiv").style.display = "none";
                                } else if (obj === "FAIL") {
                                    notify("Failed to assign user.", "danger");
                                    document.getElementById("assignUsersBtnDiv").style.display = "block";
                                } else {
                                    document.getElementById("assignUsersBtnDiv").style.display = "block";
                                    notify("System error occurred.", "danger");
                                }
                                location.reload()
                            },
                            error: function() {
                                document.getElementById("assignUsersBtnDiv").style.display = "block";
                                notify("Network or server error occurred.", "danger");

                            }
                        });

                    }else{
                        document.getElementById("assignUsersBtnDiv").style.display = "block";
                    }
                }
            });
        }

    </script>
</head>

<body class="scroll" onload="hideLoader()">
<input value="${motorEngineerDto.totalApproveAcrAmount}" hidden id="totalApprovedAcr">
<div class="container-fluid">
    <div class="row header-bg bg-dark">
        <div class="col-sm-12 py-2" >
            <h6 class="float-left text-dark hide-for-small"> Inspection Report
                Details-${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}
                Review | <span
                        class="text-success"> Assigned RTE : ${motorEngineerDto.inspectionDetailsDto.assignRteUser} (${contactDetailDto.contactNo})</span>
                <c:if test="${8!=motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}">
                    |<span class="text-primary"> Assigned Assessor : ${motorEngineerDto.inspectionDetailsDto.inputUserId}</span>
                </c:if>
            </h6>

            <c:if test="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo ne '' }">
                <h6 class="text-dark float-right">Vehicle No
                    : ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}</h6><br>
            </c:if>

            <c:if test="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo eq '' }">
                <h6 class="text-dark float-right">Cover Note No
                    : ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.coverNoteNo}</h6><br>
            </c:if>
            <h6 class="text-dark float-right" style="margin-right: -162px">Claim No
                : ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}</h6>
            <p class="text-danger float-right mr-3">ISF Claim No
                : ${not empty motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.isfClaimNo  ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.isfClaimNo:'PENDING'}
                |</p>
        </div>
<%--        <div class="col-sm-12 py-2" style="--%>
<%--            display: flex;--%>
<%--            flex-direction: row;--%>
<%--            justify-content: space-around;--%>
<%--            align-items: center;--%>
<%--            background: #a7d1d6;--%>
<%--        ">--%>
<%--            <h6 style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                ${(motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product eq "") || (null eq motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product) ? "N/A" : motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.product }--%>
<%--            </h6>--%>
<%--            <h6 id="service-factor-header" style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                Service Factors--%>
<%--            </h6>--%>
<%--        </div>--%>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="stamp-container">
                <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'Y'}">
                    <%--                <c:if test="${true}">--%>
                    <img src="${pageContext.request.contextPath}/resources/stamps/vehicle_not_available.png"
                         class="ml-3"
                         width="100" height="100">
                </c:if>
                <c:if test="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.categoryDescription eq 'VIP'}">
                    <img src="${pageContext.request.contextPath}/resources/stamps/vip.png"
                         class="stamp-container-vip"
                    >
                </c:if>
            </div>
            <div class="f1-steps">
                <div class="f1-progress">
                    <div class="f1-progress-line" data-now-value="10" data-number-of-steps="5"
                         style="width: 70%;"></div>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimStatus>=1?"active":""}">
                        1
                    </div>
                    <p>Call Center</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">2</div>
                    <p>Assessor Coordinator</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">3</div>
                    <p>Assessor</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">4</div>
                    <p>Motor Engineer</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon">5</div>
                    <p>Claim Handler</p>
                </div>
            </div>
        </div>
    </div>
    <form id="frmMain" name="frmMain">
        <input type="hidden" name="ACTION" value="${ACTION}"/>
        <input type="hidden" value="${TYPE}" name="TYPE"/>
        <input type="hidden" name="outstand" id="outstand"
               value=""/>
        <input type="hidden" id="isCancelled" name="isCancelled"
               value=""/>
        <input type="hidden" id="paymentStatus"
               value="${paymentStatus}">
        <input type="hidden" id="rteSpecialRemark" name="rteSpecialRemark" value="">
        <c:if test="${ACTION eq 'SAVE'}">
            <c:set var="isEmptyValue" value="false" scope="request"/>
        </c:if>
        <c:if test="${ACTION eq 'UPDATE'}">
            <c:set var="isEmptyValue" value="false" scope="request"/>
        </c:if>
        <c:if test="${motorEngineerDto.inspectionDetailsDto.recordStatus eq 0}">
            <c:set var="isEmptyValue" value="true" scope="request"/>
        </c:if>
        <c:if test="${motorEngineerDto.inspectionDto.inspectionId eq 8}">
            <c:set var="isDesktopInspection" value="true" scope="request"/>
        </c:if>
        <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 8}">
            <c:set var="isDesktopInspection" value="false" scope="request"/>
        </c:if>
        <c:if test="${motorEngineerDto.desktopInspection eq 'Y'}">
            <c:set var="isEmptyValue" value="false" scope="request"/>
        </c:if>

        <input type="hidden" id="assignUser" value="${G_USER.userId}">

        <c:if test="${motorEngineerDto.assignRteUser eq 'N/A'}">
            <!-- Corrected Button -->
            <div id="assignUsersBtnDiv" style="padding:20px; box-shadow:0 4px 10px rgba(0,0,0,0.15); border-radius:10px; background-color:#fff; text-align:center;">
                <button
                        type="button"
                        id="assignUsersBtn"
                        style="background-color:#015AAA; color:white; border:none; padding:10px 20px; border-radius:5px; font-size:16px; cursor:pointer;"
                        onclick="updateUser()"
                >
                    Assign to Me
                </button>
            </div>
        </c:if>
        <div class="row">
            <fieldset class="col-md-7 border scroll" style="height: calc(100vh - 210px);">
                <div id="accordionOne">
                    <div class="card mt-3">
                        <div class="card-header p-0" id="heading0">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse0"
                                   aria-expanded="true" aria-controls="collapse0">
                                    Underwriting Details
                                </a>
                            </h5>
                        </div>
                        <div id="collapse0" class="collapse" aria-labelledby="heading0"
                             data-parent="#accordionOne">
                            <div class="card-body p-lg-3 p-2">
                                <div class="row">
                                    <div id="underWritingDetails" class="col-lg-12">
                                        <jsp:include
                                                page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/underwritingDetails.jsp"></jsp:include>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="inspectionDetailsDiv" class="card mt-2">
                        <div class="card-header p-0" id="heading1">
                            <input type="hidden" id="refNos"
                                   value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse1"
                                   aria-expanded="true" aria-controls="collapse1">
                                    Inspection Report
                                    Details
                                </a>
                            </h5>
                        </div>
                        <input type="hidden" name="claimNo" id="claimNos"
                               value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}">
                        <div id="collapse1" class="collapse" aria-labelledby="heading1"
                             data-parent="#accordionOne">
                            <div class="card-body p-1 p-2">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <c:if test="${not isDesktopInspection}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Code of Assessor
                                                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assessorDto.name} &nbsp; ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assessorDto.assessorContactNo}</span>
                                                </div>
                                            </div>
                                        </c:if>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Job No :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobId}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Customer Name :</label>
                                            <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="custName">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custName}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Contact Address :</label>
                                            <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="contactAddress">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine1},</span>
                                                <span class="label_Value input-view"
                                                      id="contactAddress2">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine2},</span>
                                                <span class="label_Value input-view"
                                                      id="contactAddress3">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine3}.</span>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Customer Contact Number :</label>
                                            <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="contactNumber">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custMobileNo}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Registration No. :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleNumber}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Make<span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                    <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleMake}</span>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.makeConfirm eq 'Confirm'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Correct</span>
                                                    </c:if>

                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.makeConfirm eq 'Wrong'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-danger">Wrong</span>
                                                    </c:if>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.makeConfirm eq 'Not_Checked'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Not Checked </span>
                                                    </c:if>
                                                </div>
                                                <div class="row">
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.makeConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                name="makeConfirm" type="radio"
                                                                class="align-middle" value="Confirm"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Correct</span>


                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.makeConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                name="makeConfirm" type="radio"
                                                                class="align-middle" value="Wrong"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Wrong</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.makeConfirm eq 'Not_Checked' and not isEmptyValue ? 'checked' : ''}
                                                                name="makeConfirm" type="radio"
                                                                class="align-middle" value="Not_Checked"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Not Checked</span>
                                                    </label>
                                                    <c:if test="${motorEngineerDto.makeConfirm eq 'Pending'}">
                                                        <input type="hidden" value="Pending" name="makeConfirm">
                                                        <script type="text/javascript">
                                                            $('input[name="makeConfirm"]').attr('disabled', 'disabled');
                                                        </script>
                                                    </c:if>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Model<span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                    <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleModel}</span>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.modelConfirm eq 'Confirm'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Correct</span>
                                                    </c:if>

                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.modelConfirm eq 'Wrong'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-danger">Wrong</span>
                                                    </c:if>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.modelConfirm eq 'Not_Checked'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Not Checked </span>
                                                    </c:if>
                                                </div>
                                                <div class="row">
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.modelConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio" name="modelConfirm"
                                                                class="align-middle" value="Confirm"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Correct</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.modelConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio" name="modelConfirm"
                                                                class="align-middle" value="Wrong"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Wrong</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.modelConfirm eq 'Not_Checked' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio" name="modelConfirm"
                                                                class="align-middle" value="Not_Checked"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Not Checked</span>
                                                    </label>

                                                    <c:if test="${motorEngineerDto.modelConfirm eq 'Pending'}">
                                                        <input type="hidden" value="Pending" name="modelConfirm">
                                                        <script type="text/javascript">
                                                            $('input[name="modelConfirm"]').attr('disabled', 'disabled');
                                                        </script>
                                                    </c:if>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Year of Make <span
                                                    class="text-danger font-weight-bold">  *</span>:</label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                    <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.manufactYear}</span>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.yearMakeConfirm eq 'Confirm'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Correct</span>
                                                    </c:if>

                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.yearMakeConfirm eq 'Wrong'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-danger">Wrong</span>
                                                    </c:if>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.yearMakeConfirm eq 'Not_Checked'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Not Checked </span>
                                                    </c:if>
                                                </div>
                                                <div class="row">
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.yearMakeConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio"
                                                                class="align-middle" name="yearMakeConfirm"
                                                                value="Confirm"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Correct</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.yearMakeConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio"
                                                                class="align-middle" name="yearMakeConfirm"
                                                                value="Wrong"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Wrong</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.yearMakeConfirm eq 'Not_Checked' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio"
                                                                class="align-middle" name="yearMakeConfirm"
                                                                value="Not_Checked"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Not Checked</span>
                                                    </label>

                                                    <c:if test="${motorEngineerDto.yearMakeConfirm eq 'Pending'}">
                                                        <input type="hidden" value="Pending" name="yearMakeConfirm">
                                                        <script type="text/javascript">
                                                            $('input[name="yearMakeConfirm"]').attr('disabled', 'disabled');
                                                        </script>
                                                    </c:if>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Engine No :</label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                    <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.engineNo}</span>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.engNoConfirm eq 'Confirm'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Correct</span>
                                                    </c:if>

                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.engNoConfirm eq 'Wrong'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-danger">Wrong</span>
                                                    </c:if>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.engNoConfirm eq 'Not_Checked'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Not Checked </span>
                                                    </c:if></div>
                                                <div class="row">
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.engNoConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio"
                                                                class="align-middle" name="engNoConfirm"
                                                                value="Confirm"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Correct</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.engNoConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                type="radio" name="engNoConfirm"
                                                                class="align-middle" value="Wrong"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Wrong</span>
                                                    </label>
                                                    <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                        <input ${motorEngineerDto.engNoConfirm eq 'Not_Checked' ? 'checked' : ''}
                                                                type="radio" name="engNoConfirm"
                                                                class="align-middle" value="Not_Checked"/>
                                                        <span class="radiomark"></span>
                                                        <span class="custom-control-description">Not Checked</span>
                                                    </label>
                                                </div>
                                                <c:if test="${motorEngineerDto.engNoConfirm eq 'Pending'}">
                                                    <input type="hidden" value="Pending" name="engNoConfirm">
                                                    <script type="text/javascript">
                                                        $('input[name="engNoConfirm"]').attr('disabled', 'disabled');
                                                    </script>
                                                </c:if>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Chassis No in ISF<span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.chassisNoConfirm eq 'Confirm'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Correct</span>
                                                    </c:if>

                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.chassisNoConfirm eq 'Wrong'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-danger">Wrong</span>
                                                    </c:if>
                                                    <c:if test="${motorEngineerDto.inspectionDetailsDto.chassisNoConfirm eq 'Not_Checked'}">
                                                        <span class="label_Value assessor_value m-0 col-6 text-success">Not Checked-  ${DbRecordCommonFunctionBean.getValueIdString("not_check_reason_mst","value", "reason_id", motorEngineerDto.inspectionDetailsDto.notCheckedReason)}  </span>
                                                    </c:if>
                                                </div>
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}</span>
                                            </div>
                                        </div>
                                        <%--<c:if test="${not isDesktopInspection}">--%>
                                        <%--<div class="form-group row">--%>
                                        <%--<label class="col-sm-4 col-form-label">Chassis No<span--%>
                                        <%--class="text-danger font-weight-bold">  *</span> :</label>--%>
                                        <%--<div class="col-sm-8 input-group">--%>
                                        <%--<span class="label_Value assessor_value input-view">${motorEngineerDto.inspectionDetailsDto.chassisNo}</span>--%>
                                        <%--<div class="row">--%>
                                        <%--<label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">--%>
                                        <%--<input type="checkbox" name="engNoConfirm"--%>
                                        <%--id="engNoConfirmbox"--%>
                                        <%--class="align-middle" value="Y"--%>
                                        <%--onclick="chassis_correct('Y');"/>--%>
                                        <%--<span class="checkmark"></span>--%>
                                        <%--<span class="custom-control-description">Incorrect Chassis No.</span>--%>
                                        <%--</label>--%>
                                        <%--</div>--%>
                                        <%--</div>--%>
                                        <%--</div>--%>
                                        <%--</c:if>--%>
                                        <%--<div class="form-group row">--%>
                                        <%--<c:if test="${not isDesktopInspection}">--%>
                                        <%--<label class="col-sm-4 col-form-label text-mute correctChaNo">Correct--%>
                                        <%--Chassis No<span--%>
                                        <%--class="text-danger font-weight-bold">  *</span> :</label>--%>
                                        <%--</c:if>--%>
                                        <%--<c:if test="${isDesktopInspection}">--%>
                                        <%--<label class="col-sm-4 col-form-label text-mute correctChaNo">Chassis No<span--%>
                                        <%--class="text-danger font-weight-bold">  *</span> :</label>--%>
                                        <%--</c:if>--%>
                                        <%--<div class="col-sm-8">--%>
                                        <%--<input name="chassisNo" placeholder="Chassis No"--%>

                                        <%--class="lstBox form-control form-control-sm correctChaNo"--%>
                                        <%--disabled>--%>
                                        <%--</div>--%>
                                        <%--</div>--%>
                                        <c:if test="${not isDesktopInspection}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Chassis No : </label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                        <span class="label_Value m-0 col-6"></span>
                                                        <c:if test="${motorEngineerDto.chassisNoConfirm eq 'Confirm'}">
                                                            <span class="label_Value assessor_value m-0 col-6 text-success">Correct</span>
                                                        </c:if>

                                                        <c:if test="${motorEngineerDto.chassisNoConfirm eq 'Wrong'}">
                                                            <span class="label_Value assessor_value m-0 col-6 text-danger">Wrong</span>
                                                        </c:if>
                                                        <c:if test="${motorEngineerDto.chassisNoConfirm eq 'Not_Checked'}">
                                                            <span class="label_Value assessor_value m-0 col-6 text-success">Not Checked </span>
                                                        </c:if></div>
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input ${motorEngineerDto.chassisNoConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}
                                                                    type="radio"
                                                                    class="align-middle" name="chassisNoConfirm"
                                                                    value="Confirm" onclick="correctSelect()"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Correct</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input ${motorEngineerDto.chassisNoConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}
                                                                    type="radio" name="chassisNoConfirm"
                                                                    class="align-middle" value="Wrong"
                                                                    onclick="wrongSelect()"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Wrong</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input ${motorEngineerDto.chassisNoConfirm eq 'Not_Checked' ? 'checked' : ''}
                                                                    type="radio" name="chassisNoConfirm"
                                                                    class="align-middle" value="Not_Checked"
                                                                    onclick="notCheckSelect()"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Not Checked</span>
                                                        </label>

                                                        <c:if test="${motorEngineerDto.chassisNoConfirm eq 'Pending'}">
                                                            <input type="hidden" value="Pending"
                                                                   name="chassisNoConfirm">
                                                            <script type="text/javascript">
                                                                $('input[name="chassisNoConfirm"]').attr('disabled', 'disabled');
                                                            </script>
                                                        </c:if>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Inspection Type :</label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container pull-right">
                                                            <input name="typeOnlineInspection"
                                                                   title="Online Inspection"
                                                                   class="align-middle checkbox_check"
                                                                   type="checkbox"
                                                                   id="typeOnlineInspection"
                                                                   value="Y"/>
                                                            <span class="checkmark"></span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <script>
                                                $("#typeOnlineInspection").click(function (){
                                                    if(document.getElementById("typeOnlineInspection").val === 'N'){
                                                        document.getElementById("typeOnlineInspection").checked = true
                                                    }else if(document.getElementById("typeOnlineInspection").val === 'Y'){
                                                        document.getElementById("typeOnlineInspection").checked = false
                                                    }
                                                });
                                            </script>
                                            <div class="form-group row" id="chassisDiv" style="display: none">
                                                <label class="col-sm-4 col-form-label">Chassis No<span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8">
                                                    <input name="chassisNo" id="chassisNo" placeholder="Chassis No"
                                                           class="lstBox form-control form-control-sm"
                                                           value="${motorEngineerDto.chassisNo eq '' ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo  : motorEngineerDto.chassisNo}">

                                                </div>
                                            </div>
                                            <div class="form-group row " id="notCheckDiv" style="display: none;">
                                                <label class="col-sm-4 col-form-label"><span
                                                        class="text-danger font-weight-bold"> Not Checked Reason
                                                        * :</span>
                                                </label>
                                                <div class="col-sm-8">
                                                    <select name="notCheckedReason" id="notCheckedReason"
                                                            class="form-control form-control-sm "
                                                            data-fv-field="">
                                                            ${DbRecordCommonFunctionBean.getPopupList("not_check_reason_mst", "reason_id", "value")}

                                                    </select>
                                                    <script type="text/javascript">
                                                        $('#notCheckedReason').val('${motorEngineerDto.notCheckedReason}');
                                                    </script>
                                                </div>
                                            </div>
                                        </c:if>


                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Date of Registration
                                                :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.registDate}</span>
                                            </div>
                                        </div>
                                        <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 7
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 9
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 5
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 6}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Excess (Rs) :</label>
                                                <div class="col-sm-8">
                                                              <span class="label_Value input-view">
                                                                  <fmt:formatNumber
                                                                          value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                                                          pattern="###,##0.00;(###,##0.00)"
                                                                          type="number"/>
                                                              </span>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 1
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 2
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 4
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 5
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 6
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 9
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">NCB (Rs) :</label>
                                                <div class="col-sm-8">
                                                              <span class="label_Value input-view">
                                                                  <fmt:formatNumber
                                                                          value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbAmount}"
                                                                          pattern="#,##0.00;-#,##0.00"
                                                                          type="number"/></span>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">NCB Percentage :</label>
                                                <div class="col-sm-8">
                                                    <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbRate} %</span>
                                                </div>
                                            </div>
                                        </c:if>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Date of Inspection<span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8">
                                                <input name="inspectDateTime"
                                                       id="inspectDateTime"
                                                       class="form-control form-control-sm"
                                                       title=""
                                                       type="text"
                                                       value="${isEmptyValue ? '' : motorEngineerDto.inspectDateTime == '1980-01-01 00:00:00'
                                                                        ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime == '1980-01-01 00:00:00'
                                                                        ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assignDatetime
                                                                        : motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime:motorEngineerDto.inspectDateTime}"
                                                />
                                            </div>
                                        </div>
                                        <input id="policyCoverNoteNo" name="policyCoverNoteNo"
                                               type="hidden"
                                               value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"/>
                                        <c:if test="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue ne 'Supplimantary Inspection'}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Sum Insured (Rs) :</label>
                                                <div class="col-sm-8">
                                                    <input type="hidden" id="sumInsuredVal" name="sumInsuredVal"
                                                           value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.sumInsured}">
                                                    <input id="policyCoverNoteNo" name="policyCoverNoteNo"
                                                           type="hidden"
                                                           value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"/>
                                                    <span class="label_Value input-view" id="sumInsuredVal"
                                                          name="sumInsuredVal">
                                                            <fmt:formatNumber
                                                                    value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.sumInsured}"
                                                                    pattern="###,##0.00;(###,##0.00)"
                                                                    type="number"/></span>
                                                </div>
                                            </div>
                                        </c:if>

                                        <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 6
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 7
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 9}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">PAV (Rs.)<span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                              <span class="label_Value assessor_value input-view"
                                                              ><fmt:formatNumber
                                                                      value="${motorEngineerDto.inspectionDetailsDto.pav}"
                                                                      pattern="###,##0.00;"
                                                                      type="number"/></span>
                                                    <input id="pav" name="pav" value="${isEmptyValue ? not empty PREVIOUS_PAV && motorEngineerDto.inspectionDto.inspectionId eq 8
                                                                                                          ? PREVIOUS_PAV : ''
                                                                                                          : motorEngineerDto.pav}"
                                                           class="lstBox form-control form-control-sm ml-2 text-right">
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 7 && motorEngineerDto.inspectionDto.inspectionId ne 9}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Details of Damages<span
                                                        class="text-danger font-weight-bold">  </span> :</label>
                                                <div class="col-sm-8 input-group">
                                                            <textarea
                                                                    class="lstBox form-control form-control-sm mr-2 assessor_value"
                                                            >${motorEngineerDto.inspectionDetailsDto.damageDetails}</textarea>
                                                    <textarea
                                                            name="damageDetails" ${motorEngineerDto.desktopInspection eq 'Y'?'readonly' :''}
                                                            class="lstBox form-control form-control-sm">${motorEngineerDto.damageDetails}</textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">PAD :</label>
                                                <div class="col-sm-8  input-group">
                                                            <textarea name=""
                                                                      class="lstBox form-control form-control-sm assessor_value"
                                                                      readonly>${motorEngineerDto.inspectionDetailsDto.pad}</textarea>
                                                    <textarea
                                                            name="pad" ${motorEngineerDto.desktopInspection eq 'Y'?'readonly' :''}
                                                            class="lstBox form-control form-control-sm ml-2">${motorEngineerDto.pad}</textarea>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 6
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 7
                                                              && motorEngineerDto.inspectionDto.inspectionId ne 9}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Genuineness of the Accident<c:if
                                                        test="${motorEngineerDto.inspectionDto.inspectionId ne 4}">
                                                    <span class="text-danger font-weight-bold">  *</span>
                                                    <script>
                                                        //TODO - Remove Validation
                                                    </script>
                                                </c:if>
                                                    :</label>
                                                <div class="col-sm-8">
                                                    <div class="row">
                                                                  <span class="label_Value assessor_value col-md-12 input-view text-primary m-0 pl-3">
                                                                      <c:choose>
                                                                          <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Consistent'}">
                                                                              Consistent
                                                                          </c:when>
                                                                          <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Non_Consistence'}">
                                                                              Non Consistent
                                                                          </c:when>
                                                                          <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Doubtful'}">
                                                                              Doubtful
                                                                          </c:when>
                                                                      </c:choose>
                                                                  </span>
                                                        <label class="custom-control custom-checkbox m-0 mb-sm-0 ml-2 pl-3 pr-0 col col-form-label check-container first-container">
                                                            <input ${motorEngineerDto.genuineOfAccident eq 'Consistent' and not isEmptyValue ? 'checked' : ''}
                                                                    name="genuineOfAccident" type="radio"
                                                                    class="align-middle" value="Consistent"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Consistent</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox m-0 mb-sm-0 pl-3 col-4 pr-0 col-form-label check-container ml-0">
                                                            <input ${motorEngineerDto.genuineOfAccident eq 'Non_Consistence' and not isEmptyValue ? 'checked' : ''}
                                                                    name="genuineOfAccident" type="radio"
                                                                    class="align-middle" value="Non_Consistence"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Non Consistent</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox m-0 mb-sm-0 pl-3 col pr-0 col-form-label check-container ml-0">
                                                            <input ${motorEngineerDto.genuineOfAccident eq 'Doubtful' and not isEmptyValue ? 'checked' : ''}
                                                                    name="genuineOfAccident" type="radio"
                                                                    class="align-middle" value="Doubtful"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Doubtful</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">1st statement required for
                                                    Own Damage Settlement<span
                                                            class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                              <span class="label_Value assessor_value input-view text-primary"
                                                              >${motorEngineerDto.inspectionDetailsDto.firstStatementReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input ${motorEngineerDto.firstStatementReq eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                                                    name="firstStatementReq" type="radio"
                                                                    class="align-middle" value="Yes"
                                                                    onclick="enableDisableFirstStatement('Y');"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Yes</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input ${motorEngineerDto.firstStatementReq eq 'No' and not isEmptyValue ? 'checked' : ''}
                                                                    name="firstStatementReq" type="radio"
                                                                    class="align-middle" value="No"
                                                                    onclick="enableDisableFirstStatement('N');"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">No</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Reason <span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                                <div class="col-sm-8 input-group">
                                                        <%--<span class="label_Value assessor_value input-view text-primary"--%>
                                                        <%-->${DbRecordCommonFunctionBean.getValue("claim_first_statement_reason", "V_REASON", "N_ID", motorEngineerDto.inspectionDetailsDto.firstStatementReqReason)}</span>--%>
                                                    <select class=" form-control form-control-sm "
                                                            id="firstStatementReqReason"
                                                            name="firstStatementReqReason" disabled="true">
                                                            ${DbRecordCommonFunctionBean.getPopupList("claim_first_statement_reason", "N_ID", "V_REASON")}
                                                    </select>
                                                </div>
                                                <script>
                                                    $("#firstStatementReqReason").val("${motorEngineerDto.firstStatementReqReason}");
                                                </script>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Investigation Required<span
                                                        class="text-danger font-weight-bold">  *</span>
                                                    :</label>
                                                <div class="col-sm-8 input-group">
                                                              <span class="label_Value assessor_value input-view text-primary"
                                                              >${motorEngineerDto.inspectionDetailsDto.investReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                    <div class="row">
                                                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input ${motorEngineerDto.investReq eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                                                    name="investReq" type="radio"
                                                                    class="align-middle" value="Yes"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">Yes</span>
                                                        </label>
                                                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                                                            <input ${motorEngineerDto.investReq eq 'No' and not isEmptyValue ? 'checked' : ''}
                                                                    name="investReq" type="radio"
                                                                    class="align-middle" value="No"/>
                                                            <span class="radiomark"></span>
                                                            <span class="custom-control-description">No</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:if>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Assessor Remarks:</label>
                                            <div class="col-sm-8">
                                                        <textarea name="inspectionRemark"
                                                                  class="lstBox form-control form-control-sm"
                                                                  readonly>${motorEngineerDto.inspectionDetailsDto.assessorSpecialRemark}</textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">RTE Remarks:</label>
                                            <div class="col-sm-8">
                                                        <textarea name="assessorSpecialRemark"
                                                                  id="assessorSpecialRemark" ${motorEngineerDto.desktopInspection eq 'Y'?'readonly' :''}
                                                                  class="lstBox form-control form-control-sm"
                                                                  value="">${motorEngineerDto.assessorSpecialRemark}</textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <c:if test="${motorEngineerDto.desktopInspection != 'Y'}">
                                                <label class="col-sm-4 col-form-label">RTE Special Remarks:</label>
                                            </c:if>
                                            <c:if test="${motorEngineerDto.desktopInspection == 'Y'}">
                                                <label class="col-sm-4 col-form-label">Desktop Assesment
                                                    Remarks:</label>
                                            </c:if>
                                            <div class="col-sm-8">
                                                        <textarea name="inspectionSpecialRemark"
                                                                  id="inspectionSpecialRemark"
                                                                  class="lstBox form-control form-control-sm skipReadOnly"></textarea>
                                            </div>
                                        </div>
                                        <div class="my-3 clearfix">
                                            <button type="button" name="addRTERemarks"
                                                    onclick="addRemarks();"
                                                    value="Reject"
                                                    class="btn btn-primary float-right skipReadOnly">
                                                Add Special Remarks
                                            </button>
                                        </div>
                                        <div class="my-3 clearfix">
                                            <a href="${pageContext.request.contextPath}/MotorEngineerController/viewClaimHistory?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}"
                                               class="claimView">
                                                <button type="button" name="cmdViewAccident"
                                                        value="Reject"
                                                        class="btn btn-primary float-right skipReadOnly">
                                                    View Accidents
                                                </button>
                                            </a>
                                            <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
                                                <div class="float-right" style="padding-right:10px ">
                                                    <button onclick="saveInspectionDetailsRepoart()" type="button"
                                                        <%--name="cmdReject" id="cmdAuth"--%>
                                                            value="Save"
                                                            class="btn btn-primary">
                                                        Save Details
                                                    </button>
                                                </div>
                                            </c:if>
                                            <script type="text/javascript">
                                                $('.claimView').popupWindow({
                                                    height: screen.height,
                                                    width: screen.width,
                                                    resizable: 1,
                                                    centerScreen: 1,
                                                    scrollbars: 1,
                                                    windowName: 'swip'
                                                });
                                            </script>
                                        </div>
                                        <c:if test="${changed != null && changed != 'Y'}">
                                            <div class="form-group row">
                                                <label class="col-sm-4 col-form-label">Change Request Special Remarks
                                                    :</label>
                                                <div class="col-sm-8">
                                                        <textarea name="changeSpecialRemark" id="changeSpecialRemark"
                                                                  onkeypress="isRemark()"
                                                                  class="lstBox form-control form-control-sm"></textarea>
                                                </div>
                                                <div class="text-danger" id="remarkDiv" style="display: none">
                                                    This field is required
                                                </div>
                                                <div class="mt-3 col">
                                                    <button type="button" onclick="updateChangeRequest()" id="changeBtn"
                                                            class="btn btn-primary float-right"> Change Request
                                                    </button>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${changed == null}">
                                            <div class="form-group row" style="display: none">
                                                <label class="col-sm-4 col-form-label">Change Request Special Remarks
                                                    :</label>
                                                <div class="col-sm-8">
                                                        <textarea name="changeAsSpecialRemark"
                                                                  id="changeAsSpecialRemark" onkeypress="isAsRemark()"
                                                                  class="lstBox form-control form-control-sm"></textarea>
                                                </div>
                                                <div class="text-danger" id="asRemarkDiv" style="display: none">
                                                    This field is required
                                                </div>
                                                <div class="mt-3 col">
                                                    <button type="button" onclick="updateAsChangeRequest()"
                                                            id="asChangeBtn"
                                                            class="btn btn-primary"> Change Request
                                                    </button>
                                                </div>
                                            </div>
                                        </c:if>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                              && motorEngineerDto.inspectionDto.inspectionId ne 6
                                              && motorEngineerDto.inspectionDto.inspectionId ne 7
                                              && motorEngineerDto.inspectionDto.inspectionId ne 9}">
                        <div class="card mt-2">
                            <div class="card-header p-0" id="heading2">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" data-toggle="collapse" data-target="#collapse2"
                                       aria-expanded="true" aria-controls="collapse2">
                                        Condition of Tyres
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse2" class="collapse" aria-labelledby="heading2"
                                 data-parent="#accordionOne">
                                <div class="card-body p-lg-3 p-2">
                                    <div class="row">
                                        <div id="tyreConditionDiv" class="col-lg-12">
                                            <c:if test="${not isDesktopInspection}">
                                                <fieldset class="border p-2">
                                                    <h6>Assessor</h6>
                                                    <hr class="my-2">
                                                    <table class="table table-responsive tire-con-table ">
                                                        <tbody>
                                                        <tr>
                                                            <th>Position</th>
                                                            <td class="text-center">RF</td>
                                                            <td class="text-center">LF</td>
                                                            <td class="text-center">RR</td>
                                                            <td class="text-center">RL</td>
                                                            <td class="text-center">RRI</td>
                                                            <td class="text-center">LRI</td>
                                                            <td class="text-center">Other</td>
                                                        </tr>
                                                        <tr>
                                                            <th>Condition</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[0].rf}"
                                                                               name="test" id="test"
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[0].lf}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[0].rr}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[0].rl}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[0].rri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[0].lri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[0].other}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Size</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[1].rf}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[1].lf}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[1].rr}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[1].rl}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[1].rri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[1].lri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[1].other}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Make</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[2].rf}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[2].lf}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[2].rr}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[2].rl}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[2].rri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[2].lri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[2].other}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>New / RB</th>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[3].rf}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[3].lf}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[3].rr}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[3].rl}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[3].rri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[3].lri}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-group">
                                                                    <div>
                                                                        <input value="${motorEngineerDto.inspectionDetailsDto.tireCondtionDtoList[3].other}"
                                                                               name=""
                                                                               class="form-control form-control-sm "
                                                                               readonly>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </fieldset>
                                            </c:if>
                                            <fieldset class="border p-2 mt-2">
                                                <h6>Motor Engineering</h6>
                                                <hr class="my-2">
                                                <table class="table table-responsive tire-con-table">
                                                    <tbody>
                                                    <tr>
                                                        <th>Position</th>
                                                        <td class="text-center">RF</td>
                                                        <td class="text-center">LF</td>
                                                        <td class="text-center">RR</td>
                                                        <td class="text-center">RL</td>
                                                        <td class="text-center">RRI</td>
                                                        <td class="text-center">LRI</td>
                                                        <td class="text-center">Other</td>
                                                    </tr>
                                                    <tr>
                                                        <th>Condition</th>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <select name="cot_0_0"
                                                                            class="form-control form-control-sm cot_0"
                                                                            id="cot_0_0">
                                                                        <option value="Good">Good</option>
                                                                        <option value="Fair">Fair</option>
                                                                        <option value="Bald">Bald</option>
                                                                        <option value="N/A">N/A</option>
                                                                    </select>
                                                                    <script>document.getElementById("cot_0_0").value = "${motorEngineerDto.tireCondtionDtoList[0].rf}";</script>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <select name="cot_0_1"
                                                                            class="form-control form-control-sm cot_0"
                                                                            id="cot_0_1">
                                                                        <option value="Good">Good</option>
                                                                        <option value="Fair">Fair</option>
                                                                        <option value="Bald">Bald</option>
                                                                        <option value="N/A">N/A</option>
                                                                    </select>
                                                                    <script>document.getElementById("cot_0_1").value = "${motorEngineerDto.tireCondtionDtoList[0].lf}";</script>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <select name="cot_0_2"
                                                                            class="form-control form-control-sm cot_0"
                                                                            id="cot_0_2">
                                                                        <option value="Good">Good</option>
                                                                        <option value="Fair">Fair</option>
                                                                        <option value="Bald">Bald</option>
                                                                        <option value="N/A">N/A</option>
                                                                    </select>
                                                                    <script>document.getElementById("cot_0_2").value = "${motorEngineerDto.tireCondtionDtoList[0].rr}";</script>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <select name="cot_0_3"
                                                                            class="form-control form-control-sm cot_0"
                                                                            id="cot_0_3">
                                                                        <option value="Good">Good</option>
                                                                        <option value="Fair">Fair</option>
                                                                        <option value="Bald">Bald</option>
                                                                        <option value="N/A">N/A</option>
                                                                    </select>
                                                                    <script>document.getElementById("cot_0_3").value = "${motorEngineerDto.tireCondtionDtoList[0].rl}";</script>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <select name="cot_0_4"
                                                                            class="form-control form-control-sm cot_0"
                                                                            id="cot_0_4">
                                                                        <option value="Good">Good</option>
                                                                        <option value="Fair">Fair</option>
                                                                        <option value="Bald">Bald</option>
                                                                        <option value="N/A">N/A</option>
                                                                    </select>
                                                                    <script>document.getElementById("cot_0_4").value = "${motorEngineerDto.tireCondtionDtoList[0].rri}";</script>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <select name="cot_0_5"
                                                                            class="form-control form-control-sm cot_0"
                                                                            id="cot_0_5">
                                                                        <option value="Good">Good</option>
                                                                        <option value="Fair">Fair</option>
                                                                        <option value="Bald">Bald</option>
                                                                        <option value="N/A">N/A</option>
                                                                    </select>
                                                                    <script>
                                                                        document.getElementById("cot_0_5").value = "${motorEngineerDto.tireCondtionDtoList[0].lri}";
                                                                    </script>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <select name="cot_0_6"
                                                                            class="form-control form-control-sm cot_0"
                                                                            id="cot_0_6">
                                                                        <option value="Good">Good</option>
                                                                        <option value="Fair">Fair</option>
                                                                        <option value="Bald">Bald</option>
                                                                        <option value="N/A">N/A</option>
                                                                    </select>
                                                                    <script>document.getElementById("cot_0_6").value = "${motorEngineerDto.tireCondtionDtoList[0].other}";</script>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <c:if test="${fn:length(motorEngineerDto.tireCondtionDtoList) == 0 && motorEngineerDto.inspectionDto.inspectionId == 8}">
                                                        <script>
                                                            for (var i = 0; i < 7; i++) {
                                                                document.getElementById("cot_0_" + i).value = "N/A";
                                                            }
                                                        </script>
                                                    </c:if>
                                                    <tr>
                                                        <th>Size</th>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[1].rf}"
                                                                           name="cot_1_0"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[1].lf}"
                                                                           name="cot_1_1"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[1].rr}"
                                                                           name="cot_1_2"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[1].rl}"
                                                                           name="cot_1_3"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[1].rri}"
                                                                           name="cot_1_4"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[1].lri}"
                                                                           name="cot_1_5"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[1].other}"
                                                                           name="cot_1_6"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <th>Make</th>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[2].rf}"
                                                                           name="cot_2_0"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[2].lf}"
                                                                           name="cot_2_1"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[2].rr}"
                                                                           name="cot_2_2"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[2].rl}"
                                                                           name="cot_2_3"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[2].rri}"
                                                                           name="cot_2_4"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[2].lri}"
                                                                           name="cot_2_5"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[2].other}"
                                                                           name="cot_2_6"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <th>New / RB</th>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[3].rf}"
                                                                           name="cot_3_0"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[3].lf}"
                                                                           name="cot_3_1"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[3].rr}"
                                                                           name="cot_3_2"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[3].rl}"
                                                                           name="cot_3_3"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[3].rri}"
                                                                           name="cot_3_4"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[3].lri}"
                                                                           name="cot_3_5"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="form-group">
                                                                <div>
                                                                    <input value="${motorEngineerDto.tireCondtionDtoList[3].other}"
                                                                           name="cot_3_6"
                                                                           class="form-control form-control-sm ">
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </fieldset>
                                            <div class="row mt-2">
                                                <div class="col-md-12">
                                                    <div class="form-group row">
                                                        <label class="col-sm-4 col-form-label">Special remarks
                                                                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                            :</label>
                                                        <div class="col-sm-8">
                                                                         <textarea name="inspectionSpecialRemark"
                                                                                   id="inspectionSpecialRemarks"
                                                                                   class="lstBox form-control form-control-sm skipReadOnly"></textarea>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                            <div class="float-right" style="padding-right:10px ">
                                                <button type="button" name="addRTERemarks" style="margin-left: 10px"
                                                        onclick="addRemarkss();"
                                                        value="Reject"
                                                        class="btn btn-primary float-right skipReadOnly">
                                                    Add Special Remarks
                                                </button>
                                                <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
                                                    <button onclick="saveInspectionDetailsRepoart()" type="button"
                                                        <%--name="cmdReject" id="cmdAuth"--%>
                                                            value="Save"
                                                            class="btn btn-primary">
                                                        Save Details
                                                    </button>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                    <div class="card mt-2">
                        <div class="card-header p-0" id="heading3">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse3"
                                   aria-expanded="true" aria-controls="collapse3">
                                    ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}
                                    Review
                                </a>
                            </h5>
                        </div>
                        <div id="collapse3" class="collapse" aria-labelledby="heading3"
                             data-parent="#accordionOne">
                            <div class="card-body p-lg-3 p-2">
                                <div class="row">
                                    <div id="estimationDiv" class="col-lg-12">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Inspection Type
                                                <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view"
                                                >${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionValue}</span>
                                            </div>
                                        </div>
                                        <c:choose>
                                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 4}">
                                                <jsp:include
                                                        page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/garageInspection.jsp"></jsp:include>
                                            </c:when>
                                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 5 || motorEngineerDto.inspectionDto.inspectionId == 6 }">
                                                <jsp:include
                                                        page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/drSupplementaryInspection.jsp"></jsp:include>
                                            </c:when>
                                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 7 || motorEngineerDto.inspectionDto.inspectionId == 9}">
                                                <jsp:include
                                                        page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/ariInspection.jsp"></jsp:include>
                                            </c:when>
                                            <c:when test="${motorEngineerDto.inspectionDto.inspectionId == 8}">
                                                <jsp:include
                                                        page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/desktopAssesment.jsp"></jsp:include>
                                            </c:when>
                                            <c:otherwise>
                                                <jsp:include
                                                        page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/onSiteInspection.jsp"></jsp:include>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header p-0" id="heading4">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse4"
                                   aria-expanded="true" aria-controls="collapse4">
                                    Previous Inspection
                                </a>
                            </h5>
                        </div>
                        <div id="collapse4" class="collapse " aria-labelledby="heading4"
                             data-parent="#accordionOne">
                            <div class="card-body p-lg-3 p-2">
                                <div class="row">
                                    <div class="col-md-12">
                                        <table width="100%" cellpadding="0" cellspacing="1"
                                               class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                            <thead>
                                            <tr>
                                                <th class="tbl_row_header">Job No</th>
                                                <th class="tbl_row_header">Inspection Type</th>
                                                <th class="tbl_row_header">Assign Assessor</th>
                                                <th class="tbl_row_header">Assign RTE</th>
                                                <th class="tbl_row_header">Forwarded RTE</th>
                                                <th class="tbl_row_header">Approve Date Time</th>
                                                <th class="tbl_row_header">Date Of Accident</th>
                                                <th class="tbl_row_header">Status</th>
                                                <th class="tbl_row_header"></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <c:forEach var="claim" items="${previousInspectionList}">
                                                <c:forEach var="jobs" items="${claim.list}">
                                                    <tr>
                                                        <td>${jobs.jobNo}</td>
                                                        <td>${jobs.inspectionType}</td>
                                                        <td>${jobs.assignAssessor}</td>
                                                        <td>${jobs.assignRte}</td>
                                                        <td>${jobs.approveAssignRte}</td>
                                                        <td>${jobs.approveDateTime}</td>
                                                        <td>${jobs.dateOfAccident}</td>
                                                        <td>${jobs.statusDesc}</td>

                                                        <c:if test="${jobs.statusDesc ne 'ASSIGNED'}">
                                                            <td>
                                                                <div>
                                                                    <a href="${pageContext.request.contextPath}/MotorEngineerController/viewEditPrevious?P_N_JOB_NO=${jobs.jobRefNo}"
                                                                       class="jobView">
                                                                        <button type="button" name="cmdReject"
                                                                                class="btn btn-primary">
                                                                            <i class="fa fa-eye"></i>
                                                                        </button>
                                                                    </a>
                                                                </div>
                                                                <script type="text/javascript">
                                                                    $('.jobView').popupWindow({
                                                                        height: screen.height,
                                                                        width: screen.width,
                                                                        resizable: 1,
                                                                        centerScreen: 1,
                                                                        scrollbars: 1,
                                                                        windowName: 'swip'
                                                                    });
                                                                </script>
                                                            </td>
                                                        </c:if>

                                                    </tr>
                                                </c:forEach>
                                            </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header p-0" id="heading9">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse9"
                                   aria-expanded="true" aria-controls="collapse9">
                                    Previous Claims
                                </a>
                            </h5>
                        </div>
                        <div id="collapse9" class="collapse " aria-labelledby="heading9"
                             data-parent="#accordionOne">
                            <div class="card-body p-lg-3 p-2">
                                <div class="row">
                                    <div class="col-md-12">
                                        <table width="100%" cellpadding="0" cellspacing="1"
                                               class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                            <thead>
                                            <tr>
                                                <th class="tbl_row_header">Job No</th>
                                                <th class="tbl_row_header">Inspection Type</th>
                                                <th class="tbl_row_header">Vehicle No</th>
                                                <th class="tbl_row_header">Policy No</th>
                                                <th class="tbl_row_header">Date of Accident</th>
                                                <th class="tbl_row_header"></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <c:forEach var="claim" items="${previousClaimList}">
                                                <tr class="bg-dark">
                                                    <td colspan="6">
                                                        <b> ${claim.claimNo}</b>
                                                    </td>
                                                </tr>
                                                <c:forEach var="jobs" items="${claim.list}">
                                                    <tr>
                                                        <td>${jobs.jobNo}</td>
                                                        <td>${jobs.inspectionType}</td>
                                                        <td>${jobs.vehicleNo}</td>
                                                        <td>${jobs.policyNo}</td>
                                                        <td>${jobs.dateOfAccident}</td>
                                                        <td>
                                                            <div>
                                                                <c:if test="${jobs.jobNo ne 'N/A'}">
                                                                    <a href="${pageContext.request.contextPath}/MotorEngineerController/viewEditPrevious?P_N_JOB_NO=${jobs.refNo}"
                                                                       class="jobView">
                                                                        <button type="button" name="cmdPrev"
                                                                                class="btn btn-primary">
                                                                            <i class="fa fa-eye"></i>
                                                                        </button>
                                                                    </a>
                                                                </c:if>
                                                            </div>
                                                            <script type="text/javascript">
                                                                $('.jobView').popupWindow({
                                                                    height: screen.height,
                                                                    width: screen.width,
                                                                    resizable: 1,
                                                                    centerScreen: 1,
                                                                    scrollbars: 1,
                                                                    windowName: 'swip'
                                                                });
                                                            </script>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                              && motorEngineerDto.inspectionDto.inspectionId ne 6
                                              && motorEngineerDto.inspectionDto.inspectionId ne 9
                                              && motorEngineerDto.inspectionDto.inspectionId ne 7
                                              && motorEngineerDto.desktopInspection != 'Y'}">
                        <div class="card mt-2">
                            <div class="card-header p-0" id="heading10">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" data-toggle="collapse" data-target="#collapse10"
                                       aria-expanded="true" aria-controls="collapse10">
                                        Third Party Details
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse10" class="collapse" aria-labelledby="heading0"
                                 data-parent="#accordionOne">
                                <div class="card-body p-lg-3 p-2">
                                    <div class="row">
                                        <iframe width="100%" id="requestFrame" style="height: 100vh ;"
                                                frameborder="0"
                                                class="scroll"
                                                src="${pageContext.request.contextPath}/MotorEngineerController/thirdPartyDetails"></iframe>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                    <div class="card mt-2">
                        <div class="card-header p-0" id="heading12">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse12"
                                   aria-expanded="true" aria-controls="collapse12">
                                    Log Details
                                </a>
                            </h5>
                        </div>
                        <div id="collapse12" class="collapse " aria-labelledby="heading12"
                             data-parent="#accordionOne">
                            <div class="card-body p-lg-3 p-2">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="form-group row">
                                            <div class="col-sm-12" id="logDetails">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-2">
                        <div class="card-header p-0" id="heading13">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse13"
                                   aria-expanded="true" aria-controls="collapse13">
                                    Special Remarks
                                </a>
                            </h5>
                        </div>
                        <div id="collapse13" class="collapse " aria-labelledby="heading13"
                             data-parent="#accordionOne">
                            <div class="card-body p-lg-3 p-2">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="form-group row">
                                            <div class="col-sm-12">
                                                <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                                     id="specialRemarks">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>
            <fieldset class="col-md-5 border scroll" style="height: calc(100vh - 210px);">
                <%--<h6> Inspection Details</h6>--%>
                <%--<hr class="my-1">--%>
                <div class="mt-3">
                    <div id="accordion">
                        <div class="card mt-2">
                            <div class="card-header p-0" id="heading5">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" data-toggle="collapse" data-target="#collapse5"
                                       aria-expanded="true" aria-controls="collapse5">
                                        Documents
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse5" class="collapse " aria-labelledby="heading5"
                                 data-parent="#accordion">
                                <div class="card-body p-lg-3 p-2">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <iframe width="100%" style="height: 100vh;" frameborder="0"
                                                    id="iframeDocumentUpload" name="iframeDocumentUpload"
                                                    height="90vh"
                                                    class="scroll"
                                                    src="${pageContext.request.contextPath}/MotorEngineerController/documentUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}"></iframe>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mt-2">
                            <div class="card-header p-0" id="heading6">
                                <h5 class="mb-0">
                                    <a class="btn btn-link" data-toggle="collapse" data-target="#collapse6"
                                       aria-expanded="true" aria-controls="collapse6">
                                        Vehicle Image
                                    </a>
                                </h5>
                            </div>
                            <div id="collapse6" class="collapse " aria-labelledby="heading6"
                                 data-parent="#accordion">
                                <div class="card-body p-lg-3 p-2">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div id="imageUploadContainer"></div>

                                            <%--<iframe width="100%" style="height: 100vh;" frameborder="0"
                                                    id="iframeImageUpload" name="iframeImageUpload"
                                                    height="90vh"
                                                    class="scroll"
                                                    src="${pageContext.request.contextPath}/MotorEngineerController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}"></iframe>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <c:if test="${8 == motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}">
                            <div class="card mt-2">
                                <div class="card-header p-0" id="heading7">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse" data-target="#collapse7"
                                           aria-expanded="true" aria-controls="collapse7">
                                            Claim Form
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapse7" class="collapse " aria-labelledby="heading7"
                                     data-parent="#accordion">
                                    <div class="card-body p-lg-3 p-2">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="card-body p-1">
                                                    <div class="row">
                                                        <div class="col-12 pdf-thumbnails">
                                                            <c:forEach var="estimateDocument"
                                                                       items="${claimsDto.estimateDocumentList}">
                                                                <c:set var="iconColorCls" value=" text-dark "/>
                                                                <c:if test="${estimateDocument.documentStatus=='A'}">
                                                                    <c:set var="iconColorCls"
                                                                           value=" text-success"/>
                                                                </c:if>
                                                                <c:if test="${estimateDocument.documentStatus=='H'}">
                                                                    <c:set var="iconColorCls"
                                                                           value=" text-warning"/>
                                                                </c:if>
                                                                <c:if test="${estimateDocument.documentStatus=='R'}">
                                                                    <c:set var="iconColorCls" value=" text-danger"/>
                                                                </c:if>
                                                                <a href="${pageContext.request.contextPath}/MotorEngineerController/documentViewer?refNo=${estimateDocument.refNo}&jobRefNo=${estimateDocument.refNo}&PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}"
                                                                   class="claimView${estimateDocument.refNo}">
                                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>

                                                                </a>
                                                                <script type="text/javascript">
                                                                    $('.claimView${estimateDocument.refNo}').popupWindow({
                                                                        height: screen.height,
                                                                        width: screen.width,
                                                                        centerBrowser: 0,
                                                                        left: 0,
                                                                        resizable: 1,
                                                                        centerScreen: 1,
                                                                        scrollbars: 1,
                                                                        windowName: 'swip'
                                                                    });
                                                                </script>

                                                            </c:forEach>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:if>
                    </div>
                </div>
            </fieldset>
            <fieldset class="col-md-12">
                <div id="accordion3">
                    <div class="card mt-2">
                        <div class="card-header p-0" id="heading8">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#collapse8"
                                   aria-expanded="false" aria-controls="collapse8">
                                    Photo Comparison
                                </a>
                            </h5>
                        </div>
                        <div id="collapse8" class="collapse " aria-labelledby="heading8"
                             data-parent="#accordion3">
                            <div class="card-body p-0">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <iframe width="50%" style="height: 100vh;" frameborder="0"
                                                id="iframePhotoCom1" name="iframePhotoCom1"
                                                height="90vh"
                                                class="scroll float-left"
                                                src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=1&policyNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                        <iframe width="50%" style="height: 100vh;" frameborder="0"
                                                id="iframePhotoCom2" name="iframePhotoCom2"
                                                height="90vh"
                                                class="scroll float-left"
                                                src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=2&policyNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <c:if test="${PREVIOUS_INSPECTION!='Y'}">
                        <div class="float-right mt-3">
                            <button class="btn btn-link" type="button" onclick="goBack()"><b>Back</b></button>
                        </div>
                    </c:if>
                </div>
            </fieldset>
        </div>
    </form>
</div>
<c:forEach var="claimDocumentTypeDto" items="${motorEngineerDto.inspectionDetailsDto.claimDocumentTypeDtoList}">
    <form name="frmDocumentModal${claimDocumentTypeDto.documentTypeId}"
          id="frmDocumentModal${claimDocumentTypeDto.documentTypeId}">
        <input type="hidden" name="documentTypeId"
               value="${claimDocumentTypeDto.documentTypeId}">
        <input type="hidden" name="claimNo" value="${motorEngineerDto.inspectionDetailsDto.claimNo}">
        <input type="hidden" name="jobRefNo" value="${motorEngineerDto.inspectionDetailsDto.refNo}">
        <input type="hidden" name="departmentId" value="3">
        <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
             id="docUploadModal${claimDocumentTypeDto.documentTypeId}" aria-hidden="true"
             style="    background: #333333c2;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content p-2" style="overflow: hidden">
                    <div class="modal-header  p-2">
                        <h6 class="modal-title"
                            id="modalLabel${claimDocumentTypeDto.documentTypeId}">${claimDocumentTypeDto.documentTypeName} </h6>
                        <small class="text-danger pull-right"><b> .PNG / .JPG / .PDF File Formats Only.</b></small>
                    </div>
                    <p id="errorUpload${claimDocumentTypeDto.documentTypeId}"></p>
                    <div class=" mt-4">
                        <div class="col-sm-12">
                            <!-- The fileinput-button span is used to style the file input field as button -->
                            <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                <!-- The file input field used as target for the file upload widget -->
                                        <input id="fileUploadClaim${claimDocumentTypeDto.documentTypeId}" type="file"
                                               name="files[]" multiple>
                                    </span>
                            <!-- The global progress bar -->
                            <div id="progressClaim${claimDocumentTypeDto.documentTypeId}" class="progress">
                                <div class="progress-bar bg-success"></div>
                            </div>
                            <!-- The container for the uploaded files -->
                            <div id="filesClaim${claimDocumentTypeDto.documentTypeId}" class="files"></div>
                            <br>
                        </div>
                    </div>
                    <div class="modal-footer p-1">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                onclick="closeUploadWindow('${claimDocumentTypeDto.documentTypeId}');">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
             id="imgUploadModal" aria-hidden="true"
             style="    background: #333333c2;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content p-2" style="overflow: hidden">
                    <div class="modal-header  p-2">
                        <h6 class="modal-title"
                            id="imagemodalLabel">Image Upload</h6>
                    </div>
                    <p id="imageerrorUpload"></p>
                    <div class=" mt-4">
                        <div class="col-sm-12">
                            <!-- The fileinput-button span is used to style the file input field as button -->
                            <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                <!-- The file input field used as target for the file upload widget -->
                                        <input id="imageUpload" type="file" name="files[]" multiple>
                                    </span>
                            <!-- The global progress bar -->
                            <div id="imageProgress" class="progress">
                                <div class="progress-bar bg-success"></div>
                            </div>
                            <!-- The container for the uploaded files -->
                            <div id="imageFiels" class="files"></div>
                            <br>
                        </div>
                    </div>
                    <div class="modal-footer p-1">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                onclick="closeUploadImageWindow();">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <script>
            documentUploadIds.push('${claimDocumentTypeDto.documentTypeId}');
        </script>
    </form>
    <form id="backForm" name="backForm" method="POST"></form>
</c:forEach>
<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>
<script type="text/javascript">
    $(".datepicker").datepicker({dateFormat: 'yy-mm-dd'});
    $(".yearpicker").datepicker({dateFormat: 'yy-mm-dd'});

    function documentFileUploder(documentTypeId) {
        'use strict';
        var progress = 0;
        var url = '${pageContext.request.contextPath}/DocumentUploadController';
        $('#fileUploadClaim' + documentTypeId).fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {
                data.submit()
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<i class="fa fa-file-pdf-o fa-4x m-3"></i>').appendTo('#filesClaim' + documentTypeId);
                });
                $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorUpload' + documentTypeId).removeClass("bg-danger");
                $('#errorUpload' + documentTypeId).addClass("bg-success");
                $('#errorUpload' + documentTypeId).html("");
                $('#errorUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Document Uploaded Successfully!</span>').appendTo('#errorUpload' + documentTypeId);
                $('#errorUpload' + documentTypeId).fadeOut(4000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);

                $('#progressClaim' + documentTypeId + ' .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorUpload' + documentTypeId).removeClass("bg-success");
                $('#errorUpload' + documentTypeId).addClass("bg-danger");
                $('#errorUpload' + documentTypeId).html("");
                $('#errorUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center">Document Upload failed.</span>').appendTo('#errorUpload' + documentTypeId);
                $('#errorUpload' + documentTypeId).fadeOut(4000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {

                    $('#progressClaim' + documentTypeId + ' .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                    $('#errorUpload' + documentTypeId).removeClass("bg-danger");
                    $('#errorUpload' + documentTypeId).removeClass("bg-success");

                    $('#errorUpload' + documentTypeId).addClass("bg-primary");
                    $('#errorUpload' + documentTypeId).html("");
                    $('#errorUpload' + documentTypeId).fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#errorUpload' + documentTypeId);


                });
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    }

    $(document).ready(function () {
        'use strict';
        // Change this to the location of your server-side upload handler:
        var url = '${pageContext.request.contextPath}/ImageUploadController';
        var progress = 0;
        $('#imageUpload').fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {

                data.submit();
                return;
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<img src="" alt="">').appendTo('#imageFiels');
                });
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-danger");
                $('#imageerrorUpload').addClass("bg-success");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Image Uploaded Successfully!</span>').appendTo('#imageerrorUpload');
                $('#imageerrorUpload').fadeOut(9000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);
                // alert(progress);
                $('#imageProgress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-success");
                $('#imageerrorUpload').addClass("bg-danger");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center">Image Upload failed.</span>').appendTo('#imageerrorUpload');
                $('#imageerrorUpload').fadeOut(9000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {
                    $('#imageProgress .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#imageerrorUpload').removeClass("bg-primary");
                    $('#imageerrorUpload').removeClass("bg-danger");
                    $('#imageerrorUpload').removeClass("bg-success");

                    $('#imageerrorUpload').addClass("bg-primary");
                    $('#imageerrorUpload').html("");
                    $('#imageerrorUpload').fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#imageerrorUpload');


                });

            }, drop: function (e, data) {
                e.preventDefault();
            }, dragover: function (e, data) {
                // return false;
            }


        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');


        if (${motorEngineerDto.chassisNoConfirm eq 'Wrong'}) {
            $('#chassisDiv').show();
        } else if (${motorEngineerDto.chassisNoConfirm eq 'Not_Checked'}) {
            $('#notCheckDiv').show();
        }

    });

    documentUploadIds.forEach(function (t) {
        documentFileUploder(t)
    });


    function closeUploadWindow(documentTypeId) {
        loadLogDetails();
        //        $('docUploadModal'+documentTypeId).hide();
        var iframeEl = document.getElementById("iframeDocumentUpload");
        iframeEl.contentWindow.reloadPage();
        $('#filesClaim' + documentTypeId).html('');
        //        $('#progressClaim .progress-bar').css('width',0);
    }

    function closeUploadImageWindow() {
        //   var iframeEl = document.getElementById("iframeImageUpload");
        //   iframeEl.contentWindow.reloadImageUploadPage();
        //   $('#imageFiels').html('');
        loadImageUploadViewer();
        loadLogDetails();

    }

    function saveDetails() {
        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/saveDetails?ACTION_TYPE=DRAFT";
        document.frmMain.method = "POST";
        document.frmMain.submit();
    }

    $('#btnHold').click(function () {
        paymentHold();
    });

    $("#jobType").change(function () {
        calAssessorFee();
    });

    $("#mileage,#otherFee,#deductions,#costOfCall").keyup(function () {
        calAssessorFee();
    });

    $("#jobType").on("change", function () {
        getTimeSlots();
    });

    function getTimeSlots() {
        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/fetchTimeSlotForInspection";

        $.ajax({
            url: URL,
            type: 'POST',
            data: {
                jobType: $("#jobType").val(),
                inspectionTypeId: "${motorEngineerDto.inspectionDto.inspectionId}"
            },
            async: false,
            success: function (result) {
                console.log("result ", result)
                // Ensure result is a proper object (in case it's a JSON string)
                var timeSlots = typeof result === "string" ? JSON.parse(result) : result;

                var select = $('#assessorFeeDetailId');
                select.empty(); // Clear existing options

                select.append('<option value="0">-- Please Select --</option>');

                // Append new options
                $.each(timeSlots, function(index, item) {
                    select.append(
                        $('<option></option>').val(item.value).text(item.label)
                    );
                });
            },
            error: function () {
                alert("Failed to load inspection time slots.");
            }
        });
    }

    function calAssessorFee() {
        var refNo = "${motorEngineerDto.refNo}";
        if ("" == $("#jobType").val()) {
            return false;
        }
        var dataObj = {
            refNo: refNo,
            jobType: $("#jobType").val(),
            assessorFeeDetailId: $("#assessorFeeDetailId").val(),
            otherFee: $("#otherFee").val(),
            deductions: $("#deductions").val(),
            mileage: $("#mileage").val(),
            costOfCall: $("#costOfCall").val(),
            inspectionTypeId: "${motorEngineerDto.inspectionDto.inspectionId}"
        };

        var URL = "${pageContext.request.contextPath}/MotorEngineerController/calculateProfessionalFee";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            async: false,
            success: function (result) {
                if (0 > parseInt(result)) {
                    notify("Deductions can not be exceed total fee", "danger");
                    $("#deductions").val(0);
                    calAssessorFee();
                } else {
                    $("#totalAssessorFee").val(result);
                }
            }
        });
    }

    function setAsReadOnlyAllElements(selector) {
        $(selector).find(':input').not('.skipReadOnly').prop('readonly', true);
        $(selector).find('textarea').not('.skipReadOnly').prop('readonly', true);
        $(selector).find(':radio:not(:checked)').not('.skipReadOnly').prop('disabled', true);
        $(selector).find('select').not('.skipReadOnly').each(function () {
            var selectVal = $(this).val();
            $(this).children('option').each(function () {
                if (selectVal == $(this).val()) {
                    $(this).attr('disabled', false);
                    $(this).attr('selected', true);
                } else {
                    $(this).attr('disabled', true);
                }
            });
        });
        $(selector).find('button').not('.skipReadOnly').prop('disabled', true);
        $(selector).find(':checkbox[readonly="readonly"]').not('.skipReadOnly').click(function () {
            return false;
        });
    }

    $(document).ready(function () {
        $("#thirdPartyInvolved_N").prop("checked", true);
        disable_3rd_Party_details('N');

        if (${(motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 0 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 29 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14)
         or (motorEngineerDto.inspectionDto.inspectionId eq 8
         and (motorEngineerDto.inspectionDetailsDto.recordStatus eq 9
                              || motorEngineerDto.inspectionDetailsDto.recordStatus eq 33
                              || motorEngineerDto.inspectionDetailsDto.recordStatus eq 34)))}) {
            $('#estimationDiv').find('fieldset').not(':has(>.assessor_pmt_div)').each(function () {
                setAsReadOnlyAllElements($(this));
            });
            setAsReadOnlyAllElements($('#tyreConditionDiv'));
            setAsReadOnlyAllElements($('#inspectionDetailsDiv'));
        }
        if (${motorEngineerDto.inspectionDetailsDto.assessorFeeAuthStatus eq 'A'}) {
            setAsReadOnlyAllElements($('#assessorPmtDiv'));
        }

        if (${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser)}) {
            if (${motorEngineerDto.inspectionDetailsDto.recordStatus eq 80}) {
                $('#estimationDiv').find('fieldset').not(':has(>.assessor_pmt_div)').each(function () {
                    setAsReadOnlyAllElements($(this));
                });
                setAsReadOnlyAllElements($('#tyreConditionDiv'));
                setAsReadOnlyAllElements($('#inspectionDetailsDiv'));
                setAsReadOnlyAllElements($('#assessorPmtDiv'));
                $('#cmdAuth').prop('disabled', false);
                $('#btnReturn').prop('disabled', false);
                $('#forward').prop('disabled', false);
                $('#specialRemark').prop('readonly', false);
            }
        } else if (${G_USER.userId ne motorEngineerDto.inspectionDetailsDto.assignRteUser}) {
            $('#estimationDiv').find('fieldset').not(':has(>.assessor_pmt_div)').each(function () {
                setAsReadOnlyAllElements($(this));
            });
            setAsReadOnlyAllElements($('#tyreConditionDiv'));
            setAsReadOnlyAllElements($('#inspectionDetailsDiv'));
            setAsReadOnlyAllElements($('#assessorPmtDiv'));
        }

        var isDesktop = '${motorEngineerDto.desktopInspection}';
        if (isDesktop == 'Y') {
            $('input[name="genuineOfAccident"]').attr('disabled', 'disabled');
            $('input[name="investReq"]').attr('disabled', 'disabled');
        }
    });

    function processThirdParty(type, key) {

        if ("EDIT" == type) {
            disable_3rd_Party_details('Y');
            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=GET&KEY=" + key,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    $("#txnId").val(obj.txnId);
                    $("#ccTpdId").val(obj.ccTpdId);
                    $("#type").val(obj.type);
                    $("#lossType").val(obj.lossType);
                    $("#itemType").val(obj.itemType);
                    $("#vehicleNo").val(obj.vehicleNo);
                    $("#contactNo").val(obj.contactNo);
                    $("#insureDetails").val(obj.insureDetails);
                    $("#remark").val(obj.remark);
                    $("#key").val(key);

                    $("input[name=thirdPartyInvolved]").prop("checked", false);
                    $("input[name=intendClaim]").prop("checked", false);
                    $("#thirdPartyInvolved_" + obj.thirdPartyInvolved).prop("checked", true);
                    $("#intendClaim_" + obj.intendClaim).prop("checked", true);
                }
            });

        } else if ("DELETE" == type) {
            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=DELETE&KEY=" + key,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    $("#txnId").val(obj.type);
                    $("#type").val(obj.type);
                    $("#lossType").val(obj.lossType);
                    $("#itemType").val(obj.itemType);
                    $("#vehicleNo").val(obj.vehicleNo);
                    $("#contactNo").val(obj.contactNo);
                    $("#insureDetails").val(obj.insureDetails);
                    $("#remark").val(obj.remark);

                    $("#thirdPartyInvolved").val(obj.lossType);
                    $("#intendClaim").val(obj.lossType);
                }
            });
        } else if ("ADD" == type) {
            var dataObj = {
                txnId: $("#txnId").val(),
                ccTpdId: $("#ccTpdId").val(),
                type: $("#type").val(),
                lossType: $("#lossType option:selected").val(),
                itemType: $("#itemType option:selected").val(),
                vehicleNo: $("#vehicleNo").val(),
                contactNo: $("#contactNo").val(),
                insureDetails: $("#insureDetails").val(),
                remark: $("#remark").val(),
                thirdPartyInvolved: $('input[name=thirdPartyInvolved]:checked').val(),
                intendClaim: $('input[name=intendClaim]:checked').val()
            };

            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=ADD&KEY=" + key,
                type: 'POST',
                data: dataObj,
                success: function (result) {
                    $("#TPdata tbody").load(contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=LIST");
                    resetForm();
                }
            });
        }


    }

    $('#vehicleNo').focusout(function () {
        $.ajax({
            url: contextPath + "/CallCenter/searchVehicle?vehicleNo=" + this.value,
            type: 'POST',
            success: function (result) {
                if (result == 'YES') {
                    $('#vehicalAvailability').show()
                } else {
                    $('#vehicalAvailability').hide()
                }
            }
        });
    });

    function resetForm() {
        $("#txnId").val("");
        $("#ccTpdId").val("");
        $("#type").val("");
        $("#lossType").val("");
        $("#itemType").val("");
        $("#vehicleNo").val("");
        $("#contactNo").val("");
        $("#insureDetails").val("");
        $("#remark").val("");
        $("#key").val("");

        $("input[name=thirdPartyInvolved]").prop("checked", false);
        $("#thirdPartyInvolved_N").prop("checked", true);
        $("input[name=intendClaim]").prop("checked", false);
    }

    function disable_3rd_Party_details(stat) {
        if (stat == "N") {
            $(".disable").addClass("text-mute").prop('disabled', true);
            $("#addbtn").prop('disabled', true);
            resetForm()
        } else {
            $(".disable").removeClass("text-mute").attr('disabled', false);
            $("#addbtn").prop('disabled', false);
            vehicleNoFieldStatChange($('#itemType').val());

        }

    }


    function vehicleNoFieldStatChange(val) {
        if (1 == val) {
            $("#vehicleNo").prop('disabled', false);
        } else {
            $("#vehicleNo").prop('disabled', true).val("");
        }
    }

    $(function () {
        if ('${thirdPartyDto.thirdPartyInvolved}' == 'Y') {
            disable_3rd_Party_details('Y');
        }
    });


    $('[data-toggle="collapse"]').click(function () {
        $('body').animate({
            scrollTop: 0
        });
    });
    $("#inspectDateTime").datetimepicker({
        sideBySide: true,
        format: 'YYYY-MM-DD HH:mm',
        icons: {
            time: "fa fa-clock-o",
            date: "fa fa-calendar",
            up: "fa fa-arrow-up",
            down: "fa fa-arrow-down"
        }
    });

    var currentDate = '${Current_Date}';
    var accidentDate = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.accidDate}' + ' ' + '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.accidTime}';
    $("#inspectDateTime").data("DateTimePicker").maxDate(currentDate);
    $("#inspectDateTime").data("DateTimePicker").minDate(accidentDate);

    function enableDisableFirstStatement(stat) {
        if (stat == "Y") {
            //            $('#frmMain').data('formValidation').enableFieldValidators('firstStatementReqReason', true);
            $("#firstStatementReqReason").prop('disabled', false).trigger("chosen:updated");
            $("#firstStatementReqReason").val('${motorEngineerDto.firstStatementReqReason}');
        } else {
            //            $('#frmMain').data('formValidation').enableFieldValidators('firstStatementReqReason', false);
            $("#firstStatementReqReason").val('').prop('disabled', true).trigger("chosen:updated");
        }
    }


    $(document).ready(function () {
        isForward();
        if (${PREVIOUS_INSPECTION eq 'Y'}) {
            disableFormInputs('#frmMain');
            $('#changeSpecialRemark').prop("readonly", false);
        }
        if (${fieldsEnable eq  'Y'}) {
            removeDisableFormInputs("#frmMain");
        }
        var approved = $('#paymentStatus').val();
        if (approved == "P") {
            document.getElementById("btnHold").disabled = false;
            document.getElementById("jobType").disabled = true;
        } else if (approved == "H") {
            document.getElementById("jobType").disabled = false;
            document.getElementById("btnHold").disabled = true;
        } else {
            document.getElementById("jobType").disabled = false;
            document.getElementById("btnHold").disabled = true;
        }
        if (isOnsiteOrOffsite('${motorEngineerDto.inspectionDto.inspectionId}')) {
            $("#jobType option[value='0']").prop('disabled', true);
            $("#jobType option[value='2']").prop('disabled', false);
        } else {
            $("#jobType option[value='0']").prop('disabled', true);
            $("#jobType option[value='1']").prop('selected', true);
            $("#jobType option[value='2']").prop('disabled', true);
        }

        <%--if(${motorEngineerDto.chassisNoConfirm eq 'Confirm'}){--%>
        <%--correctSelect();--%>
        <%--}else if(${motorEngineerDto.chassisNoConfirm eq 'Wrong'}){--%>
        <%--wrongSelect();--%>
        <%--}else if(${motorEngineerDto.chassisNoConfirm eq 'Not_Checked'}){--%>
        <%--notCheckSelect();--%>
        <%--}--%>
        <%--if (${PREVIOUS_INSPECTION eq 'Y'}) {--%>
        <%--disableFormInputs('#frmMain');--%>
        <%--$('#changeSpecialRemark').prop("readonly", false);--%>
        <%--// $('#changeSpecialRemark').prop("disabled", false);--%>
        <%--}--%>
    });

    function viewClaimHistory(polRefNo, claimNo) {
        $("#" + claimNo).colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/CallCenter/viewClaimHistory?P_N_CLIM_NO=" + claimNo,
            onCleanup: function () {
            }
        });
    }

    function paymentHold() {
        bootbox.confirm({
            message: " Do you want to payment hold ? ",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/paymentHold";
                    document.frmMain.method = "POST";
                    document.frmMain.submit();
                }
            }
        });
    }

    $('#btnReturn').click(function () {
        returnByApproveAssignRte();
    });

    function returnByApproveAssignRte() {
        bootbox.confirm({
            message: " Do you want to return ?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    showLoader();
                    $('#rteSpecialRemark').val($('#specialRemark').val());
                    document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/returnByApproveAssignRte";
                    document.frmMain.method = "POST";
                    document.frmMain.submit();
                }
            }
        });
    }

    function viewEdit(refNo) {
        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/viewEdit?P_N_REF_NO=" + refNo;
        document.frmMain.method = "POST";
        document.frmMain.submit();
    }

    function isOnsiteOrOffsite(typeId) {
        if ('1' == typeId || '2' == typeId) {
            return true;
        } else {
            return false;
        }
    }

    function chassis_correct(stat) {
        if ($('#engNoConfirmbox').is(':checked')) {
            $(".correctChaNo").removeClass("text-mute").prop('disabled', false);
        } else {
            $(".correctChaNo").addClass("text-mute").prop('disabled', true);
        }
    }

    function authorizeAssessorFee() {
        showLoader();
        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/approve?ACTION_TYPE=AUTH_ASSESSOR_FEE";
        document.frmMain.method = "POST";
        document.frmMain.submit();
    }

    function saveInspectionDetailsRepoart() {
        $('input[name="genuineOfAccident"]').removeAttr('disabled');
        $('input[name="investReq"]').removeAttr('disabled');

        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/approve?ACTION_TYPE=SAVE_REPORT";
        document.frmMain.method = "POST";
        document.frmMain.submit();
        loadLogDetails();
        // viewClaimDetails1();
    }

    // function viewClaimDetails1() {
    //     $("#P_N_REF_NO").val($("#refNos").val());
    //     document.getElementById('frmForm').action = contextPath + "/MotorEngineerController/viewEdit";
    //     document.getElementById('frmForm').submit();
    //
    // }

    <%--$("button[type=submit]").on("click", function (e) {--%>
    <%--var val = $(this).val();--%>
    <%--$("#frmMain").submit(function (event) {--%>
    <%--var action = "";--%>
    <%--if (val === "Authorize") {--%>
    <%--action = "${pageContext.request.contextPath}/MotorEngineerController/approve?ACTION_TYPE=AUTH_ASSESSOR_FEE";--%>
    <%--$("#frmMain").attr('action', action);--%>

    <%--}--%>


    <%--});--%>


    <%--});--%>

    async function checkAcrAndAuthorize(isForwarded) {
        var inspectionType = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}';
        var sumInsuredVal = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.sumInsured}';
        var pav = ($('#pav').val());
        var acr = ($('#acr').val());
        var totalApprovedAcr = ($('#totalApprovedAcr').val());
        var policyNo = $('#policyCoverNoteNo').val();
        var claimNo = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}';
        if (null != policyNo) {
            var covn = policyNo.substring(0, 4);
        }

        let validate = await validateClosedAndPremiumOutstandingPolicies();

        $.ajax({
            type: 'POST',
            url: "${pageContext.request.contextPath}/MotorEngineerController/isApproveAcrAndIsOnsitePending",
            data: {
                acr: acr,
                inspectionType: inspectionType,
                sumInsuredVal: sumInsuredVal,
                pav: pav,
                totalApprovedAcr: totalApprovedAcr,
                covn: covn,
                claimNo: claimNo

            },
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj == "APPROVE_ACR_FAIL") {
                    notify("ACR Value Cannot be Greater than Sum Insured or PAV", "danger")
                } else {
                    let bootboxMsg = '';

                    if (validate == 0) {
                        bootboxMsg = "This is a cancelled policy and has Rs." + $('#outstand').val() + " of outstanding premium amount. Do you want to proceed?";
                    } else if (validate == 1) {
                        bootboxMsg = "This is a cancelled policy. Do you want to proceed?";
                    } else if (validate == 2) {
                        bootboxMsg = "This policy has Rs." + $('#outstand').val() + " of outstanding premium amount. Do you want to proceed?";
                    }
                    if (inspectionType == 4 || inspectionType == 8) {
                        if (obj == "IS_ONSITE_PENDING") {
                            bootboxMsg = '';
                            bootboxMsg += "This Claim has a pending On-site / Off-site inspection";
                            if (validate == 0) {
                                bootboxMsg += " and This is a Cancelled Policy and has Rs." + $('#outstand').val() + " of Outstanding Premium Amount and Also Claim has a pending On-site / Off-site inspection";
                            } else if (validate == 1) {
                                bootboxMsg += " and This is a cancelled policy";
                            } else if (validate == 2) {
                                bootboxMsg += " and This policy has Rs." + $('#outstand').val() + " of outstanding premium amount";
                            }
                            bootboxMsg += ', Do you want to proceed ?';
                            bootbox.confirm({
                                message: bootboxMsg,
                                buttons: {
                                    cancel: {
                                        label: 'No',
                                        className: 'btn-secondary float-right'
                                    },
                                    confirm: {
                                        label: 'Yes',
                                        className: 'btn-primary'
                                    }
                                },
                                callback: function (result) {
                                    if (result == true) {
                                        $('#isOnsitePending').val("Y")
                                        authorizeInspectionDetailsGarageOrDesktop(isForwarded);
                                    }
                                }
                            });
                        } else {
                            if (bootboxMsg != '') {
                                bootbox.confirm({
                                    message: bootboxMsg,
                                    buttons: {
                                        cancel: {
                                            label: 'No',
                                            className: 'btn-secondary float-right'
                                        },
                                        confirm: {
                                            label: 'Yes',
                                            className: 'btn-primary'
                                        }
                                    },
                                    callback: function (result) {
                                        if (result == true) {
                                            authorizeInspectionDetailsGarageOrDesktop(isForwarded);
                                        }
                                    }
                                });
                            } else {
                                authorizeInspectionDetailsGarageOrDesktop(isForwarded);
                            }
                        }

                    } else {
                        if (bootboxMsg != '') {
                            bootbox.confirm({
                                message: bootboxMsg,
                                buttons: {
                                    cancel: {
                                        label: 'No',
                                        className: 'btn-secondary float-right'
                                    },
                                    confirm: {
                                        label: 'Yes',
                                        className: 'btn-primary'
                                    }
                                },
                                callback: function (result) {
                                    if (result == true) {
                                        authorizeInspectionDetailsGarageOrDesktop(isForwarded);
                                    }
                                }
                            });
                        } else {
                            authorizeInspectionDetailsGarageOrDesktop(isForwarded)
                        }
                    }
                }
            }
        });
    }

    <%--function forwardToNextUser() {--%>
    <%--    removeDisableFormInputs("#frmMain");--%>
    <%--    document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/approve?FORWARD=Y&ACTION_TYPE=AUTH_INSPECTION_DETAILS";--%>
    <%--    document.frmMain.method = "POST";--%>
    <%--    document.frmMain.submit();--%>

    <%--}--%>

    $("#acr").keyup(function () {
        isForward();
    });

    $("#acr").change(function () {
        isForward();
    });

    function isForward() {
        var inspectionType = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}';
        var refNo = '${motorEngineerDto.refNo}';
        if ('1' == inspectionType || '2' == inspectionType || '4' == inspectionType || '5' == inspectionType || '6' == inspectionType || '8' == inspectionType) {

            var dataObj = {
                acr: $("#acr").val(),
                refNo,
                inspectionType
            };

            var URL = "${pageContext.request.contextPath}/MotorEngineerController/isForward";
            $.ajax({
                url: URL,
                type: 'POST',
                data: dataObj,
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj == "FAIL") {
                        if ('8' == inspectionType) {
                            $('#forward').hide();
                        } else {
                            $('#forwardDiv').hide();
                        }
                        $('#cmdAuth').show();
                    } else {
                        if ('8' == inspectionType) {
                            $('#forward').show();
                        } else {
                            $('#forwardDiv').show();
                        }
                        $('#cmdAuth').hide();
                    }
                }
            });
        }
    }


    function authorizeInspectionDetails(isForwarded) {
        var message;
        if ('Y' == isForwarded) {
            message = " Do you want to forward ?"
        } else {
            message = " Do you want to authorize ?"
        }
        bootbox.confirm({
            message: message,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    authorize(isForwarded);
                }
            }
        });


    }

    function authorize(isForwarded) {
        removeDisableFormInputs("#frmMain");

        var isChecked = document.getElementById("onsiteReviewCheck")?.checked;

        // Remove existing hidden input if exists
        var existingHidden = document.getElementById("hiddenOnsiteReview");
        if (existingHidden) {
            existingHidden.remove();
        }

        // Create and append hidden input to capture value
        var hiddenInput = document.createElement("input");
        hiddenInput.type = "hidden";
        hiddenInput.name = "onsiteReview";
        hiddenInput.id = "hiddenOnsiteReview";
        hiddenInput.value = isChecked ? "true" : "false";

        document.frmMain.appendChild(hiddenInput);

        showLoader();
        document.frmMain.action = "${pageContext.request.contextPath}/MotorEngineerController/approve?ACTION_TYPE=AUTH_INSPECTION_DETAILS&FORWARD=" + isForwarded;
        document.frmMain.method = "POST";
        document.frmMain.submit();
    }

    function authorizeInspectionDetailsGarageOrDesktop(isForwarded) {
        $('#frmMain').formValidation('revalidateField', 'advancedAmount');
        var advanced = $('#advancedamount').val();
        var settlementMethod = $('#settlementMethod').val();
        var message;
        if (isForwarded == 'Y') {
            message = " Do you want to forward ?";
        } else {
            message = " Do you want to authorize ?";
        }
        if (0 != settlementMethod) {
            if (advanced == '0.00') {
                bootbox.confirm({
                    message: "Advance amount is " + advanced + " Do you want to continue ?",
                    buttons: {
                        cancel: {
                            label: 'No',
                            className: 'btn-secondary float-right'
                        },
                        confirm: {
                            label: 'Yes',
                            className: 'btn-primary'
                        }
                    },
                    callback: function (result) {
                        if (result == true) {
                            authorize(isForwarded);
                        }
                    }
                });
            } else {

                bootbox.confirm({
                    message: message,
                    buttons: {
                        cancel: {
                            label: 'No',
                            className: 'btn-secondary float-right'
                        },
                        confirm: {
                            label: 'Yes',
                            className: 'btn-primary'
                        }
                    },
                    callback: function (result) {
                        if (result == true) {
                            authorize(isForwarded);
                        }
                    }
                });
            }
        }
    }

    function goBack() {
        bootbox.confirm({
            message: "Are you sure you want to close this Page?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }

            },
            callback: function (result) {
                if (result == true) {
                    document.frmMain.action = contextPath + "/MotorEngineerController/jobView";
                    document.frmMain.submit();
                }
            }
        });
    }

    function updateChangeRequest() {
        var remark = $("#changeSpecialRemark").val();
        var refNo =${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo};
        var claimNo = ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo};

        if (remark != '') {
            $.ajax({
                url: contextPath + "/MotorEngineerController/updateChangeRequest?refNo=" + refNo + "&remark=" + remark + "&claimNo=" + claimNo,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != '') {
                        if (obj == 'FAILED') {
                            notify("Can not be Submit, This file already assigned to claim handler", "danger");
                        } else {
                            notify("Successfully Change Request requested", "success");
                        }
                        $('#changeBtn').prop("disabled", true);
                    } else {
                        notify("Can not be updated", "danger");
                    }

                }
            });
        } else {
            $('#remarkDiv').show();
            $('#changeBtn').prop("disabled", true);
        }

    }

    function isRemark() {
        var remark = $("#changeSpecialRemark").val();
        if (remark != '' && ${not isReadOnly}) {
            $('#changeBtn').prop("disabled", false);
            $('#remarkDiv').hide();
        }
    }

    function updateAsChangeRequest() {
        var remark = $("#changeAsSpecialRemark").val();
        var refNo =${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo};
        var claimNo = ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo};

        if (remark != '') {
            $.ajax({
                url: contextPath + "/MotorEngineerController/updateAsChangeRequest?refNo=" + refNo + "&remark=" + remark + "&claimNo=" + claimNo,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != '') {
                        notify(obj, "success");
                        $('#asChangeBtn').prop("disabled", true);
                    } else {
                        notify("Can not be updated", "danger");
                    }

                }
            });
        } else {
            $('#asRemarkDiv').show();
            $('#asChangeBtn').prop("disabled", true);
        }

    }

    function isAsRemark() {
        var remark = $("#changeAsSpecialRemark").val();
        if (remark != '' && ${not isReadOnly}) {
            $('#asChangeBtn').prop("disabled", false);
            $('#asRemarkDiv').hide();
        }
    }

    function addRemarks() {
        var formData = $('#frmMain').serialize();
        $.ajax({
            type: 'POST',
            url: "${pageContext.request.contextPath}/MotorEngineerController/addRteSpecialRemarks",
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    // alert(obj);
                    document.getElementById('inspectionSpecialRemark').value = ''
                    notify(obj, "success");

                }
                loadSpecialRemarks();
            }
        });
    };

    function addRemarkss() {

        var remark = $('#inspectionSpecialRemarks').val();
        var formData = $('#frmMain').serialize();
        $.ajax({
            type: 'POST',
            url: "${pageContext.request.contextPath}/MotorEngineerController/addRteSpecialRemarks?remark=" + remark,
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    // alert(obj);
                    document.getElementById('inspectionSpecialRemark').value = ''
                    notify(obj, "success");

                }
                loadSpecialRemarks();
            }
        });
    };

    function correctSelect() {
        //$('#frmMain').data('formValidation').enableFieldValidators('notCheckedReason', false);
        //$('#frmMain').data('formValidation').enableFieldValidators('chassisNo', false);
        $("#notCheckedReason").attr('disabled', 'disabled');
        $('#chassisDiv').hide();
        $('#notCheckDiv').hide();
        $('#chassisNo').val('${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}');
    }

    function wrongSelect() {
        $('#chassisDiv').show();
        $('#chassisNo').val('${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}');
        // $('#frmMain').data('formValidation').enableFieldValidators('chassisNo', true);
        $('#notCheckedReason').val('0');
        $("#notCheckedReason").attr('disabled', 'disabled');
        // $('#frmMain').data('formValidation').enableFieldValidators('notCheckedReason', false);
        $('#notCheckDiv').hide();

    }

    function notCheckSelect() {
        $('#chassisDiv').hide();
        //  $('#frmMain').data('formValidation').enableFieldValidators('chassisNo', false);
        $('#chassisNo').val('');
        $('#notCheckDiv').show();
        $("#notCheckedReason").removeAttr('disabled');
        // $('#frmMain').data('formValidation').enableFieldValidators('notCheckedReason', true);
    }

    function validateClosedAndPremiumOutstandingPolicies() {
        return new Promise(resolve => {

            $.ajax({
                url: contextPath + "/Claim/getPolicyValidity",
                data: {
                    V_POL_NO: '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyNumber}'
                },
                type: 'POST',
                success: function (result) {
                    var response = JSON.parse(result);

                    $("#isCancelled").val(response.policyStatus);
                    $("#outstand").val(response.outstandingPremium);

                    if (response.policyStatus == 'CAN' && response.outstandingPremium > 0) {
                        return resolve(0);
                    } else if (response.policyStatus == 'CAN') {
                        return resolve(1);
                    } else if (response.outstandingPremium > 0) {
                        return resolve(2);
                    } else {
                        return resolve(null);
                    }
                }
            });
        });
    }

    <%--function viewDocument(refNo, jobRefNo, previousInspection, sideClass) {--%>

    <%--$('.viewDocumentContainer').addClass(sideClass).resizable({--%>
    <%--handles: "e, w",--%>
    <%--ghost: true,--%>
    <%--helper: "resizable-helper"--%>
    <%--});--%>

    <%--$('.viewDocumentContainer  iframe#viewDocument').attr("src", "${pageContext.request.contextPath}/ClaimHandlerController/documentViewer?refNo=" + refNo + "&jobRefNo=" + jobRefNo + "&PREVIOUS_INSPECTION=" + previousInspection);--%>
    <%--alert('${pageContext.request.contextPath}');--%>
    <%--$(".viewDocumentClose").click(function () {--%>
    <%--$('.viewDocumentContainer').removeClass(sideClass + " ui-resizable").removeAttr("style");--%>
    <%--});--%>
    <%--}--%>

    if(${((null == motorEngineerDto.assessorAllocationDto.typeOnlineInspection) ||
          ('' == motorEngineerDto.assessorAllocationDto.typeOnlineInspection)) ||
                motorEngineerDto.assessorAllocationDto.typeOnlineInspection == 'N'}){
        document.getElementById("typeOnlineInspection").checked = false
    }else{
        document.getElementById("typeOnlineInspection").checked = true
    }

</script>
<c:if test="${isDesktopInspection}">
    <script type="text/javascript">
        $('.assessor_value').hide();
        $(".correctChaNo").removeClass("text-mute").prop('disabled', false);
    </script>
</c:if>
</body>
<script>
    hideLoader();
</script>
</html>
