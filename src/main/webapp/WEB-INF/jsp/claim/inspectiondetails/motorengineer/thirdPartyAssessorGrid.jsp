<%@taglib prefix="c" uri="jakarta.tags.core" %>
<c:forEach var="entry"
           items="${motorEngineerDto.thirdPartyAssessorMap}">
        <c:if test="${entry.value.type eq 'CALL_CENTER'}">
        <tr style="background-color: #007fff">
        </c:if>
        <c:if test="${entry.value.type eq 'ASSESSOR'}">
        <tr style="background-color: #0C0">
        </c:if>
        <c:if test="${entry.value.type eq 'MOTOR_ENGINEER'}">
        <tr>
        </c:if>
        <td>${entry.value.txnId eq 0 ? 'N/A' : entry.value.txnId}</td>
        <td>${DbRecordCommonFunctionBean.getValue("claim_loss_type","V_CAUSE_OF_LOSS", "N_ID", entry.value.lossType)}</td>
        <td>${DbRecordCommonFunctionBean.getValue("tp_vehicle_item_mst", "V_ITEM_DES", "N_ID", entry.value.itemType)}</td>
        <td>${entry.value.vehicleNo}</td>
        <td>${entry.value.intendClaim == 'Y' ? 'Yes' : entry.value.intendClaim== 'N' ? 'No' : 'Want to decide'}</td>
        <td>${entry.value.remark}</td>
        <td>${entry.value.inpUserId}</td>
        <td>${entry.value.inpDateTime}</td>
        <td>${entry.value.type}</td>
        <td>${entry.value.mappingId eq 0 ? 'N/A' : entry.value.mappingId}</td>
        <td>${empty entry.value.mappingType ? 'N/A' : entry.value.mappingType}</td>
        <td class="text-center">
            <div class="btn-group">
                <button type="button"
                        class="btn btn-primary loopbtn" title="Edit"
                        onClick="processThirdParty('EDIT', '${entry.key}');">
                    <i class='fa fa-edit'></i>
                </button>
            </div>
        </td>
    </tr>
</c:forEach>