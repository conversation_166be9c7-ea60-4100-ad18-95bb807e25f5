<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page import="com.misyn.mcms.utility.AppConstant" %>
<%@ page import="com.misyn.mcms.admin.User" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);
        String userId = (String) session.getAttribute("userId");


    } catch (Exception e) {
    }

    String ERROR = "";
    String str_v_status_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_status_para ", "n_ref_id", "v_status_desc", "v_type IN(0,1)", "");
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script language="javascript" type="text/javascript">
        const userId = "${userId}";

        $(function () {
            //
            //buttonImage: '/image/common/calendar.gif',
            var d1 = document.frmForm.txtFromDate.value;
            $("#txtFromDate").datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: '1940:2099',
                minDate: '-70y',
                maxDate: '0d',
                onClose: function (dateText, inst) {
                    document.getElementById("txtToDate").focus();
                }

            });
            $("#txtFromDate").datepicker('option', {dateFormat: "yy-mm-dd"});
            document.frmForm.txtFromDate.value = d1;

        });
        $(function () {
            //
            //buttonImage: '/image/common/calendar.gif',
            var d1 = document.frmForm.txtToDate.value;
            $("#txtToDate").datepicker({
                changeMonth: true,
                changeYear: true,
                yearRange: '1940:2099',
                minDate: '-70y',
                maxDate: '0d',
                onClose: function (dateText, inst) {
//                    document.getElementById("txtToDate").focus();
                }

            });
            $("#txtToDate").datepicker('option', {dateFormat: "yy-mm-dd"});
            document.frmForm.txtToDate.value = d1;

        });


        //------------Start JQuery Script---------------------


        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }, "Cancel": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }


        //------------End JQuery Script---------------------


        //================Others=========================================================
        function setNextButtonDisable() {

        }

        function init() {
            setNextButtonDisable();
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }


        function setLoading() {
            parent.document.getElementById("cell1").style.display = "block";
            parent.document.getElementById("loading").style.display = "block";

        }
    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="P_N_JOB_NO" id="P_N_JOB_NO" type="hidden"/>
        <input name="P_N_REF_NO" id="P_N_REF_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Inspection Picker</h5>

            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote"><%=ERROR%>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Date </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number">
                                            </div>
                                        </div>


                                        <div class="form-group row">
                                            <label for="assignUser" class="col-sm-4 col-form-label">RTE Name</label>
                                            <div class="col-sm-8">
                                                <select class="form-control form-control-sm chosen" name="assignUser" id="assignUser" onchange="search()">
                                                    <option value="" selected>ALL</option>
                                                    <option value="${userId}" >${userId}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Date </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Cover Note
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <c:if test="${ME_PAGE_TYPE eq 1}">
                                                        <option value="">ALL</option>
                                                        <option value="29">ASSIGNED</option>
                                                        <option value="9">APPROVED</option>
                                                        <option value="7">ATTENDED</option>
                                                        <option value="10">CLAIM CHANGE REQUESTED</option>
                                                        <option value="80">INSPECTION FORWARDED</option>
                                                        <option value="14">INSPECTION CHANGE REQUESTED</option>
                                                        <option value="33">FORWARD FOR INFORM DESKTOP</option>
                                                        <option value="34">RETURN DESKTOP</option>
                                                        <option value="8" selected>SUBMITTED</option>
                                                    </c:if>
                                                    <c:if test="${ME_PAGE_TYPE eq 2}">
                                                        <option value="" selected>ALL</option>
                                                        <option value="33">FORWARD FOR INFORM DESKTOP</option>
                                                        <option value="34">RETURNED DESKTOP INSPECTION</option>
                                                    </c:if>

                                                </select>

                                                <script type="text/javascript">
                                                    $('#txtV_status').val("");
                                                </script>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtJobNumber" class="col-sm-4 col-form-label"> Job
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtJobNumber" id="txtJobNumber"
                                                       class="form-control form-control-sm" placeholder="Job Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Inspection
                                                Type</label>
                                            <div class="col-sm-8">
                                                <select name="txtInspectionType" id="txtInspectionType"
                                                        class="form-control form-control-sm" onchange="search()">

                                                    <option value="0">Please Select One</option>
                                                    <option value="1">On site Inspection</option>
                                                    <option value="2">Off site Inspection</option>
                                                    <option value="3">Underwiting Inspection</option>
                                                    <option value="4">Garage Inspection</option>
                                                    <option value="5">DR Insepection</option>
                                                    <option value="6">Supplimantary Inspection</option>
                                                    <option value="7">After Repair inspection</option>
                                                    <option value="8">Desktop Assesment</option>
                                                    <option value="9">Salvage Inspection</option>
                                                    <option value="10">Investigation</option>
                                                    <option value="10">Investigation</option>
                                                    <option value="11">Call Estimate</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                                <%--<div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> 3<sup>rd</sup>
                                                Party Vehicle Number</label>
                                            <div class="col-sm-8">
                                                <input name="txt3rdVehicleNumber" id="txt3rdVehicleNumber"
                                                        class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                    </div>
                                </div>--%>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-danger"></i>&nbsp;&nbsp;Late
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-warning"></i> &nbsp;&nbsp; On
                                    Site</p>
                                <%--<h6>Policy Suspend &nbsp;&nbsp;<span class="badge badge-pill badge-secondary border">&nbsp;</span></h6>--%>
                            </div>

                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-history text-warning"></i>&nbsp;&nbsp;Exceed
                                    24 Hours </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-history text-danger"></i> &nbsp;&nbsp;
                                    Exceed 48 Hours</p>
                                <%--<h6>Policy Suspend &nbsp;&nbsp;<span class="badge badge-pill badge-secondary border">&nbsp;</span></h6>--%>
                            </div>
                        </div>
                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim Result</h6>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th>ref no</th>
                                                <th style="display:none" class=""><input type="checkbox" id="selectAll"
                                                                    onclick="selectAllCheckBoxs();"/></th>
                                                <th width="40px">No</th>
                                                <th>Job No</th>
                                                <th>Claim No</th>
                                                <th>Vehicle No</th>
                                                <th>Inspection Type</th>
                                                <th>Assigned Date / Time</th>
                                                <th>Job Status</th>
                                                <th>Claim Status</th>
                                                <th>Assessment Appr. Status</th>
                                                <th>Assessor Fee Appr. Status</th>
                                                <th class="min-tablet"></th>

                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
                             aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="exampleModalLabel">Reassign User3</h5>
                                    </div>
                                    <div class="modal-body">
                                        <input type="hidden" class="form-control" name="refNo" id="refNo">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Claim No</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="claimNo" id="claimNo"
                                                       readonly>
                                                <input type="hidden" class="form-control" name="claimNo1" id="claimNo1">
                                            </div>
                                        </div>
                                        <input type="hidden" id="userId" value="${userId}" />

                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Add New Assign User2</label>
                                            <div class="col-sm-8">
                                                <select class="form-control form-control-sm chosen" name="assignUsers" id="assignUsers">
                                                    <option selected value="${userId}">${userId}</option>
                                                </select>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" id="addClaimUserbtn" class="btn btn-primary"
                                                name="addClaimUserbtn"
                                                onclick="updateUser()" disabled>Save changes
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal fade animated fadeInDown" id="BulReassignpanelUser" tabindex="-1"
                             role="dialog"
                             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
                             aria-hidden="true">
                            <div class="modal-dialog " role="document" style="max-width: 80%">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="exampleModalLabel2">Bulk Reassign User</h5>
                                    </div>
                                    <div class="modal-body">
                                        <input type="hidden" class="form-control" name="txnId" id="txnId">
                                        <input type="hidden" class="form-control" name="claimNos" id="claimNos">
                                        <%--                                        <input type="hidden" class="form-control" name="refNo" id="refNo">--%>
                                        <div class="form-group row">
                                            <div class="col-md-12">
                                                <label style="font-size: medium">Selected
                                                    Job Numbers</label>
                                            </div>
                                            <div class="col-md-12" id="jobNoListDiv">
                                            </div>
                                            <input type="hidden" name="jobNoListTxt" id="jobNoListTxt" value="">
                                            <div class="col-md-5">

                                                <label class="col-form-label" style="font-size: medium;">Add
                                                    New
                                                    Assign User</label>
                                                <select class="form-control form-control-sm chosen"
                                                        name="assignUserBulkReassign"
                                                        id="assignUserBulkReassign" onchange="showMake(this.value)">
                                                    <option value="">Please Select</option>
                                                </select>
                                            </div>

                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" id="addBulkClaimUserbtn" class="btn btn-primary"
                                                    name="addBulkClaimUserbtn"
                                                    onclick="updateBulkUser()" disabled>Save changes
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="closeModal()">
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <input type="hidden" id="selectedIds" name="selectedIds">
                        <input type="hidden" id="selectClaimNoAndJobNo" name="selectClaimNoAndJobNo">
                        <input type="hidden" id="selectRefNo" name="selectRefNo">
                        <input type="hidden" id="idArray" name="idArray">

                        <div class="row">
                            <div class="col-lg-12" style="padding: 12px">
                                <div id="rteReassignDiv">
                                    <button class="btn-success btn btn-lg float-right btn-xs" type="button"
                                            id="rteReassignBtn"
                                            onclick="getSelectedIds(document.getElementsByName('checkBox'),document.getElementsByName('ClaimNoAndJobNoBox'),document.getElementsByName('refNo'))">
                                        RTE Reassign <i class="fa fa-edit"></i>
                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </form>
</div>


<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>

</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/rte-job-datatables-self-assign.js"></script>
</body>

<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });

    $(document).ready(function () {
        $("#rteReassignDiv").hide();
        // loadEngineers();
    });

    function updateUser() {
        $('#addClaimUserbtn').prop('disabled', 'disabled');
        var txnId = $('#refNo').val();
        var assignUser = $('#assignUsers').val();
        var claimN = $('#claimNo1').val();
        $.ajax({
            url: contextPath + "/ClaimAssignUserReassignController/updateSelfAssignRTEUser?refNo=" + txnId + "&assignUser=" + assignUser + "&claimNo=" + claimN,
            type: 'POST',
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj == "SUCCESS") {
                    notify("User Updated Successfully", "success");
                    closeModal();
                } else if (obj == "FAIL") {
                    notify("Failed to Update User", "danger");
                } else {
                    notify("System Error", "danger");
                }
                search();
            }
        });

    }

    function updateBulkUser() {
        $('#addBulkClaimUserbtn').prop('disabled', 'disabled');
        var txnId = $('#refNo').val();
        var assignUser = $('#assignUserBulkReassign').val();
        var claimN = $('#claimNo1').val();

        var selectIds = $('#selectedIds').val();
        var refNoList = $('#selectRefNo').val();
        var assignUser = $('#assignUserBulkReassign').val();
        var claimNoAndJobNoList = $('#jobNoListTxt').val();

        $.ajax({
            url: contextPath + "/ClaimAssignUserReassignController/updateJobBulkAssignUser?selectIds=" + selectIds + "&assignUser=" + assignUser,
            type: 'POST',
            success: function (result) {
                var obj = JSON.parse(result);

                if (obj == "SUCCESS") {
                    notify("Successfully Update User", "success");
                    closeModal();
                } else {
                    notify("Fail to Update User", "danger");
                }
                search();
            }
        });

    }

    function selectAllCheckBoxs() {
        if (document.getElementById('selectAll').checked) {
            $('.checkBtn').prop('checked', true);
        } else {
            $('.checkBtn').prop('checked', false);
        }
        enableReassignBtn();

    }

    function getSelectedIds(checkBoxElements, ClaimNoAndJobNoBoxElements, refNoElements) {
        var selectedIds = "";
        var selectClaimNoAndJobNo = "";
        var selectRefNo = "";
        loadEngineersLevelWise($('#txtInspectionType').val(), $('#txtV_status').val());
        for (var i = 0; i < checkBoxElements.length; i++) {
            var id = checkBoxElements[i].getAttribute('id');
            var claimNoAndJobNo = ClaimNoAndJobNoBoxElements[i].getAttribute('id');
            var refNo = refNoElements[i].getAttribute('id');

            if (document.getElementById(id).checked) {
                var valId = id;
                var valClaimNoAndJobNo = claimNoAndJobNo;
                var valRefNo = refNo;
                selectedIds += valId + ",";
                selectClaimNoAndJobNo += valClaimNoAndJobNo + ",";
                selectRefNo += valRefNo + ",";
            }
        }
        $('#selectedIds').val(selectedIds);
        $('#selectClaimNoAndJobNo').val(selectClaimNoAndJobNo);
        $('#selectRefNo').val(selectRefNo);
        if (selectedIds != '') {
            setIdArray(selectClaimNoAndJobNo);
        } else {
            notify('Please Select One', "danger");
        }
    }

    function setIdArray(selectClaimNoAndJobNo) {
        $.ajax({
            type: 'POST',
            url: contextPath + "/ClaimAssignUserReassignController/selectClaimNoAndJobNo",
            data: {
                "selectClaimNoAndJobNo": selectClaimNoAndJobNo
            },
            success: function (data) {
                var obj = JSON.parse(data);
                if (null != obj) {
                    // alert(obj)

                    $("#BulReassignpanelUser").modal({
                        backdrop: 'static',
                        keyboard: false
                    });

                    var userArr = obj;
                    var selOpts = "";
                    var selectJobNos = "";
                    for (i = 0; i < userArr.length; i++) {
                        var val = userArr[i];
                        selOpts += "<label class=\"col-sm-3 col-form-label\">" + val + "</label>"
                        selectJobNos += val;
                    }

                    $('#jobNoListDiv').empty();
                    $('#jobNoListDiv').append(selOpts);
                    // var userType = $('#liabilityType').val();

                    var alreadyAssignUser = $('#jobNoListTxt').val();
                    alreadyAssignUser = alreadyAssignUser + "" + selectJobNos + ",";
                    $('#jobNoListTxt').val(alreadyAssignUser);
                }
            },
            error: function (response) {
                swal(response.responseText);
            }

        });
    }

    function closeModal() {
        $('#BulReassignpanelUser').modal('hide');
        $('#panelUser').modal('hide');
        showMake("");
    }

    function showMake(Vehiclemake) {
        $('#liabilityAssignUser').val(Vehiclemake);
        if (Vehiclemake != "") {
            document.getElementById("addClaimUserbtn").disabled = false;
            document.getElementById("addBulkClaimUserbtn").disabled = false;
        } else {
            document.getElementById("addClaimUserbtn").disabled = true;
            document.getElementById("addBulkClaimUserbtn").disabled = true;
        }
        // $('#addClaimUserbtn').disable = false;
    };

    function showMake1() {
        $("#assignUserBulkReassign option").remove();
        $("#assignUserBulkReassign").append('<option value="" >Please Select</option>');
        $.ajax({
            url: contextPath + "/ClaimAssignUserReassignController/getUserList",
            type: 'POST',
            success: function (result) {
                var userArr = JSON.parse(result);
                var selOpts = "";
                for (i = 0; i < userArr.length; i++) {
                    var val = userArr[i];
                    selOpts += "<option value='" + val + "'>" + val + "</option>";
                }
                $('#assignUserBulkReassign').append(selOpts).trigger("chosen:updated");
            }
        });
    };

    $("#submitBtn").on("click", function () {
        $("#submitBtn").attr("data-dismiss", "modal");
        location.reload();
    });

    $(document).ready(function () {
        $("#assignUser").chosen({
            no_results_text: "No results found!",
            width: "100%"
        });
        $("#assignUsers").chosen({
            no_results_text: "No results found!",
            width: "100%"
        });
    });

    function loadEngineers() {
        $.ajax({
            url: contextPath + "/ClaimAssignUserReassignController/getUserList",
            type: 'POST',
            success: function (result) {
                var userArr = JSON.parse(result);
                var selOpts = "";
                for (i = 0; i < userArr.length; i++) {
                    for (j = 0; j < userArr[i].length; j++) {
                        var val = userArr[i][j];
                        selOpts += "<option value='" + val + "'>" + val + "</option>";
                    }
                }
                $('#assignUser').append(selOpts).trigger("chosen:updated");
            }
        });
    }

    function loadEngineersLevelWise(inspectionId, statusId) {
        var userId = $("#userId").val();

        // Clear existing options
        $('#assignUsers').empty();
        $('#assignUserBulkReassign').empty();

        // Add only one option: userId selected
        var option = "<option value='" + userId + "' selected>" + userId + "</option>";
        $('#assignUsers').append(option).trigger("chosen:updated");
        $('#assignUserBulkReassign').append(option).trigger("chosen:updated");
        document.getElementById("addClaimUserbtn").disabled = false;
        document.getElementById("addBulkClaimUserbtn").disabled = false;
    }

</script>
</html>
