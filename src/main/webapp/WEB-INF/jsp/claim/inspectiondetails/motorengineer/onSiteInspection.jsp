
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="">
    <c:if test="${motorEngineerDto.inspectionDetailsDto.isVehicleAvailable ne 'N'}">
        <fieldset class="border p-2 my-2">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Offer Type
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary">
                        ${DbRecordCommonFunctionBean.getValue("claim_offer_type", "V_OFFER_TYPE_DESC", "N_OFFER_TYPE_ID", motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.offerType)}
                </span>
                    <select name="onSiteInspectionDetailsDto.offerType" class="form-control form-control-sm offerType"
                            id="offerType">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_offer_type WHERE N_OFFER_TYPE_ID NOT IN (2,6)", "N_OFFER_TYPE_ID", "V_OFFER_TYPE_DESC")}
                    </select>
                    <script>document.getElementById("offerType").value = "${motorEngineerDto.onSiteInspectionDetailsDto.offerType}";</script>
                </div>
            </div>
        </fieldset>
        <fieldset class="border p-2 my-2">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Cost of Parts * (Rs.)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary "><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.costPart}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.costPart}"
                           placeholder="Cost of Parts" class="form-control form-control-sm cospart text-right"
                           id="costPart"
                           name="onSiteInspectionDetailsDto.costPart">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Cost of Labour (Rs.)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.costLabour}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.costLabour}"
                           placeholder="Cost of Labour" class="form-control form-control-sm costlabour text-right"
                           id="costLabour"
                           name="onSiteInspectionDetailsDto.costLabour">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Excess (Rs.)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                    <fmt:formatNumber var="policyDtoExcessLbl"
                                      value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                      pattern="###,##0.00;(###,##0.00)" type="number"/>
                    <span class="label_Value input-view text-primary">${policyDtoExcessLbl}</span>
                    <fmt:formatNumber var="policyDtoExcess"
                                      value="${motorEngineerDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                      pattern="###,##0.00;(###,##0.00)" type="number"/>
                    <input readonly value="${motorEngineerDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                           placeholder="Excess" class="form-control form-control-sm text-right"
                           id="excess" name="onSiteInspectionDetailsDto.excess">
                </div>
            </div>

            <div class="form-group row ">
                <label class="col-sm-4 col-form-label text-danger font-weight-bold">Total Approve ACR (Rs.) :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary"></span>
                    <span
                            class="label_Value input-view text-danger font-weight-bold"><fmt:formatNumber
                            value="${motorEngineerDto.totalApproveAcrAmount}"
                            pattern="###,##0.00;"
                            type="number"/></span>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-4 col-form-label">ACR (Rs.) *
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                <span
                        class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.acr}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.acr}" placeholder="ACR"
                           class="form-control form-control-sm acr text-right" id="acr"
                           name="onSiteInspectionDetailsDto.acr"
                           autocomplete="off">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Request ARI:</label>
                <div class="col-sm-8">
                    <div class="row">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label  check-container">
                            <input name="onSiteInspectionDetailsDto.requestAri" type="radio" class="align-middle"
                                   value="Yes" ${motorEngineerDto.onSiteInspectionDetailsDto.requestAri eq 'Yes' and not isEmptyValue ? 'checked' : ''} />
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                            <input name="onSiteInspectionDetailsDto.requestAri" type="radio" class="align-middle"
                                   value="No" ${motorEngineerDto.onSiteInspectionDetailsDto.requestAri eq 'No' and not isEmptyValue ? 'checked' : ''} />
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="border p-2 mt-2">
            <h6> Penalty</h6>
            <hr class="my-1">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Bald Tyre Penalty<span
                        class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.boldTyrePenalty eq 'Yes' ? 'Yes' : 'No'}</span>
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.onSiteInspectionDetailsDto.boldTyrePenalty eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                    name="onSiteInspectionDetailsDto.boldTyrePenalty" type="radio"
                                    class="align-middle boldtyrepenalty " onclick="enableDisableBoldTyrePenalty('Y');"
                                    value="Yes"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.onSiteInspectionDetailsDto.boldTyrePenalty eq 'No' and not isEmptyValue ? 'checked' : ''}
                                    name="onSiteInspectionDetailsDto.boldTyrePenalty" type="radio"
                                    class="align-middle boldtyrepenalty" onclick="enableDisableBoldTyrePenalty('N');"
                                    value="No"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Penalty Percentage
                    (%)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.boldPercent}</span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.boldPercent}"
                           placeholder="Penalty Percentage" class="form-control form-control-sm boldpercent"
                           id="boldPercent" name="onSiteInspectionDetailsDto.boldPercent">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Penalty Amount (Rs.)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.boldTirePenaltyAmount}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.boldTirePenaltyAmount}"
                           placeholder="Penalty Amount"
                           class="form-control form-control-sm boldtirepenaltyamount text-right "
                           id="boldTirePenaltyAmount" name="onSiteInspectionDetailsDto.boldTirePenaltyAmount">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Under Insurance Penalty<span
                        class="text-danger font-weight-bold">  *</span>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.underInsuradPenalty eq 'Yes' ? 'Yes' : 'No'}</span>
                    <div class="row">
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.onSiteInspectionDetailsDto.underInsuradPenalty eq 'Yes' and not isEmptyValue ? 'checked' : ''}
                                    name="onSiteInspectionDetailsDto.underInsuradPenalty" value="Yes" type="radio"
                                    class="align-middle underinsuradpenalty"
                                    onclick="enableDisableUnderInsuradPenalty('Y');"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">Yes</span>
                        </label>
                        <label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                            <input ${motorEngineerDto.onSiteInspectionDetailsDto.underInsuradPenalty eq 'No' and not isEmptyValue ? 'checked' : ''}
                                    name="onSiteInspectionDetailsDto.underInsuradPenalty" value="No" type="radio"
                                    class="align-middle underinsuradpenalty"
                                    onclick="enableDisableUnderInsuradPenalty('N');"/>
                            <span class="radiomark"></span>
                            <span class="custom-control-description">No</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Penalty Percentage
                    (%)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.underPenaltyPercent}</span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.underPenaltyPercent}"
                           placeholder="Penalty Percentage" class="form-control form-control-sm underpenaltypercent "
                           id="underPenaltyPercent" name="onSiteInspectionDetailsDto.underPenaltyPercent">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Penalty Amount (Rs.)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"><fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.underPenaltyAmount}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.underPenaltyAmount}"
                           placeholder="Penalty Amount"
                           class="form-control form-control-sm underpenaltyamount text-right"
                           id="underPenaltyAmount" name="onSiteInspectionDetailsDto.underPenaltyAmount">
                </div>
            </div>
        </fieldset>
        <fieldset class="border p-2 mt-2">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Payable Amount (Rs.)
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                <span class="label_Value input-view text-primary"> <fmt:formatNumber
                        value="${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.payableAmount}"
                        pattern="###,##0.00;"
                        type="number"/></span>
                    <input value="${isEmptyValue? '':motorEngineerDto.onSiteInspectionDetailsDto.payableAmount}"
                           placeholder="Payable Amount" class="form-control form-control-sm payableamount text-right"
                           id="payableAmount" name="onSiteInspectionDetailsDto.payableAmount">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Special Remarks
                        <%--<span class="text-danger font-weight-bold"> *</span> --%>
                    :</label>
                <div class="col-sm-8 input-group">
                    <span class="label_Value input-view text-primary">${motorEngineerDto.inspectionDetailsDto.onSiteInspectionDetailsDto.specialRemark}</span>
                    <textarea name="onSiteInspectionDetailsDto.specialRemark" class="form-control form-control-sm "
                              id="specialRemark">${motorEngineerDto.onSiteInspectionDetailsDto.specialRemark}</textarea>
                </div>
            </div>
                <%--            <div class="mt-3">--%>
                <%--                <c:if test="${ (G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && ) || G_USER.accessUserType eq 1 || G_USER.accessUserType eq 23 || G_USER.accessUserType eq 24}">--%>
                <%--                    <div class="float-right">--%>
                <%--                        <button type="submit" name="cmdReject" id="cmdAuth"--%>
                <%--                                value="Authorize"--%>
                <%--                                class="btn btn-primary"  ${(motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10))  ? 'disabled' : ''}>--%>
                <%--                            Authorize--%>
                <%--                        </button>--%>
                <%--                    </div>--%>
                <%--                </c:if>--%>
                <%--            </div>--%>

            <div class="mt-6">

                <c:if test="${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && (motorEngineerDto.inspectionDetailsDto.recordStatus eq 80 || motorEngineerDto.inspectionDetailsDto.recordStatus eq 14))  || (G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser && motorEngineerDto.inspectionDetailsDto.recordStatus ne 80) || G_USER.accessUserType eq 1 || G_USER.accessUserType eq 23 || G_USER.accessUserType eq 24}">

                    <div style="display: none" class="float-right" id="forwardDiv">
                        <button type="submit" name="cmdReject" id="forward"
                                value="forward"
                                class="btn btn-primary" ${(motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 80 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14)) ? 'disabled' : ''}>
                            Forward
                        </button>
                    </div>

                    <div class="float-right">
                        <c:if test="${(G_USER.userId eq motorEngineerDto.inspectionDetailsDto.approveAssignRteUser && motorEngineerDto.inspectionDetailsDto.recordStatus eq 80)}">
                            <button id="btnReturn" value="return"
                                    class=" btn btn-danger">
                                Return
                            </button>
                        </c:if>
                        <button type="submit" name="cmdReject" id="cmdAuth"
                                value="Authorize"
                                class="btn btn-primary"  ${motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 14) ? 'disabled' : ''}>
                            Authorize
                        </button>
                    </div>
                </c:if>
            </div>


        </fieldset>
    </c:if>
    <fieldset class="border p-2 mt-2 my-2">
        <jsp:include page="../motorengineer/assessorPayment.jsp"/>
    </fieldset>
</div>

<script>
    $(document).ready(function () {
        if ('${motorEngineerDto.onSiteInspectionDetailsDto.provideOffer}' == 'No' && ('${motorEngineerDto.inspectionDetailsDto.recordStatus}' == '10' || '${motorEngineerDto.inspectionDetailsDto.recordStatus}' == '14')) {
            $("#offerType option[value='1']").remove();
        }
    });

    function enableDisableBoldTyrePenalty(stat) {
        if (stat == "Y") {
            try {
                $(".boldtirepenaltyamount").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
                $(".boldpercent").removeClass("text-mute").prop('disabled', false).trigger("chosen:updated");
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.boldTirePenaltyAmount', true);
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.boldPercent', true);

            } catch (e) {
                console.error(e);
            }
            calculateBaldTyrePenaltyAmount();
            calculatePayableAmount();
        } else {
            try {
                $('#boldTirePenaltyAmount').val('');
                $('#boldPercent').val('');
            } catch (e) {

            }
            calculatePayableAmount();
            try {
                $(".boldpercent").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
                $(".boldtirepenaltyamount").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.boldTirePenaltyAmount', false);
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.boldPercent', false);
            } catch (e) {

            }

        }
    }

    function enableDisableUnderInsuradPenalty(stat) {
        if (stat == "Y") {
            try {
                $(".underpenaltyamount").removeClass("text-mute").prop('disabled', false);
                $(".underpenaltypercent").removeClass("text-mute").prop('disabled', false);
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.underPenaltyPercent', true);
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.underPenaltyAmount', true);
            } catch (e) {

            }
            calculateUnderInsurancePenaltyAmount();
            calculatePayableAmount();
        } else {
            $('#underPenaltyAmount').val('');
            $('#underPenaltyPercent').val('');
            calculatePayableAmount();
            try {
                $(".underpenaltyamount").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
                $(".underpenaltypercent").addClass("text-mute").prop('disabled', true).trigger("chosen:updated");
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.underPenaltyAmount', false);
                $('#frmMain').data('formValidation').enableFieldValidators('onSiteInspectionDetailsDto.underPenaltyPercent', false);
            } catch (e) {
                console.error(e);
            }


        }
    }

    $(document).ready(function () {
        try {
            if ("No" == $("input[name='onSiteInspectionDetailsDto.underInsuradPenalty']:checked").val()) {
                enableDisableUnderInsuradPenalty('N');
            }
            if ("No" == $("input[name='onSiteInspectionDetailsDto.boldTyrePenalty']:checked").val()) {
                enableDisableBoldTyrePenalty('N');
            }
        } catch (e) {
            console.error(e);
        }
    });

    $("#acr").change(function () {
        calculatePayableAmount();
        calculateBaldTyrePenaltyAmount();
        calculateUnderInsurancePenaltyAmount();
    });


    $("#costPart,#costLabour").change(function () {
        var dataObj = {
            costPart: $("#costPart").val(),
            costLabour: $("#costLabour").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=ACR";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#acr").val(result);
                isForward();
                $('#frmMain').formValidation('revalidateField', 'onSiteInspectionDetailsDto.acr');
                calculateUnderInsurancePenaltyAmount();
                calculateBaldTyrePenaltyAmount();
                calculatePayableAmount();
            }
        });

    });

    $("#boldPercent").change(function () {
        calculateBaldTyrePenaltyAmount();
    });

    $("#underPenaltyPercent").change(function () {
        calculateUnderInsurancePenaltyAmount();
    });

    function calculateBaldTyrePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            boldPercent: $("#boldPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=BaldTyrePenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#boldTirePenaltyAmount").val(result);
                calculatePayableAmount();
            }
        });
    }

    function calculateUnderInsurancePenaltyAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            underPenaltyPercent: $("#underPenaltyPercent").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=UnderPenaltyAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underPenaltyAmount").val(result);
                calculatePayableAmount();
            }
        });
    }

    function calculatePayableAmount() {
        var dataObj = {
            acr: $("#acr").val(),
            excess: $("#excess").val(),
            underPenaltyAmount: $("#underPenaltyAmount").val(),
            boldTirePenaltyAmount: $("#boldTirePenaltyAmount").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateOnsiteInspectionValues?CAL_TYPE=PayableAmount";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#payableAmount").val(result);
            }
        });
    }

    function calculateUnderInsPenaltyPerc() {
        var dataObj = {
            pav: $("#pav").val(),
            sumInsured: $("#sumInsuredVal").val()
        };

        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/calculateUnderInsPenaltyPerc";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                $("#underPenaltyPercent").val(parseFloat(result.trim()) < 0 ? '0.00' : result.trim());
                calculateUnderInsurancePenaltyAmount();
            }
        });
    }

    $("#pav,input[name='onSiteInspectionDetailsDto.underInsuradPenalty']").change(function () {
        if ("Yes" == $("input[name='onSiteInspectionDetailsDto.underInsuradPenalty']:checked").val()) {
            calculateUnderInsPenaltyPerc();
        }
    });

    function calculateBoldTyerPenaltyPerc() {
        var wheelCount = 0;
        var boldWheelCount = 0;
        var boldTyrePercentage = "0.00";
        for (var i = 0; i < 7; i++) {
            var val = $("#cot_0_" + i).val();
            if ("N/A" != val) {
                wheelCount++;
            }
            if ("Bald" == val) {
                boldWheelCount++;
            }
        }
        switch (wheelCount) {
            case 2:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "20.00";
                        break;
                    case 2:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 3:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "10.00";
                        break;
                    case 2:
                        boldTyrePercentage = "30.00";
                        break;
                    case 3:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 4:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "20.00";
                        break;
                    case 3:
                        boldTyrePercentage = "30.00";
                        break;
                    case 4:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
            case 6:
                switch (boldWheelCount) {
                    case 1:
                        boldTyrePercentage = "0.00";
                        break;
                    case 2:
                        boldTyrePercentage = "10.00";
                        break;
                    case 3:
                        boldTyrePercentage = "20.00";
                        break;
                    case 4:
                        boldTyrePercentage = "30.00";
                        break;
                    case 5:
                        boldTyrePercentage = "40.00";
                        break;
                    case 6:
                        boldTyrePercentage = "50.00";
                        break;
                }
                break;
        }
        $("#boldPercent").val(parseFloat(boldTyrePercentage) < 0 ? '0.00' : boldTyrePercentage);
        calculateBaldTyrePenaltyAmount();
    }

    $(".cot_0,input[name='onSiteInspectionDetailsDto.boldTyrePenalty']").change(function () {
        if ("Yes" == $("input[name='onSiteInspectionDetailsDto.boldTyrePenalty']:checked").val()) {
            calculateBoldTyerPenaltyPerc();
        }
    });


</script>
