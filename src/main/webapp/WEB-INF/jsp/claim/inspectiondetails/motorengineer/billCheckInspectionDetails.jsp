<%@ page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %><%--
  Created by IntelliJ IDEA.
  User: Asiri
  Date: 3/13/2018
  Time: 4:34 PM
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<%
    String spCoodList = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst", "userId", "userId", "accessUserType=27", "");

    String scrList = DbRecordCommonFunction.getInstance().
            getPopupList("usr_mst", "userId", "userId", "accessUserType=28", "");
%>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">

    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">

    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/inspectiondetails-form-validations.js?v1"></script>

    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>

    <!-- Generic page styles -->
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>

    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>

    <c:set var="claimHandlerDto" value="${sessionClaimHandlerDto}" scope="session"/>
    <script>

        let isAriRequested = false;
        showLoader();
        $(document).ready(function () {
            loadImageUploadViewer();
            loadDocumentUploadView();
            loadBillUploadView();
            loadEngineeringDocuments();
            logdetails();
            loadPaymentOptionPage();
            loadSuppyOrderView();
            loadEFileUploadView();
            loadPreviousClaims();
            loadClaimSummary();
            loadAdvanceAmountPage();
            loadAriRequestPage();
            loadCustomerDetail();
        });

        function loadButtons() {
            let btnRevoke = false;
            $('#btnRequestNCalsheetReturn').hide();
            $('#btnRequestNDOReturn').hide();
            $('#btnRequest').hide();
            $('#btnRevoke').hide();
            if (${(claimCalculationSheetMainDto.status==61 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId)
                    or (claimCalculationSheetMainDto.status==59 and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId)}) {
                $('#btnRequestNCalsheetReturn').show();
                $('#btnRequest').show();
                $('#btnRequestNDOReturn').hide();
                btnRevoke = true;
            } else if (${((supplyOrderSummaryDto.supplyOrderStatus=='SCRUTINIZING-F' and G_USER.userId eq supplyOrderSummaryDto.apprvAssignScrutinizingUserId)
         or (supplyOrderSummaryDto.supplyOrderRefNo==0 and claimHandlerDto.supplyOrderAssignUser==G_USER.userId and claimHandlerDto.supplyOrderAssignStatus =='Y')
         or (supplyOrderSummaryDto.supplyOrderStatus=='P' and supplyOrderSummaryDto.supplyOrderRefNo>0 and claimHandlerDto.supplyOrderAssignUser==G_USER.userId))}) {
                $('#btnRequestNDOReturn').show();
                $('#btnRequestNCalsheetReturn').hide();
                $('#btnRequest').show();
                btnRevoke = true;
            }
            if (btnRevoke && isAriRequested) {
                $('#btnRevoke').show();
            } else {
                $('#btnRevoke').hide();
            }
        }

        function loadPaymentOptionPage() {
            $("#col_paymentoption").load(contextPath + "/CalculationSheetController/loadPaymentOptionPage?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&DOC_UPLOAD=${docUpload}");
            $("#col_RefPaymentoption").load(contextPath + "/ReferenceTwoCalculationSheetController/loadPaymentOptionPage?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&DOC_UPLOAD=${docUpload}");
        }

        function loadSuppyOrderView() {
            var refNo = '${DO_REF_NO}';
            if (0 == refNo) {
                $("#claimSupplyOrderContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSupplyOrder?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&NOTIFY=true&DOC_UPLOAD=${docUpload}");
            } else {
                $("#claimSupplyOrderContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSupplyOrder?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&N_REF_NO=" + refNo + "&NOTIFY=true&DOC_UPLOAD=${docUpload}");
            }
        }

        function logdetails() {
            $("#col_logdetails").load("${pageContext.request.contextPath}/ClaimHandlerController/viewLogTrail?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadImageUploadViewer() {
            $("#imageUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/viewBillImage?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
        }

        function loadDocumentUploadView() {
            $("#documentUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/viewDocumentUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadEFileUploadView() {
            $("#eFileDocumentUploadContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/documentUpload?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadBillUploadView() {
            $("#billUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/viewBillUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadEngineeringDocuments() {
            $("#engDocUploadContainer").load("${pageContext.request.contextPath}/MotorEngineerController/viewEngDocUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadPreviousClaims() {
            //col_previous_claims
            $("#col_previous_claims").load("${pageContext.request.contextPath}/ClaimHandlerController/viewPreviousClaims?VEHICLE_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadClaimSummary() {
            $("#col_claim_summary").load("${pageContext.request.contextPath}/ClaimHandlerController/viewClaimSummary?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&POL_STATUS=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.polStatus}");
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        }

        function loadAriRequestPage() {
            $('#col_ari_request').load(contextPath + "/RequestAriController/loadAriPage?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}", function () {
                loadButtons();
            });
        }

        function loadCustomerDetail() {
            if (${G_USER.accessUserType eq 25 or G_USER.accessUserType eq 104}) {
                $('#col_cus_dtl').load(contextPath + "/RequestAriController/loadCustomerDetail?N_CLAIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
            }
        }

        function loadAdvanceAmountPage(claimNo) {
            $("#col_advanceAmt").load(contextPath + "/ClaimHandlerController/loadAdvanceAmountPage?claimNo=" + claimNo);
        }

        $(document).ready(function () {
            loadSpecialRemarks();

        });

        function resizeIframe(obj) {
            obj.style.height = obj.contentWindow.document.body.scrollHeight + 'px';
        }

        var documentUploadIds = [];
        var documentBillUploadIds = [];


        function loadSpecialRemarks() {
            $("#specialRemarks").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSpecialRemark?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}");
        }

        function billCheckCompleted() {
            bootbox.confirm({
                message: "Are you sure, you want to forward and completed this bill check?",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    }

                },
                callback: function (result) {
                    if (result == true) {
                        var form = document.getElementById("frmForm");
                        form.action = "${pageContext.request.contextPath}/MotorEngineerController/updateBillCheckComplete";
                        form.submit();
                    }
                }
            });


        }

        function returnToClaimHandler() {
            $('#claimNo').val($('#P_N_CLIM_NO').val());
            var URL = "";

            $.ajax({
                url: contextPath + "/ClaimHandlerController/getClaimStatus?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}" ,
                type: 'GET',
                success: function (result) {
                    var messageType = JSON.parse(result);
                    var message = "";
                    if (messageType == "83") {
                        message = "Return failed! Claim file is already forwarded to Engineer";
                        notify(message, "danger");
                        return;
                    }else if(messageType == "FAIL") {
                        notify("System Error", "danger");
                    }else{

                        if (${claimCalculationSheetMainDto.status==61 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId}) {
                            URL = "${pageContext.request.contextPath}/MotorEngineerController/returnToClaimHandlerBySpecialTeam";
                        } else if (${claimCalculationSheetMainDto.status==59 and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId}) {
                            URL = "${pageContext.request.contextPath}/MotorEngineerController/returnToClaimHandlerBySpc";
                        } else {
                            notify("System Error", "danger");
                            return;
                        }
                        bootbox.confirm({
                            message: "Are you sure you want to return this Page?",
                            buttons: {
                                confirm: {
                                    label: 'Yes',
                                    className: 'btn-primary'
                                },
                                cancel: {
                                    label: 'No',
                                    className: 'btn-secondary float-right'
                                }

                            },
                            callback: function (result) {
                                if (result == true) {
                                    $('#btnFwdEngineer').hide();
                                    $('#btnCalsheetReturnToClaimHandler').hide();
                                    $('#btnReturnToSpCoord').hide();
                                    $('#fwdComplete').hide();
                                    $.ajax({
                                        type: "POST",
                                        url: URL, // Replace URL with the actual URL you want to send the form data to
                                        data: $('#frmForm').serialize(), // Serialize the form data to be sent in the request
                                        success: function() {
                                            notify("Successfully Returned", "success");
                                        },
                                        error: function() {
                                            notify("System Error", "danger");
                                        }
                                    });
                                }
                            }
                        });
                    }
                }
            });
        }

        function returnToSparePartCoord() {
            $.ajax({
                url: contextPath + "/ClaimHandlerController/getClaimStatus?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}" ,
                type: 'GET',
                success: function (result) {
                    var messageType = JSON.parse(result);
                    var message = "";
                    if (messageType == "83") {
                        message = "Return failed! Claim file is already forwarded to Engineer";
                        notify(message, "danger");
                        return;
                    }else if(messageType == "FAIL") {
                        notify("System Error", "danger");
                    }else{
                        bootbox.confirm({
                            message: "Are you sure you want to return this Page?",
                            buttons: {
                                confirm: {
                                    label: 'Yes',
                                    className: 'btn-primary'
                                },
                                cancel: {
                                    label: 'No',
                                    className: 'btn-secondary float-right'
                                }

                            },
                            callback: function (result) {
                                if (result == true) {
                                    $('#btnFwdEngineer').hide();
                                    $('#btnCalsheetReturnToClaimHandler').hide();
                                    $('#btnReturnToSpCoord').hide();
                                    $('#fwdComplete').hide();
                                    $.ajax({
                                        type: "POST",
                                        url: "${pageContext.request.contextPath}/MotorEngineerController/returnToSparePartCoordinatorByScrutinizingTeam", // Replace URL with the actual URL you want to send the form data to
                                        data: $('#frmForm').serialize(), // Serialize the form data to be sent in the request
                                        success: function() {
                                            notify("Successfully saved", "success");
                                        },
                                        error: function() {
                                            notify("System Error", "danger");
                                        }
                                    });
                                }
                            }
                        });
                    }
                }
            });
        }

        function returnDOToClaimHandler() {
            $('#claimNo').val($('#P_N_CLIM_NO').val());
            $('#supplyOrderRefNo').val(${supplyOrderSummaryDto.supplyOrderRefNo});
            $.ajax({
                url: contextPath + "/ClaimHandlerController/getClaimStatus?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}" ,
                type: 'GET',
                success: function (result) {
                    var messageType = JSON.parse(result);
                    var message = "";
                    if (messageType == "83") {
                        message = "Return failed! Claim file is already forwarded to Engineer";
                        notify(message, "danger");
                        return;
                    }else if(messageType == "FAIL") {
                        notify("System Error", "danger");
                    }else{
                        bootbox.confirm({
                            message: "Are you sure you want to return this Page?",
                            buttons: {
                                confirm: {
                                    label: 'Yes',
                                    className: 'btn-primary'
                                },
                                cancel: {
                                    label: 'No',
                                    className: 'btn-secondary float-right'
                                }

                            },
                            callback: function (result) {
                                if (result == true) {
                                    $.ajax({
                                        type: "POST",
                                        url: "${pageContext.request.contextPath}/MotorEngineerController/returnToClaimHandler", // Replace URL with the actual URL you want to send the form data to
                                        data: $('#frmForm').serialize(), // Serialize the form data to be sent in the request
                                        success: function() {
                                            notify("Successfully Returned", "success");
                                        },
                                        error: function() {
                                            notify("System Error", "danger");
                                        }
                                    });
                                }
                            }
                        });
                    }
                }
            });
        }


    </script>
</head>
<c:choose>
    <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
        <c:set var="motorEngineerDto" value="${motorEngineerDto}" scope="session"/>
    </c:when>
    <c:when test="${PREVIOUS_INSPECTION=='Y' }">
        <c:set var="motorEngineerDto" value="${motorEngineerPreviousDto}" scope="request"/>
    </c:when>
</c:choose>
<body class="scroll" onload="hideLoader()">
<form name="frmForm" id="frmForm">
    <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"
           value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}"/>
    <input name="P_CAL_SHEET_NO" id="P_CAL_SHEET_NO" type="hidden" value="${calculationSheetNo}"/>
    <input name="TYPE" id="TYPE" type="hidden" value="${TYPE}"/>
    <input type="hidden" name="claimNo" id="claimNo"
           value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}">
    <input type="hidden" name="V_REASON" id="V_REASON"/>
    <input type="hidden" name="V_REMARK" id="V_REMARK"/>
    <input type="hidden" name="supplyOrderRefNo" id="supplyOrderRefNo"/>
</form>
<div class="container-fluid">
    <div class="row header-bg bg-dark">
        <div class="col-sm-12 py-2" >
            <c:choose>

                <c:when test="${TYPE == 5}">
                    <h6 class="float-left text-dark hide-for-small"> Inspection Report
                        Details-Bill Checking</h6>
                </c:when>

                <c:when test="${TYPE == 6}">
                    <h6 class="float-left text-dark hide-for-small"> Inspection Report
                        Details-Bill Checking</h6>
                </c:when>
                <c:when test="${TYPE == 4}">
                    <h6 class="float-left text-dark hide-for-small"> Inspection Report
                        Details-Spare parts Bill Checking</h6>
                </c:when>
                <c:when test="${TYPE == 7}">
                    <h6 class="float-left text-dark hide-for-small"> Inspection Report
                        Details-Spare parts Bill Checking</h6>
                </c:when>
                <c:when test="${TYPE == 50 || TYPE == 60}">
                    <h6 class="float-left text-dark hide-for-small"> Pending Claim View</h6>
                </c:when>
                <c:when test="${TYPE == 70}">
                    <h6 class="float-left text-dark hide-for-small"> Inspection Report
                        Details-RTE Claim View</h6>
                </c:when>

            </c:choose>

            <h6 class="text-dark float-right">Vehicle No
                : ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}</h6><br>
            <h6 class="text-dark float-right" style="margin-right: -162px">Claim No
                : ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}</h6>
        </div>
<%--        <div class="col-sm-12 py-2" style="--%>
<%--            display: flex;--%>
<%--            flex-direction: row;--%>
<%--            justify-content: space-around;--%>
<%--            align-items: center;--%>
<%--            background: #a7d1d6;--%>
<%--        ">--%>
<%--            <h6 style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                ${(claimHandlerDto.claimsDto.policyDto.product eq "") || (null eq claimHandlerDto.claimsDto.policyDto.product) ? "N/A" : claimHandlerDto.claimsDto.policyDto.product }--%>
<%--            </h6>--%>
<%--            <h6 id="service-factor-header" style="margin-bottom: 0; text-transform: uppercase; color: #ff0000;">--%>
<%--                Service Factors--%>
<%--            </h6>--%>
<%--        </div>--%>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div id="claimStampContainer" class="stamp-container">
                <c:if test="${claimHandlerDto.claimsDto.policyDto.categoryDescription eq 'VIP'}">
                    <img src="${pageContext.request.contextPath}/resources/stamps/vip.png"
                         class="stamp-container-vip"
                    >
                </c:if>
            </div>
            <div class="f1-steps">
                <div class="f1-progress">
                    <div class="f1-progress-line" data-now-value="10" data-number-of-steps="5"
                         style="width: 70%;"></div>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimStatus>=1?"active":""}">
                        1
                    </div>
                    <p>Call Center</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">2</div>
                    <p>Assessor Coordinator</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">3</div>
                    <p>Assessor</p>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon">4</div>
                    <p>Motor Engineer</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon">5</div>
                    <p>Claim Handler</p>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" name="ACTION" value="${ACTION}"/>
    <c:if test="${ACTION eq 'SAVE'}">
        <c:set var="isEmptyValue" value="false" scope="request"/>
    </c:if>
    <c:if test="${ACTION eq 'UPDATE'}">
        <c:set var="isEmptyValue" value="false" scope="request"/>
    </c:if>
    <c:if test="${motorEngineerDto.inspectionDetailsDto.recordStatus eq 0}">
        <c:set var="isEmptyValue" value="true" scope="request"/>
    </c:if>
    <c:if test="${motorEngineerDto.inspectionDto.inspectionId eq 8}">
        <c:set var="isDesktopInspection" value="true" scope="request"/>
    </c:if>
    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 8}">
        <c:set var="isDesktopInspection" value="false" scope="request"/>
    </c:if>
    <div class="row">
        <fieldset class="col-md-6 border scroll" style="height: calc(100vh - 210px);">
            <div id="accordionOne">
                <div class="card mt-3">
                    <div class="card-header p-0" id="heading0">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse0"
                               aria-expanded="true" aria-controls="collapse0">
                                Underwriting Details
                            </a>
                        </h5>
                    </div>
                    <div id="collapse0" class="collapse" aria-labelledby="heading0"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div id="underWritingDetails" class="col-lg-12">
                                    <jsp:include
                                            page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/underwritingDetails.jsp"></jsp:include>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="inspectionDetailsDiv" class="card mt-2">
                    <div class="card-header p-0" id="heading1">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse1"
                               aria-expanded="true" aria-controls="collapse1">
                                Inspection Report
                                Details
                            </a>
                        </h5>
                    </div>
                    <input type="hidden" name="claimNo"
                           value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}">
                    <div id="collapse1" class="collapse" aria-labelledby="heading1"
                         data-parent="#accordionOne">
                        <div class="card-body p-1 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <c:if test="${not isDesktopInspection}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Code of Assessor
                                                    <%--<span class="text-danger font-weight-bold"> *</span> --%>
                                                :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assessorDto.name} &nbsp; ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assessorDto.assessorContactNo}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Job No :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobId}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Customer Name :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="custName">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custName}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Contact Address :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="contactAddress">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine1},</span>
                                            <span class="label_Value input-view"
                                                  id="contactAddress2">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine2},</span>
                                            <span class="label_Value input-view"
                                                  id="contactAddress3">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custAddressLine3}.</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Customer Contact Number :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view"
                                                          id="contactNumber">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.custMobileNo}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Registration No. :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleNumber}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Make :</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleMake}</span>
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.makeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.makeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.makeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.makeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                                <%--<label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">--%>
                                                <%--<input ${motorEngineerDto.makeConfirm eq 'Confirm' and not isEmptyValue ? 'checked' : ''}--%>
                                                <%--name="makeConfirm" type="radio"--%>
                                                <%--class="align-middle" value="Confirm"/>--%>
                                                <%--<span class="radiomark"></span>--%>
                                                <%--<span class="custom-control-description">Correct</span>--%>
                                                <%--</label>--%>
                                                <%--<label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">--%>
                                                <%--<input ${motorEngineerDto.makeConfirm eq 'Wrong' and not isEmptyValue ? 'checked' : ''}--%>
                                                <%--name="makeConfirm" type="radio"--%>
                                                <%--class="align-middle" value="Wrong"/>--%>
                                                <%--<span class="radiomark"></span>--%>
                                                <%--<span class="custom-control-description">Wrong</span>--%>
                                                <%--</label>--%>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Model<span
                                                class="text-danger font-weight-bold">  *</span> :</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.vehicleModel}</span>
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.modelConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.modelConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.modelConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.modelConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Year of Make <span
                                                class="text-danger font-weight-bold">  *</span>:</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.manufactYear}</span>
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.yearMakeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.yearMakeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.yearMakeConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.yearMakeConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Engine No :</label>
                                        <div class="col-sm-8">
                                            <div class="row">
                                                <span class="label_Value m-0 col-6">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.engineNo}</span>

                                                <c:choose>

                                                    <c:when test="${motorEngineerDto.engNoConfirm eq 'Confirm'}">
                                                        <span class="label_Value assessor_value m-0 col-6 ">Confirm</span>
                                                    </c:when>
                                                    <c:when test="${motorEngineerDto.engNoConfirm eq 'Wrong'}">
                                                        <span class="label_Value assessor_value m-0 col-6 ">Wrong</span>
                                                    </c:when>
                                                    <c:when test="${motorEngineerDto.engNoConfirm eq 'Not_Checked'}">
                                                        <span class="label_Value assessor_value m-0 col-6 ">Not Checked</span>
                                                    </c:when>
                                                </c:choose>
                                            </div>
                                            <div class="row">
                                                <span class="label_Value assessor_value m-0 col-6 ${motorEngineerDto.inspectionDetailsDto.engNoConfirm eq 'Confirm' ? 'text-success' : 'text-danger'}">${motorEngineerDto.inspectionDetailsDto.engNoConfirm eq 'Confirm' ? 'Correct' : 'Incorrect'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Chassis No in ISF :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.chassisNo}</span>
                                        </div>
                                    </div>
                                    <c:if test="${not isDesktopInspection}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Chassis No:</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value assessor_value input-view">${motorEngineerDto.inspectionDetailsDto.chassisNo}</span>
                                                    <%--<div class="row">--%>
                                                    <%--<label class="custom-control custom-checkbox mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">--%>
                                                    <%--<input type="checkbox" name="engNoConfirm"--%>
                                                    <%--id="engNoConfirmbox"--%>
                                                    <%--class="align-middle" value="Y"--%>
                                                    <%--onclick="chassis_correct('Y');"/>--%>
                                                    <%--<span class="checkmark"></span>--%>
                                                    <%--<span class="custom-control-description">Incorrect Chassis No.</span>--%>
                                                    <%--</label>--%>
                                                    <%--</div>--%>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <c:if test="${not isDesktopInspection}">
                                            <label class="col-sm-4 col-form-label text-mute correctChaNo">Correct
                                                Chassis No :</label>
                                        </c:if>
                                        <c:if test="${isDesktopInspection}">
                                            <label class="col-sm-4 col-form-label text-mute correctChaNo">Chassis
                                                No :</label>
                                        </c:if>
                                        <div class="col-sm-8">
                                            <span class="label_Value assessor_value input-view">${motorEngineerDto.chassisNo}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Date of Registration
                                            :</label>
                                        <div class="col-sm-8">
                                            <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.registDate}</span>
                                        </div>
                                    </div>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 7
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 6}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Excess (Rs) :</label>
                                            <div class="col-sm-8">
                                                          <span class="label_Value input-view">
                                                              <fmt:formatNumber
                                                                      value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.excess}"
                                                                      pattern="###,##0.00;(###,##0.00)"
                                                                      type="number"/>
                                                          </span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 1
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 2
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 4
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 6
                                                          &&motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">NCB (Rs) :</label>
                                            <div class="col-sm-8">
                                                          <span class="label_Value input-view">
                                                              <fmt:formatNumber
                                                                      value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbAmount}"
                                                                      pattern="#,##0.00;-#,##0.00"
                                                                      type="number"/></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">NCB Percentage :</label>
                                            <div class="col-sm-8">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.ncbRate} %</span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Date of Inspection :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view">${isEmptyValue ? '' : motorEngineerDto.inspectDateTime == '1980-01-01 00:00:00'
                                                            ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime == '1980-01-01 00:00:00'
                                                            ? motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.assignDatetime
                                                            : motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.jobFinishedDatetime:motorEngineerDto.inspectDateTime}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Sum Insured (Rs) :</label>
                                        <div class="col-sm-8">
                                                    <span class="label_Value input-view" id="sumInsuredVal"
                                                          name="sumInsuredVal">
                                                        <fmt:formatNumber
                                                                value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.sumInsured}"
                                                                pattern="###,##0.00;(###,##0.00)"
                                                                type="number"/></span>
                                        </div>
                                    </div>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 6
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">PAV (Rs.) :</label>
                                            <div class="col-sm-8 input-group">
                                                          <span class="label_Value assessor_value input-view"
                                                          ><fmt:formatNumber
                                                                  value="${motorEngineerDto.inspectionDetailsDto.pav}"
                                                                  pattern="###,##0.00;"
                                                                  type="number"/></span>
                                                <span class="label_Value assessor_value input-view text-success"
                                                ><fmt:formatNumber
                                                        value="${isEmptyValue ? not empty PREVIOUS_PAV && motorEngineerDto.inspectionDto.inspectionId eq 8
                                                                                              ? PREVIOUS_PAV : ''
                                                                                              : motorEngineerDto.pav}"
                                                        pattern="###,##0.00;"
                                                        type="number"/></span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Details of Damages<span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8 input-group">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.damageDetails}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.damageDetails}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">PAD :</label>
                                            <div class="col-sm-8  input-group">
                                                <span class="label_Value input-view">${motorEngineerDto.inspectionDetailsDto.pad}</span>
                                                <span class="label_Value input-view text-success">${motorEngineerDto.pad}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                    <c:if test="${motorEngineerDto.inspectionDto.inspectionId ne 5
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 6
                                                          && motorEngineerDto.inspectionDto.inspectionId ne 7}">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Genuineness of the Accident<c:if
                                                    test="${motorEngineerDto.inspectionDto.inspectionId ne 4}">
                                                <span class="text-danger font-weight-bold">  *</span>
                                                <script>
                                                    //TODO - Remove Validation
                                                </script>
                                            </c:if>
                                                :</label>
                                            <div class="col-sm-8">
                                                <div class="row">
                                                              <span class="label_Value assessor_value col-md-12 input-view text-primary m-0 pl-3">
                                                                  <c:choose>
                                                                      <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Consistent'}">
                                                                          Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Non_Consistence'}">
                                                                          Non Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.inspectionDetailsDto.genuineOfAccident eq 'Doubtful'}">
                                                                          Doubtful
                                                                      </c:when>
                                                                  </c:choose>
                                                              </span>
                                                    <span class="label_Value assessor_value col-md-12 input-view text-primary m-0 pl-3">
                                                                  <c:choose>
                                                                      <c:when test="${motorEngineerDto.genuineOfAccident eq 'Consistent'}">
                                                                          Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.genuineOfAccident eq 'Non_Consistence'}">
                                                                          Non Consistent
                                                                      </c:when>
                                                                      <c:when test="${motorEngineerDto.genuineOfAccident eq 'Doubtful'}">
                                                                          Doubtful
                                                                      </c:when>
                                                                  </c:choose>
                                                              </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">1st statement required for
                                                Own Damage Settlement<span
                                                        class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8 input-group">
                                                          <span class="label_Value assessor_value input-view text-primary"
                                                          >${motorEngineerDto.inspectionDetailsDto.firstStatementReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <span class="label_Value assessor_value input-view text-primary"
                                                >${motorEngineerDto.firstStatementReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <div class="row">

                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Reason <span
                                                    class="text-danger font-weight-bold">  *</span> :</label>
                                            <div class="col-sm-8 input-group">
                                                    <%--<span class="label_Value assessor_value input-view text-primary"--%>
                                                    <%-->${DbRecordCommonFunctionBean.getValue("claim_first_statement_reason", "V_REASON", "N_ID", motorEngineerDto.inspectionDetailsDto.firstStatementReqReason)}</span>--%>
                                                <span class="label_Value assessor_value input-view text-primary"
                                                >${motorEngineerDto.firstStatementReqReason}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Investigation Required<span
                                                    class="text-danger font-weight-bold">  *</span>
                                                :</label>
                                            <div class="col-sm-8 input-group">
                                                          <span class="label_Value assessor_value input-view text-primary"
                                                          >${motorEngineerDto.inspectionDetailsDto.investReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                <div class="row">
                                                            <span class="label_Value assessor_value input-view text-primary"
                                                            >${motorEngineerDto.investReq eq 'Yes' ? 'Yes' : 'No'}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">Assessor Remarks:</label>
                                        <div class="col-sm-8">
                                                        <span class="label_Value assessor_value input-view text-primary"
                                                        >${motorEngineerDto.inspectionDetailsDto.assessorSpecialRemark}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-4 col-form-label">RTE Remarks:</label>
                                        <div class="col-sm-8">
                                                         <span class="label_Value assessor_value input-view text-primary"
                                                         >${motorEngineerDto.assessorSpecialRemark}</span>

                                        </div>
                                    </div>
                                    <form name="frmMain" id="frmMain">
                                        <input type="hidden" name="claimNo"
                                               value="${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}"/>
                                        <div class="form-group row">
                                            <c:choose>

                                                <c:when test="${TYPE == 5}">
                                                    <label class="col-sm-4 col-form-label">Bill check team special
                                                        remark:</label>
                                                </c:when>

                                                <c:when test="${TYPE == 6}">
                                                    <label class="col-sm-4 col-form-label">Bill check team special
                                                        remark:</label>
                                                </c:when>
                                                <c:when test="${TYPE == 4}">
                                                    <label class="col-sm-4 col-form-label">Spare parts coordinator
                                                        special remark:</label>
                                                </c:when>
                                                <c:when test="${TYPE == 7}">
                                                    <label class="col-sm-4 col-form-label">Spare parts coordinator
                                                        special remark:</label>
                                                </c:when>

                                            </c:choose>
                                            <div class="col-sm-8">
                                                    <textarea name="remark"
                                                              id="inspectionSpecialRemark"
                                                              class="lstBox form-control form-control-sm skipReadOnly"></textarea>
                                            </div>
                                        </div>
                                        <div class="my-3 clearfix">
                                            <button type="button" name="addRTERemarks"
                                                    id="addRTERemarks"
                                                    value="Reject"
                                                    class="btn btn-primary float-right skipReadOnly">
                                                Add Special Remarks
                                            </button>
                                        </div>
                                    </form>
                                    <div class="my-3 clearfix">
                                        <a href="${pageContext.request.contextPath}/MotorEngineerController/viewClaimHistoryBillCheck?P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}"
                                           class="claimView">
                                            <button type="button" name="cmdViewAccident"
                                                    value="Reject"
                                                    class="btn btn-primary float-right skipReadOnly">
                                                View Accidents
                                            </button>
                                        </a>
                                        <div class="float-right" style="padding-right:10px ">

                                        </div>
                                        <script type="text/javascript">
                                            $('.claimView').popupWindow({
                                                height: screen.height,
                                                width: screen.width,
                                                resizable: 1,
                                                centerScreen: 1,
                                                scrollbars: 1,
                                                windowName: 'swip'
                                            });
                                        </script>
                                    </div>

                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <jsp:include page="/WEB-INF/jsp/claim/inspectiondetails/motorengineer/allInspectionTypeDetails.jsp"/>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading4">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse4"
                               aria-expanded="true" aria-controls="collapse4">
                                Previous Inspection
                            </a>
                        </h5>
                    </div>
                    <div id="collapse4" class="collapse " aria-labelledby="heading4"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2" style="overflow: auto">
                            <div class="row">
                                <div class="col-md-12">
                                    <table width="100%" cellpadding="0" cellspacing="1"
                                           class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                        <thead>
                                        <tr>
                                            <th class="tbl_row_header">Job No</th>
                                            <th class="tbl_row_header">Inspection Type</th>
                                            <th class="tbl_row_header">Assign Assessor</th>
                                            <th class="tbl_row_header">Assign RTE</th>
                                            <th class="tbl_row_header">Forwarded RTE</th>
                                            <th class="tbl_row_header">Approve Date Time</th>
                                            <th class="tbl_row_header">Date of Accident</th>
                                            <th class="tbl_row_header">Status</th>
                                            <th class="tbl_row_header"></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="claim" items="${previousInspectionList}">
                                            <c:forEach var="jobs" items="${claim.list}" varStatus="loop">
                                                <tr>
                                                    <td>${jobs.jobNo}</td>
                                                    <td>${jobs.inspectionType}</td>
                                                    <td>${jobs.assignAssessor}</td>
                                                    <td>${jobs.assignRte}</td>
                                                    <td>${jobs.approveAssignRte}</td>
                                                    <td>${jobs.approveDateTime}</td>
                                                    <td>${jobs.dateOfAccident}</td>
                                                    <td>${jobs.statusDesc}</td>

                                                    <c:if test="${jobs.statusDesc ne 'ASSIGNED'}">
                                                        <td>
                                                            <div>
                                                                <a href="${pageContext.request.contextPath}/MotorEngineerController/viewEditClaimPrevious?P_N_JOB_NO=${jobs.jobRefNo}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&P_POL_N_REF_NO=${jobs.policyRefNo}&DOC_UPLOAD=${docUpload}"
                                                                   class="jobView${loop.index}">
                                                                    <button type="button" name="cmdReject"
                                                                            class="btn btn-primary">
                                                                        <i class="fa fa-eye"></i>
                                                                    </button>
                                                                </a>
                                                            </div>
                                                            <script type="text/javascript">
                                                                $('.jobView${loop.index}').popupWindow({
                                                                    height: screen.height,
                                                                    width: screen.width,
                                                                    resizable: 1,
                                                                    centerScreen: 1,
                                                                    scrollbars: 1,
                                                                    windowName: 'swip${jobs.jobNo}'
                                                                });
                                                            </script>
                                                        </td>
                                                    </c:if>
                                                </tr>
                                            </c:forEach>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-2">
                    <div class="card-header p-0" id="heading14">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse14"
                               aria-expanded="true" aria-controls="collapse14">
                                Document
                            </a>
                        </h5>
                    </div>
                    <div id="collapse14" class="collapse " aria-labelledby="heading14"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                                 id="documentUploadContainer">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card mt-2">
                    <div class="card-header p-0" id="heading15">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse15"
                               aria-expanded="true" aria-controls="collapse15">
                                Finall Bills
                            </a>
                        </h5>
                    </div>
                    <div id="collapse15" class="collapse ${tabIndex==10?'show':''}" aria-labelledby="heading15"
                         data-parent="#accordionOne">
                        <div class="card-body p-2">
                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                 id="billUploadContainer">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="engDocTabHeading">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#engDocTab"
                               aria-expanded="true" aria-controls="collapse15">
                                Engineering Documents
                            </a>
                        </h5>
                    </div>
                    <div id="engDocTab" class="collapse ${tabIndex==11?'show':''}" aria-labelledby="engDocTabHeading"
                         data-parent="#accordionOne">
                        <div class="card-body p-2">
                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                 id="engDocUploadContainer">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading9">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse9"
                               aria-expanded="true" aria-controls="collapse9">
                                Previous Claims Settlement Details
                            </a>
                        </h5>
                    </div>
                    <div id="collapse9" class="collapse " aria-labelledby="heading9"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-md-12">
                                    <table width="100%" cellpadding="0" cellspacing="1"
                                           class="table table-hover table-sm dataTable no-footer dtr-inline ">
                                        <thead>
                                        <tr>
                                            <th class="tbl_row_header">Job No</th>
                                            <th class="tbl_row_header">Inspection Type</th>
                                            <th class="tbl_row_header">Vehicle No</th>
                                            <th class="tbl_row_header">Policy No</th>
                                            <th class="tbl_row_header">Date of Accident</th>
                                            <th class="tbl_row_header"></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="claim" items="${previousClaimList}">
                                            <tr class="bg-dark">
                                                <td colspan="6">
                                                    <b> ${claim.claimNo}</b>
                                                </td>
                                            </tr>
                                            <c:forEach var="jobs" items="${claim.list}">
                                                <tr>
                                                    <td>${jobs.jobNo}</td>
                                                    <td>${jobs.inspectionType}</td>
                                                    <td>${jobs.vehicleNo}</td>
                                                    <td>${jobs.policyNo}</td>
                                                    <td>${jobs.dateOfAccident}</td>
                                                    <td>
                                                        <div>
                                                            <c:if test="${jobs.jobNo ne 'N/A'}">
                                                                <a href="${pageContext.request.contextPath}/MotorEngineerController/viewEditPrevious?P_N_JOB_NO=${jobs.refNo}"
                                                                   class="jobView${loop.index}">
                                                                    <button type="button" name="cmdPrev"
                                                                            class="btn btn-primary">
                                                                        <i class="fa fa-eye"></i>
                                                                    </button>
                                                                </a>
                                                            </c:if>
                                                        </div>
                                                        <script type="text/javascript">
                                                            $('.jobView${loop.index}').popupWindow({
                                                                height: screen.height,
                                                                width: screen.width,
                                                                resizable: 1,
                                                                centerScreen: 1,
                                                                scrollbars: 1,
                                                                windowName: 'swip${jobs.jobNo}'
                                                            });
                                                        </script>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="previous_claims">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_previous_claims"
                               aria-expanded="true" aria-controls="col_previous_claims">
                                Previous Claims
                            </a>
                        </h5>
                    </div>
                    <div id="col_previous_claims" style="overflow: auto" class="collapse "
                         aria-labelledby="previous_claims"
                         data-parent="#accordionOne">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="paymentoption">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_paymentoption"
                               aria-expanded="false" aria-controls="col_paymentoption">
                                Payment Option
                            </a>
                        </h5>
                    </div>
                    <div id="col_paymentoption" class="collapse ${tabIndex==9?'show':''}"
                         aria-labelledby="paymentoption"
                         data-parent="#accordionOne" style="overflow: auto">
                    </div>
                </div>
<%--                <div class="card mt-2">--%>
<%--                    <div class="card-header p-0" id="RefPaymentoption">--%>
<%--                        <h5 class="mb-0">--%>
<%--                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_RefPaymentoption"--%>
<%--                               aria-expanded="false" aria-controls="col_RefPaymentoption">--%>
<%--                                Cover Based Payments--%>
<%--                            </a>--%>
<%--                        </h5>--%>
<%--                    </div>--%>
<%--                    <div id="col_RefPaymentoption" class="collapse  ${tabIndex==15?'show':''}"--%>
<%--                         aria-labelledby="RefPaymentoption"--%>
<%--                         data-parent="#accordion2" style="overflow: auto">--%>
<%--                        <!-- Content for the new div goes here -->--%>
<%--                    </div>--%>
<%--                </div>--%>
                <div class="card mt-2">
                    <div class="card-header p-0" id="claim_summary">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_claim_summary"
                               aria-expanded="true" aria-controls="col_claim_summary">
                                Claim Summary
                            </a>
                        </h5>
                    </div>
                    <div id="col_claim_summary" style="overflow: auto" class="collapse " aria-labelledby="claim_summary"
                         data-parent="#accordionOne">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="ariRequest">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_ari_request"
                               aria-expanded="false" aria-controls="col_ari_request">
                                ARI Request
                            </a>
                        </h5>
                    </div>
                    <div id="col_ari_request" class="collapse"
                         aria-labelledby="ariRequest"
                         data-parent="#accordionOne" style="overflow: auto">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="customerDetail">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_cus_dtl"
                               aria-expanded="false" aria-controls="col_cus_dtl">
                                Customer Detail
                            </a>
                        </h5>
                    </div>
                    <div id="col_cus_dtl" class="collapse ${tabIndex==9?'show':''}"
                         aria-labelledby="customerDetail"
                         data-parent="#accordionOne" style="overflow: auto">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading12">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_logdetails"
                               aria-expanded="true" aria-controls="col_logdetails">
                                Log Details
                            </a>
                        </h5>
                    </div>
                    <div id="col_logdetails" class="collapse" aria-labelledby="logdetails"
                         data-parent="#accordion2">
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading13">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse13"
                               aria-expanded="true" aria-controls="collapse13">
                                Special Remarks
                            </a>
                        </h5>
                    </div>
                    <div id="collapse13" class="collapse " aria-labelledby="heading13"
                         data-parent="#accordionOne">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <div class="w-100 scroll" style="height: calc(100vh - 350px);"
                                                 id="specialRemarks">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="col-md-6 border scroll" style="height: calc(100vh - 210px);">
            <%--<h6> Inspection Details</h6>--%>
            <%--<hr class="my-1">--%>
            <div class="mt-3">
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading6">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse6"
                               aria-expanded="true" aria-controls="collapse6">
                                Vehicle Image
                            </a>
                        </h5>
                    </div>
                    <div id="collapse6" class="collapse " aria-labelledby="heading6"
                         data-parent="#accordion">
                        <div class="card-body p-lg-3 p-2">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div id="imageUploadContainer"></div>

                                    <%--<iframe width="100%" style="height: 100vh;" frameborder="0"
                                            id="iframeImageUpload" name="iframeImageUpload"
                                            height="90vh"
                                            class="scroll"
                                            src="${pageContext.request.contextPath}/MotorEngineerController/imageUpload?PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&P_N_CLIM_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}&JOB_REF_NO=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo}"></iframe>--%>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <c:if test="${G_USER.accessUserType eq 27 || G_USER.accessUserType eq 28}">
                    <div class="card mt-2">
                        <div class="card-header p-0" id="efiledetails">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_efiledetails"
                                   aria-expanded="false" aria-controls="col_efiledetails">
                                    E-File Details
                                </a>
                            </h5>
                        </div>
                        <div id="col_efiledetails" class="collapse"
                             aria-labelledby="efiledetails"
                             data-parent="#accordion2">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div id="eFileDocumentUploadContainer"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:if>
                <c:if test="${((IS_CLAIM_HANDLER_USER or IS_OFFER_TEAM_CLAIM_HANDLER_USER or IS_TOTAL_LOSS_CLAIM_HANDLER_USER) and claimHandlerDto.advanceApprovedUser ne '')
                     or (not ((claimHandlerDto.aprvAdvanceAmount < 0) or (claimHandlerDto.aprvAdvanceAmount > 0)) and claimHandlerDto.advanceApprovalAssignUser eq G_USER.userId)
                     or (claimHandlerDto.advanceApprovedUser eq G_USER.userId)
                     or (claimHandlerDto.advanceForwardedUser eq G_USER.userId)}">
                    <div class="card mt-2">
                        <div class="card-header p-0" id="advanceAmt"
                             onclick="loadAdvanceAmountPage(${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo})">
                            <h5 class="mb-0">
                                <a class="btn btn-link" data-toggle="collapse" data-target="#col_advanceAmt"
                                   aria-expanded="false" aria-controls="col_advanceAmt">
                                    Advance Amount
                                </a>
                            </h5>
                        </div>
                        <div id="col_advanceAmt" class="collapse  ${tabIndex==16?'show':''}"
                             aria-labelledby="advanceAmt"
                             data-parent="#accordion2" style="overflow: auto">
                        </div>
                    </div>
                </c:if>
                <div class="card mt-2">
                    <div class="card-header p-0" id="supplyorder">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#col_supplyorder"
                               aria-expanded="false" aria-controls="col_supplyorder">
                                Supply Order
                            </a>
                        </h5>
                    </div>
                    <div id="col_supplyorder" class="collapse ${tabIndex==6?'show':''}"
                         aria-labelledby="supplyorder"
                         data-parent="#accordion2">
                        <div id="claimSupplyOrderContainer"></div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="col-md-12">
            <div id="accordion3">
                <div class="card mt-2">
                    <div class="card-header p-0" id="heading8">
                        <h5 class="mb-0">
                            <a class="btn btn-link" data-toggle="collapse" data-target="#collapse8"
                               aria-expanded="false" aria-controls="collapse8">
                                Photo Comparison
                            </a>
                        </h5>
                    </div>
                    <div id="collapse8" class="collapse " aria-labelledby="heading8"
                         data-parent="#accordion3">
                        <div class="card-body p-0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <iframe width="50%" style="height: 100vh;" frameborder="0"
                                            id="iframePhotoCom1" name="iframePhotoCom1"
                                            height="90vh"
                                            class="scroll float-left"
                                            src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=1&policyNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                    <iframe width="50%" style="height: 100vh;" frameborder="0"
                                            id="iframePhotoCom2" name="iframePhotoCom2"
                                            height="90vh"
                                            class="scroll float-left"
                                            src="${pageContext.request.contextPath}/PhotoComparisonController/initViewComparisonViewer?vehicleNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.vehicleNo}&comparisionTabNo=2&policyNumber=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.policyDto.policyNumber}"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="float-right my-3">

                    <div id="actionButtonDiv" style="float: left">
                        <c:if test="${(claimCalculationSheetMainDto.status==61 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId)
                        or (claimCalculationSheetMainDto.status==59 and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId)}">
                            <button type="button" id="btnCalsheetReturnToClaimHandler" name="cmdCancel"
                                    onclick="returnToClaimHandler()"
                                    class="btn btn-primary ml-1">
                                Return to Claim handler
                            </button>
                        </c:if>
                        <c:if test="${((supplyOrderSummaryDto.supplyOrderStatus=='SCRUTINIZING-F' and G_USER.userId eq supplyOrderSummaryDto.apprvAssignScrutinizingUserId)
                        or (supplyOrderSummaryDto.supplyOrderRefNo==0 and sessionClaimHandlerDto.supplyOrderAssignUser==G_USER.userId and claimHandlerDto.supplyOrderAssignStatus =='Y')
                        or (supplyOrderSummaryDto.supplyOrderStatus=='P' and supplyOrderSummaryDto.supplyOrderRefNo>0 and sessionClaimHandlerDto.supplyOrderAssignUser==G_USER.userId))}">
                            <button type="button" id="btnDoReturnToClaimHandler" name="cmdCancel"
                                    onclick="returnDOToClaimHandler()"
                                    class="btn btn-primary ml-1">
                                Return to Claim handler
                            </button>
                        </c:if>
                        <c:if test="${claimCalculationSheetMainDto.status==61 && G_USER.accessUserType eq 28 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId}">
                            <button type="button" name="cmdCancel" onclick="returnToSparePartCoord()"
                                    class="btn btn-primary ml-1">
                                Return to Spare Parts Coordinator
                            </button>
                            <c:if test="${!pendingInspection}">
                                <button class="btn btn-success" type="button" id="fwdComplete"
                                        onclick="billCheckCompleted();">Forward &
                                    Completed
                                </button>
                            </c:if>
                        </c:if>
                    </div>
                    <c:if test="${claimHandlerDto.claimStatus != 83}">
                        <button type="button" id="btnFwdEngineer" name="btnFwdRte"
                                class="btn btn-primary ml-1"
                                onclick="selectRte()">Forward to Engineer
                        </button>
                    </c:if>
                    <button type="button" id="btnRecallEngineer" name="btnRecallEngineer"
                            class="btn btn-secondary ml-1"
                            onclick="recallFromEngineer()">Recall From Engineer
                    </button>
                    <button type="button" id="btnReturnByEngineer" name="btnReturnByEngineer"
                            class="btn btn-secondary ml-1"
                            onclick="returnClaimFileByEngineer()">Return Claim File
                    </button>
                    <c:if test="${PREVIOUS_INSPECTION!='Y'}">
                        <button class="btn btn-link" type="button" onclick="goBack()"><b>Back</b></button>
                    </c:if>
                </div>

            </div>
        </fieldset>
    </div>

</div>
<div class="modal fade bd-example-modal-md" role="dialog"
     id="advanceForwardModal" aria-hidden="true"
     style="    background: #333333c2;">
    <div class="modal-dialog modal-md">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"
                    id="txtAdvanceForwardTitle">Forward for Advance Amount Request</h6>
            </div>
            <div class="modal-content p-2">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Forward to :</label>
                    <div class="col-sm-8">
                        <select class="form-control" id="selectUserType"
                                onchange="toggleForwardUsers(); validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                            <option value="1">Spare Parts Coordinator</option>
                            <option value="2">Bill Checking User</option>
                        </select>
                    </div>
                    <label class="col-sm-4 col-form-label mt-2">Select User :</label>
                    <div class="col-sm-8 mt-2">
                        <select class="form-control" id="selectDefault"
                                onchange="validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                        </select>
                        <select class="form-control" id="selectspCood" style="display: none"
                                onchange="validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                            <%out.print(spCoodList);%>
                        </select>
                        <select class="form-control" id="selectScrTeam" style="display: none"
                                onchange="validateAdvanceRequestBtn()">
                            <option value="0">Please Select</option>
                            <%out.print(scrList);%>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-1">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"
                        onclick="">
                    Close
                </button>
                <button type="button" class="btn btn-primary" id="btnRequestForAdvance" onclick="requestForAdvance()"
                        disabled>
                    Forward
                </button>
            </div>
        </div>
    </div>
</div>
<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>
<script type="text/javascript">
    $(".datepicker").datepicker({dateFormat: 'yy-mm-dd'});
    $(".yearpicker").datepicker({dateFormat: 'yy-mm-dd'});


    $(document).ready(function () {
        'use strict';
        // Change this to the location of your server-side upload handler:
        var url = '${pageContext.request.contextPath}/ImageUploadController';
        var progress = 0;
        $('#imageUpload').fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {

                data.submit()
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<img src="" alt="">').appendTo('#imageFiels');
                });
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-danger");
                $('#imageerrorUpload').addClass("bg-success");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Image Uploaded Successfully!</span>').appendTo('#imageerrorUpload');
                $('#imageerrorUpload').fadeOut(9000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);
                // alert(progress);
                $('#imageProgress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#imageerrorUpload').removeClass("bg-primary");
                $('#imageerrorUpload').removeClass("bg-success");
                $('#imageerrorUpload').addClass("bg-danger");
                $('#imageerrorUpload').html("");
                $('#imageerrorUpload').fadeIn();
                $('<span class="text-light d-block p-1 text-center">Image Upload failed.</span>').appendTo('#imageerrorUpload');
                $('#imageerrorUpload').fadeOut(9000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {
                    $('#imageProgress .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#imageerrorUpload').removeClass("bg-primary");
                    $('#imageerrorUpload').removeClass("bg-danger");
                    $('#imageerrorUpload').removeClass("bg-success");

                    $('#imageerrorUpload').addClass("bg-primary");
                    $('#imageerrorUpload').html("");
                    $('#imageerrorUpload').fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#imageerrorUpload');


                });
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    });

    function viewClaimHistory(polRefNo, claimNo) {
        $("#" + claimNo).colorbox({

            width: "100%",
            height: "100%",
            iframe: true,
            href: contextPath + "/CallCenter/viewClaimHistory?P_N_CLIM_NO=" + claimNo,
            onCleanup: function () {
            }
        });
    }

    documentUploadIds.forEach(function (t) {
        documentFileUploder(t)
    });

    documentBillUploadIds.forEach(function (t) {
        alert(t);
        documentBillFileUploder(t)
    });


    function closeUploadImageWindow() {
        loadImageUploadViewer();
    }


    $("#mileage,#otherFee,#jobType,#deductions").change(function () {
        calAssessorFee();
    });

    $("#jobType").on("change", function () {
        getTimeSlots();
    });

    function getTimeSlots() {
        var URL = "${pageContext.request.contextPath}/InspectionDetailsController/fetchTimeSlotForInspection";

        $.ajax({
            url: URL,
            type: 'POST',
            data: {
                jobType: $("#jobType").val(),
                inspectionTypeId: "${inspectionDetailsDto.assessorAllocationDto.inspectionDto.inspectionId}"
            },
            async: false,
            success: function (result) {
                console.log("result ", result)
                // Ensure result is a proper object (in case it's a JSON string)
                var timeSlots = typeof result === "string" ? JSON.parse(result) : result;

                var select = $('#assessorFeeDetailId');
                select.empty(); // Clear existing options

                select.append('<option value="0">-- Please Select --</option>');

                // Append new options
                $.each(timeSlots, function(index, item) {
                    select.append(
                        $('<option></option>').val(item.value).text(item.label)
                    );
                });
            },
            error: function () {
                alert("Failed to load inspection time slots.");
            }
        });
    }

    function calAssessorFee() {
        if ("" == $("#jobType").val()) {
            return false;
        }
        var dataObj = {
            jobType: $("#jobType").val(),
            otherFee: $("#otherFee").val(),
            deductions: $("#deductions").val(),
            mileage: $("#mileage").val(),
            inspectionTypeId: "${motorEngineerDto.inspectionDto.inspectionId}"
        };

        var URL = "${pageContext.request.contextPath}/MotorEngineerController/calculateProfessionalFee";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            async: false,
            success: function (result) {
                if (0 > parseInt(result)) {
                    notify("Deductions can not be exceed total fee", "danger");
                    $("#deductions").val(0);
                    calAssessorFee();
                } else {
                    $("#totalAssessorFee").val(result);
                }
            }
        });
    }

    function setAsReadOnlyAllElements(selector) {
        $(selector).find(':input').not('.skipReadOnly').prop('readonly', true);
        $(selector).find('textarea').not('.skipReadOnly').prop('readonly', true);
        $(selector).find(':radio:not(:checked)').not('.skipReadOnly').prop('disabled', true);
        $(selector).find('select').not('.skipReadOnly').each(function () {
            var selectVal = $(this).val();
            $(this).children('option').each(function () {
                if (selectVal == $(this).val()) {
                    $(this).attr('disabled', false);
                    $(this).attr('selected', true);
                } else {
                    $(this).attr('disabled', true);
                }
            });
        });
        $(selector).find('button').not('.skipReadOnly').prop('disabled', true);
        $(selector).find(':checkbox[readonly="readonly"]').not('.skipReadOnly').click(function () {
            return false;
        });
    }

    $(document).ready(function () {
        $("#thirdPartyInvolved_N").prop("checked", true);
        disable_3rd_Party_details('N');
        $('#btnFwdEngineer').hide();
        $('#btnRecallEngineer').hide();
        $('#btnReturnByEngineer').hide();

//                if (${(motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10))}) {
//                    $('#estimationDiv').find('fieldset').not(':has(>.assessor_pmt_div)').each(function () {
//                        setAsReadOnlyAllElements($(this));
//                    });
//                    setAsReadOnlyAllElements($('#tyreConditionDiv'));
//                    setAsReadOnlyAllElements($('#inspectionDetailsDiv'));
//                }
//                if (${motorEngineerDto.inspectionDetailsDto.assessorFeeAuthStatus eq 'A'}) {
//                    setAsReadOnlyAllElements($('#assessorPmtDiv'));
//                }
    });

    function processThirdParty(type, key) {

        if ("EDIT" == type) {
            disable_3rd_Party_details('Y');
            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=GET&KEY=" + key,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    $("#txnId").val(obj.txnId);
                    $("#ccTpdId").val(obj.ccTpdId);
                    $("#type").val(obj.type);
                    $("#lossType").val(obj.lossType);
                    $("#itemType").val(obj.itemType);
                    $("#vehicleNo").val(obj.vehicleNo);
                    $("#contactNo").val(obj.contactNo);
                    $("#insureDetails").val(obj.insureDetails);
                    $("#remark").val(obj.remark);
                    $("#key").val(key);

                    $("input[name=thirdPartyInvolved]").prop("checked", false);
                    $("input[name=intendClaim]").prop("checked", false);
                    $("#thirdPartyInvolved_" + obj.thirdPartyInvolved).prop("checked", true);
                    $("#intendClaim_" + obj.intendClaim).prop("checked", true);
                }
            });

        } else if ("DELETE" == type) {
            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=DELETE&KEY=" + key,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    $("#txnId").val(obj.type);
                    $("#type").val(obj.type);
                    $("#lossType").val(obj.lossType);
                    $("#itemType").val(obj.itemType);
                    $("#vehicleNo").val(obj.vehicleNo);
                    $("#contactNo").val(obj.contactNo);
                    $("#insureDetails").val(obj.insureDetails);
                    $("#remark").val(obj.remark);

                    $("#thirdPartyInvolved").val(obj.lossType);
                    $("#intendClaim").val(obj.lossType);
                }
            });
        } else if ("ADD" == type) {
            var dataObj = {
                txnId: $("#txnId").val(),
                ccTpdId: $("#ccTpdId").val(),
                type: $("#type").val(),
                lossType: $("#lossType option:selected").val(),
                itemType: $("#itemType option:selected").val(),
                vehicleNo: $("#vehicleNo").val(),
                contactNo: $("#contactNo").val(),
                insureDetails: $("#insureDetails").val(),
                remark: $("#remark").val(),
                thirdPartyInvolved: $('input[name=thirdPartyInvolved]:checked').val(),
                intendClaim: $('input[name=intendClaim]:checked').val()
            };

            $.ajax({
                url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=ADD&KEY=" + key,
                type: 'POST',
                data: dataObj,
                success: function (result) {
                    $("#TPdata tbody").load(contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=LIST");
                    resetForm();
                }
            });
        }


    }

    $('#vehicleNo').focusout(function () {
        $.ajax({
            url: contextPath + "/CallCenter/searchVehicle?vehicleNo=" + this.value,
            type: 'POST',
            success: function (result) {
                if (result == 'YES') {
                    $('#vehicalAvailability').show()
                } else {
                    $('#vehicalAvailability').hide()
                }
            }
        });
    });

    function resetForm() {
        $("#txnId").val("");
        $("#ccTpdId").val("");
        $("#type").val("");
        $("#lossType").val("");
        $("#itemType").val("");
        $("#vehicleNo").val("");
        $("#contactNo").val("");
        $("#insureDetails").val("");
        $("#remark").val("");
        $("#key").val("");

        $("input[name=thirdPartyInvolved]").prop("checked", false);
        $("#thirdPartyInvolved_N").prop("checked", true);
        $("input[name=intendClaim]").prop("checked", false);
    }

    function disable_3rd_Party_details(stat) {
        if (stat == "N") {
            $(".disable").addClass("text-mute").prop('disabled', true);
            $("#addbtn").prop('disabled', true);
            resetForm()
        } else {
            $(".disable").removeClass("text-mute").attr('disabled', false);
            $("#addbtn").prop('disabled', false);
            vehicleNoFieldStatChange($('#itemType').val());

        }

    }


    function vehicleNoFieldStatChange(val) {
        if (1 == val) {
            $("#vehicleNo").prop('disabled', false);
        } else {
            $("#vehicleNo").prop('disabled', true).val("");
        }
    }

    $(function () {
        if ('${thirdPartyDto.thirdPartyInvolved}' == 'Y') {
            disable_3rd_Party_details('Y');
        }
    });


    $('[data-toggle="collapse"]').click(function () {
        $('body').animate({
            scrollTop: 0
        });
    });
    // $("#inspectDateTime").datetimepicker({
    //     sideBySide: true,
    //     format: 'YYYY-MM-DD HH:mm',
    //     icons: {
    //         time: "fa fa-clock-o",
    //         date: "fa fa-calendar",
    //         up: "fa fa-arrow-up",
    //         down: "fa fa-arrow-down"
    //     }
    // });

    var currentDate = '${Current_Date}';
    var accidentDate = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.accidDate}' + ' ' + '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.accidTime}';
    // $("#inspectDateTime").data("DateTimePicker").maxDate(currentDate);
    // $("#inspectDateTime").data("DateTimePicker").minDate(accidentDate);

    function enableDisableFirstStatement(stat) {
        if (stat == "Y") {
            $("#firstStatementReqReason").prop('disabled', false).trigger("chosen:updated");
        } else {
            $("#firstStatementReqReason").val('').prop('disabled', true).trigger("chosen:updated");
        }
    }


    $(document).ready(function () {
        if (isOnsiteOrOffsite('${motorEngineerDto.inspectionDto.inspectionId}')) {
            $("#jobType option[value='0']").prop('disabled', false);
            $("#jobType option[value='2']").prop('disabled', false);
        } else {
            $("#jobType option[value='0']").prop('disabled', true);
            $("#jobType option[value='1']").prop('selected', true);
            $("#jobType option[value='2']").prop('disabled', true);
        }

        if (${PREVIOUS_INSPECTION eq 'Y'}) {
            disableFormInputs('#frmForm');
            $('#changeSpecialRemark').prop("readonly", false);
        }


    });

    function isOnsiteOrOffsite(typeId) {
        if ('1' == typeId || '2' == typeId) {
            return true;
        } else {
            return false;
        }
    }

    function chassis_correct(stat) {
        if ($('#engNoConfirmbox').is(':checked')) {
            $(".correctChaNo").removeClass("text-mute").prop('disabled', false);
        } else {
            $(".correctChaNo").addClass("text-mute").prop('disabled', true);
        }
    }


    function goBack() {
        bootbox.confirm({
            message: "Are you sure you want to close this Page?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                }

            },
            callback: function (result) {
                if (result == true) {
                    var form = document.getElementById("frmForm");
                    if (${TYPE == 4 or TYPE == 5 or TYPE == 6}) {
                        form.action = contextPath + "/RequestAriController/ariListView?TYPE=${TYPE}";
                        form.submit();
                    } else {
                        form.action = contextPath + "/CalculationSheetController/claimCalList?TYPE=6";
                        form.submit();
                    }
                }
            }
        });
    }


    function isRemark() {
        var remark = $("#changeSpecialRemark").val();
        if (remark != '') {
            $('#changeBtn').prop("disabled", false);
            $('#remarkDiv').hide();
        }
    }

    function updateAsChangeRequest() {
        var remark = $("#changeAsSpecialRemark").val();
        var refNo =${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.refNo};

        if (remark != '') {
            $.ajax({
                url: contextPath + "/MotorEngineerController/updateAsChangeRequest?refNo=" + refNo + "&remark=" + remark,
                type: 'GET',
                success: function (result) {
                    var obj = JSON.parse(result);
                    if (obj != '') {
                        notify(obj, "success");
                        $('#asChangeBtn').prop("disabled", true);
                    } else {
                        notify("Can not be updated", "danger");
                    }

                }
            });
        } else {
            $('#asRemarkDiv').show();
            $('#asChangeBtn').prop("disabled", true);
        }

    }

    function isAsRemark() {
        var remark = $("#changeAsSpecialRemark").val();
        if (remark != '') {
            $('#asChangeBtn').prop("disabled", false);
            $('#asRemarkDiv').hide();
        }
    }

    $('#addRTERemarks').click(function (e) {
        var formData = $('#frmMain').serialize();
        $.ajax({
            type: 'POST',
            url: "${pageContext.request.contextPath}/MotorEngineerController/addRteSpecialRemarks",
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    // alert(obj);
                    document.getElementById('inspectionSpecialRemark').value = ''
                    notify(obj, "success");

                }
                loadSpecialRemarks();
            }
        });
    });

    let refNos = [];

    function selectRte() {
        $.ajax({
            url: contextPath + "/ClaimHandlerController/getClaimStatus?claimNo=${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}" ,
            type: 'GET',
            success: function (result) {
                var messageType = JSON.parse(result);
                if (messageType == "83") {
                    notify("Claim file is already forwarded to Engineer!","danger");
                    return;
                }else if(messageType == "FAIL") {
                    notify("System Error", "danger");
                }else{
                    $.ajax({
                        url: contextPath + "/MotorEngineerController/getReportingRteList",
                        type: 'POST',
                        success: function (result) {

                            $('#selectFwdRte').empty();
                            $('#selectFwdRte').append(new Option('Please Select', ''));

                            let rteList = JSON.parse(result);

                            for (let i = 0; i < rteList.length; i++) {
                                $('#selectFwdRte').append(new Option(rteList[i].userId, rteList[i].userId));
                            }
                        }
                    });
                    bootbox.dialog({
                        title: 'Choose an Engineer to Forward Claim File',
                        message: "<select class='form-control form-control-md' id='selectFwdRte'></select>" +
                            "<br><textarea class='form-control' placeholder='Please Enter Remark' id='txtFwdRemark'></textarea>",
                        size: 'medium',
                        buttons: {
                            cancel: {
                                label: "Cancel",
                                className: 'btn-secondary',
                                callback: function () {
                                }
                            },
                            ok: {
                                label: "Forward",
                                className: 'btn-primary',
                                callback: function () {
                                    var rteName = $('#selectFwdRte').val();
                                    var remark = $('#txtFwdRemark').val();
                                    if (rteName == '') {
                                        $('#selectFwdRte').addClass('bg-badge-danger text-white');
                                        $('#selectFwdRte').focus();
                                        notify('Please Select a User to Forward', 'danger');
                                        return false;
                                    } else if (remark == '') {
                                        $('#selectFwdRte').removeClass('bg-badge-danger text-white');
                                        $('#txtFwdRemark').addClass('bg-badge-danger text-white');
                                        $('#txtFwdRemark').focus();
                                        return false;
                                    } else {
                                        $('#selectFwdRte').addClass('bg-badge-danger text-white');
                                        $('#txtFwdRemark').addClass('bg-badge-danger text-white');
                                        forwardToRte(rteName, remark);
                                    }
                                }
                            }
                        }
                    });
                }
            }
        });
    }

    function forwardToRte(userName, remark) {
        $.ajax({
            type: 'POST',
            url: "${pageContext.request.contextPath}/MotorEngineerController/forwardToRte",
            data: {
                V_ASSIGN_RTE: userName,
                N_CLAIM_NO: ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo},
                V_REMARK: remark
            },
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    if (obj == "SUCCESS") {
                        notify("Successfully Forwarded", "success");
                        $('#actionButtonDiv').hide();
                        $('#btnRecallEngineer').show();
                        logdetails();
                    } else if (obj == "FAIL") {
                        notify("Failed to Forward", "danger");
                    } else {
                        notify("System Error", "danger");
                    }
                    loadSuppyOrderView();
                    loadPaymentOptionPage();
                    loadSpecialRemarks();

                }
            }
        });
    }

    function toggleForwardUsers() {
        var userType = $("#selectUserType").val();
        var defaultSelect = document.getElementById('selectDefault');
        var select1 = document.getElementById('selectspCood');
        var select2 = document.getElementById('selectScrTeam');
        if (userType == 1) {
            select1.style.display = "block";
            select2.style.display = "none";
            defaultSelect.style.display = "none";
        } else if (userType == 2) {
            select2.style.display = "block";
            select1.style.display = "none";
            defaultSelect.style.display = "none";
        } else {
            defaultSelect.style.display = "block";
            select1.style.display = "none";
            select2.style.display = "none";
        }
    }

    function validateAdvanceRequestBtn() {
        var userType = $("#selectUserType").val();
        var val1 = $("#selectspCood").val();
        var val2 = $("#selectScrTeam").val();

        if (userType == 1 && (val1 != '' && val1 != 0)) {
            $("#btnRequestForAdvance").prop('disabled', false);
        } else if (userType == 2 && (val2 != '' && val2 != 0)) {
            $('#btnRequestForAdvance').prop('disabled', false);
        } else {
            $('#btnRequestForAdvance').prop('disabled', true);
        }
    }

    function requestForAdvance() {
        showLoader();
        var isForward = $("#selectUserType").prop('disabled');
        var userType = $("#selectUserType").val();
        var advanceAmount = parseFloat(isNaN($("#balanceAdvanceAmount").val()) || $("#balanceAdvanceAmount").val() == '' ? 0.00 : $("#balanceAdvanceAmount").val());
        var assignUser = '';
        var accessUserCode = '';

        if (userType == 1) {
            assignUser = $("#selectspCood").val();
            accessUserCode = 27;
        } else if (userType == 2) {
            assignUser = $("#selectScrTeam").val();
            accessUserCode = 28;
        }

        var url = '';
        var remark = $("#txtAdvanceAmountRemark").val();

        if (isForward) {
            url = contextPath + "/ClaimHandlerController/forwardForAdvance?TYPE=" + userType + "&AMOUNT=" + advanceAmount;
        } else {
            url = contextPath + "/ClaimHandlerController/requestForAdvance";
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                assignUser: assignUser,
                accessUserCode: accessUserCode,
                claimNo: ${claimHandlerDto.claimNo},
                remark: remark
            },
            success: function (result) {
                var response = JSON.parse(result);
                if (response == 'SUCCESS') {
                    loadAdvanceAmountPage(${claimHandlerDto.claimNo});
                    $("#txtAdvanceAmountRemark").val('');
                    $("#advanceForwardModal").modal('hide');
                    notify("Successfully Forwarded", "success");
                } else {
                    $("#advanceForwardModal").modal('hide');
                    notify("Failed to Forward", "danger");
                }
                $('#panleRemark').val('');
                $('#panleRemark').removeClass('bg-badge-danger text-white');
                hideLoader();
            }
        });

    }

    function recallFromEngineer() {
        bootbox.dialog({
            title: 'Enter Remark to Recall Claim File',
            message: "<textarea class='form-control' placeholder='Please Enter Remark' id='txtRecallRemark'></textarea>",
            size: 'medium',
            buttons: {
                cancel: {
                    label: "Cancel",
                    className: 'btn-secondary',
                    callback: function () {
                    }
                },
                ok: {
                    label: "Recall",
                    className: 'btn-primary',
                    callback: function () {
                        var remark = $('#txtRecallRemark').val();
                        if (remark == '') {
                            $('#txtRecallRemark').addClass('bg-badge-danger text-white');
                            $('#txtRecallRemark').focus();
                            return false;
                        } else {
                            $('#txtRecallRemark').removeClass('bg-badge-danger text-white');
                            $.ajax({
                                url: contextPath + "/MotorEngineerController/recallFromForwardedEngineer",
                                data: {
                                    N_CLAIM_NO: '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}',
                                    V_REMARK: remark,
                                },
                                type: "POST",
                                success: function (result) {
                                    var response = JSON.parse(result);
                                    if (response == "SUCCESS") {
                                        notify("Successfully Recalled", "success");
                                        $('#btnFwdEngineer').show();
                                        $('#actionButtonDiv').show();
                                        $('#btnRecallEngineer').hide();
                                        logdetails();
                                    } else if (response == "FAIL") {
                                        notify("Recall Failed", "danger");
                                    } else {
                                        notify("System Error", "danger");
                                    }
                                    loadPaymentOptionPage();
                                    loadSuppyOrderView();
                                    loadSpecialRemarks();
                                }
                            });
                        }
                    }
                }
            }
        });
    }

    function returnClaimFileByEngineer() {
        $('#btnReturnByEngineer').prop('disabled', true);
        $.ajax({
            url: contextPath + "/ClaimHandlerController/claimFileForwardedDetails?N_CLAIM_NO=" + ${claimsDto.claimNo},
            type: 'POST',
            success: function (result) {
                var response = JSON.parse(result);
                if (response == "FAIL") {
                    notify("System Error", "danger");
                    return;
                }
                var forwardedDepartment = '';
                switch (response.accessUserType) {
                    case 27:
                        forwardedDepartment = "Spare Parts Coordinator";
                        break;
                    case 28:
                        forwardedDepartment = "Bill Checking Team";
                        break;
                }
                bootbox.dialog({
                    title: 'Enter Remark to Return Claim File to ' + forwardedDepartment,
                    message: "<textarea class='form-control' placeholder='Please Enter Remark' id='txtReturnRemark'></textarea>",
                    size: 'medium',
                    buttons: {
                        cancel: {
                            label: "Cancel",
                            className: 'btn-secondary',
                            callback: function () {
                                $('#btnReturnByEngineer').prop('disabled', false);
                                return;
                            }
                        },
                        ok: {
                            label: "Return",
                            className: 'btn-primary',
                            callback: function () {
                                var remark = $('#txtReturnRemark').val();
                                if (remark == '') {
                                    notify("Please Enter Remark", "danger");
                                    $('#btnReturnByEngineer').prop('disabled', false);
                                    return;
                                }
                                $.ajax({
                                    url: contextPath + "/ClaimHandlerController/returnClaimByEngineer",
                                    type: "POST",
                                    data: {
                                        N_CLAIM_NO: '${claimsDto.claimNo}',
                                        V_USERID: response.userId,
                                        V_REMARK: remark
                                    },
                                    success: function (resultMsg) {
                                        var responseMsg = JSON.parse(resultMsg);
                                        if (responseMsg == "SUCCESS") {
                                            notify("Successfully Returned", "success");
                                            $('#btnReturnByEngineer').hide();
                                        } else if (responseMsg == "FAIL") {
                                            notify("Failed to Return", "danger");
                                        } else {
                                            notify("System Error", "danger");
                                        }
                                        $('#btnReturnByEngineer').prop('disabled', false);
                                    }
                                });
                            }
                        }
                    }
                });
            }
        });
    }

    function requestAriNReturn(type) {
        $('#claimNo').val($('#P_N_CLIM_NO').val());
        let remark = $('#ariRemark').val();
        let reason = '';
        let inputElements = document.getElementsByClassName(" requestReason");
        for (let i = 0; i < inputElements.length; i++) {
            if (inputElements[i].checked) {
                reason = inputElements[i].value;
            }
        }
        $('#V_REMARK').val(remark);
        $('#V_REASON').val(reason);
        if (${G_USER.accessUserType ne 27 and G_USER.accessUserType ne 28}) {
            notify("Failed to Request ARI", 'danger');
            return;
        }
        if (remark == '' && reason == '') {
            notify("Please Select ARI Reason and Insert a Remark", "danger");
            return;
        } else if (remark == '') {
            notify("Please Enter Remark", "danger");
            return;
        } else if (reason == '') {
            notify("Please Select ARI Reason", "danger");
            return;
        }
        if (type == 1) {
            $('#supplyOrderRefNo').val(${supplyOrderSummaryDto.supplyOrderRefNo});
            bootbox.confirm({
                message: "Are you sure you want Request ARI and return this File?",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        var form = document.getElementById("frmForm");
                        form.action = "${pageContext.request.contextPath}/MotorEngineerController/returnDoToClaimHandler";
                        form.submit();
                    }
                }
            });
        } else {
            var URL = '';
            if (${claimCalculationSheetMainDto.status==61 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId}) {
                URL = "${pageContext.request.contextPath}/MotorEngineerController/scrutinizingRequestAriAndReturn";
            } else if (${claimCalculationSheetMainDto.status==59 and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId}) {
                URL = "${pageContext.request.contextPath}/MotorEngineerController/sparePartsCoordRequestAriAndReturn";
            } else {
                notify("System Error", "danger");
                return;
            }
            bootbox.confirm({
                message: "Are you sure you want to Request ARI and return File?",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    }

                },
                callback: function (result) {
                    if (result == true) {
                        var form = document.getElementById("frmForm");
                        form.action = URL;
                        form.submit();
                    }
                }
            });
        }
    }
</script>
<c:if test="${isDesktopInspection}">
    <script type="text/javascript">
        $('.assessor_value').hide();
        $(".correctChaNo").removeClass("text-mute").prop('disabled', false);
    </script>
</c:if>
</body>
<script>
    hideLoader();
</script>
</html>
