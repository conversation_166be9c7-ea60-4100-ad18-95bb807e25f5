<%--
  Created by IntelliJ IDEA.
  User: user
  Date: 10/14/2020
  Time: 9:32 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<div class="card-body">
    <div class="form-group row">
        <div class="col-sm-4">
            <form name="frmForm" id="ariForm">
                <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
                <input name="P_CAL_SHEET_NO" id="P_CAL_SHEET_NO" type="hidden"/>
                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container pull-right">
                    <input name="ari" title="ARI" class="align-middle checkbox_check requestReason" type="checkbox"
                           id="ari"
                           onchange="validateAriReasons(this.id)"
                           value="1" ${requestAriDto.requestAriReason == '1' ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    <span class="custom-control-description" style="margin-left: 10%">ARI </span>
                </label>
                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container pull-right">
                    <input name="salvage" title="Salvage" class="align-middle checkbox_check requestReason"
                           type="checkbox"
                           id="salvage" onchange="validateAriReasons(this.id)"
                           value="2" ${requestAriDto.requestAriReason == '2' ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    <span class="custom-control-description" style="margin-left: 10%">Salvage </span>
                </label>
                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container pull-right">
                    <input name="ariSalvage" title="ARI Salvage" class="align-middle checkbox_check requestReason"
                           type="checkbox" id="ariSalvage" onchange="validateAriReasons(this.id)"
                           value="3" ${requestAriDto.requestAriReason == '3' ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    <span class="custom-control-description" style="margin-left: 10%">ARI Salvage </span>
                </label>
                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container pull-right">
                    <input name="collectSalvage" title="Collect Salvage"
                           class="align-middle checkbox_check requestReason"
                           type="checkbox" id="collectSalvage" onchange="validateAriReasons(this.id)"
                           value="4" ${requestAriDto.requestAriReason == '4' ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    <span class="custom-control-description" style="margin-left: 10%">Collect Salvage </span>
                </label>
            </form>
        </div>
        <div class="col-sm-8">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Remarks :</label>
                <div class="col-sm-8">
                    <textarea name="ariRemark" id="ariRemark"
                              class=" form-control form-control-sm"
                              title="Remarks" cols=""
                              rows="3" onkeyup=""
                              onclick="">${requestAriDto.remark}</textarea>
                </div>
            </div>
            <div class="form-group row" style="float: right">
                <button id="btnRequestNDOReturn" class="btn btn-secondary" style="margin-right: 5px; margin-top: 20px"
                        onclick="requestAriNReturn(1)">Request ARI and Return File
                </button>
                <button id="btnRequestNCalsheetReturn" class="btn btn-secondary"
                        style="margin-right: 5px; margin-top: 20px"
                        onclick="requestAriNReturn(2)">Request ARI and Return File
                </button>
                <button id="btnRequest" class="btn btn-primary" style="margin-right: 5px; margin-top: 20px"
                        onclick="requestAri()">Request ARI
                </button>
                <button id="btnRevoke" class="btn btn-danger" style="margin-right: 15px; margin-top: 20px"
                        onclick="revokeAri()">Revoke ARI
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function revokeAri() {
        let remark = $('#ariRemark').val();
        let id = '${requestAriDto.id}';
        if (remark == '') {
            notify("Please Enter Remark", "danger");
            return;
        } else {
            bootbox.confirm({
                message: "Do You want to Revoke the ARI Request?",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                },
                callback: function (result) {
                    console.log(result);
                    if (result == true) {
                        $.ajax({
                            url: contextPath + "/RequestAriController/revokeAriRequest",
                            data: {
                                V_REMARK: remark,
                                N_REQUEST_ARI_ID: id
                            },
                            type: 'POST',
                            success: function (result) {
                                let response = JSON.parse(result);
                                if (response == 'SUCCESS') {
                                    notify("ARI Revoked Successfully", "success");
                                    isAriRequested = false;
                                    logdetails();
                                    loadSpecialRemarks();
                                    loadAriRequestPage();
                                    loadButtons();
                                    loadSuppyOrderView();
                                    loadPaymentOptionPage();
                                    $('#fwdComplete').show();
                                } else if (response == 'FAIL') {
                                    notify("Revoke Failed", 'danger');
                                } else {
                                    notify("System Error", "danger");
                                }
                            }
                        });
                    }
                }
            })
        }
    }

    $(document).ready(function () {
        if (${(null != requestAriDto) or (G_USER.accessUserType ne 27 and G_USER.accessUserType ne 28)}) {
            $('input:checkbox').prop('disabled', true);
            $("#btnRequest").prop('disabled', true);
            $("#btnRequestNDOReturn").prop('disabled', true);
            $("#btnRequestNCalsheetReturn").prop('disabled', true);
            if (${requestAriDto.requestedUser eq G_USER.userId}) {
                isAriRequested = true;
            }
        }
        if (${requestAriDto.requestAriReason == 0}) {
            $("#ariRemark").val('');
        }
    });

    function validateAriReasons(id) {
        let isChecked = $('#' + id + '').is(':checked');
        $('input:checkbox').attr('checked', false);
        $('#' + id + '').prop('checked', isChecked);
    }

    function requestAri() {
        let claimNo = ${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo};
        let remark = $('#ariRemark').val();
        let reason = '';
        let inputElements = document.getElementsByClassName(" requestReason");
        for (let i = 0; i < inputElements.length; i++) {
            if (inputElements[i].checked) {
                reason = inputElements[i].value;
            }
        }
        if (${G_USER.accessUserType ne 27 and G_USER.accessUserType ne 28}) {
            notify("Failed to Request ARI", 'danger');
            return;
        }
        if (remark == '' && reason == '') {
            notify("Please Select ARI Reason and Insert a Remark", "danger");
            return;
        } else if (remark == '') {
            notify("Please Enter Remark", "danger");
            return;
        } else if (reason == '') {
            notify("Please Select ARI Reason", "danger");
            return;
        } else {
            bootbox.confirm({
                message: "Do You want to Request for an ARI?",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                },
                callback: function (result) {
                    console.log(result);
                    if (result == true) {
                        $.ajax({
                            url: contextPath + "/ClaimHandlerController/requestedAri",
                            data: {
                                V_REASON: reason,
                                V_REMARK: remark,
                                N_CLAIM_NO: claimNo
                            },
                            type: 'POST',
                            success: function (result) {
                                let response = JSON.parse(result);
                                if (response == 'SUCCESS') {
                                    notify("ARI Requested Successfully", "success");
                                    logdetails();
                                    loadAriRequestPage();
                                    loadButtons();
                                    loadSuppyOrderView();
                                    loadPaymentOptionPage();
                                    loadSpecialRemarks();
                                    $('#fwdComplete').hide();
                                } else if (response == 'FAIL') {
                                    notify("ARI Already Requested", 'danger');
                                } else {
                                    notify("System Error", "danger");
                                }
                            }
                        });
                    }
                }
            })
        }
    }

</script>
