<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Document Upload</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <!-- Generic page styles -->
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>

    <script>
        $(document).ready(function () {
            $('[data-toggle="tooltip"]').tooltip({
                html: true,
                template: '<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
            });
        });
    </script>
</head>
<body class="scroll">
<form name="frmDocumentUpload" id="frmDocumentUpload" method="post">
    <input type="hidden" id="deleteDoc" name="deleteDoc"/>
    <input type="hidden" id="documentTypeId" name="documentTypeId"/>
    <input type="hidden" id="approveStatus"
           value="${approveStatus}">

        <c:choose>
            <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
                <c:set var="claimUploadViewDtoList" value="${claimUploadViewDtoList}" scope="session"/>
            </c:when>
            <c:when test="${PREVIOUS_INSPECTION=='Y' }">
                <c:set var="claimUploadViewDtoList" value="${historyClaimUploadViewDtoList}" scope="request"/>
            </c:when>
        </c:choose>
        <div class="row">
            <c:set var="documentTypeId" scope="request" value="0"/>
            <c:forEach var="claimUploadViewDto" items="${claimUploadViewDtoList}">
                <c:if test="${documentTypeId!=claimUploadViewDto.claimDocumentTypeDto.documentTypeId}">
                    <fieldset class="col-md-12 border mt-2">
                    <div class="row">
                    <div class="col-sm-12 col-form-label">
                        <h6 class="float-left">${claimUploadViewDto.claimDocumentTypeDto.documentTypeName} </h6>
                        <c:if test="${PREVIOUS_INSPECTION != 'Y' and (G_USER.userId eq motorEngineerDto.inspectionDetailsDto.assignRteUser or G_USER.accessUserType eq 1)}">
                            <c:if test="${!(motorEngineerDto.inspectionDetailsDto.inspectionDetailsAuthStatus eq 'A' or (motorEngineerDto.inspectionDetailsDto.recordStatus ne 0 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 8 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 29 and motorEngineerDto.inspectionDetailsDto.recordStatus ne 10))}">
                                <button id="cmdDelete" type="button" name="cmdDelete"
                                        class="btn btn-red text-danger pull-right ml-2"
                                        onclick="deleteSelectedDocs('${claimUploadViewDto.claimDocumentTypeDto.documentTypeId}')">
                                    Delete
                                </button>
                            </c:if>
                            <button id="cmdUpload" type="button" name="cmdUpload" class="btn btn-primary pull-right"
                                    onclick="docUpload(${claimUploadViewDto.claimDocumentTypeDto.documentTypeId})">
                                Upload
                                Here
                            </button>
                        </c:if>
                    </div>

                    <div class="col-md-12">
                    <hr class="my-2">
                </c:if>
                <c:set var="cnt" value="1"/>
                <c:forEach var="claimDocumentDto" items="${claimUploadViewDto.claimDocumentDtoList}">

                    <div class="uploadfile-delet">

                        <a href="${pageContext.request.contextPath}/MotorEngineerController/documentViewer?refNo=${claimDocumentDto.refNo}&jobRefNo=${claimDocumentDto.jobRefNo}&PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}"
                           class="claimView${cnt}"
                           data-toggle="tooltip" title="${claimDocumentDto.toolTip}">
                            <h6>${cnt}</h6>
                            <i class="fa fa-file-pdf-o fa-4x m-3"></i>
                        </a>
                        <label class="custom-control custom-checkbox float-left col-form-label check-container">
                            <input id="intendClaim_N" name="intendClaim"
                                   onclick="setDeleteDoc('${claimDocumentDto.refNo}');" type="checkbox"
                                   class="align-middle"/>
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <script type="text/javascript">
                        $('.claimView${cnt}').popupWindow({
                            height: screen.height,
                            width: screen.width,
                            centerBrowser: 0,
                            left: 0,
                            resizable: 1,
                            centerScreen: 1,
                            scrollbars: 1,
                            windowName: 'swipdoc${cnt}'
                        });
                    </script>
                    <c:set var="cnt" value="${cnt=cnt+1}"/>
                </c:forEach>
                <c:if test="${documentTypeId!=claimUploadViewDto.claimDocumentTypeDto.documentTypeId}">
                    </div>
                    </div>
                    </fieldset>
                </c:if>
                <c:set var="documentTypeId" scope="request"
                       value="${claimUploadViewDto.claimDocumentTypeDto.documentTypeId}"/>
            </c:forEach>
        </div>
    </div>
</form>
<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        window.parent.notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        window.parent.notify('${errorMessage}', "danger");
    </script>
</c:if>
<script>

    function reloadPage() {
        document.frmDocumentUpload.action = "${pageContext.request.contextPath}/MotorEngineerController/documentUpload?JOB_REF_NO=${jobRefNo}";
        document.frmDocumentUpload.submit();
        loadLogDetails();
    }

    function docUpload(documentTypeId) {

        $(window.parent.document.getElementById('docUploadModal' + documentTypeId)).modal({
            backdrop: 'static',
            keyboard: false,
            refresh: true,
            show: true
        });
        loadLogDetails();
    }

    function setDeleteDoc(refId) {
        var docs = $('#deleteDoc').val();
        if (docs == '') {
            docs = refId;
        } else {
            docs = docs + ',' + refId;
        }

        $('#deleteDoc').val(docs);
        loadLogDetails();

    }

    function deleteSelectedDocs(typeId) {

        $('#documentTypeId').val(typeId);
        document.frmDocumentUpload.action = "${pageContext.request.contextPath}/MotorEngineerController/deleteDocs";
        document.frmDocumentUpload.submit();
        loadLogDetails();
    }

</script>
</body>
</html>
