<%-- 
    Document   : thirdPartyDetails
    Created on : Jun 13, 2018, 7:52:43 PM
    Author     : Thanuja
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <!-- Force latest IE rendering engine or ChromeFrame if installed -->
        <!--[if IE]>
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
        <meta charset="utf-8">
        <title>Document Upload</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
            <!-- Generic page styles -->
        <%--<link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/style.css">--%>
        <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
        <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
        <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
        <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
        <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
        <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
        <!-- The basic File Upload plugin -->
        <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
        <script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/ari-form-validations.js"></script>

        <script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/inspectiondetails-form-validations.js?v1"></script>
        <script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/motorengineer/assessor.js"></script>
    </head>
    <body>
        <form id="tpForm" name="tpForm">
            <div class="col-lg-12">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">If Third Party Involved<span
                            class="text-danger font-weight-bold">  *</span> :</label>
                    <div class="col-sm-8">
                        <div class="row">
                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                <input id="thirdPartyInvolved_Y" name="thirdPartyInvolved"
                                       type="radio"
                                       class="align-middle"
                                       value="Y"
                                       onclick="disable_3rd_Party_details('Y')"/>
                                <span class="radiomark"></span>
                                <span class="custom-control-description">Yes</span>
                            </label>
                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                                <input id="thirdPartyInvolved_N" name="thirdPartyInvolved"
                                       type="radio"
                                       class="align-middle"
                                       value="N"
                                       onclick="disable_3rd_Party_details('N')"/>
                                <span class="radiomark"></span>
                                <span class="custom-control-description">No</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Type of Loss <span
                            class="text-danger font-weight-bold">  *</span>:</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable" id="lossType"
                                name="lossType">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_loss_type", "N_ID", "V_CAUSE_OF_LOSS")}
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Item <span
                            class="text-danger font-weight-bold">  *</span>:</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable " id="itemType"
                                name="itemType"
                                onchange="vehicleNoFieldStatChange(this.value)">
                            ${DbRecordCommonFunctionBean.getPopupList("tp_vehicle_item_mst", "N_ID", "V_ITEM_DES")}
                        </select>
                    </div>
                </div>
                <div class="form-group row" id="vehicalDisable">
                    <label class="col-sm-4 col-form-label disable">Vehicle Number :</label>
                    <div class="col-sm-8">
                        <input class=" form-control form-control-sm disable" type="text"
                               value="${thirdPartyDto.vehicleNo}" name="vehicleNo"
                               id="vehicleNo"/>
                        <span class="text-danger" id="vehicalAvailability"
                              style="display: none;"><b>This Vehicle LOLC Insured.</b></span>
                    </div>
                </div>
                <div class="form-group row disable">
                    <label class="col-sm-4 col-form-label disable">Contact Number :</label>
                    <div class="col-sm-8">
                        <input class=" form-control form-control-sm disable" type="text"
                               value="" name="contactNo"
                               id="contactNo"/>
                    </div>
                </div>
                <div class="form-group row disable">
                    <label class="col-sm-4 col-form-label disable">Insure Details :</label>
                    <div class="col-sm-8">
                        <input class=" form-control form-control-sm  disable" type="text"
                               value="" name="insureDetails"
                               id="insureDetails"/>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">TP Intends To
                        Claim<span
                            class="text-danger font-weight-bold">  *</span> :</label>
                    <div class="col-sm-8">
                        <div class="row radio">
                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-2 col-form-label  check-container">
                                <input id="intendClaim_Y" name="intendClaim" type="radio"
                                       value="Y"
                                       class="align-middle disable"
                                       />
                                <span class="radiomark"></span>
                                <span class="custom-control-description disable ">Yes</span>
                            </label>
                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-2 col-form-label check-container">
                                <input id="intendClaim_N" name="intendClaim" type="radio"
                                       value="N"
                                       class="align-middle disable "
                                       />
                                <span class="radiomark"></span>
                                <span class="custom-control-description disable ">No</span>
                            </label>
                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-5 col-form-label check-container">
                                <input id="intendClaim_WD" name="intendClaim" type="radio"
                                       value="WD"
                                       class="align-middle disable "
                                       />
                                <span class="radiomark"></span>
                                <span class="custom-control-description disable ">Want to decide</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Remarks :</label>
                    <div class="col-sm-8">
                        <textarea name="remark" id="remark"
                                  class=" form-control form-control-sm disable" title="Remarks"
                                  cols="" rows="3"></textarea>
                    </div>
                </div>
                <input type="hidden" name="txnId" id="txnId"/>
                <input type="hidden" name="ccTpdId" id="ccTpdId"/>
                <input type="hidden" name="key" id="key"/>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label"></label>
                    <div class="col-sm-8">
                        <button type="submit" id="addbtn"
                                class="btn btn-primary "
                                >Add
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <table width="100%" cellpadding="0" cellspacing="1"
                               class="table table-hover table-sm dataTable no-footer dtr-inline "
                               id="TPdata">
                            <thead>
                                <tr>
                                    <th scope="col" class="tbl_row_header">Record ID</th>
                                    <th scope="col" class="tbl_row_header">Type of Loss</th>
                                    <th scope="col" class="tbl_row_header">Item</th>
                                    <th scope="col" class="tbl_row_header">Vehicle No.</th>
                                    <th scope="col" class="tbl_row_header">TP Intend to Claim
                                    </th>
                                    <th scope="col" class="tbl_row_header">Remark</th>
                                    <th scope="col" class="tbl_row_header">Created User ID</th>
                                    <th scope="col" class="tbl_row_header">Created Date / Time</th>
                                    <th scope="col" class="tbl_row_header">Created Module</th>
                                    <th scope="col" class="tbl_row_header">Previous Created Record ID</th>
                                    <th scope="col" class="tbl_row_header">Previous Created Module</th>
                                    <th scope="col" class="tbl_row_header">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <jsp:include page="thirdPartyAssessorGrid.jsp"/>
                            </tbody>
                        </table>
                    </div>
                </div>
                <input type="reset" name="index" id="reset" style="visibility: hidden;">
                <input type="hidden" name="index" id="index" value="${index}">
            </div>
        </form>

        <script>
            function disable_3rd_Party_details(stat) {
                $('#tpForm').formValidation('resetForm');
                if (stat == "N") {
                    $(".disable").addClass("text-mute").prop('disabled', true);
                    $("#addbtn").prop('disabled', true);
                    resetForm()
                } else {
                    $(".disable").removeClass("text-mute").attr('disabled', false);
                    $("#addbtn").prop('disabled', false);
                    vehicleNoFieldStatChange($('#itemType').val());

                }

            }

            function vehicleNoFieldStatChange(val) {
                if (1 == val) {
                    $("#vehicleNo").prop('disabled', false);
                } else {
                    $("#vehicleNo").prop('disabled', true).val("");
                }
            }

            $(function () {
                if ('${thirdPartyDto.thirdPartyInvolved}' == 'Y') {
                    disable_3rd_Party_details('Y');
                }
            });


            function resetForm() {
                $("#txnId").val("");
                $("#ccTpdId").val("");
                $("#type").val("");
                $("#lossType").val("");
                $("#itemType").val("");
                $("#vehicleNo").val("");
                $("#contactNo").val("");
                $("#insureDetails").val("");
                $("#remark").val("");
                $("#key").val("");

                $("input[name=thirdPartyInvolved]").prop("checked", false);
                $("#thirdPartyInvolved_N").prop("checked", true);
                $("input[name=intendClaim]").prop("checked", false);
            }

            $('#vehicleNo').focusout(function () {
                $.ajax({
                    url: contextPath + "/CallCenter/searchVehicle?vehicleNo=" + this.value,
                    type: 'POST',
                    success: function (result) {
                        if (result == 'YES') {
                            $('#vehicalAvailability').show()
                        } else {
                            $('#vehicalAvailability').hide()
                        }
                    }
                });
            });


            $(document).ready(function () {
                $("#thirdPartyInvolved_N").prop("checked", true);
                disable_3rd_Party_details('N');
                $("#TPdata tbody").load(contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=LIST");
            });

            function addThirdParty() {
                var key = $('#key').val();
                var dataObj = {
                    txnId: $("#txnId").val(),
                    ccTpdId: $("#ccTpdId").val(),
                    type: $("#type").val(),
                    lossType: $("#lossType option:selected").val(),
                    itemType: $("#itemType option:selected").val(),
                    vehicleNo: $("#vehicleNo").val(),
                    contactNo: $("#contactNo").val(),
                    insureDetails: $("#insureDetails").val(),
                    remark: $("#remark").val(),
                    thirdPartyInvolved: $('input[name=thirdPartyInvolved]:checked').val(),
                    intendClaim: $('input[name=intendClaim]:checked').val()
                };

                $.ajax({
                    url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=ADD&KEY=" + key,
                    type: 'POST',
                    data: dataObj,
                    success: function (result) {
                        $("#TPdata tbody").load(contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=LIST");
                        resetForm();
                    }
                });
            }

            function processThirdParty(type, key) {
                if ("EDIT" == type) {
                    disable_3rd_Party_details('Y');
                    $.ajax({
                        url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=GET&KEY=" + key,
                        type: 'GET',
                        success: function (result) {
                            var obj = JSON.parse(result);
                            $("#txnId").val(obj.txnId);
                            $("#ccTpdId").val(obj.ccTpdId);
                            $("#type").val(obj.type);
                            $("#lossType").val(obj.lossType);
                            $("#itemType").val(obj.itemType);
                            $("#vehicleNo").val(obj.vehicleNo);
                            $("#contactNo").val(obj.contactNo);
                            $("#insureDetails").val(obj.insureDetails);
                            $("#remark").val(obj.remark);
                            $("#key").val(key);

                            $("input[name=thirdPartyInvolved]").prop("checked", false);
                            $("input[name=intendClaim]").prop("checked", false);
                            $("#thirdPartyInvolved_" + obj.thirdPartyInvolved).prop("checked", true);
                            $("#intendClaim_" + obj.intendClaim).prop("checked", true);
                        }
                    });

                } else if ("DELETE" == type) {
                    $.ajax({
                        url: contextPath + "/MotorEngineerController/processThirdPartyDetails?TPP_TYPE=DELETE&KEY=" + key,
                        type: 'GET',
                        success: function (result) {
                            var obj = JSON.parse(result);
                            $("#txnId").val(obj.type);
                            $("#type").val(obj.type);
                            $("#lossType").val(obj.lossType);
                            $("#itemType").val(obj.itemType);
                            $("#vehicleNo").val(obj.vehicleNo);
                            $("#contactNo").val(obj.contactNo);
                            $("#insureDetails").val(obj.insureDetails);
                            $("#remark").val(obj.remark);

                            $("#thirdPartyInvolved").val(obj.lossType);
                            $("#intendClaim").val(obj.lossType);
                        }
                    });
                }


            }
        </script>
    </body>
</html>
