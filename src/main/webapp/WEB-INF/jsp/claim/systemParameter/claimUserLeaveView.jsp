<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Pruthuvi
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmClaimPanelUserList" id="frmClaimPanelUserList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>User Leave</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">

                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div id="accordion" class="accordion">
                            <div class="card">
                                <div class="card-header" id="headingOne">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                           data-target="#collapseOne"
                                           aria-expanded="true" aria-controls="collapseOne">
                                            Search Here <i class="fa fa-search"></i>
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                                     data-parent="#accordion">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="txtFromDate" class="col-md-4 col-form-label"> From Date
                                                        Time </label>
                                                    <div class="col-md-8">
                                                        <input name="txtFromDate" class="form-control form-control-sm"
                                                               placeholder="From Date" id="txtFromDate" type="text"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label for="userIds" class="col-md-4 col-form-label">User ID</label>
                                                    <div class="col-md-8">
                                                        <input name="userIds" id="userIds"
                                                               class="form-control form-control-sm"
                                                               placeholder="User ID">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="txtToDate" class="col-md-4 col-form-label"> To Date
                                                        Time</label>
                                                    <div class="col-md-8">
                                                        <input name="txtToDate" id="txtToDate" type="text"
                                                               class="form-control form-control-sm"
                                                               placeholder="To Date">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label for="leaveTypes" class="col-md-4 col-form-label"> Leave
                                                        Type</label>
                                                    <div class="col-md-8">
                                                        <input name="leaveTypes" id="leaveTypes"
                                                               class="form-control form-control-sm"
                                                               placeholder="Leave Type">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 text-right">
                                                <button class="btn btn-primary" type="button" name="cmdSearch"
                                                        id="cmdSearch"
                                                        onclick="search()">Search
                                                </button>
                                                <a class="btn btn-secondary" type="button" name="cmdClose"
                                                   id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                                </a>
                                                <hr>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">

                    <div class="col-md-12">
                        <div class="mt-2">
                            <h6 class="float-left">Leave Result</h6>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>Key</th>
                                <th>No</th>
                                <th>User Id</th>
                                <th>From Date Time</th>
                                <th>To Date Time</th>
                                <th>Leave Type</th>
                                <th>Input User</th>
                                <th>Input Date/Time</th>
                                <th>Name</th>
                                <th class="min-mobile"></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <form name="frmClaimPanelUser" id="frmClaimPanelUser" method="post">
        <!-- Modal -->
        <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> User Id</label>
                            <div class="col-sm-8">
                                <input id="userId" name="userId" class="form-control form-control-sm" readonly/>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="fromDateTime" class="col-sm-4 col-form-label"> From Date</label>
                            <div class="col-sm-8">
                                <input class="form-control form-control-sm"
                                       placeholder="From Date" id="fromDateTime" name="fromDateTime" type="text"
                                />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="toDateTime" class="col-sm-4 col-form-label"> To Date</label>
                            <div class="col-sm-8">
                                <input id="toDateTime" name="toDateTime" type="text"
                                       class="form-control form-control-sm" placeholder="To Date">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Leave Type</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="leaveType" name="leaveType">
                                    <option selected value="">Please Select</option>
                                    <option value="LEAVE">LEAVE</option>
                                    <option value="CANCEL">CANCEL</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="CloseReload()">
                            Close
                        </button>
                        <button type="submit" id="addClaimUserbtn" class="btn btn-primary">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimuserleave-datatables.js?v4"></script>
<script type="text/javascript">

    function CloseReload() {
        location.reload();
    }

</script>
<script type="text/javascript">
    $('#addClaimUserbtn').click(function (e) {
        if ("LEAVE" == $('#leaveType').val() || "CANCEL" == $('#leaveType').val()) {
            var formData = $('#frmClaimPanelUser').serialize();
            $.ajax({
                url: contextPath + "/ClaimUserLeaveController/save",
                type: 'POST',
                data: formData,
                success: function (result) {
                    var messageType = JSON.parse(result);
                    if (messageType != null) {
                        notify(messageType, "success")
                    } else {
                        notify("Fail to Save", "fail")
                    }

                }
            });
            // location.reload();
        }

    });
    $("#frmClaimPanelUser").submit(function (e) {
        e.preventDefault();
    });

    $(document).ready(function () {
        $('#frmClaimPanelUser')
            .formValidation({
                framework: 'bootstrap',
                excluded: 'disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {

                    fromDate: {
                        // object with has 'myClass' class.
                        validators: {
                            notEmpty: {
                                message: 'Please choose a date'
                            }
                        }
                    },
                    leaveType: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select'
                            }
                        }
                    },
                    toDate: {
                        validators: {
                            notEmpty: {
                                message: 'Please choose a date'
                            }
                        }
                    }
                }
            })
            .on('success.form.fv', function (e) {
                $(e.target).data('formValidation').disableSubmitButtons(false);

            });

        $("#txtToDate").datetimepicker({
            sideBySide: true,
            format: 'YYYY-MM-DD HH:mm',
            //  maxDate:new Date(currentDate),
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
                up: "fa fa-arrow-up",
                down: "fa fa-arrow-down"
            }
        });

        $("#txtFromDate").datetimepicker({
            sideBySide: true,
            format: 'YYYY-MM-DD HH:mm',
            //  maxDate:new Date(currentDate),
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
                up: "fa fa-arrow-up",
                down: "fa fa-arrow-down"
            }
        });
    });


</script>
</body>
</html>
