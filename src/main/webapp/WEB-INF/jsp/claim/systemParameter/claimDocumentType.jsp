<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Pruthuvi
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmClaimPanelUserList" id="frmClaimPanelUserList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Document Type</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mt-2">
                            <h6 class="float-left">Result</h6>
                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                                    data-target="#panelUser" name="addButton" id="addButton">
                                Add Document
                            </button>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>Doc type Id</th>
                                <th>No</th>
                                <th>Document Type Name</th>
                                <th>DEPARTMENT
                                    _ID
                                </th>
                                <th>Is Mandatory</th>
                                <th>Partial Loss</th>
                                <th>Total Loss</th>
                                <th>Lump Sum</th>
                                <th>IS_THIRD_PARTY
                                    _PROP_VEHICLE
                                </th>
                                <th>IS_THIRD_
                                    PARTY_DEATH
                                </th>
                                <th>IS_THIRD_
                                    PARTY
                                    _INJURIES
                                </th>
                                <th>DOC_REQ
                                    _FROM
                                </th>
                                <th>REMIN_DOC
                                    _DISPLAY _NAME
                                </th>
                                <th>REC_STATUS</th>
                                <th>INP_USER
                                    _ID
                                </th>
                                <th>INP_DATE
                                    _TIME
                                </th>
                                <th class="min-mobile"></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <form name="frmClaimPanelUser" id="frmClaimPanelUser" method="post">
        <input type="hidden" name="documentTypeId" id="documentTypeId"/>
        <!-- Modal -->
        <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Document Name</label>
                            <div class="col-sm-8">
                                <input id="documentTypeName" name="documentTypeName"
                                       class="form-control form-control-sm">
                            </div>

                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Department Id</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="departmentId" name="departmentId">
                                    <option value="">Please Select</option>
                                </select>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Is Mandatory</label>
                            <div class="col-sm-8">
                                <select id="isMandatory" name="isMandatory" class="form-control form-control-sm">
                                    <option value="Y">YES</option>
                                    <option value="N">NO</option>
                                </select></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Is Partial Loss</label>
                            <div class="col-sm-8">
                                <select id="isPartialLoss" name="isPartialLoss" class="form-control form-control-sm">
                                    <option value="Y">YES</option>
                                    <option value="N">NO</option>
                                </select></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Is Total Loss</label>
                            <div class="col-sm-8">
                                <select id="isTotLoss" name="isTotLoss" class="form-control form-control-sm">
                                    <option value="Y">YES</option>
                                    <option value="N">NO</option>
                                </select></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Is Lump Sum</label>
                            <div class="col-sm-8">
                                <select id="isLumpSum" name="isLumpSum" class="form-control form-control-sm">
                                    <option value="Y">YES</option>
                                    <option value="N">NO</option>
                                </select></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Is Third Party Prop Vehicle</label>
                            <div class="col-sm-8">
                                <select id="isThirdPartyPropVehicle" name="isThirdPartyPropVehicle"
                                        class="form-control form-control-sm">
                                    <option value="Y">YES</option>
                                    <option value="N">NO</option>
                                </select></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Is Third Party Death</label>
                            <div class="col-sm-8">
                                <select id="isThirdPartyDeath" name="isThirdPartyDeath"
                                        class="form-control form-control-sm">
                                    <option value="Y">YES</option>
                                    <option value="N">NO</option>
                                </select></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Is Third Party Injuries</label>
                            <div class="col-sm-8">
                                <select id="isThirdPartyInjuries" name="isThirdPartyInjuries"
                                        class="form-control form-control-sm">
                                    <option value="Y">YES</option>
                                    <option value="N">NO</option>
                                </select></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Doc Req From</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="docReqFrom" name="docReqFrom">
                                    <option value="">Please Select</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">reminder Doc Display Name</label>
                            <div class="col-sm-8">
                                <input id="reminderDocDisplayName" name="reminderDocDisplayName"
                                       class="form-control form-control-sm">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">rec status</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="recStatus" name="recStatus">
                                    <option value="">Please Select</option>
                                    <option value="A">A</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Inp User Id</label>
                            <div class="col-sm-8">
                                <input id="inpUserId" name="inpUserId" class="form-control form-control-sm" readonly>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Inp Date Time</label>
                            <div class="col-sm-8">
                                <input id="inpDateTime" name="inpDateTime" class="form-control form-control-sm"
                                       readonly>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="CloseReload()">
                            Close
                        </button>
                        <button type="submit" id="addClaimUserbtn" class="btn btn-primary">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimdocumenttype-datatables.js?v4"></script>
<script type="text/javascript">
    // function myFunction() {
    //     $('#inputUser,#inputDateTime ,#userStatus, #userId ').val('').attr('disabled', false);
    //     $('#userPanelId').val('');
    //     $('#userPanelId').multiselect('enable');
    //     $('#userPanelId').multiselect('refresh');
    // }

    function CloseReload() {
        location.reload();
    }

</script>
<script type="text/javascript">
    $('#addClaimUserbtn').click(function (e) {
        var formData = $('#frmClaimPanelUser').serialize();
        $.ajax({
            url: contextPath + "/ClaimDocumentTypeController/save",
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    notify(obj, "success");
                }
                else {
                    notify("Fail to Add User", "fail");
                }
            }
        });
    });
    $("#frmClaimPanelUser").submit(function (e) {
        e.preventDefault();
    });
    $(document).ready(function () {

        // $('#userPanelId').multiselect({
        //     onChange: function () {
        //         $('#frmClaimPanelUser').formValidation('revalidateField', 'userPanelId');
        //     }
        // });

        $('#frmClaimPanelUser')
            .formValidation({
                framework: 'bootstrap',
                excluded: 'disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {

                    departmentId: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select'
                            }
                        }
                    },
                    documentTypeName: {
                        validators: {
                            remote: {
                                message: 'The Document Name Already Exist',
                                method: 'POST',
                                url: contextPath + "/ClaimDocumentTypeController/validateDocTypeName",
                            },
                            notEmpty: {
                                message: 'Please Enter Document Name'
                            }
                        }
                    },
                    docReqFrom: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select Status'
                            }
                        }
                    }
                }
            })
    })
        .on('success.form.fv', function (e) {
            $(e.target).data('formValidation').disableSubmitButtons(false);

        });


</script>
<script type="text/javascript">
    $(document).ready(function () {
        $.ajax({
            url: contextPath + "/ClaimDocumentTypeController/docTypeName",
            type: 'POST',
            success: function (result) {
                var userArr = JSON.parse(result);
                var selOpts = "";
                for (i = 0; i < userArr.length; i++) {
                    var id = userArr[i]['departmentId'];
                    var val = userArr[i]['departmentName'];
                    selOpts += "<option value='" + id + "'>" + val + "</option>";
                }
                $('#departmentId').append(selOpts);
            }
        });
    });
</script>
<script type="text/javascript">
    $(document).ready(function () {
        $.ajax({
            url: contextPath + "/ClaimDocumentTypeController/docReqName",
            type: 'POST',
            success: function (result) {
                var userArr = JSON.parse(result);
                var selOpts = "";
                for (i = 0; i < userArr.length; i++) {
                    var id = userArr[i]['docReqFrom'];
                    var val = userArr[i]['reqFromDesc'];
                    selOpts += "<option value='" + id + "'>" + val + "</option>";
                }
                $('#docReqFrom').append(selOpts);
            }
        });
    });
</script>

</body>
</html>
