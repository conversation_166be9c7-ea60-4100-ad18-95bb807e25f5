<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Pruthuvi
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmClaimPanelUserList" id="frmClaimPanelUserList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Panel User</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mt-2">
                            <h6 class="float-left">Claim Panel User Result</h6>
                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                                    data-target="#panelUser" onclick="myFunction()">
                                Add User
                            </button>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>ref no</th>
                                <th>No</th>
                                <th>User Id</th>
                                <th>Panel Name</th>
                                <th>Status</th>
                                <th>Input User</th>
                                <th>Input Date/Time</th>
                                <th class="min-mobile"></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <form name="frmClaimPanelUser" id="frmClaimPanelUser" method="post">
        <input type="hidden" name="id" id="id"/>
        <!-- Modal -->
        <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> User Name</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="userId" name="userId">
                                    <option value="">Please select one</option>
                                    <c:forEach var="claimPanelUser" items="${claimPanelUserList}">
                                        <option value="${claimPanelUser.value}">${claimPanelUser.label}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Date Time</label>
                            <div class="col-sm-8">
                                <input id="inputDateTime" name="inputDateTime" class="form-control form-control-sm"
                                       readonly>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Input User Id</label>
                            <div class="col-sm-8">
                                <input id="inputUser" name="inputUser" class="form-control form-control-sm" readonly>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> Panel Name</label>
                            <div class="col-sm-8">
                                <input type="hidden" name="userPanelIds" id="userPanelIds"/>
                                <select id="userPanelId" name="userPanelId" class="form-control form-control-sm"
                                        multiple="multiple">
                                    <c:forEach var="claimPanel" items="${claimPanelList}">
                                        <option value="${claimPanel.value}">${claimPanel.label}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label"> Status</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="userStatus" name="userStatus">
                                    <option value="">Please Select</option>
                                    <option value="A">Active</option>
                                    <option value="D">Disabled</option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="CloseReload()">
                            Close
                        </button>
                        <button type="submit" id="addClaimUserbtn" class="btn btn-primary">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimpaneluser-datatables.js?v4"></script>
<script type="text/javascript">
    function myFunction() {
        $('#inputUser,#inputDateTime ,#userStatus, #userId ').val('').attr('disabled', false);
        $('#userPanelId').val('');
        $('#userPanelId').multiselect('enable');
        $('#userPanelId').multiselect('refresh');
    }

    function CloseReload() {
        location.reload();
    }

</script>
<script type="text/javascript">
    $('#addClaimUserbtn').click(function (e) {
        // e.preventDefault();
        var userPanelId = $('#userPanelId').val();
        $('#userPanelIds').val(userPanelId);
        var formData = $('#frmClaimPanelUser').serialize();
        $.ajax({
            url: contextPath + "/ClaimPanelUserController/save",
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj == "SUCCESS") {
                    notify("User Added Successfully", "success");
                } else if (obj == "FAIL") {
                    notify("User Already Saved in the Panel", "danger");
                } else {
                    notify("System Error", "danger");
                }
            }
        });
    });
    $("#frmClaimPanelUser").submit(function (e) {
        e.preventDefault();
    });
    $(document).ready(function () {

        // $('#userPanelId').multiselect({
        //     onChange: function () {
        //         $('#frmClaimPanelUser').formValidation('revalidateField', 'userPanelId');
        //     }
        // });

        $('#frmClaimPanelUser')
            .formValidation({
                framework: 'bootstrap',
                excluded: 'disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {

                    userId: {
                        // object with has 'myClass' class.
                        validators: {
                            notEmpty: {
                                message: 'Please Select'
                            }
                        }
                    },
                    // userPanelIds: {
                    //     validators: {
                    //         choice: {
                    //             min: 1,
                    //             max: 4,
                    //             message: 'Please Select'
                    //         },
                    //     }
                    // },
                    userStatus: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select Status'
                            }
                        }
                    }
                }
            })
            .on('success.form.fv', function (e) {
                $(e.target).data('formValidation').disableSubmitButtons(false);

            });
    });


</script>
</body>
</html>
