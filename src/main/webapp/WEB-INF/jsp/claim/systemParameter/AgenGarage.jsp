<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Pruthuvi
    version 2.0
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmClaimPanelUserList" id="frmClaimPanelUserList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Garage List</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mt-2">
                            <h6 class="float-left">Result</h6>
                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                                    data-target="#panelUser" name="addButton" id="addButton">
                                Add Garage
                            </button>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>id</th>
                                <th>No</th>
                                <th>Garage Code</th>
                                <th>Garage Name</th>
                                <th>Address</th>
                                <th>Contact Person</th>
                                <th>Contact Number</th>
                                <th>General Telephone No</th>
                                <th>Status</th>
                                <th class="min-mobile">Edit</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <form name="frmGarage" id="frmGarage" method="post">
        <input type="hidden" name="id" id="id"/>
        <!-- Modal -->
        <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5>Add New Garage</h5>
                    </div>
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Garage Name</label>
                            <div class="col-sm-8">
                                <input id="gargName" name="gargName"
                                       class="form-control form-control-sm">
                            </div>

                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Garage Code</label>
                            <div class="col-sm-8">
                                <input id="gargCode" name="gargCode"
                                       class="form-control form-control-sm">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Address Line 1</label>
                            <div class="col-sm-8">
                                <input id="address1" name="address1"
                                       class="form-control form-control-sm">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Address Line 2</label>
                            <div class="col-sm-8">
                                <input id="address2" name="address2"
                                       class="form-control form-control-sm">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Address Line 3</label>
                            <div class="col-sm-8">
                                <input id="address3" name="address3"
                                       class="form-control form-control-sm">
                            </div>
                        </div>
                        <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Contact Person</label>
                        <div class="col-sm-8">
                            <input id="conPerson" name="conPerson"
                                   class="form-control form-control-sm" ></div>
                    </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Contact Number</label>
                            <div class="col-sm-8">
                                <input id="conNumber" name="conNumber"
                                       class="form-control form-control-sm" ></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">General Telephone Number</label>
                            <div class="col-sm-8">
                                <input id="genTelNo" name="genTelNo"
                                       class="form-control form-control-sm" ></div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Status</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="status" name="status">
                                    <option value="">Please Select</option>
                                    <option value="A">A</option>
                                    <option value="R">P</option>
                                </select>

                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="CloseReload()">
                                Close
                            </button>
                            <button type="submit" id="addgarage" class="btn btn-primary">Save changes</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/agent_garage-datatables.js?v3"></script>
<script type="text/javascript">
    function CloseReload() {
        location.reload();
    }

</script>
<script type="text/javascript">
    $('#addgarage').click(function (e) {
        var formData = $('#frmGarage').serialize();
        e.preventDefault();
        $.ajax({
            url: contextPath + "/GarageListController/saveGarage",
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    notify(obj, "success");
                }
                else {
                    notify("Fail to Add User", "fail");
                }
            }
        });
    });
    $("#addgarage").submit(function (e) {
        e.preventDefault();
    });
    $(document).ready(function () {

        $('#frmGarage')
            .formValidation({
                framework: 'bootstrap',
                excluded: 'disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {

                    status: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select'
                            }
                        }
                    },
                    gargName: {
                        validators: {
                            remote: {
                                message: 'The Garage Name Already Exist',
                                method: 'POST',
                                url: contextPath + "/GarageListController/validateGarageName",
                            },
                            notEmpty: {
                                message: 'Please Enter Garage Name'
                            }
                        }
                    },
                }
            })
    })
        .on('success.form.fv', function (e) {
            $(e.target).data('formValidation').disableSubmitButtons(false);

        });


</script>
</body>
</html>
