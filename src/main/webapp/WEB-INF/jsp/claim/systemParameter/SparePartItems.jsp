<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Pruthuvi
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmClaimPanelUserList" id="frmClaimPanelUserList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Spare Part Items</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                   data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="sparePartsName" class="col-md-4 col-form-label">Spare Part
                                                Name</label>
                                            <div class="col-md-8">
                                                <input name="sparePartsName" id="sparePartsName"
                                                       class="form-control form-control-sm"
                                                       placeholder="Spare Part Name">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="recordStatus" class="col-sm-4 col-form-label"> Record
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="recordsStatus" id="recordsStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">All</option>
                                                    <option value="A">Active</option>
                                                    <option value="R">Remove</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch"
                                                id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mt-2">
                            <h6 class="float-left">Result</h6>
                            <!-- Button trigger modal -->
                            <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                                    data-target="#panelUser" name="addButton" id="addButton">
                                Add Spare Parts
                            </button>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>ref No</th>
                                <th>No</th>
                                <th>Spare Part Name</th>
                                <th>Record Status</th>
                                <th>Input User Id</th>
                                <th>Input Date Time</th>
                                <th class="min-mobile"></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <!-- Modal -->
    <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>Add New Spare Part</h5>
                </div>
                <div class="modal-body">
                    <form name="frmSparePartItem" id="frmSparePartItem" method="post">
                        <input type="hidden" name="sparePartRefNo" id="sparePartRefNo"/>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Spare Part Name</label>
                            <div class="col-sm-8">
                                <input id="sparePartName" name="sparePartName"
                                       class="form-control form-control-sm">
                            </div>

                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Record Status</label>
                            <div class="col-sm-8">
                                <select class="form-control form-control-sm" id="recordStatus" name="recordStatus">
                                    <option value="">Please Select</option>
                                    <option value="A">Active</option>
                                    <option value="R">Remove</option>
                                </select>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Input User Id</label>
                            <div class="col-sm-8">
                                <input id="inputUserId" name="inputUserId"
                                       class="form-control form-control-sm" readonly>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-4 col-form-label">Input Date Time</label>
                            <div class="col-sm-8">
                                <input id="inputDateTime" name="inputDateTime"
                                       class="form-control form-control-sm" readonly></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="CloseReload()">
                                Close
                            </button>
                            <button type="button" id="addSparePartbtn" class="btn btn-primary">Save changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/sparePartsIrem-datatables.js?v6"></script>
<script type="text/javascript">
    function CloseReload() {
        location.reload();
    }

    $('#addSparePartbtn').click(function (e) {
        var formData = $('#frmSparePartItem').serialize();
        e.preventDefault();
        $.ajax({
            url: contextPath + "/SparePartItemController/saveSparePartItem",
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    notify(obj, "success");
                    closeModal();
                } else {
                    notify("Fail to Add User", "fail");
                    closeModal();
                }
            }
        });
    });

    function closeModal() {
        $('#panelUser').modal('hide');
        $('#sparePartRefNo').val('');
        $('#sparePartName').val('');
        $('#recordStatus').val('');
        $('#inputUserId ').val('');
        $('#inputDateTime').val('');
        search();
    };

    $("#addSparePartbtn").submit(function (e) {
        e.preventDefault();
    });
    $(document).ready(function () {

        $('#frmSparePartItem')
            .formValidation({
                framework: 'bootstrap',
                excluded: 'disabled',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {

                    recordStatus: {
                        validators: {
                            notEmpty: {
                                message: 'Please Select'
                            }
                        }
                    },
                    sparePartName: {
                        validators: {
                            remote: {
                                message: 'The Spare Part Name Already Exist',
                                method: 'POST',
                                url: contextPath + "/SparePartItemController/validateSparePartName",
                            },
                            notEmpty: {
                                message: 'Please EnterSpare Part Name'
                            }
                        }
                    },
                }
            }).on('success.form.fv', function (e) {
            $(e.target).data('formValidation').disableSubmitButtons(false);

        });

    })
</script>

</body>
</html>
