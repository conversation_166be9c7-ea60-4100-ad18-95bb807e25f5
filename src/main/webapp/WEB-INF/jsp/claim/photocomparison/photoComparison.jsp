<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Image Upload</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <script src="${pageContext.request.contextPath}/resources/js/imagePre.js"></script>
    <script>
        $(document).ready(function () {
            $('[data-toggle="tooltip"]').tooltip({
                html: true,
                template: '<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
            });
        });

        function pageSubmit() {
           var form= document.getElementById("frmDocumentUpload");
           form.action="${pageContext.request.contextPath}/PhotoComparisonController/viewComparisonViewer";
           form.submit();
        }

    </script>
    <c:set var="photoComparisionDto" value="${photoComparisionDto}"/>
    <c:set var="claimDocumentDtoList" value="${photoComparisionDto.claimDocumentDtoList}"/>
    <c:set var="claimImageDtoList" value="${photoComparisionDto.claimImageDtoList}"/>
</head>
<body class="scroll">
<form name="frmDocumentUpload" id="frmDocumentUpload" method="post">
    <input type="hidden" name="comparisionTabNo" value="${comparisionTabNo}">
    <input type="hidden" name="vehicleNumber" value="${vehicleNumber}">
    <input type="hidden" name="policyNumber" value="${policyNumber}">
    <div class="container-fluid">
        <div class="row">
            <fieldset class="col-md-12 border">
                <div class="form-group row mt-2">
                    <label class="col-sm-4 col-form-label disable">Select Claim Number:</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable " name="claimNo" id="claimNo" onchange="pageSubmit();">
                            <option value="0">Please Select one</option>
                            <c:forEach var="popupItem" items="${photoComparisionDto.claimList}">
                                <option value="${popupItem.value}">${popupItem.label}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Select Comparision Type :</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable" id="comparisionType" name="comparisionType" onchange="pageSubmit();">
                            <option value="0">Please Select one</option>
                            <c:forEach var="comparisionType" items="${photoComparisionDto.comperisionTypeList}">
                                <option value="${comparisionType}">${comparisionType}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Select Inspection Type :</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable " id="inspectionJobNo" name="inspectionJobNo" onchange="pageSubmit();">
                            <option value="0">All</option>
                            <c:forEach var="claimInspectionType" items="${photoComparisionDto.claimInspectionTypeList}">
                                <option value="${claimInspectionType.jobNo}">${claimInspectionType.inspectionTypeDesc}
                                    - (${claimInspectionType.jobNo})
                                </option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label disable">Select Document Type:</label>
                    <div class="col-sm-8">
                        <select class=" form-control form-control-sm disable " name="documentType" id="documentType" onchange="pageSubmit();">
                            <c:if test="${comparisionType=='Document' || comparisionType=='0'}">
                                <option value="0">All</option>
                            </c:if>
                            <c:forEach var="claimDocumentType" items="${photoComparisionDto.claimDocumentTypeList}">
                                <option value="${claimDocumentType.documentTypeId}">${claimDocumentType.documentTypeName}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-12" id="photo">

                        <c:set var="cnt" value="1"/>
                        <c:forEach var="claimImageDto" items="${claimImageDtoList}">
                            <div class="uploadfile-delet imgupload">
                                <figure>
                                    <a data-magnify="gallery" data-caption="Vehicle Photo"
                                       href="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}"
                                       data-toggle="tooltip" title="${claimImageDto.toolTip}"
                                       class="preview float-none">
                                        <img class="magnify-thumb" id="vehicleImage${claimImageDto.refNo}" src="${pageContext.request.contextPath}/ImageThumbViewController?refNo=${claimImageDto.refNo}" alt="" width="100" height="95" alt="" border="0" onclick="selectImage(${claimImageDto.refNo})">
                                    </a>
                                    <figcaption>${index}</figcaption>
                                    <div class="row">
                                        <div class="col text-center">
                                            <a href="${pageContext.request.contextPath}/DocumentDownloadController?refNo=${claimImageDto.refNo}"
                                               class="mt-2 pointer" title="Download" style="height: auto;"><i
                                                    class="fa fa-download text-success"></i></a>
                                        </div>
                                    </div>
                                </figure>
                            </div>
                        </c:forEach>
                    </div>
                    <div class="col-12" id="docuent">
                        <c:forEach var="claimDocumentDto" items="${claimDocumentDtoList}">
                            <c:set var="iconColorCls" value=" text-dark "/>
                            <c:if test="${claimDocumentDto.documentStatus=='A'}">
                                <c:set var="iconColorCls" value=" text-success"/>
                            </c:if>
                            <c:if test="${claimDocumentDto.documentStatus=='H'}">
                                <c:set var="iconColorCls" value=" text-warning"/>
                            </c:if>
                            <c:if test="${claimDocumentDto.documentStatus=='R'}">
                                <c:set var="iconColorCls" value=" text-danger"/>
                            </c:if>
                            <c:if test="${claimDocumentDto.documentStatus=='C'}">
                                <c:set var="iconColorCls" value=" text-info"/>
                            </c:if>
                            <a href="${pageContext.request.contextPath}/PhotoComparisonController/documentViewer?refNo=${claimDocumentDto.refNo}&jobRefNo=${claimDocumentDto.jobRefNo}&comparisionTabNo=${comparisionTabNo}"
                               class="preview ${iconColorCls}">
                                <span><i class="fa fa-file-pdf-o fa-4x m-3 " id="documentView${claimDocumentDto.refNo}"></i></span>

                            </a>
                            <%--<figcaption>Fig.1 - Trulli, Puglia, Italy.</figcaption>--%>
                            <label>${claimDocumentDto.claimDocumentTypeDto.documentTypeName}</label>
                        </c:forEach>
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
</form>
<script src="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.js"></script>
<script>
    $('[data-magnify]').magnify({
        fixedContent: false,
        initMaximized: true,
        fixedModalPos: true
    });

    $(function () {
        $('#docuent').hide();
        $('#photo').hide();
        $('#comparisionType').change(function () {
            if ($('#comparisionType').val() == 'Photo') {
                $('#docuent').hide();
                $('#photo').show();
            }else{
                $('#photo').hide();
                $('#docuent').show();
            }
        });

        setPopupValues();
    });

    function setPopupValues() {
        $('#claimNo').val("${claimNo}");
        $('#comparisionType').val("${comparisionType}");
        $('#inspectionJobNo').val("${inspectionJobNo}");
        $('#documentType').val("${documentType}");

        if ("Photo" == '${comparisionType}') {
            $('#docuent').hide();
            $('#photo').show();
        }else if ("Document" == '${comparisionType}'){
            $('#photo').hide();
            $('#docuent').show();
        }
    }
</script>
</body>
</html>
