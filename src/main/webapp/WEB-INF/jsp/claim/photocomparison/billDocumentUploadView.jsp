<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<script>
    $(document).ready(function () {
        $('[data-toggle="tooltip"]').on('click', function(){$(this).tooltip('hide');}).tooltip({
            html: true,
            placement:"right",
            trigger: 'hover',
            template: '<div class="tooltip" role="tooltip" data-trigger="hover"><div class="arrow"></div><div class="tooltip-inner" data-placement=right"></div></div>'
        });
    });
</script>

<div class="container-fluid">
    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="claimUploadViewDtoList" value="${claimUploadViewDtoList}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="claimUploadViewDtoList" value="${historyClaimUploadViewDtoList}" scope="request"/>
        </c:when>
    </c:choose>
    <div class="row">
        <c:set var="documentTypeId" scope="request" value="0"/>
        <c:set var="index" scope="request" value="1"/>
        <c:forEach var="claimUploadViewDto" items="${claimUploadViewDtoList}">
            <%--<c:if test="${claimUploadViewDto.claimDocumentTypeDto.isMandatory eq 'Y'}">--%>
            <c:if test="${documentTypeId!=claimUploadViewDto.claimDocumentTypeDto.documentTypeId}">
                <c:choose>
                    <c:when test="${claimUploadViewDto.claimDocumentTypeDto.isMandatory=='Y'}">
                        <c:set var="iconColorCls" value=" text-light "/>
                        <c:set var="headerColorCls" value=" bg-badge-danger "/>
                    </c:when>
                    <c:otherwise>
                        <c:set var="iconColorCls" value=" text-primary "/>
                        <c:set var="headerColorCls" value=" bg-badge-primary "/>
                    </c:otherwise>
                </c:choose>
                <fieldset class="col-md-12 border mt-2">
                <div class="row">

                <div class="col-sm-12 col-form-label ${headerColorCls} ">
                    <h6 class="float-left">${index}
                        - ${claimUploadViewDto.claimDocumentTypeDto.documentTypeName}</h6>
                    <c:if test="${PREVIOUS_INSPECTION != 'Y'}">
                        <button id="cmdUpload" type="button" name="cmdUpload" class="btn btn-primary pull-right"
                                onclick="docBillUpload(${claimUploadViewDto.claimDocumentTypeDto.documentTypeId})">
                            Upload Here
                        </button>
                    </c:if>
                </div>
                <c:set var="index" scope="request" value="${index+1}"/>
                <c:set var="claimDocumentTypeDto" value="${claimUploadViewDto.claimDocumentTypeDto}"/>
                <form name="frmDocumentBillModal${claimDocumentTypeDto.documentTypeId}"
                      id="frmDocumentBillModal${claimDocumentTypeDto.documentTypeId}">
                    <input type="hidden" name="documentTypeId"
                           value="${claimDocumentTypeDto.documentTypeId}">
                    <input type="hidden" name="claimNo" value="${claimUploadViewDto.claimNo}">
                    <input type="hidden" name="jobRefNo" value="0">
                    <input type="hidden" name="departmentId" value="5">
                    <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
                         id="docBillUploadModal${claimDocumentTypeDto.documentTypeId}" aria-hidden="true"
                         style="    background: #333333c2;">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content p-2" style="overflow: hidden">
                                <div class="modal-header  p-2">
                                    <h6 class="modal-title"
                                        id="modalBillLabel${claimDocumentTypeDto.documentTypeId}">${claimDocumentTypeDto.documentTypeName} </h6>
                                    <small class="text-danger pull-right"><b> .PNG / .JPG / .PDF File Formats
                                        Only.</b>
                                    </small>
                                </div>
                                <p id="errorBillUpload${claimDocumentTypeDto.documentTypeId}"></p>
                                <div class=" mt-4">
                                    <div class="col-sm-12">
                                        <!-- The fileinput-button span is used to style the file input field as button -->
                                        <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                            <!-- The file input field used as target for the file upload widget -->
                                        <input id="fileBillUploadClaim${claimDocumentTypeDto.documentTypeId}"
                                               type="file"
                                               name="files[]" multiple>
                                    </span>
                                        <!-- The global progress bar -->
                                        <div id="progressBillClaim${claimDocumentTypeDto.documentTypeId}"
                                             class="progress">
                                            <div class="progress-bar bg-success"></div>
                                        </div>
                                        <!-- The container for the uploaded files -->
                                        <div id="filesBillClaim${claimDocumentTypeDto.documentTypeId}"
                                             class="files"></div>
                                        <br>
                                    </div>
                                </div>
                                <div class="modal-footer p-1">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                            onclick="loadBillUploadView();loadDocumentUploadView();loadEngineeringDocuments();">
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
                <script>
                    documentBillUploadIds.push('${claimDocumentTypeDto.documentTypeId}');
                </script>

                <div class="col-md-12">
                <hr class="my-2">
            </c:if>
            <c:set var="cnt" value="1"/>
            <c:forEach var="claimDocumentDto" items="${claimUploadViewDto.claimDocumentDtoList}">
                <c:set var="iconColorCls" value=" text-dark "/>
                <c:if test="${claimDocumentDto.documentStatus=='A'}">
                    <c:set var="iconColorCls" value=" text-success"/>
                </c:if>
                <c:if test="${claimDocumentDto.documentStatus=='H'}">
                    <c:set var="iconColorCls" value=" text-warning"/>
                </c:if>
                <c:if test="${claimDocumentDto.documentStatus=='R'}">
                    <c:set var="iconColorCls" value=" text-danger"/>
                </c:if>
                <c:if test="${claimDocumentDto.documentStatus=='C'}">
                    <c:set var="iconColorCls" value=" text-info"/>
                </c:if>
                <div class="uploadfile-delet">
                    <a href="${pageContext.request.contextPath}/PhotoComparisonController/viewBillDocumentViewer?refNo=${claimDocumentDto.refNo}&jobRefNo=${claimDocumentDto.jobRefNo}&PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}&ENG_DOC=${ENG_DOC}&PENDING_INSPEC=${pendingInspection}"
                       class="billView${claimDocumentDto.refNo} ${iconColorCls}"
                       data-toggle="tooltip" title="${claimDocumentDto.toolTip}">
                        <i class="fa fa-file-pdf-o fa-4x m-3"></i>
                    </a>
                </div>
                <script type="text/javascript">
                    $('.billView${claimDocumentDto.refNo}').popupWindow({
                        height: screen.height,
                        width: screen.width,
                        centerBrowser: 0,
                        left: 0,
                        resizable: 1,
                        centerScreen: 1,
                        scrollbars: 1,
                        windowName: 'claimBillHandler${claimDocumentDto.refNo}'
                    });
                </script>

            </c:forEach>

            <c:if test="${documentTypeId!=claimUploadViewDto.claimDocumentTypeDto.documentTypeId}">
                </div>
                </div>
                </fieldset>
            </c:if>
            <c:set var="documentTypeId" scope="request"
                   value="${claimUploadViewDto.claimDocumentTypeDto.documentTypeId}"/>
            <%--</c:if>--%>
        </c:forEach>
    </div>
</div>


<script>


    function docBillUpload(documentTypeId) {
        $('#docBillUploadModal' + documentTypeId).modal({
            backdrop: 'static',
            keyboard: false,
            refresh: true,
            show: true
        });
    }

    function documentBillFileUploder(documentTypeId) {
        'use strict';
        var progress = 0;
        var url = '${pageContext.request.contextPath}/DocumentUploadController';
        $('#fileBillUploadClaim' + documentTypeId).fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {
                data.submit()
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<i class="fa fa-file-pdf-o fa-4x m-3"></i>').appendTo('#filesBillClaim' + documentTypeId);
                });
                $('#errorBillUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorBillUpload' + documentTypeId).removeClass("bg-danger");
                $('#errorBillUpload' + documentTypeId).addClass("bg-success");
                $('#errorBillUpload' + documentTypeId).html("");
                $('#errorBillUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Document Uploaded Successfully!</span>').appendTo('#errorBillUpload' + documentTypeId);
                $('#errorBillUpload' + documentTypeId).fadeOut(4000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);

                $('#progressBillClaim' + documentTypeId + ' .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#errorBillUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorBillUpload' + documentTypeId).removeClass("bg-success");
                $('#errorBillUpload' + documentTypeId).addClass("bg-danger");
                $('#errorBillUpload' + documentTypeId).html("");
                $('#errorBillUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center">Document Upload failed.</span>').appendTo('#errorBillUpload' + documentTypeId);
                $('#errorBillUpload' + documentTypeId).fadeOut(4000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {

                    $('#progressBillClaim' + documentTypeId + ' .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#errorBillUpload' + documentTypeId).removeClass("bg-primary");
                    $('#errorBillUpload' + documentTypeId).removeClass("bg-danger");
                    $('#errorBillUpload' + documentTypeId).removeClass("bg-success");

                    $('#errorBillUpload' + documentTypeId).addClass("bg-primary");
                    $('#errorBillUpload' + documentTypeId).html("");
                    $('#errorBillUpload' + documentTypeId).fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#errorBillUpload' + documentTypeId);


                });
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    }

    documentBillUploadIds.forEach(function (t) {
        documentBillFileUploder(t)
    });


</script>
<%--</body>
</html>--%>
