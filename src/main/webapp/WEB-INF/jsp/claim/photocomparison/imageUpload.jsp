<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>


<div class="scroll">
    <script src="${pageContext.request.contextPath}/resources/js/imagePre.js"></script>

    <form name="frmImageUpload" id="frmImageUpload" method="post">
        <div class="container-fluid">
            <div class="row">
                <fieldset class="col-md-12 border mt-2">
                    <c:forEach var="claimImageFormDto" items="${claimImageFormDtoList}">
                        <c:set var="claimImageDtoList" value="${claimImageFormDto.claimImageDtoList}" scope="request"/>
                        <div class="row">
                            <div class="col-sm-12 col-form-label">
                                <h6 class="float-left">[${claimImageFormDto.inspectionType}] - Vehicle Image</h6>
                            </div>
                            <div class="col-md-12">
                                <hr class="my-2">
                                <c:set var="cnt" value="1"/>
                                <c:forEach var="claimImageDto" items="${claimImageDtoList}">
                                    <div class="uploadfile-delet imgupload">
                                        <figure>
                                            <figcaption>${cnt}</figcaption>
                                            <c:set var="cnt" value="${cnt=cnt+1}"/>
                                        </figure>
                                        <a data-magnify="gallery" data-caption="Vehicle Photo"
                                           href="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}"
                                           data-toggle="tooltip" title="" class="preview float-none">
                                            <img class="magnify-thumb" id="vehicleImage${claimImageDto.refNo}"
                                                 src="${pageContext.request.contextPath}/ImageThumbViewController?refNo=${claimImageDto.refNo}"
                                                 alt="" width="100%" height="95" alt="" border="0">


                                        </a>
                                        <div class="row">
                                            <div class="col">
                                                <label class="custom-control custom-checkbox float-left col-form-label check-container"
                                                       style="right: 8%; bottom: auto;">
                                                    <input id="intendClaim_N" name="intendClaim"
                                                           onclick="setDeleteImages('${claimImageDto.refNo}')"
                                                           type="checkbox"
                                                           class="align-middle"/>
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                            <div class="col">
                                                <a href="${pageContext.request.contextPath}/DocumentDownloadController?refNo=${claimImageDto.refNo}"
                                                   class="mt-2 pointer" title="Download"
                                                   style="width: auto; height: auto;"><i
                                                        class="fa fa-download text-success"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </c:forEach>
                            </div>
                        </div>
                    </c:forEach>
                </fieldset>
            </div>
        </div>
    </form>

    <script src="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.js"></script>
    <script>
        $('[data-magnify]').magnify({
            fixedContent: false,
            initMaximized: false,
            fixedModalPos: true
        });


        function docUpload() {
            $(document.getElementById('imgUploadModal')).modal({
                backdrop: 'static',
                keyboard: false,
                refresh: true,
                show: true
            });
        }

        function setDeleteImages(refId) {
            var images = $('#deleteImages').val();
            if (images == '') {
                images = refId;
            } else {
                images = images + ',' + refId;
            }

            $('#deleteImages').val(images);

        }

        function deleteSelectedImages() {
            var dataObj = {
                deleteImages: $("#deleteImages").val()
            };

            var URL = "${pageContext.request.contextPath}/MotorEngineerController/deleteImages";
            $.ajax({
                url: URL,
                type: 'POST',
                data: dataObj,
                success: function (result) {
                    var messageType = JSON.parse(result);
                    var message = "";
                    if (messageType == "SUCCESS") {
                        message = "Successfully deleted";
                        notify(message, "success");
                    } else {
                        message = "Can not be deleted";
                        notify(message, "danger");
                    }
                    loadImageUploadViewer();

                }
            });

        }
    </script>
</div>

