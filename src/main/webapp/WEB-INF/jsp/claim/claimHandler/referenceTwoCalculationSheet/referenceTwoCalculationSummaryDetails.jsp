<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<div class="col-lg-4 p-4  " style="height:380px; ">
    <ul class="list-group list-group-flush border" style="box-shadow: 1px 1px 11px 3px #dadada;">
        <li class="list-group-item p-2">
            <span class="float-left">Approved Replacement Amount</span>
            <span id="partsLabel" class="label_Value float-right">
                                0.00
                            </span>
            <input type="hidden" id="parts" name="parts" value="0"/>
        </li>
        <li class="list-group-item p-2">
            <span class="float-left">Approved Labour Amount</span>
            <span id="labourLabel" class="label_Value float-right">
                                0.00
                            </span>
            <input type="hidden" id="labour" name="labour" value="0"/>
        </li>

        <li class="list-group-item p-2 bg-badge-danger">
            <span class="float-left">Total VAT Amount</span>
            <span id="totalVatAmount" class="label_Value float-right">0.00</span>
        </li>
        <li class="list-group-item p-2 bg-badge-danger">
            <span class="float-left">Total NBT Amount</span>
            <span id="totalNbtAmount" class="label_Value float-right">0.00</span>
        </li>

        <li class="list-group-item p-2 bg-badge-danger">
            <span class="float-left">Total OA Amount</span>
            <span id="totalOaAmount" class="label_Value float-right">0.00</span>
        </li>

        <li class="list-group-item p-2 bg-badge-warning">
            <span class="float-left">Special VAT Amount</span>
            <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                   id="specialVatAmount" name="specialVatAmount"
                   value="${ACTION eq 'SAVE' ? '0.00' : claimCalculationSheetMainDto.specialVatAmount}" disabled/>
        </li>

        <li class="list-group-item p-2 bg-badge-warning">
            <div class="float-left">
                <input type="checkbox" id="isAdjustVatAmount" style="margin-right: 10px;"
                ${'Y' ==claimCalculationSheetMainDto.isAdjustVatAmount?'checked':''}>
                <span>Adjust VAT Amount</span>
            </div>
            <div>
                <input type="hidden" name="isAdjustVatAmount" id="hiddenIsAdjustVatAmount"
                       value="${claimCalculationSheetMainDto.isAdjustVatAmount}">
                <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                       id="adjustVatAmount" name="adjustVatAmount"
                       value="${null == claimCalculationSheetMainDto.adjustVatAmount? '0.00':claimCalculationSheetMainDto.adjustVatAmount}"
                ${claimCalculationSheetMainDto.isAdjustVatAmount=='Y'?'':'readonly'}/>
            </div>

            <script>
                $('#isAdjustVatAmount').change(function () {
                    if ($('#isAdjustVatAmount').prop('checked')) {
                        $('#hiddenIsAdjustVatAmount').val('Y');
                        document.getElementById("adjustVatAmount").readOnly = false;
                    } else {
                        $('#hiddenIsAdjustVatAmount').val('N');
                        document.getElementById("adjustVatAmount").readOnly = true;
                    }
                });
            </script>
        </li>
        <li class="list-group-item p-2 bg-badge-warning">
            <span class="float-left">Special NBT Amount</span>
            <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                   id="specialNbtAmount" name="specialNbtAmount"
                   value="${ACTION eq 'SAVE' ? '0.00' : claimCalculationSheetMainDto.specialNbtAmount}" disabled/>
        </li>

        <li class="list-group-item p-2 bg-badge-warning">
            <div class="float-left">
                <input type="checkbox" id="isAdjustNbtAmount" style="margin-right: 10px;"
                ${'Y' ==claimCalculationSheetMainDto.isAdjustNbtAmount?'checked':''}>
                <span>Adjust NBT Amount</span>
            </div>
            <div>
                <input type="hidden" name="isAdjustNbtAmount" id="hiddenIsAdjustNbtAmount"
                       value="${claimCalculationSheetMainDto.isAdjustNbtAmount}">

                <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                       id="adjustNbtAmount" name="adjustNbtAmount"
                       value="${null ==claimCalculationSheetMainDto.adjustNbtAmount? '0.00':claimCalculationSheetMainDto.adjustNbtAmount}"
                ${claimCalculationSheetMainDto.isAdjustNbtAmount=='Y'?'':'readonly'}/>
            </div>
            <script>
                $('#isAdjustNbtAmount').change(function () {
                    if ($('#isAdjustNbtAmount').prop('checked')) {
                        $('#hiddenIsAdjustNbtAmount').val('Y');
                        document.getElementById("adjustNbtAmount").readOnly = false;
                    } else {
                        $('#hiddenIsAdjustNbtAmount').val('N');
                        document.getElementById("adjustNbtAmount").readOnly = true;
                    }
                });

            </script>
        </li>

        <li class="list-group-item p-2 bg-badge-succes">
            <span class="float-left">Total </span>
            <span id="totalLabourAndPartsLabel" class="label_Value float-right">
                                0.00
                            </span>
            <input type="hidden" id="totalLabourAndParts" name="totalLabourAndParts" value="0"/>
        </li>
    </ul>
</div>
<div class=" col-lg-4 p-4  " style="height: 263px;">
    <ul class="list-group list-group-flush border" style="box-shadow: 1px 1px 11px 3px #dadada;">
        <li class="list-group-item p-2">
            <span class="float-left mr-4">Policy Excess</span>

            <input name="isExcessInclude" type="radio" value="Y" id="excheck1"
                   onclick="policyExcessAdd();"
                   class="align-middle" ${claimCalculationSheetMainDto.isExcessInclude eq 'Y'?'checked':''}/>Yes

            <input name="isExcessInclude" type="radio" value="N" id="excheck2"
                   onclick="policyExcessZero();"
                   class="align-middle" ${claimCalculationSheetMainDto.isExcessInclude eq 'N'?'checked':''}/> No

            <span class="label_Value float-right">
                                     <fmt:formatNumber
                                             value="${claimHandlerDto.claimsDto.policyDto.excess}"
                                             pattern="###,##0.00;"
                                             type="number"/></span>
            <input type="hidden" id="policyExcess" name="policyExcess"
                   value="${claimCalculationSheetMainDto.policyExcess}"/>

        </li>
        <c:if test="${IS_EXCESS_ALREADY_APPLY eq 'Y'}">
            <script type="text/javascript">
                $("#excheck1").attr('disabled', 'disabled');
                $("#excheck2").attr('disabled', 'disabled');
            </script>
            <%--<input type="hidden" id="policyExcess" name="policyExcess"
                   value="0.00"/>--%>
            <input type="hidden" name="isExcessInclude" value="N"/>
        </c:if>

        <li class="list-group-item p-2">
            <span class="float-left">Under Insurance</span>
            <span id="underInsuranceLabel" class="label_Value float-right">
                                0.00
                            </span>
            <input type="hidden" id="underInsurance" name="underInsurance" value="0"/>
            <span class="label_Value float-right text-warning"
                  id="underInsuranceRateLabel">${claimHandlerDto.penaltyUnderInsurceRate}%</span>
            <input class="form-control form-control-sm float-right w-25 text-right" type="text"
                   id="underInsuranceRate" name="underInsuranceRate"
                   value="${ACTION eq 'SAVE' ? claimHandlerDto.penaltyUnderInsurceRate : claimCalculationSheetMainDto.underInsuranceRate}"/>
        </li>
        <li class="list-group-item p-2">
            <span class="float-left">Bald Tyre / Other Penalty</span>
            <span id="baldTyreLabel" class="label_Value float-right">
                                0.00
                            </span>
            <input type="hidden" id="baldTyre" name="baldTyre" value="0"/>
            <span class="label_Value float-right text-warning" id="baldTyreRateLabel"
                  name="baldTyreRateLabel">${claimHandlerDto.penaltyBaldTyreRate}%</span>
            <input class="form-control form-control-sm float-right w-25 text-right" type="text"
                   name="baldTyreRate" id="baldTyreRate"
                   value="${ACTION eq 'SAVE' ? claimHandlerDto.penaltyBaldTyreRate : claimCalculationSheetMainDto.baldTyreRate}"/>
        </li>
        <li class="list-group-item p-2 bg-badge-warning">
            <span class="float-left">Special Deductions</span>
            <input class="form-control form-control-sm w-50 float-right text-right" type="text"
                   id="specialDeductions" name="specialDeductions"
                   value="${ACTION eq 'SAVE' ? '0.00' : claimCalculationSheetMainDto.specialDeductions}"/>
        </li>

        <li class="list-group-item p-2 bg-badge-warning">
            <span class="float-left">Total Deductions</span>
            <span id="totalDeductionsLabel" class="label_Value float-right">
                                0.00
                            </span>
            <input type="hidden" id="totalDeductions" name="totalDeductions" value="0"/>
        </li>
        <li class="list-group-item p-2 bg-badge-succes">
            <span class="float-left">Total</span>
            <span id="totalAfterDeductionsLabel" class="label_Value float-right">
                                0.00
                            </span>
            <input type="hidden" id="totalAfterDeductions" name="totalAfterDeductions" value="0"/>
        </li>
    </ul>
</div>
<div class="col-lg-4 p-4">
    <ul class="list-group p-2" style="box-shadow: 1px 1px 11px 3px #dadada;">
        <c:forEach items="${advanceListForClaim}" var="item">
            <c:choose>
                <c:when test="${65 eq item.status}">
                    <li class="list-group-item p-2 bg-badge-danger">
                        <span class="float-left">Paid Advance Amount - ${item.calSheetId}<br> Approved And Voucher Generated Pending</span>
                        <span class="float-left"> </span>
                        <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                        <fmt:formatNumber
                                                value="${item.payableAmount}"
                                                pattern="###,##0.00;"
                                                type="number"/>
                                    </span>
                    </li>
                </c:when>
                <c:when test="${67 eq item.status}">
                    <li class="list-group-item p-2 bg-badge-succes">
                        <span class="float-left">Paid Advance Amount - ${item.calSheetId}<br>  Approved And Voucher Generated</span>
                        <span class="float-left"> </span>
                        <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                        <fmt:formatNumber
                                                value="${item.payableAmount}"
                                                pattern="###,##0.00;"
                                                type="number"/>
                                    </span>
                    </li>
                </c:when>
                <c:otherwise>
                    <li class="list-group-item p-2 bg-badge-warning">
                        <span class="float-left">Paid Advance Amount - ${item.calSheetId}<br> Approve Pending</span>
                        <span class="float-left"> </span>
                        <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                        <fmt:formatNumber
                                                value="${item.payableAmount}"
                                                pattern="###,##0.00;"
                                                type="number"/>
                                    </span>
                    </li>

                </c:otherwise>
            </c:choose>
        </c:forEach>
        <li class="list-group-item p-2 bg-badge-danger">
            <strong>
                <span class="float-left">Total Paid Approved Advance Amount</span>
                <span id="paidAdvanceAmountLabel" class="label_Value float-right">
                                    <fmt:formatNumber
                                            value="${totalPaidAdvanceAmountForClaim}"
                                            pattern="###,##0.00;"
                                            type="number"/>
                                </span>
            </strong>
        </li>
        <input type="hidden" id="paidAdvanceAmount" name="paidAdvanceAmount"
               value="${totalPaidAdvanceAmountForClaim}"/>
        <li class="list-group-item p-2 bg-badge-primary">
            <span class="float-left" style="font-weight: 600; font-size: 20px;">Payable Amount</span>
            <span id="payableAmountLabel" class="label_Value float-right"
                  style="font-weight: 600; font-size: 18px;">
                                0.00
                            </span>
            <input type="hidden" id="payableAmount" name="payableAmount" value="0"/>
        </li>
    </ul>
    <ul class="list-group p-2 mt-1" style="display: none">
        <li class="list-group-item p-2 bg-badge-info">
            <span class="float-left">Total Amount Paid for the Claim</span>
            <span id="totalAmountPaidForClaim" class="label_Value float-right"
                  style="font-weight: 600; font-size: 18px;">
                                <fmt:formatNumber
                                        value="${totalAmountPaidForClaim}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
        </li>
    </ul>

    <ul class="list-group p-2 mt-1" style="box-shadow: 1px 1px 11px 3px #dadada;">
        <c:if test="${SUPPLIER_DO_LIST ne null}">
            <table width="100%" cellpadding="0" cellspacing="1"
                   class="table table-hover table-xs dataTable no-footer dtr-inline  table-striped">
                <thead>
                <tr>
                    <th>Serial No</th>
                    <th>Amount</th>
                </tr>
                </thead>
                <c:forEach var="supplier" items="${SUPPLIER_DO_LIST}">
                    <tr>
                        <td>
                                ${supplier.serialNumber}
                        </td>
                        <td class="text-right">
                                ${supplier.amount}
                        </td>
                    </tr>
                </c:forEach>
            </table>
        </c:if>
    </ul>
</div>
