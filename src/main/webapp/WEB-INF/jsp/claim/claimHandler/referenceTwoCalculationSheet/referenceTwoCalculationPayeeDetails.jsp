<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<div style="padding:5px;">
    <table id="TPdata" style="width: 100%;" class="table-responsive col-lg-12">
        <thead>
        <tr>
            <th scope="col" class="tbl_row_header" width=" 45px">#</th>
            <th scope="col" class="tbl_row_header" width="120px">Payee Type</th>
            <th scope="col" class="tbl_row_header" width="220px">Name</th>
            <th scope="col" class="tbl_row_header" width="100px">Amount</th>
            <th scope="col" class="tbl_row_header" width=" 100px">EFT</th>
            <th scope="col" class="tbl_row_header" width=" 100px">Account No</th>
            <th scope="col" class="tbl_row_header" width=" 100px">Bank Name</th>
            <th scope="col" class="tbl_row_header" width=" 20px">Bank Code</th>
            <th scope="col" class="tbl_row_header" width=" 100px">Branch</th>
            <th scope="col" class="tbl_row_header" width=" 40px">Contact No</th>
            <th scope="col" class="tbl_row_header" width=" 80px">E-mail address</th>
            <th scope="col" class="tbl_row_header" width=" 100px">Voucher No</th>
            <th scope="col" class="tbl_row_header" width=" 100px">Check Dispatch Location</th>
            <th scope="col" class="tbl_row_header" width=" 40px">Action</th>
        </tr>
        </thead>
        <tbody id="tblBody3">
        <c:set var="payeeUniqueTableName" value="3000"/>
        <c:set var="payeeCount" value="0"/>
        </tbody>
    </table>
    <table class="table table-bordered tablestyle table-responsive" style="width: 100%;">
        <thead>
        <tr style="border-top: 2px solid #dee2e6;">
            <th scope="col" class="tbl_row_header colorwhite" width=" 45px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="260px">Total Amount :</th>
            <th id="totalPayeeAmount" scope="col" class="tbl_row_header colorwhite" width="250px">
                <input id="totalPayeeAmountValue" type="hidden"/>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="150px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 100px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 50px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 20px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 20px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 20px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 50px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 50px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 150px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 150px"></th>
            <th scope="col" class="tbl_row_header colorwhite" width=" 50px">
                <div class="input-group-btn float-right pr-1">
                    <button class="btn btn-primary" type="button" id="cmdPayee"><i class="fa fa-plus"></i></button>
                </div>
            </th>
        </tr>
        </thead>
        <tbody>
        </tbody>
    </table>

    <script>

        loadPayeeDetails('${claimCalculationSheetMainDto.payeeDtosJson}', '${claimCalculationSheetMainDto.branchDetailsJson}', ${payeeUniqueTableName}, '${HISTORY_RECORD}', ${claimCalculationSheetMainDto.status}, '${claimHandlerDto.claimsDto.policyDto.custName}', '${claimHandlerDto.claimNo}');
    </script>
</div>
