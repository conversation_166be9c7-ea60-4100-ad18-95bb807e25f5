<%@ page import="com.misyn.mcms.admin.admin.dto.ProductDetailListDto" %>
<%@ page import="com.misyn.mcms.claim.dto.ClaimHandlerDto" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Calculation Sheet</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <jsp:include page="/WEB-INF/jsp/claim/common/policyDetailsModal.jsp"></jsp:include>

    <script>
        var totalReplacementItem = ${claimCalculationSheetMainDto.claimCalculationSheetDetailReplacementDtos.size()};
        var totalLabourItem =${claimCalculationSheetMainDto.claimCalculationSheetDetailLabourDtos.size()};
        var totalPayeeItem = 0;
        var REPLACEMENT_INDEX = ${claimCalculationSheetMainDto.claimCalculationSheetDetailReplacementDtos.size()};
        var LABOUR_INDEX =${claimCalculationSheetMainDto.claimCalculationSheetDetailLabourDtos.size()};
        var OA_LIST = "${claimCalculationSheetMainDto.oaRateSelectItem}";
        var NBT_RATE_LIST = "${claimCalculationSheetMainDto.nbtRateSelectItem}";
        var VAT_RATE_LIST = "${claimCalculationSheetMainDto.vatRateSelectItem}";
        var PAYEE_TYPE_LIST = "${claimCalculationSheetMainDto.payeeSelectItem}";
        var TOTAL_PAID_ADVANCE_AMOUNT_FOR_CLAIM = "${totalPaidAdvanceAmountForClaim}";
        var POLICY_EXCESS = ${claimHandlerDto.claimsDto.policyDto.excess};
        var OUTSTANDING_PREMIUM = ${claimHandlerDto.claimsDto.policyDto.totPremOutstand};
        var IS_EX_GRATIA = "${IS_EX_GRATIA}";
        var CAL_SHEET_TYPE = "${claimCalculationSheetMainDto.calSheetType}";
        var PAYEE_COUNT = parseFloat('${claimCalculationSheetMainDto.claimCalculationSheetPayeeDtos.size()}');
        var CAL_SHEET_STATUS = "${claimCalculationSheetMainDto.status}";
        var HISTORY_RECORD = "${HISTORY_RECORD}";
        var SCRUTINIZE_TEAM_ASSIGN_USER = "${claimCalculationSheetMainDto.scrutinizeTeamAssignUserId}";
        var SPECIAL_TEAM_ASSIGN_USER = "${claimCalculationSheetMainDto.specialTeamAssignUserId}";
        var LOGGED_USER = "${G_USER.userId}";
        var PREM_OUT_LATEST_POLICY = "";

    </script>

    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/common/dynamically-component.js?v39"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/reference-two-calculation-sheet/calculation-sheet-component.js?v49"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/reference-two-calculation-sheet/calculation-sheet-payee-component.js?v49"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/reference-two-calculation-sheet/calculation-sheet-event.js?v47"></script>
    <%--<link rel="stylesheet" type="text/css"--%>
    <%--href="${pageContext.request.contextPath}/resources/css/bootstrap-select.min.css"/>--%>
    <%--<script type="text/javascript"--%>
    <%--src="${pageContext.request.contextPath}/resources/js/bootstrap-select.min.js"></script>--%>

    <script>
        function setName() {
            showLoader();
            $("#setName").load("${pageContext.request.contextPath}/ReferenceTwoCalculationSheetController/specialRemark?calsheetId=${claimCalculationSheetMainDto.calSheetId}");
            hideLoader();
        }
    </script>
    <style>
        #close-toolbox-tools {
            cursor: pointer;
        }

        .load {
            width: 100%;
            /* height: 100%; */
            height: 2779px;
            position: absolute;
            background-color: white;
            z-index: 99999999999999999999;
            opacity: 0.9;
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #3498db;
            width: 120px;
            height: 120px;
            -webkit-animation: spin 2s linear infinite; /* Safari */
            animation: spin 2s linear infinite;
            position: absolute;
            top: 27%;
            left: 45%;
            z-index: 999999999;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }
            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
        }

        table, th, td {
            border: 1px solid #cdcdcd;
        }

        table th, table td {
            padding: 3px;
            text-align: left;
        }

        #divContainer, #divResize {
            border: dashed 1px #CCC;
            width: 660px;
            height: 650px;
            padding: 5px;
            margin: 5px;
            font: 13px Arial;
            cursor: move;
            float: left;
            /*overflow-y: scroll;*/
            /*overflow-x: hidden*/
        }

        .parentDiv .form-group:focus-within {
            border-style: solid;
            border-width: 2px;
            border-color: #025AAA;
        }
    </style>
    <script>
        $(document).ready(function () {
            $(function () {
                $("#divResize").resizable();
                $("#divResize").draggable();
            });
        });
    </script>
</head>
<body class="scroll calcsheetjsp">
<div class="load">
    <div class="loader"></div>
</div>
<c:set var="CAL_SHEET_TYPE" value="${claimCalculationSheetMainDto.calSheetTypeList}" scope="request"/>
<c:set var="SUPPLIER_DO_LIST" value="${claimCalculationSheetMainDto.supplierOrderCalculationList}" scope="request"/>
<c:set var="claimDocumentStatusDtoNoObjection" value="${claimCalculationSheetMainDto.claimDocumentStatusNoObjectionDto}"
       scope="request"/>
<c:set var="claimDocumentStatusDtoPremiumOutstanding"
       value="${claimCalculationSheetMainDto.claimDocumentStatusPremiumOutstandingDto}" scope="request"/>
<c:set var="advanceListForClaim"
       value="${claimCalculationSheetMainDto.advanceListForClaim}" scope="request"/>
<c:set var="claimHandlerDto"
       value="${claimHandlerDto}" scope="request"/>

<form name="detailform" id="detailform" method="post">
    <input type="hidden" id="isReserveAmountExceed" name="IS_RESERVE_AMOUNT_EXCEED" value="N">
    <input type="hidden" id="deleteDoc" name="deleteDoc"/>
    <input type="hidden" id="documentTypeId" name="documentTypeId"/>
    <input type="hidden" name="ACTION" value="${ACTION}"/>
    <input type="hidden" name="P_CLAIM_NO" value="${claimHandlerDto.claimNo}"/>
    <input type="hidden" name="P_CAL_SHEET_ID" value="${claimCalculationSheetMainDto.calSheetId}"/>
    <input type="hidden" name="payableDiff" id="payableDiff"/>
    <input type="hidden" name="isPayableAmount" id="isPayableAmount" value="Y"/>
    <input type="hidden" name="payableAdvanced" id="payableAdvanced"/>
    <input type="hidden" name="totalPayeeAmountValue" id="totalPayeeAmountValue"/>
    <input type="hidden" name="payeeName" id="payeeName" value="${claimHandlerDto.claimsDto.policyDto.custName}"/>
    <input type="hidden" name="rteRemark" id="rteRemark"/>
    <input type="hidden" name="isReturn" id="isReturn"/>
    <div class="container-fluid">
        <div class="row header-bg mb-2 py-2 bg-dark align-items-center" id="status">
            <div class="col-sm-7">
                <h5 class="hide-sm m-0">Calculation Sheet </h5>
                <div class="clearfix"></div>
            </div>
            <div id="claimStampContainer" class="stamp-container imagealignment">
                <c:choose>
                    <c:when test="${claimCalculationSheetMainDto.status eq '67'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/paymen_vouchert_generated.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:when test="${claimCalculationSheetMainDto.status eq '66'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/reject_document.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:when test="${claimCalculationSheetMainDto.status eq '65'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/payment_approved.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:when test="${claimCalculationSheetMainDto.status eq '70'}">
                        <img src="${pageContext.request.contextPath}/resources/stamps/paymen_voucher_pending.png"
                             class="ml-3" width="100"
                             height="100">
                    </c:when>
                    <c:otherwise>

                    </c:otherwise>
                </c:choose>

            </div>
            <c:if test="${not empty claimHandlerDto.claimsDto.policyDto.financeCompany}">
                <div class="col-sm-2 ">
                    <h6 class="text-muted  m-0">
                        <small>Leasing Company</small>
                    </h6>
                    <h5 class="text-info">${claimHandlerDto.claimsDto.policyDto.financeCompany}</h5>
                </div>
            </c:if>
        </div>
        <fieldset class=" border p-2">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label for="insuredName" class="col-sm-4 col-form-label">Insured Name
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Insured Name"
                                   name="insuredName" id="insuredName"
                                   value="${claimHandlerDto.claimsDto.policyDto.custName}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="vehicleNo" class="col-sm-4 col-form-label"> Vehicle No
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Vehicle No"
                                   name="vehicleNo" id="vehicleNo" value="${claimHandlerDto.claimsDto.vehicleNo}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="claimNo" class="col-sm-4 col-form-label"> Claim No
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder=" Claim No"
                                   name="claimNo" id="claimNo" value="${claimHandlerDto.claimNo}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Date of Loss
                        </label>
                        <div class="col-sm-8">
                            <input type="text" readonly class="form-control form-control-sm" placeholder="Date of Loss"
                                   name="dateOfLoss" id="dateOfLoss" value="${claimHandlerDto.claimsDto.accidDate}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label text-success"> Engineer Approved ACR
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left text-success">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.aprvTotAcrAmount}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label text-danger"> Current Provision
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left text-danger">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.reserveAmountAfterAprv}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Advance Amount
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.aprvAdvanceAmount}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span>
                        </div>
                    </div>

                    <div class="form-group row">
                            <div class="col-sm-4"
                                 style="display: none; justify-content: flex-start; align-items: center;">
                                <button type="button" class="btn btn-primary" name="btnCHPolicyDetails"
                                        id="btnCHPolicyDetails" data-toggle="modal"
                                        data-target="#policyDetailsModal">
                                    Policy
                                    Details
                                </button>
                            </div>
                        <div class="col-sm-8">

                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Calculation Sheet Type</label>
                        <div class="col-sm-8 ">
                            <select class="form-control form-control-sm disable" name="calSheetType" id="calSheetType"
                                    onchange="validateCalSheetType()">
                                <c:forEach var="type" items="${CAL_SHEET_TYPE}">
                                   <option ${type.isPolicyWise eq "Y" ? 'style="background: lightgreen;"' : 'style="background: lightgray;"'} value="${type.typeId}">${type.typeDesc}</option>
                                </c:forEach>

                            </select>
                            <script type="text/javascript">
                                $('#calSheetType').val('${claimCalculationSheetMainDto.calSheetType}');
                            </script>
                        </div>
                    </div>
                    <div id="supplierOrderIdDiv" class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Supplier Order Serial
                            No </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="supplierOrderId" id="supplierOrderId">
                                ${claimCalculationSheetMainDto.supplyOrderSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#supplierOrderId').val('${claimCalculationSheetMainDto.claimCalculationSheetSupplierOrderDto.supplierOrderId}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">
                            Loss Type</label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="lossType" id="lossType"
                                    onchange="validateCalSheetType()">
                                ${claimCalculationSheetMainDto.lossTypeSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#lossType').val('${claimCalculationSheetMainDto.lossType}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label"> Cause of Loss </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="causeOfLoss" id="causeOfLoss">
                                ${claimCalculationSheetMainDto.causeOfLossSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#causeOfLoss').val('${claimCalculationSheetMainDto.causeOfLoss}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label"> Payment Type </label>
                        <div class="col-sm-8">
                            <select class="form-control form-control-sm disable" name="paymentType" id="paymentType">
                                ${claimCalculationSheetMainDto.paymentTypeSelectItem}
                            </select>
                            <script type="text/javascript">
                                $('#paymentType').val('${claimCalculationSheetMainDto.paymentType}');
                            </script>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="dateOfLoss" class="col-sm-4 col-form-label"> Replacement
                        </label>
                        <div class="col-sm-8">
                            <span class="label_Value float-left">
                            <fmt:formatNumber
                                    value="${claimHandlerDto.partCost}"
                                    pattern="###,##0.00;"
                                    type="number"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
<%--        remove bill detail from ref 2 calsheet--%>
<%--        <fieldset class=" border p-2 mt-2">--%>
<%--            <jsp:include--%>
<%--                    page="/WEB-INF/jsp/claim/claimHandler//referenceTwoCalculationSheet/calculationBillDetails.jsp"></jsp:include>--%>

<%--        </fieldset>--%>
        <div class="container-fluid border mt-3 mb-3" id="supplierDiv" style="display: none">

            <div class="col-lg-3 ">
                <ul class="list-group p-2">
                    <li class="list-group-item p-2">
                        <span class="float-left">Delivery Order Details</span>
                        <span id="partsLabel1" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-succes">
                        <span class="float-left">Total Amount </span>
                        <span id="supplierTotalAmount" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-warning">
                        <span class="float-left">OA Amount </span>
                        <span id="supplierOaAmount" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-danger">
                        <span class="float-left">Other Deduction </span>
                        <span id="supplierOtherDeduction" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-warning">
                        <span class="float-left">Policy Excess </span>
                        <span id="supplierPolicyExcess" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2" id="vat">
                        <span class="float-left" id="vatLbl"></span>
                        <span id="supplierVat" class="label_Value float-right"></span>
                    </li>
                    <li class="list-group-item p-2 bg-badge-primary">
                        <span class="float-left" style="font-weight: 600; font-size: 20px;">Final DO Amount</span>
                        <span id="supplierFinalAmount" class="label_Value float-right"
                              style="font-weight: 600; font-size: 18px;"></span>
                    </li>
                </ul>
            </div>
        </div>
        <fieldset class=" border p-2 mt-2">
            <h6><label id="replacementLabel">Replacement</label></h6>
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12 col-lg-12">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/referenceTwoCalculationSheet/referenceTwoReplacementItemList.jsp"></jsp:include>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border p-2 mt-2">
            <h6><label id="labourLabelVal">Labour</label></h6> Total Approved Labour : <span class="">
                                <fmt:formatNumber
                                        value="${claimHandlerDto.labourCost}"
                                        pattern="###,##0.00;"
                                        type="number"/>
                            </span
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12  col-lg-12">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/referenceTwoCalculationSheet/referenceTwoLabourItemList.jsp"></jsp:include>
                </div>
            </div>
        </fieldset>
        <fieldset class=" border my-2">
            <div class="row">
                <jsp:include
                        page="/WEB-INF/jsp/claim/claimHandler/referenceTwoCalculationSheet/referenceTwoCalculationSummaryDetails.jsp"></jsp:include>
                <div id="payeeTableDiv" class="col-lg-12 " style="margin-top: 80px;">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/referenceTwoCalculationSheet/referenceTwoCalculationPayeeDetails.jsp"></jsp:include>
                </div>
            </div>
            <c:if test="${claimCalculationSheetMainDto.status eq 71 and G_USER.userId eq claimCalculationSheetMainDto.rteAssignUserId}">
                <div class="row mr-2 mb-8">
                    <div class="col-sm-8 p-2 offset-sm-4 mr-6" style="box-shadow: 1px 1px 11px 3px #dadada;">
                        <div class="row">
                            <div class="col-sm-6">
                                <ul class="list-group list-group-flush border">
                                    <li class="list-group-item p-2 bg-badge-danger">
                                        <span class="float-left">Reserve Amount </span>
                                        <span id="reserveAmount"
                                              class="label_Value float-right">${tempDto.prevReserveAmount}</span>
                                    </li>
                                    <li class="list-group-item p-2 bg-badge-danger">
                                        <span class="float-left">Current Provision </span>
                                        <span id="currentProvision"
                                              class="label_Value float-right">${claimHandlerDto.reserveAmountAfterAprv}</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-sm-6">
                                <ul class="list-group list-group-flush border">
                                    <li class="list-group-item p-2 bg-badge-danger">
                                        <span class="float-left">Reserve Amount Requested to Increase </span>
                                        <span id="requestedToIncrease"
                                              class="label_Value float-right">${tempDto.reserveAmount - tempDto.prevReserveAmount}</span>
                                    </li>
                                    <li class="list-group-item p-2 bg-badge-danger">
                                        <span class="float-left">Reserve Amount After Approval </span>
                                        <span id="afterApproval"
                                              class="label_Value float-right">${tempDto.reserveAmount}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>

        </fieldset>

        <fieldset class=" border p-2 mt-2">
            <h6><label id="paymentHistoryLabel">Payment History</label></h6>
            <hr class="my-2">
            <div class="row">
                <div class="col-md-12 col-lg-12">
                    <jsp:include
                            page="/WEB-INF/jsp/claim/claimHandler/referenceTwoCalculationSheet/referenceTwoCalculationSheetHistory.jsp"></jsp:include>
                </div>
            </div>

            <div id="btnPanel" class="row">

            </div>
        </fieldset>
    </div>
</form>
<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
     id="emailConform" aria-hidden="true" style="    background: #333333c2;">
    <input type="hidden" id="callType" name="callType"/>
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"
                    id="modalLabel"></h6>
            </div>
            <div class=" mt-4">
                <div class="col-sm-6 offset-3">
                    <div class="form-group row">
                        <label for="insuredName" name="" id="" class="col-sm-4 col-form-label"> Email Address
                        </label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm" placeholder="Email Address"
                                   name="email"
                                   id="email" value="" onkeypress="emailValidate()">
                            <div class="text-danger" id="errorDiv" style="display: none">
                                Please enter a valid email
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-1">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"
                        onclick="closeEmailConfirmation();">
                    Close
                </button>
                <button type="button" class="btn btn-primary" data-dismiss="modal" id="emailBtn"
                        onclick="emailSending();">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
     id="confirmBox" aria-hidden="true" style="    background: #333333c2;">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"><label id="notifyLabel"></label></h6>
            </div>
            <div class="modal-footer p-1">
                <button type="button" id="confirmModalBtn" class="btn btn-secondary"
                        onclick="saveCalculationSheet()">
                    Confirm
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()"
                >
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
     id="confirmBox2" aria-hidden="true" style="    background: #333333c2;">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content p-2" style="overflow: hidden">
            <div class="modal-header  p-2">
                <h6 class="modal-title"><label id="notifyLabel2"></label></h6>
            </div>
            <div class="modal-footer p-1">
                <button type="button" id="confirmModalBtn2" class="btn btn-secondary" onclick="confirm2()">
                    Ok
                </button>

            </div>
        </div>
    </div>
</div>
<div id="divResize" class="viewDocumentContainera">
    <h5 class="ui-widget-header">Document View<i class="fa fa-times pull-right close-panel"
                                                 id="close-toolbox-tools"></i></h5>
    <iframe width="100%" height="95%" id="viewDocument" src=""></iframe>
</div>


<div class="modal fade" id="showReturnToCLaimHandlerRemarkModal" tabindex="-1" role="dialog"
     aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <c:choose>
                    <c:when test="${G_USER.userId eq claimCalculationSheetMainDto.rteAssignUserId}">
                        <h5 class="modal-title" id="forwardToSpTeamTittle" style="display: none">Do you want proceed
                            ?</h5>
                        <h5 class="modal-title" id="returnToClaimHandlerTittle" style="display: none">Do you want return
                            to claim handler ?</h5>
                    </c:when>
                    <c:otherwise>
                        <h5 class="modal-title" id="exampleModalLongTitle">Do you want return to claim handler?</h5>
                    </c:otherwise>
                </c:choose>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="defineRemark" class="col-form-label">Remark:</label>
                    <textarea class="form-control" id="defineRemark" name="defineRemark"
                              onkeypress="hideErrorDivAfterApproved()"></textarea>
                </div>
                <div class="text-danger" id="errorDivAfterDefined" style="display: none">
                    This field is required
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeDiv()">Close</button>
                <c:choose>
                    <c:when test="${G_USER.userId eq claimCalculationSheetMainDto.rteAssignUserId}">
                        <button type="button" id="btnReserveForward" class="btn btn-primary" onclick="rteRemark()">Save
                        </button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" id="btnReserveReturn" class="btn btn-primary"
                                onclick="returnToClaimHandlerAfterApproved()">Save
                            changes
                        </button>
                    </c:otherwise>
                </c:choose>

            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    function viewDocument(refNo, jobRefNo, previousInspection, sideClass) {
        $('.viewDocumentContainera').show().addClass(sideClass);
        $('.viewDocumentContainera  iframe#viewDocument').attr("src", "${pageContext.request.contextPath}/PhotoComparisonController/viewBillDocumentViewer?refNo=" + refNo + "&jobRefNo=" + jobRefNo + "&PREVIOUS_INSPECTION=" + previousInspection);

    }

    function closeDocumentView() {
        $('.viewDocumentContainera').hide();
    }

    function loadBtnPanelNew() {
        $("#btnPanel").load("${pageContext.request.contextPath}/ReferenceTwoCalculationSheetController/loadBtnPanelNew?calSheetId=${claimCalculationSheetMainDto.calSheetId}&claimNo=${claimHandlerDto.claimNo}&DOC_UPLOAD=${docUpload}");
        var claimNo = '${claimHandlerDto.claimNo}';
        window.opener.loadPaymentOptionPage(claimNo);
        window.opener.loadClaimStampContainer(claimNo);
    }

    loadBtnPanelNew();

    function checkPendingPayment() {
        var claimNo = '${claimHandlerDto.claimNo}';
        return new Promise((resolve => {
            $.ajax({
                url: contextPath + "/ReferenceTwoCalculationSheetController/checkPendingPayments?claimNo=" + claimNo,
                type: 'POST',
                success: function (result) {
                    let ispending = JSON.parse(result);
                    return resolve(ispending)
                }
            });
        }))
    }

    async function checkAlreadySavedAndSaveCalsheet() {
        var isAlreadySavedCalsheet = '${isAlreadySavedCalsheet}';
        let isPendingPayment = await checkPendingPayment();

        if ('true' == isAlreadySavedCalsheet) {
            var note = "This is an already saved Cal sheet. The actual reserve is Rs.${tempDto.prevReserveAmount} and the current provision is Rs.${tempDto.prevReserveAmountAfterApproved}";
            $('#notifyLabel2').text(note);
            $('#confirmBox2').modal('show');
        } else if (isPendingPayment === "Y") {
            notify("Can not save", "danger");
        } else if (isPendingPayment === "N") {
            saveCalculation();
        }
    }

    function saveCalCulationMain() {
        var isAlreadySavedCalsheet = '${isAlreadySavedCalsheet}';

        var replacementIndex = totalReplacementItem;
        var labourIndex = totalLabourItem;
        var payeeIndex = totalPayeeItem;

        var calculationTypeFormErrorCode = calculationTypeFormValidateCode();
        var payeeFormErrorCode = payeeFormValidateCode(3000);
        var payItemReplacementFormErrorCode = isPayItemFormValidate(1000);
        var payItemLabourFormErrorCode = isPayItemFormValidate(2000);

        if ((replacementIndex == 0 && labourIndex == 0) && payeeIndex > 0) {
            notify("Please select replacement/labour items before saved or remove selected payee items", "danger");
            return;
        }

        if (payableAmount < 0 && ((replacementIndex > 0 || labourIndex > 0) && payeeIndex > 0)) {
            notify("Payable amount less than Zero.", "danger");
            return;
        }
        if (calculationTypeFormErrorCode != 0) {
            notify("Please enter all mandatory fields", "danger");
            return;
        }
        if (payItemReplacementFormErrorCode == false || payItemLabourFormErrorCode == false) {
            notify("Please enter valid amount", "danger");
            return;
        }
        if (payeeFormErrorCode == 2) {
            notify("Payee Type is required", "danger");
            return;
        } else if (payeeFormErrorCode == 1) {
            notify("Payee Amount is required", "danger");
            return;
        } else if (payeeFormErrorCode == 3) {
            notify("Payee Desc is required", "danger");
            return;
        } else if (payeeFormErrorCode == 4) {
            notify("Dispatch Location is Required", "danger");
            return;
        }
        $('#confirmModalBtn').removeAttr('disabled');

        if (($('#payableAmount').val() != $('#totalPayeeAmountValue').val()) && (replacementIndex != '0' || labourIndex != '0')) {
            notify("Total Payee Amount must be equal to Payable Amount", "danger");
            return;
        }
        saveCalculationSheet();
    }

    async function saveCalculation() {
        var payableAmount = parseFloat($('#payableAmount').val());
        var caltype = $("#calSheetType").val();
        var claimNo = '${claimHandlerDto.claimNo}';


        $.ajax({
            url: contextPath + "/ReferenceTwoCalculationSheetController/checkApprovalLimit?claimNo=" + claimNo+"&calSheetType="+caltype+"&payableAmount="+payableAmount,
            type: 'GET',
            success: function (result) {
                let message = JSON.parse(result);
                if(message == "EXCEED"){
                    bootbox.confirm({
                        message: "CalSheet type Limit Exceeded! Do you want to proceed?",
                        buttons: {
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            },
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            }
                        },
                        callback: function (result) {
                            if (result == true) {
                                saveCalCulationMain();
                            }
                        }
                    })
                }
                if(message == "FAIL"){
                    notify("System Error!")
                }
                if(message == "RESERVE_EXCEED"){

                }else{
                  saveCalCulationMain();
                }

            }
        });
    }

    async function draft() {
        let isPendingPayment = await checkPendingPayment();
        var isAlreadySavedCalsheet = '${isAlreadySavedCalsheet}';
        if ("true" === isAlreadySavedCalsheet || "N" === isPendingPayment) {
            $(".load").show();
            document.getElementById('detailform').action = contextPath + "/ReferenceTwoCalculationSheetController/saveCalculationSheet?ACTION_TYPE=DRAFT";
            document.getElementById('detailform').submit();
            $('#submitBtn').prop("disabled", true);
        } else {
            notify("Can not save", "danger");
        }
    }


    async function saveCalculationSheet() {

        $('#confirmBox').modal('hide');

        let validationResult = await validateClosedAndPremiumOutstandingPolicies();
        var msg = '';
        let isCancelledPolicy = false;

        if (validationResult == 0) {
            msg = "This is a cancelled policy and has [Rs." + PREM_OUT_LATEST_POLICY + "] of outstanding premium amount. ";
            isCancelledPolicy = true;
        } else if (validationResult == 1) {
            msg = "This is a Cancelled Policy. ";
            isCancelledPolicy = true;
        } else if (validationResult == 2) {
            msg = "This policy has Rs." + PREM_OUT_LATEST_POLICY + " of outstanding premium amount. ";
        }

        if ('' != msg) {
            msg = msg + "Do you want to proceed?";
            bootbox.confirm({
                message: msg,
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        SaveCalSheetAction(isCancelledPolicy);
                    }
                }
            });
        } else {
            bootbox.confirm({
                message: "Do you want to save ?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        SaveCalSheetAction(isCancelledPolicy);
                    } else {
                        $('#submitBtn').removeAttr('disabled');
                    }
                }
            });
        }

    }

    function SaveCalSheetAction(isCancelledPolicy) {
        $(".load").show();
        document.getElementById('detailform').action = contextPath + "/ReferenceTwoCalculationSheetController/saveCalculationSheet?OUTSTANDING_PREMIUM=" + PREM_OUT_LATEST_POLICY + "&IS_CANCELLED_POLICY=" + isCancelledPolicy;
        document.getElementById('detailform').submit();
        $('#submitBtn').prop("disabled", true);
    }

    function rteRemark() {
        $('#btnReserveForward').prop('disabled', true);
        $('#rteRemark').val($('#defineRemark').val())
        if ('N' == $('#isReturn').val()) {
            approveIncreaseReserveByRte();
        } else {
            returnToClaimHandlerByRte();
        }
    }

    function approveIncreaseReserveByRte() {
        document.getElementById('detailform').action = contextPath + "/ReferenceTwoCalculationSheetController/approveIncreaseReserveByRte";
        document.getElementById('detailform').submit();
    }

    function returnToClaimHandlerByRte() {
        document.getElementById('detailform').action = contextPath + "/ReferenceTwoCalculationSheetController/returnToClaimHandlerByRte";
        document.getElementById('detailform').submit();
    }

    function closeModal() {
        $('#btnGroupVerticalDrop1').show();
        $('#confirmBox').modal('hide');
        $('#submitBtn').removeAttr("disabled");
    }

    async function confirm2() {
        saveCalculation();
        $('#confirmBox2').modal('hide');
    }

    function forwardToSparePartCordinator() {
        $('#btnGroupVerticalDrop1').hide();
        <%--if ((parseFloat($('#payableAmount').val()) > parseFloat('${claimHandlerDto.reserveAmountAfterAprv}'))) {--%>
        <%--    notify("calsheet cannot be progress, calsheet total payable amount exceeds the reserve amount", "danger");--%>
        <%--    $('#btnGroupVerticalDrop1').show();--%>
        <%--    return;--%>
        <%--}--%>
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        if (${ariArranged}) {
            bootbox.confirm({
                message: "Already ARI arranged Do you want to forward Cal sheet to spare part coordinator ?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        $.ajax({
                            url: contextPath + "/ReferenceTwoCalculationSheetController/forwardToSparePartCordinator?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                            type: 'POST',
                            success: function (result) {
                                var messageType = JSON.parse(result);
                                var message = "";
                                if (messageType == "SUCCESS") {
                                    message = "Forwarded Successfully";
                                    notify(message, "success");
                                } else if (messageType == "USER_NOT_FOUND") {
                                    message = "User not found to assign claim";
                                    notify(message, "danger");
                                } else {
                                    message = "Forward Failed";
                                    notify(message, "danger");
                                }
                                loadBtnPanelNew();
                            }
                        });
                    } else {
                        $('#submitBtn').removeAttr('disabled');
                        $('#btnGroupVerticalDrop1').show();
                    }
                }
            });
        } else {
            bootbox.confirm({
                message: "Do you want to forward?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        $.ajax({
                            url: contextPath + "/ReferenceTwoCalculationSheetController/forwardToSparePartCordinator?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                            type: 'POST',
                            success: function (result) {
                                var messageType = JSON.parse(result);
                                var message = "";
                                if (messageType == "SUCCESS") {
                                    message = "Forwarded Successfully";
                                    notify(message, "success");
                                } else if (messageType == "USER_NOT_FOUND") {
                                    message = "User not found to assign claim";
                                    notify(message, "danger");
                                } else {
                                    message = "Forward Failed";
                                    notify(message, "danger");
                                }
                                loadBtnPanelNew();
                            }
                        });
                    } else {
                        $('#submitBtn').removeAttr('disabled');
                        $('#btnGroupVerticalDrop1').show();
                    }
                }
            });
        }
    }

    // recall no objection

    function revokeNoObjection() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        var email = $('#email').val();

        bootbox.confirm({
            message: "Do you want to revoke for no objection ?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/revokeNoObjection?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&email=" + email,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "No Objection Revoked Successfully";
                                notify(message, "success");
                            } else {
                                message = "No Objection Revoked Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });
    }

    // recall outstanding premium

    function revokePremiumOutstanding() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        var email = $('#email').val();

        bootbox.confirm({
            message: "Do you want to revoke premium outstanding ?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/revokePremiumOutstanding?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&email=" + email,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Premium Outstanding Revoked Successfully";
                                notify(message, "success");
                            } else {
                                message = "Premium Outstanding Revoked Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });
    }

    function recallByClaimHandler() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to recall?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/recallByClaimHandler?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Recall Successfully";
                                notify(message, "success");
                            } else {
                                message = "Recall Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    function recallCalsheetforwardedtoSpTeam() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to recall?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/recallCalsheetforwardedtoSpTeamByClaimHandler?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Recall Successfully";
                                notify(message, "success");
                                loadBtnPanelNew();
                                $('#submitBtn').removeAttr('disabled');
                            } else {
                                message = "Recall Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }


    async function forwardToSpecialTeam(value) {
        $('#btnGroupVerticalDrop1').hide();

        <%--if ((parseFloat($('#payableAmount').val()) > parseFloat('${claimHandlerDto.reserveAmountAfterAprv}'))) {--%>
        <%--    notify("calsheet cannot be progress, calsheet total payable amount exceeds the reserve amount", "danger");--%>
        <%--    $('#btnGroupVerticalDrop1').show();--%>
        <%--    return;--%>
        <%--}--%>
        var payableAmount = parseFloat('${claimCalculationSheetMainDto.payableAmount}');
        if (PAYEE_COUNT <= 0) {
            notify("Please enter payee details", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        if (payableAmount <= 0) {
            notify("Payable amount equal or less than Zero.", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        if (${'C' eq claimCalculationSheetMainDto.noObjectionStatus and 'NO_VALID_DOCUMENT' eq claimDocumentStatusDtoNoObjection.documentStatusEnum}) {
            notify("No Objection Document Pending", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }
        if (${'C' eq claimCalculationSheetMainDto.premiumOutstandingStatus and 'NO_VALID_DOCUMENT' eq claimDocumentStatusDtoPremiumOutstanding.documentStatusEnum}) {
            notify("Premium Outstanding Confirmation Document Pending", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        let validationResult = await validateClosedAndPremiumOutstandingPolicies();

        // $.ajax({
        //     url: contextPath + "/ReferenceTwoCalculationSheetController/claimDocumentStatusDto?claimNo=" + claimNo + "&calSheetType=" + $("#calSheetType").val(),
        //     success: function (result) {
        //         var message = JSON.parse(result);
        //         if ("333" == $("#calSheetType").val() && (message == 'P' || message == 'N')) {
        //             bootbox.alert("Please upload and check Approved Delivery Order Bill  before forward to".concat(value == 1 ? " RTE" : " special team!"));
        //             $('#btnGroupVerticalDrop1').show();
        //         } else if ("555" == $("#calSheetType").val() && (message == 'P' || message == 'N')) {
        //             bootbox.alert("Please upload and check Approved  Release Order Bill before forward to".concat(value == 1 ? " RTE" : " special team!"));
        //             $('#btnGroupVerticalDrop1').show();
        //         } else {
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/checkedAllLiabilityApproveDocument?claimNo=" + claimNo,
                        success: function (result) {
                            var message = JSON.parse(result);
                            if (message == 'P') {
                                bootbox.alert("Please upload and check all mandatory documents before forward to".concat(value == 1 ? " RTE" : " special team!"));
                                $('#btnGroupVerticalDrop1').show();
                                return;
                            }

                            let isCancelled = false;
                            var msg = '';

                            if (validationResult == 0) {
                                msg = "This is a cancelled policy and has Rs." + PREM_OUT_LATEST_POLICY + " of outstanding premium amount. Do you want to proceed?";
                                isCancelled = true;
                            } else if (validationResult == 1) {
                                msg = "This is a cancelled policy. Do you want to proceed?";
                                isCancelled = true;
                            } else if (validationResult == 2) {
                                msg = "This policy has Rs." + PREM_OUT_LATEST_POLICY + " of outstanding premium amount. Do you want to proceed?";
                            } else {
                                msg = "Do you want to forward?"
                            }

                            bootbox.confirm({
                                message: msg,
                                buttons: {
                                    cancel: {
                                        label: 'No',
                                        className: 'btn-secondary float-right'
                                    },
                                    confirm: {
                                        label: 'Yes',
                                        className: 'btn-primary'
                                    }
                                },
                                callback: function (result) {
                                    if (result == true) {
                                        var URL = '';
                                        if (value == 1) {
                                            URL = contextPath + "/ReferenceTwoCalculationSheetController/forwardToRte?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&OUTSTANDING_PREMIUM=" + OUTSTANDING_PREMIUM + "&PAYABLE=" + payableAmount + "&IS_CANCELLED_POLICY=" + isCancelled;
                                        } else {
                                            URL = contextPath + "/ReferenceTwoCalculationSheetController/forwardToSpecialTeam?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&OUTSTANDING_PREMIUM=" + OUTSTANDING_PREMIUM + "&IS_CANCELLED_POLICY=" + isCancelled;
                                        }
                                        $.ajax({
                                            url: URL,
                                            type: 'POST',
                                            success: function (result) {
                                                var messageType = JSON.parse(result);
                                                var message = "";
                                                if (messageType == "SUCCESS") {
                                                    message = "Forwarded Successfully";
                                                    notify(message, "success");
                                                } else if (messageType == "301") {
                                                    message = "Please select valid payee - Return Failed";
                                                    notify(message, "danger");
                                                } else if (messageType == "302") {
                                                    message = "Please select valid payee amount - Return Failed";
                                                    notify(message, "danger");
                                                } else if (messageType == "303") {
                                                    message = "Please select valid payee desc - Return Failed";
                                                    notify(message, "danger");
                                                } else if (messageType == "USER_NOT_FOUND") {
                                                    message = "User not found to assign claim";
                                                    notify(message, "danger");
                                                } else {
                                                    message = "Forward Failed";
                                                    notify(message, "danger");
                                                }
                                                loadBtnPanelNew();
                                            }
                                        });
                                    } else {
                                        $('#submitBtn').removeAttr('disabled');
                                        $('#btnGroupVerticalDrop1').show();
                                    }
                                }
                            });


                        }
                    });
            //     }
            // }

    }

    function forwardToScrutinizingTeam() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to forward?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/forwardToScrutinizingTeam?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Forwarded Successfully";
                                notify(message, "success");
                            } else if (messageType == "USER_NOT_FOUND") {
                                message = "User not found to assign claim";
                                notify(message, "danger");
                            } else {
                                message = "Forward Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    function forwardToScrutinizingTeamByClaimHandler() {
        $('#btnGroupVerticalDrop1').hide();
        <%--if ((parseFloat($('#payableAmount').val()) > parseFloat('${claimHandlerDto.reserveAmountAfterAprv}'))) {--%>
        <%--    notify("calsheet cannot be progress, calsheet total payable amount exceeds the reserve amount", "danger");--%>
        <%--    $('#btnGroupVerticalDrop1').show();--%>
        <%--    return;--%>
        <%--}--%>
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        if (${ariArranged}) {
            bootbox.confirm({
                message: "Already ARI arranged Do you want to forward Cal sheet to scrutinizing team ?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        $.ajax({
                            url: contextPath + "/ReferenceTwoCalculationSheetController/forwardToScrutinizingTeamByClaimHandler?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                            type: 'POST',
                            success: function (result) {
                                var messageType = JSON.parse(result);
                                var message = "";
                                if (messageType == "SUCCESS") {
                                    message = "Forwarded Successfully";
                                    notify(message, "success");
                                } else if (messageType == "USER_NOT_FOUND") {
                                    message = "User not found to assign claim";
                                    notify(message, "danger");
                                } else {
                                    message = "Forward Failed";
                                    notify(message, "danger");
                                }
                                loadBtnPanelNew();
                            }
                        });
                    } else {
                        $('#submitBtn').removeAttr('disabled');
                        $('#btnGroupVerticalDrop1').show();
                    }
                }
            });
        } else {
            bootbox.confirm({
                message: "Do you want to forward?",
                buttons: {
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary float-right'
                    },
                    confirm: {
                        label: 'Yes',
                        className: 'btn-primary'
                    }
                },
                callback: function (result) {
                    if (result == true) {
                        $.ajax({
                            url: contextPath + "/ReferenceTwoCalculationSheetController/forwardToScrutinizingTeamByClaimHandler?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                            type: 'POST',
                            success: function (result) {
                                var messageType = JSON.parse(result);
                                var message = "";
                                if (messageType == "SUCCESS") {
                                    message = "Forwarded Successfully";
                                    notify(message, "success");
                                } else if (messageType == "USER_NOT_FOUND") {
                                    message = "User not found to assign claim";
                                    notify(message, "danger");
                                } else {
                                    message = "Forward Failed";
                                    notify(message, "danger");
                                }
                                loadBtnPanelNew();
                            }
                        });
                    } else {
                        $('#submitBtn').removeAttr('disabled');
                        $('#btnGroupVerticalDrop1').show();
                    }
                }
            });
        }

    }

    function returnToClaimHandlerBySpc() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/returnToClaimHandlerBySpc?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    function showRemark(isReturn) {
        $('#showReturnToCLaimHandlerRemarkModal').modal('show');
        if ('${G_USER.userId eq claimCalculationSheetMainDto.rteAssignUserId}') {
            $('#isReturn').val(isReturn)
            if ('N' == isReturn) {
                $('#forwardToSpTeamTittle').show();
                $('#returnToClaimHandlerTittle').hide();
            } else {
                $('#forwardToSpTeamTittle').hide();
                $('#returnToClaimHandlerTittle').show();
            }
        }
    }

    function returnToClaimHandlerAfterApproved() {
        $('#btnReserveReturn').prop('disabled', true);
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        var defineRemark = $('#defineRemark').val();
        if (defineRemark != '') {
            showLoader();
            $.ajax({
                url: contextPath + "/ReferenceTwoCalculationSheetController/returnToClaimHandlerAfterApproved?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&specialRemark=" + defineRemark,
                type: 'POST',
                success: function (result) {

                    var messageType = JSON.parse(result);
                    var message = "";
                    if (messageType == "SUCCESS") {
                        message = "Returned Successfully";
                        notify(message, "success");
                    } else {
                        message = "Return Failed";
                        notify(message, "danger");
                    }
                    loadBtnPanelNew();
                    $('#showReturnToCLaimHandlerRemarkModal').modal('hide')
                }
            });
            closeModal();
        } else {
            $('#errorDivAfterDefined').show();
            $('#btnReserveReturn').prop('disabled', false);
        }


    }

    async function generateVoucher() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        var payableAmount = parseFloat('${claimCalculationSheetMainDto.payableAmount}');
        if (PAYEE_COUNT <= 0) {
            notify("Can not be generated.Please enter payee details", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        if (payableAmount <= 0) {
            notify("Can not be generated.Payable amount equal or less than Zero.", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        if ('A' != '${claimHandlerDto.liabilityAprvStatus}' && '3' != $('#paymentType').val()) {
            notify("Liability Approve Pending", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        var claimNo = '${claimHandlerDto.claimNo}';

        let validationResult = await validateClosedAndPremiumOutstandingPolicies();

        $.ajax({
            url: contextPath + "/ReferenceTwoCalculationSheetController/claimDocumentStatusDto?claimNo=" + claimNo + "&calSheetType=" + $("#calSheetType").val(),
            success: function (result) {
                var message = JSON.parse(result);
                if ("3" == $("#calSheetType").val() && (message == 'P' || message == 'N')) {
                    bootbox.alert("Please upload and check Approved Delivery Order Bill before generating the voucher!");
                    $('#btnGroupVerticalDrop1').show();
                } else if ("5" == $("#calSheetType").val() && (message == 'P' || message == 'N')) {
                    bootbox.alert("Please upload and check Approved  Release Order Bill before generating the voucher!");
                    $('#btnGroupVerticalDrop1').show();
                } else {
                    $.ajax({
                        url: contextPath + "/ClaimHandlerController/checkedAllLiabilityApproveDocument?claimNo=" + claimNo,
                        success: function (result) {
                            var message = JSON.parse(result);
                            if (message == 'P') {
                                bootbox.alert("Please upload and check Approved all mandatory documents before generating voucher!");
                                return;
                            }

                            let bootBoxMsg = "Do you want to generate voucher?";

                            let isCancelled = false;

                            if (validationResult == 0) {
                                bootBoxMsg = "This is a cancelled policy and has Rs." + PREM_OUT_LATEST_POLICY + " of outstanding premium amount. Do you want to proceed?";
                                isCancelled = true;
                            } else if (validationResult == 1) {
                                bootBoxMsg = "This is a cancelled policy. Do you want to proceed?";
                                isCancelled = true;
                            } else if (validationResult == 2) {
                                bootBoxMsg = "This policy has Rs." + PREM_OUT_LATEST_POLICY + " of outstanding premium amount. Do you want to proceed?";
                            }

                            bootbox.confirm({
                                message: bootBoxMsg,
                                buttons: {
                                    cancel: {
                                        label: 'No',
                                        className: 'btn-secondary float-right'
                                    },
                                    confirm: {
                                        label: 'Yes',
                                        className: 'btn-primary'
                                    }
                                },
                                callback: function (result) {
                                    if (result == true) {
                                        $(".load").show();
                                        $.ajax({
                                            url: contextPath + "/ReferenceTwoCalculationSheetController/generateVoucher?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&OUTSTANDING_PREMIUM=" + PREM_OUT_LATEST_POLICY + "&IS_CANCELLED_POLICY=" + isCancelled,
                                            type: 'POST',
                                            success: function (result) {
                                                var messageType = JSON.parse(result);
                                                var message = "";
                                                if (messageType == "SUCCESS") {
                                                    message = "Generated Successfully";
                                                    notify(message, "success");
                                                } else if (messageType == "301") {
                                                    message = "Please select valid payee - Generate Failed";
                                                    notify(message, "danger");
                                                } else if (messageType == "302") {
                                                    message = "Please select valid payee amount - Generate Failed";
                                                    notify(message, "danger");
                                                } else if (messageType == "303") {
                                                    message = "Please select valid payee desc - Return Failed";
                                                    notify(message, "danger");
                                                } else if (messageType == "PAYABLE_AMOUNT_ERROR") {
                                                    message = "Can not be Approved,Total Payable Amount exceeds the Reserve Amount. " +
                                                        "Please ask claim handler to save the cal sheet and forward again";
                                                    notify(message, "danger");
                                                } else if (messageType == "EXCESS_RESERVE") {
                                                    message = "Cannot be Approved, Reserve Amount Exceeds the Total Payable Amount. " +
                                                        "Please ask claim handler to save the cal sheet and forward again";
                                                    notify(message, 'danger');
                                                } else {
                                                    message = "Generate Failed";
                                                    notify(message, "danger");
                                                }
                                                $(".load").hide();
                                                loadBtnPanelNew();
                                            }
                                        });
                                    } else {
                                        $('#btnGroupVerticalDrop1').show();
                                    }
                                }
                            });


                        }
                    });
                }
            }
        });


    }

    function returnToClaimHandlerByStm() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/returnToClaimHandlerByStm?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });

    }

    function returnToClaimHandlerBySpecialTeam() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/returnToClaimHandlerBySpecialTeam?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    function returnToClaimHandlerByMofa() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/returnToClaimHandlerByMofa?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    function returnToSparePartCoordinatorByStm() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        bootbox.confirm({
            message: "Do you want to return?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/returnToSparePartCoordinatorByStm?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Returned Successfully";
                                notify(message, "success");
                            } else if (messageType == "USER_NOT_FOUND") {
                                message = "User not found to assign claim";
                                notify(message, "danger");
                            } else {
                                message = "Return Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    async function approvePayment(userStat) {
        $('#btnGroupVerticalDrop1').hide();
        var payableAmount = parseFloat('${claimCalculationSheetMainDto.payableAmount}');
        if (PAYEE_COUNT <= 0) {
            notify("Can not be generated.Please enter payee details", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        if (payableAmount <= 0) {
            notify("Can not be generated.Payable amount equal or less than Zero.", "danger");
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        <%--if (payableAmount > ${G_USER.n_payment_auth_limit}) {--%>
        <%--    notify("Auth Limit Exceeded. Please Forward TO MOFA.", "danger");--%>
        <%--    return;--%>
        <%--}--%>
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        let validationResult = await validateClosedAndPremiumOutstandingPolicies();
        let bootboxMsg = "Do you want to approve?";
        let isCancelled = false;
        let premiumOutstanding = PREM_OUT_LATEST_POLICY;

        let validDoCalsheet = await validateDO();
        if (validDoCalsheet == 'INVALID') {
            notify('DO for this calsheet has been amended. Please return the Calsheet for Claim handler', 'danger');
            $('#btnGroupVerticalDrop1').show();
            return;
        } else if (validDoCalsheet == 'FAIL') {
            notify('Process Failed');
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        if (userStat == 2) {
            premiumOutstanding = 0.00;
        } else {
            if (validationResult == 0) {
                bootboxMsg = "This is a cancelled policy and has Rs." + premiumOutstanding + " of outstanding premium amount. Do you want to proceed?";
                isCancelled = true;
            } else if (validationResult == 1) {
                bootboxMsg = "This is a cancelled policy. Do you want to proceed?";
                isCancelled = true;
            } else if (validationResult == 2) {
                bootboxMsg = "This policy has Rs." + premiumOutstanding + " of outstanding premium amount. Do you want to proceed?";
            }
        }

        bootbox.confirm({
            message: bootboxMsg,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/approvePayment?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&OUTSTANDING_PREMIUM=" + premiumOutstanding + "&IS_CANCELLED_POLICY=" + isCancelled,
                        type: 'POST',
                        async: false,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Approved Successfully";
                                notify(message, "success");
                            } else if (messageType == "301") {
                                message = "Please select valid payee - Approve Failed";
                                notify(message, "danger");
                            } else if (messageType == "302") {
                                message = "Please select valid payee amount - Approve Failed";
                                notify(message, "danger");
                            } else if (messageType == "303") {
                                message = "Please select valid payee desc - Return Failed";
                                notify(message, "danger");
                            } else if (messageType == "PAYABLE_AMOUNT_ERROR") {
                                message = "Can not be Approved,Total Payable Amount exceeds the Reserve Amount. " +
                                    "Please ask claim handler to save the cal sheet and forward again";
                                notify(message, "danger");
                            } else if (messageType == "ADVANCE_AMOUNT_ERROR") {
                                message = "Can not be Approved, Advance Amount exceeds the Total Approved Amount";
                                notify(message, "danger");
                            } else if (messageType == "APPROVE_ERROR") {
                                message = "Can not be Approved, Please generate voucher for the previous cal sheet prior to approving this cal sheet";
                                notify(message, "danger");
                            } else if (messageType == "EXCESS_RESERVE") {
                                message = "Cannot be Approved, Reserve Amount Exceeds the Total Payable Amount. " +
                                    "Please ask claim handler to save the cal sheet and forward again";
                                notify(message, 'danger');
                            } else {
                                message = "Approve Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    async function recommendAndForwardToNextLevel(userStat) {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        let validationResult = await validateClosedAndPremiumOutstandingPolicies();
        let bootboxMsg = "Do you want to recommend & forward to next level?";
        let isCancelled = false;
        let premiumOutstanding = PREM_OUT_LATEST_POLICY;

        let validDoCalsheet = await validateDO();
        if (validDoCalsheet == 'INVALID') {
            notify('DO for this calsheet has been amended. Please return the Calsheet for Claim handler', 'danger');
            $('#btnGroupVerticalDrop1').show();
            return;
        } else if (validDoCalsheet == 'FAIL') {
            notify('Process Failed');
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        if (userStat == 2) {
            premiumOutstanding = 0.00;
        } else {
            if (validationResult == 0) {
                bootboxMsg = "This is a cancelled policy and has Rs." + premiumOutstanding + " of outstanding premium amount. Do you want to recommend & forward to next level?";
                isCancelled = true;
            } else if (validationResult == 1) {
                bootboxMsg = "This is a cancelled policy. Do you want to recommend & forward to next level?";
                isCancelled = true;
            } else if (validationResult == 2) {
                bootboxMsg = "This policy has Rs." + premiumOutstanding + " of outstanding premium amount. Do you want to recommend & forward to next level?";
            }
        }

        bootbox.confirm({
            message: bootboxMsg,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/recommendAndForwardToNextLevel",
                        data: {
                            "calSheetId": calSheetId,
                            "claimNo": claimNo,
                            "OUTSTANDING_PREMIUM": premiumOutstanding,
                            "IS_CANCELLED_POLICY": isCancelled
                        },
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Forwarded Successfully";
                                notify(message, "success");
                            } else if (messageType == "USER_NOT_FOUND") {
                                message = "User not found to assign claim";
                                notify(message, "danger");
                            } else if (messageType == "EXCESS_RESERVE") {
                                message = "Cannot Forward, Reserve Amount Exceeds the Total Payable Amount. " +
                                    "Please ask claim handler to save the cal sheet and forward again";
                                notify(message, 'danger');
                            } else {
                                message = "Forward Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                            logdetails();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });
    }

    async function rejectPayment() {
        $('#btnGroupVerticalDrop1').hide();
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';

        let validDoCalsheet = await validateDO();
        if (validDoCalsheet == 'INVALID') {
            notify('DO for this calsheet has been amended. Please return the Calsheet for Claim handler', 'danger');
            $('#btnGroupVerticalDrop1').show();
            return;
        } else if (validDoCalsheet == 'FAIL') {
            notify('Process Failed');
            $('#btnGroupVerticalDrop1').show();
            return;
        }

        bootbox.confirm({
            message: "Do you want to reject?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: contextPath + "/ReferenceTwoCalculationSheetController/rejectPayment?calSheetId=" + calSheetId + "&claimNo=" + claimNo,
                        type: 'POST',
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Rejected Successfully";
                                notify(message, "success");
                            } else {
                                message = "Reject Failed";
                                notify(message, "danger");
                            }
                            loadBtnPanelNew();
                        }
                    });
                } else {
                    $('#submitBtn').removeAttr('disabled');
                    $('#btnGroupVerticalDrop1').show();
                }
            }
        });


    }

    function showEmailConfirmation(id) {
        $('#btnGroupVerticalDrop1').hide();
        <%--if ((parseFloat($('#payableAmount').val()) > parseFloat('${claimHandlerDto.reserveAmountAfterAprv}'))) {--%>
        <%--    notify("calsheet cannot be progress, calsheet total payable amount exceeds the reserve amount", "danger");--%>
        <%--    $('#btnGroupVerticalDrop1').show();--%>
        <%--    return;--%>
        <%--}--%>
        $('#callType').val(id);
        $('#emailConform').modal({
            backdrop: 'static',
            keyboard: false,
            refresh: true,
            show: true
        });
    }

    function callNoObjection() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        var email = $('#email').val();

        $.ajax({
            url: contextPath + "/ReferenceTwoCalculationSheetController/callNoObjection?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&email=" + email,
            type: 'POST',
            async: false,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "No Objection Called Successfully";
                    notify(message, "success");
                } else {
                    message = "No Objection Call Failed";
                    notify(message, "danger");
                }
                loadBtnPanelNew();
            }
        });
    }

    function closeEmailConfirmation() {
        $('#btnGroupVerticalDrop1').show();
        $('#emailConform').modal('hide');
    }

    function emailSending() {
        var emailId = $('#callType').val();
        var email = $('#email').val();

        if ('' != email) {

            if (emailId === '1') {
                callNoObjection();
            } else if (emailId === '2') {
                callPremiumOutstanding();
            } else {
                callNoClaimBonus();
            }
        } else {
            $('#errorDiv').show();
            $('#emailBtn').attr('disabled', 'disabled');
        }

    }

    function callNoClaimBonus() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        var email = $('#email').val();

        $.ajax({
            url: contextPath + "/ReferenceTwoCalculationSheetController/callNoClaimBonus?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&email=" + email,
            type: 'POST',
            async: false,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "No Claim Bonus Called Successfully";
                    notify(message, "success");
                } else {
                    message = "No Claim Bonus Call Failed";
                    notify(message, "danger");
                }
                loadBtnPanelNew();
            }
        });
    }

    function emailValidate() {
        var email = $('#email').val();
        if ('' != email) {
            $('#errorDiv').hide();
            $('#emailBtn').removeAttr('disabled');
        }
    }

    function callPremiumOutstanding() {

        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimCalculationSheetMainDto.claimNo}';
        var email = $('#email').val();

        $.ajax({
            url: contextPath + "/ReferenceTwoCalculationSheetController/callPremiumOutstanding?calSheetId=" + calSheetId + "&claimNo=" + claimNo + "&email=" + email,
            type: 'POST',
            async: false,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Premium Outstanding Confirmation Called Successfully";
                    notify(message, "success");
                } else {
                    message = "Premium Outstanding Confirmation Call Failed";
                    notify(message, "danger");
                }
                loadBtnPanelNew();
            }
        });
    }

    function forwardToMofa() {
        var dataObj = {
            amount: $('#payableAmount').val(),
            claimNo: '${claimCalculationSheetMainDto.claimNo}'
        };
        $.ajax({
            url: contextPath + "/ReferenceTwoCalculationSheetController/getMofaUserList",
            type: 'POST',
            data: dataObj,
            success: function (result) {

                var optionArr = [];
                var userArr = JSON.parse(result);
                var option1 = {text: 'Please Select', value: ''};
                optionArr.push(option1);
                for (var i = 0; i < userArr.length; i++) {
                    var option = {
                        text: userArr[i].userId + ' : ' + userArr[i].v_firstname + ' ' + userArr[i].v_lastname + ' : [Limit - ' + formatCurrency(userArr[i].n_payment_auth_limit) + ']',
                        value: userArr[i].userId
                    };
                    optionArr.push(option);
                }

                bootbox.prompt({
                    title: "Please select user to forward",
                    inputType: 'select',
                    inputOptions: optionArr,
                    callback: function (result) {
                        if (result === null) {
                            // Prompt dismissed
                        } else {
                            if (result === '') {
                                return false;
                            }

                            var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
                            var claimNo = '${claimCalculationSheetMainDto.claimNo}';
                            $.ajax({
                                url: contextPath + "/ReferenceTwoCalculationSheetController/forwardToMofa?calSheetId=" + calSheetId + "&forwardingUserId=" + result + "&claimNo=" + claimNo,
                                type: 'POST',
                                success: function (result) {
                                    var messageType = JSON.parse(result);
                                    var message = "";
                                    if (messageType == "SUCCESS") {
                                        message = "Forwared Successfully";
                                        notify(message, "success");
                                    } else if (messageType == "301") {
                                        message = "Please select valid payee - Forward Failed";
                                        notify(message, "danger");
                                    } else if (messageType == "302") {
                                        message = "Please select valid payee amount - Forward Failed";
                                        notify(message, "danger");
                                    } else if (messageType == "303") {
                                        message = "Please select valid payee desc - Return Failed";
                                        notify(message, "danger");
                                    } else {
                                        message = "Forward Failed";
                                        notify(message, "danger");
                                    }
                                    loadBtnPanelNew();
                                }
                            });
                        }
                    }
                });
            }
        });
    }

    function checkAcrAndPayable(amount) {
        var payableAmount = parseFloat(${claimCalculationSheetMainDto.payableAmount});
        if (amount == payableAmount) {
            $('#btnGroupVerticalDrop1').show();
        } else {
            $('#btnGroupVerticalDrop1').hide();
        }
    }

    function checkAcrAmountAndPaybleAmount(payableAmount) {
        return new Promise(resolve => {

            var reserveAmount = parseFloat('${claimHandlerDto.reserveAmount}');
            var totalApprovedAcr = parseFloat('${claimHandlerDto.aprvTotAcrAmount}');
            var rteAction = '${claimCalculationSheetMainDto.rteAction}';
            var claimNo = '${claimHandlerDto.claimNo}';
            var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';

            $.ajax({
                url: contextPath + "/ReferenceTwoCalculationSheetController/checkPaybleAmountAndAcrAmount",
                data: {
                    payableAmount,
                    reserveAmount,
                    totalApprovedAcr,
                    rteAction,
                    claimNo,
                    calSheetId
                },
                type: 'POST',
                async: false,
                success: function (result) {
                    if (JSON.parse(result) == "FAIL") {
                        return resolve('Fail');
                    } else {
                        $('#payableDiff').val(parseFloat(result));
                        if (parseFloat(result) > 0) {
                            return resolve(parseFloat(result));
                        }
                        return resolve('Success');
                    }
                }
            });
        });
    }

    function checkPayableAmountAndAdvancedAmount() {
        var payableAmount = parseFloat($('#payableAmount').val());
        var advancedAmount = parseFloat('${claimHandlerDto.aprvAdvanceAmount}');

        $.ajax({
            url: contextPath + "/ReferenceTwoCalculationSheetController/checkPayableAmountAndAdvancedAmount?payableAmount=" + payableAmount + "&advancedAmount=" + advancedAmount,
            type: 'POST',
            async: false,
            success: function (result) {
                $('#payableAdvanced').val(result);
                var note = "Total Payable amount exceeds Approved Advanced by Rs." + result + "Do you want to Proceed ?";
                $('#notifyLabel').text(note);
                $('#confirmBox').modal('show');

            }
        });

    }


    $(document).ready(function () {
        if (HISTORY_RECORD == 'Y'
            || CAL_SHEET_STATUS == '63'
            || CAL_SHEET_STATUS == '64'
            || CAL_SHEET_STATUS == '65'
            || CAL_SHEET_STATUS == '67'
            || CAL_SHEET_STATUS == '70'
            || CAL_SHEET_STATUS == '71') {
            disableFormInputs('#detailform');
            $('#cmdReplacement').prop("disabled", true);
            $('#cmdLabour').prop("disabled", true);
            $('#cmdPayee').prop("disabled", true);
        }
        disableBillChecked();
        let broadcast = new BroadcastChannel('tab_reload');
        broadcast.postMessage('reload');
    });


    function genReleaseLatter() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimHandlerDto.claimNo}';
        var myWindow = window.open(contextPath + "/ClaimReportController/releaseLetter?calSheetId=" + calSheetId + "&claimNo=" + claimNo, 'newwindow', 'width=1366,height=768');
        myWindow.focus();

    }

    function printCalculationSheet() {
        var calSheetId = '${claimCalculationSheetMainDto.calSheetId}';
        var claimNo = '${claimHandlerDto.claimNo}';
        var myWindow = window.open(contextPath + "/ClaimReportController/referenceTwoCalculationSheetPrintView?calSheetId=" + calSheetId + "&claimNo=" + claimNo, 'newwindow', 'width=1366,height=768');
        myWindow.focus();

    }

    function validateClosedAndPremiumOutstandingPolicies() {
        return new Promise(resolve => {

            $.ajax({
                url: contextPath + "/Claim/getPolicyValidity",
                data: {
                    V_POL_NO: '${claimHandlerDto.claimsDto.policyDto.policyNumber}'
                },
                type: 'POST',
                success: function (result) {
                    var response = JSON.parse(result);

                    PREM_OUT_LATEST_POLICY = response.outstandingPremium;

                    if (response.policyStatus == 'CAN' && response.outstandingPremium > 0) {
                        return resolve(0);
                    } else if (response.policyStatus == 'CAN') {
                        return resolve(1);
                    } else if (response.outstandingPremium > 0) {
                        return resolve(2);
                    } else {
                        return resolve(null);
                    }
                }
            });
        });
    }

    function validateCalSheetType() {
        var currentCalSheetType = '${claimCalculationSheetMainDto.calSheetType}';
        var currentLossType = '${claimCalculationSheetMainDto.lossType}';
        var calSheetType = $("#calSheetType").val();
        var lossType = $("#lossType").val();
        if (calSheetType == currentCalSheetType && lossType == currentLossType) {
            $('#btnGroupVerticalDrop1').show();
        } else {
            $('#btnGroupVerticalDrop1').hide();
        }
    }

    function validateDO() {
        return new Promise(resolve => {
            $.ajax({
                url: contextPath + "/ReferenceTwoCalculationSheetController/validateDO",
                data: {
                    N_CALSHEET_ID: '${claimCalculationSheetMainDto.calSheetId}'
                },
                type: 'POST',
                success: function (result) {
                    var response = JSON.parse(result);
//                    if (response == "VALID") {
//                        return resolve(respo)
//                    } else if (response == "INVALID") {
//
//                    } else {
//
//                    }
                    return resolve(response);
                }
            });
        })
    }

</script>

<script>
    calculateReplacement();
    calculateLabour();
    calculateTotalPayeeAmount();
    //  calculateUnderInsurancePenaltyAmount();
    //   calculateBaldTyrePenaltyAmount();
    calculatePayableAmount();
</script>


<c:if test="${successMessage!=null && successMessage!=''}">
    <script type="text/javascript">
        notify('${successMessage}', "success");
    </script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
    <script type="text/javascript">
        notify('${errorMessage}', "danger");
    </script>
</c:if>
</body>
</html>

<script>
    $(document).ready(function () {
        $('.viewDocumentContainere').hide();
        // Add drag and resize option to panel
        // Expand and collaps the toolbar
        $("#toggle-toolbox-tools").on("click", function () {
            var panel = $("#toolbox-tools");

            if ($(panel).data("org-height") == undefined) {
                $(panel).data("org-height", $(panel).css("height"));
                $(panel).css("height", "41px");
            } else {
                $(panel).css("height", $(panel).data("org-height"));
                $(panel).removeData("org-height");
            }

            $(this).toggleClass('fa-chevron-down').toggleClass('fa-chevron-right');
        });


        // Make toolbar groups sortable
        $("#sortable").sortable({
            stop: function (event, ui) {
                var ids = [];
                $.each($(".draggable-group"), function (idx, grp) {
                    ids.push($(grp).attr("id"));
                });

                // Save order of groups in cookie
                //$.cookie("group_order", ids.join());
            }
        });
        $("#sortable").disableSelection();


        // Make Tools panel group minimizable
        $.each($(".draggable-group"), function (idx, grp) {
            var tb = $(grp).find(".toggle-button-group");

            $(tb).on("click", function () {
                $(grp).toggleClass("minimized");
                $(this).toggleClass("fa-caret-down").toggleClass("fa-caret-up");

                // Save draggable groups to cookie (frue = Minimized, false = Not Minimized)
                var ids = [];
                $.each($(".draggable-group"), function (iidx, igrp) {
                    var itb = $(igrp).find(".toggle-button-group");
                    var min = $(igrp).hasClass("minimized");

                    ids.push($(igrp).attr("id") + "=" + min);
                });

                $.cookie("group_order", ids.join());
            });
        });

        // Close thr panel
        $(".close-panel").on("click", function () {
            $(this).parent().parent().hide();
        });


        // Add Tooltips
        $('button').tooltip();
        $('.toggle-button-group').tooltip();

    });
    function checkPendingPaymentLimit(payableAmount,calType) {
        var claimNo = '${claimHandlerDto.claimNo}';


                    notify("")

    }


</script>
<style>
    #close-toolbox-tools {
        cursor: pointer;
    }

</style>

