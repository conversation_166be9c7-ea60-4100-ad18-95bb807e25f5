<%@taglib prefix="c" uri="jakarta.tags.core" %>

<div class=" col-sm-2 mr-auto mt-3 btn-group">
    <!--For Clai<PERSON> Handler And Spare Part Coordinator-->
    <c:if test="${((G_USER.accessUserType eq 41 or G_USER.accessUserType eq 61) and (G_USER.userId eq claimCalculationSheetMainDto.assignUserId) and (58 eq claimCalculationSheetMainDto.status or 72 eq claimCalculationSheetMainDto.status or empty claimCalculationSheetMainDto.status or 0 eq claimCalculationSheetMainDto.status))
                      or (G_USER.accessUserType eq 46 and (G_USER.userId eq claimCalculationSheetMainDto.assignUserId) and (58 eq claimCalculationSheetMainDto.status or 72 eq claimCalculationSheetMainDto.status or empty claimCalculationSheetMainDto.status or 0 eq claimCalculationSheetMainDto.status))}">
        <button id="btnDraft" type="button" class="btn btn-secondary" onclick="draft()">
            Save as Draft
        </button>
    </c:if>
</div>
<div class="col-sm-4 col-md-3 ml-auto mt-3">
    <div class="btn-group">
        <c:if test="${72 ne claimCalculationSheetMainDto.status && !docUpload}">
            <div class="btn-group" role="group">
                <button id="btnGroupVerticalDrop1" type="button" class="btn btn-secondary dropdown-toggle"
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Action
                </button>
                <div class="dropdown-menu" aria-labelledby="btnGroupVerticalDrop1">
                    <!--Claim Handler-->
                    <c:if test="${(G_USER.accessUserType eq 41 or G_USER.accessUserType eq 61 or G_USER.accessUserType eq 46) and (G_USER.userId eq claimCalculationSheetMainDto.assignUserId )}">
                        <c:if test="${58 eq claimCalculationSheetMainDto.status or 60 eq claimCalculationSheetMainDto.status or 62 eq claimCalculationSheetMainDto.status}">
                            <c:if test="${'C' ne claimCalculationSheetMainDto.noObjectionStatus}">
                                <a class="dropdown-item" href="#" onclick="showEmailConfirmation(1)">Call For No
                                    Objection</a>
                            </c:if>
                            <c:if test="${claimCalculationSheetMainDto.noObjectionStatus=='C'
                        && (claimCalculationSheetMainDto.isNoObjectionUpload=='P' || claimCalculationSheetMainDto.isNoObjectionUpload=='R')}">
                                <a class="dropdown-item" href="#" onclick="revokeNoObjection()">Revoke For No
                                    Objection</a>
                            </c:if>
                            <c:if test="${'C' ne claimCalculationSheetMainDto.premiumOutstandingStatus}">
                                <a class="dropdown-item" href="#" onclick="showEmailConfirmation(2)">Call Premium
                                    Outstanding</a>
                            </c:if>
                            <c:if test="${'C' ne claimCalculationSheetMainDto.ncbStatus}">
                                <a class="dropdown-item" href="#" onclick="showEmailConfirmation(3)">Call NCB
                                    Confirmation</a>
                            </c:if>
                            <c:if test="${claimCalculationSheetMainDto.premiumOutstandingStatus =='C'
                        && (claimCalculationSheetMainDto.isPremiumOutstandingUpload == 'P' || claimCalculationSheetMainDto.isPremiumOutstandingUpload == 'R')}">
                                <a class="dropdown-item" href="#" onclick="revokePremiumOutstanding()">Revoke Premium
                                    Outstanding</a>
                            </c:if>
                        </c:if>
                        <!--58 CALP Calculation Verify Pending 5-->
                        <c:if test="${58 eq claimCalculationSheetMainDto.status}">
                            <a class="dropdown-item" href="#" onclick="forwardToSparePartCordinator()">Forward To Spare
                                Parts Coordinator</a>
                        </c:if>
                        <c:if test="${58 eq claimCalculationSheetMainDto.status}">
                            <a class="dropdown-item" href="#" onclick="forwardToScrutinizingTeamByClaimHandler()">Forward
                                To
                                Scrutinizing Team</a>
                        </c:if>
                        <!--60 CALC Checked & Calculation sheet created by SP 5-->
                        <!--62 CALCF Checked & Calculation sheet verify by Scrutinizing Team 5-->
                        <c:if test="${58 eq claimCalculationSheetMainDto.status or 60 eq claimCalculationSheetMainDto.status or 62 eq claimCalculationSheetMainDto.status}">
                            <a class="dropdown-item" id="btnForwardCalSheet" href="#"
                               onclick="forwardToSpecialTeam(this.value)">Forward To Special
                                Team</a>
                            <script>
                                validateIfReserveExceed();

                                async function validateIfReserveExceed() {
                                    var isExceed = await checkAcrAmountAndPaybleAmount(parseFloat($('#payableAmount').val()));
                                    if (isExceed != 'Fail' && isExceed != 'Success') {
                                        $("#btnForwardCalSheet").val(1);
                                        $("#btnForwardCalSheet").text('Forward to RTE');
                                    } else {
                                        $("#btnForwardCalSheet").val(0);
                                        $("#btnForwardCalSheet").text('Forward To Special Team');
                                    }
                                }
                            </script>
                        </c:if>
                        <c:if test="${59 eq claimCalculationSheetMainDto.status or 61 eq claimCalculationSheetMainDto.status or 60 eq claimCalculationSheetMainDto.status or 62 eq claimCalculationSheetMainDto.status}">
                            <a class="dropdown-item" href="#" onclick="recallByClaimHandler()">Recall</a>
                        </c:if>
                    </c:if>
                    <c:if test="${63 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.assignUserId}">
                        <c:if test="${63 eq claimCalculationSheetMainDto.status}">
                            <a class="dropdown-item" href="#" onclick="recallCalsheetforwardedtoSpTeam()">Recall</a>
                        </c:if>
                    </c:if>
                    <!--Spare Part Coordinator-->
                    <c:if test="${G_USER.accessUserType eq 27 and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId}">
                        <!--59 CALF Forwarded to the SP for the creation of Calculation sheet 5-->
                        <c:if test="${59 eq claimCalculationSheetMainDto.status}">
<%--                            <c:if test="${!pendingInspection}">--%>
                                <a class="dropdown-item" href="#" onclick="forwardToScrutinizingTeam()">Forward To
                                    Scrutinizing
                                    Team</a>
<%--                            </c:if>--%>
                            <a class="dropdown-item" href="#" onclick="returnToClaimHandlerBySpc()">Return To Claim
                                Handler</a>
                        </c:if>
                    </c:if>
                    <!--Scrutinizing Team-->
                    <c:if test="${G_USER.accessUserType eq 28 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId}">
                        <!--61 CALCR Forwarded to the Scrutinizing Team for the recommendation of the calculation sheet 5-->
                        <c:if test="${61 eq claimCalculationSheetMainDto.status}">
                            <a class="dropdown-item" href="#" onclick="returnToClaimHandlerByStm()">Return To Claim
                                Handler</a>
                            <a class="dropdown-item" href="#" onclick="returnToSparePartCoordinatorByStm()">Return
                                To
                                Spare
                                Parts Coordinator</a>
                        </c:if>
                    </c:if>
                    <!--Special Team-->
                    <c:if test="${(G_USER.accessUserType eq 43 or G_USER.accessUserType eq 63) and 63 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.specialTeamAssignUserId}">

                        <c:choose>
                            <c:when test="${claimCalculationSheetMainDto.userAuthorityLimitDto.levelCode eq 1}">
                                <a class="dropdown-item" href="#" onclick="approvePayment(1)">Payment Approve</a>
                                <a class="dropdown-item" href="#" onclick="rejectPayment()">Payment Reject</a>
                            </c:when>
                            <c:otherwise>
                                <a class="dropdown-item" href="#" onclick="recommendAndForwardToNextLevel(1)">Recommend
                                    &
                                    Forward to Next Level</a>
                            </c:otherwise>
                        </c:choose>

                        <a class="dropdown-item" href="#" onclick="returnToClaimHandlerBySpecialTeam()">Return To Claim
                            Handler</a>
                    </c:if>
                    <!--65	CALAPR	Payment Approved	5-->
                    <c:if test="${(G_USER.accessUserType eq 43 or G_USER.accessUserType eq 63) and G_USER.userId eq claimCalculationSheetMainDto.specialTeamAssignUserId and 65 eq claimCalculationSheetMainDto.status}">
                        <a class="dropdown-item" href="#" onclick="generateVoucher()">Voucher Generate</a>
                        <a class="dropdown-item" href="#" onclick="showRemark('Y')">Return To Claim
                            Handler</a>
                    </c:if>
                    <!--MOFA-->
                    <c:if test="${(G_USER.accessUserType eq 42 or G_USER.accessUserType eq 62 or G_USER.accessUserType eq 47 or G_USER.accessUserType eq 48) and 64 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.specialTeamMofaAssignUserId}">
                        <c:choose>
                            <c:when test="${claimCalculationSheetMainDto.userAuthorityLimitDto.levelCode le claimCalculationSheetMainDto.userAuthorityLimitDto.logMofaUserLevelCode}">
                                <a class="dropdown-item" href="#" onclick="approvePayment(2)">Payment Approve</a>
                                <a class="dropdown-item" href="#" onclick="rejectPayment()">Payment Reject</a>
                                <a class="dropdown-item" href="#" onclick="returnToClaimHandlerByMofa()">Return To Claim
                                    Handler</a>
                            </c:when>
                            <c:otherwise>
                                <a class="dropdown-item" href="#" onclick="recommendAndForwardToNextLevel(2)">Recommend
                                    &
                                    Forward to Next Level</a>
                                <a class="dropdown-item" href="#" onclick="returnToClaimHandlerByMofa()">Return To Claim
                                    Handler</a>
                            </c:otherwise>
                        </c:choose>
                    </c:if>

                        <%--                        Motor Engineer Department--%>
                    <c:if test="${claimCalculationSheetMainDto.status eq 71 and G_USER.userId eq claimCalculationSheetMainDto.rteAssignUserId}">
                        <a class="dropdown-item" href="#" onclick="showRemark('N')">Approve Reserve & Proceed</a>
                        <a class="dropdown-item" href="#" onclick="showRemark('Y')">Return to Claim Handler</a>
                    </c:if>

                </div>
            </div>
        </c:if>
        <!--For Claim Handler And Spare Part Coordinator-->
        <c:if test="${(((G_USER.accessUserType eq 41 or G_USER.accessUserType eq 61) and (G_USER.userId eq claimCalculationSheetMainDto.assignUserId) and (58 eq claimCalculationSheetMainDto.status or 72 eq claimCalculationSheetMainDto.status or empty claimCalculationSheetMainDto.status or 0 eq claimCalculationSheetMainDto.status))
                      or (G_USER.accessUserType eq 27 and 59 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId and !pendingInspection)
                      or (G_USER.accessUserType eq 28 and 61 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId and !pendingInspection)
                      or (G_USER.accessUserType eq 46 and (G_USER.userId eq claimCalculationSheetMainDto.assignUserId) and (58 eq claimCalculationSheetMainDto.status or 72 eq claimCalculationSheetMainDto.status or empty claimCalculationSheetMainDto.status or 0 eq claimCalculationSheetMainDto.status))) and !docUpload}">
            <button id="submitBtn" type="button" class="btn btn-primary" onclick="checkAlreadySavedAndSaveCalsheet();">
                Save
            </button>
        </c:if>
        <c:if test="${claimCalculationSheetMainDto.calSheetType eq 5
            and claimCalculationSheetMainDto.status eq 65
            and claimCalculationSheetMainDto.assignUserId eq G_USER.userId
            and (claimHandlerDto.isFileStore eq 'N' or claimHandlerDto.isFileStore eq 'AR' )
            and !docUpload}">
            <button type="button" onclick="genReleaseLatter()" class="btn btn-success" style="margin-left: -130px;"><i
                    class="fa fa-print" style="margin-right: 10px"></i>
                Generate Release Order Letter
            </button>
        </c:if>

        <c:if test="${(claimCalculationSheetMainDto.status eq 67
            or claimCalculationSheetMainDto.status eq 65) and !docUpload}">
            <button type="button" onclick="printCalculationSheet()" class="btn btn-success" style="margin-left: 10px;">
                <i class="fa fa-print" style="margin-right: 10px"></i>
                Print Calculation Sheet
            </button>
        </c:if>

    </div>
</div>

<script type="text/javascript">
    var count = $(".dropdown-menu .dropdown-item").length;
    if (count == 0) {
        $('.dropdown-menu').hide();
        $('#btnGroupVerticalDrop1').hide();
    }
    $('.dropdown-item').bind('contextmenu', function (e) {
        e.preventDefault();
    });
</script>
