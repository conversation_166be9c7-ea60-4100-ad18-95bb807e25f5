<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 11/3/2018
  Time: 6:33 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<div>
    <c:set var="replacementTableName" value="replacement"/>
    <table style="width: 100%;" class="table-responsive sfont">
        <thead>
        <tr>
            <th scope="col" class="tbl_row_header" style="width: 4%;">Item No</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Approved <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">VAT Rate (%)</th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Remove VAT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailReplacementAllRemoveVat"
                                   id="allReplacementRemoveVat" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y" onchange="selectAllRemoveVatCheckBoxes('replacement')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Amount <br>Without <br>VAT <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">O/A (%)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">O/A <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Total <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">NBT <br>Rate <br>(%)</th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Add<br>NBT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailReplacementAllAddNbt"
                                   id="allReplacementAddNbt" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y" onchange="selectAllAddNbtCheckBoxes('replacement')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">NBT <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Amount <br>with <br>NBT <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">VAT <br>Rate <br>(%)</th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Add <br>VAT <br>Without <br>NBT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailReplacementAllAddVatWithoutNbt"
                                   id="allReplacementAddVatWithoutNbt" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y" onchange="selectAllAddVatWithoutNbtCheckBoxes('replacement')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Add <br>VAT <br>With <br>NBT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailReplacementAllAddVatWithNbt"
                                   id="allReplacementAddVatWithNbt" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y"
                                   onchange="selectAllAddVatWithNbtCheckBoxes('replacement')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">VAT <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Total <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">Bills <br>Checked</th>
            <th scope="col" class="tbl_row_header" style="width: 2%;">Action</th>
        </tr>
        </thead>
        <tbody id="tblBody1">
        <c:set var="replacementUniqueTableName" value="1000"/>
        <c:set var="replacementCount" value="0"/>
        <c:forEach var="item"
                   items="${claimCalculationSheetMainDto.claimCalculationSheetDetailReplacementDtos}"
                   varStatus="index">
            <c:set var="replacementCount" value="${index.count}"/>
            <tr id="tr-${replacementUniqueTableName}-${replacementCount}">
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control item-no-class indexEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].itemNo"
                               readonly="true"
                               placeholder="Item No" value="${index.count}"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control  classReplacementApprovedAmount text-right approvedAmountEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].approvedAmount"
                               placeholder="Approved Amount" value="${item.approvedAmount}" autocomplete="off"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailReplacementDtos[${index.count}].removeVatRate"
                                class="form-control form-control-sm removeVatRateEvent-${replacementCount}-${replacementUniqueTableName}">
                                ${claimCalculationSheetMainDto.vatRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailReplacementDtos[${index.count}].removeVatRate']").val('${item.removeVatRate}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2 dynacheckcontainer">
                            <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].isRemoveVat"
                                   id="isRemoveVat" title="" type="checkbox"
                                   class="align-middle isReplacementRemoveVat isRemoveVatEvent-${replacementCount}-${replacementUniqueTableName}"
                                   value="Y" ${item.isRemoveVat eq 'Y' ? 'checked' : ''} />
                            <span class="checkmark dynacheck"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right amountWithoutVatEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].amountWithoutVat"
                               placeholder="Amount Without VAT" value="${item.amountWithoutVat}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailReplacementDtos[${index.count}].oa"
                                class="form-control form-control-sm oaEvent-${replacementCount}-${replacementUniqueTableName}">
                                ${claimCalculationSheetMainDto.oaRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailReplacementDtos[${index.count}].oa']").val('${item.oa}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right oaAmountEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].oaAmount"
                               placeholder="O/A Amount" value="${item.oaAmount}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right totalAmountAfterOaEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].totalAmountAfterOa"
                               placeholder="Total Amount" value="${item.totalAmountAfterOa}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailReplacementDtos[${index.count}].nbtRate"
                                class="form-control form-control-sm nbtRateEvent-${replacementCount}-${replacementUniqueTableName}">
                                ${claimCalculationSheetMainDto.nbtRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailReplacementDtos[${index.count}].nbtRate']").val('${item.nbtRate}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2 dynacheckcontainer">
                            <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].isAddNbt"
                                   id="isAddNbt" title="" type="checkbox"
                                   class="align-middle isReplacementAddNbt isAddNbtEvent-${replacementCount}-${replacementUniqueTableName}"
                                   value="Y" ${item.isAddNbt eq 'Y' ? 'checked' : ''} />
                            <span class="checkmark dynacheck"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right nbtAmountEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].nbtAmount"
                               placeholder="NBT Amount" value="${item.nbtAmount}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control  text-right amountWithNbtEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].amountWithNbt"
                               placeholder="Amount With NBT" value="${item.amountWithNbt}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailReplacementDtos[${index.count}].vatRate"
                                class="form-control form-control-sm vatRateEvent-${replacementCount}-${replacementUniqueTableName}">
                                ${claimCalculationSheetMainDto.vatRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailReplacementDtos[${index.count}].vatRate']").val('${item.vatRate}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container check-container2 dynaradiocontainer">
                            <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].addVatType"
                                   type="radio"
                                   value="WITHOUTNBT"
                                   class="align-middle replacementAddVatWithoutNbt addVatWithoutNbtEvent-${replacementCount}-${replacementUniqueTableName} addVatTypeEvent-${replacementCount}-${replacementUniqueTableName} "
                                ${item.addVatType eq 'WITHOUTNBT' ? 'checked' : ''}/>
                            <span class="radiomark dynaradio"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container check-container2 dynaradiocontainer">
                            <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].addVatType"
                                   type="radio"
                                   value="WITHNBT"
                                   class="align-middle replacementAddVatWithNbt addVatWithNbt-${replacementCount}-${replacementUniqueTableName} addVatTypeEvent-${replacementCount}-${replacementUniqueTableName} "
                                ${item.addVatType eq 'WITHNBT' ? 'checked' : ''}/>
                            <span class="radiomark dynaradio"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right vatAmountEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].vatAmount"
                               readonly="true"
                               placeholder="VAT Amount" value="${item.vatAmount}"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right totalAmountEvent-${replacementCount}-${replacementUniqueTableName}"
                               name="claimCalculationSheetDetailReplacementDtos[${index.count}].totalAmount"
                               readonly="true"
                               placeholder="Total Amount" value="${item.totalAmount}"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2 dynacheckcontainer check1">
                            <input name="claimCalculationSheetDetailReplacementDtos[${index.count}].billChecked"
                                   id="checkReminderPrint0" title="" type="checkbox"
                                   class="align-middle billchecked ${item.calSheetDetailId}"
                                   value="Y" ${item.billChecked eq 'Y' ? 'checked' : ''}
                                   onclick="billCheck(${item.calSheetDetailId})"/>
                            <span class="checkmark dynacheck"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                              <span class="input-group-btn">
                                        <button type="button"
                                                class="btn btn-default removeButtonReplacement" ${HISTORY_RECORD == 'Y' or claimCalculationSheetMainDto.status ==67 or
                                                claimCalculationSheetMainDto.status ==63 or claimCalculationSheetMainDto.status ==64
                                                or claimCalculationSheetMainDto.status ==65  ? 'disabled':''}
                                                onclick="deleteRow(${replacementUniqueTableName},${index.count})"><i
                                                class="fa fa-minus"></i></button>
                                    </span>
                </td>
            </tr>
            <script>
                registerEvent(${replacementUniqueTableName}, ${index.count});
            </script>
        </c:forEach>
        </tbody>
    </table>

    <table class="table table-bordered tablestyle table-responsive" style="font-size: 80%;">
        <tbody>
        <tr style="border-top: 2px solid #dee2e6;">

            <td scope="col" class="tbl_row_header colorwhite" width="4%"
                style="background: #fffff5!important; color:black"></td>
            <td scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="replacementTotalApproveAmount" class="form-control  text-right bg-badge-succes">0.00</div>
            </td>
            <td scope="col" class="tbl_row_header colorwhite" width="6%"></td>

            <td scope="col" class="tbl_row_header colorwhite" width="3%"></td>
            <td scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="replacementTotalAmountWithoutVat" class="form-control  text-right bg-badge-succes">0.00</div>
            </td>
            <td scope="col" class="tbl_row_header colorwhite" width="6%"></td>

            <td scope="col" class="tbl_row_header colorwhite" width="6%">
                <div id="replacementOaAmount" class="form-control  text-right bg-badge-succes">0.00</div>
                <input type="hidden" id="replacementOa" name="replacementOa" value="0"/>
            </td>
            <td scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="replacementTotalAmountAfterOa" class="form-control  text-right bg-badge-succes">0.00</div>
            </td>
            <td scope="col" class="tbl_row_header colorwhite" width="6%"></td>

            <td scope="col" class="tbl_row_header colorwhite" width="3%"></td>
            <td scope="col" class="tbl_row_header colorwhite" width="6%">
                <div id="replacementNbtAmount" class="form-control  text-right bg-badge-succes">0.00</div>
                <input type="hidden" id="replacementNbt" name="replacementNbt" value="0"/>
            </td>
            <td scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="replacementTotalAmountWithNbt" class="form-control  text-right bg-badge-succes">0.00</div>
            </td>

            <td scope="col" class="tbl_row_header colorwhite" width="6%"></td>
            <td scope="col" class="tbl_row_header colorwhite" width="2%"></td>
            <td scope="col" class="tbl_row_header colorwhite" width="2%"></td>


            <td scope="col" class="tbl_row_header colorwhite" width="6%">
                <div id="replacementVatAmount" class="form-control  text-right bg-badge-succes">0.00</div>
                <input type="hidden" id="replacementVat" name="replacementVat" value="0"/>
            </td>
            <td scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="replacementTotalAmount" class="form-control  text-right bg-badge-succes">0.00</div>
            </td>
            <td scope="col" class="tbl_row_header colorwhite" width="2%"></td>
            <td scope="col" class="tbl_row_header colorwhite" width="2%">
                <div class="input-group-btn float-right pr-1">
                    <button class="btn btn-primary" type="button" id="cmdReplacement"><i
                            class="fa fa-plus"></i></button>
                </div>
            </td>

        </tr>
        </tbody>
    </table>


</div>
