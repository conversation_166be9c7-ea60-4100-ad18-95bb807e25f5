<%--
  Created by IntelliJ IDEA.
  User: Thanura
  Date: 1/28/2020
  Time: 4:55 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="card-body">
    <div class=" form-group row">
        <form id="frmPreviousClaims" name="frmPreviousClaims">
            <input type="hidden" id="P_N_CLIM_NO" name="P_N_CLIM_NO"/>
        </form>
        <div class="col-md-12">
            <table width="100%" cellpadding="0" cellspacing="1"
                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                <thead>
                <tr>
                    <th scope="col" class="tbl_row_header">Claim No</th>
                    <th scope="col" class="tbl_row_header">Policy No</th>
                    <th scope="col" class="tbl_row_header">Accident Date</th>
                    <th scope="col" class="tbl_row_header">Total Approved ACR(Rs.)</th>
                    <th scope="col" class="tbl_row_header">Value Of Claim
                        (Rs.)
                    </th>
                    <th scope="col" class="tbl_row_header">Liability Status</th>
                    <th scope="col" class="tbl_row_header">Finalize Status</th>
                    <th scope="col" class="tbl_row_header"></th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="claims" items="${previousClaimList}">
                    <tr>
                        <td>${claims.claimNo}</td>
                        <td>${claims.policyNumber}</td>
                        <td>${claims.accidDate}</td>
                        <td style="text-align: right">${claims.totalAcr}</td>
                        <td style="text-align: right">${claims.claimReserve}</td>
                        <td>${claims.claimStatusDesc}</td>
                        <td>
                            <c:choose>
                                <c:when test="${claims.closeStatus == 'PENDING'}">
                                    PENDING
                                </c:when>
                                <c:when test="${claims.closeStatus == 'REOPEN'}">
                                    REOPEN
                                </c:when>
                                <c:when test="${claims.closeStatus == 'SETTLE'}">
                                    SETTLE
                                </c:when>
                                <c:when test="${claims.closeStatus == 'SETTLE_PENDING'}">
                                    SETTLE PENDING
                                </c:when>
                                <c:when test="${claims.closeStatus == 'PARTIALLY_SETTLE'}">
                                    PARTIALLY SETTLE
                                </c:when>
                                <c:when test="${claims.closeStatus == 'CLOSE'}">
                                    CLOSE
                                </c:when>
                            </c:choose>
                        </td>
                        <c:if test="${not IS_PREVIOUS_CLAIM}">
                            <td>
                                <a id="clm${claims.claimNo}" class="btn-primary btn btn-sm float-right"
                                   href="${pageContext.request.contextPath}/ClaimHandlerController/viewEdit?P_N_CLIM_NO=${claims.claimNo}&IS_PREVIOUS_CLAIM=true"
                                   type="button" title="View Claim">
                                    <i class="fa fa-eye"></i></a>
                                <script>
                                    $('#clm${claims.claimNo}').popupWindow({
                                        height: screen.height,
                                        width: screen.width,
                                        resizable: 1,
                                        status: 1,
                                        centerScreen: 1,
                                        scrollbars: 1,
                                        top: 1,
                                        windowName: 'win' +${claims.claimNo}
                                    });
                                </script>
                            </td>
                        </c:if>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>
