<%--
  Created by IntelliJ IDEA.
  User: thanura
  Date: 8/14/2020
  Time: 12:20 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="card-body">
    <div class="form-group">
        <div class="row">
            <div class="col-md-12" style="text-align: center; background-color: #025AAA; color: white; margin-bottom: 2vh;"><span>${panelDecisionDto.panelDecision}</span></div>
            <div class="col-sm-6">
                <span>Decision Maker :</span>
                <span class="label_Value"
                      id="txtDmaker">${panelDecisionDto.dMaker}</span>
            </div>
            <div class="col-sm-6"/>
            <div class="col-sm-6">
                <span>Rejection Reason :</span>
                <span class="label_Value"
                      id="txtReason">${panelDecisionDto.rejectionReason}</span>
            </div>
            <div class="col-sm-6">
                <span>Panel Decision :</span>
                <span class="label_Value"
                      id="txtpanelDecision">${panelDecisionDto.panelDecision}</span>
            </div>
<%--            <div class="col-sm-6">--%>
<%--                <span>Comment :</span>--%>
<%--                <span class="label_Value"--%>
<%--                      id="txtComment">${panelDecisionDto.dmComment}</span>--%>
<%--            </div>--%>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <table width="100%" cellpadding="0" cellspacing="1"
                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                <thead>
                <tr>
                    <th scope="col" class="tbl_row_header">Panel Date & Time</th>
                    <th scope="col" class="tbl_row_header" style="width: 17%">Assign Date & Time</th>
                    <th scope="col" class="tbl_row_header">Panel Members</th>
                    <th scope="col" class="tbl_row_header">Panel Decision</th>
                    <th scope="col" class="tbl_row_header" style="width: 17%">Approved/ Rejected Date & Time</th>
                    <th scope="col" class="tbl_row_header">Panel Member Comment</th>
                    <th scope="col" class="tbl_row_header">DM Comment</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="panelUser" items="${panelDecisionDto.userDtos}">
                    <tr style="background-color: ${panelUser.highlightColor}">
                        <td>${panelUser.panelDate}</td>
                        <td>${panelUser.assignDateTime}</td>
                        <td>${panelUser.userName}</td>
                        <c:choose>
                            <c:when test="${panelUser.decision == 'A'}">
                                <td>APPROVED</td>
                            </c:when>
                            <c:when test="${panelUser.decision == 'P'}">
                                <td>PENDING</td>
                            </c:when>
                            <c:when test="${panelUser.decision == 'R'}">
                                <td>REJECTED</td>
                            </c:when>
                            <c:when test="${panelUser.decision == 'D'}">
                                <td>RETURNED</td>
                            </c:when>
                            <c:otherwise>
                                <td>N/A</td>
                            </c:otherwise>
                        </c:choose>
                        <td>${panelUser.decisionDateTime}</td>
                        <td>${panelUser.comment}</td>
                        <td>${panelUser.dmRemark}</td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        if ('${panelDecisionDto.isPanelDecision}') {
            document.getElementById('divPanelDecision').style.backgroundColor ='#fffa65';
        }
    });
</script>