<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/23/2018
  Time: 9:06 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>

<a href="#" class="p-2 showHideBtn" onclick="showHide();"><i class="fa fa-angle-left fa-2x"></i></a>
<c:if test="${claimDocumentDto.documentStatus=='A'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/checked.png" class="ml-3" width="100"
         height="50">
</c:if>
<c:if test="${claimDocumentDto.documentStatus=='H'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/hold_documnet.png" class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${claimDocumentDto.documentStatus=='R'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/reject_document.png" class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${claimDocumentDto.documentStatus=='C'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/cancelled.png" class="ml-3" width="100"
         height="100">
</c:if>

<button id="btnGroupDrop1" type="button" class="btn btn-primary dropdown-toggle"
        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
    Check/Hold/Reject
</button>

<div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
    <c:if test="${claimDocumentDto.documentStatus!='A' && claimDocumentDto.documentStatus!='R' && claimDocumentDto.documentStatus!='C'}">
        <a class="dropdown-item" href="#" onclick="checkDocument('A')">Check Document</a>
    </c:if>
    <c:if test="${claimDocumentDto.documentStatus!='R' && claimDocumentDto.documentStatus!='C'}">
        <%--<a class="dropdown-item" href="#" onclick="checkDocument('R')">Reject Document</a>--%>
        <a class="dropdown-item" href="#"  onclick="rejectModal()">Reject Document</a>
    </c:if>
    <c:if test="${claimDocumentDto.documentStatus!='R' && claimDocumentDto.documentStatus!='H' && claimDocumentDto.documentStatus!='C'}">
        <a class="dropdown-item" href="#" onclick="checkDocument('H')">Hold Document</a>
    </c:if>
</div>

<c:if test="${claimDocumentDto.documentStatus=='A'}">

    <hr>
    <h4 class="text-left"> Status : <span class="badge badge-success float-right" style="font-weight: bold;padding: 3px;">Checked</span></h4>
    <hr class="my-1">
    <p class="mb-0">Checked User :</p>
    <h4>${claimDocumentDto.checkUser}</h4>
    <hr class="my-1">
    <p class="mb-0">Checked Date/Time : </p>
    <h4 class="mb-0">${claimDocumentDto.checkDateTime}</h4>
</c:if>

<c:if test="${claimDocumentDto.documentStatus=='R'}">

    <hr>
    <h4 class="text-left">Status : <span class="badge badge-danger float-right">Rejected</span></h4>
    <hr class="my-1">
    <p class="mb-0">Reject User :</p>
    <h4 class="mb-0">${claimDocumentDto.rejectUser}</h4>
    <hr class="my-1">
    <p class="mb-0">Reject Date/Time :</p>
    <h4 class="mb-0">${claimDocumentDto.rejectDateTime}</h4>
    <hr class="my-1">
    <p class="mb-0">Reject Reason :</p>
    <h4 class="mb-0">${claimDocumentDto.remark}</h4>
</c:if>

<c:if test="${claimDocumentDto.documentStatus=='H'}">

    <hr>
    <h4  class="text-left">Status :<span class="badge badge-warning float-right">Hold</span></h4>
    <hr class="my-1">
    <p class="mb-0">Hold User :</p>
    <h4 class="mb-0">${claimDocumentDto.holdUser}</h4>
    <hr class="my-1">
    <p class="mb-0">Hold Date/Time :</p>
    <h4 class="mb-0">${claimDocumentDto.holdDateTime}</h4>
</c:if>

<c:if test="${claimDocumentDto.documentStatus=='C'}">

    <hr>
    <h4 class="text-left">Status : <span class="badge badge-danger float-right">Cancelled</span></h4>
    <hr class="my-1">
    <p class="mb-0">Cancel User :</p>
    <h4 class="mb-0">${claimDocumentDto.cancelUser}</h4>
    <hr class="my-1">
    <p class="mb-0">Cancel Date/Time :</p>
    <h4 class="mb-0">${claimDocumentDto.cancelDateTime}</h4>
    <hr class="my-1">
    <p class="mb-0">Cancel Reason :</p>
    <h4 class="mb-0">${claimDocumentDto.cancelRemark}</h4>
</c:if>

<script>
    if (${claimDocumentDto.documentStatus!='P' && claimDocumentDto.documentStatus!='H' && claimDocumentDto.documentStatus!='C'}) {
        $('#btnChecknSave').hide();
    } else {
        $('#btnChecknSave').show();
    }
</script>
