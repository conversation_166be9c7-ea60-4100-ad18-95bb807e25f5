<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        var type = '${TYPE}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);


        });

        function init() {
            hideLoader();
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="N_TXN_NO" id="N_TXN_NO" type="hidden"/>
        <div class="row">
            <c:choose>
                <c:when test="${TYPE==10}">
                    <div class="col-sm-12 bg-dark py-2">
                        <h5>All Claim Details Lists</h5>
                    </div>
                </c:when>
                <c:otherwise>
                    <div class="col-sm-12 bg-dark py-2">
                        <h5>Claim Details List</h5>
                    </div>
                </c:otherwise>
            </c:choose>

        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="false" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                       value="${searchFromDate}"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle /
                                                Trade Plate Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle / Trade Plate Number"
                                                       value="${searchVehicleNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number"
                                                       value="${searchClaimNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number"
                                                       value="${searchPolicyNo}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <c:forEach var="listDto" items="${statusList}">
                                                        <c:choose>
                                                            <c:when test="${listDto.value==50 || listDto.value==36}">
                                                                <option style="color: #007fff;"
                                                                        value="${listDto.value}">${listDto.label}</option>
                                                            </c:when>
                                                            <c:when test="${listDto.value==17 || listDto.value==35}">
                                                                <option style="color: red;"
                                                                        value="${listDto.value}">${listDto.label}</option>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <option value="${listDto.value}">${listDto.label}</option>
                                                            </c:otherwise>
                                                        </c:choose>

                                                    </c:forEach>


                                                    <script type="text/javascript">
                                                        $('#txtV_status').val('${searchClaimStatus}');
                                                    </script>
                                                    <c:choose>
                                                        <c:when test="${TYPE==10}">
                                                            <script type="text/javascript">
                                                                if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                    $('#txtV_status').val("0");
                                                                }
                                                            </script>
                                                        </c:when>

                                                        <c:when test="${(sessionClaimUserTypeDto.initLiabilityUser || sessionClaimUserTypeDt.offerTeamInitLiabilityUser
                                                        || sessionClaimUserTypeDto.claimHandlerUser || sessionClaimUserTypeDto.offerTeamClaimHandlerUser) && TYPE==21}">
                                                            <script type="text/javascript">
                                                                if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                    $('#txtV_status').val("38");
                                                                }
                                                            </script>
                                                        </c:when>


                                                        <c:when test="${sessionClaimUserTypeDto.decisionMaker}">
                                                            <script type="text/javascript">
                                                                if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                    $('#txtV_status').val("38");
                                                                }
                                                            </script>

                                                            <c:if test="${TYPE==22}">
                                                                <script type="text/javascript">
                                                                    if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                        $('#txtV_status').val("41");
                                                                    }
                                                                </script>
                                                            </c:if>
                                                            <c:if test="${TYPE==23}">
                                                                <script type="text/javascript">
                                                                    if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                        $('#txtV_status').val("42");
                                                                    }
                                                                </script>
                                                            </c:if>
                                                            <c:if test="${TYPE==24}">
                                                                <script type="text/javascript">
                                                                    if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                        $('#txtV_status').val("44");
                                                                    }
                                                                </script>
                                                            </c:if>
                                                            <c:if test="${TYPE==25}">
                                                                <script type="text/javascript">
                                                                    if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                        $('#txtV_status').val("45");
                                                                    }
                                                                </script>
                                                            </c:if>
                                                            <c:if test="${TYPE==26}">
                                                                <script type="text/javascript">
                                                                    if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                        $('#txtV_status').val("68");
                                                                    }
                                                                </script>
                                                            </c:if>

                                                        </c:when>
                                                        <c:when test="${sessionClaimUserTypeDto.twoPanelUser}">
                                                            <script type="text/javascript">
                                                                if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                    $('#txtV_status').val("39");
                                                                }
                                                            </script>
                                                        </c:when>

                                                        <c:when test="${sessionClaimUserTypeDto.claimHandlerUser || sessionClaimUserTypeDto.offerTeamClaimHandlerUser}">
                                                            <script type="text/javascript">
                                                                if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                    $('#txtV_status').val("36");
                                                                }
                                                            </script>
                                                        </c:when>

                                                        <c:when test="${sessionClaimUserTypeDto.sparePartsCoordinator || sessionClaimUserTypeDto.scrutinizingTeam}">
                                                            <script type="text/javascript">
                                                                if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                    $('#txtV_status').val("50");
                                                                }
                                                            </script>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <script type="text/javascript">
                                                                if (null == $('#txtV_status').val() || $('#txtV_status').val() == '') {
                                                                    $('#txtV_status').val("35");
                                                                }
                                                            </script>
                                                        </c:otherwise>
                                                    </c:choose>

                                                    <c:if test="${TYPE==29 || TYPE==30}">
                                                        <script type="text/javascript">
                                                            $('#txtV_status').val("0");
                                                        </script>
                                                    </c:if>

                                                    <c:if test="${TYPE==55 || TYPE==65}">
                                                        <script type="text/javascript">
                                                            $('#txtV_status').val("83");
                                                        </script>
                                                    </c:if>


                                                </select>
                                            </div>
                                        </div>


                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> File
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFileStatus" id="txtFileStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All">All</option>
                                                    <option value="1" selected>Active & Auto Restored</option>
                                                    <option value="2">Stored</option>
                                                    <option value="3">Auto Restored</option>
                                                </select>
                                                <script>
                                                    if (null == '${searchFileStatus}' || '${searchFileStatus}' == '') {
                                                        $('#txtFileStatus').val('All');
                                                    } else {
                                                        $('#txtFileStatus').val('${searchFileStatus}');
                                                    }
                                                </script>
                                            </div>
                                        </div>

                                        <c:if test="${TYPE==80}">
                                            <div class="form-group row">
                                                <label for="txtV_status" class="col-sm-4 col-form-label"> Priority</label>
                                                <div class="col-sm-8">
                                                    <select name="txtPriority" id="txtPriority"
                                                            class="form-control form-control-sm">
                                                        <option value="">All</option>
                                                        <option value="1">High</option>
                                                        <option value="0">Normal</option>
                                                    </select>
                                                    <script>
                                                        $("#txtPriority").val('');
                                                    </script>
                                                </div>
                                            </div>
                                        </c:if>

                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date"
                                                       value="${searchToDate}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Reference
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number"
                                                       value="${searchRefNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Finalized
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFinalizedStatus" id="txtFinalizedStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">All</option>
                                                    <c:if test="${!((sessionClaimUserTypeDto.initLiabilityUser || sessionClaimUserTypeDto.offerTeamInitLiabilityUser)  && TYPE==1)}">
                                                        <option value="CLOSE">Closed</option>
                                                    </c:if>
                                                    <option value="REOPEN">Reopened</option>
                                                    <option value="SETTLE">Settled</option>
                                                    <option value="PENDING">Pending</option>
                                                </select>
                                                <c:choose>
                                                    <c:when test="${TYPE==29}">
                                                        <script type="text/javascript">
                                                            $('#txtFinalizedStatus').val("");
                                                        </script>
                                                    </c:when>
                                                    <c:when test="${TYPE==30}">
                                                        <script type="text/javascript">
                                                            $('#txtFinalizedStatus').val("CLOSE");
                                                        </script>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <script type="text/javascript">
                                                            if (null == '${searchFinalizedStatus}' || '${searchFinalizedStatus}' == '') {
                                                                $('#txtFinalizedStatus').val("");
                                                            } else {
                                                                $('#txtFinalizedStatus').val('${searchFinalizedStatus}');
                                                            }
                                                        </script>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label">
                                                Location</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id=""
                                                       class="form-control form-control-sm" placeholder="Location"
                                                       value="${searchLocation}">
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Liability
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtLiabilityStatus" id="txtLiabilityStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All">All</option>
                                                    <option value="P">Pending</option>
                                                    <option value="A">Approved</option>
                                                    <%--option value="F">Forwarded to Higher Level</option>
                                                    <option value="H">Held</option>
                                                    <option value="R">Rejected</option--%>

                                                </select>
                                                <script type="text/javascript">
                                                    if (null == '${searchLiabilityStatus}' || '${searchLiabilityStatus}' == '') {
                                                        $("#txtLiabilityStatus").val("All");
                                                    } else {
                                                        $("#txtLiabilityStatus").val('${searchLiabilityStatus}');
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="policyChannelType" class="col-sm-4 col-form-label">Policy
                                                Channel Type</label>
                                            <div class="col-sm-8">
                                                <select name="policyChannelType" id="policyChannelType"
                                                        class="form-control form-control-sm">
                                                    <option value="">Please Select</option>
                                                    <option value="TAKAFUL">TAKAFUL</option>
                                                    <option value="CONVENTIONAL">CONVENTIONAL</option>
                                                </select>
                                                <script type="text/javascript">
                                                    $('#policyChannelType').val('${searchPolicyChannelType}');
                                                </script>
                                            </div>
                                        </div>
                                        <c:if test="${TYPE==4 || TYPE == 5 || TYPE == 50 ||TYPE ==60}">
                                            <div class="form-group row">
                                                <label for="txtV_status" class="col-sm-4 col-form-label"> Supplier Order
                                                    Status</label>
                                                <div class="col-sm-8">
                                                    <select name="txtSupplierOrderStatus" id="txtSupplierOrderStatus"
                                                            class="form-control form-control-sm">
                                                        <option value="0">All</option>
                                                        <option value="1">PENDING</option>
                                                        <option value="3">FORWARD TO SCRUTINIZING TEAM</option>
                                                        <option value="4">SCRUTINIZING TEAM APPROVED</option>
                                                        <option value="6">CLAIM HANDLER APPROVED</option>
                                                        <option value="9">LETTER GENERATED</option>
                                                        <option value="10">UPDATED</option>
                                                        <option value="11">SPARE PARTS COORDINATOR APPROVAL PENDING</option>
                                                            <%--option value="F">Forwarded to Higher Level</option>
                                                            <option value="H">Held</option>
                                                            <option value="R">Rejected</option--%>

                                                    </select>
                                                </div>
                                            </div>
                                        </c:if>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="badge badge-pill badge-priority border ">&nbsp;</span>&nbsp;&nbsp;
                                    Priority High
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-repeat text-warning">&nbsp;</span>&nbsp;&nbsp; Reopen
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-close text-danger">&nbsp;</span>&nbsp;&nbsp; Closed
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-file text-info">&nbsp;</span>&nbsp;&nbsp; File Store
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="	fa fa-car text-dark">&nbsp;</span>&nbsp;&nbsp; Partial loss
                                </p>
                            </div>

                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-car text-danger">&nbsp;</span>&nbsp;&nbsp; Total Loss
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-refresh text-warning">&nbsp;</span>&nbsp;&nbsp; Document Pending
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-check text-success">&nbsp;</span>&nbsp;&nbsp; Document Complete
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa fa-question-circle text-warning">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Arranged
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa fa-check-circle text-success">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Done
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="badge badge-pill badge-warning border ">&nbsp;</span>
                                    &nbsp;&nbsp; On Site Offer </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="badge badge-pill badge-danger border ">&nbsp;</span>
                                    &nbsp;&nbsp; Doubt Claim </p>
                            </div>
                        </div>

                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim Result</h6>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover table-striped"
                                               cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th class="">ref no</th>
                                                <th class="" width="50px">No</th>
                                                <th class="">Claim No</th>
                                                <th class="">Vehicle No</th>
                                                <th class="">Policy No</th>
                                                <th class="">Policy Channel Type</th>
                                                <th class="">Claim Status</th>
                                                <th class="">Accident Date</th>
                                                <th class="">File Assigned User</th>
                                                <th class="">File Assigned Date/Time</th>
                                                <th class="">Liability Assigned User</th>
                                                <th class="">Liability Assigned Date/Time</th>
                                                <c:choose>
                                                    <c:when test="${TYPE == 4 || TYPE == 5 || TYPE == 50 ||TYPE ==60}">
                                                        <th class="">Supply Order Status</th>
                                                        <th class="">Supply Order Assigned Date/Time</th>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <th class="">Int.Liability Assigned User</th>
                                                        <th class="">Int.Liability Assigned Date/Time</th>
                                                    </c:otherwise>
                                                </c:choose>


                                                <th class="">Document Status</th>
                                                <th class="">Document Checked</th>
                                                <th class="">Liability Status</th>
                                                <th class="">Finalize Status</th>
                                                <th class=" text-right">ACR</th>
                                                <th class=" text-right">Present Reserve Amount</th>
                                                <th class="">Loss Type</th>
                                                <th class="min-mobile"></th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade bd-example-modal-md" tabindex="-1" role="dialog"
             id="prioritySelect" aria-hidden="true" style="    background: #333333c2;">
            <input type="hidden" id="callType" name="callType"/>
            <div class="modal-dialog modal-md modal-dialog-centered">
                <div class="modal-content p-2" style="overflow: hidden">
                    <div class="modal-header  p-2">
                        <h5 class="modal-title"
                            id="modalLabel">Mark Priority</h5><br>
                    </div>
                    <div class=" mt-4">
                        <div class="col-sm-8 offset-2">
                            <div class="form-group row">
                                <input type="hidden" id="priority" name="priority"/>
                                <label class="col-sm-4 col-form-label"> Priority
                                </label>
                                <div class="col-sm-8">
                                    <select class="form-control form-control-sm" name="prioritySelection"
                                            id="prioritySelection" onchange="prioritySelect()">
                                        <option value="1">HIGH</option>
                                        <option value="0">NORMAL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label"> Remark
                                </label>
                                <div class="col-sm-8">
                                    <textarea type="text" class="form-control form-control-sm"
                                              placeholder="Please Enter Remark"
                                              name="priorityRemark"
                                              id="priorityRemark" value=""></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer p-1">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" id="btnPriority"
                                onclick="markPriority()">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>


<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimhandler-datatables.js?v15"></script>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');

    });

    function prioritySelect() {
        $("#priority").val($("#prioritySelection").val());
        $('#btnPriority').prop('disabled', false);
    }

    function markPriority() {
        let url = contextPath + "/CallCenter/markPriority";
        let data = $('#frmForm').serialize();
        let val = $('#priority').val() == 1 ? "HIGH" : "NORMAL";
        let message = "Do You Want to Mark the Claim Priority as " + val;

        if ($("#priorityRemark").val() == '') {
            notify("Please enter Remark", "danger");
            return;
        }

        bootbox.confirm({
            message: message,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            }, callback: function (result) {
                if (result == true) {
                    $.ajax({
                        url: url,
                        type: 'POST',
                        data: data,
                        success: function (result) {
                            const resp = JSON.parse(JSON.stringify(result));
                            if (resp.message == 'SUCCESS') {
                                notify("Priority Changed Successfully!", "success");
                                $("#priorityRemark").val('');
                                table.ajax.reload();
                            } else {
                                notify(resp.message, "danger");
                            }
                        }
                    });
                }
            }
        });
    }
</script>
</body>
</html>
