<%@taglib prefix="c" uri="jakarta.tags.core" %>
<div class="col-sm-4 col-md-3 ml-auto mt-3">
    <div class="btn-group">
        <div class="btn-group" role="group">
            <button id="btnGroupVerticalDrop1" type="button" class="btn btn-secondary dropdown-toggle"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Action
            </button>
            <div class="dropdown-menu" aria-labelledby="btnGroupVerticalDrop1">
                <!--Claim <PERSON>-->
                <c:if test="${G_USER.accessUserType eq 41 or G_USER.accessUserType eq 61 or G_USER.accessUserType eq 46 }">
                    <c:if test="${58 eq claimCalculationSheetMainDto.status or 60 eq claimCalculationSheetMainDto.status or 62 eq claimCalculationSheetMainDto.status}">
                        <c:if test="${'C' ne claimCalculationSheetMainDto.noObjectionStatus}">
                            <a class="dropdown-item" href="#" onclick="showEmailConfirmation(1)">Call For No
                                Objection</a>
                        </c:if>
                        <c:if test="${'C' ne claimCalculationSheetMainDto.premiumOutstandingStatus}">
                            <a class="dropdown-item" href="#" onclick="showEmailConfirmation(2)">Call Premium
                                Outstanding</a>
                        </c:if>
                        <c:if test="${'C' ne claimCalculationSheetMainDto.ncbStatus}">
                            <a class="dropdown-item" href="#" onclick="showEmailConfirmation(3)">Call NCB
                                Confirmation</a>
                        </c:if>
                    </c:if>
                    <!--58 CALP Calculation Verify Pending 5-->
                    <c:if test="${58 eq claimCalculationSheetMainDto.status}">
                        <a class="dropdown-item" href="#" onclick="forwardToSparePartCordinator()">Forward To Spare
                            Parts Coordinator</a>
                    </c:if>
                    <c:if test="${58 eq claimCalculationSheetMainDto.status}">
                        <a class="dropdown-item" href="#" onclick="forwardToScrutinizingTeamByClaimHandler()">Forward To
                            Scrutinizing Team</a>
                    </c:if>
                    <!--60 CALC Checked & Calculation sheet created by SP 5-->
                    <!--62 CALCF Checked & Calculation sheet verify by Scrutinizing Team 5-->
                    <c:if test="${58 eq claimCalculationSheetMainDto.status or 60 eq claimCalculationSheetMainDto.status or 62 eq claimCalculationSheetMainDto.status}">
                        <a class="dropdown-item" href="#" onclick="forwardToSpecialTeam()">Forward To Special Team</a>
                    </c:if>
                    <c:if test="${59 eq claimCalculationSheetMainDto.status or 61 eq claimCalculationSheetMainDto.status or 60 eq claimCalculationSheetMainDto.status or 62 eq claimCalculationSheetMainDto.status}">
                        <a class="dropdown-item" href="#" onclick="recallByClaimHandler()">Recall</a>
                    </c:if>
                </c:if>
                <!--Spare Part Coordinator-->
                <c:if test="${G_USER.accessUserType eq 27 and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId}">
                    <!--59 CALF Forwarded to the SP for the creation of Calculation sheet 5-->
                    <c:if test="${59 eq claimCalculationSheetMainDto.status}">
                        <a class="dropdown-item" href="#" onclick="forwardToScrutinizingTeam()">Forward To Scrutinizing
                            Team</a>
                        <a class="dropdown-item" href="#" onclick="returnToClaimHandlerBySpc()">Return To Claim
                            Handler</a>
                    </c:if>
                </c:if>
                <!--Scrutinizing Team-->
                <c:if test="${G_USER.accessUserType eq 28 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId}">
                    <!--61 CALCR Forwarded to the Scrutinizing Team for the recommendation of the calculation sheet 5-->
                    <c:if test="${61 eq claimCalculationSheetMainDto.status}">
                        <a class="dropdown-item" href="#" onclick="returnToClaimHandlerByStm()">Return To Claim
                            Handler</a>
                        <a class="dropdown-item" href="#" onclick="returnToSparePartCoordinatorByStm()">Return To Spare
                            Parts Coordinator</a>
                    </c:if>
                </c:if>
                <!--Special Team-->
                <c:if test="${(G_USER.accessUserType eq 43 or G_USER.accessUserType eq 63) and 63 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.specialTeamAssignUserId}">
                    <a class="dropdown-item" href="#" onclick="forwardToMofa()">Forward To MOFA</a>
                    <a class="dropdown-item" href="#" onclick="approvePayment()">Payment Approve</a>
                    <a class="dropdown-item" href="#" onclick="rejectPayment()">Payment Reject</a>
                    <a class="dropdown-item" href="#" onclick="returnToClaimHandlerBySpecialTeam()">Return To Claim
                        Handler</a>
                </c:if>
                <!--65	CALAPR	Payment Approved	5-->
                <c:if test="${(G_USER.accessUserType eq 43 or G_USER.accessUserType eq 63) and G_USER.userId eq claimCalculationSheetMainDto.specialTeamAssignUserId and 65 eq claimCalculationSheetMainDto.status}">
                    <a class="dropdown-item" href="#" onclick="generateVoucher()">Voucher Generate</a>
                </c:if>
                <!--MOFA-->
                <c:if test="${(G_USER.accessUserType eq 42 or G_USER.accessUserType eq 62 or G_USER.accessUserType eq 47 or G_USER.accessUserType eq 48) and 64 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.specialTeamMofaAssignUserId}">
                    <a class="dropdown-item" href="#" onclick="approvePayment()">Payment Approve</a>
                    <a class="dropdown-item" href="#" onclick="rejectPayment()">Payment Reject</a>
                    <a class="dropdown-item" href="#" onclick="returnToClaimHandlerByMofa()">Return To Claim Handler</a>
                </c:if>
            </div>
        </div>
        <!--For Claim Handler And Spare Part Coordinator-->
        <c:if test="${(G_USER.accessUserType eq 41 or G_USER.accessUserType eq 61 and (58 eq claimCalculationSheetMainDto.status or empty claimCalculationSheetMainDto.status or 0 eq claimCalculationSheetMainDto.status))
                      or (G_USER.accessUserType eq 27 and 59 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId)
                      or (G_USER.accessUserType eq 28 and 61 eq claimCalculationSheetMainDto.status and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId)
                      or (G_USER.accessUserType eq 46 and (58 eq claimCalculationSheetMainDto.status or empty claimCalculationSheetMainDto.status or 0 eq claimCalculationSheetMainDto.status))}">
            <button id="submitBtn" type="submit" class="btn btn-primary">Save</button>
        </c:if>
        <c:if test="${claimCalculationSheetMainDto.calSheetType eq 5
            and claimCalculationSheetMainDto.status eq 65
            and claimCalculationSheetMainDto.assignUserId eq G_USER.userId
            and (claimHandlerDto.isFileStore eq 'N' or claimHandlerDto.isFileStore eq 'AR' )}">
            <button type="button" onclick="genReleaseLatter()" class="btn btn-success" style="margin-left: -130px;"><i class="fa fa-print"  style="margin-right: 10px"></i>
                Generate Release Order Letter
            </button>
        </c:if>

        <c:if test="${claimCalculationSheetMainDto.status eq 67
            or claimCalculationSheetMainDto.status eq 65}">
            <button type="button" onclick="getClaSheet()" class="btn btn-success" style="margin-left: 10px;"><i class="fa fa-print"  style="margin-right: 10px"></i>
                   Print Calculation Sheet
            </button>
        </c:if>

    </div>
</div>

<script type="text/javascript">
    var count = $(".dropdown-menu .dropdown-item").length;
    if (count == 0) {
        $('.dropdown-menu').hide();
        $('#btnGroupVerticalDrop1').hide();
    }
</script>
