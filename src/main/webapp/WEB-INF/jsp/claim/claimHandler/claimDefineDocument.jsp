<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/21/2018
  Time: 7:54 PM
  To change this template use File | Settings | File Templates.
--%>

<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<form name="frmDefineDocument" id="frmDefineDocument">
    <div class="form-group">
        <div class="input-group">
            <input type="text" name="searchDocumentName" id="searchDocumentName" class="form-control-sm form-control"
                   value="${searchValue}"/>
            <button type="button" class="btn btn-primary" onclick="loadDefineDocumentView()">Search</button>
        </div>
    </div>
    <table width="100%" cellpadding="0" cellspacing="1"
           class="table table-hover table-xs dataTable no-footer dtr-inline">
        <thead>
        <tr>
            <th scope="col" class="tbl_row_header">No</th>
            <th scope="col" class="tbl_row_header">Document Type Name</th>
            <th scope="col" class="tbl_row_header">Document Type
                Mandatory/Optional
            </th>
            <th scope="col" class="tbl_row_header">Document Required From
            </th>
        </tr>
        </thead>
        <tbody>
        <c:set var="index" value="0"/>
        <input type="hidden" name="defineDocumentCount" value="${claimWiseDocumentDtoList.size()}"/>
        <c:forEach var="claimWiseDocumentDto" items="${claimWiseDocumentDtoList}">
            <input type="hidden" name="refNo${index}" value="${claimWiseDocumentDto.refNo}"/>
            <input type="hidden" name="claimNo" id="claimNo" value="${claimWiseDocumentDto.claimNo}"/>
            <input type="hidden" name="defineStatus${index}" id="defineStatus${index}"
                   value="${claimWiseDocumentDto.isMandatory}"/>
            <c:choose>
                <c:when test="${claimWiseDocumentDto.isMandatory=='Y'}">
                    <c:set var="iconColorCls" value=" text-danger "/>
                    <c:set var="headerColorCls" value=" bg-badge-danger "/>
                </c:when>
                <c:otherwise>
                    <c:set var="iconColorCls" value=" text-primary "/>
                    <c:set var="headerColorCls" value=" bg-badge-primary "/>
                </c:otherwise>
            </c:choose>
            <tr>
                <td class="${headerColorCls}">${index+1}</td>
                <td class="${headerColorCls}">${claimWiseDocumentDto.documentTypeName}</td>
                <td class="${headerColorCls}">
                    <div class="form-group row">
                        <div class="col-sm-12 input-group">
                            <select id="isMandatory${index}" name="isMandatory${index}"
                                    class="form-control form-control-sm IntimationType">
                                <option value="Y">Mandatory</option>
                                <option value="N">Optional</option>
                            </select>
                        </div>
                    </div>
                </td>
                <td class="${headerColorCls}">
                    <div class="form-group row">
                        <div class="col-sm-12 input-group">
                            <select id="docReqFrom${index}" name="docReqFrom${index}"
                                    class="form-control form-control-sm IntimationType">
                                    ${documentReqFromList}
                            </select>
                        </div>
                    </div>
                </td>
            </tr>
            <script type="text/javascript">
                $("#isMandatory${index}").val("${claimWiseDocumentDto.isMandatory}");
                $("#docReqFrom${index}").val("${claimWiseDocumentDto.docReqFrom}");
            </script>
            <c:set var="index" value="${index+1}"/>
        </c:forEach>
        </tbody>


    </table>

    <div class="modal fade" id="defineDocumentModal" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Do you want to update define documents?</h5>

                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="defineDocRemark" class="col-form-label">Remark:</label>
                        <textarea class="form-control" id="defineDocRemark" name="defineDocRemark"
                                  onkeypress="hideErrorDiv()"></textarea>
                    </div>
                    <div class="text-danger" id="errorDiv" style="display: none">
                        This field is required
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeDiv()">Close
                    </button>
                    <button type="button" class="btn btn-primary" onclick="updateDefineDocument(true)">Save changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <c:if test="${claimWiseDocumentDtoList.size() > 0}">
        <c:if test="${IS_CLAIM_HANDLER_USER or IS_OFFER_TEAM_CLAIM_HANDLER_USER or IS_INIT_LIABILITY_USER or IS_OFFER_TEAM_INIT_LIABILITY_USER or IS_DECISION_MAKER or IS_TOTAL_LOSS_CLAIM_HANDLER_USER or IS_SPECIAL_TEAM or IS_OFFER_TEAM_SPECIAL_TEAM or IS_LETTER_PANEL_USER}">
            <button type="button" name="cmdDefineButton" id="cmdDefineButton"
                    onclick="showDefineDocument('${index}')"
                    class="btn btn-primary mt-2">
                Submit
            </button>
        </c:if>
    </c:if>
</form>
