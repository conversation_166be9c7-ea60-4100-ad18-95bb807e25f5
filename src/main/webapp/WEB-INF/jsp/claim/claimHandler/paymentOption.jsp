<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="card-body">
    <div class="row">
        <input id="policyCoverNoteNo"
               name="policyCoverNoteNo"
               type="hidden"
               value="${claimHandlerDto.claimsDto.policyDto.policyNumber}"/>

        <c:choose>
            <c:when test="${isAdvancePaymentPending eq 'Y'}">
                <h6 class="ml-3 text-danger">Please approve/reject the pending "Advance/Final" Cal sheet, prior to
                    generating
                    a new one</h6>
            </c:when>
            <c:when test="${isPendingPayment eq 'Y'}">
                <h6 class="ml-3 text-danger">Please approve/reject the pending Calculation sheet, prior to generating
                    a new one</h6>
            </c:when>
            <c:otherwise>
                <div id="coverNoteWarning"><h6 class="ml-3 text-danger">Cannot create calculation sheet for cover
                    note</h6></div>
                <a id="calsheet" href="${pageContext.request.contextPath}/CalculationSheetController/viewCalculationSheet?claimNo=${claimHandlerDto.claimNo}" class="previousView"></a>
                <a class="previousView" id="addButtonPanel">
                    <c:choose>
                        <c:when test="${isPendingPayment ne 'Y'  && claimHandlerDto.closeStatus eq 'REOPEN' && claimHandlerDto.reOpenType eq 'EX'  }">
                            <c:if test="${G_USER.userId==claimHandlerDto.assignUserId}">
                                <button type="button" name="cmdViewAccident"
                                        class="btn btn-primary btnclick">
                                    Add New
                                </button>
                            </c:if>
                        </c:when>
                        <c:when test="${isPendingPayment ne 'Y'  && 'A' eq claimHandlerDto.liabilityAprvStatus && (claimHandlerDto.closeStatus eq 'PENDING' || claimHandlerDto.closeStatus eq 'REOPEN')}">
                            <c:if test="${G_USER.userId==claimHandlerDto.assignUserId}">
                                <button type="button" name="cmdViewAccident"
                                        class="btn btn-primary btnclick">
                                    Add New
                                </button>
                            </c:if>
                        </c:when>

                    </c:choose>

                </a>
            </c:otherwise>
        </c:choose>
        <script type="text/javascript">

            $('.btnclick').mousedown(function (event) {
                if (1 === event.which && "${isPendingPayment}" != "Y") {
                    setTimeout(function(){$("#calsheet")[0].click();}, 500);
                }
            });

            $(document).ready(function () {
                $('#coverNoteWarning').hide();
                var policyNo = $('#policyCoverNoteNo').val();
                var covn = policyNo.substring(0, 4);

                if (('COVN' == covn)) {
                    $('#addButtonPanel').hide();
                    $('#coverNoteWarning').show();
                }
            });

        </script>

        <script type="text/javascript">
            $('.previousView').popupWindow({
                height: screen.height,
                width: screen.width,
                resizable: 1,
                centerScreen: 1,
                scrollbars: 1,
                windowName: 'swip'
            });
        </script>
        <div class="col-md-12">
            <table width="100%" cellpadding="0" cellspacing="1"
                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                <thead>
                <tr>
                    <th scope="col" class="tbl_row_header">No</th>
                    <th scope="col" class="tbl_row_header">Cal. Sheet No</th>
                    <th scope="col" class="tbl_row_header">Cal. Type</th>
                    <th scope="col" class="tbl_row_header">Claim No</th>
                    <th scope="col" class="tbl_row_header">Approved Total Amount</th>
                    <th scope="col" class="tbl_row_header">Input User</th>
                    <th scope="col" class="tbl_row_header">Input Date / Time</th>
                    <th scope="col" class="tbl_row_header">Approved User</th>
                    <th scope="col" class="tbl_row_header">Approved Date</th>
                    <th scope="col" class="tbl_row_header">Status</th>
                    <th scope="col" class="tbl_row_header">Action</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="calculationSheetDto" items="${calculationSheetList}" varStatus="itemCount">
                    <td>${itemCount.count}</td>
                    <td>${calculationSheetDto.calSheetId}</td>
                    <td>${calculationSheetDto.calSheetTypeDesc}</td>
                    <td>${calculationSheetDto.claimNo}</td>
                    <td class="text-right"><fmt:formatNumber
                            value="${calculationSheetDto.payableAmount}"
                            pattern="###,##0.00;(###,##0.00)" type="number"/></td>
                    <td>${calculationSheetDto.inputUser}</td>
                    <td>${calculationSheetDto.inputDatetime}</td>
                    <td>${calculationSheetDto.aprUserId}</td>
                    <td>${calculationSheetDto.aprDateTime eq '1980-01-01 00:00:00' ? '' : calculationSheetDto.aprDateTime}</td>
                    <td class="text-center">
                        <c:choose>
                            <c:when test="${calculationSheetDto.status eq 58}">
                                <i class="fa fa-spinner text-warning" title="Pending "></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 59}">
                                <i class="fa fa-forward text-muted"
                                   title="Forwarded to the SP for the creation of Calculation sheet "></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 60}">
                                <i class="fa fa-check text-warning"
                                   title="Checked & Calculation sheet created by SP"></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 61}">
                                <i class="fa fa-forward text-warning"
                                   title=" Forwarded to the Scrutinizing Team for the recommendation of the calculation sheet"></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 62}">
                                <i class="fa fa-check text-warning"
                                   title="  Checked & Calculation sheet verify by Scrutinizing Team"></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 63}">
                                <i class="fa fa-forward text-info"
                                   title=" Forwarded to the Payment Approval Special Team for Payment Approval "></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 64}">
                                <i class="fa fa-forward text-primary"
                                   title="Forwarded to the Mofa limit for Payment Approval "></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 65}">
                                <i class="fa fa-check text-warning" title="Payment Approved "></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 66}">
                                <i class="fa fa-times text-danger" title=" Payment Rejected"></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 67}">
                                <i class="fa fa-check text-success" title="Payment voucher generated"></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 68}">
                                <i class="fa fa-check-circle text-success" title="INVESTIGATION REQUEST APPROVED"></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 70}">
                                <i class="fa fa-check text-warning" title="Payment voucher pending"></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 71}">
                                <i class="fa fa-forward text-dark"
                                   title="Forwarded to the motor engineering team for reserve amount approval ">
                                </i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 72}">
                                <i class="fa fa-spinner text-dark" title="Save as Draft "></i>
                            </c:when>
                            <c:when test="${calculationSheetDto.status eq 73}">
                                <i class="fa fa-times text-dark" title=" Payment Cancelled "></i>
                            </c:when>
                        </c:choose>
                    </td>
                    <td>
                        <a href="${pageContext.request.contextPath}/CalculationSheetController/viewPreviousCalculationSheet?calSheetId=${calculationSheetDto.calSheetId}&claimNo=${calculationSheetDto.claimNo}&DOC_UPLOAD=${docUpload}"
                           class="previousView${calculationSheetDto.claimNo}">
                            <button type="button" name="cmdViewAccident"
                                    class="btn btn-primary">
                                <i class="fa fa-eye"></i>
                            </button>
                        </a>
                        <script type="text/javascript">
                            $('.previousView${calculationSheetDto.claimNo}').bind('contextmenu', function (e) {
                                e.preventDefault();
                            })
                            $('.previousView${calculationSheetDto.claimNo}').popupWindow({
                                height: (screen.height - 150),
                                width: screen.width,
                                resizable: 1,
                                centerScreen: 1,
                                scrollbars: 1,
                                top: 1,
                                windowName: 'previousView${calculationSheetDto.claimNo}'
                            });
                        </script>
                    </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>
