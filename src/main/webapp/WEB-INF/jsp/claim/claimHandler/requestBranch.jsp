<%@taglib prefix="c" uri="jakarta.tags.core" %>
<c:if test="${pendingAri == 'Y'}">
    <span class="badge badge-pill badge-warning text-uppercase mb-3 px-3">Request Pending<i
            class="fa fa-check"></i></span>
</c:if>
<c:if test="${null != docUploadDateTime}">
    <span class="badge badge-pill badge-primary text-uppercase mb-3 px-3">Last Document : ${docUploadDateTime}<i style="padding-left: 2px" class="fa fa-file"></i></span>
</c:if>
<fieldset class="border p-2 ">
    <h6>Customer Details </h6>
    <input type="hidden" name="claimNo" id="claimNo"
           value="${claimNo}">
    <input type="hidden" id="isUploaded" name="isUploaded" value="${isUploaded}">
    <hr class="my-2">
    <div class="col-md-12">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Customer Name * :</label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm" name="customerName"
                           placeholder="Customer Name"
                           id="customerName" value="${requestAriDto.customerName}">
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Address * :</label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm" placeholder="Address 1"
                           name="address1" id="address1" value="${requestAriDto.address1}"/>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label"></label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm mt-1"
                           placeholder="Address 2" name="address2" id="address2" value="${requestAriDto.address2}">
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label"></label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm mt-1"
                           placeholder="Address 3" id="address3" name="address3" value="${requestAriDto.address3}">
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Contact Number * :</label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm" name="contactNo"
                           placeholder="Contact Number"
                           id="contactNo" value="${requestAriDto.contactNo}">
                </div>
            </div>
        </div>
        <c:if test="${'Y' != pendingAri  and 'Y' != ariArranged }">
            <div class="row">
                <div class="col text-right p-0">
                    <button type="button" class="btn btn-primary" id="btnSubmit2" disabled>Submit</button>
                </div>
            </div>
        </c:if>
        <c:if test="${pendingAri == 'Y' or ariArranged == 'Y'}">
            <div class="row">
                <div class="col text-right p-0">
                    <button type="button" class="btn btn-primary" id="btnSubmit" onclick="submitCustomerDetails()">
                        Submit
                    </button>
                </div>
            </div>
        </c:if>

    </div>
</fieldset>