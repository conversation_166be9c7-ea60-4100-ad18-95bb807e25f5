<%--
  Created by IntelliJ IDEA.
  User: akila
  Date: 9/6/18
  Time: 4:39 PM
  To change this template use File | Settings | File Templates.
--%>
<%@taglib prefix="c" uri="jakarta.tags.core" %>

<input type="hidden" id="calSheetId" name="calSheetId" value="${calSheetId}"/>

<div class="card-body">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-sm-4 col-form-label"> Remark
                </label>
                <div class="col-sm-8">
                    <textarea class="form-control form-control-sm" rows="4" cols="50" id="remark" name="remark"></textarea>
                </div>
            </div>
            <c:if test="${0 ne calSheetId}">
            <div class="mt-2 text-right mb-3">
                <button type="button" id="checkBtn" name="" class="btn btn-primary " onclick="saveSpecialRemark()">Save
                </button>
            </div>
            </c:if>
        </div>
        <div class="col-md-6">
            <table class="table ">
                <thead>
                <tr>
                    <th scope="col">Remark</th>
                    <th scope="col">Inp User</th>
                    <th scope="col">Inp Datetime</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="remark" items="${remarkList}">
                <tr>
                    <td>${remark.remark}</td>
                    <td>${remark.inpUser}</td>
                    <td>${remark.inpDatetime}</td>
                </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    function saveSpecialRemark(){
        var formData = $('#detailform').serialize();
        var remark=$('#remark').val();

        if(remark != ''){
            $.ajax({
                url: contextPath + "/CalculationSheetController/saveSpecialRemark",
                type: 'POST',
                data: formData,
                success: function (result) {
                    var obj = JSON.parse(result);

                    if (obj != "") {
                        notify(obj, "success");
                        setName();
                    } else {
                        notify("Can not be updated", "danger");
                    }

                }
            });
        }else{
            notify("Please enter a remark", "danger")
        }



    }
</script>