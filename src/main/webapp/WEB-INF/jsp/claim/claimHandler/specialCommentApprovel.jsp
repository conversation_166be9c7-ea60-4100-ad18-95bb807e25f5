<%--
  Created by IntelliJ IDEA.
  User: Tharaka
  Date: 12/27/2018
  Time: 2:35 PM
  To change this template use File | Settings | File Templates.
--%>

<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        var type = '${TYPE}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);


        });

        function init() {
            hideLoader();
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="N_TXN_NO" id="N_TXN_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Special Comment Approval List</h5>
            </div>


        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle /
                                                Trade Plate Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle / Trade Plate Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <c:forEach var="listDto" items="${statusList}">
                                                        <c:choose>
                                                            <c:when test="${listDto.value==50 || listDto.value==36}">
                                                                <option style="color: #007fff;"
                                                                        value="${listDto.value}">${listDto.label}</option>
                                                            </c:when>
                                                            <c:when test="${listDto.value==17 || listDto.value==35}">
                                                                <option style="color: red;"
                                                                        value="${listDto.value}">${listDto.label}</option>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <option value="${listDto.value}">${listDto.label}</option>
                                                            </c:otherwise>
                                                        </c:choose>

                                                    </c:forEach>
                                                    <script type="text/javascript">
                                                        $('#txtV_status').val("57");
                                                    </script>

                                                    <%--<c:choose>--%>
                                                    <%--<c:when test="${TYPE==10}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("0");--%>
                                                    <%--</script>--%>
                                                    <%--</c:when>--%>

                                                    <%--<c:when test="${(sessionClaimUserTypeDto.initLiabilityUser--%>
                                                    <%--|| sessionClaimUserTypeDto.claimHandlerUser) && TYPE==21}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("38");--%>
                                                    <%--</script>--%>
                                                    <%--</c:when>--%>


                                                    <%--&lt;%&ndash;<c:when test="${sessionClaimUserTypeDto.decisionMaker}">&ndash;%&gt;--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("38");--%>
                                                    <%--</script>--%>

                                                    <%--<c:if test="${TYPE==22}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("41");--%>
                                                    <%--</script>--%>
                                                    <%--</c:if>--%>
                                                    <%--<c:if test="${TYPE==23}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("42");--%>
                                                    <%--</script>--%>
                                                    <%--</c:if>--%>
                                                    <%--<c:if test="${TYPE==24}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("44");--%>
                                                    <%--</script>--%>
                                                    <%--</c:if>--%>
                                                    <%--<c:if test="${TYPE==25}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("45");--%>
                                                    <%--</script>--%>
                                                    <%--</c:if>--%>
                                                    <%--<c:if test="${TYPE==26}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("68");--%>
                                                    <%--</script>--%>
                                                    <%--</c:if>--%>

                                                    <%--</c:when>--%>
                                                    <%--<c:when test="${sessionClaimUserTypeDto.twoPanelUser}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("39");--%>
                                                    <%--</script>--%>
                                                    <%--</c:when>--%>

                                                    <%--<c:when test="${sessionClaimUserTypeDto.claimHandlerUser}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("36");--%>
                                                    <%--</script>--%>
                                                    <%--</c:when>--%>

                                                    <%--<c:when test="${sessionClaimUserTypeDto.sparePartsCoordinator || sessionClaimUserTypeDto.scrutinizingTeam}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("50");--%>
                                                    <%--</script>--%>
                                                    <%--</c:when>--%>
                                                    <%--<c:otherwise>--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("35");--%>
                                                    <%--</script>--%>
                                                    <%--</c:otherwise>--%>
                                                    <%--</c:choose>--%>

                                                    <%--<c:if test="${TYPE==29 || TYPE==30}">--%>
                                                    <%--<script type="text/javascript">--%>
                                                    <%--$('#txtV_status').val("0");--%>
                                                    <%--</script>--%>
                                                    <%--</c:if>--%>


                                                </select>
                                            </div>
                                        </div>


                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> File
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFileStatus" id="txtFileStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All">All</option>
                                                    <option value="1">Active & Auto Restored</option>
                                                    <option value="2">Stored</option>
                                                    <option value="3">Auto Stored</option>
                                                </select>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Reference
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Finalized
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFinalizedStatus" id="txtFinalizedStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">All</option>
                                                    <option value="CLOSE">Closed</option>
                                                    <option value="REOPEN">Reopened</option>
                                                    <option value="SETTLE">Settled</option>
                                                </select>
                                                <c:if test="${TYPE==29}">
                                                    <script type="text/javascript">
                                                        $('#txtFinalizedStatus').val("");
                                                    </script>
                                                </c:if>
                                                <c:if test="${TYPE==30}">
                                                    <script type="text/javascript">
                                                        $('#txtFinalizedStatus').val("CLOSE");
                                                    </script>
                                                </c:if>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label">
                                                Location</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id=""
                                                       class="form-control form-control-sm" placeholder="Location">
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Liability
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtLiabilityStatus" id="txtLiabilityStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All">All</option>
                                                    <option value="P">Pending</option>
                                                    <option value="A">Approved</option>
                                                    <%--option value="F">Forwarded to Higher Level</option>
                                                    <option value="H">Held</option>
                                                    <option value="R">Rejected</option--%>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-repeat text-warning">&nbsp;</span>&nbsp;&nbsp; Reopen
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-close text-danger">&nbsp;</span>&nbsp;&nbsp; Closed
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-file text-info">&nbsp;</span>&nbsp;&nbsp; File Store
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="	fa fa-car text-dark">&nbsp;</span>&nbsp;&nbsp; Partial loss
                                </p>
                            </div>

                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-car text-danger">&nbsp;</span>&nbsp;&nbsp; Total Loss
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-refresh text-warning">&nbsp;</span>&nbsp;&nbsp; Document Pending
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa-check text-success">&nbsp;</span>&nbsp;&nbsp; Document Complete
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa fa-question-circle text-warning">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Arranged
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="fa fa fa-check-circle text-success">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Done
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="badge badge-pill badge-warning border ">&nbsp;</span>
                                    &nbsp;&nbsp; On Site Offer </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 text-right"><span
                                        class="badge badge-pill badge-danger border ">&nbsp;</span>
                                    &nbsp;&nbsp; Doubt Claim </p>
                            </div>
                        </div>

                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim Result</h6>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover table-striped"
                                               cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th class="">ref no</th>
                                                <th class="" width="50px">No</th>
                                                <th class="">Claim No</th>
                                                <th class="">Vehicle No</th>
                                                <th class="">Policy No</th>
                                                <th class="">Claim Status</th>
                                                <th class="">Accident Date</th>
                                                <th class="">File Assigned User</th>
                                                <th class="">File Assigned Date/Time</th>
                                                <th class="">Liability Assigned User</th>
                                                <th class="">Liability Assigned Date/Time</th>
                                                <th class="">Int.Liability Assigned User</th>
                                                <th class="">Int.Liability Assigned Date/Time</th>
                                                <th class="">Document Status</th>
                                                <th class="">Document Checked</th>
                                                <th class="">Liability Status</th>
                                                <th class="">Finalize Status</th>
                                                <th class=" text-right">ACR</th>
                                                <th class=" text-right">Present Reserve Amount</th>
                                                <th class="">Loss Type</th>
                                                <th class="min-mobile"></th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimhandler-datatables.js?v11"></script>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');

    });
</script>
</body>
</html>

