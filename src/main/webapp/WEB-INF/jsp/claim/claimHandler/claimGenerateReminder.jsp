<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/21/2018
  Time: 7:54 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<div class="col-md-12">
    <table width="100%" cellpadding="0" cellspacing="1"
           class="table table-hover table-xs dataTable no-footer dtr-inline">
        <thead>
        <tr>
            <th scope="col" class="tbl_row_header">No</th>
            <th scope="col" class="tbl_row_header">Document Name</th>
            <th scope="col" class="tbl_row_header">Action</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="index" value="0"/>
        <c:set var="reminderPrintSummaryDto" value="${reminderLetterFormDto.reminderPrintSummaryDto}"/>
        <c:set var="reminderPrintSummaryList" value="${reminderLetterFormDto.reminderPrintSummaryList}"/>
        <input type="hidden" name="count" value="${reminderPrintSummaryDto.reminderPrintDetailsList.size()}"/>
        <c:forEach var="reminderPrintDetails" items="${reminderPrintSummaryDto.reminderPrintDetailsList}">
            <input type="hidden" name="docTypeId${index}" value="${reminderPrintDetails.docTypeId}"/>
            <tr>
                <td>${index+1}</td>
                <td>${reminderPrintDetails.reminderDocDisplayName} </td>
                <td>
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container">
                            <input name="checkReminderPrint${index}" id="checkReminderPrint${index}"
                                   title="" type="checkbox"
                                   class="align-middle"
                                   value="Y"/>
                            <span class="checkmark"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
            </tr>
            <script type="text/javascript">
                <c:if test="${reminderPrintDetails.checkReminderPrint=='Y'}">
                $("#checkReminderPrint${index}").prop('checked', true);
                </c:if>
                <c:if test="${reminderPrintDetails.checkReminderPrint=='N'}">
                $("#checkReminderPrint${index}").prop('checked', false);
                </c:if>

            </script>

            <c:forEach var="docTypes" items="${uploadedDocumentsType}">
                <c:if test="${docTypes.key eq reminderPrintDetails.docTypeId}">
                    <script type="text/javascript">
                        $("#checkReminderPrint${index}").prop('checked', false);
                    </script>

                </c:if>
            </c:forEach>

            <c:set var="index" value="${index+1}"/>
        </c:forEach>

        </tbody>
    </table>
</div>
<div class="col-md-12 text-right">
    <c:if test="${IS_CLAIM_HANDLER_USER or IS_OFFER_TEAM_CLAIM_HANDLER_USER or IS_DECISION_MAKER  or IS_INIT_LIABILITY_USER or IS_OFFER_TEAM_INIT_LIABILITY_USER or IS_TOTAL_LOSS_CLAIM_HANDLER_USER}">
        <c:if test="${reminderPrintSummaryList.size()<=5}">
            <button type="button" name="cmdGenerateReminder" id="cmdGenerateReminder"
                    onclick="generateReminderPrint()"
                    class="btn btn-success mt-2">
                Generate Reminder Letter
            </button>
        </c:if>
    </c:if>
</div>
<div class="col-md-12">
    <table width="100%" cellpadding="0" cellspacing="1"
           class="table table-hover table-xs dataTable no-footer dtr-inline">
        <thead>
        <tr>
            <th scope="col" class="tbl_row_header">No</th>
            <th scope="col" class="tbl_row_header">Reminder No</th>
            <th scope="col" class="tbl_row_header">Claim No</th>
            <th scope="col" class="tbl_row_header">Generated User</th>
            <th scope="col" class="tbl_row_header">Generated Date / Time</th>
            <th scope="col" class="tbl_row_header">Action</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="recordNo" value="0"/>
        <c:set var="letterIndex" value="${reminderPrintSummaryList.size()}"/>
        <c:forEach var="reminderPrintSummary" items="${reminderPrintSummaryList}">
            <c:set var="sup" value="st"/>
            <c:choose>
                <c:when test="${letterIndex==1}">
                    <c:set var="sup" value="st"/>
                </c:when>
                <c:when test="${letterIndex==2}">
                    <c:set var="sup" value="nd"/>
                </c:when>
                <c:when test="${letterIndex==3}">
                    <c:set var="sup" value="rd"/>
                </c:when>
                <c:when test="${letterIndex==4 || letterIndex==5 || letterIndex==6}">
                    <c:set var="sup" value="th"/>
                </c:when>
            </c:choose>
            <tr>
                <td>${recordNo+1}</td>
                <td>${letterIndex}<sup>${sup}</sup> Reminder</td>
                <td>${reminderPrintSummary.claimNo}</td>
                <td>${reminderPrintSummary.generatedUserId}</td>
                <td>${reminderPrintSummary.generatedDateTime}</td>
                <td>
                    <a href="${pageContext.request.contextPath}/ClaimReportController/viewReminderLetter?reminderSummaryRefId=${reminderPrintSummary.reminderSummaryRefId}"
                       class="claimReminderLetterView${letterIndex}">
                        <button type="button" name="cmdViewLetter${recordNo}" id="cmdViewLetter${recordNo}"
                                onclick=""
                                class="btn btn-primary mt-2">
                            View ${letterIndex}<sup>${sup}</sup> Reminder
                        </button>
                    </a>
                    <script type="text/javascript">
                        $('.claimReminderLetterView${letterIndex}').popupWindow({
                            height: screen.height,
                            width: screen.width,
                            resizable: 1,
                            centerScreen: 1,
                            scrollbars: 1,
                            windowName: '${letterIndex}<sup>${sup}</sup> Reminder'
                        });
                    </script>
                </td>
            </tr>
            <c:set var="letterIndex" value="${letterIndex-1}"/>
            <c:set var="recordNo" value="${recordNo+1}"/>
        </c:forEach>

        </tbody>
    </table>
</div>
