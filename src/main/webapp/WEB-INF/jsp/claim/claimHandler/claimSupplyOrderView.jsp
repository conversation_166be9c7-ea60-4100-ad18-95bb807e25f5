<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/26/2018
  Time: 12:18 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="card-body">
    <c:set var="policyDto" value="${sessionClaimHandlerDto.claimsDto.policyDto}"/>
    <form name="frmSupplyOrder" id="frmSupplyOrder" method="post">
        <input type="hidden" name="scrutinizingUserId" id="scrutinizingUserId">
        <input type="hidden" name="claimNo" value="${sessionClaimHandlerDto.claimsDto.claimNo}">
        <input type="hidden" name="supplyOrderRefNo" id="supplyOrderRefNo"
               value="${supplyOrderSummaryDto.supplyOrderRefNo}">
        <input type="hidden" name="sparePartCount" id="sparePartCount" value="0">
        <c:if test="${supplyOrderList.size() > 0}">
            <div class="row">
                <div class="col-md-12">
                    <span>Letter Generated DO</span>
                    <table width="100%" cellpadding="0" cellspacing="1"
                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                        <thead>
                        <tr>
                            <th scope="col" class="tbl_row_header">No</th>
                            <th scope="col" class="tbl_row_header">Claim No</th>
                            <th scope="col" class="tbl_row_header">Supply Order Serial No</th>
                            <th scope="col" class="tbl_row_header">Supplier</th>
                            <th scope="col" class="tbl_row_header">User</th>
                            <th scope="col" class="tbl_row_header">Generated Date</th>
                            <th scope="col" class="tbl_row_header">View</th>
                            <th scope="col" class="tbl_row_header"></th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach var="listDto" items="${supplyOrderList}">
                            <tr>
                                <td>${listDto.no}</td>
                                <td>${listDto.claimNo}</td>
                                <td>${listDto.supplyOrderSerialNo}</td>
                                <td>${listDto.supplierName}</td>
                                <td>${listDto.generateUserId}</td>
                                <td>${listDto.generateDateTime}</td>
                                <td style="text-align: center">
                                    <a
                                       class="supplyOrderView">
                                        <button onclick="openDocument('${listDto.supplyOrderRefNo}','${listDto.claimNo}','${listDto.masterRecord}')" class="btn-primary btn btn-sm btn-xs" type="button" title="View Document"><i
                                                class="fa fa-file"></i></button>
                                    </a>
                                    <script type="text/javascript">
                                        function openDocument(refNo,claimNo,masterRecord){
                                            window.open("${pageContext.request.contextPath}/ClaimReportController/supplyOderLetter?supplyOrderRefNo="+refNo+"&N_CLAIM_NO="+claimNo+"&MASTER_RECORD="+masterRecord,
                                                'popUpWindow','_top,width='+screen.width+',height='+screen.height+'resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
                                        }
                                        $('.supplyOrderView').bind('contextmenu', function (e) {
                                            e.preventDefault();
                                        })

                                    </script>
                                </td>
                                <c:if test="${isNotify && listDto.masterRecord == 'Y' && listDto.voucherGenerated == 'N'}">
                                    <td style="text-align: center">
                                        <c:choose>
                                            <c:when test="${supplyOrderSummaryDto.inputUserId==G_USER.userId || supplyOrderSummaryDto.approveAssignSparePartCoordinator==G_USER.userId || supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId}">
                                                <button class="btn-primary btn btn-sm btn-xs" title="Update DO" onclick="updateDOAfterGenerate('${listDto.supplyOrderRefNo}', false)" ${listDto.masterRecord == 'Y' ? '' : 'disabled'}><i class="fa fa-edit"></i></button>
                                            </c:when>
                                            <c:otherwise>
                                                <button class="btn-primary btn btn-sm btn-xs" title="View DO" onclick="updateDOAfterGenerate('${listDto.supplyOrderRefNo}', false)" ${listDto.masterRecord == 'Y' ? '' : 'disabled'}><i class="fa fa-eye"></i></button>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:if>
                            </tr>
                            <script>
                                console.log('${listDto.masterRecord}');
                            </script>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
            <hr>
        </c:if>

        <c:if test="${supplyOrderListPending.size() > 0}">
            <div class="row">
                <div class="col-md-12">
                    <span>Pending DO</span>
                    <table width="100%" cellpadding="0" cellspacing="1"
                           class="table table-hover table-xs dataTable no-footer dtr-inline">
                        <thead>
                        <tr>
                            <th scope="col" class="tbl_row_header">No</th>
                            <th scope="col" class="tbl_row_header">Claim No</th>
                            <th scope="col" class="tbl_row_header">Supply Order Serial No</th>
                            <th scope="col" class="tbl_row_header">Supplier</th>
                            <th scope="col" class="tbl_row_header">User</th>
                            <th scope="col" class="tbl_row_header">Last Updated Date</th>
                            <th scope="col" class="tbl_row_header"></th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach var="listDto" items="${supplyOrderListPending}">
                            <tr>
                                <td>${listDto.no}</td>
                                <td>${listDto.claimNo}</td>
                                <td>${listDto.supplyOrderSerialNo}</td>
                                <td>${listDto.supplierName}</td>
                                <td>${listDto.inputUserId}</td>
                                <td>${listDto.inputDateTime}</td>
                                <c:if test="${isNotify}">
                                    <td style="text-align: center">
                                        <c:choose>
                                            <c:when test="${supplyOrderSummaryDto.inputUserId==G_USER.userId || supplyOrderSummaryDto.approveAssignSparePartCoordinator==G_USER.userId || supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId}">
                                                <button class="btn-primary btn btn-sm btn-xs" title="Update DO" onclick="updateDOAfterGenerate('${listDto.supplyOrderRefNo}', false)"><i class="fa fa-edit"></i></button>
                                            </c:when>
                                            <c:otherwise>
                                                <button class="btn-primary btn btn-sm btn-xs" title="View DO" onclick="updateDOAfterGenerate('${listDto.supplyOrderRefNo}', false)"><i class="fa fa-eye"></i></button>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:if>
                            </tr>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
            <hr>
        </c:if>

        <div class="form-group row" id="divInUse">
            <label style="top: 0; bottom: 0; left: 0; right: 0; margin: auto;">
                <h4 style="color: red" id="txtInUse"></h4>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Spare Parts Supplier :</label>
            <div class="col-sm-8">
                <select class="form-control form-control-sm" name="supplierId" id="supplierId">
                    <option value="0">-- Please Select --</option>
                    <c:forEach var="listDto" items="${supplierList}">
                        <option value="${listDto.value}">${listDto.label}</option>
                    </c:forEach>
                </select>
                <script type="text/javascript">
                    $("#supplierId").val('${supplyOrderSummaryDto.supplierId}');
                </script>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Email :</label>
            <div class="col-sm-8">
                <input type="text" class="form-control form-control-sm" name="supplierEmail" id="supplierEmail"
                       value="${supplyOrderSummaryDto.supplierEmail}">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Contact No :</label>
            <div class="col-sm-8">
                <input type="text" class="form-control form-control-sm" name="supplierContactNo" id="supplierContactNo"
                       value="${supplyOrderSummaryDto.supplierContactNo}">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Insured Name :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2">${policyDto.custName}</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Make & Model :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2">${policyDto.vehicleMake} / ${policyDto.vehicleModel}</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Vehicle No :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2">${policyDto.vehicleNumber}</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Year of Make :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2">${policyDto.manufactYear}</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Chassis No :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2">${policyDto.chassisNo}</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Claim No / Policy No :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2"
                      name="claimNo">${sessionClaimHandlerDto.claimsDto.claimNo}/${policyDto.policyNumber}</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Date of Loss :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2">${sessionClaimHandlerDto.claimsDto.accidDate}</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Serial No :</label>
            <div class="col-sm-8">
                <span class="label_Value ml-2"
                      name="supplyOrderSerialNo">${supplyOrderSummaryDto.supplyOrderSerialNo}</span>
            </div>
        </div>


        <div class="row supplierorderpage">
            <div class="col-md-12">
                <table width="100%" cellpadding="0" cellspacing="1"
                       class="table table-hover table-xs dataTable no-footer dtr-inline">
                    <thead>
                    <tr>
                        <th scope="col" class="tbl_row_header">No</th>
                        <th scope="col" class="tbl_row_header">ITEM</th>
                        <th scope="col" class="tbl_row_header">QUANTITY</th>
                        <th scope="col" class="tbl_row_header">INDIVIDUAL PRICE (Rs.)</th>
                        <th scope="col" class="tbl_row_header">O/A</th>
                        <th scope="col" class="tbl_row_header">TOTAL AMOUNT</th>
                        <th scope="col" class="tbl_row_header">ACTION</th>
                    </tr>
                    </thead>
                    <tbody>
                    <div class="webserverdiv row" id="webserverdiv">
                        <div class="form-group">
                            <tr class=" d-none " id="removeSparePartPanel">
                                <td>
                                    <div class="form-group">
                                        <input type="text" class="form-control "
                                               name="indexValue" readonly
                                        />
                                        <label class="col-sm-4 col-form-label" name="index"></label>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <select type="text" class="form-control sparePartSec supplyOrderFocusOutRefresh"
                                                name="sparePartRefNo"
                                                placeholder="Spare Part">
                                            <c:forEach var="sparePart" items="${sparePartList}">
                                                <option value="${sparePart.value}">${sparePart.label}</option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input type="text"
                                               class="form-control numericval quantityField supplyOrderFocusOutRefresh"
                                               name="quantity"
                                               id="quantity"
                                               placeholder="Quantity"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group" style="display: flex;gap: 5px">
                                        <input type="text"
                                               class="form-control numericval supplyOrderFocusOutRefresh text-right individualPriceField"
                                               name="individualPrice"
                                               placeholder="Individual Price"/>
                                        <input type="checkbox" name="isPendingIndividualPrice" id="isPendingIndividualPrice_${index}" class="isPendingCheckbox" title="Pending Individual Price?"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input type="text"
                                               class="form-control numericval supplyOrderFocusOutRefresh text-right"
                                               name="oaRate"
                                               placeholder="O/A"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <input type="text" class="form-control numericval  text-right"
                                               name="totalAmount"
                                               placeholder="Total Amount" readonly/>
                                    </div>
                                </td>
                                <td>
                                   <span class="input-group-btn">
                                        <button type="button" class="btn btn-default removeButtonweb"><i
                                                class="fa fa-minus"></i></button>
                                    </span>
                                </td>
                            </tr>
                        </div>
                    </div>
                    </tbody>
                </table>
            </div>
        </div>
        <button class="btn btn-primary addButtonweb" type="button"><i
                class="fa fa-plus"></i></button>
        <div class="form-group row mb-1">
            <label class="col-sm-9" style="text-align: right;">Total :</label>
            <div class="col-sm-3">
                <input type="text" class="form-control form-control-sm" style="text-align: right;" readonly
                       placeholder="Total" id="totalAmount" name="totalAmount"
                       value="${supplyOrderSummaryDto.totalAmount}">
            </div>
        </div>
        <div class="form-group row mb-1">
            <label class="col-sm-9 col-form-label" style="text-align: right;">Total O/A
                Amount ${supplyOrderSummaryDto.totalOwnersAccountAmount} :</label>
            <div class="col-sm-3">
                <input type="text" class="form-control form-control-sm" style="text-align: right;"
                       placeholder="Total O/A Amount" id="totalOwnersAccountAmount" name="totalOwnersAccountAmount"
                       readonly
                       value="${supplyOrderSummaryDto.totalOwnersAccountAmount}">
            </div>
        </div>
        <div class="form-group row mb-1">
            <label class="col-sm-9 col-form-label" style="text-align: right;">Other Deductions :</label>
            <div class="col-sm-3">
                <input type="text" class="form-control form-control-sm" style="text-align: right;"
                       placeholder="Other Deductions" id="othertDeductionAmount" name="othertDeductionAmount"
                       value="${supplyOrderSummaryDto.othertDeductionAmount}" onfocusout="removeSupplyOrder()">
            </div>
        </div>
        <div class="form-group row mb-1">

            <label class="col-sm-9 col-form-label" style="text-align: right;">Policy Excess </label>
            <div class="col-sm-3">
                <input name="isExcessInclude" type="radio" value="Y" id="excheck1" onclick="policyValueChanged();"
                       class="align-middle" ${supplyOrderSummaryDto.isExcessInclude eq 'Y'?'checked':''}/>Yes

                <input name="isExcessInclude" type="radio" value="N" id="excheck2" onclick="policyValueChanged();"
                       class="align-middle" ${supplyOrderSummaryDto.isExcessInclude eq 'N'?'checked':''}/> No

                <input type="text" class="mt-1 form-control form-control-sm" style="text-align: right;"
                       placeholder="Policy Excess" id="policyExcess" name="policyExcess" value="${policyDto.excess}"
                       readonly>
            </div>
            <c:if test="${sessionClaimHandlerDto.isExcessInclude eq 'Y'  && supplyOrderSummaryDto.supplyOrderStatus ne 'P'}">
                <script type="text/javascript">
                    $("#excheck1").attr('disabled', 'disabled');
                    $("#excheck2").attr('disabled', 'disabled');
                </script>
                <%--<input type="hidden" id="policyExcess" name="policyExcess"
                       value="0.00"/>--%>
                <input type="hidden" name="isExcessInclude" value="N"/>
            </c:if>
        </div>
        <div class="form-group row mb-1">
            <label class="col-sm-9 col-form-label" style="text-align: right;">VAT :</label>
            <div class="col-sm-3" id="vatRadio">
                <input name="vatStatus" type="radio" value="ADD" id="addVat" onclick="changeVat();"
                       class="align-middle" ${supplyOrderSummaryDto.vatStatus eq 'ADD'?'checked':''}/>Add

                <input name="vatStatus" type="radio" value="REMOVE" id="removeVat" onclick="changeVat();"
                       class="align-middle"${supplyOrderSummaryDto.vatStatus eq 'REMOVE'?'checked':''}/> Remove
            </div>
            <div class="col-sm-9"></div>
            <div class="col-sm-3">
                <select type="text" class="form-control  vatAmount"
                        name="vatAmount" id="vatAmount"
                        placeholder="Vat Amount">
                    <c:forEach var="vatList" items="${vatRateList}">
                        <option value="${vatList.vatRate}">${vatList.vatRate}</option>
                    </c:forEach>
                </select>
                <script type="text/javascript">
                    $("#vatAmount").val('${supplyOrderSummaryDto.vatAmount}');
                </script>
            </div>
        </div>
        <div class="form-group row mb-1">
            <label class="col-sm-9 col-form-label" style="text-align: right;">Final Amount :</label>
            <div class="col-sm-3">
                <input type="text" class="form-control form-control-sm" style="text-align: right;" readonly
                       placeholder="Final Amount" id="finalAmount" name="finalAmount"
                       value="${supplyOrderSummaryDto.finalAmount}">
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-7">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Supplier Remarks :</label>
                    <div class="col-sm-8">
                        <textarea type="text" class="form-control form-control-sm" id="supplyOrderRemark"
                                  name="supplyOrderRemark">${supplyOrderSummaryDto.supplyOrderRemark}</textarea>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Other Remarks :</label>
                    <div class="col-sm-8">
                        <textarea type="text" class="form-control form-control-sm" id="otherRemark"
                                  name="otherRemark">${supplyOrderSummaryDto.otherRemark}</textarea>
                    </div>
                </div>
            </div>
            <div class="col-md-5">
                <div class="form-group row mb-1">
                    <label class="col-sm-4 col-form-label" style="text-align: right;">Workshop Name:</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control form-control-sm" style="text-align: right;"
                               placeholder="Workshop Name" id="workShopName" name="workShopName"
                               value="${supplyOrderSummaryDto.workShopName}">
                    </div>
                </div>
                <div class="form-group row mb-1">
                    <label class="col-sm-4 col-form-label" style="text-align: right;">Address 1 :</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control form-control-sm" style="text-align: right;"
                               placeholder="Address 1" id="workShopAddress1" name="workShopAddress1"
                               value="${supplyOrderSummaryDto.workShopAddress1}">
                    </div>
                </div>
                <div class="form-group row mb-1">
                    <label class="col-sm-4 col-form-label" style="text-align: right;">Address 2 :</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control form-control-sm" style="text-align: right;"
                               placeholder="Address 2" id="workShopAddress2" name="workShopAddress2"
                               value="${supplyOrderSummaryDto.workShopAddress2}">
                    </div>
                </div>
                <div class="form-group row mb-1">
                    <label class="col-sm-4 col-form-label" style="text-align: right;">Address 3 :</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control form-control-sm" style="text-align: right;"
                               placeholder="Address 3" id="workShopAddress3" name="workShopAddress3"
                               value="${supplyOrderSummaryDto.workShopAddress3}">
                    </div>
                </div>
                <div class="form-group row mb-1">
                    <label class="col-sm-4 col-form-label" style="text-align: right;">Contact No :</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control form-control-sm" style="text-align: right;"
                               placeholder="Contact No" id="workShopContactNo" name="workShopContactNo"
                               value="${supplyOrderSummaryDto.workShopContactNo}">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <fieldset class="border p-2">
                    <h6>Authorized By</h6>
                    <hr>
                    <div class="form-group row">
                        <div class="label_Value col-md-4 ml-0 text-center">
                            <%--<img src="../img/signature.png" alt="signature" height="30px" width="100%">--%>
                            <label style="margin-bottom: -10px;padding-bottom: 0;">${supplyOrderSummaryDto.inputUserFullName}</label>
                            <p class="m-0" style="overflow: hidden; height: 20px">
                                .........................................................................................</p>
                            <label class="col-form-label">Spare parts coordinator</label>
                        </div>
                        <div class="label_Value col-md-4 ml-0 text-center">
                            <label style="margin-bottom: -10px;padding-bottom: 0;">${supplyOrderSummaryDto.apprvScrutinizingUserFullName}</label>
                            <p class="m-0" style="overflow: hidden; height: 20px">
                                .........................................................................................</p>
                            <label class="col-form-label">Authorized Officer Engineering Department</label>
                        </div>
                        <div class="label_Value col-md-4 ml-0 text-center">
                            <label style="margin-bottom: -10px;padding-bottom: 0;">${supplyOrderSummaryDto.apprvClaimHandlerUserFullName}</label>
                            <p class="m-0" style="overflow: hidden; height: 20px">
                                .........................................................................................</p>
                            <label class="col-form-label">Authorized Officer Motor Claims Department</label>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>

        <c:if test="${!docUpload}">
            <div class="mt-2 text-right mb-3">
                <c:if test="${!doRequest && assignedDo == 0 && doRefNo != 0 && claimHandlerDto.supplyOrderAssignStatus =='Y' && (supplyOrderSummaryDto.inputUserId==G_USER.userId || supplyOrderSummaryDto.approveAssignSparePartCoordinator==G_USER.userId)}">
                    <button type="button" class="btn btn-danger" onclick="cancelDoUpdate(true)">Add New</button>
                </c:if>
                <c:if test="${(!isNotify && doRefNo != 0) || doRequest}">
                    <button type="button" class="btn btn-danger" onclick="cancelDoUpdate(false)">Cancel</button>
                </c:if>
                <c:if test="${supplyOrderSummaryDto.supplyOrderRefNo==0 && sessionClaimHandlerDto.supplyOrderAssignUser==G_USER.userId && claimHandlerDto.supplyOrderAssignStatus =='Y' && !pendingInspection}">
                    <button id="btnGroupDrop1" type="button" class="btn btn-secondary dropdown-toggle"
                            data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false" style="/* display: none; */">
                        Action
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(8);">Return to Claim
                            Handler</a>
                    </div>
                </c:if>

                <c:if test="${supplyOrderSummaryDto.supplyOrderRefNo!=0}">
                    <button id="btnGroupDrop1" type="button" class="btn btn-secondary dropdown-toggle"
                            data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false" style="/* display: none; */">
                        Action
                    </button>
                    <div class="dropdown-menu">
                        <c:if test="${supplyOrderSummaryDto.supplyOrderStatus ne 'P' and supplyOrderSummaryDto.supplyOrderStatus ne 'G'}">
                            <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(5);">RE-Generate
                                letter</a>
                        </c:if>
                        <c:choose>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='SCRUTINIZING-F'}">
                                <c:if test="${sessionClaimHandlerDto.supplyOrderAssignUser==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(1);">Recall</a>
                                </c:if>
                                <c:if test="${supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(2);">Return to
                                        Spare
                                        Parts Coordinator</a>
                                    <c:if test="${!pendingInspection}">
                                        <a class="dropdown-item" href="#" id="fwdToClaimHandler" onclick="updateSupplyOrderStatusFlow(3);">Approved
                                            &
                                            Forward to claim handler</a>
                                    </c:if>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='SPC-R'}">
                                <c:if test="${supplyOrderSummaryDto.inputUserId==G_USER.userId && !pendingInspection}">
                                    <c:if test="${supplyOrderSummaryDto.limitExceeded}">
                                        <a class="dropdown-item" href="#" onclick="forwardToSupplyOrder();">Forward to spare
                                            parts manager</a>
                                    </c:if>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='SCRUTINIZING-R'}">
                                <c:if test="${supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(9)">Forward to Spare Parts Coordinator</a>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='U'}">
                                <c:if test="${supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId && (supplyOrderSummaryDto.inputUserId==null || supplyOrderSummaryDto.inputUserId=='') && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(9)">Forward to Spare Parts Coordinator</a>
                                </c:if>
                                <c:if test="${supplyOrderSummaryDto.inputUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" id="fwdToClaimHandlerStsUM" onclick="forwardToSupplyOrder();">Forward to spare
                                        parts manager</a>
                                    <a class="dropdown-item" href="#" id="fwdToClaimHandlerStsUF" onclick="updateSupplyOrderStatusFlow(3);">Approved
                                        &
                                        Forward to claim handler</a>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='A'}">
                                <c:if test="${supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(16)">Approve And Forward To Claim Handler</a>
                                </c:if>
                                <c:if test="${supplyOrderSummaryDto.inputUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(14)">Recall</a>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='SPC-F'}">
                                <c:if test="${supplyOrderSummaryDto.approveAssignSparePartCoordinator==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(10)">Return to Scrutinizing Team</a>
                                    <c:if test="${supplyOrderSummaryDto.limitExceeded}">
                                        <a class="dropdown-item" href="#" onclick="forwardToSupplyOrder();">Forward to spare
                                            parts manager</a>
                                    </c:if>
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(16)">Approve And Forward To Claim Handler</a>
                                </c:if>
                                <c:if test="${supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(13)">Recall</a>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='SPC-A'}">
                                <c:if test="${supplyOrderSummaryDto.inputUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(14)">Recall</a>
                                </c:if>
                                <c:if test="${supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId && !pendingInspection}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(15)">Return to Spare Parts Coordinator</a>
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(16)">Approve And Forward To Claim Handler</a>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='P'
                                        && supplyOrderSummaryDto.supplyOrderRefNo>0
                                        && sessionClaimHandlerDto.supplyOrderAssignUser==G_USER.userId}">
                                <c:if test="${!pendingInspection}">
                                   <%-- <a class="dropdown-item" href="#" onclick="forwardToSupplyOrder();">Forward to
                                        Scrutinizing
                                        Team</a>--%>
                                    <a class="dropdown-item" href="#" id="fwdToClaimHandlerStsPM" onclick="forwardToSupplyOrder();">Forward to spare
                                        parts manager</a>
                                    <a class="dropdown-item" href="#" id="fwdToClaimHandlerStsPF" onclick="updateSupplyOrderStatusFlow(3);">Approved &
                                        Forward to claim handler</a>
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(8);">Return to claim
                                        handler</a>
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(5);">Generate
                                        letter</a>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='G'
                                        && supplyOrderSummaryDto.supplyOrderRefNo>0
                                        && sessionClaimHandlerDto.supplyOrderAssignUser==G_USER.userId}">
                                <c:if test="${!pendingInspection}">
                                    <a class="dropdown-item" href="#" id="fwdToClaimHandlerStsGM" onclick="forwardToSupplyOrder();">Forward to spare
                                        parts manager</a>
                                    <a class="dropdown-item" href="#" id="fwdToClaimHandlerStsGF" onclick="updateSupplyOrderStatusFlow(3);">Approved &
                                        Forward to claim handler</a>
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(8);">Return to claim
                                        handler</a>
                                </c:if>
                            </c:when>
                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='SCRUTINIZING-A' && sessionClaimHandlerDto.assignUserId==G_USER.userId}">
                                <a class="dropdown-item" href="#" onclick="forwardGenerateSupplyOrder();">Forward to
                                    Spare
                                    Parts Coordinator for generating the letter</a>
                                <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(6);">Return to
                                    Spare
                                    Parts Coordinator</a>
                                <c:if test="${supplyOrderSummaryDto.inputUserFullName!=supplyOrderSummaryDto.apprvScrutinizingUserFullName}">
                                    <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(7);">Return to
                                        Scrutinizing Team</a>
                                </c:if>
                            </c:when>
<%--                            <c:when test="${supplyOrderSummaryDto.supplyOrderStatus=='CH-A' && supplyOrderSummaryDto.inputUserId==G_USER.userId && !pendingInspection}">--%>
<%--                                <a class="dropdown-item" href="#" onclick="updateSupplyOrderStatusFlow(5);">Generate--%>
<%--                                    letter</a>--%>
<%--                            </c:when>--%>

                        </c:choose>
                    </div>
                </c:if>
                <c:if test="${supplyOrderSummaryDto.supplyOrderStatus=='P' && sessionClaimHandlerDto.supplyOrderAssignStatus=='Y' && sessionClaimHandlerDto.supplyOrderAssignUser==G_USER.userId && !pendingInspection}">
                    <button type="submit" value="Save" class="btn btn-primary">Save</button>
                </c:if>
                <c:if test="${(((supplyOrderSummaryDto.supplyOrderStatus=='SCRUTINIZING-A' || supplyOrderSummaryDto.supplyOrderStatus=='CH-A' || supplyOrderSummaryDto.supplyOrderStatus=='G') &&
                (supplyOrderSummaryDto.approveAssignSparePartCoordinator==G_USER.userId ||supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId))
                  || ((supplyOrderSummaryDto.supplyOrderStatus=='SPC-F' || supplyOrderSummaryDto.supplyOrderStatus=='SPC-R') && supplyOrderSummaryDto.approveAssignSparePartCoordinator==G_USER.userId)
                  || (supplyOrderSummaryDto.supplyOrderStatus=='SPC-A' && supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId)
                  || (supplyOrderSummaryDto.supplyOrderStatus=='SCRUTINIZING-R' && supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId)
                  || (supplyOrderSummaryDto.supplyOrderStatus=='U' && (supplyOrderSummaryDto.inputUserId==G_USER.userId || ((null == supplyOrderSummaryDto.inputUserId || supplyOrderSummaryDto.inputUserId == '') && supplyOrderSummaryDto.apprvAssignScrutinizingUserId==G_USER.userId)))
                  || (supplyOrderSummaryDto.supplyOrderStatus=='A' && apprvScrutinizingUserId==G_USER.userId))
                  && !hasVouGenCalSheet}">
                    <button type="button" class="btn btn-primary" onclick="updateDO()">Update</button>
                </c:if>
            </div>
        </c:if>
    </form>
</div>

<script type="text/javascript">
    var maxSparePartCount = 0;
    var sparePartCount = "${supplyOrderSummaryDto.supplyOrderDetailsDtoList.size()}";
    var sparePartValidator;
    var pricevalidator;
    var credentialIndex = 0;

    function updateDO() {
        bootbox.confirm({
            message: "Do you want to Update the Supply Order?",
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    var formData = $('#frmSupplyOrder').serializeArray();
                    $.ajax({
                        url: contextPath + '/ClaimHandlerController/updateSupplyOrder',
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            var message = "";
                            if (messageType == "SUCCESS") {
                                message = "Updated Successfully";
                                notify(message, "success");
                            } else {
                                message = "Can not be Updated";
                                notify(message, "danger");
                            }
                            if('${supplyOrderSummaryDto.supplyOrderRefNo}' == ${MAX_DO}) {
                                loadSuppyOrderView();
                            } else {
                                updateDOAfterGenerate('${supplyOrderSummaryDto.supplyOrderRefNo}', false);
                            }
                        }
                    });
                }
            }
        });
    }

    function getMax() {
        if (credentialIndex > maxSparePartCount) {
            maxSparePartCount = credentialIndex;
        }
        return maxSparePartCount;
    }

    $('#vatAmount').change(function () {
        calVatAmount();
    });

    function calVatAmount() {
        if (document.getElementById('addVat').checked || document.getElementById('removeVat').checked) {
            var dataObj = {
                vatRadio: $("input[name='vatStatus']:checked").val(),
                totalAmount: $('#totalAmount').val(),
                vatAmount: $('#vatAmount').val(),
                deduction: $('#othertDeductionAmount').val()
            };

            var URL = "${pageContext.request.contextPath}/ClaimHandlerController/calVatAmount";
            $.ajax({
                url: URL,
                type: 'POST',
                data: dataObj,
                success: function (result) {
                    $('#finalAmount').val(result);
                }
            });
        } else {
            var element = document.getElementById("vatRadio");
            element.classList.add("text-danger");
        }

    }

    $(document).ready(function () {
        calVatAmount();
//        $("#supplierId").chosen({
//            no_results_text: "No results found!",
//            width: "100%"
//        });

        $(document).on('change', '.isPendingCheckbox', function () {
            const $checkbox = $(this);
            const isChecked = $checkbox.is(':checked');
            const $priceField = $checkbox.closest('td').find('.individualPriceField');
            const fieldName = $priceField.attr('name');
            var $form = $('#frmSupplyOrder');
            var idParts = $checkbox.attr('id').split('_');
            if (idParts.length < 2 || !idParts[1]) {
                console.error('Invalid ID format:', $checkbox.attr('id'));
                return;
            }
            var index = idParts[1];

            var formValidation = $form.data('formValidation');
            if (!$priceField.length || !formValidation) {
                console.warn('Price field or validation not found');
                return;
            }

            if (isChecked) {
                $priceField.val('0');
                $priceField.prop('readonly', true);
                $form.formValidation('removeField', fieldName);
                $priceField.closest('.form-group').removeClass('has-error has-success');
                $priceField.next('.help-block').remove();
                refreshSupplyOrder(index, 0);
            } else {
                $priceField.val('');
                $priceField.prop('readonly', false);
                $form.formValidation('addField', fieldName, pricevalidator);
            }
        });


        pricevalidator = {
            validators: {
                notEmpty: {
                    message: 'Required.'
                },
                numeric: {
                    message: "Invalid"
                }
            }
        };
        sparePartValidator = {
            selector: '.sparePartSec',
            validators: {
                callback: dropDownValidation
            }
        };
        var numericvalues = {
            selector: '.numericval',
            validators: {
                notEmpty: {
                    message: 'Required.'
                },
                numeric: {
                    message: "Invalid"
                }
            }

        };


        $('#frmSupplyOrder')
            .formValidation({
                framework: 'bootstrap',
                icon: {
                    valid: 'fa fa-ok',
                    invalid: 'fa fa-remove',
                    validating: 'fa fa-refresh'
                },
                fields: {
                    supplierId: {
                        validators: {
                            callback: dropDownValidation
                        }
                    },
                    supplierEmail: {
                        validators: {
                            emailAddress: {
                                message: 'The value is not a valid email address'
                            }, notEmpty: {
                                message: 'The valid email address is required'
                            }
                        }
                    }, vatStatus: {
                        validators: {
                            notEmpty: {
                                message: 'this field is required'
                            }
                        }
                    },
                    vatAmount: {
                        validators: {
                            notEmpty: {
                                message: 'The vatAmount is required'
                            }
                        }
                    }, workShopName: {
                        validators: {
                            notEmpty: {
                                message: 'The workshop name is required'
                            }
                        }
                    }, workShopAddress1: {
                        validators: {
                            notEmpty: {
                                message: 'The workshop address1 is required'
                            }
                        }
                    },
                    // workShopAddress2: {
                    //     validators: {
                    //         notEmpty: {
                    //             message: 'The workshop address2 is required'
                    //         }
                    //     }
                    // },
                    sparePartRefNo: {
                        validators: {
                            callback: dropDownValidation
                        }
                    },
                    sparePartRefNo: sparePartValidator,
                    categoryinputs: numericvalues,
                    currency: {      // <-- this is not working but i want to validate those
                        // object with has 'myClass' class.
                        validators: {
                            notEmpty: {
                                message: 'required'
                            },
                            numeric: {
                                message: 'Invalid'
                            }
                        }
                    }
                }
            }).on('success.form.fv', function (e) {
            // Prevent form submission
            e.preventDefault();
            var $form = $(e.target);     // Form instance
            // Get the clicked button
            var $button = $form.data('formValidation').getSubmitButton();
            var type = $button.attr('value');
            if ("Save" == type) {
                saveSupplyOrder(type);
            } else if ("C" == type) {
                updateInvestigation('C');
            } else if ("CAN" == type) {
                updateInvestigation('CAN');
            }
        })
        // Add button click handler
            .on('click', '.addButtonweb', function () {
                addComponent({}, credentialIndex++);
            })
            // Remove button click handler
            .on('click', '.removeButtonweb', function () {
                $(this).parents('.input-group-btn').parent('td').parent('tr.removeble').remove();
                credentialIndex--;
                var $row = $(this).parents('.form-group'),
                    index = $row.attr('data-spare-part-panel-index', credentialIndex);
                // Remove fields
                $('#frmSupplyOrder')
                    .formValidation('removeField', $row.find('[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].index"]'))
                    .formValidation('removeField', $row.find('[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].sparePartRefNo"]'))
                    .formValidation('removeField', $row.find('[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].quantity"]'))
                    .formValidation('removeField', $row.find('[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice"]'))
                    .formValidation('removeField', $row.find('[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].oaRate"]'))
                    .formValidation('removeField', $row.find('[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].totalAmount"]'));
                $row.remove();
                removeSupplyOrder();
                $("#sparePartCount").val(getMax());


            });

        loadWarningMessage();
        enableForwardBtn();

    });

    function enableForwardBtn() { // Common Buttons
        if (${claimHandlerDto.claimStatus eq 83}) {
            if (${G_USER.userId eq claimHandlerDto.forwardedEngineer}) {
                $('#btnReturnByEngineer').show();
                $('#btnFwdEngineer').show();
                $('#btnRecallEngineer').hide();
            } else {
                $('#btnReturnByEngineer').hide();
                $('#btnRecallEngineer').show();
                $('#btnFwdEngineer').hide();
            }
        }
        if (${(claimCalculationSheetMainDto.status==61 and G_USER.userId eq claimCalculationSheetMainDto.scrutinizeTeamAssignUserId)
                    or (claimCalculationSheetMainDto.status==59 and G_USER.userId eq claimCalculationSheetMainDto.sparePartCordinatorAssignUserId)
                    or (G_USER.userId eq supplyOrderSummaryDto.apprvAssignScrutinizingUserId or G_USER.userId eq supplyOrderSummaryDto.approveAssignSparePartCoordinator)
                    or (claimHandlerDto.supplyOrderAssignStatus == 'Y' and G_USER.userId eq claimHandlerDto.supplyOrderAssignUser)
                    and !pendingInspection}) {
            $('#btnFwdEngineer').show();
            $('#btnRecallEngineer').hide();
        } else{
            $('#btnFwdEngineer').hide();
            $('#btnRecallEngineer').hide();
        }
    }

    function loadWarningMessage() {
        if (${supplyOrderSummaryDto.supplyOrderStatus == 'U' and (null eq supplyOrderSummaryDto.inputUserId or '' eq supplyOrderSummaryDto.inputUserId) and (supplyOrderSummaryDto.approveAssignSparePartCoordinator eq G_USER.userId or sessionClaimHandlerDto.assignUserId==G_USER.userId)}) {
            $("#txtInUse").text('Currently In Use by Scrutinizing User');
            $("#divInUse").show();
        } else if (${supplyOrderSummaryDto.supplyOrderStatus == 'U' and (null ne supplyOrderSummaryDto.inputUserId and '' ne supplyOrderSummaryDto.inputUserId) and (supplyOrderSummaryDto.apprvAssignScrutinizingUserId eq G_USER.userId or sessionClaimHandlerDto.assignUserId==G_USER.userId)}) {
            $("#txtInUse").text('Currently In Use by Spare Parts Coordinator');
            $("#divInUse").show();
        } else {
            $("#divInUse").hide();
        }
    }

    function addComponent(data, index) {
        console.log('Adding component with index:', index);
        getDeliverOrderDocumentStatus()
        var $template = $('#removeSparePartPanel');
        var $clone = $('#xxxxx' + index);
        var names = [
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].indexValue"]',
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].index"]',
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].sparePartRefNo"]',
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].quantity"]',
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice"]',
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].oaRate"]',
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].totalAmount"]',
            '[name="supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].isPendingIndividualPrice"]'
        ];
        if (!$clone.length) {
            $clone = $template
                .clone()
                .removeClass('d-none')
                .removeAttr('id')
                .attr('data-spare-part-panel-index', index)
                .attr('id', 'xxxxx' + index)
                .addClass('removeble')
                .insertBefore($template);
            names = [
                '[name="indexValue"]',
                '[name="index"]',
                '[name="sparePartRefNo"]',
                '[name="quantity"]',
                '[name="individualPrice"]',
                '[name="oaRate"]',
                '[name="totalAmount"]',
                '[name="isPendingIndividualPrice"]'
            ];
        }
        $clone
            .find(names[0]).attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].indexValue').attr('id', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].indexValue').val(index + 1).end()
            .find(names[1]).attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].index').attr('id', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].index').end()
            .find(names[2]).attr('id', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].sparePartRefNo').attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].sparePartRefNo').val(data.sparePartRefNo || 0).end()
            .find(names[3]).attr('id', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].quantity').attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].quantity').attr('onfocusout', 'refreshSupplyOrder(' + index + ',0)').val(data.quantity || '').end()
            // .find(names[4]).attr('id', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice').attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice').attr('onfocusout', 'refreshSupplyOrder(' + index + ',0)').val(data.individualPrice || 0).end()
            .find(names[5]).attr('id', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].oaRate').attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].oaRate').attr('onfocusout', 'refreshSupplyOrder(' + index + ',0)').val(data.oaRate || 0).end()
            .find(names[6]).attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].totalAmount').val(data.totalAmount || 0).end()
            .find(names[7]).attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].isPendingIndividualPrice').attr('id', 'isPendingIndividualPrice_' + index).prop('checked', data.isPendingIndividualPrice || false).end();

        var $individualPrice = $clone.find(names[4]) // Assuming names[4] targets the individualPrice input
            .attr('id', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice')
            .attr('name', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice')
            .attr('onfocusout', 'refreshSupplyOrder(' + index + ',0)')
            .val(data.individualPrice || 0);

        if (data.isPendingIndividualPrice === true || data.isPendingIndividualPrice === 'true') {
            $individualPrice.prop('readonly', true);
            $individualPrice.val('0'); // Set to 0 when pending
        } else {
            $individualPrice.prop('readonly', false);
        }

        $('#frmSupplyOrder')
            .formValidation('addField', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].index')
            .formValidation('addField', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].sparePartRefNo', sparePartValidator)
            .formValidation('addField', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].quantity', pricevalidator)
            .formValidation('addField', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice', pricevalidator)
            .formValidation('addField', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].oaRate', pricevalidator)
            .formValidation('addField', 'supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].totalAmount', pricevalidator);

        $("#sparePartCount").val(getMax());
    }

    var countXxx = 0;

    /*
    isLoad = 1 - Form Load,0=Add Component

    */
    function refreshSupplyOrder(index, isLoad) {
        var formData = $('#frmSupplyOrder').serialize();

        if (index !== undefined) {
            var item = document.getElementById('supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].sparePartRefNo').value;
            var qty = document.getElementById('supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].quantity').value;
            var indPrice = document.getElementById('supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].individualPrice').value;
            var oaRate = document.getElementById('supplyOrderSummaryDto.supplyOrderDetailsDtoList[' + index + '].oaRate').value;
            if (!(item && qty && indPrice && oaRate && item !== '' && qty !== '' && indPrice !== '' && oaRate !== ''))
                return;
        }

        $.ajaxq('supply', {
            url: contextPath + '/ClaimHandlerController/supplyOrderData?isLoad=' + isLoad,
            type: 'POST',
            data: formData,
            success: function (data) {
                if (data && data.supplyOrderDetailsDtoList) {
                    credentialIndex = 0;
                    //$('.removeble').remove();

                    for (var i = 0; i < data.supplyOrderDetailsDtoList.length; i++) {
                        addComponent(data.supplyOrderDetailsDtoList[i], data.supplyOrderDetailsDtoList[i].index - 1);
                        credentialIndex++;
                    }
                    $("#totalAmount").val(accounting.formatNumber(data.totalAmount, 2, "", "."));
                    $("#finalAmount").val(accounting.formatNumber(data.finalAmount, 2, "", "."));
                    $("#totalOwnersAccountAmount").val(accounting.formatNumber(data.totalOwnersAccountAmount, 2, "", "."));
                    $("#othertDeductionAmount").val(accounting.formatNumber(data.othertDeductionAmount, 2, "", "."));

                }

                if (data.limitExceeded){
                    handleLinkAfterLimitExceed();
                }else{
                    handleLinkAfterLimitNotExceed();
                }

            },
            error: function (e) {
                console.log(e);
            }
        });
    }

    function handleLinkAfterLimitExceed() {
        var linkPF = document.getElementById("fwdToClaimHandlerStsPF");
        var linkPM = document.getElementById("fwdToClaimHandlerStsPM");
        var linkUF = document.getElementById("fwdToClaimHandlerStsUF");
        var linkUM = document.getElementById("fwdToClaimHandlerStsUM");
        var linkGF = document.getElementById("fwdToClaimHandlerStsGF");
        var linkGM = document.getElementById("fwdToClaimHandlerStsGM");

        if (linkPF && linkPM){
            linkPM.style.display = "block"
            linkPF.style.display = "none"
        }
        if (linkUF && linkUM){
            linkUM.style.display = "block"
            linkUF.style.display = "none"
        }
        if (linkGF && linkGM){
            linkGM.style.display = "block"
            linkGF.style.display = "none"
        }
    }

    function handleLinkAfterLimitNotExceed() {
        var linkPF = document.getElementById("fwdToClaimHandlerStsPF");
        var linkPM = document.getElementById("fwdToClaimHandlerStsPM");
        var linkUF = document.getElementById("fwdToClaimHandlerStsUF");
        var linkUM = document.getElementById("fwdToClaimHandlerStsUM");
        var linkGF = document.getElementById("fwdToClaimHandlerStsGF");
        var linkGM = document.getElementById("fwdToClaimHandlerStsGM");

        if (linkPF && linkPM){
            linkPM.style.display = "none"
            linkPF.style.display = "block"
        }
        if (linkUF && linkUM){
            linkUM.style.display = "none"
            linkUF.style.display = "block"
        }
        if (linkGF && linkGM){
            linkGM.style.display = "none"
            linkGF.style.display = "block"
        }
    }

    async function getDeliverOrderDocumentStatus() {
        const claimNo = $('input[name="claimNo"]').val();
        const docTypeId = 133;
        try {
            let result = await fetchDeliverOrderDocumentStatus(claimNo, docTypeId);
            return result && result.toLowerCase().trim() === "true";
        }catch (e) {
            console.log(e.toString())
        }
    }

    function fetchDeliverOrderDocumentStatus(claimNo,docTypeId){
        return $.ajax({
            url: '${pageContext.request.contextPath}/ClaimHandlerController/checkDeliverOrderDocumentStatus?claimNo='+claimNo+'&documentTypeId='+docTypeId,
            method: 'GET',
        });
    }

    function removeSupplyOrder() {
        var formData = $('#frmSupplyOrder').serialize();

        $.ajaxq('supply', {
            url: contextPath + '/ClaimHandlerController/supplyOrderData?isLoad=0',
            type: 'POST',
            data: formData,
            success: function (data) {
                if (data) {
                    $("#totalAmount").val(accounting.formatNumber(data.totalAmount, 2, "", "."));
                    $("#finalAmount").val(accounting.formatNumber(data.finalAmount, 2, "", "."));
                    $("#totalOwnersAccountAmount").val(accounting.formatNumber(data.totalOwnersAccountAmount, 2, "", "."));
                    $("#othertDeductionAmount").val(accounting.formatNumber(data.othertDeductionAmount, 2, "", "."));
                }
            },
            error: function (e) {
                console.log(e);

            }

        });
    }

    $(function () {
        refreshSupplyOrder(undefined, 1);
    });


    function saveSupplyOrder(type) {
        maxSparePartCount = 0;
        var url;
        if (type == 'Save') {
            url = contextPath + "/ClaimHandlerController/saveSupplyOrder";
        } else if (type == 'C') {
            url = contextPath + "/ClaimHandlerController/updateCompleteInvestigation";
        } else if (type == 'CAN') {
            url = contextPath + "/ClaimHandlerController/updateCancelInvestigation";
        }
        var claimNo = '${sessionClaimHandlerDto.claimsDto.claimNo}';
        showLoader();
        var formData = $('#frmSupplyOrder').serializeArray();
        console.log(formData)
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != "") {
                    notify(obj, "success");
                    loadSuppyOrderView();
                } else {
                    notify("Can not be updated", "danger");
                }
                hideLoader();
            }
        });
    }

    async function updateSupplyOrderStatusFlow(type) {
        var message = "";
        var notifyMessage = "Forwarded Successfully";
        var URL = contextPath + "/ClaimHandlerController/recallSupplyOrder";
        if (type == 1) {
            URL = contextPath + "/ClaimHandlerController/recallSupplyOrder";
            message = "Do you want to recall this supply order?";
        } else if (type == 2) {
            URL = contextPath + "/ClaimHandlerController/returnSpCoordSupplyOrder";
            message = "Do you want to return this supply order?";
        } else if (type == 3) {
            const deliverOrderDocumentStatus = await getDeliverOrderDocumentStatus();
            if (deliverOrderDocumentStatus){
                notify("Please submit bill for delivery order and please approve it!!", "danger")
                return
            }
            var status = '${supplyOrderSummaryDto.isUpdated=='Y'}';
            URL = contextPath + "/ClaimHandlerController/approvedAndForwardClaimHandlerSupplyOrder?IS_UPDATED=" + status;
            message = "Do you want to approve this supply order?";
        } else if (type == 4) {
            URL = contextPath + "/ClaimHandlerController/forwardGenerateSupplyOrder";
            message = "Do you want to forward this supply order for generating letter?";

            var formData = $('#frmSupplyOrder').serialize();
            $.ajax({
                url: contextPath + "/ClaimHandlerController/validateSupplyOrderCalculationSheetApproval",
                type: 'POST',
                data: formData,
                success: function (result) {
                    var messageType = JSON.parse(result);
                    var message = "";
                    if(type==1){
                        $('#btnRequest').show();
                        $('#btnRequestNDOReturn').show();
                    }
                    if (messageType == "INVALID") {
                        message = "Please create and approved supply order payment";
                        notify(message, "danger");
                        return false;
                    }
                }
            });


        } else if (type == 5) {
            URL = contextPath + "/ClaimHandlerController/updateSupplyOrderGenerate";
            message = "Do you want to generate letter";
            notifyMessage = "Updated Successfully";
        }
        else if (type == 6) {
            URL = contextPath + "/ClaimHandlerController/returnSpCoordSupplyOrderByClaimHandler";
            message = "Do you want to return this supply order to spare parts coordinator?";
        }
        else if (type == 7) {
            URL = contextPath + "/ClaimHandlerController/returnScrTeamSupplyOrderByClaimHandler";
            message = "Do you want to return this supply order to Scrutinizing Team?";
        }
        else if (type == 8) {
            URL = contextPath + "/ClaimHandlerController/returnToClaimHandler";
            message = "Do you want to return this supply order to Claim Handler?";
        } else if (type == 9) {
            URL = contextPath + "/ClaimHandlerController/forwardSPCSupplyOrder";
            message = "Do You want to Forward this supply order to Spare Parts Coordinator?";
        } else  if (type == 10) {
            //return to scr team
            URL = contextPath + "/ClaimHandlerController/returnScrTeamSupplyOrderBySPC";
            message = "Do you want to return this supply order to Scrutinizing Team?";
        } else if (type == 11) {
            URL = contextPath + "/ClaimHandlerController/approveAndForwardToScrutinizingTeam";
            message = "Do you want to Approve and Forward to Scrutinizing Team?";
        } else if (type == 12) {
            URL = contextPath + "/ClaimHandlerController/forwardToScrutinizingTeam";
            message = "Do you want to Forward to Scrutinizing Team?";
        } else if (type == 13) {
            //recall from spcood by scr
            URL = contextPath + "/ClaimHandlerController/recallFromSparePartsCoordinator";
            message = "Do you want to recall this Supply Order?";
        } else if (type == 14) {
            //recall from scr by spcood
            URL = contextPath + "/ClaimHandlerController/recallFromScrutinizingTeam";
            message = "Do you want to recall this Supply Order?";
        } else if (type == 15) {
            //return to spcood
            URL = contextPath + "/ClaimHandlerController/returnToSparePartsCoordinatorAfterUpdate";
            message = "Do you want to return this Supply Order to Spare Parts Coordinator?"
        } else if (type == 16) {
            URL = contextPath + "/ClaimHandlerController/approvedAndForwardClaimHandlerSupplyOrderAfterUpdate"
            message = "Do you want to Approve and Forward this Supply Order?"
        }
        bootbox.confirm({
            message: message,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    var formData = $('#frmSupplyOrder').serialize();
                    $.ajax({
                        url: URL,
                        type: 'POST',
                        data: formData,
                        success: function (result) {
                            var messageType = JSON.parse(result);
                            if (messageType == "SUCCESS") {
                                notify(notifyMessage, "success");
                            } else {
                                notifyMessage = "Can not be Forward";
                                notify(notifyMessage, "danger");
                            }
                            loadSuppyOrderView();
                            loadAriRequestPage();
                        }
                    });
                }
            }
        });
    }


    function forwardGenerateSupplyOrder() {

        var URL = contextPath + "/ClaimHandlerController/forwardGenerateSupplyOrder";
        var message = "Do you want to forward this supply order for generating letter?";
        var formData = $('#frmSupplyOrder').serialize();
        $.ajax({
            url: contextPath + "/ClaimHandlerController/validateSupplyOrderCalculationSheetApproval",
            type: 'POST',
            data: formData,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";

                if (messageType == "INVALID") {
                    message = "Cannot be forwarded. Please approve the supply order payment first.";
                    notify(message, "danger");
                    return;

                } else if (messageType == "UPDATED") {
                    message = "DO Updated. Can not forward to Spare Parts Coordinator";
                    notify(message, "danger");
                    return;

                } else {
                    message = "Do you want to forward this supply order for generating letter?";
                    bootbox.confirm({
                        message: message,
                        buttons: {
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            },
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            }
                        },
                        callback: function (result) {
                            if (result == true) {
                                var formData = $('#frmSupplyOrder').serialize();
                                $.ajax({
                                    url: URL,
                                    type: 'POST',
                                    data: formData,
                                    success: function (result) {
                                        var messageType = JSON.parse(result);
                                        var message = "";
                                        if (messageType == "SUCCESS") {
                                            message = "Forwarded Successfully";
                                            notify(message, "success");
                                        } else {
                                            message = "Can not be Forward";
                                            notify(message, "danger");
                                        }
                                        loadSuppyOrderView();
                                    }
                                });
                            }
                        }
                    });
                }
            }
        });


    }


    async function forwardToSupplyOrder(type) {
        const deliverOrderDocumentStatus = await getDeliverOrderDocumentStatus();
        if (deliverOrderDocumentStatus) {
            notify("Please submit bill for delivery order and please approve it!!", "danger")
            return
        }
        if (null != '${supplyOrderSummaryDto.apprvAssignScrutinizingUserId}' && '${supplyOrderSummaryDto.apprvAssignScrutinizingUserId}' != '') {
            updateSupplyOrderStatusFlow(12);
            return;
        }
        /*if ('' == $('#remark').val()) {
            notify("Please enter remark!", "danger");
            $('#remark').addClass('bg-badge-danger text-white');
            $('#remark').focus();
            return;
        }*/

        $.ajax({
            url: contextPath + "/ClaimHandlerController/scrutinizingUserList",
            type: 'GET',
            success: function (result) {

                var optionArr = [];
                var userArr = JSON.parse(result);
                var option1 = {text: 'Please Select', value: ''};
                optionArr.push(option1);
                for (var i = 0; i < userArr.length; i++) {
                    var option = {text: userArr[i], value: userArr[i]};
                    optionArr.push(option);
                }

                bootbox.prompt({
                    title: "Please select Scrutinizing Team user to forward",
                    inputType: 'select',
                    inputOptions: optionArr,
                    callback: function (result) {
                        if (result === null) {
                            // Prompt dismissed
                        } else {
                            if (result === '') {
                                return false;
                            }
                            $("#scrutinizingUserId").val(result);
                            var formData = $('#frmSupplyOrder').serialize();
                            $.ajax({
                                url: contextPath + "/ClaimHandlerController/forwardScrTeamSupplyOrder",
                                type: 'POST',
                                data: formData,
                                success: function (result) {
                                    var messageType = JSON.parse(result);
                                    var message = "";
                                    if (messageType == "SUCCESS") {
                                        message = "Forward Successfully";
                                        notify(message, "success");
                                    } else {
                                        message = "Can not be Forward";
                                        notify(message, "danger");
                                    }
                                    loadSuppyOrderView();
                                    $('#btnRequest').hide();
                                    $('#btnRequestNDOReturn').hide();
                                }
                            });
                        }
                    }
                });
            }
        });
    }

    function changeVat() {
        var element = document.getElementById("vatRadio");
        element.classList.remove("text-danger");
        calVatAmount();
    }

    function policyValueChanged() {

        var formData = $('#frmSupplyOrder').serialize();

        $.ajax({
            url: contextPath + "/ClaimHandlerController/policyExcessChange",
            type: 'POST',
            data: formData,
            success: function (result) {
                $('#finalAmount').val(result);
            }
        });


    }

    function updateDOAfterGenerate(refNo, isNotify) {
        var claimNo = 0;
        if(${null eq motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo or '' eq motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo or 0 eq motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}) {
            claimNo = '${claimHandlerDto.claimsDto.claimNo}';
        } else {
            claimNo = '${motorEngineerDto.inspectionDetailsDto.assessorAllocationDto.claimsDto.claimNo}';
        }
        $("#claimSupplyOrderContainer").load("${pageContext.request.contextPath}/ClaimHandlerController/viewSupplyOrder?P_N_CLIM_NO=" + claimNo + "&N_REF_NO=" + refNo + "&NOTIFY=" + isNotify);
    }

    function cancelDoUpdate(addNew) {
        var message = '';
        if (addNew) {
            message = 'Do You want to Create New DO?';
        } else {
            if (${sessionClaimHandlerDto.assignUserId==G_USER.userId}) {
                message = "Do you want to Switch to Previous Record?"
            } else {
                message = 'Do you want to Cancel the Update Process?';
            }
        }
        bootbox.confirm({
            message: message,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    var refNo = '${doRefNo}';
                    var isNotify = true;
                    if (addNew) {
                        refNo = 0;
                        isNotify = false;
                    }
                    updateDOAfterGenerate(refNo, isNotify);
                }
            }
        });
    }

    // function policyExcessAdd() {
    //     $('#policyExcess').val(POLICY_EXCESS);
    //     calculatePayableAmount();
    // }
    //
    // function policyExcessZero() {
    //     $('#policyExcess').val('0.00');
    //     calculatePayableAmount();
    //
    // }
</script>
