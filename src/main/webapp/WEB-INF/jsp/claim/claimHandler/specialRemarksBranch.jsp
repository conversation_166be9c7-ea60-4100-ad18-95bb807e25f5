<%@taglib prefix="c" uri="jakarta.tags.core" %>
<div class="card-body">

    <c:forEach var="spclRemark" items="${claimHandlerSecialRemarkList}">
        <a href="#"
           class="list-group-item list-group-item-action flex-column align-items-start"
           style="min-height: 140px;">
            <div class="font-bg log-left"
                 style="width: 50px; height:50px; overflow: hidden;">
                <h2 class="name text-white">${spclRemark.inputUserId}</h2>
            </div>
            <div class="float-left"
                 style="    margin-left: 20px; width: calc(100% - 100px);">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1">${spclRemark.sectionName}</h5>
                    <p><b>claim-${spclRemark.claimNo}</b></p>
                </div>
                <p class="mb-1"
                   style="word-break: break-all;">${spclRemark.remark}</p>
                <hr class="m-0 mt-1 mb-1">
                <h6 class="float-right">${spclRemark.inputUserId}</h6>
                <p class="float-left">${spclRemark.inputDateTime}</p>
            </div>
        </a>
    </c:forEach>



</div>