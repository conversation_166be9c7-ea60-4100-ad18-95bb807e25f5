<%--
  Created by IntelliJ IDEA.
  User: Thanura
  Date: 4/7/2021
  Time: 11:04 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<div class="card-body">
    <div class="col-md-12" style="margin-top: 1%">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Customer Name * :</label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm" name="customerName"
                           placeholder="Customer Name"
                           id="customerName" value="${requestAriDto.customerName}" disabled>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Address * :</label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm" placeholder="Address 1"
                           name="address1" id="address1" value="${requestAriDto.address1}" disabled/>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label"></label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm mt-1"
                           placeholder="Address 2" name="address2" id="address2" value="${requestAriDto.address2}" disabled>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label"></label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm mt-1"
                           placeholder="Address 3" id="address3" name="address3" value="${requestAriDto.address3}" disabled>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">Contact Number * :</label>
            <div class="col-sm-8">
                <div class="row">
                    <input type="text" class="form-control form-control-sm" name="contactNo"
                           placeholder="Contact Number"
                           id="contactNo" value="${requestAriDto.contactNo}" disabled>
                </div>
            </div>
        </div>
    </div>
</div>