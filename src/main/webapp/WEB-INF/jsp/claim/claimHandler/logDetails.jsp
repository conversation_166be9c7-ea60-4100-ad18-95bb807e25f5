
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<div class="card-body">

    <c:forEach var="logs" items="${claimLogList}">



        <div class="row">

            <div class="col-md-12">

                <a href="#"

                   class="list-group-item list-group-item-action flex-column align-items-start">

                    <div class="font-bg log-left"

                         style="width: 50px; height:50px; overflow: hidden;">

                        <h2 class="name text-white">${logs.userId}</h2>

                    </div>

                    <div class="float-left log-right">

                        <div class="d-flex w-100 justify-content-between">

                            <h5 class="mb-1">${logs.heading}</h5>

                            <p><b>Claim- ${logs.claimNo}</b></p>

                        </div>

                        <p class="mb-1"

                           style="word-break: break-all;">${logs.description}</p>

                        <hr class="m-0 mt-1 mb-1">

                        <h6 class="float-right">${logs.userId}</h6>

                        <p class="float-left badge-primary "
                           style="padding-left: 3px;padding-right: 3px;">${logs.txndate} &nbsp ${logs.txntime}</p>

                    </div>

                    <div class="clearfix"></div>

                </a>

            </div>

        </div>

    </c:forEach>

</div>