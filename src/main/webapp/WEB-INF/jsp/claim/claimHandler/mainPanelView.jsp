<%--
  Created by IntelliJ IDEA.
  User: thanura
  Date: 8/12/2020
  Time: 9:00 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<html>
<head>
    <title>Title</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script>
        $(function () {
            $("#inputDateTime").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });
        });

        $('#inputDateTime').on('dp.change', function (e) {
            $("#inputDateTime").data("DateTimePicker").minDate(e.date);
            $("#inputDateTime").data("DateTimePicker").maxDate(currentDate);
        });
        $(document).ready(function () {
            $('#addClaimUserbtn').prop('disabled', true);
        });
    </script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmClaimPanelUserList" id="frmClaimPanelUserList" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Main Panel Registry</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">

                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div id="accordion" class="accordion">
                            <div class="card">
                                <div class="card-header" id="headingOne">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                                           data-target="#collapseOne"
                                           aria-expanded="true" aria-controls="collapseOne">
                                            Search Here <i class="fa fa-search"></i>
                                        </a>
                                    </h5>
                                </div>
                                <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                                     data-parent="#accordion">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="inputUserId" class="col-md-4 col-form-label">Input User ID</label>
                                                    <div class="col-md-8">
                                                        <input name="inputUserId" id="inputUserId"
                                                               class="form-control form-control-sm"
                                                               placeholder="User ID">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group row">
                                                    <label for="txtClaimNo" class="col-md-4 col-form-label">Claim No</label>
                                                    <div class="col-md-8">
                                                        <input name="txtClaimNo" id="txtClaimNo"
                                                               class="form-control form-control-sm"
                                                               placeholder="Claim No">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 text-right">
                                                <button class="btn btn-primary" type="button" name="cmdSearch"
                                                        id="cmdSearch"
                                                        onclick="search()">Search
                                                </button>
                                                <a class="btn btn-secondary" type="button" name="cmdClose"
                                                   id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                                </a>
                                                <hr>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">

                    <div class="col-md-12">
                        <div class="mt-2">
                            <h6 class="float-left">Main Panel Users</h6>
                        </div>
                    </div>
                </div>

                <hr class="my-2">
                <div class="row">
                    <div class="col-md-12 mt-2">
                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>No</th>
                                <th>Claim No</th>
                                <th>Forwarded User</th>
                                <th>Forwarded Date/Time</th>
                                <th>Last Updated Date/Time</th>
                                <th>Panel Members</th>
                                <th>Status</th>
                                <th class="min-mobile" style="width: 50px;"></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade animated fadeInDown" id="panelUsers" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
             aria-hidden="true">
            <div class="modal-dialog " role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Panel Members</h4>
                        <h5 class="modal-title" id="lblClaimNo">Claim</h5>
                    </div>
                    <div class="modal-body">
                        <%--                        <input type="hidden" class="form-control" name="txnId" id="txnId">--%>
                            <%--                        <input type="hidden" class="form-control" name="claimNos" id="claimNos">--%>
                            <div id="panelMembers">
                            </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="addClaimUserbtn" class="btn btn-primary"
                                name="addClaimUserbtn"
                                onclick="updatePanelMembers()">Save changes
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
</body>
<script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/mainpanel-datatables.js?v3"></script>
<script>
    var userList = [];
    var removedUserList = [];

    function search() {
        table.ajax.reload();
    }

    function closeModal() {
        userList = [];
        removedUserList = [];
        $('#panelUsers').modal('hide');
    }

    function updatePanelMembers() {
        $('#addClaimUserbtn').prop('disabled', true);
        var userNames = '';
        var removedUserNames = '';
        userList.forEach((item, index) => {
            userNames += item + ',';
        });

        removedUserList.forEach((item, index) => {
            removedUserNames += item + ',';
        });

        $.ajax({
            url: contextPath + "/ClaimHandlerController/updatePanelMembers",
            type: 'POST',
            data: {
                "userList": userNames,
                "removedUserList": removedUserNames,
                "claimNo": $("#lblClaimNo").text()
            },
            success: function (result) {
                var response = JSON.parse(result);
                if (response == "SUCCESS") {
                    notify("Successfully Updated", 'success');
                } else if (response == "FAIL") {
                    notify("Failed to Update", 'danger')
                } else {
                    notify("System Error", 'danger');
                }
                table.ajax.reload();
                $('#panelUsers').modal('hide');
            }
        })
    }

    function markPanelMember(element) {
        const userListIndex = userList.indexOf(element.value);
        const removedListIndex = removedUserList.indexOf(element.value);
        $('#addClaimUserbtn').prop('disabled', false);
        if (element.checked) {
            if (removedListIndex > -1) {
                removedUserList.splice(removedListIndex, 1);
            }
            userList.push(element.value);
        } else {
            if (userListIndex > -1) {
                userList.splice(userListIndex, 1);
            }
            removedUserList.push(element.value);
        }
    }
</script>
</html>
