<%--
  Created by IntelliJ IDEA.
  User: Pruthuvi_Sam
  Date: 07/26/18
  Time: 15:03
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>

    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script language="javascript" type="text/javascript">
        $(document).ready(function () {
            // search();
        });
    </script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid" style="width: 100%; overflow: hidden">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="P_N_JOB_NO" id="P_N_JOB_NO" type="hidden"/>
        <input name="P_N_REF_NO" id="P_N_REF_NO" type="hidden"/>
        <div class="row">
            <div class="col-md-12 bg-dark py-2">
                <h5>Supplier Details List</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <%--<label for="txtFromDate" class="col-md-4 col-form-label"> From Date </label>--%>
                                            <%--<div class="col-md-8">--%>
                                            <%--<input name="txtFromDate" class="form-control form-control-sm"--%>
                                            <%--placeholder="From Date" id="txtFromDate" type="text">--%>
                                            <%--</div>--%>
                                        </div>
                                        <div class="form-group row">
                                            <%--<label for="txtVehicleMake" class="col-md-4 col-form-label"> Vehicle--%>
                                            <%--Make </label>--%>
                                            <%--<div class="col-md-8">--%>
                                            <%--<select class="form-control form-control-sm chosen" id="txtVehicleMake"--%>
                                            <%--name="txtVehicleMake" onchange="showMake(this.value)">--%>
                                            <%--<option value="">Please Select</option>--%>
                                            <%--</select>--%>

                                            <%--</div>--%>
                                        </div>
                                        <div class="form-group row">
                                            <%--<label for="txtManufactureYear" class="col-md-4 col-form-label"> Vehicle--%>
                                            <%--Manufacture--%>
                                            <%--Year</label>--%>
                                            <%--<div class="col-md-8">--%>
                                            <%--<input name="txtManufactureYear" id="txtManufactureYear" type="text"--%>
                                            <%--class="date-own form-control form-control-sm "--%>
                                            <%--placeholder="Vehicle Manufacture Year"/>--%>
                                            <%--<script type="text/javascript">--%>
                                            <%--$(function () {--%>
                                            <%--$('#txtManufactureYear').datetimepicker({--%>
                                            <%--viewMode: 'years',--%>
                                            <%--format: 'YYYY'--%>
                                            <%--});--%>
                                            <%--});--%>
                                            <%--</script>--%>
                                            <%--</div>--%>
                                        </div>
                                        <div class="form-group row">
                                            <label for="supplerName" class="col-md-4 col-form-label">Supplier
                                                Name</label>
                                            <div class="col-md-8">
                                                <input name="supplerName1" id="supplerName1" type="text"
                                                       class="form-control form-control-sm"
                                                       placeholder="Supplier Name">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <%--<div class="form-group row">--%>
                                        <%--<label for="txtToDate" class="col-md-4 col-form-label"> To Date </label>--%>
                                        <%--<div class="col-md-8">--%>
                                        <%--<input name="txtToDate" id="txtToDate" type="text"--%>
                                        <%--class="form-control form-control-sm"--%>
                                        <%--placeholder="To Date">--%>
                                        <%--</div>--%>
                                        <%--</div>--%>
                                        <div class="form-group row">
                                            <%--<label for="txtVehicleModel" class="col-md-4 col-form-label">Vehicle--%>
                                            <%--Model </label>--%>
                                            <%--<div class="col-md-8">--%>
                                            <%--<select name="txtVehicleModel" id="txtVehicleModel"--%>
                                            <%--class="form-control form-control-sm chosen"--%>
                                            <%--placeholder="Vehicle Model ">--%>
                                            <%--<option value="">Please Select</option>--%>
                                            <%--</select>--%>
                                            <%--</div>--%>
                                        </div>
                                        <div class="form-group row">
                                            <%--<label for="txtSupplierName" class="col-md-4 col-form-label">--%>
                                            <%--Supplier </label>--%>
                                            <%--<div class="col-md-8">--%>
                                            <%--<select name="txtSupplierName" id="txtSupplierName"--%>
                                            <%--class="form-control form-control-sm chosen"--%>
                                            <%--placeholder="Supplier">--%>
                                            <%--<option value="">Please Select One</option>--%>
                                            <%--<c:forEach var="listDto" items="${supplierList}">--%>
                                            <%--<option value="${listDto.value}">${listDto.label}</option>--%>
                                            <%--</c:forEach>--%>
                                            <%--</select>--%>
                                            <%--</div>--%>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12 text-right">
                                        <hr>
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Search Result</h6>
                                    <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                                            data-target="#exampleModal">
                                        Add Supplier
                                    </button>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th>Id</th>
                                                <th>No</th>
                                                <th>Suppler Name</th>
                                                <th>Supplier Address</th>
                                                <th>Contact No</th>
                                                <th>Contact Person</th>
                                                <th>Email</th>
                                                <th>Record Status</th>
                                                <th>Input User Id</th>
                                                <th class="min-mobile"></th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Supplier Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addSupplierFrm">
                    <div class="form-group">
                        <label for="supplierId" class="col-form-label">Supplier Id</label>
                        <input type="text" class="form-control" name="supplierId" id="supplierId" readonly>
                    </div>
                    <div class="form-group">
                        <label for="supplerName" class="col-form-label">Suppler Name</label>
                        <input class="form-control" name="supplerName" id="supplerName"/>
                    </div>
                    <div class="form-group">
                        <label for="supplierAddressLine1" class="col-form-label">Supplier Address Line1</label>
                        <input class="form-control" name="supplierAddressLine1" id="supplierAddressLine1">
                    </div>
                    <div class="form-group">
                        <label for="supplierAddressLine2" class="col-form-label">Supplier Address Line2</label>
                        <input class="form-control" name="supplierAddressLine2" id="supplierAddressLine2">
                    </div>
                    <div class="form-group">
                        <label for="supplierAddressLine3" class="col-form-label">Supplier Address Line3</label>
                        <input class="form-control" name="supplierAddressLine3" id="supplierAddressLine3">
                    </div>
                    <div class="form-group">
                        <label for="contactNo" class="col-form-label">Contact No</label>
                        <input class="form-control" name="contactNo" id="contactNo">
                    </div>
                    <div class="form-group">
                        <label for="contactPerson" class="col-form-label">Contact Person</label>
                        <input class="form-control" name="contactPerson" id="contactPerson">
                    </div>
                    <div class="form-group">
                        <label for="email" class="col-form-label">Email</label>
                        <input class="form-control" name="email" id="email">
                    </div>
                    <div class="form-group">
                        <label for="recordStatus" class="col-form-label">Record Status</label>
                        <select class="form-control"  name="recordStatus" id="recordStatus">
                            <option value="A">A</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="inputUserId" class="col-form-label">Input User Id</label>
                        <input class="form-control" name="inputUserId" id="inputUserId" readonly>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="search()">Close</button>
                <button type="button" id="addSupplier" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>
</body>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/supplierdetails-datatables.js?v5">

</script>
<script type="text/javascript">
    $('#addSupplier').click(function (e) {
        // e.preventDefault();
        var formData = $('#addSupplierFrm').serialize();
        $.ajax({
            url: contextPath + "/SupplierDetailsController/saveSupplierDetails",
            type: 'POST',
            data: formData,
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    notify(obj, "success");
                }
                else {
                    notify("Fail to Save Supplier", "fail");
                }
            }
        });
    });
</script>
</html>
