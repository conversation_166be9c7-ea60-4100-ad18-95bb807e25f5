<%--
  Created by IntelliJ IDEA.
  User: Tharuka
  Date: 2/21/2019
  Time: 11:24 AM
  To change this template use File | Settings | File Templates.
--%>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <link rel="stylesheet" type="text/css"
          href="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/multiselect/bootstrap-multiselect.min.js"></script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmMain" id="frmMain" method="post" action="">
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Dashboard </h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input Date </label>
                            <div class="col-sm-8">

                                <input value=${txtFromDate} name="txtFromDate" id="txtFromDate" type="text"
                                       class="form-control form-control-sm" placeholder="From Date">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input Date </label>
                            <div class="col-sm-8">
                                <input value=${txtToDate} name="txtToDate" id="txtToDate" type="text"
                                       class="form-control form-control-sm" placeholder="To Date">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 text-right">
                        <button class="btn btn-primary" type="button" onclick="searchByFromDateAndTodate()"
                                name="cmdSearch">Search
                        </button>
                    </div>
                </div>
                <%--<div class="row">--%>
                <%--<div class="col-md-12">--%>
                <%--<div class="mt-2">--%>
                <%--<h6 class="float-left">Claim Panel User Result</h6>--%>
                <%--</div>--%>
                <%--</div>--%>
                <%--</div>--%>
                <hr class="my-2">
                <div class="row">
                    <div class="col-md-6 mt-2 table-responsive">
                        <h6 class="float-left">Claim Status Summary</h6>
                        <table class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>Claim Status</th>
                                <th>Count</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="claimHandlerStatus"
                                       items="${claimDashboardDto.claimHandlerStatusDetailList}">
                                <tr>

                                    <td>${claimHandlerStatus.status}</td>
                                    <td>${claimHandlerStatus.count}</td>

                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6 mt-2 table-responsive">
                        <h6 class="float-left">Calsheet Status Summary</h6>
                        <table class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer" width="100%">
                            <thead>
                            <tr>
                                <th>Calsheet Status</th>
                                <th>Count</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="claimHandlerStatus" items="${claimDashboardDto.calsheetStatusDetailList}">
                                <tr>

                                    <td>${claimHandlerStatus.status}</td>
                                    <td>${claimHandlerStatus.count}</td>

                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>

                <%--<div class="row">--%>
                <%--<div class="col-md-12">--%>
                <%--<div class="mt-2">--%>
                <%--<h6 class="float-left">Claim Panel User Result</h6>--%>
                <%--</div>--%>
                <%--</div>--%>
                <%--</div>--%>
                <hr class="my-2">


                <%--Hide for build--%>
                <%--Start--%>

                <%--<div class="row">--%>
                <%--<div class="col-md-6 mt-2 table-responsive">--%>
                <%--<h6 class="float-left">Inital Liability Team</h6>--%>
                <%--<table class="table table-sm table-hover"--%>
                <%--style="cursor:pointer;" width="100%;">--%>
                <%--<thead>--%>
                <%--<tr>--%>
                <%--<th style="width:100px;">User</th>--%>
                <%--<th>A</th>--%>
                <%--<th>B</th>--%>
                <%--<th>C</th>--%>
                <%--<th>E</th>--%>
                <%--<th>F</th>--%>
                <%--<th>G</th>--%>
                <%--<th>G</th>--%>
                <%--<th>G</th>--%>
                <%--<th>G</th>--%>
                <%--</tr>--%>
                <%--</thead>--%>
                <%--<tbody>--%>
                <%--<tr>--%>
                <%--<td>User 1</td>--%>
                <%--<td>saasd5</td>--%>
                <%--<td>5ad</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--</tr>--%>
                <%--</tbody>--%>
                <%--</table>--%>
                <%--</div>--%>
                <%--<div class="col-md-6 mt-2 table-responsive">--%>
                <%--<h6 class="float-left">Test Table 4</h6>--%>
                <%--<table class="table table-sm table-hover"--%>
                <%--style="cursor:pointer;" width="100%;">--%>
                <%--<thead>--%>
                <%--<tr>--%>
                <%--<th style="width:100px;">User</th>--%>
                <%--<th>A</th>--%>
                <%--<th>B</th>--%>
                <%--<th>C</th>--%>
                <%--<th>E</th>--%>
                <%--<th>F</th>--%>
                <%--<th>G</th>--%>
                <%--<th>G</th>--%>
                <%--<th>G</th>--%>
                <%--<th>G</th>--%>
                <%--</tr>--%>
                <%--</thead>--%>
                <%--<tbody>--%>
                <%--<tr>--%>
                <%--<td>User 1</td>--%>
                <%--<td>saasd5</td>--%>
                <%--<td>5ad</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--<td>5sdasd</td>--%>
                <%--</tr>--%>
                <%--</tbody>--%>
                <%--</table>--%>
                <%--</div>--%>
                <%--</div>--%>

                <%--End--%>


            </div>
        </div>
    </form>
</div>
<script>
    $(function () {
        $("#txtFromDate").datetimepicker({
            sideBySide: true,
            format: 'YYYY-MM-DD',
            //  maxDate:new Date(currentDate),
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
                up: "fa fa-arrow-up",
                down: "fa fa-arrow-down"
            }
        });

        $("#txtToDate").datetimepicker({
            sideBySide: true,
            format: 'YYYY-MM-DD',
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
                up: "fa fa-arrow-up",
                down: "fa fa-arrow-down"
            }
        });

        // $('#txtFromDate').on('dp.change', function (e) {
        //     // $("#txtToDate").data("DateTimePicker").minDate(e.date);
        //     // $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
        // });

        // $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);
        //
        // $("#txtToDate").data("DateTimePicker").maxDate(currentDate);

    });

    function searchByFromDateAndTodate() {

        var form = document.getElementById("frmMain");
        form.action = contextPath + "/ClaimHandlerDashboardController/dashboard";
        form.submit();


    }
</script>

</body>
</html>
