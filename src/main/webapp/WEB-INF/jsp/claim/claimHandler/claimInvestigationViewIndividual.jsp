<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/26/2018
  Time: 10:21 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<div class="card-body">
    <c:set var="investigationDetailsDto1" value="${investigationDetailsFormDto1.investigationDetailsDto}"/>
    <div class="row">
        <div class="col-6">
            <div class="form-group row">

                <div class="col-12">
                    <div class="row">
                        <label class="col-sm-4 col-form-label">Investigation Status :</label>
                        <div class="col-sm-8">
                            <select name="investigationStatus1"
                                    id="investigationStatus1"
                                    class="form-control form-control-sm">
                                <c:choose>
                                    <c:when test="${investigationDetailsDto1.investigationStatus=='AR'}">
                                        <option value="AR">Investigation Arrange</option>
                                        <option value="C">Investigation Completed</option>
                                        <option value="CAN">Investigation Cancel</option>
                                    </c:when>
                                    <c:when test="${investigationDetailsDto1.investigationStatus=='C'}">
                                        <option value="C">Investigation Completed</option>
                                    </c:when>
                                    <c:when test="${investigationDetailsDto1.investigationStatus=='CAN'}">
                                        <option value="CAN">Investigation Cancel</option>
                                    </c:when>
                                    <c:otherwise>
                                        <option value="N">-- Please Select --</option>
                                        <option value="AR">Investigation Arrange</option>
                                    </c:otherwise>
                                </c:choose>
                            </select>
                            <script type="text/javascript">
                                $("#investigationStatus1").val('${investigationDetailsDto1.investigationStatus}');
                            </script>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <input name="investTxnNo" type="hidden" value="${investigationDetailsDto1.investTxnNo}">
                <label class="col-sm-4 col-form-label">Vehicle No :</label>
                <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.vehicleNo}</span>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Claim No :</label>
                <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.claimNo}</span>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Insured Name :</label>
                <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.policyDto.custName}</span>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Date/Time of Accident :</label>
                <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.accidDate} &nbsp; ${sessionClaimHandlerDto.claimsDto.accidTime}</span>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Place of Accident :</label>
                <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.placeOfAccid}</span>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Policy Inception Year :</label>
                <span class="label_Value col-md-8 ml-0">${sessionClaimHandlerDto.claimsDto.claimNo}</span>
            </div>
            <div class="form-group row">
                <label class="col-form-label col-sm-4 text-warning">Reasons for Investigation :</label>
                <span class="label_Value col-md-8 ml-0"></span>
            </div>
        </div>
        <div class="col-6 border bg-dark pt-2" style="height: fit-content;">
            <div class="form-group row">
                <div class="col-sm-4">Investigator's Name :</div>
                <span class="label_Value col-md-8 ml-0">${investigationDetailsDto1.assignInvestigatorName}</span>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Payment Status :</label>
                <c:choose>
                    <c:when test="${investigationDetailsDto1.paymentStatus=='A'}">
                        <span class="label_Value col-md-8 ml-0 text-success">Approved</span>
                    </c:when>
                    <c:otherwise>
                        <span class="label_Value col-md-8 ml-0 text-warning">Pending</span>
                    </c:otherwise>
                </c:choose>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Professional Fee :</label>
                <div class="col-sm-8">
                    <input name="profFee1" id="profFee1" class="form-control form-control-sm text-right"
                           value="${investigationDetailsDto1.profFee}"
                           type="text">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Traveling Fee :</label>
                <div class="col-sm-8">
                    <input name="travelFee1" id="travelFee1" class="form-control form-control-sm text-right"
                           value="${investigationDetailsDto1.travelFee}"
                           type="text" >
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Other Fee :</label>
                <div class="col-sm-8">
                    <input name="otherFee1" id="otherFee1" class="form-control form-control-sm text-right"
                           value="${investigationDetailsDto1.otherFee}"
                           type="text" >
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-4 col-form-label">Total Amount :</label>
                <div class="col-sm-8">
                    <input name="totalFee1" id="totalFee1" class="form-control form-control-sm text-right"
                           value="${investigationDetailsDto1.totalFee}"
                           type="text" readonly>
                </div>
            </div>
        </div>
    </div>

    <c:forEach var="investigationReasonDetailsDto1" items="${investigationDetailsDto1.investigationReasonDetailsDtoList}">
        <div class="form-group row">
            <label class="col-form-label col-sm-4">${investigationReasonDetailsDto1.invesReason} : </label>
            <div class="col-sm-8">

                <label class="custom-control custom-checkbox check-container ml-0">
                    <input name="investigationDetailsDto1.investigationReasonDetailsDtoList[0].isCheck"
                           title="" type="checkbox" class="align-middle"
                           value="Y" ${investigationReasonDetailsDto1.isCheck=='Y'?"checked":""} />
                    <span class="checkmark"></span>
                    <span class="custom-control-description"></span>
                </label>
            </div>
        </div>
    </c:forEach>


    <div class="form-group row">
        <label class="col-form-label col-sm-4">Reason : </label>
        <div class="col-sm-8">
            <textarea class="form-control" name="reason1" id="reason1" rows="5">${investigationDetailsDto.reason}</textarea>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-sm-4 col-form-label">Type of Loss :</label>
        <div class="col-sm-8">
            <div class="row">
                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                    <input type="checkbox" name="isAccident1" id="isAccident1" title="Accident" class="align-middle"
                           value="Y" ${investigationDetailsDto1.isAccident=='Y'?"checked":""}>
                    <span class="checkmark"></span>
                    <span class="custom-control-description">Accident </span>
                </label>
                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                    <input name="isTheft1" title="Theft" type="checkbox" id="isTheft1"
                           class="align-middle" value="Y" ${investigationDetailsDto1.isTheft=='Y'?"checked":""}>
                    <span class="checkmark"></span>
                    <span class="custom-control-description">Theft</span>
                </label>
                <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container">
                    <input name="isFire1" id="isFire1" title="Fire" type="checkbox"
                           class="align-middle" value="Y" ${investigationDetailsDto1.isFire=='Y'?"checked":""}>
                    <span class="checkmark"></span>
                    <span class="custom-control-description">Fire</span>
                </label>
            </div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-sm-4 col-form-label">Investigation Assigned to :</label>
        <div class="col-sm-8">
            <select name="assignInvestigatorUserRefId1" id="assignInvestigatorUserRefId1"
                    class="form-control form-control-sm">
                <option value="">-- Please Select --</option>
                <c:forEach var="listDto" items="${investigatorList}">
                    <option value="${listDto.value}">${listDto.label}</option>
                </c:forEach>
            </select>
            <script type="text/javascript">
                $("#assignInvestigatorUserRefId1").val('${investigationDetailsDto1.assignInvestigatorUserRefId}');
            </script>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-sm-4 col-form-label">Assigned User & Date/Time :</label>
        <span class="label_Value col-md-4 ml-0">${investigationDetailsDto1.investArrangeUser}</span>
        <span class="label_Value col-md-4 ml-0">${investigationDetailsDto1.investArrangeDateTime}</span>
    </div>
    <div class="form-group row">
        <label class="col-sm-4 col-form-label">Completed User & Date/Time :</label>
        <span class="label_Value col-md-4 ml-0">${investigationDetailsDto1.investCompletedUser}</span>
        <span class="label_Value col-md-4 ml-0">${investigationDetailsDto1.investCompletedDateTime}</span>
    </div>
    <div class="form-group row">
        <label class="col-sm-4 col-form-label">Investigator Accepting the Assignment :</label>
        <span class="label_Value col-md-4 ml-0">..............................................</span>
        <span class="label_Value col-md-4 ml-0">..............................................</span>
    </div>
    <div class="form-group row">
        <label class="col-sm-4 col-form-label">Important :</label>
        <div class="col-sm-8">
            <p class="text-danger">If Audio / Video evidence is part of the
                investigation report, an affidavit confirming the authenticity of the
                recording has to be
                provided by the investigator along with the recording.</p>
        </div>
    </div>

    <div class="form-group row">
        <div class="col-sm-6">
            <label class="col-form-label label_Value ">Date/Time :
                <span> ${investigationDetailsDto1.investArrangeDateTime}</span></label>
        </div>
        <div class="col-sm-6">
            <label class="col-form-label label_Value ">Job No
                :<span> ${investigationDetailsDto1.investJobNo}</span></label>
        </div>
    </div>

</div>

