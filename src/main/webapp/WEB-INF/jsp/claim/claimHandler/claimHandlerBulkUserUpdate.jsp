<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }


%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/ScrollTabla.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/datatables.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/datatables.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/dataTables.fixedHeader.min.js"></script>


    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        var type = '${TYPE}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);


        });

        function init() {
            hideLoader();
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="N_TXN_NO" id="N_TXN_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim User Reassign</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="liabilityType" class="col-sm-4 col-form-label">Assign User
                                                Type</label>
                                            <div class="col-sm-8">
                                                <c:choose>
                                                <c:when test="${TYPE == 200}">
                                                <select name="liabilityType" id="liabilityType"
                                                        class="form-control form-control-sm"
                                                        onchange="resetFields(this.value)">
                                                    </c:when>
                                                    <c:when test="${TYPE == 100}">
                                                    <select name="liabilityType" id="liabilityType"
                                                            class="form-control form-control-sm"
                                                            onchange="setDoStatus(this.value); showMake1(this.value)">
                                                        </c:when>
                                                    <c:otherwise>
                                                    <select name="liabilityType" id="liabilityType"
                                                            class="form-control form-control-sm"
                                                            onchange="showMake1(this.value)">
                                                        </c:otherwise>
                                                        </c:choose>
                                                    <c:choose>
                                                        <c:when test="${TYPE == 100}">
                                                            <option value="">Please Select</option>
                                                            <option value="13">Spare Parts Coordinator - Create Supply
                                                                Order
                                                            </option>
                                                            <option value="14">Spare Parts Coordinator - Check
                                                                Calsheet
                                                            </option>
                                                            <option value="15">Bill Checking Team - Check Supply Order
                                                            </option>
                                                            <option value="16">Bill Checking Team - Check Calsheet
                                                            </option>
                                                        </c:when>
                                                        <c:when test="${TYPE == 200}">
                                                            <option value="">Please Select</option>
                                                            <option value="6">MOFA</option>
                                                            <option value="12">Offer Team - MOFA</option>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <option value="">Please Select</option>
                                                            <option value="1">Claim Handler</option>
                                                            <option value="7">Initial Liability Team</option>
                                                            <option value="2">Decision Maker</option>
                                                            <option value="3">Sub Panel Member</option>
                                                            <option value="4">Main Panel Member</option>
                                                            <option value="5">Special Team</option>
                                                            <option value="8">Total Loss Team</option>
                                                            <option value="9">Offer Team - Claim Handler</option>
                                                            <option value="10">Offer Team - Initial Liability Team
                                                            </option>
                                                            <option value="11">Offer Team - Special Team</option>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </select>
                                            </div>
                                        </div>
                                        <c:if test="${TYPE == 200}">
                                            <div class="form-group row">
                                                <label for="selMofaLevel" class="col-sm-4 col-form-label">Assign User
                                                    Level</label>
                                                <div class="col-sm-8">
                                                    <select name="selMofaLevel" id="selMofaLevel"
                                                            class="form-control form-control-sm"
                                                            onchange="showMake1(this.value)">
                                                        <option value="0">Please Select</option>
                                                        <c:forEach var="menuItem" items="${mofaLevels}">
                                                            <option value="${menuItem.value}">${menuItem.label}</option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </c:if>
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <c:forEach var="listDto" items="${statusList}">
                                                        <option value="${listDto.value}">${listDto.label}</option>
                                                    </c:forEach>

                                                    <script type="text/javascript">
                                                        $('#txtV_status').val("0");
                                                    </script>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtFinalizedStatus" class="col-sm-4 col-form-label"> Finalized
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFinalizedStatus" id="txtFinalizedStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">All</option>
                                                    <option value="CLOSE">Closed</option>
                                                    <option value="REOPEN">Reopened</option>
                                                    <option value="SETTLE">Settled</option>
                                                    <option value="PENDING">Pending</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtFileStatus" class="col-sm-4 col-form-label"> File
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFileStatus" id="txtFileStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All" selected>All</option>
                                                    <option value="1">Active & Auto Restored</option>
                                                    <option value="2">Stored</option>
                                                    <option value="3">Auto Stored</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <c:if test="${TYPE == 200}">
                                            <span>&nbsp;</span>
                                            <div class="form-group row">
                                                <span>&nbsp;</span>
                                                <div class="col-sm-8">
                                                    <span>&nbsp;</span>
                                                </div>
                                            </div>
                                        </c:if>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label">Assign
                                                User </label>
                                            <div class="col-sm-8">
                                                <select class="form-control form-control-sm chosen"
                                                        name="assignUserName"
                                                        id="assignUserName" onchange="validateMofaGridResults()">
                                                    <option value="">Please Select</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Reference
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle /
                                                Trade Plate Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle / Trade Plate Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtLiabilityStatus" class="col-sm-4 col-form-label"> Liability
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtLiabilityStatus" id="txtLiabilityStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">All</option>
                                                    <option value="P">Pending</option>
                                                    <option value="A">Approved</option>
                                                    <option value="F">Forwarded to Higher Level</option>
                                                    <option value="H">Held</option>
                                                    <option value="R">Rejected</option>

                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtCalsheetStatus" class="col-sm-4 col-form-label"> Calsheet
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtCalsheetStatus" id="txtCalsheetStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">All</option>
                                                    <option value="REJECTED">Rejected</option>
                                                    <option value="ADVANCE_PAID">Advance Paid</option>
                                                </select>
                                                <c:if test="${TYPE==29}">
                                                    <script type="text/javascript">
                                                        $('#txtCalsheetStatus').val("");
                                                    </script>
                                                </c:if>
                                                <c:if test="${TYPE==30}">
                                                    <script type="text/javascript">
                                                        $('#txtCalsheetStatus').val("");
                                                    </script>
                                                </c:if>
                                            </div>
                                        </div>
                                        <div class="row" id="divDoStatus">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Supplier Order
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtSupplierOrderStatus" id="txtSupplierOrderStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <option value="1">PENDING</option>
                                                    <option value="3">FORWARD TO SCRUTINIZING TEAM</option>
                                                    <option value="4">SCRUTINIZING TEAM APPROVED</option>
                                                    <option value="6">CLAIM HANDLER APPROVED</option>
                                                    <option value="9">LETTER GENERATED</option>
                                                    <option value="10">UPDATED</option>
                                                    <option value="11">SPARE PARTS COORDINATOR APPROVAL PENDING</option>
                                                    <%--option value="F">Forwarded to Higher Level</option>
                                                    <option value="H">Held</option>
                                                    <option value="R">Rejected</option--%>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-file text-info">&nbsp;</span>&nbsp;&nbsp; File Store
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="	fa fa-car text-dark">&nbsp;</span>&nbsp;&nbsp; Partial loss
                                </p>
                            </div>

                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-car text-danger">&nbsp;</span>&nbsp;&nbsp; Total Loss
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-times text-warning">&nbsp;</span>&nbsp;&nbsp; Document Pending
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-check text-success">&nbsp;</span>&nbsp;&nbsp; Document Complete
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa fa-question-circle text-warning">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Arranged
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa fa-check-circle text-success">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Done
                                </p>
                            </div>
                        </div>

                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim List</h6>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover table-striped"
                                               cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th class=""><input type="checkbox" id="selectAll"
                                                                    onclick="selectAllCheckBoxs();"/></th>
                                                <th class="">ref no</th>
                                                <th class="" width="50px">No</th>
                                                <th class="">Claim No</th>
                                                <th class="">Vehicle No</th>
                                                <th class="">Policy No</th>
                                                <th class="">Calsheet Status</th>
                                                <%--<th class="">Claim Status</th>--%>
                                                <th class="">Assign User</th>
                                                <th class="">Assign Date Time</th>
                                                <th class="">Claim Status Desc</th>
                                                <th class="">Finalize Status</th>
                                                <th class="min-mobile">Edit</th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
                             aria-hidden="true">
                            <div class="modal-dialog " role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="exampleModalLabel">Reassign User</h5>
                                    </div>
                                    <div class="modal-body">
                                        <input type="hidden" class="form-control" name="txnId" id="txnId">
                                        <input type="hidden" class="form-control" name="claimNos" id="claimNos">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Claim No</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="claimNo" id="claimNo"
                                                       readonly>
                                            </div>
                                        </div>

                                        <div class="form-group row" style="display: none">
                                            <%--<label class="col-sm-4 col-form-label">style="display: none"</label>--%>
                                            <div class="col-sm-8">
                                                <input class="form-control" name="liabilityAssignUser"
                                                       id="liabilityAssignUser">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Assign User</label>
                                            <div class="col-sm-8">
                                                <input class="form-control" name="currentUser"
                                                       id="currentUser" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group row" id="divMofaLevel">
                                            <label class="col-sm-4 col-form-label">New Mofa Level</label>
                                            <div class="col-sm-8">
                                                <input class="form-control" name="mofaLevel"
                                                       id="mofaLevel" readonly>
                                            </div>
                                        </div>
                                        <script>
                                            if (${TYPE == 200}) {
                                                $('#divMofaLevel').show();
                                            } else {
                                                $('#divMofaLevel').hide();
                                            }
                                        </script>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Add New Assign User</label>
                                            <div class="col-sm-8">
                                                <select class="form-control form-control-sm chosen" name="assignUser"
                                                        id="assignUser" onchange="showMake(this.value)">
                                                    <option value="">Please Select</option>
                                                </select>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" id="addClaimUserbtn" class="btn btn-primary"
                                                name="addClaimUserbtn"
                                                onclick="updateUser()" disabled>Save changes
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%--bulk panel user--%>
                        <div class="modal fade animated fadeInDown" id="BulReassignpanelUser" tabindex="-1"
                             role="dialog"
                             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
                             aria-hidden="true">
                            <div class="modal-dialog " role="document" style="max-width: 80%">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="exampleModalLabel">Bulk Reassign User</h5>
                                    </div>
                                    <div class="modal-body">
                                        <input type="hidden" class="form-control" name="txnId" id="txnId">
                                        <input type="hidden" class="form-control" name="claimNos" id="claimNos">
                                        <input type="hidden" class="form-control" name="mofalevelNo" id="mofalevelNo">
                                        <div class="form-group row">
                                            <div class="col-md-12">
                                                <label style="font-size: medium">Selected
                                                    Claim Numbers</label>
                                            </div>
                                            <div class="col-md-12" id="claimNoListDiv">
                                            </div>
                                            <input type="hidden" name="claimNoListTxt" id="claimNoListTxt" value="">

                                            <div class="col-md-5">
                                                <label style="font-size: medium;">Add
                                                    New
                                                    Assign User</label>

                                                <select class="form-control form-control-sm chosen"
                                                        name="assignUserBulkReassign"
                                                        id="assignUserBulkReassign" onchange="showMake(this.value)">
                                                    <option value="">Please Select</option>
                                                </select>
                                            </div>


                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" id="addBulkClaimUserbtn" class="btn btn-primary"
                                                    name="addBulkClaimUserbtn"
                                                    onclick="updateBulkReassignUser()" disabled>Save changes
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="closeModal()">
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12" style="padding: 12px">
                        <div id="claimReassignDiv">
                            <button class="btn-success btn btn-lg float-right btn-xs" type="button"
                                    id="claimReassignBtn"
                                    onclick="getSelectedIds(document.getElementsByName('checkBox'),document.getElementsByName('ClaimNoAndAssignUserBox'),document.getElementsByName('claimNo'))">
                                Claim Reassign <i class="fa fa-edit"></i>
                            </button>
                        </div>

                    </div>
                </div>
            </div>
            <input type="hidden" id="selectedIds" name="selectedIds">
            <input type="hidden" id="selectClaimNoAndAssignUsers" name="selectClaimNoAndAssignUsers">
            <input type="hidden" id="selectClaimNos" name="selectClaimNos">
            <input type="hidden" id="idArray" name="idArray">
        </div>

    </form>
</div>


<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimbulkuserupdate-datatables.js?v7"></script>
<script type="text/javascript">

    $(document).ready(function () {
        $("#claimCloseDiv").hide();
    });

    function selectAllCheckBoxs() {
        if (document.getElementById('selectAll').checked) {
            $('.checkBtn').prop('checked', true);
        } else {
            $('.checkBtn').prop('checked', false);
        }
        enableReassignBtn();

    }

    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });

    function updateUser() {
        var txnId = $('#txnId').val();
        var claimNo = $('#claimNos').val();
        var assignUser = $('#assignUser').val();
        var liabilityAssignUser = $('#liabilityAssignUser').val();
        var liabilityType = $('#liabilityType').val();
        var alreadyAssignUser = $('#currentUser').val();
        var level = $('#mofalevelNo').val();
        var status = $('#txtV_status').val();
        if (status =='57'){
            level=0;
        }

    $.ajax({
        url: contextPath + "/LiabilityUserUpdateController/update?txnId=" + txnId + "&assignUser=" + assignUser + "&liabilityAssignUser=" + liabilityAssignUser + "&claimNo=" + claimNo + "&assignUserType=" + liabilityType + "&alreadyAssignUser=" + alreadyAssignUser + "&level=" + level + "&status=" + status,
        type: 'POST',
        success: function (result) {
            var obj = JSON.parse(result);
            if (obj != null) {
                if (obj == "1") {
                    closeModal();
                    notify("Successfully Update User", "success");
                } else {
                    notify("Fail to Update User", "danger");
                }

            }
        }
    });

    }

    function updateBulkReassignUser() {

        if ($('#assignUserBulkReassign').val() == '') {
            return;
        }

        var notifyMsg = "Successfully Update User";

        if (type == 200) {
            notifyMsg = "User Successfully Re-Assigned";
        }

        var claimNoAndTxnIds = $('#selectedIds').val();
        var claimNoList = $('#selectClaimNos').val();
        var assignUser = $('#assignUserBulkReassign').val();
        var liabilityType = $('#liabilityType').val();
        var claimNoAndAlreadyAssignUser = $('#claimNoListTxt').val();
        var status = $('#txtV_status').val();
        var level = $("#mofalevelNo").val();
        if (status =='57'){
            level=0;
        }
        $.ajax({
            url: contextPath + "/LiabilityUserUpdateController/updateBulkReassign",
            type: 'POST',
            data: {
                'claimNoAndTxnIds': claimNoAndTxnIds,
                'assignUser': assignUser,
                'claimNoList': claimNoList,
                'assignUserType': liabilityType,
                'claimNoAndAlreadyAssignUser': claimNoAndAlreadyAssignUser,
                level,
                status
            },
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    if (obj == "SUCCESS") {
                        notify(notifyMsg, "success");
                        closeModal();
                    } else {
                        notify("Fail to Update User", "danger");
                    }

                }
            }
        });

    }


    $(document).ready(function () {
        $("#assignUserName").chosen({
            no_results_text: "No results found!",
            width: "100%"
        });

        $("#assignUser").chosen({
            no_results_text: "No results found!",
            width: "100%"
        });
    });

    function showMake(Vehiclemake) {
        $('#liabilityAssignUser').val(Vehiclemake);
        if (Vehiclemake != "") {
            document.getElementById("addClaimUserbtn").disabled = false;
            document.getElementById("addBulkClaimUserbtn").disabled = false;
        } else {
            document.getElementById("addClaimUserbtn").disabled = true;
            document.getElementById("addBulkClaimUserbtn").disabled = true;
        }
    };

    function showMake1(userType) {
        $("#assignUser option").remove();
        $("#assignUserName option").remove();
        $("#assignUserBulkReassign option").remove();
        $("#assignUser").append('<option value="0" >Please Select</option>');
        $("#assignUserName").append('<option value="0" >Please Select</option>');
        $("#assignUserBulkReassign").append('<option value="0" >Please Select</option>');
        if (userType != 0) {
            if (type == 200) {
                if ($("#selMofaLevel").val() == '' || $("#selMofaLevel").val() == 0) {
                    $("#assignUserName").trigger("chosen:updated");
                    return;
                } else if ($("#liabilityType").val() == '' || $("#liabilityType").val() == 0) {
                    $("#assignUserName").val(0);
                    $("#assignUserName").trigger("chosen:updated");
                    notify('Please Select the User Type', 'danger');
                    return;
                }
            }
            $.ajax({
                url: contextPath + "/LiabilityUserUpdateController/getUserList",
                data: {
                    assignUserType: userType,
                    level: userType,
                    userType: $("#liabilityType").val(),
                    TYPE: type
                },
                type: 'POST',
                success: function (result) {
                    var userArr = JSON.parse(result);
                    if (userArr == 'NOT FOUND') {
                        $("#assignUserName").trigger("chosen:updated");
                        notify("No Users Found For the Level " + userType, 'danger');
                        return;
                    }
                    var selOpts = "";
                    for (i = 0; i < userArr.length; i++) {
                        var val = userArr[i];

                        selOpts += "<option value='" + val + "'>" + val + "</option>";
                    }

                    $('#assignUser').append(selOpts).trigger("chosen:updated");
                    $('#assignUserName').append(selOpts).trigger("chosen:updated");
                    $('#assignUserBulkReassign').append(selOpts).trigger("chosen:updated");
                }
            });
        } else {
            $("#assignUserName").trigger("chosen:updated");
        }
    }

    $("#submitBtn").on("click", function () {
        $("#submitBtn").attr("data-dismiss", "modal");
        location.reload();
    });

    function getSelectedIds(checkBoxElements, ClaimNoAndAssignUserBoxElements, claimNoElements) {
        var selectedIds = "";
        var selectClaimNoAndAssignUsers = "";
        var selectClaimNos = "";
        var levelReassign = $("#txtV_status").val() == 57;
        console.log(checkBoxElements);
        if (type == 200) {
            if ($("#assignUserName").val() == '' || $("#assignUserName").val() == 0) {
                notify('Please Select a File Assign User', 'danger')
                return;
            }
        }
        var mofaUser = '';
        for (var i = 0; i < checkBoxElements.length; i++) {
            var id = checkBoxElements[i].getAttribute('id');
            var claimNoAndAssignUser = ClaimNoAndAssignUserBoxElements[i].getAttribute('id');
            var claimNos = claimNoElements[i].getAttribute('id');

            if (document.getElementById(id).checked) {
                var valId = id;
                var valclaimNoAndAssignUser = claimNoAndAssignUser;
                var valClaimNo = claimNos;
                mofaUser = valclaimNoAndAssignUser.split('- ')[1];
                selectedIds += valId + ",";
                selectClaimNoAndAssignUsers += valclaimNoAndAssignUser + ",";
                selectClaimNos += valClaimNo + ",";
            }
        }

        if (type == 200) {
            var status = $('#txtV_status').val();
            console.log(status+"hello2")
            $.ajax({
                url: contextPath + "/LiabilityUserUpdateController/searchMofa",
                data: {
                    assignUser: mofaUser,
                    txnNo: 0,
                    levelReassign,
                    claimStatus:status
                },
                type: 'POST',
                success: function (result) {
                    var response = JSON.parse(result);
                    if (response == "NOT FOUND") {
                        selectedIds = '';
                        notify('No Users Found to Reassign File', 'danger');
                        return;
                    }
                    if (response.length > 1) {
                        $("#mofalevelNo").val(response[response.length - 1]);
                        $("#assignUserBulkReassign option").remove();
                        var selOpts = "<option value='0'>Please Select</option>";
                        for (i = 0; i < response.length - 2; i++) {
                            var val = response[i];

                            selOpts += "<option value='" + val + "'>" + val + "</option>";
                        }
                        $("#assignUserBulkReassign").append(selOpts).trigger("chosen:updated");
                    } else {
                        selectedIds = '';
                        notify('No Users Found to Reassign File', 'danger');
                        return;
                    }
                }
            });
        }
        $('#selectedIds').val(selectedIds);
        $('#selectClaimNoAndAssignUsers').val(selectClaimNoAndAssignUsers);
        $('#selectClaimNos').val(selectClaimNos);
        if (selectedIds != '') {
            setIdArray(selectClaimNoAndAssignUsers);
        } else {
            notify('Please Select One', "danger");
        }
    }

    function setIdArray(selectClaimNoAndAssignUsers) {
        $.ajax({
            type: 'POST',
            url: contextPath + "/LiabilityUserUpdateController/selectClaimNoAndAssignUsers",
            data: {
                "selectClaimNoAndAssignUsers": selectClaimNoAndAssignUsers
            },
            success: function (data) {
                var obj = JSON.parse(data);
                var userType = $('#liabilityType').val();
                if (type != 200) {
                    showMake1(userType);
                }
                if (null != obj) {

                    $("#BulReassignpanelUser").modal({
                        backdrop: 'static',
                        keyboard: false
                    });

                    var userArr = obj;
                    var selOpts = "";
                    var selectClaims = "";
                    for (i = 0; i < userArr.length; i++) {
                        var val = userArr[i];
                        selOpts += "<label class=\"col-sm-3 col-form-label\">" + val + "</label>"
                        selectClaims += val;
                    }

                    $('#claimNoListDiv').append(selOpts);
                    var userType = $('#liabilityType').val();

                    var alreadyAssignUser = $('#claimNoListTxt').val();
                    alreadyAssignUser = alreadyAssignUser + "" + selectClaims + ",";
                    $('#claimNoListTxt').val(alreadyAssignUser);
                }
            },
            error: function (response) {
                swal(response.responseText);
            }

        });
    }

    function validateMofaGridResults() {
        if (type == 200) {
            search();
        }
    }

    function resetFields(value) {
        if (value == 0) {
            $("#selMofaLevel").val(0);
            $("#assignUserName option").remove();
            $("#assignUserName").append('<option value="0" >Please Select</option>');
            $("#assignUserName").trigger("chosen:updated");
        }else{
            $("#assignUserName").val(0);
            $("#assignUserName").trigger("chosen:updated");
            $("#selMofaLevel").val(0);
            console.log("done");
        }
    }

    function closeModal() {
        $('#BulReassignpanelUser').modal('hide');
        $('#panelUser').modal('hide');
        showMake("")
        search();
    }

    function setDoStatus(value) {
        if (value == 13 || value == 15) {
            $("#divDoStatus").show();
        } else if(value == 14 || value == 16) {
            $("#divDoStatus").hide();
        }
    }

</script>

</body>
</html>
