<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }


%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/ScrollTabla.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/datatables.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/datatables.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/dataTables.fixedHeader.min.js"></script>

    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        var type = '${TYPE}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);


        });

        function init() {
            hideLoader();
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="N_TXN_NO" id="N_TXN_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Handler Reassign</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label">Assign User
                                                Type</label>
                                            <div class="col-sm-8">
                                                <select name="liabilityType" id="liabilityType"
                                                        class="form-control form-control-sm"
                                                        onchange="showMake1(this.value)">
                                                    <option value="0">Please Select</option>
                                                    <option value="1">Claim Handler</option>
                                                    <option value="7">Initial Liability Team</option>
                                                    <option value="2">Decision Maker</option>
                                                    <option value="3">Sub Panel Member</option>
                                                    <option value="4">Main Panel Member</option>
                                                    <option value="5">Special Team</option>
                                                    <option value="8">Total Loss Team</option>
                                                    <option value="9">Offer Team - Claim Handler</option>
                                                    <option value="10">Offer Team - Initial Liability Team</option>
                                                    <option value="11">Offer Team - Special Team</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number">
                                            </div>
                                        </div>
                                        <div class="form-group row" id="statusDiv">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <option value="38">FORWARD TO DECISION MAKER</option>
                                                    <option value="41">2 MEMBER PANEL REJECTION APPROVED</option>
                                                    <option value="44">4 MEMBER PANEL REJECTION APPROVED</option>
                                                </select>
                                            </div>
                                        </div>
                                        <script type="text/javascript">
                                            var liabilityType = $('#liabilityType').val();

                                            if ('2' == liabilityType) {
                                                $("#statusDiv").show();
                                                $("#txtV_status").val("0")
                                            } else {
                                                $("#statusDiv").hide();
                                                $("#txtV_status").val("");
                                            }
                                        </script>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Finalized
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFinalizedStatus" id="txtFinalizedStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="">All</option>
                                                    <option value="CLOSE">Closed</option>
                                                    <option value="REOPEN">Reopened</option>
                                                    <option value="SETTLE">Settled</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> File
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFileStatus" id="txtFileStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All" selected>All</option>
                                                    <option value="1">Active & Auto Restored</option>
                                                    <option value="2">Stored</option>
                                                    <option value="3">Auto Stored</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label">Assign
                                                User </label>
                                            <div class="col-sm-8">
                                                <select class="form-control form-control-sm chosen"
                                                        name="assignUserName"
                                                        id="assignUserName">
                                                    <option value="">Please Select</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Reference
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle /
                                                Trade Plate Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle / Trade Plate Number">
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Liability
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtLiabilityStatus" id="txtLiabilityStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All">All</option>
                                                    <option value="P">Pending</option>
                                                    <option value="A">Approved</option>
                                                    <option value="F">Forwarded to Higher Level</option>
                                                    <option value="H">Held</option>
                                                    <option value="R">Rejected</option>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-file text-info">&nbsp;</span>&nbsp;&nbsp; File Store
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="	fa fa-car text-dark">&nbsp;</span>&nbsp;&nbsp; Partial loss
                                </p>
                            </div>

                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-car text-danger">&nbsp;</span>&nbsp;&nbsp; Total Loss
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-times text-warning">&nbsp;</span>&nbsp;&nbsp; Document Pending
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-check text-success">&nbsp;</span>&nbsp;&nbsp; Document Complete
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa fa-question-circle text-warning">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Arranged
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa fa-check-circle text-success">&nbsp;</span>&nbsp;&nbsp;
                                    Investigation Done
                                </p>
                            </div>
                        </div>

                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim List</h6>
                                    <div class="mt-2">
                                        <table id="demo-dt-basic" class="table table-sm table-hover table-striped"
                                               cellspacing="0"
                                               style="cursor:pointer" width="100%">
                                            <thead>
                                            <tr>
                                                <th class="">ref no</th>
                                                <th class="" width="50px">No</th>
                                                <th class="">Claim No</th>
                                                <th class="">Vehicle No</th>
                                                <th class="">Policy No</th>
                                                <%--<th class="">Claim Status</th>--%>
                                                <th class="">Assign User</th>
                                                <th class="">Assign Date Time</th>
                                                <th class="">Claim Status Desc</th>
                                                <th class="min-mobile">Edit</th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade animated fadeInDown" id="panelUser" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
                             aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="exampleModalLabel">Reassign User</h5>
                                    </div>
                                    <div class="modal-body">
                                        <input type="hidden" class="form-control" name="txnId" id="txnId">
                                        <input type="hidden" class="form-control" name="claimNos" id="claimNos">
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Claim No</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="claimNo" id="claimNo"
                                                       readonly>
                                            </div>
                                        </div>

                                        <div class="form-group row" style="display: none">
                                            <%--<label class="col-sm-4 col-form-label">style="display: none"</label>--%>
                                            <div class="col-sm-8">
                                                <input class="form-control" name="liabilityAssignUser"
                                                       id="liabilityAssignUser">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Assign User</label>
                                            <div class="col-sm-8">
                                                <input class="form-control" name="currentUser"
                                                       id="currentUser" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-4 col-form-label">Add New Assign User</label>
                                            <div class="col-sm-8">
                                                <select class="form-control form-control-sm chosen" name="assignUser"
                                                        id="assignUser" onchange="showMake(this.value)">
                                                    <option value="">Please Select</option>
                                                </select>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" id="addClaimUserbtn" class="btn btn-primary"
                                                name="addClaimUserbtn"
                                                onclick="updateUser()" disabled>Save changes
                                        </button>
                                        <button type="button" class="btn btn-secondary"
                                                onclick="closeModal()">
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>


<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimuserupdate-datatables.js?v5"></script>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });

    function updateUser() {
        var txnId = $('#txnId').val();
        var claimNo = $('#claimNos').val();
        var assignUser = $('#assignUser').val();
        var liabilityAssignUser = $('#liabilityAssignUser').val();
        var alreadyAssignUser = $('#currentUser').val();
        var liabilityType = $('#liabilityType').val();

        if ('2' == liabilityType) {
            $("#statusDiv").show();
            $("#txtV_status").val("0")
        } else {
            $("#statusDiv").hide();
            $("#txtV_status").val("");
        }


        $.ajax({
            url: contextPath + "/LiabilityUserUpdateController/update?txnId=" + txnId + "&assignUser=" + assignUser + "&liabilityAssignUser=" + liabilityAssignUser + "&claimNo=" + claimNo + "&assignUserType=" + liabilityType + "&alreadyAssignUser=" + alreadyAssignUser,
            type: 'POST',
            success: function (result) {
                var obj = JSON.parse(result);
                if (obj != null) {
                    if (obj == "1") {
                        closeModal();
                        notify("Successfully Update User", "success");
                    } else {
                        notify("Fail to Update User", "danger");
                    }

                }
            }
        });

    }

    $(document).ready(function () {
        $("#assignUserName").chosen({
            no_results_text: "No results found!",
            width: "100%"
        });

        $("#assignUser").chosen({
            no_results_text: "No results found!",
            width: "100%"
        });
    });

    function showMake(Vehiclemake) {
        $('#liabilityAssignUser').val(Vehiclemake);
        if (Vehiclemake != "") {
            document.getElementById("addClaimUserbtn").disabled = false;
        } else {
            document.getElementById("addClaimUserbtn").disabled = true;
        }
        // $('#addClaimUserbtn').disable = false;
    };

    function showMake1(userType) {
        if ('2' == userType) {
            $("#statusDiv").show();
            $("#txtV_status").val("0")
        } else {
            $("#statusDiv").hide();
            $("#txtV_status").val("");
        }
        $("#assignUser option").remove();
        $("#assignUserName option").remove();
        $("#assignUser").append('<option value="" >Please Select</option>');
        $("#assignUserName").append('<option value="" >Please Select</option>');
        $.ajax({
            url: contextPath + "/LiabilityUserUpdateController/getUserList?assignUserType=" + userType,
            type: 'POST',
            success: function (result) {
                var userArr = JSON.parse(result);
                var selOpts = "";
                for (i = 0; i < userArr.length; i++) {
                    var val = userArr[i];

                    selOpts += "<option value='" + val + "'>" + val + "</option>";
                }

                $('#assignUser').append(selOpts).trigger("chosen:updated");
                $('#assignUserName').append(selOpts).trigger("chosen:updated");
            }
        });
    };

    $("#submitBtn").on("click", function () {
        $("#submitBtn").attr("data-dismiss", "modal");
        location.reload();
    });

    function closeModal() {
        $('#panelUser').modal('hide');
        search();
    }

</script>

</body>
</html>
