<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 11/15/2018
  Time: 1:01 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div>
    <%--fileview 1--%>
    <div class="row">
        <div class="col-lg-12">
            <div id="accordionone" class="accordion">
                <div class="card">
                    <div class="card-header" id="headingone">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                               data-target="#collapseone" aria-expanded="true" aria-controls="collapseone">
                                View Bills<i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapseone" class="collapse hide" aria-labelledby="headingone"
                         data-parent="#accordion1">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 pdf-thumbnails">
                                    <c:set var="tempDocTypeId" value=""/>
                                    <c:forEach var="estimateDocument"
                                               items="${claimCalculationSheetMainDto.otherBillClaimDocumentStatusDto.claimDocumentDtoList}">
                                        <c:set var="iconColorCls" value=" text-dark "/>
                                        <c:if test="${estimateDocument.documentStatus=='A'}">
                                            <c:set var="iconColorCls" value=" text-success"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='H'}">
                                            <c:set var="iconColorCls" value=" text-warning"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='R'}">
                                            <c:set var="iconColorCls" value=" text-danger"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                            <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                        </c:if>
                                        <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>

                                        <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                           href="#"
                                           class="claimView${estimateDocument.refNo} ${iconColorCls}">

                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                        </a>

                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div id="accordiontwo" class="accordion">
                <div class="card">
                    <div class="card-header" id="headingtwo">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                               data-target="#collapsetwo" aria-expanded="true" aria-controls="collapsetwo">
                                View Approved Bills <i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapsetwo" class="collapse hide" aria-labelledby="headingtwo"
                         data-parent="#accordion1">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 pdf-thumbnails">
                                    <c:forEach var="estimateDocument"
                                               items="${claimCalculationSheetMainDto.billClaimDocumentStatusDto.claimDocumentDtoList}">
                                        <c:set var="iconColorCls" value=" text-dark "/>
                                        <c:if test="${estimateDocument.documentStatus=='A'}">
                                            <c:set var="iconColorCls" value=" text-success"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='H'}">
                                            <c:set var="iconColorCls" value=" text-warning"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='R'}">
                                            <c:set var="iconColorCls" value=" text-danger"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                            <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                        </c:if>
                                        <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                        <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                           href="#"
                                           class="claimAproveBillView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                        </a>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--fileview 2--%>
    <div class="row">
        <div class="col-lg-12">
            <div id="accordion3" class="accordion">
                <div class="card">
                    <div class="card-header" id="heading3">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse3"
                               aria-expanded="true" aria-controls="collapse3">
                                View Approved Estimate <i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapse3" class="collapse hide" aria-labelledby="heading3"
                         data-parent="#accordion3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 pdf-thumbnails">
                                    <c:forEach var="estimateDocument"
                                               items="${claimCalculationSheetMainDto.estimateClaimDocumentStatusDto.claimDocumentDtoList}">
                                        <c:set var="iconColorCls" value=" text-dark "/>
                                        <c:if test="${estimateDocument.documentStatus=='A'}">
                                            <c:set var="iconColorCls" value=" text-success"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='H'}">
                                            <c:set var="iconColorCls" value=" text-warning"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='R'}">
                                            <c:set var="iconColorCls" value=" text-danger"/>
                                        </c:if>

                                        <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                            <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                        </c:if>
                                        <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>

                                        <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                           href="#"
                                           class="claimEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                    <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                        </a>

                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--fileview 3--%>
    <div class="row">
        <div class="col-lg-12">
            <div id="accordion4" class="accordion">
                <div class="card">
                    <div class="card-header" id="heading4">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse4"
                               aria-expanded="true" aria-controls="collapse4">
                                View DR <i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapse4" class="collapse hide" aria-labelledby="heading4"
                         data-parent="#accordion4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 pdf-thumbnails">
                                    <c:forEach var="estimateDocument"
                                               items="${claimCalculationSheetMainDto.drClaimDocumentStatusDto.claimDocumentDtoList}">
                                        <c:set var="iconColorCls" value=" text-dark "/>
                                        <c:if test="${estimateDocument.documentStatus=='A'}">
                                            <c:set var="iconColorCls" value=" text-success"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='H'}">
                                            <c:set var="iconColorCls" value=" text-warning"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='R'}">
                                            <c:set var="iconColorCls" value=" text-danger"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                            <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                        </c:if>
                                        <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                        <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                           href="#"
                                           class="claimApproveDrEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                        </a>

                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--fileview 4--%>
    <div class="row">
        <div class="col-lg-12">
            <div id="accordion5" class="accordion">
                <div class="card">
                    <div class="card-header" id="heading5">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse5"
                               aria-expanded="true" aria-controls="collapse5">
                                View Supplementary Estimate <i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapse5" class="collapse hide" aria-labelledby="heading5"
                         data-parent="#accordion5">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 pdf-thumbnails">
                                    <c:forEach var="estimateDocument"
                                               items="${claimCalculationSheetMainDto.supplyEstimateClaimDocumentStatusDto.claimDocumentDtoList}">
                                        <c:set var="iconColorCls" value=" text-dark "/>
                                        <c:if test="${estimateDocument.documentStatus=='A'}">
                                            <c:set var="iconColorCls" value=" text-success"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='H'}">
                                            <c:set var="iconColorCls" value=" text-warning"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='R'}">
                                            <c:set var="iconColorCls" value=" text-danger"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                            <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                        </c:if>
                                        <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                        <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                           href="#"
                                           class="claimApproveSupplyEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                        </a>

                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--fileview 5--%>
    <div class="row">
        <div class="col-lg-12">
            <div id="accordion1" class="accordion">
                <div class="card">
                    <div class="card-header" id="heading1">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapse1"
                               aria-expanded="true" aria-controls="collapse1">
                                View Other Estimates <i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapse1" class="collapse hide" aria-labelledby="heading1"
                         data-parent="#accordion1">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 pdf-thumbnails">
                                    <c:forEach var="estimateDocument"
                                               items="${claimCalculationSheetMainDto.otherEstimateClaimDocumentStatusDto.claimDocumentDtoList}">
                                        <c:set var="iconColorCls" value=" text-dark "/>
                                        <c:if test="${estimateDocument.documentStatus=='A'}">
                                            <c:set var="iconColorCls" value=" text-success"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='H'}">
                                            <c:set var="iconColorCls" value=" text-warning"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentStatus=='R'}">
                                            <c:set var="iconColorCls" value=" text-danger"/>
                                        </c:if>
                                        <c:if test="${estimateDocument.documentTypeId!=tempDocTypeId}">
                                            <div class="text-dark">${estimateDocument.claimDocumentTypeDto.documentTypeName}</div>
                                        </c:if>
                                        <c:set var="tempDocTypeId" value="${estimateDocument.documentTypeId}"/>
                                        <a onclick="viewDocument('${estimateDocument.refNo}','${estimateDocument.refNo}','${PREVIOUS_INSPECTION}','right');"
                                           href="#"
                                           class="claimApproveOtherEstimateView${estimateDocument.refNo} ${iconColorCls}">
                                                <span>
                                                    <i class="fa fa-file-pdf-o fa-2x m-3 "></i>
                                                </span>
                                        </a>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div id="accordionseven" class="accordion">
                <div class="card">
                    <div class="card-header" id="headingseven" onclick="setName()">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse"
                               data-target="#collapseseven" aria-expanded="true" aria-controls="collapseseven">
                                Special Remark<i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapseseven" class="collapse hide" aria-labelledby="headingseven"
                         data-parent="#accordionseven">
                        <div id="setName"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
