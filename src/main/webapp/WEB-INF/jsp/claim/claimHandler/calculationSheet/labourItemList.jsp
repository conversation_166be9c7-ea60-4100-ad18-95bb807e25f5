<%--
  Created by IntelliJ IDEA....
  User: M I Synergy
  Date: 11/3/2018
  Time: 6:33 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<div>

    <table style="width: 100%;" class="table-responsive sfont">
        <thead>
        <c:set var="labourTableName" value="labour"/>
        <tr>
            <th scope="col" class="tbl_row_header" style="width: 4%;">Item No</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Approved <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">VAT Rate (%)</th>

            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Remove VAT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailLabourAllRemoveVat"
                                   id="allLabourRemoveVat" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y" onchange="selectAllRemoveVatCheckBoxes('labour')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Amount <br>Without <br>VAT <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">O/A (%)</th>

            <th scope="col" class="tbl_row_header" style="width: 6%;">O/A <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Total <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">NBT <br>Rate <br>(%)</th>

            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Add<br>NBT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailLabourAllAddNbt"
                                   id="allLabourAddNbt" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y" onchange="selectAllAddNbtCheckBoxes('labour')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>
            <th scope="col" class="tbl_row_header" style="width: 6%;">NBT <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Amount <br>with <br>NBT <br>(Rs.)</th>

            <th scope="col" class="tbl_row_header" style="width: 6%;">VAT <br>Rate <br>(%)</th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Add <br>VAT <br>Without <br>NBT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailLabourAllAddVatWithoutNbt"
                                   id="allLabourAddVatWithoutNbt" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y" onchange="selectAllAddVatWithoutNbtCheckBoxes('labour')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">
                <div style="text-align: center">
                    Add <br>VAT <br>With <br>NBT
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2">
                            <input name="claimCalculationSheetDetailLabourAllAddVatWithNbt"
                                   id="allLabourAddVatWithNbt" title="" type="checkbox"
                                   class="align-middle"
                                   value="Y"
                                   onchange="selectAllAddVatWithNbtCheckBoxes('labour')"/>
                            <span class="checkmark dynacheckheader"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </div>
            </th>

            <th scope="col" class="tbl_row_header" style="width: 6%;">VAT <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 8%;">Total <br>Amount <br>(Rs.)</th>
            <th scope="col" class="tbl_row_header" style="width: 2%; padding: 8px 1px 8px 1px;">Bills <br>Checked</th>
            <th scope="col" class="tbl_row_header" style="width: 2%;">Action</th>
        </tr>
        </thead>
        <tbody id="tblBody2">
        <c:set var="labourUniqueTableName" value="2000"/>
        <c:set var="labourCount" value="0"/>
        <c:forEach var="item"
                   items="${claimCalculationSheetMainDto.claimCalculationSheetDetailLabourDtos}"
                   varStatus="index">
            <c:set var="labourCount" value="${index.count}"/>
            <tr id="tr-${labourUniqueTableName}-${labourCount}">
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control item-no-class indexEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].itemNo"
                               readonly="true"
                               placeholder="Item No" value="${index.count}"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text" id="abc"
                               class="inputFocus form-control classReplacementApprovedAmount text-right approvedAmountEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].approvedAmount"
                               placeholder="Approved Amount" value="${item.approvedAmount}" autocomplete="off"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailLabourDtos[${index.count}].removeVatRate"
                                class="inputFocus form-control form-control-sm removeVatRateEvent-${labourCount}-${labourUniqueTableName}">
                                ${claimCalculationSheetMainDto.vatRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailLabourDtos[${index.count}].removeVatRate']").val('${item.removeVatRate}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2 dynacheckcontainer">
                            <input name="claimCalculationSheetDetailLabourDtos[${index.count}].isRemoveVat"
                                   id="isRemoveVat" title="" type="checkbox"
                                   class="align-middle isLabourRemoveVat isRemoveVatEvent-${labourCount}-${labourUniqueTableName}"
                                   value="Y" ${item.isRemoveVat eq 'Y' ? 'checked' : ''} />
                            <span class="checkmark dynacheck"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right amountWithoutVatEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].amountWithoutVat"
                               placeholder="Amount Without VAT" value="${item.amountWithoutVat}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailLabourDtos[${index.count}].oa"
                                class="form-control form-control-sm oaEvent-${labourCount}-${labourUniqueTableName}">
                                ${claimCalculationSheetMainDto.oaRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailLabourDtos[${index.count}].oa']").val('${item.oa}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right oaAmountEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].oaAmount"
                               placeholder="O/A Amount" value="${item.oaAmount}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right totalAmountAfterOaEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].totalAmountAfterOa"
                               placeholder="Total Amount" value="${item.totalAmountAfterOa}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailLabourDtos[${index.count}].nbtRate"
                                class="form-control form-control-sm nbtRateEvent-${labourCount}-${labourUniqueTableName}">
                                ${claimCalculationSheetMainDto.nbtRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailLabourDtos[${index.count}].nbtRate']").val('${item.nbtRate}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2 dynacheckcontainer">
                            <input name="claimCalculationSheetDetailLabourDtos[${index.count}].isAddNbt"
                                   id="isAddNbt" title="" type="checkbox"
                                   class="align-middle isLabourAddNbt isAddNbtEvent-${labourCount}-${labourUniqueTableName}"
                                   value="Y" ${item.isAddNbt eq 'Y' ? 'checked' : ''} />
                            <span class="checkmark dynacheck"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right nbtAmountEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].nbtAmount"
                               placeholder="NBT Amount" value="${item.nbtAmount}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control  text-right amountWithNbtEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].amountWithNbt"
                               placeholder="Amount With NBT" value="${item.amountWithNbt}"
                               readonly="true"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <select name="claimCalculationSheetDetailLabourDtos[${index.count}].vatRate"
                                class="form-control form-control-sm vatRateEvent-${labourCount}-${labourUniqueTableName}">
                                ${claimCalculationSheetMainDto.vatRateSelectItem}
                        </select>
                        <script type="text/javascript">
                            $("select[name='claimCalculationSheetDetailLabourDtos[${index.count}].vatRate']").val('${item.vatRate}');
                        </script>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container check-container2 dynaradiocontainer">
                            <input name="claimCalculationSheetDetailLabourDtos[${index.count}].addVatType"
                                   type="radio"
                                   value="WITHOUTNBT"
                                   class="align-middle labourAddVatWithoutNbt addVatWithoutNbtEvent-${labourCount}-${labourUniqueTableName} addVatTypeEvent-${labourCount}-${labourUniqueTableName} "
                                ${item.addVatType eq 'WITHOUTNBT' ? 'checked' : ''}/>
                            <span class="radiomark dynaradio"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container check-container2 dynaradiocontainer">
                            <input name="claimCalculationSheetDetailLabourDtos[${index.count}].addVatType"
                                   type="radio"
                                   value="WITHNBT"
                                   class="align-middle labourAddVatWithNbt addVatWithNbt-${labourCount}-${labourUniqueTableName} addVatTypeEvent-${labourCount}-${labourUniqueTableName} "
                                ${item.addVatType eq 'WITHNBT' ? 'checked' : ''}/>
                            <span class="radiomark dynaradio"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right vatAmountEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].vatAmount"
                               readonly="true"
                               placeholder="VAT Amount" value="${item.vatAmount}"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <input type="text"
                               class="form-control text-right totalAmountEvent-${labourCount}-${labourUniqueTableName}"
                               name="claimCalculationSheetDetailLabourDtos[${index.count}].totalAmount"
                               readonly="true"
                               placeholder="Total Amount" value="${item.totalAmount}"/>
                    </div>
                </td>
                <td class="parentDiv">
                    <div class="form-group">
                        <label class="custom-control custom-checkbox check-container check-container2 dynacheckcontainer">
                            <input name="claimCalculationSheetDetailLabourDtos[${index.count}].billChecked"
                                   id="checkReminderPrint0" title="" type="checkbox"
                                   class="align-middle billchecked ${item.calSheetDetailId}"
                                   value="Y" ${item.billChecked eq 'Y' ? 'checked' : ''}
                                   onclick="billCheck(${item.calSheetDetailId})"/>
                            <span class="checkmark dynacheck"></span>
                            <span class="custom-control-description"></span>
                        </label>
                    </div>
                </td>
                <td class="parentDiv">
                              <span class="input-group-btn">
                                        <button type="button"
                                                class="btn btn-default removeButtonReplacement" ${HISTORY_RECORD == 'Y' or claimCalculationSheetMainDto.status ==67 or
                                                claimCalculationSheetMainDto.status ==63 or claimCalculationSheetMainDto.status ==64
                                                or claimCalculationSheetMainDto.status ==65  ? 'disabled':''}
                                                onclick="deleteRow(${labourUniqueTableName},${index.count})"><i
                                                class="fa fa-minus"></i></button>
                                    </span>
                </td>
            </tr>
            <script>
                registerEvent(${labourUniqueTableName}, ${index.count});
            </script>
        </c:forEach>
        </tbody>
    </table>

    <table class="table table-bordered tablestyle table-responsive sfont" style="width: 100%;">
        <thead>
        <tr style="border-top: 2px solid #dee2e6;">

            <th scope="col" class="tbl_row_header colorwhite" width="4%"
                style="background: #fffff5!important; color:black"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="labourTotalApproveAmount" class="form-control  text-right bg-badge-succes">0.00</div>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="6%"></th>

            <th scope="col" class="tbl_row_header colorwhite" width="2%"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="labourTotalAmountWithoutVat" class="form-control  text-right bg-badge-succes">0.00</div>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="6%"></th>

            <th scope="col" class="tbl_row_header colorwhite" width="6%">
                <div id="labourOaAmount" class="form-control  text-right bg-badge-succes">0.00</div>
                <input type="hidden" id="labourOa" name="labourOa" value="0"/>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="labourTotalAmountAfterOa" class="form-control  text-right bg-badge-succes">0.00</div>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="6%"></th>

            <th scope="col" class="tbl_row_header colorwhite" width="2%"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="6%">
                <div id="labourNbtAmount" class="form-control  text-right bg-badge-succes">0.00</div>
                <input type="hidden" id="labourNbt" name="labourNbt" value="0"/>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="labourTotalAmountWithNbt" class="form-control  text-right bg-badge-succes">0.00</div>
            </th>

            <th scope="col" class="tbl_row_header colorwhite" width="6%"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="2%"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="2%"></th>


            <th scope="col" class="tbl_row_header colorwhite" width="6%">
                <div id="labourVatAmount" class="form-control  text-right bg-badge-succes">0.00</div>
                <input type="hidden" id="labourVat" name="labourVat" value="0"/>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="8%">
                <div id="labourTotalAmount" class="form-control  text-right bg-badge-succes">0.00</div>
            </th>
            <th scope="col" class="tbl_row_header colorwhite" width="2%"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="2%">
                <div class="input-group-btn float-right pr-1">
                    <button class="btn btn-primary" type="button" id="cmdLabour"><i
                            class="fa fa-plus"></i></button>
                </div>
            </th>

        </tr>
        </thead>
    </table>


</div>
