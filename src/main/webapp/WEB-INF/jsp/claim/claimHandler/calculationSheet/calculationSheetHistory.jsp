<%--
  Created by IntelliJ IDEA.
  User: madhushanka
  Date: 1/22/2020
  Time: 9:38 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<div>
    <table style="width: 100%;" class="table-responsive">
        <thead>
        <tr style="text-align: center">
            <th scope="col" class="tbl_row_header text-center" style="width: 1%;">Cal.<br>No</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 8%;">Calculation Sheet Type</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 6%;">Payee</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 8%;">Paid Amount (Rs.)</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 10%;">Date Of Paid</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 6%;">Claim Handler</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 6%;">ACR Approved RTE</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 6%;">Recommended By<br>(Motor Engineer)</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 8%;">Recommended By<br>(Special Team)</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 10%;">Recommended By<br>(Mofa Level)</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 6%;">Approved By</th>
            <th scope="col" class="tbl_row_header text-center" style="width: 4%;">Payment Status</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="item"
                   items="${claimCalculationSheetMainDto.calculationSheetHistoryDtoList}"
                   varStatus="index">
            <tr style="text-align: center; background-color: ${item.calsheetId == claimCalculationSheetMainDto.calSheetId ? 'deepskyblue' : ''}">
                <td class="text-center">${item.calsheetId}</td>
                <td class="text-center">${item.calsheetTypeDesc}</td>
                <td class="tbl_row_header text-center"><a onclick="loadPayeeModal('${item.calsheetId}')"
                                                          class="MainNavText" href="#myModal">Payee List</a>
                </td>
                <td class="text-right"><fmt:formatNumber
                        value="${item.paidAmount}"
                        pattern="###,##0.00;(###,##0.00)" type="number"/></td>
                <td class="tbl_row_header text-center">${item.dateOfPaid eq '1980-01-01 00:00:00' ? '' : item.dateOfPaid}</td>
                <td class="tbl_row_header text-center">${item.claimHandlerUserId}</td>
                <td class="tbl_row_header text-center">${item.acrApprovedRte}</td>
                <td class="tbl_row_header text-center">${'A' ==item.rteAction ? 'Approved By - ':'R' ==item.rteAction ? 'Returned By - ':''}${'A' ==item.rteAction || 'R' ==item.rteAction ? item.approvedRte : ''}</td>
                <td class="tbl_row_header text-center">${item.spteadmUserId}</td>
                <td class="tbl_row_header text-center"><a onclick="loadMofaModal('${item.calsheetId}')"
                                                          class="MainNavText" href="#myModal2">MOFA User List
                    users</a></td>
                <td class="tbl_row_header text-center">${item.approvedUserId}</td>
                <td class="text-center">
                    <c:choose>
                        <c:when test="${item.paymentStatus eq 58}">
                            <i class="fa fa-spinner text-warning" title="Pending "></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 59}">
                            <i class="fa fa-forward text-muted"
                               title="Forwarded to the SP for the creation of Calculation sheet "></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 60}">
                            <i class="fa fa-check text-warning"
                               title="Checked & Calculation sheet created by SP"></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 61}">
                            <i class="fa fa-forward text-warning"
                               title=" Forwarded to the Scrutinizing Team for the recommendation of the calculation sheet"></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 62}">
                            <i class="fa fa-check text-warning"
                               title="  Checked & Calculation sheet verify by Scrutinizing Team"></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 63}">
                            <i class="fa fa-forward text-info"
                               title=" Forwarded to the Payment Approval Special Team for Payment Approval "></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 64}">
                            <i class="fa fa-forward text-primary"
                               title="Forwarded to the Mofa limit for Payment Approval "></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 65}">
                            <i class="fa fa-check text-warning" title="Payment Approved "></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 66}">
                            <i class="fa fa-times text-danger" title=" Payment Rejected"></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 67}">
                            <i class="fa fa-check text-success" title="Payment voucher generated"></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 68}">
                            <i class="fa fa-check-circle text-success" title="INVESTIGATION REQUEST APPROVED"></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 70}">
                            <i class="fa fa-check text-warning" title="Payment voucher pending"></i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 71}">
                            <i class="fa fa-forward text-dark"
                               title="Forwarded to the motor engineering team for reserve amount approval ">
                            </i>
                        </c:when>
                        <c:when test="${item.paymentStatus eq 72}">
                            <i class="fa fa-spinner text-dark" title="Save as Draft "></i>
                        </c:when>
                    </c:choose>
                </td>
            </tr>

            <div class="modal fade animated fadeInDown" id="payeePanel${item.calsheetId}" tabindex="-1"
                 role="dialog"
                 aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
                 aria-hidden="true">

                <div class="modal-dialog " role="document" style="max-width: 60%">
                    <div class="modal-content">


                        <fieldset class=" border p-2 mt-2">
                            <h6><label id="paymentHistoryLabel">Payees Of Calsheet</label></h6>
                            <hr class="my-2">
                            <div class="row">
                                <div class="col-md-12 col-lg-12">

                                    <div id="payeeTable${item.calsheetId}">
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                    onclick="closePayeeModal(${item.calsheetId})">
                                Close
                            </button>
                        </div>
                    </div>
                </div>

            </div>

            <div class="modal fade animated fadeInDown" id="mofaPanel${item.calsheetId}" tabindex="-1"
                 role="dialog"
                 aria-labelledby="exampleModalLabel" data-backdrop="static" data-keyboard="false"
                 aria-hidden="true">

                <div class="modal-dialog " role="document" style="max-width: 60%">
                    <div class="modal-content">


                        <fieldset class=" border p-2 mt-2">
                            <h6><label id="mofaHistoryLabel">Other Approved MOFA Users</label></h6>
                            <hr class="my-2">
                            <div class="row">
                                <div class="col-md-12 col-lg-12">

                                    <div id="mofaTable${item.calsheetId}">
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                    onclick="closeMofaModal(${item.calsheetId})">
                                Close
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </c:forEach>
        </tbody>
    </table>

    <table class="table table-bordered tablestyle table-responsive" style="width: 100%;">
        <tbody>
        <tr style="border-top: 2px solid #dee2e6;">

            <th scope="col" class="tbl_row_header colorwhite" width="45px"
                style="background: #fffff5!important; color:black"></th>
            <th scope="col" class="tbl_row_header colorwhite" width="260px">Total Paid Amount :</th>
            <th scope="col" class="tbl_row_header colorwhite" width="250px">
                <fmt:formatNumber
                        value="${claimCalculationSheetMainDto.totalPaidAmount}"
                        pattern="###,##0.00;(###,##0.00)" type="number"/>
            </th>
        </tr>
        </tbody>
    </table>


</div>

<script type="text/javascript">


    function closePayeeModal(calsheetId) {
        $('#payeePanel' + calsheetId).modal('hide');
    }

    function closeMofaModal(calsheetId) {
        $('#mofaPanel' + calsheetId).modal('hide');
    }

    function loadPayeeModal(calsheetId) {

        $('#payeeTable' + calsheetId).html('');

        var appendDiv =
            "<table style=\"width: 100%;\" class=\"table-responsive\">\n" +
            "<thead>\n" +
            "<tr>\n" +
            "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Payee Type</th>\n" +
            "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 8%;\">Payee Name</th>\n" +
            "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Amount (Rs.)\n" +
            "    </th>\n" +
            "</tr>\n" +
            "</thead>\n" +
            "<tbody>";

        $.ajax({
            url: contextPath + "/CalculationSheetController/getPayeeHistoryList?calsheetId=" + calsheetId + "&claimNo=${claimHandlerDto.claimNo}",
            type: 'POST',
            success: function (result) {
                var payeeListArray = JSON.parse(result);

                for (i = 0; i < payeeListArray.length; i++) {
                    var payeeType = payeeListArray[i].payeeType;
                    var payeeName = payeeListArray[i].payeeName;
                    var amount = payeeListArray[i].amount;

                    appendDiv += "<tr><td class=\"text-center\">" + payeeType + "</td>\n" +
                        "<td class=\"text-center\">" + payeeName + "</td>\n" +
                        "<td class=\"text-right\">" + accounting.formatMoney(amount, "") + "</td></tr>"
                }

                appendDiv += "</tbody>"
                $('#payeeTable' + calsheetId).append(appendDiv);
                $('#payeePanel' + calsheetId).modal('show');
            }
        });

    };


    function loadMofaModal(calsheetId) {

        $('#mofaTable' + calsheetId).html('');

        var appendDiv =
            "<table style=\"width: 100%;\" class=\"table-responsive\">\n" +
            "<thead>\n" +
            "<tr>\n" +
            "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Mofa User</th>\n" +
            "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 8%;\">Authority Limit\n" +
            "        (Rs.)\n" +
            "    </th>\n" +
            "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Mofa Level\n" +
            "    </th>\n" +
            "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Approved Datetime\n" +
            "    </th>\n" +
            "</tr>\n" +
            "</thead>\n" +
            "<tbody>";

        $.ajax({
            url: contextPath + "/CalculationSheetController/getMofaHistoryList?calsheetId=" + calsheetId,
            type: 'POST',
            success: function (result) {
                var mofaListArray = JSON.parse(result);

                for (i = 0; i < mofaListArray.length; i++) {
                    var mofaUserId = mofaListArray[i].mofaUserId;
                    var toLimit = mofaListArray[i].toLimit;
                    var fromLimit = mofaListArray[i].fromLimit;
                    var mofaLevel = mofaListArray[i].mofaLevel;
                    var approvedDateTime = mofaListArray[i].approvedDateTime;

                    appendDiv += "<tr><td class=\"text-center\">" + mofaUserId + "</td>\n" +
                        "<td class=\"text-center\">" + accounting.formatMoney(fromLimit, "") + " - " + accounting.formatMoney(toLimit, "") + "</td>\n" +
                        "<td class=\"text-center\">" + mofaLevel + "</td>\n" +
                        "<td class=\"text-center\">" + approvedDateTime + "</td></tr>"
                }

                appendDiv += "</tbody>"
                $('#mofaTable' + calsheetId).append(appendDiv);
                $('#mofaPanel' + calsheetId).modal('show');
            }
        });

    };
</script>
