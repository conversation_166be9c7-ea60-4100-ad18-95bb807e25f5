<%--
  Created by IntelliJ IDEA.
  User: madhushanka
  Date: 9/12/2019
  Time: 12:19 PM
  To change this template use File | Settings | File Templates.
--%>

<%@include file="/common/ValidateUser.jsp" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<jsp:useBean id="DbRecordCommonFunctionBean" class="com.misyn.mcms.dbconfig.DbRecordCommonFunction"
             scope="session"/>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/callcenter/policypage.js?v8"></script>
    <%-- Generic page styles --%>
    <%-- The jQuery UI widget factory, can be omitted if jQuery UI is already included --%>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <%-- The Iframe Transport is required for browsers without support for XHR file uploads --%>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <%-- The basic File Upload plugin --%>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-scrollto.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/imagePre.js"></script>


    <script src="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.js"></script>
    <script>
        $('[data-magnify]').magnify({
            fixedContent: false,
            initMaximized: false,
            fixedModalPos: true
        });
    </script>
</head>
<body>

<form name="frmAllImages" id="frmAllImages">
    <input type="hidden" value="${selectedImagesCount}" id="selectedImagesCount"/>
    <div class="">
        <div class="container-fluid">
            <div class="row">
                <fieldset class="col-md-12 border mt-2">
                    <div class="row">
                        <div class="col-sm-12 col-form-label">
                            <h6 class="float-left">Vehicle Image</h6>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" id="selectedImages" name="selectedImages"/>
                            <input type="hidden" id="claimNo" value="${CLAIM_NO}"/>
                            <hr class="my-2">
                            <c:set var="cnt" value="1"/>
                            <c:forEach var="claimImageFormDto" items="${claimImageFormList}">
                                <c:forEach var="claimImageDto" items="${claimImageFormDto.claimImageDtoList}">

                                    <div class="uploadfile-delet imgupload">
                                        <figure>
                                            <figcaption>${cnt}</figcaption>
                                            <c:set var="cnt" value="${cnt=cnt+1}"/>
                                        </figure>
                                        <a data-magnify="gallery" data-caption="Vehicle Photo"
                                           href="${pageContext.request.contextPath}/ImageViewController?refNo=${claimImageDto.refNo}"
                                           data-toggle="tooltip" title="" class="preview float-none">
                                            <img class="magnify-thumb" id="vehicleImage${claimImageDto.refNo}"
                                                 src="${pageContext.request.contextPath}/ImageThumbViewController?refNo=${claimImageDto.refNo}"
                                                 alt="" width="100%" height="95" alt="" border="0">


                                        </a>
                                        <div class="row text-center">
                                            <div class="col">
                                                <input type="hidden" id="refNo" value="${claimImageDto.refNo}">

                                                <label class="custom-control custom-checkbox float-left col-form-label check-container"
                                                       style="right: 8%; bottom: auto;">
                                                    <input id="${claimImageDto.refNo}" name="selectCheckbox"
                                                           onclick="setSelectedImages('${claimImageDto.refNo}')"
                                                           type="checkbox"
                                                           class="align-middle"/>
                                                    <span class="checkmark align-checkbox-center"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </c:forEach>
                            </c:forEach>
                        </div>
                        <div class="col-md-12 p-2">
                            <button type="button" id="saveBtn" class="btn btn-info ml-2" style="float: right;"
                                    onclick="selectedInvestigationImages()">Save
                            </button>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </div>
</form>


<script>
    function setSelectedImages(refId) {
        var images = $('#selectedImages').val();
        if (images == '') {
            images = refId;
        } else {
            images = images + ',' + refId;
        }

        $('#selectedImages').val(images);
    }


    function selectedInvestigationImages() {

        var selectedImageCount = parseInt($('#selectedImagesCount').val());
        var selectedIds = "";
        var elements = document.getElementsByName('selectCheckbox')
        for (var i = 0; i < elements.length; i++) {
            var id = elements[i].getAttribute('id');

            if (document.getElementById(id).checked) {
                var val = id;
                selectedIds += val + ",";
                selectedImageCount = ++selectedImageCount;
            }
        }
        $('#selectedImages').val(selectedIds);
        if (selectedIds != '') {
            if (selectedImageCount < 7) {
                document.getElementById("saveBtn").disabled = true;
                save()
            } else {
                notify('Please Select Only 6 Images', "danger");
            }
        } else {
            notify('Please Select One', "danger");
        }
    }


    function save() {
        var dataObj = {
            selectedImages: $("#selectedImages").val(),
            claimNo: $("#claimNo").val()
        };

        var URL = "${pageContext.request.contextPath}/ClaimHandlerController/investigationSelectedImages";
        $.ajax({
            url: URL,
            type: 'POST',
            data: dataObj,
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Successfully saved";
                    notify(message, "success");
                    $('#saveBtn').hide();
                } else {
                    message = "Can not be saved";
                    notify(message, "danger");
                }
            }
        });

    }
</script>

</body>
</html>

