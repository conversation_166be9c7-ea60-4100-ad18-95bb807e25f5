<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

    String ERROR = "";
    String str_v_status_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_status_para ", "n_ref_id", "v_status_desc", "v_type IN(0,1)", "");
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/ScrollTabla.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/datatables.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/datatables.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/dataTables.fixedHeader.min.js"></script>

    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        var type = '${TYPE}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);

            //  $("#txtFromDate").val('');
            //  $("#txtToDate").val('');


//            var d1 = document.frmForm.txtFromDate.value;
//            $("#txtFromDate").datepicker({
//                changeMonth: true,
//                changeYear: true,
//                yearRange: '1940:2099',
//                minDate: '-70y',
//                maxDate: '0d',
//                onClose: function (dateText, inst) {
//                    document.getElementById("txtToDate").focus();
//                }
//
//            });
//            $("#txtFromDate").datepicker('option', {dateFormat: "yy-mm-dd"});
//            document.frmForm.txtFromDate.value = d1;

        });
        /*$(function () {
         //
         //buttonImage: '/image/common/calendar.gif',
         var d1 = document.frmForm.txtToDate.value;
         $("#txtToDate").datepicker({
         changeMonth: true,
         changeYear: true,
         yearRange: '1940:2099',
         minDate: '-70y',
         maxDate: '0d',
         onClose: function (dateText, inst) {
         //                    document.getElementById("txtToDate").focus();
         }

         });
         $("#txtToDate").datepicker('option', {dateFormat: "yy-mm-dd"});
         document.frmForm.txtToDate.value = d1;

         });*/


        //------------Start JQuery Script---------------------


        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }, "Cancel": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }


        //------------End JQuery Script---------------------


        //================Others=========================================================
        function setNextButtonDisable() {

        }

        function init() {
            setNextButtonDisable();
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }


    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="N_TXN_NO" id="N_TXN_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Claim Details List </h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote"><%=ERROR%>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <c:forEach var="listDto" items="${statusList}">
                                                        <option value="${listDto.value}">${listDto.label}</option>
                                                    </c:forEach>

                                                </select>
                                                <script>
                                                    if (type == 2) {
                                                        $("#txtV_status").val("40");
                                                    } else {
                                                        $("#txtV_status").val("0");
                                                    }
                                                </script>
                                            </div>
                                        </div>
                                        <%--<div class="form-group row">--%>
                                        <%--<label for="txtV_status" class="col-sm-4 col-form-label"> Intimation--%>
                                        <%--Type</label>--%>
                                        <%--<div class="col-sm-8">--%>
                                        <%--<select name="txtV_status" id=""--%>
                                        <%--class="form-control form-control-sm">--%>
                                        <%--<option value="All">All</option>--%>
                                        <%--<option value="N">Normal</option>--%>
                                        <%--<option value="NFT">Normal Fast Track</option>--%>
                                        <%--<option value="OS">On Site</option>--%>
                                        <%--<option value="OSFT">On Site Fast Track</option>--%>
                                        <%--</select>--%>
                                        <%--</div>--%>
                                        <%--</div>--%>

                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> File
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFileStatus" id="txtFileStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All">All</option>
                                                    <option value="1" selected>Active & Auto Restored</option>
                                                    <option value="2">Stored</option>
                                                    <option value="3">Auto Restored</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Reference
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle /
                                                Trade Plate Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle / Trade Plate Number">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label">
                                                Location</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id=""
                                                       class="form-control form-control-sm" placeholder="Location">
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Liability
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtLiabilityStatus" id="txtLiabilityStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="All">All</option>
                                                    <option value="P">Pending</option>
                                                    <option value="A">Approved</option>
                                                    <option value="F">Forwarded to Higher Level</option>
                                                    <option value="H">Held</option>
                                                    <option value="R">Rejected</option>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-file text-info">&nbsp;</span>&nbsp;&nbsp; File Store
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="	fa fa-car text-dark">&nbsp;</span>&nbsp;&nbsp; Partial loss
                                </p>
                            </div>

                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-car text-danger">&nbsp;</span>&nbsp;&nbsp; Total Loss
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-times text-warning">&nbsp;</span>&nbsp;&nbsp; Doc Pending
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa-check text-success">&nbsp;</span>&nbsp;&nbsp; Doc Done
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa fa-question-circle text-warning">&nbsp;</span>&nbsp;&nbsp; Investigation Arranged
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="fa fa fa-check-circle text-success">&nbsp;</span>&nbsp;&nbsp; Investigation Done
                                </p>
                            </div>


                        </div>
                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim Result</h6>
                                    <div class="mt-2" style="overflow-x: auto">
                                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                               style="cursor:pointer">
                                            <thead>
                                            <tr>
                                                <th>ref no</th>
                                                <th width="40px">No</th>
                                                <th>Claim No</th>
                                                <th>Vehicle No</th>
                                                <th>Policy No</th>
                                                <th>Claim Status</th>
                                                <th>Accident Date</th>
                                                <th>Liability Assigned User</th>
                                                <th>Liability Assigned Date/Time</th>
                                                <th>Int.Liability Assigned User</th>
                                                <th>Int.Liability Assigned Date/Time</th>
                                                <th>Document Status</th>
                                                <th>Document Checked</th>
                                                <th>Liability Status</th>
                                                <th>Finalize Status</th>
                                                <th class=" text-right">ACR</th>
                                                <th class=" text-right">Present Reserve Amount</th>
                                                <th>Loss Type</th>
                                                <th class="min-mobile"></th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/claimhandlerpanel-datatables.js?v3"></script>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });
</script>
</body>
</html>
