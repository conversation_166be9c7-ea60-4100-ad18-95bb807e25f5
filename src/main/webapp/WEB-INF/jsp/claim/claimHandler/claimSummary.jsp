<%--
  Created by IntelliJ IDEA.
  User: madhushanka
  Date: 12/24/2019
  Time: 1:34 PM
  To change this template use File | Settings | File Templates.
--%>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="card-body">
    <div class="form-group">
        <div class="row">
            <div class="col-sm-6">
                <span>Policy Status :</span>
                <span class="label_Value"
                      id="policyStaus">${DbRecordCommonFunctionBean.getValueIdString("claim_policy_status", "V_STATUS_DESC", "V_STATUS_CODE", claimSummaryDto.policyStatus)}</span>
            </div>
            <div class="col-sm-6">
                <span>Total Approved Acr :</span>
                <span class="label_Value"
                      id="totalAcr">${claimSummaryDto.totalApprovedAcr}</span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="row">
            <div class="col-sm-6">
                <span>Initial Liability Approved User :</span>
                <span class="label_Value"
                      id="initLiabilityUser">${claimSummaryDto.initialLiabilityApprovedUser}</span>
            </div>
            <div class="col-sm-6">
                <span>Initial Liability Status :</span>
                <span class="label_Value"
                      id="initLiabilityStatus">${claimSummaryDto.initialLiabilityStatus =='A'?'Approved':'Pending'}</span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="row">
            <div class="col-sm-6">
                <span>Liability Approved User :</span>
                <span class="label_Value"
                      id="liabilityUser">${claimSummaryDto.liabilityApprovedUser}</span>
            </div>
            <div class="col-sm-6">
                <span>Liability Status :</span>
                <span class="label_Value"
                      id="liabilityStatus">${claimSummaryDto.liabilityStatus =='A'?'Approved':'Pending'}</span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table width="100%" cellpadding="0" cellspacing="1"
                   class="table table-hover table-xs dataTable no-footer dtr-inline">
                <thead>
                <tr>
                    <th scope="col" class="tbl_row_header">Job No</th>
                    <th scope="col" class="tbl_row_header">Inspection Type</th>
                    <th scope="col" class="tbl_row_header">Acr</th>
                    <th scope="col" class="tbl_row_header">Genuineness of the Accident</th>
                    <th scope="col" class="tbl_row_header">Status</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="listDto" items="${claimSummaryDto.inspectionDetailsSummaryDtoList}">
                    <tr>
                        <td>${listDto.jobRefNo}</td>
                        <td>${listDto.inspectionDesc}</td>
                        <td align="left">${listDto.acr}</td>
                        <td>${listDto.consistency}</td>
                        <td>${listDto.statusDesc}</td>

                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>

</div>
