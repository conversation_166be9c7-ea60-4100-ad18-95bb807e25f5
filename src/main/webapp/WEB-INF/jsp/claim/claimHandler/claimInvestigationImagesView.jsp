<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="row">
    <div class="col-12 pdf-thumbnails">
        <c:if test="${investigationSelectedImagesDetail.selectedInvestigationImages=='false'}">
            <div class="col-md-12" style="margin-bottom:28px;">
                <button class="btn btn-info ml-2" id="viewAllImagesBtn"
                        style="float: right;"
                        onclick="viewAllImages()">Browse
                </button>
            </div>
        </c:if>
        <div class="col-md-12">
            <c:set var="cnt" value="1"/>
            <c:forEach var="selectImageDto"
                       items="${investigationSelectedImagesDetail.investigationSelectImageDtoList}">
                <div class="uploadfile-delet imgupload col-md-3">
                    <figure>
                        <figcaption>${cnt}</figcaption>
                        <c:set var="cnt" value="${cnt=cnt+1}"/>
                    </figure>
                    <div class="row text-center">
                        <a data-magnify="gallery" data-caption="Vehicle Photo"
                           href="${pageContext.request.contextPath}/ImageViewController?refNo=${selectImageDto.imageRefNo}"
                           data-toggle="tooltip" title=""
                           class="preview float-none">
                            <img class="magnify-thumb"
                                 id="vehicleImage${selectImageDto.imageRefNo}"
                                 src="${pageContext.request.contextPath}/ImageThumbViewController?refNo=${selectImageDto.imageRefNo}"
                                 alt="" width="100%" height="95" alt="" border="0">
                        </a>
                    </div>
                </div>
            </c:forEach>
        </div>
    </div>
</div>
