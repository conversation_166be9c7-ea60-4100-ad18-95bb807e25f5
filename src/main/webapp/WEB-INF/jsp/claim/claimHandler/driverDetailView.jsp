<%--
  Created by IntelliJ IDEA.
  User: super-user
  Date: 10/3/2019
  Time: 10:20 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="card-body">
    <c:set var="claimsDto" value="${sessionClaimHandlerDto.claimsDto}"/>
    <div class="row">
        <div class="col-md-12">
            <form name="frmDriverDetails" id="frmDriverDetails">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label"></label>
                    <div class="col-sm-8">
                        <input type="hidden" name="P_N_CLIM_NO" id="txtClaimNumber">
                        <div class="row">
                            <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 col-sm-12 pl-3 col-form-label check-container pull-right">
                                <input name="driverDetailNotRelevant"
                                       title="Not Relevant to the Accident"
                                       class="align-middle checkbox_check"
                                       type="checkbox" ${claimsDto.driverDetailNotRelevant=='Y'?'checked':''}
                                       id="notRelevant"
                                       value="N"/>
                                <span class="checkmark"></span>
                                <span class="custom-control-description">Not Relevant to the Accident </span>
                            </label>
                        </div>
                        <script>
                            if (${claimsDto.driverDetailNotRelevant == "Y"} || ${claimsDto.driverDetailSubmit == "Y"}) {
                                $("#notRelevant").prop('disabled', true);
                            }
                            $("#notRelevant").click(function () {
                                if ($(this).is(":checked")) {
                                    $("#notRelevant").val("Y");
                                    $("#saveDriverDetailBtn").show();
                                } else {
                                    $("#notRelevant").val("N");
                                    $("#saveDriverDetailBtn").hide();
                                }
                            });
                        </script>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Driver Status <span
                            class="text-danger font-weight-bold">  *</span> :</label>
                    <div class="col-sm-8 ">
                        <select class="form-control form-control-sm" id="driverStatus"
                                name="driverStatus"
                                onchange="checkValidation()">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_driver_status", "N_ID", "V_DRIVER_STATUS_DESC")}
                        </select>
                    </div>
                    <script>
                        if (${claimsDto.driverStatus != 0} || ${claimsDto.driverDetailNotRelevant == 'Y'}) {
                            $("#driverStatus").val("${claimsDto.driverStatus}").prop('disabled', true);
                        }
                    </script>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Driver's Name :</label>
                    <div class="col-sm-8 input-group">
                        <select class=" form-control form-control-sm" id="driverTitle"
                                name="driverTitle"
                                style="width: 20%;"
                                onchange="checkValidation()">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_salutation", "salutation_id", "salutation_name")}
                        </select>
                        <script>
                            if (${claimsDto.driverTitle != 0} || ${claimsDto.driverDetailNotRelevant == 'Y'}) {
                                $("#driverTitle").val("${claimsDto.driverTitle}").prop('disabled', true);
                            }
                        </script>
                        <input name="driverName" id="driverName"
                               class=" form-control form-control-sm" maxlength="200"
                               title="Driver Name" type="text" style="width:80%;"
                               value="${claimsDto.driverName}"
                               onkeyup="checkValidation()"
                               onclick="checkValidation()"/>
                        <script>
                            if (${claimsDto.driverName != ''} || ${claimsDto.driverDetailNotRelevant == 'Y'}) {
                                $("#driverName").val("${claimsDto.driverName}").prop('disabled', true);
                            }
                        </script>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Relationship To Insured
                        :</label>
                    <div class="col-sm-8 input-group">
                        <select class=" form-control form-control-sm"
                                id="driverReleshipInsured"
                                name="driverReleshipInsurd"
                                style="width: 20%;"
                                onchange="checkValidation()">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_insured_relationship", "n_id", "v_desc")}
                        </select>
                    </div>
                    <script>
                        if (${claimsDto.driverReleshipInsurd != 0} || ${claimsDto.driverDetailNotRelevant == 'Y'}) {
                            $("#driverReleshipInsured").val("${claimsDto.driverReleshipInsurd}").prop('disabled', true);
                        }
                    </script>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Driver's NIC :</label>
                    <div class="col-sm-8">
                        <input name="driverNic" id="driverNic" maxlength="20"
                               class=" form-control form-control-sm"
                               title="Driver National Identity Card Number" type="text"
                               value="${claimsDto.driverNic}"
                               onkeyup="checkValidation()"
                               onclick="checkValidation()"
                               onchange="checkValidation()" autocomplete="off"/>
                        <script>
                            if (${claimsDto.driverNic != ''} || ${claimsDto.driverDetailNotRelevant == 'Y'}) {
                                $("#driverNic").val("${claimsDto.driverNic}").prop('disabled', true);
                            }
                        </script>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-8 offset-sm-4">
                        <div id="accordion4">
                            <div class="card ">
                                <div class="card-header p-0" id="driving_License"
                                     style="background-color: rgba(255, 235, 59, 0.5) !important;">
                                    <h5 class="mb-0">
                                        <a class="btn btn-link" data-toggle="collapse"
                                           data-target="#driving_License2"
                                           aria-expanded="false"
                                           aria-controls="driving_License2">
                                            Driving License
                                        </a>
                                    </h5>
                                </div>
                                <div id="driving_License2" class="collapse"
                                     aria-labelledby="driving_License2"
                                     data-parent="#accordion4">
                                    <div class="card-body p-1">
                                        <div class="row">
                                            <div class="col-12 pdf-thumbnails">
                                                <a href="" class="">
                                                    <c:forEach
                                                            var="drivenLicenseDocument"
                                                            items="${claimsDto.drivenLicenseDocumentList}">
                                                        <c:set var="iconColorCls"
                                                               value=" text-dark "/>
                                                        <c:if test="${drivenLicenseDocument.documentStatus=='A'}">
                                                            <c:set var="iconColorCls"
                                                                   value=" text-success"/>
                                                        </c:if>
                                                        <c:if test="${drivenLicenseDocument.documentStatus=='H'}">
                                                            <c:set var="iconColorCls"
                                                                   value=" text-warning"/>
                                                        </c:if>
                                                        <c:if test="${drivenLicenseDocument.documentStatus=='R'}">
                                                            <c:set var="iconColorCls"
                                                                   value=" text-danger"/>
                                                        </c:if>
                                                        <a onclick="viewDoc('${drivenLicenseDocument.refNo}','${drivenLicenseDocument.refNo}','${PREVIOUS_INSPECTION}','left');">
                                                            <span><i
                                                                    class="fa fa-file-pdf-o fa-2x m-3 ">

                                                            </i></span>
                                                        </a>
                                                        <%--<script type="text/javascript">--%>
                                                        <%--$('.claimView${drivenLicenseDocument.refNo}').popupWindow({--%>
                                                        <%--height: screen.height,--%>
                                                        <%--width: screen.width,--%>
                                                        <%--centerBrowser: 0,--%>
                                                        <%--left: 0,--%>
                                                        <%--resizable: 1,--%>
                                                        <%--centerScreen: 1,--%>
                                                        <%--scrollbars: 1,--%>
                                                        <%--windowName: 'callCenter${drivenLicenseDocument.refNo}'--%>
                                                        <%--});--%>
                                                        <%--</script>--%>
                                                    </c:forEach>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group row mt-2"><label class="col-sm-4 col-form-label">Driver's
                    Licenses Number :</label>
                    <div class="col-sm-8">
                        <input name="dlNo" id="dlNo" maxlength="20"
                               title="Driver License Number" type="text"
                               class=" form-control form-control-sm"
                               value="${claimsDto.dlNo}"
                               onkeyup="checkValidation()"
                               onclick="checkValidation()" autocomplete="off"/>
                    </div>
                    <script>
                        if (${claimsDto.dlNo != ''} || ${claimsDto.driverDetailNotRelevant == 'Y'}) {
                            $("#dlNo").val("${claimsDto.dlNo}").prop('disabled', true);
                        }
                    </script>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Driver's License Type :</label>
                    <div class="col-sm-8 input-group">
                        <select class=" form-control form-control-sm"
                                id="driverLicenceType"
                                name="driverLicenceType"
                                style="width: 20%; ">
                            ${DbRecordCommonFunctionBean.getPopupList("claim_driver_license_type", "n_id", "v_type")}
                        </select>
                    </div>
                    <script>

                        if (${claimsDto.driverReleshipInsurd != 0} || ${claimsDto.driverDetailNotRelevant == 'Y'}) {
                            $("#driverLicenceType").val("${claimsDto.driverLicenceType}").prop('disabled', true);
                        }
                    </script>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Remarks :</label>
                    <div class="col-sm-8">
                        <textarea name="reporterRemark" id="reporterRemark"
                                  class=" form-control form-control-sm"
                                  title="Remarks" cols=""
                                  rows="3" onkeyup="checkValidation()"
                                  onclick="checkValidation()">
                        </textarea>
                    </div>
                    <script>
                        $('#reporterRemark').val('${claimsDto.reporterRemark}');
                        let report = "${claimsDto.reporterRemark}".trimEnd();
                        if (report != '' || ${claimsDto.driverDetailNotRelevant == 'Y' || claimsDto.driverDetailSubmit == 'Y'}) {
                            $("#reporterRemark").val(report).prop('disabled', true);
                        }
                    </script>
                </div>
                <%--<c:if test="${claimHandlerDto.assignUserId == G_USER.accessUserType}">--%>
                <c:if test="${(G_USER.userId == sessionClaimHandlerDto.assignUserId || G_USER.userId == sessionClaimHandlerDto.decisionMakingAssignUserId) && claimsDto.driverDetailSubmit != 'Y'}">
                    <div class="float-right">
                        <button type="button" onclick="saveDriverDetails()" id="saveDriverDetailBtn"
                                class="btn btn-primary">Save Details
                        </button>
                        <script>
                            $("#saveDriverDetailBtn").hide();
                        </script>
                    </div>
                </c:if>

                <%--</c:if>--%>
            </form>
        </div>
    </div>
</div>
