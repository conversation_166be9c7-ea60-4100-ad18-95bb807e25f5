<%--
  Created by IntelliJ IDEA.
  User: Tharuka
  Date: 7/18/2018
  Time: 1:15 PM
  To change this template use File | Settings | File Templates.
--%>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/select.dataTables.min.css" rel="stylesheet"
          type="text/css"/>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <style type="text/css">
        table.dataTable th.select-checkbox::after {
        }

    </style>

    <script>
        function updateFinanceReason(val, voucherNo) {
            $.ajax({
                url: contextPath + "/FinanceVoucherController/updateFinanceReason?financeReasonType=" + val + "&voucherNo=" + voucherNo,
                type: 'POST',
                success: function (result) {
                    var messageType = JSON.parse(result);
                    var message = "";
                    if (messageType == "SUCCESS") {
                        message = "Saved Successfully";
                        notify(message, "success");
                    } else {
                        message = "Save Failed";
                        notify(message, "danger");
                    }
                }
            });
        }
    </script>

</head>
<body class="scroll" onload="hideLoader()">
<div class="container-fluid">
    <form name="frmMain" id="frmMain" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5> Voucher Details List</h5>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> From Date</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" value="${txtFromDate}" name="txtFromDate"
                                       id="txtFromDate">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label"> To Date</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" value="${txtToDate}" name="txtToDate"
                                       id="txtToDate">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group ">
                            <label class="col-sm-12 col-form-label">Voucher Status</label>
                            <div class="col-sm-12">
                                <select class="form-control form-control-sm" id="voucherStatus" name="voucherStatus">
                                    <option value="All">All</option>
                                    <option value="AWAITING FOR VERIFICATION">AWAITING FOR VERIFICATION</option>
                                    <option value="AWAITING FOR APPROVAL">AWAITING FOR APPROVAL</option>
                                    <option value="AWAITING FOR PRINTING">AWAITING FOR PRINTING</option>
                                    <option value="PRINTED">PRINTED</option>
                                </select>
                                <script type="text/javascript">
                                    $('#voucherStatus').val('${voucherStatus}');
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">

<%--                    <div class="col-md-3">--%>
<%--                        <div class="form-group ">--%>
<%--                            <label class="col-sm-12 col-form-label"> Vehicle Number</label>--%>
<%--                            <div class="col-sm-12">--%>
<%--                                <input type="text" class="form-control" value="${vehicleNumber}" name="vehicleNumber"--%>
<%--                                       id="vehicleNumber">--%>
<%--                            </div>--%>
<%--                        </div>--%>
<%--                    </div>--%>
    <div class="col-md-4">
        <div class="form-group ">
            <label class="col-sm-12 col-form-label"> Voucher Number</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" value="${voucherNumber}" name="voucherNumber"
                       id="voucherNumber">
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group ">
            <label class="col-sm-12 col-form-label"> Claim No</label>
            <div class="col-sm-12">
                <input type="text" class="form-control" value="${claimNo}" name="claimNo"
                       id="claimNo">
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group ">
            <button type="button" name="cancel" id="remarkButton" class="btn btn-primary"
                    style="margin-top: 33px!important; transform: translateX(10px)" onclick="search();">
                Search
            </button>
        </div>
    </div>
                </div>
                <hr>
                <div class="mt-2 ">
                    <h6>Voucher Result</h6>
                    <div class="mt-2" style="overflow-x: auto">
                        <table id="approvaltable" class="table table-sm table-hover" cellspacing="0"
                               style="cursor:pointer">
                            <thead>
                            <tr>
                                <th width="40px">#</th>
                                <th width="40px">Claim No</th>
                                <th width="40px">Voucher Number</th>
                                <th>Vehicle No</th>
                                <th>Instrument No</th>
                                <th>Source Ref No</th>
                                <th>Payee Name</th>
                                <th>Voucher Amount</th>
                                <th>Voucher Status</th>
                                <th>Last Update User</th>
                                <th>Voucher Date</th>
                                <th>Payee Type</th>
                                <th>Instrument Type</th>
                                <th>Bank Code</th>
                                <th>Bank Branch Code</th>
                                <th>Bank Account No</th>
                                <th class="min-mobile">Reason</th>
                                <th class="min-mobile"></th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="voucher" varStatus="status" items="${voucherList}">
                                <tr>
                                    <td>${(status.index)+1}</td>
                                    <td>${voucher.claimNo}</td>
                                    <td>${voucher.voucherNo}</td>
                                    <td>${voucher.vehicleRegNo}</td>
                                    <td>${voucher.chequeNumber}</td>
                                    <td>${voucher.sourceRefNo}</td>
                                    <td>${voucher.payeeName}</td>
                                    <td><fmt:formatNumber
                                            value="${voucher.voucherAmount}" pattern="###,##0.00;(###,##0.00)"
                                            type="number"/></td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${voucher.statusDesc=='PRINTED'}">
                                                <div class="p-2 bg-badge-succes clearfix">
                                                        ${voucher.statusDesc}
                                                </div>
                                            </c:when>
                                            <c:when test="${voucher.statusDesc=='AWAITING FOR PRINTING'}">
                                                <div class="p-2 bg-badge-primary clearfix">
                                                        ${voucher.statusDesc}
                                                </div>
                                            </c:when>
                                            <c:when test="${voucher.statusDesc=='AWAITING FOR APPROVAL'}">
                                                <div class="p-2 bg-badge-warning clearfix">
                                                        ${voucher.statusDesc}
                                                </div>
                                            </c:when>
                                            <c:when test="${voucher.statusDesc=='AWAITING FOR VERIFICATION'}">
                                                <div class="p-2 bg-badge-danger clearfix">
                                                        ${voucher.statusDesc}
                                                </div>
                                            </c:when>
                                            <c:otherwise>
                                                <div class="p-2 bg-badge-danger clearfix">
                                                        ${voucher.statusDesc}
                                                </div>
                                            </c:otherwise>
                                        </c:choose>


                                    </td>
                                    <td>${voucher.lastUpdatedUser}</td>
                                    <td>${voucher.voucherDate}</td>
                                    <td>${voucher.payeeType}</td>
                                    <td>${voucher.instrumentType}</td>
                                    <td>${voucher.bankCode}</td>
                                    <td>${voucher.branchCode}</td>
                                    <td>${voucher.bankAccountNo}</td>
                                    <td>
                                        <select id="txtFinanceReasonType${voucher.voucherNo}"
                                                onchange="updateFinanceReason(this.value,'${voucher.voucherNo}')">
                                            <c:forEach var="item" items="${financeReasonList}">
                                                <option value="${item.value}">${item.lable}</option>
                                            </c:forEach>
                                        </select>
                                        <script type="text/javascript">
                                            $("#txtFinanceReasonType${voucher.voucherNo}").val("${voucher.financeReasonTypeId}");
                                        </script>
                                    </td>
                                    <td>
                                        <button class="btn-primary btn" type="button"
                                                onclick="viewClaimDetails('${voucher.voucherNo}')"><i
                                                class="fa fa-eye"></i></button>
                                    </td>
                                </tr>

                            </c:forEach>
                            </tbody>
                            <tfoot>
                            <tr>
                                <th width="40px">#</th>
                                <th width="40px">Claim No</th>
                                <th width="40px">Voucher Number</th>
                                <th>Vehicle No</th>
                                <th>Instrument No</th>
                                <th>Source Ref No</th>
                                <th>Payee Name</th>
                                <th>Voucher Amount</th>
                                <th>Voucher Status</th>
                                <th>Last Update User</th>
                                <th>Voucher Date</th>
                                <th>Payee Type</th>
                                <th>Instrument Type</th>
                                <th>Bank Code</th>
                                <th>Bank Branch Code</th>
                                <th>Bank Account No</th>
                                <th class="min-mobile">Reason</th>
                                <th class="min-mobile"></th>
                            </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <input type="hidden" id="selectedIds" name="selectedIds">


                <c:if test="${successMessage!=null && successMessage!=''}">
                <script type="text/javascript">
                    notify('${successMessage}', "success");
                </script>
                </c:if>
                <c:if test="${errorMessage!=null && errorMessage!=''}">
                <script type="text/javascript">
                    notify('${errorMessage}', "danger");
                </script>
                </c:if>


    </form>
</div>


<script type="text/javascript">


    $(document).ready(function () {
        var example = $('#approvaltable').DataTable({
            paging: true,
            searching: true,
            columnDefs: [{
                orderable: false,
                targets: 0,

            }],
            // select: {
            //     style: 'multi',
            //     selector: 'td:first-child'
            // },
            // order: [
            //     [1, 'asc']
            // ]
        });

    });

    function search() {
        if ($("#voucherStatus").val() != 'All' && $("#txtFromDate").val() == '' && $("#txtToDate").val() == '') {
            notify('Please select date range', "danger");
            return;
        }
        document.frmMain.action = contextPath + "/FinanceVoucherController/viewVoucherDetails";
        document.frmMain.submit();
    }

    $('#txtFromDate').datepicker({dateFormat: 'yy-mm-dd'});
    $('#txtToDate').datepicker({dateFormat: 'yy-mm-dd'});

</script>
</body>
</html>
