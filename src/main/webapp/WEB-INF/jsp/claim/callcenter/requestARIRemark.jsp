<%@taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE HTML>
<!--
/*
* jQuery File Upload Plugin Basic Demo
* https://github.com/blueimp/jQuery-File-Upload
*
* Copyright 2013, <PERSON>
* https://blueimp.net
*
* Licensed under the MIT license:
* https://opensource.org/licenses/MIT
*/
-->
<html lang="en">
<head>
    <!-- Force latest IE rendering engine or ChromeFrame if installed -->
    <!--[if IE]>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
    <meta charset="utf-8">
    <title>Document Upload</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <!-- Generic page styles -->
    <%--<link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/style.css">--%>
    <!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <!-- The basic File Upload plugin -->
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/inspectiondetails/assessor/ari-form-validations.js"></script>
</head>
<body onload="hideLoader()">

<div class="container-fluid">
    <%--<div class="row header-bg">--%>
    <%--<div class="col-sm-12 py-2 bg-dark">--%>
    <%--<h6 class="float-left text-dark hide-for-small"> Inspection Report Details</h6>--%>
    <%--</div>--%>
    <%--</div>--%>

    <div class="row">
        <div class="offset-md-3 col-md-6">
            <form id="frmMain" name="frmMain" method="post">
                <input type="hidden" id="requestAriId" name="requestAriId" value="${N_ARI_ID}" >
                <input type="hidden" id="TYPE" name="TYPE" value="${TYPE}" >
                <fieldset class="border p-2 mt-3">
                    <h6>Remarks</h6>
                    <hr class="my-2">
                    <div class="col-md-12">
                        <%--<ul class="nav nav-tabs" id="myTab" role="tablist">--%>
                            <%--<li class="nav-item item1">--%>
                                <%--<a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Remark</a>--%>
                            <%--</li>--%>
                            <%--<li class="nav-item item2">--%>
                                <%--<a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Remark List</a>--%>
                            <%--</li>--%>
                        <%--</ul>--%>
                        <%--<div class="tab-content" id="myTabContent">--%>
                            <%--<ul class="nav  nav-tabs mt-3" role="tablist">--%>
                                <%--<div class="tab-pane fade active" id="home" role="tabpanel" aria-labelledby="home-tab">--%>
                                    <%--<div class="form-group row mt-4">--%>
                                        <%--<label class="col-sm-2 col-form-label">Remark</label>--%>
                                        <%--<div class="col-sm-10">--%>
                                            <%--<div class="row">--%>
                                                <%--<textarea rows="4" cols="50" style="width: 100%;" id="remarkText"></textarea>--%>
                                            <%--</div>--%>
                                        <%--</div>--%>
                                    <%--</div>--%>
                                    <%--<div class="row">--%>
                                        <%--<div class="col text-right p-0">--%>
                                            <%--<button type="button" class="btn btn-primary" id="btnSubmit" onclick="saveRemark()">Submit</button>--%>
                                        <%--</div>--%>
                                    <%--</div>--%>
                                <%--</div >--%>
                                <%--<div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab" >--%>


                                <%--</div>--%>




                                <%--&lt;%&ndash;<div class="form-group row">&ndash;%&gt;--%>
                                <%--&lt;%&ndash;<label class="col-sm-4 col-form-label">Contact Number :</label>&ndash;%&gt;--%>
                                <%--&lt;%&ndash;<div class="col-sm-8">&ndash;%&gt;--%>
                                <%--&lt;%&ndash;<div class="row">&ndash;%&gt;--%>
                                <%--&lt;%&ndash;<input type="text" class="form-control form-control-sm" name="contactNo"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;id="contactNo">&ndash;%&gt;--%>
                                <%--&lt;%&ndash;</div>&ndash;%&gt;--%>
                                <%--&lt;%&ndash;</div>&ndash;%&gt;--%>
                                <%--&lt;%&ndash;</div>&ndash;%&gt;--%>

                        <%--</div>--%>

                            <ul class="nav nav-tabs" id="myTab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Add Remark</a>
                                </li>
                                <li class="nav-item data1">
                                    <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Remark List</a>
                                </li>

                            </ul>
                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                                    <div class="form-group row mt-4">
                                        <label class="col-sm-2 col-form-label">Remark</label>
                                        <div class="col-sm-10">
                                            <div class="row">
                                                <textarea rows="4" cols="50" style="width: 100%;"
                                                          id="remarkText"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col text-right p-0">
                                            <c:if test="${G_USER.accessUserType ne 105}">
                                                <button type="button" class="btn btn-primary" id="btnSubmit"
                                                        onclick="saveRemark()">Submit
                                                </button>
                                            </c:if>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="profile" role="tabpanel"
                                     aria-labelledby="profile-tab"></div>

                            </div>
                            <hr>
                            <div class="row mt-4">
                                <div class="col-lg-12 text-right p-0">
                                    <button type="button" class="btn " onclick="closePage()">Close</button>
                                </div>
                            </div>
                </fieldset>



            </form>
            <c:if test="${successMessage!=null && successMessage!=''}">
                <script type="text/javascript">
                    window.parent.notify('${successMessage}', "success");
                </script>
            </c:if>
            <c:if test="${errorMessage!=null && errorMessage!=''}">
                <script type="text/javascript">
                    window.parent.notify('${errorMessage}', "danger");
                </script>
            </c:if>


        </div>
    </div>

</div>
</body>
<script type="text/javascript">

    function saveRemark() {
        var requestId = '${N_ARI_ID}';
        var remark = $('#remarkText').val();

        $.ajax({
            url: contextPath + "/RequestAriController/saveRemark?requestId=" + requestId + "&remark=" + remark,
            type: 'POST',
            success: function (result) {
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType == "SUCCESS") {
                    message = "Saved Successfully";
                    notify(message, "success");
                    $('#remarkText').val('');
                } else {
                    message = "Saved Successfully";
                    notify(message, "danger");
                }
            }
        });
    }

    function requestAriDiv() {
        $("#profile").load("${pageContext.request.contextPath}/RequestAriController/requestSpecialRemark?N_ARI_ID=${N_ARI_ID}");
    }

    $(".data1 a").click(function (e) {
        requestAriDiv();
    });



    $('#myTab a').on('click', function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    function closePage() {
        document.frmMain.action = contextPath + "/RequestAriController/ariListView";
        document.frmMain.submit();
    }


    <%--function pageSubmit() {--%>

    <%--document.frmMain.action = "${pageContext.request.contextPath}/InspectionDetailsController/saveAri";--%>
    <%--document.frmMain.submit();--%>

    <%--}--%>
</script>
</html>
