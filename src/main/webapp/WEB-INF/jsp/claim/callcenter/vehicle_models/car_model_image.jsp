<%--
    Document   : car_model_image
    Created on : Oct 9, 2011, 2:23:29 PM
    Author     : <PERSON><PERSON>
--%>
<%@page import="com.misyn.mcms.claim.dto.DamageBodyPartDto" %>
<%@page import="com.misyn.mcms.claim.service.CallCenterService" %>
<%@page import="com.misyn.mcms.utility.AppConstant" %>
<%@ page import="java.util.List" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>


    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/vehicle-model/jquery.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/vehicle-model/jquery.maphilight.js.pagespeed.ce.MH6j2FpPiZ.js"></script>
    <script>
        /*  $(function() {
          $('.map').maphilight({
              fillColor: '008800'
          });
          $('#part1').click(function(e) {
              e.preventDefault();
              var data = $('#part1').mouseout().data('maphilight') || {};
              data.alwaysOn = !data.alwaysOn;
              alert(data.alwaysOn);
              $('#part1').data('maphilight', data).trigger('alwaysOn.maphilight');
          });

      });*/

        function setImageMap(map_id) {
            $(function () {
                $('.map').maphilight({
                    fillColor: '008800'
                });
                // $('#part1').click(function(e) {
                // e.preventDefault();
                var data = $('#part' + map_id).mouseout().data('maphilight') || {};
                data.alwaysOn = !data.alwaysOn;
                parent.toggleGroup(map_id, data.alwaysOn);
                toggleGroup(map_id, data.alwaysOn);
                $('#part' + map_id).data('maphilight', data).trigger('alwaysOn.maphilight');
                // });

            });
        }

        function setSelectedImage() {


            var listData = parent.getDamageBodyPartsList();
            var listCheckRedio = parent.getCheckRedioList();
            //alert(listCheckRedio);
            var listSplitDataResult = listData.split(",");
            var listSplitCheckRedioResult = listCheckRedio.split(",");

            for (i = 0; i < listSplitDataResult.length - 1; i++) {
                setImageMap(listSplitDataResult[i]);
                initRedioButton(listSplitDataResult[i], listSplitCheckRedioResult[i]);
                setTextOwnTextBox(listSplitDataResult[i]);
                //alert("<br /> Element " + i + " = " + listSplitCheckRedioResult[i]);
            }

            parent.parent.document.getElementById("cell1").style.display = "none";
            parent.parent.document.getElementById("loading").style.display = "none";
        }

        function toggleGroup(id, val) {
            // alert(val);
            if (!val) {
                document.getElementById("rdoDamageParts1" + id).style.display = "none";
                document.getElementById("rdoDamageParts1" + id).checked = false;

                document.getElementById("rdoDamageParts2" + id).style.display = "none";
                document.getElementById("rdoDamageParts2" + id).checked = false;

                document.getElementById("rdoDamageParts3" + id).style.display = "none";
                document.getElementById("rdoDamageParts3" + id).checked = false;

                document.getElementById("rdoDamageParts4" + id).style.display = "none";
                document.getElementById("rdoDamageParts4" + id).checked = false;

                document.getElementById("rdoDamageParts5" + id).style.display = "none";
                document.getElementById("rdoDamageParts5" + id).checked = false;

                document.getElementById("txtOther5" + id).style.display = "none";
                document.getElementById("txtOther5" + id).value = "";

                document.getElementById("div_head" + id).style.backgroundColor = "#F2F2F2";
                document.getElementById("div_sub" + id).style.backgroundColor = "#F9F9F9";

                document.getElementById("chkDamageParts" + id).checked = false;

            }
            else {
                document.getElementById("rdoDamageParts1" + id).style.display = "block";
                document.getElementById("rdoDamageParts2" + id).style.display = "block";
                document.getElementById("rdoDamageParts3" + id).style.display = "block";
                document.getElementById("rdoDamageParts4" + id).style.display = "block";
                document.getElementById("rdoDamageParts5" + id).style.display = "block";
                document.getElementById("txtOther5" + id).style.display = "block";

                document.getElementById("div_head" + id).style.backgroundColor = "#015aaa";
                document.getElementById("div_head" + id).style.color = "#FFF";
                document.getElementById("div_sub" + id).style.backgroundColor = "#015aaa";
                document.getElementById("div_sub" + id).style.color = "#FFF";
                document.getElementById("chkDamageParts" + id).checked = true;
            }
        }

        function clickRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                parent.document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                parent.document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                parent.document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                parent.document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                parent.document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

        function initRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

        function setTextParentTextBox(id) {
            parent.document.getElementById("txtOther5" + id).value = document.getElementById("txtOther5" + id).value;
        }

        function setTextOwnTextBox(id) {
            document.getElementById("txtOther5" + id).value = parent.document.getElementById("txtOther5" + id).value;
        }

    </script>
    <title>Car Model</title>
</head>
<body onLoad="setSelectedImage()">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" action="" method="post">
        <div class="row">
            <div class="col-md-6 test">
                <img src="${pageContext.request.contextPath}/image/vehicle_images/car.png"  border="0" alt="Car" class="map"
                     usemap="#features">
                <map name="features">
                    <area id="part1" shape="rect" coords="103,5,143,45" href="#" alt="HEAD LAMP" title="RIGT HEAD LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(1)">

                    <area id="part2" shape="rect" coords="154,5,194,45" href="#" alt="RIGHT SIDE MIRROR"
                          title="RIGHT SIDE MIRROR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(2)">

                    <area id="part3" shape="rect" coords="201,5,241,45" href="#" alt="RIGHT FOG LAMP"
                          title="RIGHT FOG LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(3)">

                    <area id="part4" shape="rect" coords="249,5,289,45" href="#" alt="FRONT GRILL" title="FRONT GRILL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(4)">

                    <area id="part5" shape="rect" coords="297,5,337,45" href="#" alt="LEFT HEAD LAMP"
                          title="LEFT HEAD LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(5)">

                    <area id="part6" shape="rect" coords="344,5,384,45" href="#" alt="LEFT SIDE MIRROR"
                          title="LEFT SIDE MIRROR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(6)">

                    <area id="part7" shape="rect" coords="393,5,433,45" href="#" alt="LEFT FOG LAMP"
                          title="LEFT FOG LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(7)">

                    <area id="part8" shape="rect" coords="440,4,480,44" href="#" alt="LEFT TAIL LAMP"
                          title="LEFT TAIL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(8)">

                    <area id="part9" shape="rect" coords="489,4,529,44" href="#" alt="DICKY LID" title="DICKY LID"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(9)">

                    <area id="part10" shape="rect" coords="551,6,591,46" href="#" alt="RIGHT TAIL LAMP"
                          title="RIGHT TAIL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(10)">

                    <area id="part11" shape="rect" coords="13,90,53,130" href="#" alt="LEFT FRONT DOOR GLASS"
                          title="LEFT FRONT DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(11)">

                    <area id="part12" shape="rect" coords="13,136,53,176" href="#" alt="LEFT FRONT DOOR"
                          title="LEFT FRONT DOORP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(12)">

                    <area id="part13" shape="rect" coords="13,181,53,221" href="#" alt="LEFT FRONT FENDER"
                          title="LEFT FRONT FENDER"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(13)">

                    <area id="part14" shape="rect" coords="14,228,54,268" href="#" alt="FRONT BUFFER"
                          title="FRONT BUFFER"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(14)">

                    <area id="part15" shape="rect" coords="14,272,54,312" href="#" alt="LEFT FRONT WHELL"
                          title="LEFT FRONT WHELL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(15)">

                    <area id="part16" shape="rect" coords="13,316,53,356" href="#" alt="RIGHT REAR QUARTER GLASS"
                          title="RIGHT REAR QUARTER GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(16)">

                    <area id="part17" shape="rect" coords="13,359,53,399" href="#" alt="RIGHT QUARTER PANLE"
                          title="RIGHT QUARTER PANLE"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(17)">

                    <area id="part18" shape="rect" coords="13,403,53,443" href="#" alt="RIGHT REAR DOOR GLASS"
                          title="RIGHT REAR DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(18)">

                    <area id="part19" shape="rect" coords="13,446,53,486" href="#" alt="RIGHT REAR DOOR"
                          title="RIGHT REAR DOOR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(19)">

                    <area id="part20" shape="rect" coords="14,489,54,529" href="#" alt="RIGHT REAR WHEEL"
                          title="RIGHT REAR WHEELP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(20)">

                    <area id="part21" shape="rect" coords="68,504,108,544" href="#" alt="REAR WINDSCREEN GLASS"
                          title="REAR WINDSCREEN GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(21)">

                    <area id="part22" shape="rect" coords="552,50,592,90" href="#" alt="LEFT REAR DOOR GLASS"
                          title="LEFT REAR DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(22)">

                    <area id="part23" shape="rect" coords="553,94,593,134" href="#" alt="LEFT REAR QUARTER GLASS	"
                          title="LEFT REAR QUARTER GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(23)">

                    <area id="part24" shape="rect" coords="554,140,594,180" href="#" alt="LEFT REAR DOOR"
                          title="LEFT REAR DOOR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(24)">

                    <area id="part25" shape="rect" coords="555,184,595,224" href="#" alt="LEFT QUARTER PANLE"
                          title="LEFT QUARTER PANLE"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(25)">

                    <area id="part26" shape="rect" coords="556,227,596,267" href="#" alt="REAR BUFFER"
                          title="REAR BUFFER"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(26)">

                    <area id="part27" shape="rect" coords="556,270,596,310" href="#" alt="LEFT REAR WHEEL"
                          title="LEFT REAR WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(27)">

                    <area id="part28" shape="rect" coords="557,312,597,352" href="#" alt="LEFT VALANCE BAR"
                          title="LEFT VALANCE BAR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(28)">

                    <area id="part29" shape="rect" coords="558,355,598,395" href="#" alt="RIGHT FRONT FENDER"
                          title="RIGHT FRONT FENDER"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(29)">

                    <area id="part30" shape="rect" coords="558,397,598,437" href="#" alt="RIGHT FRONT DOOR GLASS"
                          title="RIGHT FRONT DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(30)">

                    <area id="part31" shape="rect" coords="558,441,598,481" href="#" alt="RIGHT FRONT WHEEL"
                          title="RIGHT FRONT WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(31)">

                    <area id="part32" shape="rect" coords="557,486,597,526" href="#" alt="RIGHT FRONT DOORS"
                          title="RIGHT FRONT DOOR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(32)">

                    <area id="part33" shape="rect" coords="222,508,262,548" href="#" alt="ROOF PANLE (HOOD)"
                          title="ROOF PANLE (HOOD)"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(33)">

                    <area id="part34" shape="rect" coords="316,508,356,548" href="#" alt="FRONT WINDSCREEN GLASS"
                          title="FRONT WINDSCREEN GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(34)">

                    <area id="part35" shape="rect" coords="405,508,445,548" href="#" alt="BONNET" title="BONNET"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(35)">

                    <area id="part36" shape="rect" coords="514,507,554,547" href="#" alt="RIGHT VALANCE BAR"
                          title="RIGHT VALANCE BAR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(36)">
                </map>
            </div>
            <div class="col-md-6">
                <div>
                    <%
                        CallCenterService callCenterService = (CallCenterService) session.getAttribute(AppConstant.CALL_CENTER_SERVICE);
                        List<DamageBodyPartDto> list = callCenterService.getDamageBodyPartDtoList(0, 1);

                        String TEXTBOX_DISABLED = "";
                        int cnt = 0;
                        int size = list.size();
                        String checked = "checked=\"checked\"";
                        String display = "display: none;";
                        String color1 = "#F2F2F2";
                        String color2 = "#F9F9F9";
                        for (DamageBodyPartDto damageBodyPart : list) {
                            if (damageBodyPart.isRecFoundMainTable()) {
                                checked = "checked=\"checked\"";
                                color1 = "#015aaa";
                                color2 = "#015aaa";
                                display = "display: block;";
                            } else {
                                checked = "";
                                color1 = "#F2F2F2";
                                color2 = "#F9F9F9";
                                display = "display: none;";
                            }
                            cnt = damageBodyPart.getTxnId();

                    %>

                    <div>
                        <div class="row p-2" id="div_head<%=cnt%>" style="height:35px;background-color:<%=color1%>;">
                            <div class="col-1">
                                <input type="hidden" name="txtN_txn_id<%=cnt%>" id="txtN_txn_id<%=cnt%>"
                                       value="<%=cnt%>"/>
                                <input name="chkDamageParts<%=cnt%>" id="chkDamageParts<%=cnt%>"
                                       type="checkbox" <%=checked%>
                                       value="Y" onClick="setImageMap(<%=cnt%>);" <%=TEXTBOX_DISABLED%> />
                                <input type="hidden" name="txtDamageParts<%=cnt%>" id="txtDamageParts<%=cnt%>"
                                       value="<%=damageBodyPart.getPartCode() %>"/>
                            </div>
                            <div class="col-5" style="font-weight:bold;font-family:Arial;font-size:11px;">
                                (<%=cnt%>) <%=damageBodyPart.getPartName()%>  (<%=damageBodyPart.getPartCode()%>)
                            </div>
                            <div class="col-5" style="font-weight:bold;font-family:Arial;font-size:11px;height:20px;">
                                <input style="width: 100%" name="txtOther<%=cnt%>" id="txtOther5<%=cnt%>" type="text"
                                       value="<%=damageBodyPart.getOtherText()%>"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>
                                       onkeyup="setTextParentTextBox(<%=cnt%>)"/></div>
                        </div>


                        <div class="row p-2" id="div_sub<%=cnt%>" style="height:30px;background-color:<%=color2%>;">
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts1<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Broken") ? "checked=\"checked\"" : "" %>
                                       value="Broken" onClick="clickRedioButton(<%=cnt%>,1)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Broken
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts2<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Dented") ? "checked=\"checked\"" : "" %>
                                       value="Dented" onClick="clickRedioButton(<%=cnt%>,2)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Dented
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts3<%=cnt%>"
                                       type="radio"  <%=damageBodyPart.getDamegType().equalsIgnoreCase("Scraped") ? "checked=\"checked\"" : "" %>
                                       value="Scraped" onClick="clickRedioButton(<%=cnt%>,3)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Scraped
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts4<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Come out") ? "checked=\"checked\"" : "" %>
                                       value="Come out" onClick="clickRedioButton(<%=cnt%>,4)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-2 pl-0" style="font-size:11px;font-family:Arial;">
                                Come
                                out
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts5<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Other") ? "checked=\"checked\"" : "" %>
                                       value="Other" onClick="clickRedioButton(<%=cnt%>,5)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Other
                            </div>
                        </div>
                    </div>


                    <%
                        }
                        out.print("<input type=\"hidden\" name=\"txtDamagePartCount\" id=\"txtDamagePartCount\" value=\"" + size + "\" />");
                    %>
                </div>

            </div>
        </div>


    </form>

</div>
</body>
</html>
