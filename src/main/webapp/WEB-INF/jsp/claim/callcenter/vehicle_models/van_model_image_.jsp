<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>jQuery maphilight documentation</title>
    <script type="text/javascript" src="jquery.min.js"></script>
    <script type="text/javascript" src="jquery.maphilight.js.pagespeed.ce.MH6j2FpPiZ.js"></script>

    <script>
        /*  $(function() {
          $('.map').maphilight({
              fillColor: '008800'
          });
          $('#part1').click(function(e) {
              e.preventDefault();
              var data = $('#part1').mouseout().data('maphilight') || {};
              data.alwaysOn = !data.alwaysOn;
              alert(data.alwaysOn);
              $('#part1').data('maphilight', data).trigger('alwaysOn.maphilight');
          });

      });*/

        function setImageMap(map_id) {
            $(function () {
                $('.map').maphilight({
                    fillColor: 'FC0'
                });
                // $('#part1').click(function(e) {
                // e.preventDefault();
                var data = $('#part' + map_id).mouseout().data('maphilight') || {};
                data.alwaysOn = !data.alwaysOn;
                //alert(data.alwaysOn);
                $('#part' + map_id).data('maphilight', data).trigger('alwaysOn.maphilight');
                // });

            });
        }

    </script>
</head>
<body onLoad="setImageMap(1);">


<h1 style="color:#0C0">Assorted features</h1>
<p>This is an assortment of feature-demonstrations.</p>
<p>
    <img src="../../../image/vehicle_images/car.png" width="619" height="550" alt="Van" class="map" usemap="#features">

    <map name="features">
        <area id="part1" shape="rect" coords="103,5,143,45" href="#" alt="HEAD LAMP" title="RIGT HEAD LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(1)">

        <area id="part2" shape="rect" coords="154,6,194,46" href="#" alt="RIGHT SIDE MIRROR" title="RIGHT SIDE MIRROR"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(2)">

        <area id="part3" shape="rect" coords="201,6,241,46" href="#" alt="RIGHT FOG LAMP" title="RIGHT FOG LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(3)">

        <area id="part4" shape="rect" coords="249,6,289,46" href="#" alt="FRONT GRILL" title="FRONT GRILL"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(4)">

        <area id="part5" shape="rect" coords="297,6,337,46" href="#" alt="LEFT HEAD LAMP" title="LEFT HEAD LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(5)">

        <area id="part6" shape="rect" coords="344,7,384,47" href="#" alt="LEFT SIDE MIRROR" title="LEFT SIDE MIRROR"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(6)">

        <area id="part7" shape="rect" coords="389,6,429,46" href="#" alt="LEFT FOG LAMP" title="LEFT FOG LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(7)">

        <area id="part8" shape="rect" coords="436,6,476,46" href="#" alt="LEFT TAIL LAMP" title="LEFT TAIL LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(8)">

        <area id="part9" shape="rect" coords="483,5,523,45" href="#" alt="DICKY LID" title="DICKY LID"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(9)">

        <area id="part10" shape="rect" coords="540,6,580,46" href="#" alt="RIGHT TAIL LAMP" title="RIGHT TAIL LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(10)">

        <area id="part11" shape="rect" coords="13,90,53,130" href="#" alt="LEFT FRONT DOOR GLASS"
              title="LEFT FRONT DOOR GLASS"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(11)">

        <area id="part12" shape="rect" coords="13,136,53,176" href="#" alt="LEFT FRONT DOOR" title="LEFT FRONT DOORP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(12)">

        <area id="part13" shape="rect" coords="13,181,53,221" href="#" alt="LEFT FRONT FENDER" title="LEFT FRONT FENDER"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(13)">

        <area id="part14" shape="rect" coords="14,228,54,268" href="#" alt="FRONT BUFFER" title="FRONT BUFFER"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(14)">

        <area id="part15" shape="rect" coords="14,272,54,312" href="#" alt="LEFT FRONT WHELL" title="LEFT FRONT WHELL"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(15)">

        <area id="part16" shape="rect" coords="13,316,53,356" href="#" alt="RIGHT REAR QUARTER GLASS"
              title="RIGHT REAR QUARTER GLASS"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(16)">

        <area id="part17" shape="rect" coords="13,359,53,399" href="#" alt="RIGHT QUARTER PANLE"
              title="RIGHT QUARTER PANLE"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(17)">

        <area id="part18" shape="rect" coords="13,403,53,443" href="#" alt="RIGHT REAR DOOR GLASS"
              title="RIGHT REAR DOOR GLASS"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(18)">

        <area id="part19" shape="rect" coords="13,446,53,486" href="#" alt="RIGHT REAR DOOR" title="RIGHT REAR DOOR"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(19)">

        <area id="part20" shape="rect" coords="14,488,54,528" href="#" alt="RIGHT REAR WHEEL" title="RIGHT REAR WHEELP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(20)">


        <area id="shadowed" shape="rect" coords="515,506,555,546" href="#" alt="Shadow for some"
              data-maphilight='{"fillColor":"00ff00","shadow":true,"shadowBackground":"ffffff","alwaysOn":true}'>
    </map>
</p>
<p>Features:</p>
<ul>
    <li>The star uses the metadata plugin to adjust how it looks</li>
    <li>The right-hand square uses the metadata plugin to always be visible, to have no border, and to have a greenish
        fill
    </li>
    <li>The lower-left square is linked to the star using groupBy. When you mouse over it the star will hilight as
        well.
    </li>
    <li>The lower-right square has a shadow.</li>
    <li><a id="hilightlink" href="#">Mouse over this to trigger a hilight from an external element!</a></li>
    <li><a id="starlink" href="#">Click this to toggle whether or not the star will hilight</a></li>
    <li><a id="starlink2" href="#">Click this to toggle whether or not the star is always hilighted; or click the star
        itself</a></li>
</ul>

</body>
</html>