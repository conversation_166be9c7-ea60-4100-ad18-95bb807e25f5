<%--
    Document   : car_model_image
    Created on : Oct 9, 2011, 2:23:29 PM
    Author     : <PERSON><PERSON>
--%>
<%@page import="com.misyn.mcms.claim.dto.DamageBodyPart" %>
<%@page import="com.misyn.mcms.claim.dao.PolicyClaimManager" %>
<%@page import="java.util.List" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="jquery.min.js"></script>
    <script type="text/javascript" src="jquery.maphilight.js.pagespeed.ce.MH6j2FpPiZ.js"></script>
    <script>
        /*  $(function() {
          $('.map').maphilight({
              fillColor: '008800'
          });
          $('#part1').click(function(e) {
              e.preventDefault();
              var data = $('#part1').mouseout().data('maphilight') || {};
              data.alwaysOn = !data.alwaysOn;
              alert(data.alwaysOn);
              $('#part1').data('maphilight', data).trigger('alwaysOn.maphilight');
          });

      });*/

        function setImageMap(map_id) {
            $(function () {
                $('.map').maphilight({
                    fillColor: '008800'
                });
                // $('#part1').click(function(e) {
                // e.preventDefault();
                var data = $('#part' + map_id).mouseout().data('maphilight') || {};
                data.alwaysOn = !data.alwaysOn;
                parent.toggleGroup(map_id, data.alwaysOn);
                toggleGroup(map_id, data.alwaysOn);
                $('#part' + map_id).data('maphilight', data).trigger('alwaysOn.maphilight');
                // });

            });
        }

        function setSelectedImage() {


            var listData = parent.getDamageBodyPartsList();
            var listCheckRedio = parent.getCheckRedioList();
            //alert(listCheckRedio);
            var listSplitDataResult = listData.split(",");
            var listSplitCheckRedioResult = listCheckRedio.split(",");

            for (i = 0; i < listSplitDataResult.length - 1; i++) {
                setImageMap(listSplitDataResult[i]);
                initRedioButton(listSplitDataResult[i], listSplitCheckRedioResult[i]);
                //alert("<br /> Element " + i + " = " + listSplitCheckRedioResult[i]);
            }

            parent.parent.document.getElementById("cell1").style.display = "none";
            parent.parent.document.getElementById("loading").style.display = "none";
        }

        function toggleGroup(id, val) {
            // alert(val);
            if (!val) {
                document.getElementById("rdoDamageParts1" + id).style.display = "none";
                document.getElementById("rdoDamageParts1" + id).checked = false;

                document.getElementById("rdoDamageParts2" + id).style.display = "none";
                document.getElementById("rdoDamageParts2" + id).checked = false;

                document.getElementById("rdoDamageParts3" + id).style.display = "none";
                document.getElementById("rdoDamageParts3" + id).checked = false;

                document.getElementById("rdoDamageParts4" + id).style.display = "none";
                document.getElementById("rdoDamageParts4" + id).checked = false;

                document.getElementById("rdoDamageParts5" + id).style.display = "none";
                document.getElementById("rdoDamageParts5" + id).checked = false;

                document.getElementById("txtOther5" + id).style.display = "none";
                document.getElementById("txtOther5" + id).value = "";

                document.getElementById("div_head" + id).style.backgroundColor = "#F2F2F2";
                document.getElementById("div_sub" + id).style.backgroundColor = "#F9F9F9";

                document.getElementById("chkDamageParts" + id).checked = false;

            }
            else {
                document.getElementById("rdoDamageParts1" + id).style.display = "block";
                document.getElementById("rdoDamageParts2" + id).style.display = "block";
                document.getElementById("rdoDamageParts3" + id).style.display = "block";
                document.getElementById("rdoDamageParts4" + id).style.display = "block";
                document.getElementById("rdoDamageParts5" + id).style.display = "block";
                document.getElementById("txtOther5" + id).style.display = "block";

                document.getElementById("div_head" + id).style.backgroundColor = "#F90";
                document.getElementById("div_sub" + id).style.backgroundColor = "#F90";
                document.getElementById("chkDamageParts" + id).checked = true;
            }
        }

        function clickRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                parent.document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                parent.document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                parent.document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                parent.document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                parent.document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

        function initRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

    </script>
    <title>JSP Page</title>
</head>
<body onLoad="setSelectedImage()">
<form name="frmForm" id="frmForm" action="" method="post">
    <img src="../../../image/vehicle_images/motorbike.png" width="619" height="550" border="0" alt="Car" class="map"
         usemap="#features">
    <map name="features">
        <area id="part1" shape="rect" coords="28,11,68,51" href="#" alt="REAR WHEEL" title="REAR WHEEL"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(1)">

        <area id="part2" shape="rect" coords="74,11,114,51" href="#" alt="TAIL LAMP" title="TAIL LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(2)">

        <area id="part3" shape="rect" coords="117,11,157,51" href="#" alt="SEAT COWLING (RIGHT/LEFT)"
              title="SEAT COWLING (RIGHT/LEFT)"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(3)">

        <area id="part4" shape="rect" coords="160,11,200,51" href="#" alt="SEAT" title="SEAT"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(4)">

        <area id="part5" shape="rect" coords="203,11,243,51" href="#" alt="SIDE COWLING (RIGHT/LEFT)"
              title="SIDE COWLING (RIGHT/LEFT)"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(5)">

        <area id="part6" shape="rect" coords="246,11,286,51" href="#" alt="FUEL TANK" title="FUEL TANK"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(6)">

        <area id="part7" shape="rect" coords="288,11,328,51" href="#" alt="HANDLE BAR" title="HANDLE BAR"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(7)">

        <area id="part8" shape="rect" coords="330,11,370,51" href="#" alt="SIDE MIRROR (LEFT/RIGHT)"
              title="SIDE MIRROR (LEFT/RIGHT)"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(8)">

        <area id="part9" shape="rect" coords="372,11,412,51" href="#" alt="MEETER SET" title="MEETER SET"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(9)">

        <area id="part10" shape="rect" coords="420,11,460,51" href="#" alt="HEAD LAMP" title="HEAD LAMP"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(10)">

        <area id="part11" shape="rect" coords="466,11,506,51" href="#" alt="SIGNAL LAMP (LEFT/RIGHT)"
              title="SIGNAL LAMP (LEFT/RIGHT)"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(11)">

        <area id="part12" shape="rect" coords="509,11,549,51" href="#" alt="FRONT FENDER" title="FRONT FENDER"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(12)">

        <area id="part13" shape="rect" coords="553,11,593,51" href="#" alt="FRONT WHEEL" title="FRONT WHEEL"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(13)">

        <area id="part14" shape="rect" coords="39,492,79,532" href="#" alt="REAR SHOCK ABSOBER"
              title="REAR SHOCK ABSOBER"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(14)">

        <area id="part15" shape="rect" coords="81,493,121,533" href="#" alt="SILENCER TUBE" title="SILENCER TUBE"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(15)">

        <area id="part16" shape="rect" coords="123,493,163,533" href="#" alt="CRASH BAR" title="CRASH BAR"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(16)">

        <area id="part17" shape="rect" coords="165,493,205,533" href="#" alt="FOOT REST" title="FOOT REST"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(17)">

        <area id="part18" shape="rect" coords="208,493,248,533" href="#" alt="FRONT FORK" title="FRONT FORK"
              class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
              onClick="setImageMap(18)">
    </map>


    <div style="width:430px; height:auto; padding:3px; height:48px; position:absolute; left: 626px; top: 16px;">
        <%
            PolicyClaimManager claim_Manager = PolicyClaimManager.getInstance();
            List<DamageBodyPart> list = claim_Manager.getDamageBodyPartList(0, 6);

            String TEXTBOX_DISABLED = "";
            int cnt = 0;
            int size = list.size();
            String checked = "checked=\"checked\"";
            String display = "display: none;";
            String color1 = "#F2F2F2";
            String color2 = "#F9F9F9";
            for (DamageBodyPart damageBodyPart : list) {
                if (damageBodyPart.isIsRecFoundMainTable()) {
                    checked = "checked=\"checked\"";
                    color1 = "#F90";
                    color2 = "#F90";
                    display = "display: block;";
                } else {
                    checked = "";
                    color1 = "#F2F2F2";
                    color2 = "#F9F9F9";
                    display = "display: none;";
                }
                cnt = damageBodyPart.getN_txn_id();

        %>

        <div style="width:430px; height:auto; padding:3px; height:48px; position:relative; left: 0px; top: 0px;">
            <div id="div_head<%=cnt%>" style="height:25px;width:420px;background-color:<%=color1%>; padding-top:3px;">
                <div style="float:left;width:20px;">
                    <input type="hidden" name="txtN_txn_id<%=cnt%>" id="txtN_txn_id<%=cnt%>" value="<%=cnt%>"/>
                    <input name="chkDamageParts<%=cnt%>" id="chkDamageParts<%=cnt%>" type="checkbox" <%=checked%>
                           value="Y" onClick="setImageMap(<%=cnt%>);" <%=TEXTBOX_DISABLED%> />
                    <input type="hidden" name="txtDamageParts<%=cnt%>" id="txtDamageParts<%=cnt%>"
                           value="<%=damageBodyPart.getV_part_code() %>"/>
                </div>
                <div style="float:left;width:230px;font-weight:bold;font-family:Arial;font-size:11px;padding:3px;">
                    (<%=cnt%>)  <%=damageBodyPart.getV_part_name()%> (<%=damageBodyPart.getV_part_code()%>)
                </div>
                <div style="float:left;width:140px;font-weight:bold;font-family:Arial;font-size:11px;padding:3px;height:20px;">
                    <input name="txtOther<%=cnt%>" id="txtOther5<%=cnt%>" type="text"
                           value="<%=damageBodyPart.getV_other_text()%>" style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                </div>
            </div>
            <div style="clear:both;"></div>
            <div id="div_sub<%=cnt%>" style="height:20px;width:420px;background-color:<%=color2%>;padding-top:3px;">
                <div style="float:left;width:30px;height:25px;">
                    <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts1<%=cnt%>"
                           type="radio" <%=damageBodyPart.getV_dameg_type().equalsIgnoreCase("Broken") ? "checked=\"checked\"" : "" %>
                           value="Broken" onClick="clickRedioButton(<%=cnt%>,1)"
                           style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                </div>
                <div style="float:left;width:50px;height:20px;font-size:11px;font-family:Arial;padding-top:5px;">
                    Broken
                </div>
                <div style="float:left;width:30px;height:25px;">
                    <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts2<%=cnt%>"
                           type="radio" <%=damageBodyPart.getV_dameg_type().equalsIgnoreCase("Dented") ? "checked=\"checked\"" : "" %>
                           value="Dented" onClick="clickRedioButton(<%=cnt%>,2)"
                           style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                </div>
                <div style="float:left;width:50px;height:20px;font-size:11px;font-family:Arial;padding-top:5px;">
                    Dented
                </div>
                <div style="float:left;width:30px;height:25px;">
                    <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts3<%=cnt%>"
                           type="radio"  <%=damageBodyPart.getV_dameg_type().equalsIgnoreCase("Scraped") ? "checked=\"checked\"" : "" %>
                           value="Scraped" onClick="clickRedioButton(<%=cnt%>,3)"
                           style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                </div>
                <div style="float:left;width:50px;height:20px;font-size:11px;font-family:Arial;padding-top:5px;">
                    Scraped
                </div>
                <div style="float:left;width:30px;height:25px;">
                    <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts4<%=cnt%>"
                           type="radio" <%=damageBodyPart.getV_dameg_type().equalsIgnoreCase("Come out") ? "checked=\"checked\"" : "" %>
                           value="Come out" onClick="clickRedioButton(<%=cnt%>,4)"
                           style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                </div>
                <div style="float:left;width:60px;height:20px;font-size:11px;font-family:Arial;padding-top:5px;">Come
                    out
                </div>
                <div style="float:left;width:30px;height:25px;">
                    <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts5<%=cnt%>"
                           type="radio" <%=damageBodyPart.getV_dameg_type().equalsIgnoreCase("Other") ? "checked=\"checked\"" : "" %>
                           value="Other" onClick="clickRedioButton(<%=cnt%>,5)"
                           style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                </div>
                <div style="float:left;width:50px;height:20px;font-size:11px;font-family:Arial;padding-top:5px;">Other
                </div>
            </div>
        </div>


        <%
            }
            out.print("<input type=\"hidden\" name=\"txtDamagePartCount\" id=\"txtDamagePartCount\" value=\"" + size + "\" />");
        %>
    </div>
</form>
</body>
</html>
