<%--
    Document   : car_model_image
    Created on : Oct 9, 2011, 2:23:29 PM
    Author     : <PERSON><PERSON>
--%>
<%@page import="com.misyn.mcms.claim.dto.DamageBodyPartDto" %>
<%@ page import="com.misyn.mcms.claim.service.CallCenterService" %>
<%@ page import="com.misyn.mcms.utility.AppConstant" %>
<%@page import="java.util.List" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>


    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/vehicle-model/jquery.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/vehicle-model/jquery.maphilight.js.pagespeed.ce.MH6j2FpPiZ.js"></script>
    <script>
        /*  $(function() {
          $('.map').maphilight({
              fillColor: '008800'
          });
          $('#part1').click(function(e) {
              e.preventDefault();
              var data = $('#part1').mouseout().data('maphilight') || {};
              data.alwaysOn = !data.alwaysOn;
              alert(data.alwaysOn);
              $('#part1').data('maphilight', data).trigger('alwaysOn.maphilight');
          });

      });*/

        function setImageMap(map_id) {
            $(function () {
                $('.map').maphilight({
                    fillColor: '008800'
                });
                // $('#part1').click(function(e) {
                // e.preventDefault();
                var data = $('#part' + map_id).mouseout().data('maphilight') || {};
                data.alwaysOn = !data.alwaysOn;
                parent.toggleGroup(map_id, data.alwaysOn);
                toggleGroup(map_id, data.alwaysOn);
                $('#part' + map_id).data('maphilight', data).trigger('alwaysOn.maphilight');
                // });

            });
        }

        function setSelectedImage() {


            var listData = parent.getDamageBodyPartsList();
            var listCheckRedio = parent.getCheckRedioList();
            //alert(listCheckRedio);
            var listSplitDataResult = listData.split(",");
            var listSplitCheckRedioResult = listCheckRedio.split(",");

            for (i = 0; i < listSplitDataResult.length - 1; i++) {
                setImageMap(listSplitDataResult[i]);
                initRedioButton(listSplitDataResult[i], listSplitCheckRedioResult[i]);
                setTextOwnTextBox(listSplitDataResult[i]);
                //alert("<br /> Element " + i + " = " + listSplitCheckRedioResult[i]);
            }

            parent.parent.document.getElementById("cell1").style.display = "none";
            parent.parent.document.getElementById("loading").style.display = "none";
        }

        function toggleGroup(id, val) {
            // alert(val);
            if (!val) {
                document.getElementById("rdoDamageParts1" + id).style.display = "none";
                document.getElementById("rdoDamageParts1" + id).checked = false;

                document.getElementById("rdoDamageParts2" + id).style.display = "none";
                document.getElementById("rdoDamageParts2" + id).checked = false;

                document.getElementById("rdoDamageParts3" + id).style.display = "none";
                document.getElementById("rdoDamageParts3" + id).checked = false;

                document.getElementById("rdoDamageParts4" + id).style.display = "none";
                document.getElementById("rdoDamageParts4" + id).checked = false;

                document.getElementById("rdoDamageParts5" + id).style.display = "none";
                document.getElementById("rdoDamageParts5" + id).checked = false;

                document.getElementById("txtOther5" + id).style.display = "none";
                document.getElementById("txtOther5" + id).value = "";

                document.getElementById("div_head" + id).style.backgroundColor = "#F2F2F2";
                document.getElementById("div_sub" + id).style.backgroundColor = "#F9F9F9";

                document.getElementById("chkDamageParts" + id).checked = false;

            }
            else {
                document.getElementById("rdoDamageParts1" + id).style.display = "block";
                document.getElementById("rdoDamageParts2" + id).style.display = "block";
                document.getElementById("rdoDamageParts3" + id).style.display = "block";
                document.getElementById("rdoDamageParts4" + id).style.display = "block";
                document.getElementById("rdoDamageParts5" + id).style.display = "block";
                document.getElementById("txtOther5" + id).style.display = "block";

                document.getElementById("div_head" + id).style.backgroundColor = "#015aaa";
                document.getElementById("div_head" + id).style.color = "#FFF";
                document.getElementById("div_sub" + id).style.backgroundColor = "#015aaa";
                document.getElementById("div_sub" + id).style.color = "#FFF";
                document.getElementById("chkDamageParts" + id).checked = true;
            }
        }

        function clickRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                parent.document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                parent.document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                parent.document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                parent.document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                parent.document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

        function initRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

        function setTextParentTextBox(id) {
            parent.document.getElementById("txtOther5" + id).value = document.getElementById("txtOther5" + id).value;
        }

        function setTextOwnTextBox(id) {
            document.getElementById("txtOther5" + id).value = parent.document.getElementById("txtOther5" + id).value;
        }

    </script>
    <title>JSP Page</title>
</head>
<body onLoad="setSelectedImage()">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" action="" method="post">
        <div class="row">
            <div class="col-md-6">
                <img src="${pageContext.request.contextPath}/image/vehicle_images/motorbike.png"  border="0" alt="Car" class="map"
                     usemap="#features">
                <map name="features">
                    <area id="part1" shape="rect" coords="24,2,64,42" href="#" alt="REAR WHEEL" title="REAR WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(1)">

                    <area id="part2" shape="rect" coords="70,2,110,42" href="#" alt="TAIL LAMP" title="TAIL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(2)">

                    <area id="part3" shape="rect" coords="116,2,156,42" href="#" alt="SEAT COWL RIGHT"
                          title="SEAT COWL RIGHT"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(3)">

                    <area id="part4" shape="rect" coords="158,2,198,42" href="#" alt="SEAT" title="SEAT"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(4)">

                    <area id="part5" shape="rect" coords="201,2,241,42" href="#" alt="RIGHT SIDE COWL"
                          title="RIGHT SIDE COWL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(5)">

                    <area id="part6" shape="rect" coords="242,2,282,42" href="#" alt="FUEL TANK" title="FUEL TANK"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(6)">

                    <area id="part7" shape="rect" coords="283,2,323,42" href="#" alt="HANDLE BAR" title="HANDLE BAR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(7)">

                    <area id="part8" shape="rect" coords="325,2,365,42" href="#" alt="RIGHT SIDE MIRROR"
                          title="RIGHT SIDE MIRROR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(8)">

                    <area id="part9" shape="rect" coords="367,2,407,42" href="#" alt="MEETER SET" title="MEETER SET"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(9)">

                    <area id="part10" shape="rect" coords="413,2,453,42" href="#" alt="HEAD LAMP" title="HEAD LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(10)">

                    <area id="part11" shape="rect" coords="455,2,495,42" href="#" alt="FRONT RIGHT SIGNAL LAMP"
                          title="FRONT RIGHT SIGNAL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(11)">

                    <area id="part12" shape="rect" coords="497,2,537,42" href="#" alt="FRONT FENDER"
                          title="FRONT FENDER"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(12)">

                    <area id="part13" shape="rect" coords="539,2,579,42" href="#" alt="FRONT WHEEL" title="FRONT WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(13)">

                    <area id="part14" shape="rect" coords="76,84,116,124" href="#" alt="REAR RIGHT SIGNAL LAMP"
                          title="REAR RIGHT SIGNAL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(14)">

                    <area id="part15" shape="rect" coords="76,125,116,165" href="#" alt="SILENCER TUBE"
                          title="SILENCER TUBE"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(15)">

                    <area id="part16" shape="rect" coords="76,166,116,206" href="#" alt="CRASH BAR RIGHT"
                          title="CRASH BAR RIGHT"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(16)">

                    <area id="part17" shape="rect" coords="76,207,116,247" href="#" alt="RIGHT FOOT REST"
                          title="RIGHT FOOT REST"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(17)">

                    <area id="part18" shape="rect" coords="76,248,116,288" href="#" alt="FRONT FORK TUBE"
                          title="FRONT FORK TUBE"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(18)">

                    <area id="part19" shape="rect" coords="31,261,71,301" href="#" alt="LEFT SIDE MIRROR"
                          title="LEFT SIDE MIRROR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(19)">

                    <area id="part20" shape="rect" coords="31,313,71,353" href="#" alt="FRONT LEFT SIGNAL LAMP"
                          title="FRONT LEFT SIGNAL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(20)">

                    <area id="part21" shape="rect" coords="31,368,71,408" href="#" alt="LEFT SIDE COWL"
                          title="LEFT SIDE COWL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(21)">

                    <area id="part22" shape="rect" coords="31,419,71,459" href="#" alt="CRASH BAR LEFT"
                          title="CRASH BAR LEFT"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(22)">

                    <area id="part23" shape="rect" coords="284,500,324,540" href="#" alt="LEFT FOOT REST"
                          title="LEFT FOOT REST"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(23)">

                    <area id="part24" shape="rect" coords="329,500,369,540" href="#" alt="LEFT REAR SIGNAL LAMP"
                          title="LEFT REAR SIGNAL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(24)">

                    <area id="part25" shape="rect" coords="373,500,413,540" href="#" alt="SEAT COWL LEFT"
                          title="SEAT COWL LEFT"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(25)">
                </map>
            </div>
            <div class="col-md-6">

                <div>
                    <%
                        CallCenterService callCenterService = (CallCenterService) session.getAttribute(AppConstant.CALL_CENTER_SERVICE);
                        List<DamageBodyPartDto> list = callCenterService.getDamageBodyPartDtoList(0, 6);


                        String TEXTBOX_DISABLED = "";
                        int cnt = 0;
                        int size = list.size();
                        String checked = "checked=\"checked\"";
                        String display = "display: none;";
                        String color1 = "#F2F2F2";
                        String color2 = "#F9F9F9";
                        for (DamageBodyPartDto damageBodyPart : list) {
                            if (damageBodyPart.isRecFoundMainTable()) {
                                checked = "checked=\"checked\"";
                                color1 = "#015aaa";
                                color2 = "#015aaa";
                                display = "display: block;";
                            } else {
                                checked = "";
                                color1 = "#F2F2F2";
                                color2 = "#F9F9F9";
                                display = "display: none;";
                            }
                            cnt = damageBodyPart.getTxnId();

                    %>

                    <div>
                        <div class="row p-2" id="div_head<%=cnt%>" style="height:35px;background-color:<%=color1%>;">
                            <div class="col-1">
                                <input type="hidden" name="txtN_txn_id<%=cnt%>" id="txtN_txn_id<%=cnt%>"
                                       value="<%=cnt%>"/>
                                <input name="chkDamageParts<%=cnt%>" id="chkDamageParts<%=cnt%>"
                                       type="checkbox" <%=checked%>
                                       value="Y" onClick="setImageMap(<%=cnt%>);" <%=TEXTBOX_DISABLED%> />
                                <input type="hidden" name="txtDamageParts<%=cnt%>" id="txtDamageParts<%=cnt%>"
                                       value="<%=damageBodyPart.getPartCode() %>"/>
                            </div>
                            <div class="col-5" style="font-weight:bold;font-family:Arial;font-size:11px;">
                                (<%=cnt%>)<%=damageBodyPart.getPartName()%> (<%=damageBodyPart.getPartCode()%>)
                            </div>
                            <div class="col-5" style="font-weight:bold;font-family:Arial;font-size:11px;height:20px;">
                                <input style="width: 100%" name="txtOther<%=cnt%>" id="txtOther5<%=cnt%>" type="text"
                                       value="<%=damageBodyPart.getOtherText()%>"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>
                                       onkeyup="setTextParentTextBox(<%=cnt%>)"/></div>
                        </div>


                        <div class="row p-2" id="div_sub<%=cnt%>" style="height:30px;background-color:<%=color2%>;">
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts1<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Broken") ? "checked=\"checked\"" : "" %>
                                       value="Broken" onClick="clickRedioButton(<%=cnt%>,1)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Broken
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts2<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Dented") ? "checked=\"checked\"" : "" %>
                                       value="Dented" onClick="clickRedioButton(<%=cnt%>,2)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Dented
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts3<%=cnt%>"
                                       type="radio"  <%=damageBodyPart.getDamegType().equalsIgnoreCase("Scraped") ? "checked=\"checked\"" : "" %>
                                       value="Scraped" onClick="clickRedioButton(<%=cnt%>,3)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Scraped
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts4<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Come out") ? "checked=\"checked\"" : "" %>
                                       value="Come out" onClick="clickRedioButton(<%=cnt%>,4)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-2 pl-0" style="font-size:11px;font-family:Arial;">
                                Come
                                out
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts5<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Other") ? "checked=\"checked\"" : "" %>
                                       value="Other" onClick="clickRedioButton(<%=cnt%>,5)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Other
                            </div>
                        </div>
                    </div>


                    <%
                        }
                        out.print("<input type=\"hidden\" name=\"txtDamagePartCount\" id=\"txtDamagePartCount\" value=\"" + size + "\" />");
                    %>
                </div>
            </div>
        </div>
    </form>
</div>
</body>
</html>
