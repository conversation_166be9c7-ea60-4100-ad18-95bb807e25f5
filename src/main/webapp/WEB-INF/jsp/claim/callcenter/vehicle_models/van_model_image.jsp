<%--
    Document   : car_model_image
    Created on : Oct 9, 2011, 2:23:29 PM
    Author     : <PERSON><PERSON>
--%>

<%@page import="com.misyn.mcms.claim.dto.DamageBodyPartDto" %>
<%@ page import="com.misyn.mcms.claim.service.CallCenterService" %>
<%@ page import="com.misyn.mcms.utility.AppConstant" %>
<%@ page import="java.util.List" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>


    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/vehicle-model/jquery.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/vehicle-model/jquery.maphilight.js.pagespeed.ce.MH6j2FpPiZ.js"></script>
    <script>
        /*  $(function() {
          $('.map').maphilight({
              fillColor: '008800'
          });
          $('#part1').click(function(e) {
              e.preventDefault();
              var data = $('#part1').mouseout().data('maphilight') || {};
              data.alwaysOn = !data.alwaysOn;
              alert(data.alwaysOn);
              $('#part1').data('maphilight', data).trigger('alwaysOn.maphilight');
          });

      });*/

        function setImageMap(map_id) {
            $(function () {
                $('.map').maphilight({
                    fillColor: '008800'
                });
                // $('#part1').click(function(e) {
                // e.preventDefault();
                var data = $('#part' + map_id).mouseout().data('maphilight') || {};
                data.alwaysOn = !data.alwaysOn;
                parent.toggleGroup(map_id, data.alwaysOn);
                toggleGroup(map_id, data.alwaysOn);
                $('#part' + map_id).data('maphilight', data).trigger('alwaysOn.maphilight');
                // });

            });
        }

        function setSelectedImage() {


            var listData = parent.getDamageBodyPartsList();
            var listCheckRedio = parent.getCheckRedioList();
            //alert(listCheckRedio);
            var listSplitDataResult = listData.split(",");
            var listSplitCheckRedioResult = listCheckRedio.split(",");

            for (i = 0; i < listSplitDataResult.length - 1; i++) {
                setImageMap(listSplitDataResult[i]);
                initRedioButton(listSplitDataResult[i], listSplitCheckRedioResult[i]);
                setTextOwnTextBox(listSplitDataResult[i]);
                //alert("<br /> Element " + i + " = " + listSplitCheckRedioResult[i]);
            }

            parent.parent.document.getElementById("cell1").style.display = "none";
            parent.parent.document.getElementById("loading").style.display = "none";
        }

        function toggleGroup(id, val) {
            // alert(val);
            if (!val) {
                document.getElementById("rdoDamageParts1" + id).style.display = "none";
                document.getElementById("rdoDamageParts1" + id).checked = false;

                document.getElementById("rdoDamageParts2" + id).style.display = "none";
                document.getElementById("rdoDamageParts2" + id).checked = false;

                document.getElementById("rdoDamageParts3" + id).style.display = "none";
                document.getElementById("rdoDamageParts3" + id).checked = false;

                document.getElementById("rdoDamageParts4" + id).style.display = "none";
                document.getElementById("rdoDamageParts4" + id).checked = false;

                document.getElementById("rdoDamageParts5" + id).style.display = "none";
                document.getElementById("rdoDamageParts5" + id).checked = false;

                document.getElementById("txtOther5" + id).style.display = "none";
                document.getElementById("txtOther5" + id).value = "";

                document.getElementById("div_head" + id).style.backgroundColor = "#F2F2F2";
                document.getElementById("div_sub" + id).style.backgroundColor = "#F9F9F9";

                document.getElementById("chkDamageParts" + id).checked = false;

            }
            else {
                document.getElementById("rdoDamageParts1" + id).style.display = "block";
                document.getElementById("rdoDamageParts2" + id).style.display = "block";
                document.getElementById("rdoDamageParts3" + id).style.display = "block";
                document.getElementById("rdoDamageParts4" + id).style.display = "block";
                document.getElementById("rdoDamageParts5" + id).style.display = "block";
                document.getElementById("txtOther5" + id).style.display = "block";

                document.getElementById("div_head" + id).style.backgroundColor = "#015aaa";
                document.getElementById("div_head" + id).style.color = "#FFF";
                document.getElementById("div_sub" + id).style.backgroundColor = "#015aaa";
                document.getElementById("div_sub" + id).style.color = "#FFF";
                document.getElementById("chkDamageParts" + id).checked = true;
            }
        }

        function clickRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                parent.document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                parent.document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                parent.document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                parent.document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                parent.document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

        function initRedioButton(id, sqeNo) {
            if (sqeNo == 1) {
                document.getElementById("rdoDamageParts1" + id).checked = true;

            }
            else if (sqeNo == 2) {
                document.getElementById("rdoDamageParts2" + id).checked = true;
            }
            else if (sqeNo == 3) {
                document.getElementById("rdoDamageParts3" + id).checked = true;
            }
            else if (sqeNo == 4) {
                document.getElementById("rdoDamageParts4" + id).checked = true;
            }
            else if (sqeNo == 5) {
                document.getElementById("rdoDamageParts5" + id).checked = true;
            }
        }

        function setTextParentTextBox(id) {
            parent.document.getElementById("txtOther5" + id).value = document.getElementById("txtOther5" + id).value;
        }

        function setTextOwnTextBox(id) {
            document.getElementById("txtOther5" + id).value = parent.document.getElementById("txtOther5" + id).value;
        }

    </script>
    <title>JSP Page</title>
</head>
<body onLoad="setSelectedImage()">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" action="" method="post">
        <div class="row">
            <div class="col-md-6">
                <img src="${pageContext.request.contextPath}/image/vehicle_images/van.png" border="0" alt="Car"
                     class="map"
                     usemap="#features">
                <map name="features">
                    <area id="part1" shape="rect" coords="12,8,52,48" href="#" alt="FRONT BUFFER" title="FRONT BUFFER"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(1)">

                    <area id="part2" shape="rect" coords="58,8,98,48" href="#" alt="RIGHT HEAD LAMP"
                          title="RIGHT HEAD LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(2)">

                    <area id="part3" shape="rect" coords="102,8,142,48" href="#" alt="FRONT FACE" title="FRONT FACE"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(3)">

                    <area id="part4" shape="rect" coords="149,8,189,48" href="#" alt="RIGHT SIDE MIRROR"
                          title="RIGHT SIDE MIRROR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(4)">

                    <area id="part5" shape="rect" coords="194,8,234,48" href="#" alt="FRONT WINDSCREEN GLASS"
                          title="FRONT WINDSCREEN GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(5)">

                    <area id="part6" shape="rect" coords="237,8,277,48" href="#" alt="FRONT GRILL" title="FRONT GRILL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(6)">

                    <area id="part7" shape="rect" coords="283,8,323,48" href="#" alt="LEFT HEAD LAMP"
                          title="LEFT HEAD LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(7)">

                    <area id="part8" shape="rect" coords="328,8,368,48" href="#" alt="LEFT SIDE MIRROR"
                          title="LEFT SIDE MIRROR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(8)">

                    <area id="part9" shape="rect" coords="373,8,413,48" href="#" alt="LEFT TAIL LAMP"
                          title="LEFT TAIL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(9)">

                    <area id="part10" shape="rect" coords="423,8,463,48" href="#" alt="TAIL GATE" title="TAIL GATE"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(10)">

                    <area id="part11" shape="rect" coords="472,7,512,47" href="#" alt="REAR WINDSCREEN GLASS"
                          title="REAR WINDSCREEN GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(11)">

                    <area id="part12" shape="rect" coords="517,7,557,47" href="#" alt="RIGHT TAIL LAMP"
                          title="RIGHT TAIL LAMP"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(12)">

                    <area id="part13" shape="rect" coords="564,8,604,48" href="#" alt="REAR BUFFER" title="REAR BUFFER"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(13)">

                    <area id="part14" shape="rect" coords="13,101,53,141" href="#" alt="LEFT REAR (SLIDING) DOOR GLASS"
                          title="LEFT REAR (SLIDING) DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(14)">

                    <area id="part15" shape="rect" coords="561,106,601,146" href="#" alt="LEFT REAR BODY GLASS"
                          title="LEFT REAR BODY GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(15)">

                    <area id="part16" shape="rect" coords="13,148,53,188" href="#" alt="LEFT FRONT DOOR GLASS"
                          title="LEFT FRONT DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(16)">

                    <area id="part17" shape="rect" coords="561,154,601,194" href="#" alt="LEFT SLIDING DOOR"
                          title="LEFT SLIDING DOOR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(17)">

                    <area id="part18" shape="rect" coords="13,199,53,239" href="#" alt="UNDEFINED" title="UNDEFINED"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(18)">

                    <area id="part19" shape="rect" coords="561,200,601,240" href="#" alt="LEFT REAR BODY"
                          title="LEFT REAR BODY"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(19)">

                    <area id="part20" shape="rect" coords="13,245,53,285" href="#" alt="LEFT FRONT WHEEL"
                          title="LEFT FRONT WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(20)">

                    <area id="part21" shape="rect" coords="562,246,602,286" href="#" alt="LEFT REAR WHEEL"
                          title="LEFT REAR WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(21)">

                    <area id="part22" shape="rect" coords="13,295,53,335" href="#" alt="RIGHT REAR BODY GLASS"
                          title="RIGHT REAR BODY GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(22)">

                    <area id="part23" shape="rect" coords="562,290,602,330" href="#" alt="RIGHT FRONT DOOR GLASS"
                          title="RIGHT FRONT DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(23)">

                    <area id="part24" shape="rect" coords="12,339,52,379" href="#" alt="RIGHT REAR (SLIDING) DOOR GLASS"
                          title="RIGHT REAR (SLIDING) DOOR GLASS"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(24)">

                    <area id="part25" shape="rect" coords="562,334,602,374" href="#" alt="RIGHT SLIDING DOOR"
                          title="RIGHT SLIDING DOOR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(25)">

                    <area id="part26" shape="rect" coords="12,386,52,426" href="#" alt="RIGHT REAR BODY"
                          title="RIGHT REAR BODY"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(26)">

                    <area id="part27" shape="rect" coords="563,378,603,418" href="#" alt="RIGHT FRONT DOOR"
                          title="RIGHT FRONT DOOR"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(27)">

                    <area id="part28" shape="rect" coords="12,431,52,471" href="#" alt="RIGHT REAR WHEEL"
                          title="RIGHT REAR WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(28)">

                    <area id="part29" shape="rect" coords="562,423,602,463" href="#" alt="RIGHT FRONT WHEEL"
                          title="RIGHT FRONT WHEEL"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(29)">

                    <area id="part30" shape="rect" coords="562,473,602,513" href="#" alt="ROOF PANLE (HOOD)"
                          title="ROOF PANLE (HOOD)"
                          class="group {strokeColor:'0000ff',strokeWidth:5,fillColor:'ff0000',fillOpacity:0.6}"
                          onClick="setImageMap(30)">

                </map>
            </div>

            <div class="col-md-6">
                <div>
                    <%

                        CallCenterService callCenterService = (CallCenterService) session.getAttribute(AppConstant.CALL_CENTER_SERVICE);
                        List<DamageBodyPartDto> list = callCenterService.getDamageBodyPartDtoList(0, 2);

                        String TEXTBOX_DISABLED = "";
                        int cnt = 0;
                        int size = list.size();
                        String checked = "checked=\"checked\"";
                        String display = "display: none;";
                        String color1 = "#F2F2F2";
                        String color2 = "#F9F9F9";
                        for (DamageBodyPartDto damageBodyPart : list) {
                            if (damageBodyPart.isRecFoundMainTable()) {
                                checked = "checked=\"checked\"";
                                color1 = "#F90";
                                color2 = "#F90";
                                display = "display: block;";
                            } else {
                                checked = "";
                                color1 = "#F2F2F2";
                                color2 = "#F9F9F9";
                                display = "display: none;";
                            }
                            cnt = damageBodyPart.getTxnId();

                    %>

                    <div>
                        <div class="row p-2" id="div_head<%=cnt%>" style="height:35px;background-color:<%=color1%>;">
                            <div class="col-1">
                                <input type="hidden" name="txtN_txn_id<%=cnt%>" id="txtN_txn_id<%=cnt%>"
                                       value="<%=cnt%>"/>
                                <input name="chkDamageParts<%=cnt%>" id="chkDamageParts<%=cnt%>"
                                       type="checkbox" <%=checked%>
                                       value="Y" onClick="setImageMap(<%=cnt%>);" <%=TEXTBOX_DISABLED%> />
                                <input type="hidden" name="txtDamageParts<%=cnt%>" id="txtDamageParts<%=cnt%>"
                                       value="<%=damageBodyPart.getPartCode() %>"/>
                            </div>
                            <div class="col-5" style="font-weight:bold;font-family:Arial;font-size:11px;">
                                (<%=cnt%>)  <%=damageBodyPart.getPartName()%> (<%=damageBodyPart.getPartCode()%>)
                            </div>
                            <div class="col-5" style="font-weight:bold;font-family:Arial;font-size:11px;height:20px;">
                                <input style="width: 100%" name="txtOther<%=cnt%>" id="txtOther5<%=cnt%>" type="text"
                                       value="<%=damageBodyPart.getOtherText()%>"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>
                                       onKeyUp="setTextParentTextBox(<%=cnt%>)"/></div>
                        </div>


                        <div class="row p-2" id="div_sub<%=cnt%>" style="height:30px;background-color:<%=color2%>;">
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts1<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Broken") ? "checked=\"checked\"" : "" %>
                                       value="Broken" onClick="clickRedioButton(<%=cnt%>,1)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Broken
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts2<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Dented") ? "checked=\"checked\"" : "" %>
                                       value="Dented" onClick="clickRedioButton(<%=cnt%>,2)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%>  />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Dented
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts3<%=cnt%>"
                                       type="radio"  <%=damageBodyPart.getDamegType().equalsIgnoreCase("Scraped") ? "checked=\"checked\"" : "" %>
                                       value="Scraped" onClick="clickRedioButton(<%=cnt%>,3)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Scraped
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts4<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Come out") ? "checked=\"checked\"" : "" %>
                                       value="Come out" onClick="clickRedioButton(<%=cnt%>,4)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-2 pl-0" style="font-size:11px;font-family:Arial;">
                                Come
                                out
                            </div>
                            <div class="col-1">
                                <input name="rdoDamageParts<%=cnt%>" id="rdoDamageParts5<%=cnt%>"
                                       type="radio" <%=damageBodyPart.getDamegType().equalsIgnoreCase("Other") ? "checked=\"checked\"" : "" %>
                                       value="Other" onClick="clickRedioButton(<%=cnt%>,5)"
                                       style="<%=display%>" <%=TEXTBOX_DISABLED%> />
                            </div>
                            <div class="col-1 pl-0" style="font-size:11px;font-family:Arial;">
                                Other
                            </div>
                        </div>
                    </div>


                    <%
                        }
                        out.print("<input type=\"hidden\" name=\"txtDamagePartCount\" id=\"txtDamagePartCount\" value=\"" + size + "\" />");
                    %>
                </div>
            </div>
        </div>
    </form>
</div>
</body>
</html>
