<%--
  Created by IntelliJ IDEA.
  User: Kelum
  Date: 4/26/2018
  Time: 3:28 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>


<c:set var="cnt" value="0" scope="page"/>
<c:set var="checked" value="checked='checked'" scope="page"/>
<c:set var="color1" value="#F2F2F2" scope="page"/>
<c:set var="color2" value="#F9F9F9" scope="page"/>
<c:set var="display" value="display: none;" scope="page"/>
<c:set var="textboxDisabled" value="" scope="page"/>


<c:choose>
    <c:when test="${FORM_TYPE!='HISTORY'}">
        <c:set var="damageList" value="${damageList}" scope="session"/>
    </c:when>
    <c:when test="${FORM_TYPE=='HISTORY'}">
        <c:set var="damageList" value="${historyDamageList}" scope="request"/>
    </c:when>
</c:choose>
<c:set var="size" value="${damageList.size()}" scope="page"/>
<c:forEach var="damageBodyPartDto" items="${damageList}">
    <c:set var="cnt" value="${damageBodyPartDto.txnId}" scope="page"/>
    <c:choose>
        <c:when test="${damageBodyPartDto.recFoundMainTable}">
            <c:set var="checked" value="checked='checked'" scope="page"/>
            <c:set var="color1" value="#015aaa" scope="page"/>
            <c:set var="color2" value="#015aaa" scope="page"/>
            <c:set var="display" value="display: block;" scope="page"/>
        </c:when>
        <c:otherwise>
            <c:set var="checked" value="" scope="page"/>
            <c:set var="color1" value="#F2F2F2" scope="page"/>
            <c:set var="color2" value="#F9F9F9" scope="page"/>
            <c:set var="display" value="display: none;" scope="page"/>
        </c:otherwise>
    </c:choose>


    <div style="width:430px;height:auto;padding:3px;height:48px;">
        <div id="div_head${cnt}" style="height:25px;width:420px;background-color:${color1}; padding-top:3px;">
            <div style="float:left;width:20px;">
                <input type="hidden" name="txtN_txn_id${cnt}" id="txtN_txn_id${cnt}" value="${cnt}"/>
                <input name="chkDamageParts${cnt}" id="chkDamageParts${cnt}" type="checkbox" ${checked} value="Y"
                       onclick="toggleGroup(${cnt},this.checked);" ${textboxDisabled} />
                <input type="hidden" name="txtDamageParts${cnt}" id="txtDamageParts${cnt}"
                       value="${damageBodyPartDto.partCode}"/>
            </div>
            <div style="float:left;width:230px;font-weight:bold;font-family:Arial;font-size:11px;padding:3px;">${damageBodyPartDto.partName}
                (${damageBodyPartDto.partCode})
            </div>
            <div style="float:left;width:140px;font-weight:bold;font-family:Arial;font-size:11px;padding:3px;"><input
                    name="txtOther${cnt}" id="txtOther5${cnt}" type="text" value="${damageBodyPartDto.otherText}"
                    style="${display}" ${textboxDisabled} /></div>
        </div>
        <div style="clear:both;"></div>
        <div id="div_sub${cnt}" style="height:20px;width:420px;background-color:${color2};padding-top:3px;">
            <div style="float:left;width:30px;height:25px;">
                <input name="rdoDamageParts${cnt}" id="rdoDamageParts1${cnt}"
                       type="radio" ${damageBodyPartDto.damegType=="Broken"?"checked='checked'":""}
                       value="Broken" style="${display}" ${textboxDisabled}  />
            </div>
            <div style="float:left;width:50px;height:25px;">Broken</div>
            <div style="float:left;width:30px;height:25px;">
                <input name="rdoDamageParts${cnt}" id="rdoDamageParts2${cnt}"
                       type="radio" ${damageBodyPartDto.damegType=="Dented"?"checked='checked'":""}
                       value="Dented" style="${display}" ${textboxDisabled}  />
            </div>
            <div style="float:left;width:50px;height:25px;">Dented</div>
            <div style="float:left;width:30px;height:25px;">
                <input name="rdoDamageParts${cnt}" id="rdoDamageParts3${cnt}"
                       type="radio" ${damageBodyPartDto.damegType=="Scraped"?"checked='checked'":""}
                       value="Scraped" style="${display}" ${textboxDisabled} />
            </div>
            <div style="float:left;width:50px;height:25px;">Scraped</div>
            <div style="float:left;width:30px;height:25px;">
                <input name="rdoDamageParts${cnt}" id="rdoDamageParts4${cnt}"
                       type="radio" ${damageBodyPartDto.damegType=="Come out"?"checked='checked'":""}
                       value="Come out" style="${display}" ${textboxDisabled} />
            </div>
            <div style="float:left;width:60px;height:25px;">Come out</div>
            <div style="float:left;width:30px;height:25px;">
                <input name="rdoDamageParts${cnt}" id="rdoDamageParts5${cnt}"
                       type="radio" ${damageBodyPartDto.damegType=="Other"?"checked='checked'":""}
                       value="Other" style="${display}" ${textboxDisabled} />
            </div>
            <div style="float:left;width:50px;height:25px;">Other</div>
        </div>
    </div>
    <div style="clear:both;"></div>
</c:forEach>

<input type="hidden" name="txtDamagePartCount" id="txtDamagePartCount" value="${size}">


