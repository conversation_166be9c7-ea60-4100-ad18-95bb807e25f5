<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

    String ERROR = "";
    String str_v_status_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_status_para ", "n_ref_id", "v_status_desc", "v_type IN(0,1)", "");
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/ScrollTabla.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/datatables.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/datatables.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/dataTables.fixedHeader.min.js"></script>

    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        var techCor =
        ${IS_TECHNICAL_COORDINATOR}
        var type =${TYPE};

        $(document).ready(function () {
            if (${null ne P_N_CLIM_NO and P_N_CLIM_NO ne ''}) {
                $("#txtClaimNumber").val('${P_N_CLIM_NO}');
                search();
            }
        });

        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);

            //  $("#txtFromDate").val('');
            //  $("#txtToDate").val('');


//            var d1 = document.frmForm.txtFromDate.value;
//            $("#txtFromDate").datepicker({
//                changeMonth: true,
//                changeYear: true,
//                yearRange: '1940:2099',
//                minDate: '-70y',
//                maxDate: '0d',
//                onClose: function (dateText, inst) {
//                    document.getElementById("txtToDate").focus();
//                }
//
//            });
//            $("#txtFromDate").datepicker('option', {dateFormat: "yy-mm-dd"});
//            document.frmForm.txtFromDate.value = d1;

        });
        /*$(function () {
         //
         //buttonImage: '/image/common/calendar.gif',
         var d1 = document.frmForm.txtToDate.value;
         $("#txtToDate").datepicker({
         changeMonth: true,
         changeYear: true,
         yearRange: '1940:2099',
         minDate: '-70y',
         maxDate: '0d',
         onClose: function (dateText, inst) {
         //                    document.getElementById("txtToDate").focus();
         }

         });
         $("#txtToDate").datepicker('option', {dateFormat: "yy-mm-dd"});
         document.frmForm.txtToDate.value = d1;

         });*/


        //------------Start JQuery Script---------------------


        function setConfirmbox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "No": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        },
                        "Yes": function () {
                            $(this).dialog("close");
                            document.frmCampaign.action = "CampaignResult.jsp?" + timeUrl;
                            document.frmCampaign.submit();
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });

        }

        function setDialogBox() {
            $(document).ready(function () {
                // jQuery UI Dialog

                $('#dialog').modal({
                    autoOpen: false,
                    width: 400,
                    modal: true,
                    bgiframe: false,
                    resizable: false,
                    //closeOnEscape: false ,
                    //dialogClass: 'alert',
                    //position: [400,200],
                    //show: 'bounce',
                    //dragStop: function(event, ui) { alert("drag"+ui); },
                    buttons: {
                        "Ok": function () {
                            $(this).dialog("close");
                            //$(this).dialog({show: 'explode'});

                            //document.testconfirmJQ.submit();
                        }, "Cancel": function () {
                            //$(this).hide("explode", {}, 1000);

                            $(this).dialog("close");
                            //$(this).dialog( 'destroy' ) ;
                        }
                    }
                });

                $('form#testconfirmJQ').submit(function () {
                    // $("p#dialog-email").html($("input#emailJQ2").val());
                    //$('#dialog').dialog('open');
                    return false;
                });

            });
        }

        function showConfirmbox(str) {
            setConfirmbox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }

        function showDialogbox(str) {
            setDialogBox();
            $("p#dialog-email").html(str);
            $('#dialog').dialog('open');
        }


        //------------End JQuery Script---------------------


        //================Others=========================================================
        function setNextButtonDisable() {

        }

        function init() {
            loadRequestedUsers();
            setNextButtonDisable();
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }

        function loadRequestedUsers() {
            let URL;
            if (${TYPE == 4 || TYPE == 5}) {
                URL = contextPath + "/RequestAriController/allAriRequestUsers?TYPE=" + ${TYPE};
            } else if (TYPE == 6) {
                URL = contextPath + "/RequestAriController/ariRequestUsers";
            }
            $.ajax({
                url: URL,
                type: 'POST',
                success: function (result) {
                    $('#txtV_requestedUser').find('option').remove();
                    var selOpts = "";
                    var userArr = JSON.parse(result);
                    $('#txtV_requestedUser').append('<option value="" >Please Select</option>');
                    for (var i = 0; i < userArr.length; i++) {
                        var val = userArr[i];
                        selOpts += "<option value='" + val + "'>" + val + "</option>";
                    }
                    $('#txtV_requestedUser').append(selOpts).trigger("chosen:updated");
                }
            });
        }

    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <input name="N_REQ_REF_ID" id="N_REQ_REF_ID" type="hidden"/>
        <input name="N_ARI_ID" id="N_ARI_ID" type="hidden"/>
        <input name="TYPE" id="TYPE" type="hidden" value="${TYPE}"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <c:if test="${TYPE == 4}">
                    <h5> Pending ARI List</h5>
                </c:if>
                <c:if test="${TYPE == 5}">
                    <h5>Requested ARI List</h5>
                </c:if>
                <c:if test="${TYPE == 6}">
                    <h5>Requested ARI List - Spare Parts Coordinator/ Bill Checking Team</h5>
                </c:if>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote"><%=ERROR%>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle
                                                No </label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Vehicle No">
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label">
                                                Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <c:if test="${TYPE ==4}">
                                                        <option value="1">Pending</option>
                                                        <option value="2">Revoked</option>
                                                        <option value="3">Submitted</option>
                                                        <option value="6">Partially Reviewed</option>
                                                        <option value="7">Fully Reviewed</option>
                                                    </c:if>
                                                    <c:if test="${TYPE !=4}">
                                                        <option value="4">Hold</option>
                                                        <option value="5">Uploaded</option>
                                                    </c:if>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRequestDate" class="col-sm-4 col-form-label"> ARI Requested
                                                Date</label>
                                            <div class="col-sm-8">
                                                <input name="txtRequestDate" id="txtRequestDate" type="text"
                                                       class="form-control form-control-sm"
                                                       placeholder="ARI Requested Date">
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                No </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim No">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_requestedUser" class="col-sm-4 col-form-label">
                                                ARI Requested User
                                            </label>
                                            <div class="col-sm-8">
                                                <select name="txtV_requestedUser" id="txtV_requestedUser"
                                                        class="form-control form-control-sm chosen">
                                                    <option value="">All</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 float-left"><span
                                        class="badge badge-pill badge-priority border ">&nbsp;</span>&nbsp;&nbsp;
                                    Priority High
                                </p>
                            </div>
                        </div>
                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>ARI Result</h6>
                                    <div class="mt-2" style="overflow-x: auto">
                                        <div class="col-12">
                                            <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                                   style="cursor:pointer">
                                                <thead>
                                                <tr>
                                                    <th>Id</th>
                                                    <th width="40px">No</th>
                                                    <th>Claim No</th>
                                                    <th>Vehicle No</th>
                                                    <th style="width: 20%">Customer Name</th>
                                                    <th>Contact No</th>
                                                    <th>Accident Date</th>
                                                    <th>Requested User</th>
                                                    <th>Requested Date/Time</th>
                                                    <c:if test="${TYPE == 5 || TYPE == 6}">
                                                        <th style="width: 2%">Days From Request</th>
                                                    </c:if>
                                                    <c:if test="${TYPE ==4}">
                                                        <th>Assigned Assessor Code</th>
                                                        <th>Assigned Assessor Name</th>
                                                        <th>Assigned Datetime</th>
                                                        <th>Report Submitted Date/Time</th>
                                                        <th style="width: 2%">Days From Assignment</th>
                                                    </c:if>
                                                    <c:if test="${TYPE ==5}">
                                                        <th class="min-mobile"></th>
                                                        <th class="min-mobile"></th>
                                                    </c:if>
                                                    <th>Status</th>
                                                    <c:if test="${TYPE ==6}">
                                                        <th class="min-mobile"></th>
                                                        <th class="min-mobile"></th>
                                                    </c:if>
                                                    <c:if test="${TYPE ==4}">
                                                        <th class="min-mobile"></th>
                                                        <th class="min-mobile"></th>
                                                        <th class="min-mobile"></th>
                                                    </c:if>
                                                </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialogView" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h6 class="modal-title">Revoke ARI</h6>

                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group row">
                                                    <label class="col-sm-4 col-form-label"> Revoke
                                                        Reason </label>
                                                    <div class="col-sm-8">
                                                        <textarea name="remark" id="remark"
                                                                  class="form-control form-control-sm"
                                                                  placeholder="Revoke Reason"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-12 text-right">
                                                <c:if test="${G_USER.accessUserType ne 105}">
                                                    <button class="btn btn-primary" type="button" id="revokeBtn"
                                                            onclick="revoke()">
                                                        Revoke
                                                    </button>
                                                </c:if>
                                                <button class="btn btn-secondary" type="button" id="closeBtn"
                                                        onclick="closePage();">
                                                    Close
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <%--                        Discontinued with New ARI Process--%>
                        <%--                        <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"--%>
                        <%--                             id="emailConform" aria-hidden="true" style="    background: #333333c2;">--%>
                        <%--                            <input type="hidden" id="callType" name="callType"/>--%>
                        <%--                            <div class="modal-dialog modal-lg modal-dialog-centered">--%>
                        <%--                                <div class="modal-content p-2" style="overflow: hidden">--%>
                        <%--                                    <div class="modal-header  p-2">--%>
                        <%--                                        <h6 class="modal-title"--%>
                        <%--                                            id="modalLabel">Inform Document Upload User</h6>--%>
                        <%--                                    </div>--%>
                        <%--                                    <div class=" mt-4">--%>
                        <%--                                        <div class="col-sm-6 offset-3">--%>
                        <%--                                            <div class="form-group row">--%>
                        <%--                                                <label name="rej" id="rej" class="col-sm-4 col-form-label"> Reject--%>
                        <%--                                                    Reason--%>
                        <%--                                                </label>--%>
                        <%--                                                <div class="col-sm-8">--%>
                        <%--                                                    <select name="rejReason" id="rejReason"--%>
                        <%--                                                            class="form-control form-control-sm">--%>
                        <%--                                                        <option value="0">Please select</option>--%>
                        <%--                                                        <option value="Customer Phone/Mobile Not Contactable">Customer’s--%>
                        <%--                                                            Phone/Mobile Not Contactable--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Customer Phone/mobile Did not answer">Customer’s--%>
                        <%--                                                            Phone/mobile Didn’t answer--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Vehicle Not Available">Vehicle Not Available--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="salvage Not Available">salvage Not Available--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Repairs Not Completed">Repairs Not Completed--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Customer still not provide Vehicle & salvage for Inspection">--%>
                        <%--                                                            Customer still not provide Vehicle & salvage for Inspection--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Wrong Salvage Produced">Wrong Salvage Produced--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Wrong Customer Contact">Wrong Customer Contact--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Given locations wrong">Given locations wrong--%>
                        <%--                                                        </option>--%>
                        <%--                                                        <option value="Other">Other</option>--%>
                        <%--                                                    </select>--%>
                        <%--                                                    <div class="text-danger" id="reasonErrorDiv" style="display: none">--%>
                        <%--                                                        Please Enter Remark--%>
                        <%--                                                    </div>--%>
                        <%--                                                </div>--%>
                        <%--                                                <label name="" id="" class="col-sm-4 col-form-label"> Remark--%>
                        <%--                                                </label>--%>
                        <%--                                                <div class="col-sm-8">--%>
                        <%--                                                    <textarea type="text" class="form-control form-control-sm"--%>
                        <%--                                                              placeholder="Remark"--%>
                        <%--                                                              name="ariRemark"--%>
                        <%--                                                              id="ariRemark" value=""></textarea>--%>
                        <%--                                                </div>--%>
                        <%--                                            </div>--%>
                        <%--                                        </div>--%>
                        <%--                                    </div>--%>
                        <%--                                    <div class="modal-footer p-1">--%>
                        <%--                                        <button type="button" class="btn btn-secondary" data-dismiss="modal"--%>
                        <%--                                                id="emailBtn"--%>
                        <%--                                                onclick="emailSending();">--%>
                        <%--                                            Send--%>
                        <%--                                        </button>--%>
                        <%--                                    </div>--%>
                        <%--                                </div>--%>
                        <%--                            </div>--%>
                        <%--                        </div>--%>
                    </div>
                </div>
            </div>
        </div>
        <c:if test="${successMessage!=null && successMessage!=''}">
            <script type="text/javascript">
                notify('${successMessage}', "success");
            </script>
        </c:if>
        <c:if test="${errorMessage!=null && errorMessage!=''}">
            <script type="text/javascript">
                notify('${errorMessage}', "danger");
            </script>
        </c:if>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/callcenter/request-ari-list-datatables.js?v6"></script>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });

    function revoke() {
        document.getElementById('frmForm').action = contextPath + "/RequestAriController/updateRevoke?TYPE=${TYPE}";
        document.getElementById('frmForm').submit();
    }

    function closePage() {
        document.getElementById('frmForm').action = contextPath + "/RequestAriController/ariListView?TYPE=${TYPE}";
        document.getElementById('frmForm').submit();
    }

    // Discontinued with new ARI Process
    // function emailSending() {
    //     const rejReason = $("#rejReason").val();
    //     const remark = $("#ariRemark").val();
    //
    //     if (rejReason == '' || rejReason == "0") {
    //         notify("Please Provide a Reason for Rejection", "danger");
    //     } else {
    //         const URL = contextPath + "/RequestAriController/sendAriPendingMail";
    //         let formData = $('#frmForm');
    //         $.ajax({
    //             url: URL,
    //             type: 'POST',
    //             data: formData.serialize(),
    //             success: function (result) {
    //                 if (JSON.parse(result) == "SUCCESS") {
    //                     notify("Message Successfully Sent to Branch User", "success");
    //                 } else {
    //                     notify("Failed to Send the Message", "danger");
    //                 }
    //             }
    //         });
    //         $("#rejReason").val(0);
    //         $("#ariRemark").val("")
    //     }
    // }

    $("#txtRequestDate").datetimepicker({
        format: 'YYYY-MM-DD',
        //  maxDate:new Date(currentDate),
        icons: {
            date: "fa fa-calendar",
            up: "fa fa-arrow-up",
            down: "fa fa-arrow-down"
        }
    });
</script>
</body>
</html>
