<%--
  Created by IntelliJ IDEA.
  User: thanura
  Date: 12/26/2019
  Time: 11:30 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<div class="card mt-2">
    <div class="card-header">
        <h5 class="float-left m-0 text-uppercase">Claim Process Flow Details</h5>
    </div>

    <div class="card-body">
        <%--<c:forEach var="claimProcessFlowDto"--%>
        <%--items="${claimSuperDashboardDto.claimProcessFlowDtos}">--%>
        <%--<c:set var="calculationProcessFlowDtoList"--%>
        <%--value="${claimCalculationSheetDto.calculationProcessFlowDtos}"/>--%>

        <div class="row">
            <div class="col-md-12 border-left border-right">
                <a href="#" class="list-group-item list-group-item-action flex-column align-items-start">
                    <div class="float-left" style="width: 100%;">
                        <hr class="m-0 mt-1 mb-1">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-0">Assigned Claim Handler</p>
                                <p class="mb-1"><b>${claimSuperDashboardDto.claimHandlerDto.assignUserId}</b></p>
                            </div>
                            <div class="col-md-6 border-left">
                                <p class="mb-0">Assigned Initial Liability User</p>
                                <p class="mb-1"><b>${claimSuperDashboardDto.claimHandlerDto.initLiabilityAssignUserId}</b></p>
                            </div>
                        </div>
                        <hr class="m-0 mt-1 mb-1">
                    </div>
                    <div class="clearfix"></div>
                </a>
                <div class="row">
                    <div class="col text-left align-self-center">
                        <table width="100%" cellpadding="0" cellspacing="1"
                               class="table table-hover table-xs dataTable no-footer dtr-inline">
                            <thead>
                            <tr>
                                <th>Task Description</th>
                                <th>Performed By</th>
                                <th>Assigned To</th>
                                <th>Date/Time</th>
                                <th width="150px">TAT</th>
                            </tr>
                            </thead>
                            <tbody>

                            <tr class="text-left">
                                <td colspan="1"><i
                                        class="fa fa-circle-o text-success pl-5 ml-1"></i>
                                </td>
                            </tr>
                            <tr class="text-left">
                                <td colspan="1"><i
                                        class="fa fa-arrow-down text-success pl-5 ml-1"></i>
                                </td>
                            </tr>
                            <c:forEach var="claimProcessFlowDto"
                                       items="${claimSuperDashboardDto.claimProcessFlowDtos}">
                                <tr>
                                    <td>${claimProcessFlowDto.task}</td>
                                    <td>${claimProcessFlowDto.inpUserId}</td>
                                    <td>${claimProcessFlowDto.assignUserId}</td>
                                    <td>${claimProcessFlowDto.taskCompletedDateTime eq '1980-01-01 00:00:00' ? '':claimProcessFlowDto.taskCompletedDateTime}</td>
                                    <td class="align-middle text-center">
                                        <label class="m-0 badge  badge-info px-3">${claimProcessFlowDto.tat}</label>
                                    </td>
                                </tr>
                                <tr class="text-left">
                                    <td colspan="1"><i
                                            class="fa fa-arrow-down text-success pl-5 ml-1"></i>
                                    </td>
                                </tr>
                            </c:forEach>
                            <c:choose>
                                <c:when test="${claimCalculationSheetDto.status == 67}">
                                    <tr class="text-left">
                                        <td colspan="1"><i
                                                class="fa fa-dot-circle-o text-success pl-5 ml-1"></i>
                                        </td>
                                    </tr>
                                </c:when>
                                <c:otherwise>
                                    <tr class="text-left">
                                        <td colspan="1"><i
                                                class="fa fa-dot-circle-o text-warning pl-5 ml-1"></i>
                                        </td>
                                    </tr>
                                </c:otherwise>
                            </c:choose>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-2 align-self-center">
                <div class="row">
                    <div class="col text-center align-self-center">

                    </div>
                </div>
            </div>
        </div>
        <hr>
        <form name="frmSuperDashboard" id="frmSuperDashboard" method="post">
            <input type="hidden" name="P_N_CLIM_NO" id="txtSuperDashboardClaimNo"/>
            <a onclick="viewSuperDashboard(${claimsDto.claimNo})" id="viewSuperDashBoardBtn"
               class="btn btn-info ml-2 float-right"><b>Super Dashboard</b>
            </a>
        </form>
    </div>
</div>
<script>
    function viewSuperDashboard(claimNo) {
        $("#txtSuperDashboardClaimNo").val(claimNo);
        document.getElementById("frmSuperDashboard").target = "_blank";
        document.getElementById('frmSuperDashboard').action = contextPath + "/DashboardController/viewSuperDashboard";
        document.getElementById('frmSuperDashboard').submit();
    }
</script>
