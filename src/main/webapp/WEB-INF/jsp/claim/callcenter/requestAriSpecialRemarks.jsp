<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON><PERSON>
  Date: 5/6/2018
  Time: 8:13 PM
  To change this template use File | Settings | File Templates.
--%>

<%--<c:choose>--%>
    <%--<c:when test="${FORM_TYPE!='HISTORY'}">--%>
        <%--<c:set var="claimsDto" value="${claimsDto}" scope="session"/>--%>
    <%--</c:when>--%>
    <%--<c:when test="${FORM_TYPE=='HISTORY'}">--%>
        <%--<c:set var="claimsDto" value="${historyClaimsDto}" scope="request"/>--%>
    <%--</c:when>--%>
<%--</c:choose>--%>

<%@taglib prefix="c" uri="jakarta.tags.core" %>
<c:forEach var="spclRemark" items="${remarkList}">
    <a href="#"
       class="list-group-item list-group-item-action flex-column align-items-start">
        <div class="font-bg log-left"
             style="width: 50px; height:50px; overflow: hidden;">
            <h2 class="name text-white">${spclRemark.inputUser}</h2>
        </div>
        <div class="float-left log-right">
            <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">${spclRemark.sectionName}</h5>

            </div>
            <p class="mb-1"
               style="word-break: break-all;">${spclRemark.remark}</p>
            <hr class="m-0 mt-1 mb-1">
            <h6 class="float-right">${spclRemark.inputUser} </h6>
            <p class="float-left">${spclRemark.inputDatetime}</p>
        </div>
        <div class="clearfix"></div>
    </a>
</c:forEach>
