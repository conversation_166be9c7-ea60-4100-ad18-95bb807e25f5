<%--
    Document   : claimList
    Created on : Feb 11, 2011, 12:01:25 PM
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@page import="com.misyn.mcms.dbconfig.DbRecordCommonFunction" %>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);

    } catch (Exception e) {
    }

    String ERROR = "";
    String str_v_status_popList = DbRecordCommonFunction.getInstance().
            getPopupList("claim_status_para ", "n_ref_id", "v_status_desc", "v_type IN(0,1)", "");
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="SHORTCUT ICON" href="${pageContext.request.contextPath}/image/favico.png"/>
    <link href="${pageContext.request.contextPath}/resources/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/css/jquery-ui.css">
    <link href="${pageContext.request.contextPath}/resources/css/ScrollTabla.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/datatables.min.css" rel="stylesheet" type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${pageContext.request.contextPath}/resources/css/custom.css" rel="stylesheet" type="text/css"/>
<%--    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/jquery.min.js"></script>--%>
<%--    <script src="${pageContext.request.contextPath}/resources/js/jquery-ui.js"></script>--%>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/resources/js/datatables.min.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/dataTables.fixedHeader.min.js"></script>

    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>

    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var currentDate = '${Current_Date}';
        $(function () {
            $("#txtFromDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                //  maxDate:new Date(currentDate),
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#txtToDate").datetimepicker({
                sideBySide: true,
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $('#txtFromDate').on('dp.change', function (e) {
                $("#txtToDate").data("DateTimePicker").minDate(e.date);
                $("#txtToDate").data("DateTimePicker").maxDate(currentDate);
            });

            $("#txtFromDate").data("DateTimePicker").maxDate(currentDate);

            $("#txtToDate").data("DateTimePicker").maxDate(currentDate);

        });

        function init() {
            parent.document.getElementById("cell1").style.display = "none";
            parent.document.getElementById("loading").style.display = "none";
            document.getElementById("txtPolNumber").focus();
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }
    </script>
</head>
<body class="scroll" onload="init();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
        <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <c:choose>
                    <c:when test="${TYPE eq 3}">
                        <h5>Assigned Inspection List-Technical coordinator</h5>
                    </c:when>
                    <c:otherwise>
                        <h5> Accidents Details List</h5>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <div class="ErrorNote"><%=ERROR%>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">

                                        <div class="form-group row">
                                            <label for="txtFromDate" class="col-sm-4 col-form-label"> From Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtFromDate" class="form-control form-control-sm"
                                                       placeholder="From Date" id="txtFromDate" type="text"
                                                       value="${searchFromDate}"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle /
                                                Trade Plate Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtVehicleNumber" id="txtVehicleNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Vehicle / Trade Plate Number"
                                                       value="${searchVehicleNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtClaimNumber" class="col-sm-4 col-form-label"> Claim
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtClaimNumber" id="txtClaimNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Claim Number"
                                                       value="${searchClaimNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtPolNumber" id="txtPolNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="Policy Number"
                                                       value="${searchPolicyNo}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtInsuredName" class="col-sm-4 col-form-label"> Insured
                                                Name</label>
                                            <div class="col-sm-8">
                                                <input name="txtInsuredName" id="txtInsuredName"
                                                       class="form-control form-control-sm" placeholder="Insured Name"
                                                       value="${searchInsuredName}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtV_status" class="col-sm-4 col-form-label"> Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtV_status" id="txtV_status"
                                                        class="form-control form-control-sm"><%out.print(str_v_status_popList);%>
                                                </select>
                                                <script>
                                                    $("#txtV_status").val("${searchClaimStatus==null?'0':searchClaimStatus}");
                                                </script>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtFollowupCallDone" class="col-sm-4 col-form-label">Follow up
                                                Call Complete Status</label>
                                            <div class="col-sm-8">
                                                <select name="txtFollowupCallDone" id="txtFollowupCallDone"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <option value="Y">Yes</option>
                                                    <option value="N">No</option>
                                                </select>
                                                <script>
                                                    $("#txtFollowupCallDone").val("${searchFollowupCallDone==null?'0':searchFollowupCallDone}");
                                                </script>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtCliNumber" class="col-sm-4 col-form-label">CLI Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtCliNumber" id="txtCliNumber" type="text"
                                                       class="form-control form-control-sm" placeholder="CLI Number"
                                                       value="${searchCliNo}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="cmbpolicyChannelType" class="col-sm-4 col-form-label">Policy
                                                Channel
                                                Type </label>
                                            <div class="col-sm-8">
                                                <select name="policyChannelType" id="cmbpolicyChannelType"
                                                        class="form-control form-control-sm">
                                                    <option value="0">Please Select</option>
                                                    <option value="TAKAFUL">TAKAFUL</option>
                                                    <option value="CONVENTIONAL">CONVENTIONAL</option>
                                                </select>
                                                <script>
                                                    $("#cmbpolicyChannelType").val("${searchPolicyChannelType==null?'0':searchPolicyChannelType}");
                                                </script>
                                            </div>
                                        </div>


                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtToDate" class="col-sm-4 col-form-label"> To Input
                                                Date/Time </label>
                                            <div class="col-sm-8">
                                                <input name="txtToDate" id="txtToDate" type="text"
                                                       class="form-control form-control-sm" placeholder="To Date"
                                                       value="${searchToDate}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Cover Note
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input name="txtRefNumber" id="txtRefNumber"
                                                       class="form-control form-control-sm"
                                                       placeholder="Cover Note Number" value="${searchRefNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtInsuredNic" class="col-sm-4 col-form-label"> Insured
                                                NIC </label>
                                            <div class="col-sm-8">
                                                <input name="txtInsuredNic" id="txtInsuredNic"
                                                       class="form-control form-control-sm" placeholder="Insured NIC"
                                                       value="${searchInsuredNic}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtEngineNo" class="col-sm-4 col-form-label"> Engine
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtEngineNo" id="txtEngineNo"
                                                       class="form-control form-control-sm" placeholder="Engine Number"
                                                       value="${searchEngineNo}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtChassisNo" class="col-sm-4 col-form-label"> Chassis
                                                Number</label>
                                            <div class="col-sm-8">
                                                <input name="txtChassisNo" id="txtChassisNo"
                                                       class="form-control form-control-sm"
                                                       placeholder="Chassis Number" value="${searchChassisNo}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtLocation" class="col-sm-4 col-form-label"> Location</label>
                                            <div class="col-sm-8">
                                                <input name="txtLocation" id="txtLocation"
                                                       class="form-control form-control-sm" placeholder="Location"
                                                       value="${searchLocation}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtCallUserName" class="col-sm-4 col-form-label">User
                                                Name</label>
                                            <div class="col-sm-8">
                                                <input name="txtCallUserName" id="txtCallUserName"
                                                       class="form-control form-control-sm" placeholder="User Name"
                                                       value="${searchUserName}">
                                            </div>
                                        </div>
                                        <%--                                        <div class="form-group row">--%>
                                        <%--                                            <label for="txtOtherContactNumber" class="col-sm-4 col-form-label">Other Contact No</label>--%>
                                        <%--                                            <div class="col-sm-8">--%>
                                        <%--                                                <input name="txtOtherContactNumber" id="txtOtherContactNumber" type="text"--%>
                                        <%--                                                       class="form-control form-control-sm" placeholder="Other Contact Number"--%>
                                        <%--                                                       value="${searchOtherContactNo}">--%>
                                        <%--                                            </div>--%>
                                        <%--                                        </div>--%>
                                        <div class="form-group row">
                                            <label for="txtISFClaimNumber" class="col-sm-4 col-form-label">ISF Claim
                                                No</label>
                                            <div class="col-sm-8">
                                                <input name="txtISFClaimNumber" id="txtISFClaimNumber" type="text"
                                                       class="form-control form-control-sm"
                                                       placeholder="ISF Claim Number"
                                                       value="${searchISFClaimNo}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                                onclick="search()">Search
                                        </button>
                                        <a class="btn btn-secondary" type="button" name="cmdClose"
                                           id="cmdClose" href="${pageContext.request.contextPath}/welcome.do">Close
                                        </a>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="but_cont" style="float:right">
        </div>
        <div class="card mt-3">
            <div class="card-body table-bg">
                <input type="hidden" value="${TYPE}" name="type" id="type">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="badge badge-pill badge-priority border ">&nbsp;</span>&nbsp;&nbsp;
                                    Priority High
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="badge badge-pill badge-light border ">&nbsp;</span>&nbsp;&nbsp; Draft
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="badge badge-pill badge-dark border ">&nbsp;</span>&nbsp;&nbsp; Draft &
                                    Assigned
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="badge badge-pill badge-success border">&nbsp;</span> &nbsp;&nbsp;Assigned
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="badge badge-pill badge-danger border">&nbsp;</span>&nbsp;&nbsp;Incorrect
                                    Record
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="badge badge-pill badge-secondary border">&nbsp;</span>&nbsp;&nbsp;Forwarded
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><span
                                        class="badge badge-pill badge-warning border">&nbsp;</span>&nbsp;&nbsp;Assign
                                    Pending
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-danger"></i>&nbsp;&nbsp;Late
                                </p>
                            </div>
                            <div class="col">
                                <p class="mb-1 float-right"><i class="fa fa-warning text-warning"></i> &nbsp;&nbsp; On
                                    Site</p>
                                <%--<h6>Policy Suspend &nbsp;&nbsp;<span class="badge badge-pill badge-secondary border">&nbsp;</span></h6>--%>
                            </div>
                        </div>
                        <hr class="my-2">
                        <div class="">
                            <div class="">
                                <div class="mt-2 ">
                                    <h6>Claim Result</h6>
                                    <div class="mt-2" style="overflow-x: auto">
                                        <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                               style="cursor:pointer">
                                            <thead>
                                            <tr>
                                                <th>ref no</th>
                                                <th width="40px">No</th>
                                                <th>Claim No</th>
                                                <th>ISF No</th>
                                                <th>CLI No</th>
                                                <th>Policy No</th>
                                                <th>Vehicle No</th>
                                                <th>Chassis No</th>
                                                <th>User Name</th>
                                                <th>Accident Date</th>
                                                <th>Accident Time</th>
                                                <th>Cover Note Number</th>
                                                <th>Informer</th>
                                                <th>Reported Date</th>
                                                <th>Reported Time</th>
                                                <th>Location</th>
                                                <th>Policy Channel Type</th>
                                                <th>Status</th>
                                                <%--                                                <th>Other Contact No</th>--%>
                                                <th class="min-mobile"></th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                             aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <%--<h6 class="modal-title" id="exampleModalLabel">${CompanyTitle} Lanka PLC.</h6>--%>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <i class="fa fa-info-circle fa-5x text-info"></i>
                                            </div>
                                        </div>
                                        <p id="dialog-email" class="mt-5 text-muted"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/callcenter/claim-datatables.js?v5"></script>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").val('');
        $("#txtToDate").val('');
    });
</script>
</body>
</html>
