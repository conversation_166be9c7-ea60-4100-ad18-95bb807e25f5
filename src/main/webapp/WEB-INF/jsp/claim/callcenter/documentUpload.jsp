<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<%@ taglib prefix="fn" uri="jakarta.tags.functions" %>
<!DOCTYPE HTML>

<html>
<head>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/assessorallocation/assessor-allocation.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/script/jquery_js/jquery.popupWindow.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/assessorallocation/assessor-form-validations.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.iframe-transport.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/jquery.fileupload.js"></script>
    <script src="${pageContext.request.contextPath}/resources/file-upload/js/vendor/jquery.ui.widget.js"></script>
    <script>
        var documentUploadIds = [];
        $(document).ready(function () {
            $('[data-toggle="tooltip"]').tooltip({
                html: true,
                template: '<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
            });
        });
    </script>
</head>
<body>
<div class="container-fluid">

    <c:choose>
        <c:when test="${PREVIOUS_INSPECTION!='Y' || PREVIOUS_INSPECTION == null}">
            <c:set var="claimUploadViewDtoList" value="${claimUploadViewDtoList}" scope="session"/>
        </c:when>
        <c:when test="${PREVIOUS_INSPECTION=='Y' }">
            <c:set var="claimUploadViewDtoList" value="${historyClaimUploadViewDtoList}" scope="request"/>
        </c:when>
    </c:choose>
    <div class="row">
        <input type="hidden" id="inspectionType" name="inspectionType" value="${inspectionType}">
        <c:set var="documentTypeId" scope="request" value="0"/>
        <c:set var="index" scope="request" value="1"/>
        <c:forEach var="claimUploadViewDto" items="${claimUploadViewDtoList}">
            <c:if test="${documentTypeId!=claimUploadViewDto.claimDocumentTypeDto.documentTypeId}">
                <c:choose>
                    <c:when test="${claimUploadViewDto.claimDocumentTypeDto.isMandatory=='Y'}">
                        <c:set var="iconColorCls" value=" text-light "/>
                        <c:set var="headerColorCls" value=" bg-badge-danger "/>
                    </c:when>
                    <c:otherwise>
                        <c:set var="iconColorCls" value=" text-primary "/>
                        <c:set var="headerColorCls" value=" bg-badge-primary "/>
                    </c:otherwise>
                </c:choose>
                <fieldset class="col-md-12 border mt-2">
                <div class="row">

                <div class="col-sm-12 col-form-label ${headerColorCls} ">
                    <h6 class="float-left">${index}
                        - ${claimUploadViewDto.claimDocumentTypeDto.documentTypeName}</h6>
                    <c:if test="${PREVIOUS_INSPECTION != 'Y'}">
                        <button id="cmdUpload" type="button" name="cmdUpload" class="btn btn-primary pull-right"
                                onclick="docUpload(${claimUploadViewDto.claimDocumentTypeDto.documentTypeId})">
                            Upload Here
                        </button>
                    </c:if>
                </div>
                <c:set var="index" scope="request" value="${index+1}"/>
                <c:set var="claimDocumentTypeDto" value="${claimUploadViewDto.claimDocumentTypeDto}"/>
                <form name="frmDocumentModal${claimDocumentTypeDto.documentTypeId}"
                      id="frmDocumentModal${claimDocumentTypeDto.documentTypeId}">
                    <input type="hidden" name="documentTypeId"
                           value="${claimDocumentTypeDto.documentTypeId}">
                    <input type="hidden" id="claimId" name="claimNo" value="${claimUploadViewDto.claimNo}">
                    <input type="hidden" name="jobRefNo" value="0">
                    <input type="hidden" name="departmentId" value="5">
                    <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
                         id="docUploadModal${claimDocumentTypeDto.documentTypeId}" aria-hidden="true"
                         style="    background: #333333c2;">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content p-2" style="overflow: hidden">
                                <div class="modal-header  p-2">
                                    <h6 class="modal-title"
                                        id="modalLabel${claimDocumentTypeDto.documentTypeId}">${claimDocumentTypeDto.documentTypeName} </h6>
                                    <small class="text-danger pull-right"><b> .PNG / .JPG / .PDF File Formats
                                        Only.</b>
                                    </small>
                                </div>
                                <p id="errorUpload${claimDocumentTypeDto.documentTypeId}"></p>
                                <div class=" mt-4">
                                    <div class="col-sm-12">
                                        <!-- The fileinput-button span is used to style the file input field as button -->
                                        <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                            <!-- The file input field used as target for the file upload widget -->
                                        <input id="fileUploadClaim${claimDocumentTypeDto.documentTypeId}" type="file"
                                               name="files[]" multiple>
                                    </span>
                                        <!-- The global progress bar -->
                                        <div id="progressClaim${claimDocumentTypeDto.documentTypeId}"
                                             class="progress">
                                            <div class="progress-bar bg-success"></div>
                                        </div>
                                        <!-- The container for the uploaded files -->
                                        <div id="filesClaim${claimDocumentTypeDto.documentTypeId}"
                                             class="files"></div>
                                        <br>
                                    </div>
                                </div>
                                <div class="modal-footer p-1">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                            onclick="closeUploadDocumets();">
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
                <script>
                    documentUploadIds.push('${claimDocumentTypeDto.documentTypeId}');
                </script>

                <div class="col-md-12">
                <hr class="my-2">
            </c:if>
            <c:set var="cnt" value="1"/>
            <c:forEach var="claimDocumentDto" items="${claimUploadViewDto.claimDocumentDtoList}">
                <c:set var="iconColorCls" value=" text-dark "/>
                <c:if test="${claimDocumentDto.documentStatus=='A'}">
                    <c:set var="iconColorCls" value=" text-success"/>
                </c:if>
                <c:if test="${claimDocumentDto.documentStatus=='H'}">
                    <c:set var="iconColorCls" value=" text-warning"/>
                </c:if>
                <c:if test="${claimDocumentDto.documentStatus=='R'}">
                    <c:set var="iconColorCls" value=" text-danger"/>
                </c:if>
                <c:if test="${claimDocumentDto.documentStatus=='C'}">
                    <c:set var="iconColorCls" value=" text-info"/>
                </c:if>
                <div class="uploadfile-delet ">
                    <a href="${pageContext.request.contextPath}/AssessorAllocationController/viewDocumentViewer?refNo=${claimDocumentDto.refNo}&jobRefNo=${claimDocumentDto.jobRefNo}&PREVIOUS_INSPECTION=${PREVIOUS_INSPECTION}"
                       class="claimView${claimDocumentDto.refNo} ${iconColorCls}"
                       data-toggle="tooltip" title="${claimDocumentDto.toolTip}">
                        <i class="fa fa-file-pdf-o fa-4x m-3"></i>
                    </a>
                </div>
                <script type="text/javascript">
                    $('.claimView${claimDocumentDto.refNo}').popupWindow({
                        height: screen.height,
                        width: screen.width,
                        centerBrowser: 0,
                        left: 0,
                        resizable: 1,
                        centerScreen: 1,
                        scrollbars: 1,
                        windowName: 'claimHandler${claimDocumentDto.refNo}'
                    });
                </script>

            </c:forEach>

            <c:if test="${documentTypeId!=claimUploadViewDto.claimDocumentTypeDto.documentTypeId}">
                </div>
                </div>
                </fieldset>
            </c:if>
            <c:set var="documentTypeId" scope="request"
                   value="${claimUploadViewDto.claimDocumentTypeDto.documentTypeId}"/>
        </c:forEach>
    </div>
</div>

<c:if test="${successMessage!=null && successMessage!=''}">
<script type="text/javascript">
    window.parent.notify('${successMessage}', "success");
</script>
</c:if>
<c:if test="${errorMessage!=null && errorMessage!=''}">
<script type="text/javascript">
    window.parent.notify('${errorMessage}', "danger");
</script>
</c:if>
<script type="text/javascript">


    function docUpload(documentTypeId) {
        $('#docUploadModal' + documentTypeId).modal({
            backdrop: 'static',
            keyboard: false,
            refresh: true,
            show: true
        });
    }

    function documentFileUploder(documentTypeId) {
        'use strict';
        var progress = 0;
        var url = '${pageContext.request.contextPath}/DocumentUploadController';
        $('#fileUploadClaim' + documentTypeId).fileupload({

            url: url,
            dataType: 'json',
            add: function (e, data) {
                data.submit()
            },
            done: function (e, data) {
                $.each(data.files, function (index, file) {
                    $('<i class="fa fa-file-pdf-o fa-4x m-3"></i>').appendTo('#filesClaim' + documentTypeId);
                });
                $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorUpload' + documentTypeId).removeClass("bg-danger");
                $('#errorUpload' + documentTypeId).addClass("bg-success");
                $('#errorUpload' + documentTypeId).html("");
                $('#errorUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center" >Document Uploaded Successfully!</span>').appendTo('#errorUpload' + documentTypeId);
                $('#errorUpload' + documentTypeId).fadeOut(4000);

            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);

                $('#progressClaim' + documentTypeId + ' .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            fail: function (e, data) {
                $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                $('#errorUpload' + documentTypeId).removeClass("bg-success");
                $('#errorUpload' + documentTypeId).addClass("bg-danger");
                $('#errorUpload' + documentTypeId).html("");
                $('#errorUpload' + documentTypeId).fadeIn();
                $('<span class="text-light d-block p-1 text-center">Document Upload failed.</span>').appendTo('#errorUpload' + documentTypeId);
                $('#errorUpload' + documentTypeId).fadeOut(4000);
            }, change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {

                    $('#progressClaim' + documentTypeId + ' .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#errorUpload' + documentTypeId).removeClass("bg-primary");
                    $('#errorUpload' + documentTypeId).removeClass("bg-danger");
                    $('#errorUpload' + documentTypeId).removeClass("bg-success");

                    $('#errorUpload' + documentTypeId).addClass("bg-primary");
                    $('#errorUpload' + documentTypeId).html("");
                    $('#errorUpload' + documentTypeId).fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#errorUpload' + documentTypeId);


                });
            }
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    }

    documentUploadIds.forEach(function (t) {
        documentFileUploder(t)
    });


    function closeUploadDocumets() {
        var claim = $('#claimId').val();
        var inspectionType = $('#inspectionType').val();
        location.href = contextPath + "/AssessorAllocationController/documentUpload?P_N_CLIM_NO=" + claim + "&inspectionType=" + inspectionType;

    }


</script>
<%--&lt;%&ndash;</body>--%>
<%--</html>&ndash;%&gt;--%>
