<%--
  Created by IntelliJ IDEA.
  User: Asiri
  Date: 3/13/2018
  Time: 4:34 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/resources/js/custom/callcenter/third-party-validation.js"></script>
    <c:choose>
        <c:when test="${FORM_TYPE!='HISTORY'}">
            <c:set var="claimsDto" value="${claimsDto}" scope="session"/>
        </c:when>
        <c:when test="${FORM_TYPE=='HISTORY'}">
            <c:set var="claimsDto" value="${historyClaimsDto}" scope="request"/>
        </c:when>
    </c:choose>
</head>
<body class="scroll">
<div class="col">
    <h6> Third Party Details</h6>
    <hr class="my-1">
    <form name="tpForm" id="tpForm">
        <div class="form-group row">
            <label class="col-sm-4 col-form-label">If Third Party Involved:</label>
            <div class="col-sm-8">
                <div class="row">
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                        <input name="thirdPartyInvolved" type="radio" class="align-middle"
                               value="Y" ${thirdPartyDto.thirdPartyInvolved=='Y'?'checked':''}
                               onclick="disable_3rd_Party_details('Y')"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-3 col-form-label check-container">
                        <input name="thirdPartyInvolved" type="radio" class="align-middle"
                               value="N" ${thirdPartyDto.thirdPartyInvolved=='N'?'checked':''}
                               onclick="disable_3rd_Party_details('N')"/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description">No</span>
                    </label>
                </div>
            </div>
        </div>
        <script>
            //            disable_3rd_Party_details('Y');
            <%--disable_3rd_Party_details('${thirdPartyDto.thirdPartyInvolved}');--%>
        </script>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label disable text-mute">Type of Loss :</label>
            <div class="col-sm-8">
                <select class=" form-control form-control-sm disable text-mute" id="lossType" name="lossType" disabled>
                    ${DbRecordCommonFunctionBean.getPopupList("claim_loss_type", "N_ID", "V_CAUSE_OF_LOSS")}
                </select>
            </div>
            <script>
                $("#lossType").val("${thirdPartyDto.lossType}");
            </script>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label disable text-mute">Item :</label>
            <div class="col-sm-8">
                <select class=" form-control form-control-sm disable  text-mute" id="itemType" name="itemType" disabled
                        onchange="vehicleNoFieldStatChange(this.value)">
                    ${DbRecordCommonFunctionBean.getPopupList("tp_vehicle_item_mst", "N_ID", "V_ITEM_DES")}
                </select>
            </div>
            <script>
                $("#itemType").val("${thirdPartyDto.itemType}");
            </script>
        </div>
        <div class="form-group row" id="vehicalDisable">
            <label class="col-sm-4 col-form-label disable text-mute">Vehicle Number :</label>
            <div class="col-sm-8">
                <input class=" form-control form-control-sm disable text-mute" type="text"
                       value="${thirdPartyDto.vehicleNo}" name="vehicleNo"
                       id="vehicleNo" disabled/>
                <span class="text-danger" id="vehicalAvailability" style="display: none;"><b>This Vehicle LOLC Insured.</b></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label disable text-mute">TP Intends To Claim:</label>
            <div class="col-sm-8">
                <div class="row radio">
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-2 col-form-label  check-container">
                        <input name="intendClaim" type="radio" value="Y" class="align-middle disable"
                        ${thirdPartyDto.intendClaim=='Y'?'checked':''} disabled/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description disable  text-mute">Yes</span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-2 col-form-label check-container">
                        <input name="intendClaim" type="radio" value="N" class="align-middle disable "
                        ${thirdPartyDto.intendClaim=='N'?'checked':''} disabled/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description disable  text-mute">No</span>
                    </label>
                    <label class="custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col-sm-5 col-form-label check-container">
                        <input name="intendClaim" type="radio" value="WD" class="align-middle disable "
                        ${thirdPartyDto.intendClaim=='WD'?'checked':''} disabled/>
                        <span class="radiomark"></span>
                        <span class="custom-control-description disable  text-mute">Want to decide</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label disable text-mute">Remarks :</label>
            <div class="col-sm-8">
            <textarea name="remark" id="remark" class=" form-control form-control-sm disable text-mute" title="Remarks"
                      cols="" disabled
                      rows="3">${thirdPartyDto.remark}</textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-4 col-form-label"></label>
            <c:if test="${FORM_TYPE!='HISTORY'}">
                <div class="col-sm-8">
                    <button type="submit" name="addbtn" id="addbtn" value="add" class="btn btn-primary "
                            disabled>Add
                    </button>
                </div>
            </c:if>
        </div>
        <div class="row">
            <div class="col">
                <table width="100%" cellpadding="0" cellspacing="1"
                       class="table table-hover table-sm dataTable no-footer dtr-inline " id="TPdata">
                    <thead>
                    <tr>
                        <th scope="col" class="tbl_row_header">Type of Loss</th>
                        <th scope="col" class="tbl_row_header">Item</th>
                        <th scope="col" class="tbl_row_header">Vehicle No.</th>
                        <th scope="col" class="tbl_row_header">TP Intend to Claim</th>
                        <th scope="col" class="tbl_row_header">Remark</th>
                        <th scope="col" class="tbl_row_header">Actions</th>
                    </tr>
                    </thead>
                    <c:forEach var="entry" items="${claimsDto.thirdPartyDtoMap}">
                        <tbody>
                        <td>${DbRecordCommonFunctionBean.getValue("claim_loss_type","V_CAUSE_OF_LOSS", "N_ID", entry.value.lossType)}</td>
                        <td>${DbRecordCommonFunctionBean.getValue("tp_vehicle_item_mst", "V_ITEM_DES", "N_ID", entry.value.itemType)}</td>
                        <td>${entry.value.vehicleNo}</td>
                        <td>${entry.value.intendClaim == 'Y' ? 'Yes' : entry.value.intendClaim== 'N' ? 'No' : 'Want to decide'}</td>
                        <td>${entry.value.remark}</td>
                        <td class="text-center">
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary loopbtn" title="Edit"
                                        onClick="processThirdParty('E','${entry.key}');"><i class='fa fa-edit'></i>
                                </button>
                                <button type="button" class="btn btn-primary loopbtn" title="Delete"
                                        onClick="processThirdParty('D','${entry.key}');"><i class='fa fa-trash'></i>
                                </button>
                            </div>
                        </td>
                        </tbody>
                    </c:forEach>
                </table>
            </div>
        </div>
        <input type="reset" name="index" id="reset" style="visibility: hidden;">
        <input type="hidden" name="index" id="index" value="${index}">
    </form>
</div>
<script>

    $(document).ready(function () {
        var formType = parent.document.getElementById("formType").value || "MASTER";
        if ("HISTORY" == formType) {
            $('#addbtn').attr('disabled', 'disabled');
            $('.loopbtn').attr('disabled', 'disabled');
            $('[name="thirdPartyInvolved"]').attr('disabled', 'disabled');
        }
    });

    function processThirdParty(type, index) {
        var URL = contextPath + "/Claim/thirdPartySave?index=" + $("#index").val();
        var message = "Successfully added to the temporally queue";
        switch (type) {
            case 'D':
                URL = contextPath + "/Claim/thirdPartyRemove?index=" + index;
                message = "Successfully deleted from the temporally queue";
                break;
            case 'E':
                window.location.href = contextPath + "/CallCenter/viewThirdPartyDetails?index=" + index;
                message = "Successfully updated from the temporally queue";
                break;
        }

        if (type != 'E') {
            var $form = $('#tpForm');
            $.ajax({
                url: URL,
                type: 'POST',
                data: $form.serialize(),
                success: function (result) {
                    if (result.errorCode == 200) {
                        window.location.href = contextPath + "/CallCenter/viewThirdPartyDetails?P_N_CLIM_NO=" + parent.document.getElementById("claimNo").value;
                        window.parent.assesoraddRemark(message, 'success');
                    } else {
                        window.parent.AlertErrorTirdprty(result.message, 'danger');
                        $("[name='" + result.dtoFieldName + "']").focus().addClass('error');
                    }
                }
            });
        }
    }

    $('#vehicleNo').focusout(function () {
        $.ajax({
            url: contextPath + "/CallCenter/searchVehicle?vehicleNo=" + this.value,
            type: 'POST',
            success: function (result) {
                if (result == 'YES') {
                    $('#vehicalAvailability').show()
                } else {
                    $('#vehicalAvailability').hide()
                }
            }
        });
    });

    function resetForm() {
        $("select.disable").val("0");
        $("textarea , input[type='text']").val("");
        $("[type='radio'].disable").prop("checked", false);
    }

    function disable_3rd_Party_details(stat) {
        if (stat == "N") {
            resetForm()
            $(".disable").addClass("text-mute").prop('disabled', true);
            $("#addbtn").prop('disabled', true);
        }
        else {
            $(".disable").removeClass("text-mute").attr('disabled', false);
            $("#addbtn").prop('disabled', false);
            vehicleNoFieldStatChange($('#itemType').val());

        }

    }


    function vehicleNoFieldStatChange(val) {
        if (1 == val) {
            $("#vehicleNo").prop('disabled', false);
        } else {
            $("#vehicleNo").prop('disabled', true).val("");
        }
    }

    $(function () {
        if ('${thirdPartyDto.thirdPartyInvolved}' == 'Y') {
            disable_3rd_Party_details('Y');
        }
    })
</script>
</body>
</html>
