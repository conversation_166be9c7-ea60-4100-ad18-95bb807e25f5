<%--
  Created by IntelliJ IDEA.
  User: HP
  Date: 6/19/2024
  Time: 11:40 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
</head>
<body style="padding: 10px">

<div id="accordion" class="accordion">
    <div class="card">
        <div class="card-header" id="headingOne">
            <h5 class="mb-0">
                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                   aria-expanded="true" aria-controls="collapseOne">
                    Search Here <i class="fa fa-search"></i>
                </a>
            </h5>
        </div>
        <div id="collapseOne" class="collapse show p-3" aria-labelledby="headingOne"
             data-parent="#accordion">

            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                            data-target="#addModal"><i class="fa fa-plus"></i> Add Task
                    </button>
                </div>
            </div>


            <div class="row mt-3">
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Task ID</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm ">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Task Name</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control form-control-sm ">
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right">Search</button>
                    <button type="button" class="btn btn-primary float-right mr-1">Clear</button>
                </div>
            </div>

            <!-- Edit Modal -->
            <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editModalLabel">Edit Task</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editForm">
                                <div class="form-group">
                                    <label for="taskName">Task Name</label>
                                    <input type="text" class="form-control" id="taskName">
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveChanges">Update
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Modal -->
            <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addModalLabel">Add Task</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="taskName">Task Name</label>
                                <input type="text" id="taskNameVal" class="form-control">
                            </div>
                            <button type="button" id="saveDetail" class="btn btn-primary float-right">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card mt-3">
        <div class="card-body table-bg">
            <div class="row">
                <div class="col-lg-12 pl-0 pr-0">
                    <table id="example" class="table table-sm table-striped table-bordered" style="width:100%">
                        <thead>
                        <tr>
                            <th>No</th>
                            <th>Task ID</th>
                            <th>Created Date</th>
                            <th>Updated Date</th>
                            <th>Task Name</th>
                            <th>Input User</th>
                            <th>Edit</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>1</td>
                            <td>T12345</td>
                            <td>2024-06-19</td>
                            <td>2024-06-20</td>
                            <td>Sample Task 1</td>
                            <td>User A</td>
                            <td style="text-align: center">
                                <button class="btn btn-primary btn-sm edit-btn" data-toggle="modal"
                                        data-target="#editModal" style="width: auto">
                                    <i class="fa fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>T67890</td>
                            <td>2024-06-18</td>
                            <td>2024-06-19</td>
                            <td>Sample Task 2</td>
                            <td>User B</td>
                            <td style="text-align: center">
                                <button class="btn btn-primary btn-sm edit-btn" data-toggle="modal"
                                        data-target="#editModal" style="width: auto">
                                    <i class="fa fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    $(document).ready(function () {
        $('#example').DataTable({
            fixedHeader: true,
            searching: false
        });
        $('#example').on('click', '.edit-btn', function () {
            var currentRow = $(this).closest('tr');
            var taskName = currentRow.find('td:eq(4)').text();
            $('#editModal #taskName').val(taskName);
        });

        $('#saveChanges').click(function () {
            var taskName = $('#taskName').val();
            $('#editModal').modal('hide');
        });
        $('#saveDetail').click(function () {
            console.log("save")
            var URL = "${pageContext.request.contextPath}/ClaimFlowDetailController/save-update";
            var taskName = $('#taskNameVal').val();
            $.ajax({
                url: URL,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ taskName: taskName }),
                success: function(response) {
                    console.log('Data saved successfully:', response);
                    notify("Task saved successfully","success")
                },
                error: function(xhr, status, error) {
                    console.error('Error saving data:', error);
                }
            });
            $('#addModal').modal('hide');
        });
        hideLoader();
    });
</script>
</body>
</html>
