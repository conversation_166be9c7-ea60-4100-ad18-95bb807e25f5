<%-- 
    Document   : notificationView
    Created on : Apr 5, 2012, 1:37:52 PM
    Author     : <PERSON><PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <%--<jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>--%>
    <script type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
    </script>
    <title></title>
    <script type="text/javascript">
        function goNotificationPage(url) {
            document.getElementById("frmForm").target = "imain_frm";
            document.frmForm.action = url;
            document.frmForm.submit();
        }
    </script>
    <c:set var="notificationDtoList" value="${notificationFormDto.notificationDtoList}" scope="request"/>
    <c:set var="notificationCount" value="${notificationFormDto.notificationCount}" scope="request"/>
    <c:set var="unreadNotificationCount" value="${notificationFormDto.unreadNotificationCount}" scope="request"/>
    <c:set var="index" value="1" scope="request"/>
</head>
<body style="position: relative" class="scroll">
<form action="" method="POST" name="frmForm" id="frmForm" target="imain_frm">
    <input name="P_POL_N_REF_NO" id="P_POL_N_REF_NO" type="hidden"/>
    <input name="P_N_CLIM_NO" id="P_N_CLIM_NO" type="hidden"/>
    <input name="P_N_REF_NO" id="P_N_REF_NO" type="hidden"/>
    <input name="P_N_NOTIFICATION_REF_NO" id="P_N_NOTIFICATION_REF_NO" type="hidden"/>
    <input type="hidden" name="notificationCount" id="notificationCount" value="${notificationCount}">
    <input type="hidden" name="unreadNotificationCount" id="unreadNotificationCount" value="${unreadNotificationCount}">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12" style="display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 !important;
    text-align: center;
    width: 100%;
    border-bottom: 1px solid #ffffff69;
    margin-bottom: 12px;
    padding-bottom: 4px !important;">
                <span style="color: #ffff;
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0.35px; text-align: center;
    width: 100%;">
                    Notifications
                </span>
                <div class="">
                    <div style="float: left;
    font-size: 16px;
    height: 20px;
    cursor: pointer;
    color: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ffffff63;
    padding: 8px;
    width: 20px;"
                         onClick="showNotification();"><i style="font-weight:400;" class="fa fa-times"></i>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="list-group scroll" style="height: calc(100vh - 135px)">
        <c:forEach var="notificationDto" items="${notificationDtoList}">
            <div href="#" class="list-group-item list-group-item-action flex-column align-items-start px-2 py-1  mb-2"
                 style="background-color: ${notificationDto.colorCode == "#FFFFFF" && notificationDto.readStatus == 'N' ? '#E9ECEF' : notificationDto.colorCode}">
                <div class="d-block w-100 pb-1">
                    <div class="d-flex text-right justify-content-between">
                        <c:choose>
                            <c:when test="${notificationDto.priorityStatus=='HIGH'}">
                                <i class="fa fa-exclamation-circle float-left text-danger" title="Priority High"></i>
                            </c:when>
                            <c:when test="${notificationDto.priorityStatus=='MED'}">
                                <i class="fa fa-exclamation-circle float-left text-warning" title="Priority Medium"></i>
                            </c:when>
                            <c:otherwise>
                                <i class="fa fa-exclamation-circle float-left text-info" title="Priority Low"></i>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${notificationDto.checkedStatus=='Y'}">
                                <span class="btn btn-success btn-xs  text-uppercase px-3"
                                      style="font-size: 10px; padding: 0 5px;">Checked</span>
                            </c:when>
                            <c:when test="${notificationDto.checkedStatus=='N' and notificationDto.readStatus=='Y'}">
                                <button class="btn btn-warning btn-xs text-uppercase px-3" type="button"
                                        style="font-size: 10px; padding: 0 5px;"
                                        onclick="updateStatus('${notificationDto.txnId}')">Pending
                                </button>
                            </c:when>
                        </c:choose>
                        <c:if test="${!((null == notificationDto.productName) || ('' == notificationDto.productName))}">
                        <span class="btn btn-success btn-xs  text-uppercase px-3"
                              style="font-size: 10px; padding: 0 5px;">${notificationDto.productName}</span>
                        </c:if>
                        <c:choose>
                            <c:when test="${notificationDto.policyChannelType=='TAKAFUL'}">
                                   <span class="btn btn-success btn-xs  text-uppercase px-3"
                                         style="font-size: 10px; padding: 0 5px;">${notificationDto.policyChannelType}</span>
                            </c:when>
                            <c:when test="${notificationDto.policyChannelType=='CONVENTIONAL'}">
                                  <span class="btn btn-primary btn-xs  text-uppercase px-3"
                                        style="font-size: 10px; padding: 0 5px;">${notificationDto.policyChannelType}</span>
                            </c:when>
                        </c:choose>
                        <i class="fa fa-times float-right" title="Close"
                           onclick="closeStatus('${notificationDto.txnId}')"></i>
                    </div>
                </div>
                <hr class="my-1"/>
                <a href="#"
                   onclick="pageLoad('${notificationDto.txnId}','${notificationDto.claimNo}','${notificationDto.refNo}','${notificationDto.url}')">
                    <div class=" float-left w-100">
                        <div class="d-flex w-100 justify-content-between">
                            <c:if test="${notificationDto.vehicleNo != ''}">
                                <h6 class="mb-1 float-left font-weight-normal">
                                    <small class="text-mute" style="font-size: 12px">V. No</small>
                                    <span class="text-info ">${notificationDto.vehicleNo}  | ${notificationDto.claimNo}</span>
                                </h6>
                            </c:if>

                            <c:if test="${notificationDto.vehicleNo == ''}">
                                <h6 class="mb-1 float-left font-weight-normal">
                                    <small class="text-mute" style="font-size: 12px">Cover Note No</small>
                                    <span class="text-info ">${notificationDto.coverNoteNo} ${notificationDto.categoryDesc} ${notificationDto.productName}  | ${notificationDto.claimNo}</span>
                                </h6>
                            </c:if>
                            <p class="mb-1 font-weight-bold float-right text-danger"
                               title="Accident Date">${notificationDto.accidentDate}</p>

                            <p class="mb-1 font-weight-normal float-right">
                                <small class="text-mute">User Name</small>
                                    ${notificationDto.inpUserId}</p>
                        </div>
                        <hr class="my-0">
                        <p class="mb-0">${notificationDto.message}</p>
                        <small>${notificationDto.ipAddress}</small>
                        <c:if test="${!((notificationDto.categoryDesc ) || (null == notificationDto.categoryDesc)) and notificationDto.categoryDesc == 'VIP'}">
                        <label class="text-danger float-left" style="font-weight: bold; font-size: large">${notificationDto.categoryDesc}</label>
                        </c:if>
                        <small class="text-primary float-right">${notificationDto.notifyDateTime}</small>
                    </div>
                </a>
            </div>
            <c:set var="index" value="${index=index+1}" scope="request"/>
        </c:forEach>
    </div>
    <div id="viewAllNotification" style="position: fixed; bottom: 14px; right: 8px; padding: 5px 10px; text-align: center; width: 450px; border-top: 1px solid #CCCCCC50; color: #333333">
        <a href="#" class="text-white">View All <i class="fa fa-angle-double-right"></i></a>
    </div>
    <script type="text/javascript">
        let today = new Date();
        let currentDateFormatted = today.toISOString().slice(0, 10);
        let threeMonthsAgoFormatted = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate() < 29 ? today.getDate() : 28).toISOString().slice(0,10);
        $('#viewAllNotification').click(function () {
            goNotificationPage('${pageContext.request.contextPath}/NotificationController/viewAllNotification?txtFromDate='+ threeMonthsAgoFormatted +'&txtToDate=' + currentDateFormatted);
            showNotification();
        });
    </script>
</form>
</body>
<script type="text/javascript">
    function pageLoad(notificationRefNo, claimNo, refNo, url) {
//        $(this).hasClass('bg-dark').removeClass('bg-dark');
        //claimNo=P_N_CLIM_NO
        //refNo=P_N_REF_NO

        if (url === '#') {
            return;
        }
        $("#P_N_NOTIFICATION_REF_NO").val(notificationRefNo);
        $.ajax({
            url: '${pageContext.request.contextPath}/NotificationController/updateNotification?P_N_NOTIFICATION_REF_NO=' + notificationRefNo,
            cache: false,
            success: function (data) {
                showNotification();
                decrementNotification();
                var form = document.getElementById('frmForm');
                form.target = "imain_frm";
                form.action = contextPath + url;
                form.submit();
                callNotificationTimer();
            }
        });
    }

    function updateStatus(notificationRefNo) {
        $("#P_N_NOTIFICATION_REF_NO").val(notificationRefNo);
        $.ajax({
            url: '${pageContext.request.contextPath}/NotificationController/deleteCheckedNotification?P_N_NOTIFICATION_REF_NO=' + notificationRefNo,
            cache: false,
            success: function (data) {
                callNotificationTimer();
            }
        });
    }

    function closeStatus(notificationRefNo) {
        $("#P_N_NOTIFICATION_REF_NO").val(notificationRefNo);
        $.ajax({
            url: '${pageContext.request.contextPath}/NotificationController/closeSidePanelNotification?P_N_NOTIFICATION_REF_NO=' + notificationRefNo,
            cache: false,
            success: function (data) {
                callNotificationTimer();
            }
        });
    }

</script>
</html>
