<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.List" %>
<%@ page import="com.misyn.mcms.claim.dto.ExcessDetailsDto" %><%--
  Created by IntelliJ IDEA.
  User: Chathura
  Date: 12/29/2023
  Time: 11:04 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%-- Policy Details Model --%>
<div class="modal fade" id="policyDetailsModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col">
                        <c:set var="premiumBreakupFormDto"
                               value="${claimsDto.policyDto.premiumBreakupFormDto}"
                               scope="request"/>
                        <c:set var="chargesBreakupDto"
                               value="${claimsDto.policyDto.premiumBreakupFormDto.chargesBreakupDto}"
                               scope="request"/>
                        <c:set var="productDetailListDto"
                               value="${claimsDto.policyDto.productDetailListDto}"
                               scope="request"/>

                        <ul class="nav nav-tabs flex-container" id="policyTabs" role="tablist">
                            <li class="nav-item" style="flex-grow: 1">
                                <a class="nav-link active text-center" id="serviceFactor-tab" data-toggle="tab"
                                   href="#serviceFactor" role="tab">Service Factors</a>
                            </li>

                            <li class="nav-item" style="flex-grow: 1">
                                <a class="nav-link text-center" id="cover-tab" data-toggle="tab" href="#cover"
                                   role="tab">Covers</a>
                            </li>

                            <li class="nav-item" style="flex-grow: 1">
                                <a class="nav-link text-center" id="benefit-tab" data-toggle="tab" href="#benefit"
                                   role="tab">Benefits</a>
                            </li>

                            <li class="nav-item" style="flex-grow: 1">
                                <a class="nav-link text-center" id="condition-tab" data-toggle="tab" href="#condition"
                                   role="tab">Conditions/Exclusions</a>
                            </li>

                            <li class="nav-item" style="flex-grow: 1">
                                <a class="nav-link text-center" id="special-package-tab" data-toggle="tab"
                                   href="#special" role="tab">Special Package</a>
                            </li>

                            <li class="nav-item" style="flex-grow: 1">
                                <a class="nav-link text-center" id="other-tab" data-toggle="tab" href="#other"
                                   role="tab">Other</a>
                            </li>

                            <li class="nav-item" style="flex-grow: 1">
                                <a class="nav-link text-center" id="memo-tab" data-toggle="tab" href="#memo" role="tab">Memo</a>
                            </li>

                        </ul>


                        <div class="tab-content mt-2">
                            <!-- Content for Service Factors tab -->
                            <div class="tab-pane fade show active  " id="serviceFactor" role="tabpanel">
                                <div class="table-responsive">
                                    <!-- Include the reusable memo table JSP and pass the dataList attribute -->
                                    <jsp:include page="/WEB-INF/jsp/claim/common/policyCoverTable.jsp">
                                        <jsp:param name="coverType" value="serviceData"/>
                                    </jsp:include>

                                </div>
                            </div>


                            <div class="tab-pane fade" id="cover" role="tabpanel">
                                <div class="table-responsive">

                                    <jsp:include page="/WEB-INF/jsp/claim/common/policyCoverTable.jsp">
                                        <jsp:param name="coverType" value="coverData"/>
                                    </jsp:include>
                                </div>
                            </div>


                            <div class="tab-pane fade  " id="benefit" role="tabpanel">
                                <div class="table-responsive">
                                    <jsp:include page="/WEB-INF/jsp/claim/common/policyCoverTable.jsp">
                                        <jsp:param name="coverType" value="benefitsData"/>
                                    </jsp:include>

                                </div>
                            </div>

                            <div class="tab-pane fade  " id="condition" role="tabpanel">
                                <div class="table-responsive">
                                    <jsp:include page="/WEB-INF/jsp/claim/common/policyCoverTable.jsp">
                                        <jsp:param name="coverType" value="conditionData"/>
                                    </jsp:include>

                                </div>
                            </div>

                            <div class="tab-pane fade  " id="special" role="tabpanel">
                                <div class="table-responsive">
                                    <jsp:include page="/WEB-INF/jsp/claim/common/policyCoverTable.jsp">
                                        <jsp:param name="coverType" value="specialData"/>
                                    </jsp:include>

                                </div>
                            </div>

                            <div class="tab-pane fade  " id="other" role="tabpanel">
                                <div class="table-responsive">

                                    <jsp:include page="/WEB-INF/jsp/claim/common/policyCoverTable.jsp">
                                        <jsp:param name="coverType" value="otherData"/>
                                    </jsp:include>

                                </div>
                            </div>

                            <!-- Content for Memo tab -->
                            <div class="tab-pane fade" id="memo" role="tabpanel">
                                <!-- Content specific to Memo tab -->
                                <div class="table-responsive">

                                    <!-- Include the reusable memo table JSP and pass the dataList attribute -->
                                    <jsp:include page="/WEB-INF/jsp/claim/common/policyMemoTable.jsp">
                                        <jsp:param name="memoType" value="memoData"/>
                                    </jsp:include>

                                </div>

                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="viewMoreDetailsModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body" style="background: #fdfdfd">
                <div class="row">
                    <div class="col">
                        <div style="font-size: 15px">
                            <p id="moreDetails"></p>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%-- Service Factor Model --%>
<div class="modal fade" id="serviceFactorModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col">
                        <div class="table-responsive">

                            <jsp:include page="/WEB-INF/jsp/claim/common/serviceFactorTable.jsp">
                                <jsp:param name="coverType" value="serviceData"/>
                            </jsp:include>

                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="excessDetailModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col">
                        <div class="table-responsive">
                            <jsp:include page="/WEB-INF/jsp/claim/common/excessDetailTable.jsp">
                                <jsp:param name="coverType" value="excessData"/>
                            </jsp:include>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var policyDataList;
    var serviceFactorName = "Not have a service factor";

    $(document).ready(function () {

        var polNumber = "${claimsDto.policyDto.policyNumber}";
        var polChannelType = "${claimsDto.policyDto.policyChannelType}";
        var end = "${claimsDto.policyDto.endCount}";
        var ren = "${claimsDto.policyDto.renCount}";


        var requestData = {
            polNumber: polNumber,
            polChannelType: polChannelType,
            renCount: ren,
            endCount: end
        };

        console.log("Policy Number: " + polNumber);
        console.log("Policy Channel Type: " + polChannelType);
        console.log("Endorsement Count: " + end);
        console.log("Renewal Count: " + ren);


        $.ajax({
            url: contextPath + "/PolicyDetailsController/policyDataList",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function (data) {

                console.log("Successfully")
                console.log(data)
                policyDataList = data;

                console.log(policyDataList)
                console.log(policyDataList.policyServiceDataList)
                console.log(policyDataList.policyCoverDataList)
                console.log(policyDataList.policyBenefitDataList)
                console.log(policyDataList.policyConditionDataList)
                console.log(policyDataList.policySpecialDataList)
                console.log(policyDataList.policyOtherDataList)
                console.log(policyDataList.policyMemoDataList)
                console.log(policyDataList.policyExcessDataList)
                if (policyDataList.policyServiceDataList && policyDataList.policyServiceDataList.length > 0) {
                    var serviceFactor = policyDataList.policyServiceDataList[0];
                    serviceFactorName = serviceFactor.name;
                    if (serviceFactorName) {
                        $("#service-factor-btn").text(serviceFactorName);
                        $("#service-factor-header").text(serviceFactorName);
                    }


                } else {
                    console.log("No elements in policyServiceDataList");

                }


            },
            error: function (error) {
                console.error("Error fetching data: " + error);
            }
        });

        function loadPolicyDetailsTable() {
            if (policyDataList && policyDataList.policyServiceDataList) {
                populateTable('serviceFactor', policyDataList.policyServiceDataList)
            }

        }

        function populateTable(tableId, dataList) {

            $('#' + tableId + ' tbody').empty();
            var redRows = [];
            var otherRows = [];


            $.each(dataList, function (index, item) {
                var rowStyle = (item.deleted) ? 'style="color: red;text-align: center"' : 'style="text-align: center"';
                var buttonId = tableId + index;
                var buttonClass = (item.deleted) ? 'btn btn-danger callViewMore' : 'btn btn-primary callViewMore';

                var buttonHtml = '';
                if (item.type === 'CWE') {
                    buttonHtml = '<button id="' + buttonId + '" type="button" class="' + buttonClass + '" data-toggle="modal" data-target="#viewMoreDetailsModal" data-extra-info="' + item.more + '" style="justify-content: center" name="callViewMore">' +
                        '<i class="fas fa-info-circle"></i>' +
                        '</button>';
                }


                var tableRowHtml = '<tr ' + rowStyle + '>' +
                    '<td>' + item.type + '</td>' +
                    '<td>' + item.code + '</td>' +
                    '<td>' + item.description + '</td>' +
                    '<td>' + item.amount + '</td>' +
                    '<td>' + buttonHtml + '</td>' +
                    '</tr>';

                if (item.deleted) {
                    redRows.push(tableRowHtml);
                } else {
                    otherRows.push(tableRowHtml);
                }

            });

            $('#' + tableId + ' tbody').append(otherRows.concat(redRows));


        }

        function populateMemoTable(tableId, dataList) {
            $('#' + tableId + ' tbody').empty();

            var redRows = [];
            var otherRows = [];

            $.each(dataList, function (index, item) {
                var rowStyle = (item.deleted) ? 'style="color: red;text-align: center"' : 'style="text-align: center"';
                var buttonId = tableId + index;
                var buttonClass = (item.deleted) ? 'btn btn-danger callViewMore' : 'btn btn-primary callViewMore';

                var buttonHtml = '';

                buttonHtml = '<button id="' + buttonId + '" type="button" class="' + buttonClass + '" data-toggle="modal" data-target="#viewMoreDetailsModal" data-extra-info="' + item.more + '" style="justify-content: center" name="callViewMore">' +
                    '<i class="fas fa-info-circle"></i>' +
                    '</button>';


                var tableRowHtml = '<tr ' + rowStyle + '>' +
                    '<td>' + item.no + '</td>' +
                    '<td>' + item.memo + '</td>' +
                    '<td>' + item.date + '</td>' +
                    '<td>' + item.exclusion + '</td>' +
                    '<td>' + buttonHtml + '</td>' +
                    '</tr>';

                if (item.deleted) {
                    redRows.push(tableRowHtml);
                } else {
                    otherRows.push(tableRowHtml);
                }
            });

            $('#' + tableId + ' tbody').append(otherRows.concat(redRows));
        }


        function handleTabClick(tabId, coverType) {
            console.log(tabId + " tab clicked");
            switch (tabId) {
                case 'serviceFactor-tab':
                    populateTable(coverType, policyDataList.policyServiceDataList);
                    break;
                case 'cover-tab':
                    populateTable(coverType, policyDataList.policyCoverDataList);
                    break;
                case 'benefit-tab':
                    populateTable(coverType, policyDataList.policyBenefitDataList);
                    break;
                case 'condition-tab':
                    populateTable(coverType, policyDataList.policyConditionDataList);
                    break;
                case 'special-package-tab':
                    populateTable(coverType, policyDataList.policySpecialDataList);
                    break;
                case 'other-tab':
                    populateTable(coverType, policyDataList.policyOtherDataList);
                    break;
                case 'memo-tab':
                    populateMemoTable(coverType, policyDataList.policyMemoDataList);
                    break;
            }
        }

        function setupTabListener(tabId, coverType) {
            $('#' + tabId).on('shown.bs.tab', function () {
                handleTabClick(tabId, coverType);
            });
        }


        function setupEventListeners() {
            setupTabListener('serviceFactor-tab', 'servicefactor');
            setupTabListener('cover-tab', 'cover');
            setupTabListener('benefit-tab', 'benefit');
            setupTabListener('condition-tab', 'condition');
            setupTabListener('special-package-tab', 'special');
            setupTabListener('other-tab', 'other');
            setupTabListener('memo-tab', 'memo');

            $('#policyDetailsModal').on('show.bs.modal', function (event) {
                loadPolicyDetailsTable();
            });
            $('#viewMoreDetailsModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var extraInfo = button.data('extra-info');


                $('#moreDetails').text(extraInfo);
            });

            $('#serviceFactorModal').on('show.bs.modal', function (event) {

                if (policyDataList && policyDataList.policyServiceDataList) {

                    $('#service-factor-tb tbody').empty();


                    $.each(policyDataList.policyServiceDataList, function (index, item) {
                        var tableRowHtml = '<tr style="text-align: center;">' +
                            '<td class="font-weight-bold" style="background-color: lightgray;">' + index + '</td>' +
                            '<td>' + item.description + '</td>' +
                            '</tr>';


                        $('#service-factor-tb tbody').append(tableRowHtml);
                    });
                } else {
                    console.error('No data available for the service factor table.');
                }
            });


            $('#excessDetailModal').on('show.bs.modal', function (event) {

                if (policyDataList && policyDataList.policyExcessDataList) {

                    $('#excess-tb tbody').empty();
                    var total = 0;

                    $.each(policyDataList.policyExcessDataList, function (index, item) {
                        console.log("Came");
                        console.log(item);


                        var rowStyle = 'style="text-align: center"';


                        var tableRowHtml = '<tr ' + rowStyle + '>' +
                            '<td>' + item.code + '</td>' +
                            '<td>' + item.excessDesc + '</td>' +
                            '<td>' + item.excessAmount + '</td>' +
                            '</tr>';

                        total = total + item.excessAmount;


                        $('#excess-tb tbody').append(tableRowHtml);
                    });

                    var totalRowHtml = '<tr style="background-color: lightgray; text-align: center;">' +
                        '<td class="font-weight-bold" colspan="2">Excess Amount</td>' +
                        '<td class="font-weight-bold" >' + total + '</td>' +
                        '</tr>';


                    $('#excess-tb tbody').append(totalRowHtml);
                } else {
                    console.error('No data available for the excess table.');
                }
            });


        }

        setupEventListeners();
    });


</script>


<script>
    $(document).ready(function () {

        // Attach an event listener to the document for a custom event triggered by the iframe
        $(document).on('underwritingModalShowEvent', function () {
            $("#excessDetailModal").modal("show"); // Trigger the modal in policymodal.jsp

        });
    });
</script>
