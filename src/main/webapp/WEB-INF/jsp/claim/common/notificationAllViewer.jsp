<%-- 
    Document   : notificationAllView
    Created on : Apr 5, 2012, 1:37:52 PM
    Author     : <PERSON><PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title></title>
    <script type="text/javascript">
        function goPage(url) {
            var m_url = url;
            document.getElementById("frmForm").target = "imain_frm";
            document.frmForm.action = m_url;
            document.frmForm.submit();
            // alert("HEllo"+url);
        }

        function goNotificationPage(url) {
            document.getElementById("frmForm").target = "imain_frm";
            document.frmForm.action = url;
            document.frmForm.submit();
        }


        function callStausUpdate(id, n_txn_id, url) {
            var TimeURL = new Date().getTime();

            var val = 0;
            $.ajax({
                url: '/notificationUpdateResult.jsp?' + TimeURL + '&P_N_TXN_ID=' + n_txn_id, cache: false,
                success: function (data) {
                    val = data;
                    try {
                        document.getElementById("DIV_STATUS" + id).style.display = "none";
                        goPage(url);
                    } catch (e) {
                    }

                }
            });
        }

        function searchAllNotificationView() {
            var url = '${pageContext.request.contextPath}/NotificationController/viewAllNotification';
            document.getElementById("frmForm").target = "imain_frm";
            document.frmForm.action = url;
            document.frmForm.submit();
        }


    </script>
    <c:set var="notificationDtoList" value="${notificationFormDto.notificationDtoList}" scope="request"/>
    <c:set var="notificationCount" value="${notificationFormDto.notificationCount}" scope="request"/>
    <c:set var="index" value="1" scope="request"/>
</head>
<body style="position: relative" class="scroll">
<form action="" method="POST" name="frmForm" id="frmForm">
    <div class="container-fluid">
        <div class="row header-bg mb-2 py-2 bg-dark align-items-center bg-dark">
            <div class="col-sm-7">
                <h5 class="hide-sm m-0">All Notification</h5>
                <div class="clearfix"></div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-6 col-sm-12 ">
                        <div class="form-group row">
                            <label for="txtFromDate" class="col-md-4 col-form-label"> From Date :</label>
                            <div class="col-md-8">
                                <input name="txtFromDate" class="form-control form-control-sm"
                                       placeholder="From Date" id="txtFromDate" type="text" value="${fromDate}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12 ">
                        <div class="form-group row">
                            <label for="txtToDate" class="col-md-4 col-form-label"> To Date :</label>
                            <div class="col-md-8">
                                <input name="txtToDate" id="txtToDate" type="text"
                                       class="form-control form-control-sm"
                                       placeholder="To Date" value="${toDate}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12 ">
                        <div class="form-group row">
                            <label for="" class="col-md-4 col-form-label"> Vehicle No. :</label>
                            <div class="col-md-8">
                                <input name="vehicleNo" type="text"
                                       class="form-control form-control-sm"
                                       placeholder="Vehicle Number" value="${vehicleNo}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12 ">
                        <div class="form-group row">
                            <label for="" class="col-md-4 col-form-label"> Claim No. </label>
                            <div class="col-md-8">
                                <input name="claimNo" id="" type="text"
                                       class="form-control form-control-sm"
                                       placeholder=" Claim Number" value="${claimNo}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-right">
                        <hr>
                        <button class="btn btn-primary" type="button" name="cmdSearch" id="cmdSearch"
                                onclick="searchAllNotificationView()">Search
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6 ">
                <input type="hidden" name="notificationCount" id="notificationCount" value="${notificationCount}">
                <div class="list-group scroll " style="height: calc(100vh - 115px)">
                    <c:forEach var="notificationDto" items="${notificationDtoList}">
                        <div href="#"
                             class="list-group-item list-group-item-action flex-column align-items-start  px-2 py-1 mb-1 ${notificationDto.readStatus=='N'?'bg-dark':''}">
                            <div class="d-block w-100 pb-1">
                                <div class="d-flex text-right justify-content-between">
                                    <c:choose>
                                        <c:when test="${notificationDto.priorityStatus=='HIGH'}">
                                            <i class="fa fa-exclamation-circle float-left text-danger"
                                               title="Priority High"></i>
                                        </c:when>
                                        <c:when test="${notificationDto.priorityStatus=='MED'}">
                                            <i class="fa fa-exclamation-circle float-left text-warning"
                                               title="Priority Medium"></i>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="fa fa-exclamation-circle float-left text-info"
                                               title="Priority Low"></i>
                                        </c:otherwise>
                                    </c:choose>
                                    <c:choose>
                                        <c:when test="${notificationDto.checkedStatus=='Y'}">
                                <span class="btn btn-success btn-xs  text-uppercase px-3"
                                      style="font-size: 10px; padding: 0 5px;">Checked</span>
                                        </c:when>
                                        <c:when test="${notificationDto.checkedStatus=='C'}">
                                <span class="btn btn-danger btn-xs  text-uppercase px-3"
                                      style="font-size: 10px; padding: 0 5px;">Closed</span>
                                        </c:when>
                                        <c:when test="${notificationDto.checkedStatus=='N' and notificationDto.readStatus=='Y'}">
                                            <button class="btn btn-warning btn-xs text-uppercase px-3" type="button"
                                                    style="font-size: 10px; padding: 0 5px;"
                                                    onclick="updateStatus('${notificationDto.txnId}')">Pending
                                            </button>
                                        </c:when>
                                    </c:choose>

                                    <c:if test="${!((null == notificationDto.productName) || ('' == notificationDto.productName))}">
                                        <span class="btn btn-success btn-xs  text-uppercase px-3"
                                              style="font-size: 10px; padding: 0 5px;">${notificationDto.productName}</span>
                                    </c:if>

                                    <c:choose>
                                        <c:when test="${notificationDto.policyChannelType=='TAKAFUL'}">
                                            <span class="btn btn-success btn-xs  text-uppercase px-3"
                                                  style="font-size: 10px; padding: 0 5px;">${notificationDto.policyChannelType}</span>
                                        </c:when>
                                        <c:when test="${notificationDto.policyChannelType=='CONVENTIONAL'}">
                                  <span class="btn btn-primary btn-xs  text-uppercase px-3"
                                        style="font-size: 10px; padding: 0 5px;">${notificationDto.policyChannelType}</span>
                                        </c:when>
                                    </c:choose>
                                    <i class="fa fa-times float-right" title="Close"
                                       onclick="closeStatus('${notificationDto.txnId}')"></i>
                                </div>
                            </div>
                            <hr class="my-1">
                            <a href="#"
                               onclick="pageLoad('${notificationDto.txnId}','${notificationDto.claimNo}','${notificationDto.refNo}','${notificationDto.url}')">
                                <div class=" float-left w-100">
                                    <div class="d-flex w-100 justify-content-between">
                                        <c:if test="${notificationDto.vehicleNo != ''}">
                                            <h6 class="mb-1 float-left font-weight-normal">
                                                <small class="text-mute" style="font-size: 12px">Vehicle No</small>
                                                <span class="text-info ">${notificationDto.vehicleNo} | ${notificationDto.claimNo}</span>
                                            </h6>
                                        </c:if>
                                        <c:if test="${notificationDto.vehicleNo == ''}">
                                            <h6 class="mb-1 float-left font-weight-normal">
                                                <small class="text-mute" style="font-size: 12px">Cover Note No</small>
                                                <span class="text-info ">${notificationDto.coverNoteNo} | ${notificationDto.claimNo}</span>
                                            </h6>
                                        </c:if>
                                        <p class="mb-1 font-weight-bold float-right text-danger"
                                           title="Accident Date">${notificationDto.accidentDate}</p>
                                        <p class="mb-1 font-weight-normal float-right">
                                            <small class="text-mute">User Name</small>
                                                ${notificationDto.inpUserId}</p>
                                    </div>
                                    <hr class="my-0">
                                    <p class="mb-0">${notificationDto.message}</p>
                                    <small>${notificationDto.ipAddress}</small>
                                    <c:if test="${!((notificationDto.categoryDesc ) || (null == notificationDto.categoryDesc)) and notificationDto.categoryDesc == 'VIP'}">
                                        <label class="text-danger float-left" style="font-weight: bold; font-size: large">${notificationDto.categoryDesc}</label>
                                    </c:if>
                                    <small class="text-primary float-right">${notificationDto.notifyDateTime}</small>
                                </div>
                            </a>
                        </div>
                        <c:set var="index" value="${index=index+1}" scope="request"/>
                    </c:forEach>
                </div>
                <div class="w-100">
                    <div class="float-right  pr-1"><a class="btn btn-secondary text-white mt-2"
                                                      onClick="window.location.href='${pageContext.request.contextPath}/welcome.do'">Back</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
</body>
<script type="text/javascript">
    $(function () {
        $("#txtFromDate").datetimepicker({
            format: 'YYYY-MM-DD',
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
            }
        });

        $("#txtToDate").datetimepicker({
            format: 'YYYY-MM-DD',
            icons: {
                time: "fa fa-clock-o",
                date: "fa fa-calendar",
            }
        });
    });

    function pageLoad(notificationRefNo, claimNo, refNo, url) {
        $(this).removeClass('bg-dark');
        //claimNo=P_N_CLIM_NO
        //refNo=P_N_REF_NO
        if (url === '#') {
            return;
        }

        $("#P_N_NOTIFICATION_REF_NO").val(notificationRefNo);
        $.ajax({
            url: '${pageContext.request.contextPath}/NotificationController/updateNotification?P_N_NOTIFICATION_REF_NO=' + notificationRefNo,
            cache: false,
            success: function (data) {

                var form = document.getElementById('frmForm');
                form.target = "imain_frm";
                form.action = contextPath + url;
                form.submit();
            }
        });
    }

    function updateStatus(notificationRefNo) {
        $("#P_N_NOTIFICATION_REF_NO").val(notificationRefNo);
        $.ajax({
            url: '${pageContext.request.contextPath}/NotificationController/deleteCheckedNotification?P_N_NOTIFICATION_REF_NO=' + notificationRefNo,
            cache: false,
            success: function (data) {
                searchAllNotificationView();
            }
        });
    }

    function closeStatus(notificationRefNo) {
        $("#P_N_NOTIFICATION_REF_NO").val(notificationRefNo);
        $.ajax({
            url: '${pageContext.request.contextPath}/NotificationController/closeSidePanelNotification?P_N_NOTIFICATION_REF_NO=' + notificationRefNo,
            cache: false,
            success: function (data) {
                searchAllNotificationView();
            }
        });
    }

</script>
</html>
