<%@ page import="com.misyn.mcms.utility.AppConstant" %><%--
  Created by IntelliJ IDEA.
  User: Chathura
  Date: 3/14/2024
  Time: 10:14 AM
  To change this template use File | Settings | File Templates.
--%>
<%--<%@ page contentType="text/html;charset=UTF-8" language="java" %>--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html>
<head>
    <script src="${pageContext.request.contextPath}/resources/js/common/payment-card.js"></script>
    <c:set var="payeeIdValue" value="${param.payeeIdValue}"/>
    <c:set var="payeeDescValue" value="${param.payeeDescValue}"/>
</head>
<body>
<div id="cardContainer">

    <div class="card" id="paymentCard" style="display: none;">
        <div id="paymentCardHeader" class="card-header">
            <button type="button" onclick="addCard()" name="" value="BR"
                    class="btn-add btn btn-primary ml-2">
                Add
            </button>
            <button type="button" onclick="removeCard(this)" name="" value="BR"
                    class="btn-remove btn btn-primary ml-2">
                Remove
            </button>
        </div>
        <div class="card-body">
            <label id="cardIndex" class="cardId" style="display: none">0</label>
            <label id="isUpdate" class="isUpdate" style="display: none">N</label>
            <label id="isVerify" class="isVerify" style="display: none">N</label>
            <label id="isUpload" class="isUpload" style="display: none">N</label>
            <br>
            <label id="refIndex" class="refIndex" style="display: none">0</label>
            <fieldset class="border p-2 mx-2">
                <div class="form-group row has-feedback">
                    <label class="col-sm-4 col-form-label">Instrument Type :</label>
                    <div class="col-sm-8">
                        <select name="instrumentType" id="instrumentType"
                                class="instrumentType form-control form-control-sm"
                                data-fv-field="instrumentType">
                        </select>
                    </div>
                </div>
                <div class="form-group row has-feedback">
                    <label class="col-sm-4 col-form-label">Payee Type :</label>
                    <div class="col-sm-8">
                        <select name="payeeType" id="payeeType"
                                class="payeeType form-control form-control-sm"
                                data-fv-field="payeeType">
                        </select>
                    </div>
                </div>
                <div class="form-group row has-feedback">
                    <label class="col-sm-4 col-form-label">Payee Name :</label>
                    <div class="col-sm-8">
                        <select name="payeeName" id="payeeName"
                                class="payeeName form-control form-control-sm"
                                data-fv-field="payeeName">
                        </select>
                    </div>
                </div>

                <div class="mt-3 mb-2 row">
                    <div class="col-sm-6 text-left">
                        <button id="btn-view" type="button" name="" value="BR" class="btn-view btn btn-primary"
                                style="display: none" onclick="previewDoc(this)">
                            <i class="fa fa-file-pdf-o fa-2x m-1"></i>
                        </button>

                        <button id="btn-check" type="button" name="" value="BR" class="btn-check btn btn-success ml-2"
                                style="display: none">
                        </button>
<%--                        <button id="btn-check" type="button" name="" value="BR" class="btn-check btn btn-danger ml-2"--%>
<%--                                style="display: none">--%>
<%--                            <i class="fa fa-times-circle-o fa-2x m-1"></i>--%>
<%--                        </button>--%>
                    </div>

                    <div class="col-sm-6 text-right">
                        <button id="btn-upload" type="button" name="" value="BR"
                                class="btn-upload btn btn-primary ml-2" onclick="uploadFile(this)">
                            Upload
                        </button>


                        <button id="btn-save" type="button" name="" value="BR"
                                class="btn-save btn btn-primary ml-2" onclick="saveDetails(this)">
                            Save
                        </button>
                    </div>
                </div>

                <c:if test="${param.isVerifyUser eq 'Y'}">
                    <div class="text-right mb-2">
                        <input id="checkbox-verify" class="checkbox-verify checkbox" type="checkbox">
                        <button id="btn-verify" type="button" name="" value="BR"
                                class="btn-verify btn btn-primary ml-2" onclick="verifyDetails(this)">
                            Verify
                        </button>
                    </div>
                </c:if>
                <c:if test="${param.isSpecialTeamUser eq 'Y'}">
                    <div class="text-right mb-2">
                        <input id="checkbox-reject" class=" checkbox-reject checkbox" type="checkbox">
                        <button id="btn-reject" type="button" name="" value="BR"
                                class="btn-reject btn btn-danger ml-2" onclick="rejectDetails(this)">
                            Reject
                        </button>
                    </div>
                </c:if>
            </fieldset>


        </div>

        <form name="frmDocumentModal"
              id="frmDocumentModal" ENCTYPE="multipart/form-data">
            <div class="paymentModal modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
                 id="paymentModal" aria-hidden="true"
                 style="    background: #333333c2;">
                <input type="hidden" name="documentTypeId" value="<%= AppConstant.BANK_DETAILS_DOCUMENT_TYPE_ID %>">
                <input type="hidden" name="claimNo" value="${claimHandlerDto.claimsDto.claimNo}">
                <input type="hidden" name="jobRefNo" value="0">
                <input type="hidden" name="departmentId" value="5">
                <input type="hidden" name="requestFormId" value="${requestFormId}">
                <input type="hidden" name="instrumentType" value="0">
                <input type="hidden" name="payeeType" value="0">
                <input type="hidden" name="payeeName" value="0">
                <input type="hidden" name="cardId" value="0">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content p-2" style="overflow: hidden">
                        <div class="modal-header  p-2">
                            <label id="modalIndex" class="modalId" style="display: none">0</label>
                            <%--                    <label id="refIndex" class="refId">0</label>--%>
                            <h6 class="modal-title"
                            >Bank Details </h6>
                            <small class="text-danger pull-right"><b> .PNG / .JPG / .PDF File Formats
                                Only.</b>
                            </small>
                        </div>
                        <p id="errorUpload"></p>
                        <div class=" mt-4">
                            <div class="col-sm-12">
                <span class="btn btn-success fileinput-button p-5 float-left mr-3 mb-3">
                                        <i class="fa fa-plus"></i>
                                        <span>Select files...</span>
                                        <input id="fileUploadClaim" type="file"
                                               name="files[]" multiple accept=".png, .jpg, .pdf">
                                    </span>
                                <div id="progressClaim"
                                     class="progress">
                                    <div class="progress-bar bg-success"></div>
                                </div>
                                <div id="filesClaim"
                                     class="files"></div>
                                <br>
                            </div>
                        </div>
                        <div class="modal-footer p-1">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"
                                    onclick="closeModal(this);">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>


    </div>


</div>


<script>

    var instrumentTypeList = null;
    var payeeTypeListMaster = null;
    var initialCardIndex = 0;
    var searchPayeeTypeList;
    var searchPayeeTypeID;
    var searchPayeeNameList;
    var searchPayeeNameID;
    var isVerifyUser;
    var isSpecialTeamUser;
    var isBranchUser;
    var claimNo;
    var endCount;
    var policyNumber;
    var policyChannelType;
    var customerName;
    var customerNIC;
    var calSheetId;
    var URL_INSTRUMENT;
    var URL_PAYEE_TYPE;
    var URL_PREV;
    var URL_SEARCH;
    var URL_INS;
    var URL_PAYEE_NAME;
    var URL_DEL;
    var URL_DOC_UPLOAD;
    var URL_IS_VERIFIED;
    var URL_DOC_REF;
    var URL_VERIFY;
    var URL_REJECT;
    var URL_SAVE;

    $(document).ready(function () {
        URL_INSTRUMENT = contextPath + "/BankDetailsController/instrument-types";
        URL_PAYEE_TYPE = contextPath + "/BankDetailsController/payee-types";
        URL_PREV = contextPath + "/BankDetailsController/saved-bank-details";
        URL_SEARCH = contextPath + "/BankDetailsController/search";
        URL_INS = contextPath + "/BankDetailsController/insured-bank-details";
        URL_PAYEE_NAME = contextPath + "/BankDetailsController/payee-names";
        URL_DEL = contextPath + "/BankDetailsController/delete";
        URL_DOC_UPLOAD = contextPath + '/DocumentUploadController';
        URL_IS_VERIFIED = contextPath + "/BankDetailsController/is-bank-details-verified";
        URL_DOC_REF = contextPath + "/BankDetailsController/uploaded-doc-ref";
        URL_VERIFY = contextPath + "/BankDetailsController/update-verification";
        URL_REJECT = contextPath + "/BankDetailsController/update-rejection";
        URL_SAVE = contextPath + "/BankDetailsController/save-update";
        isVerifyUser = '<%= request.getParameter("isVerifyUser") %>';
        isSpecialTeamUser = '<%= request.getParameter("isSpecialTeamUser") %>';
        isBranchUser = '<%= request.getParameter("isBranchUser") %>';
        calSheetId = '<%= request.getParameter("calSheetId") %>';
        claimNo = '${claimsDto.claimNo}';
        endCount = '${claimsDto.policyDto.endCount}';
        policyNumber = '${claimsDto.policyDto.policyNumber}';
        policyChannelType = '${claimsDto.policyDto.policyChannelType}';
        customerName = '${claimsDto.policyDto.custName}';
        customerNIC = '${claimsDto.policyDto.custNic}';

        $.when(
            $.ajax({url: URL_INSTRUMENT, method: 'GET', contentType: 'application/json'}),
            $.ajax({url: URL_PAYEE_TYPE, method: 'GET', contentType: 'application/json'})
        ).then(function (instrumentData, payeeData) {
            instrumentTypeList = instrumentData[0];
            payeeTypeListMaster = payeeData[0];
        }).fail(function (error) {
            notify('Failed to retrieve payee types and instrument types.', 'danger');
        });

        $('#bankDetailsModal').on('show.bs.modal', function (event) {
            var modal = $(this);
            var payeeType = modal.find('input[name="payee-type"]').val();
            var payeeDesc = modal.find('input[name="payee-desc"]').val();
            modal.find("#cardContainer").children('.dynamic-card').remove();
            searchBankDetails(payeeType, payeeDesc);
        });

        $('#paymentDtlCollapse').on('show.bs.collapse', function () {
            var modal = $(this);
            modal.find("#cardContainer").children('.dynamic-card').remove();
            loadSavedBankDetails();
        });

    });

    function addCard() {
        if (isSpecialTeamUser === 'Y' || isVerifyUser === 'Y') {
            let cardId = duplicateCard();

            let $clonedCard = $('#' + cardId);
            let $clonePayeeType = $clonedCard.find('.payeeType');
            let $clonePayeeName = $clonedCard.find('.payeeName');

            $clonePayeeType.val(searchPayeeTypeID);

            // Clear existing options before adding new ones
            $clonePayeeName.empty();

            // Append new options to the payee name select element
            $.each(searchPayeeNameList, function (index, item) {
                $clonePayeeName.append($('<option>').val(item.value).text(item.lable));
            });

            $clonePayeeName.val(searchPayeeNameID); // Set selected value for payee name

            // Trigger chosen:updated event after updating options
            $clonePayeeName.trigger("chosen:updated");
        } else {
            let cardId = duplicateCard();
        }
    }

    function removeCard(element) {
        let $closestCard = $(element).closest('.card');
        let cardIdElement = $closestCard.find('.cardId');
        let cardIndex = cardIdElement.text().trim();
        let isUpdateValue = $closestCard.find('.isUpdate').text().trim();

        let $parentElement = $(element).closest('#cardContainer');
        let $cardList = $parentElement.find('.card');

        let countVerified = $cardList.filter(function () {
            return $(this).find('.isVerify').text().trim() === 'Y';
        }).length;

        let isLastCard = $cardList.length <= 2;

            if (isUpdateValue === 'Y') {
                let requestData = {
                    instrumentType: $closestCard.find('.instrumentType').val(),
                    payeeType: $closestCard.find('.payeeType').val(),
                    payeeName: $closestCard.find('.payeeName').val(),
                    instrumentTypeDesc: $closestCard.find('.instrumentType option:selected').text(),
                    payeeTypeDesc: $closestCard.find('.payeeType option:selected').text(),
                    payeeNameDesc: $closestCard.find('.payeeName option:selected').text(),
                    cardId: cardIndex,
                    claimNo: claimNo,
                };

                $.ajax({
                    type: 'POST',
                    url: URL_DEL,
                    data: requestData,
                    success: function (response) {
                        $closestCard.remove();
                        if(isLastCard){
                            addCard();
                        }
                        notify('Card successfully deleted.', 'success');
                    },
                    error: function (error) {
                        notify('Failed to delete card.', 'danger');
                    }
                });
            } else {
                $closestCard.remove();
                if(isLastCard){
                    addCard();
                }
                notify('Card successfully deleted.', 'success');
            }
    }

    function cloneCard() {
        let originalCard = $('#paymentCard');
        let clonedCard = originalCard.clone();
        clonedCard.addClass('dynamic-card');
        return clonedCard;
    }

    function initialCard() {
        let clonedCard = cloneCard();
        initializeCard(clonedCard, initialCardIndex, false);
        return clonedCard.attr('id');
    }

    function prevCard(card) {
        var cardIndex = card.id;
        let clonedCard = cloneCard();
        clonedCard.attr('id', 'paymentCard' + cardIndex);
        initializeCard(clonedCard, cardIndex, true);

        let isUpdateElement = clonedCard.find('#isUpdate' + cardIndex);
        if (isUpdateElement.length > 0) {
            isUpdateElement.text(card.updateStatus);
        } else {
            console.error("Element '#isUpdate" + cardIndex + "' not found in cloned card.");
        }

        let isVerifyElement = clonedCard.find('#isVerify' + cardIndex);
        if (isVerifyElement.length > 0) {
            isVerifyElement.text(card.verifyStatus);
        } else {
            console.error("Element '#isVerify" + cardIndex + "' not found in cloned card.");
        }

        if (isVerifyUser === 'Y') {
            let checkboxVerify = clonedCard.find('#checkbox-verify' + cardIndex);
            let btnVerify = clonedCard.find('#btn-verify' + cardIndex);
            $("#btn-verify" + cardIndex).prop('disabled', false);
        }

        if (isSpecialTeamUser === 'Y') {
            let checkboxReject = clonedCard.find('#checkbox-reject' + cardIndex);
            let btnReject = clonedCard.find('#btn-reject' + cardIndex);
            $("#btn-reject" + cardIndex).prop('disabled', false);
        }

        let isUploadElement = clonedCard.find('#isUpload' + cardIndex);
        if (isUploadElement.length > 0) {
            isUploadElement.text(card.uploadStatus);
        } else {
            console.error("Element '#isUpload" + cardIndex + "' not found in cloned card.");
        }

        let refIndexElement = clonedCard.find('#refIndex' + cardIndex);
        if (refIndexElement.length > 0 && card.uploadStatus === 'Y') {
            refIndexElement.text(card.docRefNo);
        } else {
            console.error("Element '#refIndex" + cardIndex + "' not found in cloned card.");
        }

        return clonedCard.attr('id');
    }

    function duplicateCard() {
        let clonedCard = cloneCard();
        initializeCard(clonedCard, 0, false);
        let clonedCardId = clonedCard.attr('id');
        return clonedCardId;
    }

    function initializeCard(clonedCard, cardIndex, isPreDataLoading) {

        clonedCard.find('select').val('');

        clonedCard.find('#cardIndex').text(cardIndex);

        clonedCard.show();

        let parentElement = $('#cardContainer');

        if (isPreDataLoading) {
            parentElement.append(clonedCard);
            clonedCard.css('background-color', '#dae2ec');
        } else {
            parentElement.prepend(clonedCard);
        }

        setListsForCard(clonedCard);

        setIDs(clonedCard, cardIndex);

        addFileUploadChangeListener(cardIndex);

        addPayeeTypeChangeListener(clonedCard);
    }

    function setIDs(clonedCard, cardIndex) {

        $(clonedCard).find('#instrumentType').attr('id', 'instrumentType' + cardIndex);
        $(clonedCard).find('#payeeType').attr('id', 'payeeType' + cardIndex);
        $(clonedCard).find('#payeeName').attr('id', 'payeeName' + cardIndex);
        $(clonedCard).find('#cardIndex').attr('id', 'cardIndex' + cardIndex);
        $(clonedCard).find('#isUpdate').attr('id', 'isUpdate' + cardIndex);
        $(clonedCard).find('#isVerify').attr('id', 'isVerify' + cardIndex);
        $(clonedCard).find('#isUpload').attr('id', 'isUpload' + cardIndex);

        let clonedFrmDocumentModal = $('#frmDocumentModal').clone();
        clonedFrmDocumentModal.attr('id', 'frmDocumentModal' + cardIndex);

        let clonedModal = clonedFrmDocumentModal.find('#paymentModal');
        clonedModal.attr('id', 'paymentModal' + cardIndex);
        let modalIndex = clonedModal.find('#modalIndex');
        modalIndex.attr('id', 'modalIndex' + cardIndex).text(cardIndex);
        let refIndex = $(clonedCard).find('#refIndex');
        refIndex.attr('id', 'refIndex' + cardIndex);
        let id = clonedModal.find('input[name="cardId"]');
        id.val(cardIndex);

        let fileUploadClaim = clonedModal.find('#fileUploadClaim');
        fileUploadClaim.attr('id', 'fileUploadClaim' + cardIndex);

        $('#fileUploadClaim' + cardIndex).on('change', function (e) {
            $('#fileUploadClaim' + cardIndex).fileupload('send', {files: e.target.files});
        });

        let errorUpload = clonedModal.find('#errorUpload');
        errorUpload.attr('id', 'errorUpload' + cardIndex);

        let progressClaim = clonedModal.find('#progressClaim');
        progressClaim.attr('id', 'progressClaim' + cardIndex);

        let filesClaim = clonedModal.find('#filesClaim');
        filesClaim.attr('id', 'filesClaim' + cardIndex);

        $('#paymentModalContainer').append(clonedFrmDocumentModal);

        let uploadButton = clonedCard.find('#btn-upload');
        let viewButton = clonedCard.find('#btn-view');
        let saveButton = clonedCard.find('#btn-save');
        let checkButton = clonedCard.find('#btn-check');
        uploadButton.attr('id', 'btn-upload' + cardIndex);
        viewButton.attr('id', 'btn-view' + cardIndex);
        saveButton.attr('id', 'btn-save' + cardIndex);
        checkButton.attr('id', 'btn-check' + cardIndex);

        if (isVerifyUser === 'Y') {
            let verifyButton = clonedCard.find('#btn-verify');
            let checkbox = clonedCard.find('#checkbox-verify');
            verifyButton.attr('id', 'btn-verify' + cardIndex);
            checkbox.attr('id', 'checkbox-verify' + cardIndex);
            $("#btn-verify" + cardIndex).prop('disabled', true);
            checkbox.on('change', function () {
                if (this.checked) {
                    $("#btn-verify" + cardIndex).prop('disabled', false);
                } else {
                    $("#btn-verify" + cardIndex).prop('disabled', true);
                }
            });
        }

        if (isSpecialTeamUser === 'Y') {
            let rejectButton = clonedCard.find('#btn-reject');
            let checkbox = clonedCard.find('#checkbox-reject');
            rejectButton.attr('id', 'btn-reject' + cardIndex);
            checkbox.attr('id', 'checkbox-reject' + cardIndex);
            $("#btn-reject" + cardIndex).prop('disabled', true);
            checkbox.on('change', function () {
                if (this.checked) {
                    $("#btn-reject" + cardIndex).prop('disabled', false);
                } else {
                    $("#btn-reject" + cardIndex).prop('disabled', true);
                }
            });
        }

        let instrumentTypeSelector = clonedCard.find('.instrumentType');
        instrumentTypeSelector.on('change', function () {
            var selectedOption = $(this).find('option:selected');
            var selectedText = selectedOption.text();

            if (selectedText === "CHQ" || selectedText === "DRAFT") {
                var btnUpload = clonedCard.find('.btn-upload');
                btnUpload.prop('disabled', true);
            } else {
                var btnUpload = clonedCard.find('.btn-upload');
                btnUpload.prop('disabled', false);
            }
        });
    }

    function setListsForCard(card) {
        loadInstrumentTypeListForCard(card);
        loadPayeeTypeListForCard(card);
    }

    function loadInstrumentTypeListForCard(card) {
        var selectElement = $(card).find('.card-body .instrumentType');
        selectElement.empty();

        selectElement.append($('<option>', {
            value: '', text: '-- Please Select --'
        }));

        var obj = JSON.parse(instrumentTypeList);
        $.map(obj, function (value, key) {
            selectElement.append($('<option>', {
                value: key, text: value
            })).trigger("chosen:updated");
        });
    }

    function loadPayeeTypeListForCard(card) {
        var selectElement = $(card).find('.card-body .payeeType');
        selectElement.empty();

        selectElement.append($('<option>', {
            value: '', text: '-- Please Select --'
        }));

        $.each(payeeTypeListMaster, function (index, item) {
            selectElement.append($('<option>', {
                value: item.calSheetPayeeNameId, text: item.payeeName
            }));
        });
    }

    function addPayeeTypeChangeListener(clonedCard) {
        let payeeTypeSelector = $(clonedCard).find('.payeeType');

        payeeTypeSelector.on('change', function () {

            var selectedPayeeType = $(this).val();
            var closestCard = $(this).closest('.card');
            var payeeNameSelect = closestCard.find('.payeeName');
            let $viewElement=closestCard.find('.btn-view'); // Corrected: Use .text() as a function
            let isUploadElement = closestCard.find('.isUpload');
            let cardIndex = closestCard.find('.cardId').text();


            $.ajax({
                url: URL_PAYEE_NAME,
                method: 'GET',
                contentType: 'application/json',
                data: {
                    payeeType: selectedPayeeType,
                    customerName: customerName,
                    claimNo: claimNo,
                    policyChannelType: policyChannelType,
                },
                success: function (data) {
                    payeeNameSelect.empty();

                    var obj = JSON.parse(data);
                    $.each(obj, function (index, item) {
                        payeeNameSelect.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                    });
                    closestCard.find('.btn-upload').prop('disabled', true);
                    if(selectedPayeeType=== '1' && isUploadElement.text() !== 'Y' ){
                            $.ajax({
                                url: URL_INS,
                                method: 'GET',
                                contentType: 'application/json',
                                data: {
                                    claimNo: claimNo,
                                    endCount: endCount,
                                    policyNumber: policyNumber,
                                    customerNIC: customerNIC,
                                    cardId: cardIndex,
                                },
                                success: function (data) {
                                    let insuredData = JSON.parse(data);
                                    let docRefNumber=insuredData.docRefNo;
                                    if (insuredData &&  docRefNumber > 0) {
                                        let cloneInstrumentType = closestCard.find('.instrumentType');
                                        let clonePayeeType = closestCard.find('.payeeType');
                                        let clonePayeeName = closestCard.find('.payeeName');

                                        cloneInstrumentType.val(insuredData.instrumentType);
                                        clonePayeeType.val(insuredData.payeeType);
                                        clonePayeeName.empty().append($('<option>').val(insuredData.payeeName).text(insuredData.payeeName));
                                        clonePayeeName.trigger("chosen:updated");

                                        closestCard.find('.refIndex').text(docRefNumber); // Corrected: Use .text() as a function
                                        closestCard.find('.btn-view').show(); // Corrected: Use .text() as a function
                                        let $isUploadElement = closestCard.find('.isUpload');
                                        $isUploadElement.text("Y");
                                        let $isVerifyElement = closestCard.find('.isVerify');
                                        $isVerifyElement.text("N");
                                    }
                                },
                                error: function (error) {
                                    console.error('AJAX request error insured bank details:', error);
                                }
                            });

                    }else  {
                        closestCard.find('.btn-view').hide(); // Corrected: Use .text() as a function
                        closestCard.find('.btn-check').hide(); // Corrected: Use .text() as a function
                        let $isUploadElement = closestCard.find('.isUpload');
                        let $isVerifyElement = closestCard.find('.isVerify');
                        $isUploadElement.text("N");
                        $isVerifyElement.text("N");
                    }
                },
                error: function (error) {

                    console.error('AJAX request error payee name:', error);
                }
            });
        });
    }

    function addFileUploadChangeListener(cardIndex) {
        documentFileUploader(cardIndex);
    }

    function documentFileUploader(cardIndex) {

        $('#fileUploadClaim' + cardIndex).fileupload({
            url: URL_DOC_UPLOAD,
            dataType: 'json',
            add: function (e, data) {
                data.submit();
            },
            done: function (e, data) {

                $('#filesClaim' + cardIndex).empty();
                $('<i class="fa fa-file-pdf-o fa-4x m-3"></i>').appendTo('#filesClaim' + cardIndex);

                $('#errorUpload' + cardIndex).removeClass("bg-primary");
                $('#errorUpload' + cardIndex).removeClass("bg-danger");
                $('#errorUpload' + cardIndex).addClass("bg-success");
                $('#errorUpload' + cardIndex).html("");
                $('#errorUpload' + cardIndex).fadeIn();

                $.ajax({
                    url: URL_DOC_REF,
                    method: 'GET',
                    data: {
                        claimNo: claimNo,
                        cardId: cardIndex,
                    },
                    success: function (data) {
                        var docRefNumber = parseInt(data);
                        if (!isNaN(docRefNumber)) {
                            var cloneElm = $('#paymentCard' + cardIndex);

                            if (docRefNumber > 0) {

                                cloneElm.find('.isUpload').text("Y"); // Corrected: Use .text() as a function
                                cloneElm.find('.isVerify').text("N"); // Corrected: Use .text() as a function
                                cloneElm.find('.refIndex').text(docRefNumber); // Corrected: Use .text() as a function
                                cloneElm.find('.btn-view').show(); // Corrected: Use .text() as a function
                                cloneElm.find('.btn-check').hide(); // Corrected: Use .text() as a function
                                $('<span class="text-light d-block p-1 text-center" >Document Uploaded Successfully!</span>').appendTo('#errorUpload' + cardIndex);
                                $('#errorUpload' + cardIndex).fadeOut(1000);
                            } else {
                                notify('Document not uploaded, try again', 'warning');
                            }

                        } else {
                            notify('Invalid document', 'warning');
                        }

                    },
                    error: function (error) {
                        notify('Failed to upload document.', 'danger');
                    }
                });

            },
            fail: function (e, data) {
                $('#errorUpload' + cardIndex).removeClass("bg-primary");
                $('#errorUpload' + cardIndex).removeClass("bg-success");
                $('#errorUpload' + cardIndex).addClass("bg-danger");
                $('#errorUpload' + cardIndex).html("");
                $('#errorUpload' + cardIndex).fadeIn();
                $('<span class="text-light d-block p-1 text-center">Document Upload failed.</span>').appendTo('#errorUpload' + cardIndex);
                $('#errorUpload' + cardIndex).fadeOut(4000);
            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);

                $('#progressClaim' + cardIndex + ' .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {

                    $('#progressClaim' + cardIndex + ' .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#errorUpload' + cardIndex).removeClass("bg-primary");
                    $('#errorUpload' + cardIndex).removeClass("bg-danger");
                    $('#errorUpload' + cardIndex).removeClass("bg-success");

                    $('#errorUpload' + cardIndex).addClass("bg-primary");
                    $('#errorUpload' + cardIndex).html("");
                    $('#errorUpload' + cardIndex).fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#errorUpload' + cardIndex);

                });
            },
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    }



    function previewDoc(clickedButton) {
        var selectedCardId = $(clickedButton).closest('.card');
        var refId = selectedCardId.find('.refIndex').text();

        if (refId !== null && refId !== '') {
            var url = "${pageContext.request.contextPath}/DashboardController/viewDocumentViewer?refNo=" + refId + "&jobRefNo=0&PREVIOUS_INSPECTION=0&isBankDetails=Y";

            var screenWidth = window.screen.width;
            var screenHeight = window.screen.height;

            var newWindow = window.open(url, '_blank', 'toolbar=yes,scrollbars=yes,resizable=yes,width=' + screenWidth + ',height=' + screenHeight);
            if (newWindow) {
                newWindow.focus();
            } else {
                notify('Unable to open preview in a new window.', 'warning');

            }
        } else {
            notify('Please upload bank details first.', 'warning');

        }
    }

    function uploadFile(clickedButton) {
        var selectedCardId = $(clickedButton).closest('.card');
        var cardIndex = selectedCardId.find('.cardId').text();
        var isUpdate = selectedCardId.find('.isUpdate').text();
        var isVerify = selectedCardId.find('.isVerify').text();
        if (isVerify === "Y") {
            notify('Already verified.', 'warning');
            return;
        }
        if (isUpdate === "Y") {
            var cardElement = $('#paymentModal' + cardIndex).find('input[name="cardId"]');
            var modalElement = $('#paymentModal' + cardIndex).find('.modalId');
            modalElement.text(cardIndex)
            cardElement.val(cardIndex)
            $('#paymentModal' + cardIndex).modal('show');
        } else {
            notify('Please save the details first.', 'warning');
        }
    }

    function closeModal(clickedButton) {
        var selectedCardId = $(clickedButton).closest('.card');
        var cardIndex = selectedCardId.find('.cardId').text();

        $('#paymentModal' + cardIndex).modal('hide');

    }

    function verifyDetails(clickedButton) {
        let $clickedButton = $(clickedButton);
        let $closestCardElement = $clickedButton.closest('.card');

        // Extract form elements
        let $cardIndexElement = $closestCardElement.find('.cardId');
        let $instrumentTypeElement = $closestCardElement.find('.instrumentType');
        let $payeeTypeElement = $closestCardElement.find('.payeeType');
        let $payeeNameElement = $closestCardElement.find('.payeeName');

        // Extract values from form elements
        let cardIDValue = $cardIndexElement.text();
        let instrumentTypeValue = $instrumentTypeElement.val();
        let payeeTypeValue = $payeeTypeElement.val();
        let payeeNameValue = $payeeNameElement.val();

        // Extract selected option texts
        let instrumentTypeDesc = $instrumentTypeElement.find('option:selected').text();
        let payeeTypeDesc = $payeeTypeElement.find('option:selected').text();
        let payeeNameDesc = $payeeNameElement.find('option:selected').text();

        // Check if details have been updated and document uploaded
        let isUpdateValue = $closestCardElement.find('.isUpdate').text();
        let isUploadValue = $closestCardElement.find('.isUpload').text();

        if (isUpdateValue === "N") {
            notify('Please save the details first.');
            return;
        }

        if (isUploadValue === "N") {
            notify('Please upload the document first.');
            return;
        }

        let missingSelects = [];

        if (!instrumentTypeValue) {
            missingSelects.push("instrument type");
        }
        if (!payeeTypeValue) {
            missingSelects.push("payee type");
        }
        if (!payeeNameValue) {
            missingSelects.push("payee name");
        }

        if (missingSelects.length === 1) {
            notify("Please select " + missingSelects[0], 'warning');
            return;
        } else if (missingSelects.length === 2) {
            notify("Please select " + missingSelects[0] + " and " + missingSelects[1], 'warning');
            return;
        } else if (missingSelects.length === 3) {
            notify("Please select all details", 'warning');
            return;
        }

        // Prepare data for AJAX request
        let requestData = {
            instrumentType: instrumentTypeValue,
            payeeType: payeeTypeValue,
            payeeName: payeeNameValue,
            instrumentTypeDesc: instrumentTypeDesc,
            payeeTypeDesc: payeeTypeDesc,
            payeeNameDesc: payeeNameDesc,
            cardId: cardIDValue,
            claimNo: claimNo,
        };

        // Send AJAX request
        $.ajax({
            type: 'POST',
            url: URL_VERIFY,
            data: requestData,
            success: function (response) {
                $closestCardElement.find('.isVerify').text("Y");
                let btnCheck = $closestCardElement.find('.btn-check');
                btnCheck.find('i').remove();
                btnCheck.removeClass('btn-danger btn-success');
                btnCheck.addClass('btn-success');
                btnCheck.html('<i class="fa fa-check-square-o fa-2x m-1"></i>');
                btnCheck.show();
                $closestCardElement.find('button, select').not('.btn-view, .btn-check, .btn-add').prop('disabled', true);
                $closestCardElement.find('input[type="checkbox"]').prop('disabled', true);
                notify('Verification successful', "success");
            },
            error: function (error) {
                notify('Verification failed', "danger");
            }
        });
    }

    function rejectDetails(clickedButton) {
        let $clickedButton = $(clickedButton);
        let $closestCardElement = $clickedButton.closest('.card');

        // Extract form elements and values
        let $cardIndexElement = $closestCardElement.find('.cardId');
        let $instrumentTypeElement = $closestCardElement.find('.instrumentType');
        let $payeeTypeElement = $closestCardElement.find('.payeeType');
        let $payeeNameElement = $closestCardElement.find('.payeeName');

        let instrumentTypeValue = $instrumentTypeElement.val();
        let payeeTypeValue = $payeeTypeElement.val();
        let payeeNameValue = $payeeNameElement.val();
        let cardIDValue = $cardIndexElement.text();

        // Extract selected option texts
        let instrumentTypeDesc = $instrumentTypeElement.find('option:selected').text();
        let payeeTypeDesc = $payeeTypeElement.find('option:selected').text();
        let payeeNameDesc = $payeeNameElement.find('option:selected').text();

        // Check if details have been updated and document uploaded
        let isUpdateValue = $closestCardElement.find('.isUpdate').text();
        let isUploadValue = $closestCardElement.find('.isUpload').text();

        // Validation
        let validationErrors = [];

        if (isUpdateValue === "N") {
            validationErrors.push('Please save the details first.');
        }

        if (isUploadValue === "N") {
            validationErrors.push('Please upload the document first.');
        }

        // Handle validation errors
        if (validationErrors.length > 0) {
            notify(validationErrors.join(' '), 'warning');
            return;
        }

        // Validate required select elements
        let missingSelects = [];

        if (!instrumentTypeValue) {
            missingSelects.push("instrument type");
        }
        if (!payeeTypeValue) {
            missingSelects.push("payee type");
        }
        if (!payeeNameValue) {
            missingSelects.push("payee name");
        }

        // Notify if any select values are missing
        if (missingSelects.length > 0) {
            let missingSelectMessage = (missingSelects.length === 1) ?
                `Please select ${missingSelects[0]}` :
                `Please select ${missingSelects.join(" and ")}`;
            notify(missingSelectMessage, 'warning');
            return;
        }

        // Prepare data for AJAX request
        let requestData = {
            instrumentType: instrumentTypeValue,
            payeeType: payeeTypeValue,
            payeeName: payeeNameValue,
            instrumentTypeDesc: instrumentTypeDesc,
            payeeTypeDesc: payeeTypeDesc,
            payeeNameDesc: payeeNameDesc,
            cardId: cardIDValue,
            claimNo: claimNo,
        };

        // Send AJAX request to reject the details
        $.ajax({
            type: 'POST',
            url: URL_REJECT,
            data: requestData,
            success: function (response) {
                // Update UI elements on success
                $closestCardElement.find('.isVerify').text("R");
                let btnCheck = $closestCardElement.find('.btn-check');
                btnCheck.find('i').remove();
                btnCheck.removeClass('btn-danger btn-success');
                btnCheck.addClass('btn-danger');
                btnCheck.html('<i class="fa fa-times-circle-o fa-2x m-1"></i>');
                btnCheck.show();
                $closestCardElement.find('button, select').not('.btn-view, .btn-check').prop('disabled', true);
                $closestCardElement.find('input[type="checkbox"]').prop('disabled', true);
                notify('Rejection successful', 'success');
            },
            error: function (error) {
                // Handle AJAX error
                notify('Rejection failed', 'danger');
            }
        });
    }

    function saveDetails(clickedButton) {
        let $clickedButton = $(clickedButton);
        let $closestCardElement = $clickedButton.closest('.card');
        let $cardIdLblElement = $closestCardElement.find('.cardId');
        let $isUpdateElement = $closestCardElement.find('.isUpdate');
        let $isUploadElement = $closestCardElement.find('.isUpload');
        let $instrumentTypeElement = $closestCardElement.find('.instrumentType');
        let $payeeTypeElement = $closestCardElement.find('.payeeType');
        let $payeeNameElement = $closestCardElement.find('.payeeName');
        let $refIndexElement = $closestCardElement.find('.refIndex');

        // Extract values from form elements
        let instrumentTypeValue = $instrumentTypeElement.val();
        let payeeTypeValue = $payeeTypeElement.val();
        let payeeNameValue = $payeeNameElement.val();
        let cardIDValue = $cardIdLblElement.text();
        let refIdValue = $refIndexElement.text();
        let isUploadValue = $isUploadElement.text();

        // Extract selected option texts
        let instrumentTypeDesc = $instrumentTypeElement.find('option:selected').text();
        let payeeTypeDesc = $payeeTypeElement.find('option:selected').text();
        let payeeNameDesc = $payeeNameElement.find('option:selected').text();

        // Validate required fields
        let missingSelects = [];

        if (!instrumentTypeValue) {
            missingSelects.push("instrument type");
        }
        if (!payeeTypeValue) {
            missingSelects.push("payee type");
        }
        if (!payeeNameValue) {
            missingSelects.push("payee name");
        }

        if (missingSelects.length === 1) {
            notify("Please select " + missingSelects[0], 'warning');
            return;
        } else if (missingSelects.length === 2) {
            notify("Please select " + missingSelects[0] + " and " + missingSelects[1], 'warning');
            return;
        } else if (missingSelects.length === 3) {
            notify("Please select all details", 'warning');
            return;
        }

        // Prepare data for AJAX request
        let requestData = {
            instrumentType: instrumentTypeValue,
            payeeType: payeeTypeValue,
            payeeName: payeeNameValue,
            instrumentTypeDesc: instrumentTypeDesc,
            payeeTypeDesc: payeeTypeDesc,
            payeeNameDesc: payeeNameDesc,
            cardId: cardIDValue,
            refId: refIdValue,
            isUpload: isUploadValue,
            claimNo: claimNo,
        };

        // Send AJAX request to save data
        $.ajax({
            type: 'POST',
            url: URL_SAVE,
            data: requestData,
            success: function (response) {
                // Process successful response
                let generatedCardIndex = parseInt(response);

                // Update UI elements with generated card index
                $cardIdLblElement.text(generatedCardIndex);
                $closestCardElement.attr('id', 'paymentCard' + generatedCardIndex);
                setIDs($closestCardElement, generatedCardIndex);
                addFileUploadChangeListener(generatedCardIndex);
                addPayeeTypeChangeListener($closestCardElement);

                // Update modal input values
                let $parentModal = $('#frmDocumentModal' + generatedCardIndex);
                let $clonedModal = $parentModal.find('#paymentModal' + generatedCardIndex);
                $clonedModal.find('input[name="instrumentType"]').val(instrumentTypeDesc);
                $clonedModal.find('input[name="payeeType"]').val(payeeTypeDesc);
                $clonedModal.find('input[name="payeeName"]').val(payeeNameDesc);

                // Update status element and notify success
                $isUpdateElement.text("Y");
                $closestCardElement.css('background-color', '#dae2ec');
                $closestCardElement.find('.btn-upload').prop('disabled', false);
                notify('Saved successfully', 'success');
            },
            error: function (error) {
                // Handle AJAX error and notify failure
                notify('Save failed', 'danger');
            }
        });
    }




    function isVerifiedBankDetails(callback) {
        $.ajax({
            url: URL_IS_VERIFIED,
            method: 'GET',
            contentType: 'application/json',
            data: {claimNo: claimNo, calSheetId: calSheetId},
            success: function (data) {
                var isVerified = JSON.parse(data);
                callback(isVerified);
            },
            error: function (error) {
                notify("Error occurred while checking verify bank details", "danger");
            }
        });
    }

    function openBankDetailsModal(itemIndex, uniqueTableName) {
        let payeeDescClass = '.payeeDescEvent-' + itemIndex + '-' + uniqueTableName;
        let payeeIdClass = ".payeeIdEvent-" + itemIndex + '-' + uniqueTableName;
        let payeeIdVal = $(payeeIdClass).val();
        let payeeDescriptionVal = $(payeeDescClass).val();
        $('input[name="payee-type"]').val(payeeIdVal);
        $('input[name="payee-desc"]').val(payeeDescriptionVal);
        $('#bankDetailsModal').modal('show');
    }

    function searchBankDetails(payeeIdValue, payeeDescValue) {
        $.ajax({
            url: URL_SEARCH,
            method: 'GET',
            contentType: 'application/json',
            data: {
                claimNo: claimNo,
                endCount: endCount,
                policyNumber: policyNumber,
                customerName: customerName,
                policyChannelType: policyChannelType,
                payeeType: payeeIdValue,
                payeeName: payeeDescValue
            },
            success: function (data) {
                let bankDetailsDto = JSON.parse(data);
                let bankDetailsDtoList = bankDetailsDto.bankDetailsDtoList;
                let payeeTypeList = bankDetailsDto.payeeTypeList;
                let payeeNameList = bankDetailsDto.payeeNameList;
                let payeeTypeId = bankDetailsDto.payeeTypeId;
                let payeeNameId = bankDetailsDto.payeeNameId;

                searchPayeeTypeList = payeeTypeList;
                searchPayeeNameList = payeeNameList;
                searchPayeeTypeID = payeeTypeId;
                searchPayeeNameID = payeeNameId;


                    $.each(bankDetailsDtoList, function (index, item) {
                        let clonedCardId = prevCard(item);
                        let $clonedCard = $('#' + clonedCardId);
                        let cloneInstrumentType = $clonedCard.find('.instrumentType');
                        let clonePayeeType = $clonedCard.find('.payeeType');
                        let clonePayeeName = $clonedCard.find('.payeeName');
                        let cardId = $clonedCard.find('.cardId');
                        let btnCheck = $clonedCard.find('.btn-check');
                        let btnView = $clonedCard.find('.btn-view');
                        let btnSave = $clonedCard.find('.btn-save');
                        // var refId = $clonedCard.find('.refIndex');
                        // refId.text(item.docRefNo);
                        cloneInstrumentType.val(item.instrumentType);
                        clonePayeeType.val(payeeTypeId);
                        $.each(item.payeeNameList, function (index, item) {
                            clonePayeeName.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                        });
                        clonePayeeName.val(payeeNameId);

                        let parentModal = $('#frmDocumentModal' + cardId.text());
                        let clonedModal = parentModal.find('#paymentModal' + cardId.text());
                        let instrumentTypeID = clonedModal.find('input[name="instrumentType"]');
                        let payeeTypeID = clonedModal.find('input[name="payeeType"]');
                        let payeeNameID = clonedModal.find('input[name="payeeName"]');
                        // Get the selected text from cloneInstrumentType, clonePayeeType, clonePayeeName
                        var selectedInstrumentTypeText = cloneInstrumentType.find('option:selected').text();
                        var selectedPayeeTypeText = clonePayeeType.find('option:selected').text();
                        var selectedPayeeNameText = clonePayeeName.find('option:selected').text();

                        instrumentTypeID.val(selectedInstrumentTypeText);
                        payeeTypeID.val(selectedPayeeTypeText);
                        payeeNameID.val(selectedPayeeNameText);


                        if (item.verifyStatus == "Y") {
                            if (isSpecialTeamUser === 'Y') {
                                $clonedCard.find('button, select').not('.btn-view').not('.btn-check').prop('disabled', true);
                            } else if (isVerifyUser === 'Y') {
                                $clonedCard.find('button, select').not('.btn-view').not('.btn-check').not('.btn-add').prop('disabled', true);
                                $clonedCard.find('.checkbox-verify').prop('checked', true);
                                $clonedCard.find('.checkbox-verify').prop('disabled', true);
                            } else {
                                $clonedCard.find('button, select').not('.btn-view, .btn-check').prop('disabled', true);
                                $clonedCard.find('input[type="checkbox"]').not('.checkbox-verify').prop('disabled', true);
                            }
                            btnCheck.addClass('btn-success');
                            btnCheck.html('<i class="fa fa-check-square-o fa-2x m-1"></i>');
                            btnCheck.show();

                        }else if(item.verifyStatus == "R"){
                            if (isSpecialTeamUser === 'Y') {
                                $clonedCard.find('button, select').not('.btn-view').not('.btn-check').prop('disabled', true);
                                $clonedCard.find('.checkbox-reject').prop('checked', true);
                                $clonedCard.find('.checkbox-reject').prop('disabled', true);
                            } else if (isVerifyUser === 'Y') {
                                // $clonedCard.find('button, select').not('.btn-view').not('.btn-check').not('.btn-add').prop('disabled', true);
                            } else {
                                $clonedCard.find('button, select').not('.btn-view, .btn-check').prop('disabled', true);
                            }
                            btnCheck.addClass('btn-danger');
                            btnCheck.html('<i class="fa fa-times-circle-o fa-2x m-1"></i>');
                            btnCheck.show();

                        }
                        else {
                            if (isSpecialTeamUser === 'Y') {
                                $clonedCard.find('button, select').not('.btn-view').not('.btn-check').prop('disabled', true);
                            } else if (isVerifyUser === 'Y') {
                                $clonedCard.find('button').not('.btn-view').not('.btn-check').not('.btn-add').not('.btn-upload').not('.btn-save').not('.btn-remove').prop('disabled', true);
                            } else {
                                $clonedCard.find('button, select').not('.btn-view, .btn-check').prop('disabled', true);
                            }
                        }

                        if (item.uploadStatus == "Y") {
                            btnView.show();
                        }

                    });





                if (bankDetailsDtoList.length === 0 && isVerifyUser === 'Y') {
                        let clonedCardId = initialCard();
                        let $clonedCard = $('#' + clonedCardId);
                        let clonePayeeType = $clonedCard.find('.payeeType');
                        let clonePayeeName = $clonedCard.find('.payeeName');

                        if (payeeTypeId !== 0) {
                            clonePayeeType.val(payeeTypeId);
                        }

                        clonePayeeName.empty();
                        $.each(payeeNameList, function (index, item) {
                            clonePayeeName.append($('<option>').val(item.value).text(item.lable));
                        });
                        clonePayeeName.val(payeeNameId);
                }


            },
            error: function (error) {
                console.error('AJAX request error prev bank details:', error);
            }
        });

    }

    function loadSavedBankDetails() {
        $.ajax({
            url: URL_PREV,
            method: 'GET',
            contentType: 'application/json',
            data: {
                claimNo: claimNo,
                endCount: endCount,
                policyNumber: policyNumber,
                customerName: customerName,
                policyChannelType: policyChannelType
            },
            success: function (data) {
                let prevData = JSON.parse(data);
                let isInsuredExist = false;
                if(prevData.length>0){
                    prevData.forEach(function (item) {
                        if (item.payeeType === 1) {
                            isInsuredExist = true;
                        }

                        let clonedCardId = prevCard(item);
                        let $clonedCard = $('#' + clonedCardId);
                        let cloneInstrumentType = $clonedCard.find('.instrumentType');
                        let clonePayeeType = $clonedCard.find('.payeeType');
                        let clonePayeeName = $clonedCard.find('.payeeName');
                        let cardId = $clonedCard.find('.cardId');
                        let btnCheck = $clonedCard.find('.btn-check');
                        let btnView = $clonedCard.find('.btn-view');
                        let refId = $clonedCard.find('.refIndex');

                        refId.text(item.docRefNo);
                        cloneInstrumentType.val(item.instrumentType);
                        clonePayeeType.val(item.payeeType);

                        clonePayeeName.empty();
                        item.payeeNameList.forEach(function (payee) {
                            clonePayeeName.append($('<option>').val(payee.value).text(payee.lable));
                        });
                        clonePayeeName.val(item.payeeName);

                        let parentModal = $('#frmDocumentModal' + cardId.text());
                        let clonedModal = parentModal.find('#paymentModal' + cardId.text());
                        let instrumentTypeID = clonedModal.find('input[name="instrumentType"]');
                        let payeeTypeID = clonedModal.find('input[name="payeeType"]');
                        let payeeNameID = clonedModal.find('input[name="payeeName"]');

                        let selectedInstrumentTypeText = cloneInstrumentType.find('option:selected').text();
                        let selectedPayeeTypeText = clonePayeeType.find('option:selected').text();
                        let selectedPayeeNameText = clonePayeeName.find('option:selected').text();

                        instrumentTypeID.val(selectedInstrumentTypeText);
                        payeeTypeID.val(selectedPayeeTypeText);
                        payeeNameID.val(selectedPayeeNameText);

                        if (item.verifyStatus === "Y") {
                            let disableSelectors = $clonedCard.find('button, select').not('.btn-view');
                            if (isBranchUser === 'Y') {
                                disableSelectors.not('.btn-add, .btn-check').prop('disabled', true);
                            } else {
                                disableSelectors.not('.btn-view, .btn-check').prop('disabled', true);
                            }

                            btnCheck.addClass('btn-success');
                            btnCheck.html('<i class="fa fa-check-square-o fa-2x m-1"></i>');
                            btnCheck.show();

                        }else if(item.verifyStatus === "R"){
                            let disableSelectors = $clonedCard.find('button, select').not('.btn-view');
                            if (isBranchUser === 'Y') {
                                // disableSelectors.not('.btn-add, .btn-check').prop('disabled', true);
                            } else {
                                disableSelectors.not('.btn-view, .btn-check').prop('disabled', true);
                            }
                            btnCheck.addClass('btn-danger');
                            btnCheck.html('<i class="fa fa-times-circle-o fa-2x m-1"></i>');
                            btnCheck.show();

                        }
                        else {
                            let disableSelectors = $clonedCard.find('button, select').not('.btn-view');
                            if (isBranchUser === 'Y') {
                                // disableSelectors.not('.btn-add, .btn-check').prop('disabled', true);
                            } else {
                                disableSelectors.not('.btn-view, .btn-check').prop('disabled', true);
                            }
                        }

                        if (item.uploadStatus === "Y") {
                            btnView.show();
                        }
                    });

                }else {
                    initialCard();
                }
            },
            error: function (error) {
                console.error('AJAX request error prev bank details:', error);
            }
        });
    }


</script>

</body>

</html>
