<%--
  Created by IntelliJ IDEA.
  User: Chathura
  Date: 3/14/2024
  Time: 10:14 AM
  To change this template use File | Settings | File Templates.
--%>
<%--<%@ page contentType="text/html;charset=UTF-8" language="java" %>--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<html>
<head>
    <script src="${pageContext.request.contextPath}/resources/js/common/payment-card.js"></script>
    <c:set var="payeeIdValue" value="${param.payeeIdValue}" />
    <c:set var="payeeDescValue" value="${param.payeeDescValue}" />
</head>
<body>
<div id="cardContainer">

    <div class="card" id="paymentCard" style="display: none;">
        <div id="paymentCardHeader" class="card-header">
            <button type="button" onclick="duplicateCard()" name="" value="BR"
                    class="btn-add btn btn-primary ml-2">
                Add
            </button>
            <button type="button" onclick="removeCard(this)" name="" value="BR"
                    class="btn-remove btn btn-primary ml-2">
                Remove
            </button>
        </div>
        <div class="card-body">
            <label id="cardIndex" class="cardId">0</label>
            <label id="isUpdate" class="isUpdate">N</label>
            <label id="isVerify" class="isVerify">N</label>
            <label id="isUpload" class="isUpload">N</label>
            <fieldset class="border p-2 mx-2">
                <div class="form-group row has-feedback">
                    <label class="col-sm-4 col-form-label">Instrument Type :</label>
                    <div class="col-sm-8">
                        <select name="instrumentType" id="instrumentType"
                                class="instrumentType form-control form-control-sm"
                                data-fv-field="instrumentType">
                        </select>
                    </div>
                </div>
                <div class="form-group row has-feedback">
                    <label class="col-sm-4 col-form-label">Payee Type :</label>
                    <div class="col-sm-8">
                        <select name="payeeType" id="payeeType"
                                class="payeeType form-control form-control-sm"
                                data-fv-field="payeeType">
                        </select>
                    </div>
                </div>
                <div class="form-group row has-feedback">
                    <label class="col-sm-4 col-form-label">Payee Name :</label>
                    <div class="col-sm-8">
                        <select name="payeeName" id="payeeName"
                                class="payeeName form-control form-control-sm"
                                data-fv-field="payeeName">
                        </select>
                    </div>
                </div>

                <div class="mt-3 mb-2 row">
                    <div class="col-sm-6 text-left">
                        <button id="btn-view" type="button" name="" value="BR" class="btn-view btn btn-primary" style="display: none" onclick="previewDoc(this)">
                            <i class="fa fa-file-pdf-o fa-2x m-1"></i>
                        </button>

                        <button id="btn-check" type="button" name="" value="BR" class="btn-check btn btn-danger ml-2" style="display: none">
                            <i class="fa fa-check-square-o fa-2x m-1" ></i>
                        </button>
                    </div>

                    <div class="col-sm-6 text-right">
                        <button id="btn-upload" type="button" name="" value="BR"
                                class="btn-upload btn btn-primary ml-2" onclick="uploadFile(this)">
                            Upload
                        </button>


                        <button id="btn-save" type="button" name="" value="BR"
                                class="btn-save btn btn-primary ml-2" onclick="saveDetails(this)">
                            Save
                        </button>
                    </div>
                </div>

                <c:if test="${param.isVerifyUser eq 'true' or param.isSpecialTeamUser eq 'true'}">
                    <div class="text-right mb-2">
                        <input id="checkbox" class="checkbox" type="checkbox">
                        <button id="btn-verify" type="button" name="" value="BR"
                                class="btn-verify btn btn-primary ml-2" onclick="verifyDetails(this)">
                            Verify
                        </button>
                    </div>
                </c:if>
            </fieldset>


        </div>



    </div>


</div>


<script>

    var instrumentTypeList = null;
    var payeeTypeList = null;
    var cardIndex = 0;

    $(document).ready(function () {
        alert('payment details loaded...')
        var URL_INSTRUMENT = contextPath + "/BankDetailsController/instrument-types";
        var URL_PAYEE_TYPE = contextPath + "/BankDetailsController/payee-types";

        $.when(
            $.ajax({url: URL_INSTRUMENT, method: 'GET', contentType: 'application/json'}),
            $.ajax({url: URL_PAYEE_TYPE, method: 'GET', contentType: 'application/json'})
        ).then(function(instrumentData, payeeData) {
            instrumentTypeList = instrumentData[0];
            payeeTypeList = payeeData[0];

            // Further processing if needed
        }).fail(function(error) {
            console.error('AJAX request error:', error);
        });
    });

    function setListsForCard(card) {
        loadInstrumentTypeListForCard(card);
        loadPayeeTypeListForCard(card);
    }

    function initializeCard(clonedCard, cardIndex, isPreDataLoding) {
        // Reset input values in the cloned card
        clonedCard.querySelectorAll('select').forEach(select => select.value = '');

        // Update label in the cloned card
        clonedCard.querySelector('#cardIndex').textContent = cardIndex;
        clonedCard.style.display = '';
        const parentElement =  document.getElementById('cardContainer');
        if(isPreDataLoding){
            parentElement.appendChild(clonedCard);
            clonedCard.style.backgroundColor = '#dae2ec';
        }else {
            const firstChild = parentElement.firstChild;
            parentElement.insertBefore(clonedCard, firstChild);
        }



        setListsForCard(clonedCard);

        setIDs(clonedCard,cardIndex);

        addFileUploadChangeListener(cardIndex);

        addPayeeTypeChangeListener(clonedCard);

    }

    function setIDs(clonedCard,cardIndex) {

        const instrumentType = clonedCard.querySelector('#instrumentType');
        const payeeType = clonedCard.querySelector('#payeeType');
        const payeeName = clonedCard.querySelector('#payeeName');
        const cardId = clonedCard.querySelector('#cardIndex');
        const isUpdate = clonedCard.querySelector('#isUpdate');
        const isVerify = clonedCard.querySelector('#isVerify');
        const isUpload = clonedCard.querySelector('#isUpload');

        instrumentType.id = 'instrumentType' + cardIndex;
        payeeType.id = 'payeeType' + cardIndex;
        payeeName.id = 'payeeName' + cardIndex;
        cardId.id = 'cardIndex' + cardIndex;
        isUpdate.id = 'isUpdate' + cardIndex;
        isVerify.id = 'isVerify' + cardIndex;
        isUpload.id = 'isUpload' + cardIndex;

        // Update modal ID and association
        const clonedModal = clonedCard.querySelector('#paymentModal');
        clonedModal.id = 'paymentModal' + cardIndex;
        const modalIndex = clonedModal.querySelector('#modalIndex');
        modalIndex.id = 'modalIndex' + cardIndex;
        modalIndex.textContent = cardIndex;
        const refIndex = clonedModal.querySelector('#refIndex');
        refIndex.id = 'refIndex' + cardIndex;
        const id = clonedModal.querySelector('input[name="cardId"]');
        id.value = cardIndex;


        const uploadButton = clonedCard.querySelector('#btn-upload');
        const viewButton = clonedCard.querySelector('#btn-view');
        const saveButton = clonedCard.querySelector('#btn-save');
        const checkButton = clonedCard.querySelector('#btn-check');
        uploadButton.id = 'btn-upload' + cardIndex;
        viewButton.id = 'btn-view' + cardIndex;
        saveButton.id = 'btn-save' + cardIndex;
        checkButton.id = 'btn-check' + cardIndex;
        // Access server-side parameter in JavaScript

        var isVerifyUser = <%= request.getParameter("isVerifyUser") %>;
        if(isVerifyUser){
            const verifyButton = clonedCard.querySelector('#btn-verify');
            const checkbox = clonedCard.querySelector('#checkbox');
            verifyButton.id = 'btn-verify' + cardIndex;
            checkbox.id = 'checkbox' + cardIndex;
        }


        const fileUploadClaim = clonedModal.querySelector('#fileUploadClaim');
        fileUploadClaim.id = 'fileUploadClaim' + cardIndex;

        // Set ID for file upload input
        const errorUpload = clonedModal.querySelector('#errorUpload');
        errorUpload.id = 'errorUpload' + cardIndex;

        // Set ID for file upload input
        const progressClaim = clonedModal.querySelector('#progressClaim');
        progressClaim.id = 'progressClaim' + cardIndex;

        // Set ID for file upload input
        const filesClaim = clonedModal.querySelector('#filesClaim');
        filesClaim.id = 'filesClaim' + cardIndex;

    }

    function addPayeeTypeChangeListener(clonedCard) {
        const payeeTypeSelector = clonedCard.querySelector('.payeeType');

        payeeTypeSelector.addEventListener('change', function () {

            var selectedPayeeType = $(this).val();
            var closestCard = $(this).closest('.card');
            var payeeNameSelect = closestCard.find('.payeeName');

            // Additional data to be sent with the request
            var policyChannelType = '${claimsDto.policyDto.policyChannelType}';
            var claimNo = '${claimsDto.claimNo}';
            var customerName = '${claimsDto.policyDto.custName}';

            // Add your logic to send an AJAX request to get payee name list based on selected payee type
            var URL_PAYEE_NAME = contextPath + "/BankDetailsController/payee-names";
            $.ajax({
                url: URL_PAYEE_NAME,
                method: 'GET',
                contentType: 'application/json',
                data: {
                    payeeType: selectedPayeeType,
                    customerName: customerName,
                    claimNo: claimNo,
                    policyChannelType: policyChannelType,
                },
                success: function (data) {
                    payeeNameSelect.empty();

                    var obj = JSON.parse(data);
                    $.each(obj, function (index, item) {
                        payeeNameSelect.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                    });
                },
                error: function (error) {

                    console.error('AJAX request error payee name:', error);
                }
            });
        });
    }

    function cloneCard() {
        const originalCard = document.getElementById('paymentCard');
        return  originalCard.cloneNode(true);
    }


    function initialCard() {
        const clonedCard = cloneCard();
        clonedCard.id = 'paymentCard' + (++cardIndex);
        initializeCard(clonedCard, cardIndex,false);
        return clonedCard.id;
    }

    function prevCard(card) {
        cardIndex = card.id;
        const clonedCard = cloneCard();
        clonedCard.id = 'paymentCard' + cardIndex;
        initializeCard(clonedCard, cardIndex, true);
        const isUpdateElement = clonedCard.querySelector('#isUpdate' + cardIndex);
        if (isUpdateElement) {
            isUpdateElement.textContent = card.updateStatus;
        } else {
            console.error("Element '#isUpdate" + cardIndex + "' not found in cloned card.");
        }

        const isVerifyElement = clonedCard.querySelector('#isVerify' + cardIndex);
        const checkboxElement = clonedCard.querySelector('#checkbox' + cardIndex);
        if (isVerifyElement) {
            isVerifyElement.textContent = card.verifyStatus;
            if(checkboxElement){
                if (card.verifyStatus === "Y") {
                    checkboxElement.checked = true;
                } else {
                    checkboxElement.checked = false;
                }
            }


        } else {
            console.error("Element '#isVerify" + cardIndex + "' not found in cloned card.");
        }
        const isUploadElement = clonedCard.querySelector('#isUpload' + cardIndex);
        if (isUploadElement) {
            isUploadElement.textContent = card.uploadStatus;
        } else {
            console.error("Element '#isUpload" + cardIndex + "' not found in cloned card.");
        }
        return clonedCard.id;
    }

    function duplicateCard() {
        const clonedCard = cloneCard();
        clonedCard.id = 'paymentCard' + (++cardIndex);
        initializeCard(clonedCard, cardIndex,false);
        return clonedCard.id;
    }

    function removeCard(element) {
        var URL_DEL = contextPath + "/BankDetailsController/delete";
        const parentElement = element.closest('#cardContainer'); // Replace 'parent-element' with the class of the parent element containing the cards
        const cardList = parentElement.querySelectorAll('.card');
        console.log('Card List:', cardList);
        // Initialize cardIndex variable
        var cardIndex;
        const closestCard = element.closest('.card');
        var cardIdElement = closestCard.querySelector('.cardId');
        if (cardIdElement) {
            cardIndex = cardIdElement.textContent.trim(); // label value
        }

        // Loop through each card to find the cardIndex
        var countVerified = 0;

        // Loop through each card to find the cardIndex and count verified cards
        cardList.forEach(function (card) {
            var isVerify = card.querySelector('.isVerify');
            if (isVerify && isVerify.textContent.trim() === 'Y') {
                // If the card is verified, increment the count
                countVerified++;
            }
        });


        var claimNo = '${claimSuperDashboardDto.claimHandlerDto.claimNo}';

        // Check if there's more than one card before removing
        console.log('Card List:', cardList);
        if (cardList.length > 2) {
            $.ajax({
                type: 'POST',
                url: URL_DEL,
                data: {
                    claimNo: claimNo,
                    cardId: cardIndex,
                },
                success: function (response) {
                    notify('Delete Successfully', "success");
                    const cardToRemove = element.closest('.card');
                    cardToRemove.remove();
                },
                error: function (error) {
                    // Handle the error response
                    console.error('Update failed:', error);
                }
            });
        } else {
            notify('At least one card must remain.','warning');
        }
    }

    function loadInstrumentTypeListForCard(card) {
        var selectElement = $(card).find('.card-body .instrumentType');
        selectElement.empty(); // Clear existing options

        // Add a default option
        selectElement.append($('<option>', {
            value: '', text: '-- Please Select --'
        }));

        var obj = JSON.parse(instrumentTypeList);
        $.map(obj, function (value, key) {
            selectElement.append($('<option>', {
                value: key, text: value // Corrected from "lable" to "label"
            })).trigger("chosen:updated");
        });
    }

    function loadPayeeTypeListForCard(card) {
        var selectElement = $(card).find('.card-body .payeeType');
        selectElement.empty(); // Clear existing options

        // Add a default option
        selectElement.append($('<option>', {
            value: '', text: '-- Please Select --'
        }));

        // Add options based on the global payeeTypeList
        $.each(payeeTypeList, function (index, item) {
            selectElement.append($('<option>', {
                value: item.calSheetPayeeNameId, text: item.payeeName
            }));
        });

    }

    function addFileUploadChangeListener(cardIndex) {
        // File input change callback
        $('#fileUploadClaim' + cardIndex).on('change', function (e) {
            $('#fileUploadClaim' + cardIndex).fileupload('send', {files: e.target.files});
        });

        // Initialize file uploader
        documentFileUploader(cardIndex);

        // Click event listener for #refIndex element

    }

    function documentFileUploader(cardIndex) {
        var url = contextPath + '/DocumentUploadController';

        $('#fileUploadClaim' + cardIndex).fileupload({
            url: url,
            dataType: 'json',
            add: function (e, data) {
                // Triggered when a file is added to the queue
                data.submit();
            },
            done: function (e, data) {
                alert('uploaded success')
                $('#errorUpload' + cardIndex).removeClass("bg-primary");
                $('#errorUpload' + cardIndex).removeClass("bg-danger");
                $('#errorUpload' + cardIndex).addClass("bg-success");
                $('#errorUpload' + cardIndex).html("");
                $('#errorUpload' + cardIndex).fadeIn();
                var URL_DOC_REF = contextPath + "/BankDetailsController/uploaded-doc-ref";
                var claimNo = '${claimsDto.claimNo}';
                $.ajax({
                    url: URL_DOC_REF,
                    method: 'GET',
                    data: {
                        claimNo: claimNo,
                        cardId: cardIndex,
                    },
                    success: function (data) {
                        var docRefNumber = parseInt(data);
                        if (!isNaN(docRefNumber)) {
                            alert('success doc ref ' + docRefNumber);
                            $('#refIndex' + cardIndex).text(docRefNumber);
                            $('#isUpload' + cardIndex).text("Y");
                            $('#btn-view'+cardIndex).show();
                        } else {
                            alert('invalid ' + docRefNumber);
                        }


                    },
                    error: function (error) {
                        // Handle errors
                    }
                });
                $('<span class="text-light d-block p-1 text-center" >Document Uploaded Successfully!</span>').appendTo('#errorUpload' + cardIndex);
                $('#errorUpload' + cardIndex).fadeOut(4000);
            },
            fail: function (e, data) {
                $('#errorUpload' + cardIndex).removeClass("bg-primary");
                $('#errorUpload' + cardIndex).removeClass("bg-success");
                $('#errorUpload' + cardIndex).addClass("bg-danger");
                $('#errorUpload' + cardIndex).html("");
                $('#errorUpload' + cardIndex).fadeIn();
                $('<span class="text-light d-block p-1 text-center">Document Upload failed.</span>').appendTo('#errorUpload' + cardIndex);
                $('#errorUpload' + cardIndex).fadeOut(4000);
            },
            progressall: function (e, data) {
                progress = parseInt(data.loaded / data.total * 100, 10);

                $('#progressClaim' + cardIndex + ' .progress-bar').css(
                    'width',
                    progress + '%'
                );
            },
            change: function (e, data) {
                progress = 0;
                $.each(data.files, function (index, file) {

                    $('#progressClaim' + cardIndex + ' .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                    $('#errorUpload' + cardIndex).removeClass("bg-primary");
                    $('#errorUpload' + cardIndex).removeClass("bg-danger");
                    $('#errorUpload' + cardIndex).removeClass("bg-success");

                    $('#errorUpload' + cardIndex).addClass("bg-primary");
                    $('#errorUpload' + cardIndex).html("");
                    $('#errorUpload' + cardIndex).fadeIn();
                    $('<span class="text-light d-block p-1 text-center">Processing file...</span>').appendTo('#errorUpload' + cardIndex);

                });
            },
        }).prop('disabled', !$.support.fileInput)
            .parent().addClass($.support.fileInput ? undefined : 'disabled');
    }

    function previewDoc(clickedButton) {
        var selectedCardId = $(clickedButton).closest('.card');
        var refId = selectedCardId.find('.refId').text(); // select box value

        if (refId !== null && refId !== '') {
            var url = "${pageContext.request.contextPath}/ClaimHandlerController/documentViewer?refNo=" + refId + "&jobRefNo=0&PREVIOUS_INSPECTION=0";

            // Get the screen width and height
            var screenWidth = window.screen.width;
            var screenHeight = window.screen.height;

            // Open the URL in a new browser window with full-screen size
            var newWindow = window.open(url, '_blank', 'toolbar=yes,scrollbars=yes,resizable=yes,width=' + screenWidth + ',height=' + screenHeight);
            if (newWindow) {
                newWindow.focus();
            } else {
                // Notify if the popup blocker prevents opening the new window
                notify('Unable to open preview in a new window. Please check your browser settings.', "danger");
            }
        } else {
            notify('Upload bank details first', "danger");
        }
    }


    function uploadFile(clickedButton) {
        //TODO need to check is alrady verify
        alert('upload why')
        var selectedCardId = $(clickedButton).closest('.card');
        var cardIndex = selectedCardId.find('.cardId').text(); // label value
        var isUpdate = selectedCardId.find('.isUpdate').text(); // label value
        var isVerify = selectedCardId.find('.isVerify').text(); // label value
        if (isVerify === "Y") {
            notify('Already verified.','warning');
            return;
        }
        if (isUpdate === "Y") {
            alert('upload why payment'+cardIndex)
            $('#paymentModal' + cardIndex).modal('show');
        } else {
            notify('Please save the details first.','warning');
        }
    }

    function closeModal(clickedButton) {
        var selectedCardId = $(clickedButton).closest('.card');
        var cardIndex = selectedCardId.find('.cardId').text(); // label value

        $('#paymentModal' + cardIndex).modal('hide');

    }

    function verifyDetails(clickedButton) {
        var selectedCardId = $(clickedButton).closest('.card');
        var cardIndex = selectedCardId.find('.cardId').text(); // label value
        var isVerify = selectedCardId.find('.isVerify'); // label value
        var instrumentType = selectedCardId.find('.instrumentType').val(); // select box value
        var payeeType = selectedCardId.find('.payeeType').val(); // select box value
        var payeeName = selectedCardId.find('.payeeName').val(); // select box value
        var checkboxVal = selectedCardId.find('.checkbox').prop('checked');
        var checkboxElement = selectedCardId.find('.checkbox');
        var isVerifyUser = <%= request.getParameter("isVerifyUser") %>;
        var isSpecialTeamUser = <%= request.getParameter("isSpecialTeamUser") %>;


        var isUpdate = selectedCardId.find('.isUpdate').text(); // label value
        var isUpload = selectedCardId.find('.isUpload').text(); // label value
        if (isUpdate === "N") {
            notify('Please save the details first');
            return; // Stop further execution
        }

        if (isUpload === "N") {
            notify('Please upload the document first');
            return; // Stop further execution
        }


        var URL_VERIFY = contextPath + "/BankDetailsController/update-verification";
        var URL_REJECT = contextPath + "/BankDetailsController/update-rejection";
        var claimNo = '${claimSuperDashboardDto.claimHandlerDto.claimNo}';
        let missingSelects = [];

        if (!instrumentType) {
            missingSelects.push("instrument type");
        }
        if (!payeeType) {
            missingSelects.push("payee type");
        }
        if (!payeeName) {
            missingSelects.push("payee name");
        }

        if (missingSelects.length === 1) {
            notify("Please select " + missingSelects[0], 'warning');
        } else if (missingSelects.length === 2) {
            notify("Please select " + missingSelects[0] + " and " + missingSelects[1], 'warning');
        } else if (missingSelects.length === 3) {
            notify("Please select all details", 'warning');
        }
        else {
            if(checkboxVal){
                $.ajax({
                    type: 'POST', url: URL_VERIFY, data: {
                        claimNo: claimNo,
                        instrumentType: instrumentType,
                        payeeType: payeeType,
                        payeeName: payeeName,
                        cardId: cardIndex,
                    }, success: function (response) {

                        isVerify.text("Y");
                        $('#btn-check'+cardIndex).show();
                        if(isSpecialTeamUser){
                            selectedCardId.find('button, select').not('.btn-view').not('.btn-verify').not('.btn-check').prop('disabled', true);
                        }else {
                            selectedCardId.find('button, select').not('.btn-view').not('.btn-add').not('.btn-verify').not('.btn-check').prop('disabled', true);
                        }

                        notify('Verify Successfully', "success");

                    }, error: function (error) {
                        // Handle the error response
                        console.error('Verification failed:', error);
                    }
                });

            }else {
                // You can customize the AJAX parameters based on your requirements
                $.ajax({
                    type: 'POST', url: URL_REJECT, data: {
                        claimNo: claimNo,
                        instrumentType: instrumentType,
                        payeeType: payeeType,
                        payeeName: payeeName,
                        cardId: cardIndex,
                    }, success: function (response) {

                        isVerify.text("R");
                        $('#btn-check'+cardIndex).hide();
                        if(isSpecialTeamUser){
                            selectedCardId.find('button, select').not('.btn-view').not('.btn-verify').not('.btn-check').prop('disabled', true);
                        }else {
                            selectedCardId.find('button, select').not('.btn-view').not('.btn-add').not('.btn-verify').not('.btn-check').prop('disabled', false);
                        }

                        notify('Reject Successfully', "success");
                    }, error: function (error) {
                        // Handle the error response
                        console.error('Rejection failed:', error);
                    }
                });
            }


        }
    }

    function saveDetails(clickedButton) {
        alert('save details')
        var selectedCardId = $(clickedButton).closest('.card');
        var cardIndex = selectedCardId.find('.cardId').text(); // label value
        var isUpdate = selectedCardId.find('.isUpdate'); // label value
        var instrumentType = selectedCardId.find('.instrumentType').val(); // select box value
        var payeeType = selectedCardId.find('.payeeType').val(); // select box value
        var payeeName = selectedCardId.find('.payeeName').val(); // select box value


        var URL_SAVE = contextPath + "/BankDetailsController/save-update";
        var claimNo = '${claimSuperDashboardDto.claimHandlerDto.claimNo}';
        let missingSelects = [];

        if (!instrumentType) {
            missingSelects.push("instrument type");
        }
        if (!payeeType) {
            missingSelects.push("payee type");
        }
        if (!payeeName) {
            missingSelects.push("payee name");
        }

        if (missingSelects.length === 1) {
            notify("Please select " + missingSelects[0], 'warning');
        } else if (missingSelects.length === 2) {
            notify("Please select " + missingSelects[0] + " and " + missingSelects[1], 'warning');
        } else if (missingSelects.length === 3) {
            notify("Please select all details", 'warning');
        } else {
            // You can customize the AJAX parameters based on your requirements
            $.ajax({
                type: 'POST', url: URL_SAVE, data: {
                    claimNo: claimNo,
                    instrumentType: instrumentType,
                    payeeType: payeeType,
                    payeeName: payeeName,
                    cardId: cardIndex,
                }, success: function (response) {
                    if (response.isVerified) {
                        notify('Already verified, cannot be changed', "danger");
                    } else {
                        isUpdate.text("Y");
                        selectedCardId.css('background-color', '#dae2ec');
                        notify('Update Successfully', "success");
                    }

                }, error: function (error) {
                    // Handle the error response
                    console.error('Update failed:', error);
                }
            });

        }


    }



    function sampleMethod(payeeIdValue, payeeDescValue) {
        alert('came: '+payeeIdValue+' '+payeeIdValue)
        // var originalCard = $('#paymentCard');
        // $('#cardContainer').children('.card').not(originalCard).remove();
        var isVerifyUser = '<%= request.getParameter("isVerifyUser") %>';
        var isSpecialTeamUser = '<%= request.getParameter("isSpecialTeamUser") %>';
        var isBranchUser = '<%= request.getParameter("isBranchUser") %>';
        var claimNo = '${claimsDto.claimNo}';
        var endCount = '${claimsDto.policyDto.endCount}';
        var policyNumber = '${claimsDto.policyDto.policyNumber}';
        var policyChannelType = '${claimsDto.policyDto.policyChannelType}';
        var customerName = '${claimsDto.policyDto.custName}';
        var customerNIC = '${claimsDto.policyDto.custNic}';

        var URL_INSTRUMENT = contextPath + "/BankDetailsController/instrument-types";
        var URL_PAYEE_TYPE = contextPath + "/BankDetailsController/payee-types";
        var URL_SEARCH = contextPath + "/BankDetailsController/search";
        var URL_INS = contextPath + "/BankDetailsController/insured-bank-details";

        var cardID=[];

        $.ajax({
            url: URL_SEARCH,
            method: 'GET',
            contentType: 'application/json',
            data: {
                claimNo: claimNo,
                endCount: endCount,
                policyNumber: policyNumber,
                customerName: customerName,
                policyChannelType: policyChannelType,
                payeeType: payeeIdValue,
                payeeName: payeeDescValue
            },
            success: function (data) {
                var prevData = JSON.parse(data);
                var bankDetailsDtoList = prevData.bankDetailsDtoList;
                var payeeTypeList = prevData.payeeTypeList;
                var payeeNameList = prevData.payeeNameList;
                var payeeTypeId = prevData.payeeTypeId;
                var payeeNameId = prevData.payeeNameId;
                console.log('data: '+data)

                var isInsuredExist = false;
                var isAllAreVerified = true;
                $.each(bankDetailsDtoList, function (index, item) {
                    const clonedCardId = prevCard(item);
                    var $clonedCard = $('#' + clonedCardId);
                    cardID.push($clonedCard);
                    var cloneInstrumentType = $clonedCard.find('.instrumentType');
                    var clonePayeeType = $clonedCard.find('.payeeType');
                    var clonePayeeName = $clonedCard.find('.payeeName');
                    var btnCheck = $clonedCard.find('.btn-check');
                    var btnView = $clonedCard.find('.btn-view');
                    var btnSave = $clonedCard.find('.btn-save');
                    var refId = $clonedCard.find('.refId');
                    refId.text(item.docRefNo);
                    cloneInstrumentType.val(item.instrumentType);
                    clonePayeeType.val(payeeTypeId);
                    $.each(item.payeeNameList, function (index, item) {
                        clonePayeeName.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                    });
                    clonePayeeName.val(payeeNameId);

                    if (item.verifyStatus == "Y") {
                        if (isSpecialTeamUser==='true') {
                            $initCard1.find('button, select').not('.btn-view').not('.btn-verify').not('.btn-check').prop('disabled', true);


                        } else if (isVerifyUser==='true') {
                            $initCard1.find('select').not('.instrumentType').prop('disabled', true);
                        } else {
                            $initCard1.find('button, select').not('.btn-view').prop('disabled', true);
                        }
                        btnCheck.show();

                    } else {
                        isAllAreVerified = false;
                    }

                    if (item.uploadStatus == "Y") {
                        btnView.show();
                    }

                });

                if (bankDetailsDtoList.length == 0 && isVerifyUser==='true') {
                    alert('else..')
                    if (payeeTypeId == 1) {
                        $.ajax({
                            url: URL_INS,
                            method: 'GET',
                            contentType: 'application/json',
                            data: {
                                claimNo: claimNo,
                                endCount: endCount,
                                policyNumber: policyNumber,
                                customerNIC: customerNIC
                            },
                            success: function (data) {
                                var insuredData = JSON.parse(data);

                                if (insuredData.length == 0) {
                                    alert('insuredData..')
                                    var initialCard1 = initialCard();
                                    var $initCard1 = $('#' + initialCard1);
                                    var clonePayeeType1 = $initCard1.find('.payeeType');
                                    var clonePayeeName1 = $initCard1.find('.payeeName');
                                    clonePayeeType1.val(payeeTypeId);


                                    $.each(payeeNameList, function (index, item) {
                                        clonePayeeName1.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                                    });

                                    clonePayeeName1.val(payeeNameId);

                                    if (isSpecialTeamUser==='true') {
                                        $initCard1.find('button, select').not('.btn-view').not('.btn-verify').not('.btn-check').prop('disabled', true);


                                    } else if (isVerifyUser==='true') {
                                        $initCard1.find('select').not('.instrumentType').prop('disabled', true);
                                    } else {
                                        $initCard1.find('button, select').not('.btn-view').prop('disabled', true);
                                    }
                                } else {
                                    alert('insuredData else..')
                                    const clonedCardId = duplicateCard();
                                    var $clonedCard = $('#' + clonedCardId);
                                    var cloneInstrumentType = $clonedCard.find('.instrumentType');
                                    var clonePayeeType = $clonedCard.find('.payeeType');
                                    var clonePayeeName = $clonedCard.find('.payeeName');
                                    var btnCheck = $clonedCard.find('.btn-check');
                                    var btnView = $clonedCard.find('.btn-view');
                                    var btnSave = $clonedCard.find('.btn-save');
                                    cloneInstrumentType.val(insuredData.instrumentType);
                                    clonePayeeType.val(payeeTypeId);
                                    clonePayeeType.val(payeeTypeId);


                                    $.each(payeeNameList, function (index, item) {
                                        clonePayeeName.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                                    });

                                    clonePayeeName.val(payeeNameId);
                                    if (isSpecialTeamUser==='true') {
                                        $clonedCard.find('button, select').not('.btn-view').not('.btn-verify').not('.btn-check').prop('disabled', true);


                                    } else if (isVerifyUser==='true') {
                                        $clonedCard.find('select').not('.instrumentType').prop('disabled', true);
                                    } else {
                                        $clonedCard.find('button, select').not('.btn-view').prop('disabled', true);
                                    }

                                }
                            },
                            error: function (error) {
                                console.error('AJAX request error insured bank details:', error);
                            }
                        });

                    } else {
                        alert('else else..')
                        var initialCard1 = initialCard();
                        var $initCard1 = $('#' + initialCard1);
                        var clonePayeeType1 = $initCard1.find('.payeeType');
                        var clonePayeeName1 = $initCard1.find('.payeeName');
                        clonePayeeType1.val(payeeTypeId);


                        $.each(payeeNameList, function (index, item) {
                            clonePayeeName1.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                        });

                        clonePayeeName1.val(payeeNameId);
                        alert('isSpecialTeamUser: '+ isSpecialTeamUser+" isVerifyUser:  "+isVerifyUser+" ")
                        if (isSpecialTeamUser==='true') {
                            $initCard1.find('button, select').not('.btn-view').not('.btn-verify').not('.btn-check').prop('disabled', true);


                        } else if (isVerifyUser==='true') {
                            $initCard1.find('select').not('.instrumentType').prop('disabled', true);
                        } else {
                            $initCard1.find('button, select').not('.btn-view').prop('disabled', true);
                        }


                    }

                }

            },
            error: function (error) {
                console.error('AJAX request error prev bank details:', error);
            }
        });

    }

    function  sampleMethod2() {
        var originalCard = $('#paymentCard');
        $('#cardContainer').children('.card').not(originalCard).remove();
        var isVerifyUser = '<%= request.getParameter("isVerifyUser") %>';
        var isSpecialTeamUser = '<%= request.getParameter("isSpecialTeamUser") %>';
        var isBranchUser = '<%= request.getParameter("isBranchUser") %>';
        var claimNo = '${claimsDto.claimNo}';
        var endCount = '${claimsDto.policyDto.endCount}';
        var policyNumber = '${claimsDto.policyDto.policyNumber}';
        var policyChannelType = '${claimsDto.policyDto.policyChannelType}';
        var customerName = '${claimsDto.policyDto.custName}';
        var customerNIC = '${claimsDto.policyDto.custNic}';

        var URL_INSTRUMENT = contextPath + "/BankDetailsController/instrument-types";
        var URL_PAYEE_TYPE = contextPath + "/BankDetailsController/payee-types";
        var URL_PREV = contextPath + "/BankDetailsController/saved-bank-details";
        var URL_SEARCH = contextPath + "/BankDetailsController/search";
        var URL_INS = contextPath + "/BankDetailsController/insured-bank-details";

        $.ajax({
            url: URL_PREV,
            method: 'GET',
            contentType: 'application/json',
            data: {claimNo: claimNo, endCount: endCount, policyNumber: policyNumber,customerName: customerName,policyChannelType: policyChannelType},
            success: function (data) {
                var isInsuredExist = false;
                var prevData = JSON.parse(data);
                var isAllAreVerified=true;

                $.each(prevData, function (index, item) {
                    if (item.payeeType == 1) {
                        isInsuredExist = true;
                    }
                    const clonedCardId = prevCard(item);
                    var $clonedCard = $('#' + clonedCardId);
                    var cloneInstrumentType = $clonedCard.find('.instrumentType');
                    var clonePayeeType = $clonedCard.find('.payeeType');
                    var clonePayeeName = $clonedCard.find('.payeeName');
                    var btnCheck = $clonedCard.find('.btn-check');
                    var btnView = $clonedCard.find('.btn-view');
                    var btnSave = $clonedCard.find('.btn-save');
                    var refId = $clonedCard.find('.refId');
                    refId.text(item.docRefNo);



                    cloneInstrumentType.val(item.instrumentType);
                    clonePayeeType.val(item.payeeType);


                    $.each(item.payeeNameList, function (index, item) {
                        clonePayeeName.append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                    });

                    clonePayeeName.val(item.payeeName);


                    // clonePayeeName.append($('<option>').val(item.payeeName).html(item.payeeName)).trigger("chosen:updated");





                    if(item.verifyStatus=="Y"){
                        // btnSave.prop('disabled', true);
                        $clonedCard.find('button, select').not('.btn-view').not('.btn-add').not('.btn-verify').not('.btn-check').prop('disabled', true);
                        btnCheck.show();

                    }else {
                        isAllAreVerified=false;
                    }

                    if(item.uploadStatus=="Y"){
                        btnView.show();
                    }

                });

                if (!isInsuredExist) {
                    alert('customerName: '+customerName+'customerNIC: '+customerNIC)
                    $.ajax({
                        url: URL_INS,
                        method: 'GET',
                        contentType: 'application/json',
                        data: {claimNo: claimNo, endCount: endCount, policyNumber: policyNumber,customerNIC: customerNIC},
                        success: function (data) {
                            var insuredData = JSON.parse(data);

                            if (insuredData.length == 0) {
                                initialCard();
                            } else {
                                const clonedCardId = duplicateCard();
                                var $clonedCard = $('#' + clonedCardId);
                                var cloneInstrumentType = $clonedCard.find('.instrumentType');
                                var clonePayeeType = $clonedCard.find('.payeeType');
                                var clonePayeeName = $clonedCard.find('.payeeName');
                                var btnCheck = $clonedCard.find('.btn-check');
                                var btnView = $clonedCard.find('.btn-view');
                                var btnSave = $clonedCard.find('.btn-save');
                                cloneInstrumentType.val(insuredData.instrumentType);
                                clonePayeeType.val(insuredData.payeeType);
                                clonePayeeName.append($('<option>').val(insuredData.payeeName).html(insuredData.payeeName)).trigger("chosen:updated");

                            }
                        },
                        error: function (error) {
                            console.error('AJAX request error insured bank details:', error);
                        }
                    });
                }else {
                    initialCard();
                }
            },
            error: function (error) {
                console.error('AJAX request error prev bank details:', error);
            }
        });

    }

</script>

</body>

</html>
