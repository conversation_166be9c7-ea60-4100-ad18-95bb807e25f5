<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/21/2018
  Time: 7:54 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>

<div class="col-md-12">
    <table width="100%" cellpadding="0" cellspacing="1"
           class="table table-hover table-xs dataTable no-footer dtr-inline">
        <thead>
        <tr>
            <th scope="col" class="tbl_row_header">No</th>
            <th scope="col" class="tbl_row_header">Reminder No</th>
            <th scope="col" class="tbl_row_header">Claim No</th>
            <th scope="col" class="tbl_row_header">Generated User</th>
            <th scope="col" class="tbl_row_header">Generated Date / Time</th>
            <th scope="col" class="tbl_row_header">Action</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="recordNo" value="0"/>
        <c:set var="letterIndex" value="${reminderLetterFormDto.reminderPrintSummaryList.size()}"/>
        <c:forEach var="reminderPrintSummary" items="${reminderLetterFormDto.reminderPrintSummaryList}">
            <c:set var="sup" value="st"/>
            <c:choose>
                <c:when test="${letterIndex==1}">
                    <c:set var="sup" value="st"/>
                </c:when>
                <c:when test="${letterIndex==2}">
                    <c:set var="sup" value="nd"/>
                </c:when>
                <c:when test="${letterIndex==3}">
                    <c:set var="sup" value="rd"/>
                </c:when>
                <c:when test="${letterIndex==4 || letterIndex==5 || letterIndex==6}">
                    <c:set var="sup" value="th"/>
                </c:when>
            </c:choose>
            <tr>
                <td>${recordNo+1}</td>
                <td>${letterIndex}<sup>${sup}</sup> Reminder</td>
                <td>${reminderPrintSummary.claimNo}</td>
                <td>${reminderPrintSummary.generatedUserId}</td>
                <td>${reminderPrintSummary.generatedDateTime}</td>
                <td>
                    <a href="${pageContext.request.contextPath}/ClaimReportController/viewReminderLetter?reminderSummaryRefId=${reminderPrintSummary.reminderSummaryRefId}"
                       class="claimReminderLetterView">
                    <button type="button" name="cmdViewLetter${recordNo}" id="cmdViewLetter${recordNo}"
                            onclick=""
                            class="btn btn-primary mt-2">
                        View ${letterIndex}<sup>${sup}</sup> Reminder
                    </button>
                    </a>
                    <script type="text/javascript">
                        $('.claimReminderLetterView').popupWindow({
                            height: screen.height,
                            width: screen.width,
                            resizable: 1,
                            centerScreen: 1,
                            scrollbars: 1,
                            windowName: '${letterIndex}<sup>${sup}</sup> Reminder'
                        });
                    </script>
                </td>
            </tr>
            <c:set var="letterIndex" value="${letterIndex-1}"/>
            <c:set var="recordNo" value="${recordNo+1}"/>
        </c:forEach>

        </tbody>
    </table>
</div>
