<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 1/25/2019
  Time: 2:08 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>

<div class="col-md-12">
    <table width="100%" cellpadding="0" cellspacing="1"
           class="table table-hover table-xs dataTable no-footer dtr-inline">
        <thead>
        <tr class="bg-primary">
            <th scope="col">No</th>
            <th scope="col">Customer Remark</th>
            <th scope="col">Special Remark</th>
            <th scope="col">Print Date/Time</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="previous" items="${previousList}">
            <tr>
                <th scope="row">${previous.no}</th>
                <td>${previous.acknowledgementRemark}</td>
                <td>${previous.specialRemark}</td>
                <td>${previous.printDatetime}</td>
                <td>
                    <button class="btn btn-primary" onclick="printPrevious('${previous.acknowledgementId}');"
                            type="button">Print
                    </button>
                </td>
            </tr>

        </c:forEach>

        </tbody>
    </table>
</div>
<script type="text/javascript">
    function printPrevious(id) {
        var myWindow = window.open(contextPath + "/ClaimReportController/AcknowledgemenLetter?acknowledgementId=" + id, 'newwindow', 'width=1366,height=768');
        myWindow.focus();
    }
</script>
