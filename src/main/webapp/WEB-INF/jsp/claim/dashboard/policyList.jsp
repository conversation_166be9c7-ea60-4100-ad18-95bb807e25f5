<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%--
    Document   : policyList
    Created on : Feb 11, 2011, 12:01:25 PM
    Modified on : Feb 06, 2018
    Product    : Aviva Claim System
    Copyright  : Copyright, 2009-2010 (c)
    Company    : M.I. Synergy (Pvt) Ltd
    Author     : Kelum Sepala
    version 2.0
--%>
<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%
    String ERROR = "";
    int TYPE = 0;
    try {
        session.removeAttribute("TYPE");
        TYPE = Integer.valueOf(request.getParameter("TYPE"));
        session.setAttribute("TYPE", TYPE);
    } catch (Exception e) {
    }
%>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>${CompanyTitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <script language="javascript" type="text/javascript">
        var contextPath = "${pageContext.request.contextPath}";
        var viewType = "${TYPE}";
        var currentDate='${Current_Date}';
        $(function (){
            $("#accidentDate").datetimepicker({
                defaultDate: currentDate,
                format: 'YYYY-MM-DD',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                }
            });
        });

        function setDialogBox() {
            $(document).ready(function () {

                $('#dialog').modal({
                    show: true
                });

                $('#testconfirmJQ').submit(function () {
                    return false;
                });

            });
        }

        function showDialogbox(str) {
            setDialogBox();
            $("#dialog-email").html(str);
            $('#dialog').modal('show')
        }


        function setLoading(url) {
//            showLoader()
            window.location = url;
        }

        document.onkeyup = KeyCheck;

        function KeyCheck(e) {
            var KeyID = (window.event) ? event.keyCode : e.keyCode;
            switch (KeyID) {
                case 13:
                    search();
                    break;
                case 17:
                    break;
                case 19:
                    break;
                case 37:
                    break;
                case 38:
                    break;
                case 39:
                    break;
                case 40:
                    break;
            }
        }

        function Trim(str) {
            while (str.substring(0, 1) == ' ') // check for white spaces from beginning
            {
                str = str.substring(1, str.length);
            }
            while (str.substring(str.length - 1, str.length) == ' ') // check white space from end
            {
                str = str.substring(0, str.length - 1);
            }

            return str;
        }

        function okButton() {
            $('#dialog').modal('hide');

            if ($("#txtVehicleNumber").val() == "") {
                $("#txtVehicleNumber").focus();
                return;
            }
            if ($("#txtName").val() == "") {
                $("#txtName").focus();
            }
        }


        function pageSubmit(type) {
            var vehNo, refNo, name, chassisNo, policyChannelType;
            vehNo = $("#txtVehicleNumber").val();
            refNo = $("#txtRefNumber").val();
            name = $("#txtInsuredName").val();
            chassisNo = $("#txtChassisNo").val();
            policyChannelType = $("#policyChannelType").val();
            if (((vehNo.length > 0 || name.length > 0) || (refNo.length > 0 || name.length > 0))) {
                document.frmForm.cmdSearch.disabled = true;
//                document.frmForm.cmdClose.disabled = true;
                document.frmForm.cmdCvrNote.disabled = true;
                showLoader();
                $.ajax({
                    url: contextPath + "/CallCenter/coverNote",
                    type: 'POST',
                    data: {
                        P_VEH_NO: vehNo,
                        P_REF_NO: refNo,
                        P_NAME: name,
                        P_CHASSIS_NO: chassisNo,
                        POLICY_CHANNEL_TYPE: policyChannelType
                    },
                    success: function (result) {
                        hideLoader();
                        if (result.errorCode == 200) {
                            $("#P_POL_REF_NO").val(result.dtoFieldName);
                            AlertErrorTirdprty("Cover Note successfully created", 'success');
                            document.getElementById('frmForm').action = contextPath + "/CallCenter/viewReportAccident";
                            document.getElementById('frmForm').submit();
                        } else {
                            AlertErrorTirdprty(result.message, 'danger');
                            document.frmForm.cmdSearch.disabled = false;
//                            document.frmForm.cmdClose.disabled = false;
                            document.frmForm.cmdCvrNote.disabled = false;
                        }

                    }, error: function (request, status, error) {
                        hideLoader();
                        AlertErrorTirdprty(result.message, 'danger');
                    }
                });

            } else {
                AlertErrorTirdprty("Please Enter either Vehicle Number, Cover Note Number or Insured Name ", 'danger');


            }
        }


    </script>
</head>
<body class="scroll" onload="hideLoader();">
<div class="container-fluid">
    <form name="frmForm" id="frmForm" method="post" action="">
        <input name="P_POL_REF_NO" id="P_POL_REF_NO" type="hidden"/>
        <div class="row">
            <div class="col-sm-12 bg-dark py-2">
                <h5>Policy Details List</h5>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 py-1 mt-3">
                <p class='progressview m-0'>
                </p>
            </div>
        </div>
        <div class="ErrorNote"><%=ERROR%>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div id="accordion" class="accordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h5 class="mb-0">
                                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                                   aria-expanded="true" aria-controls="collapseOne">
                                    Search Here <i class="fa fa-search"></i>
                                </a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne"
                             data-parent="#accordion">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label for="txtVehicleNumber" class="col-sm-4 col-form-label"> Vehicle /
                                                Trade Plate Number
                                            </label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder="Vehicle / Trade Plate Number"
                                                       name="txtVehicleNumber"
                                                       id="txtVehicleNumber" value="${searchVehicleNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolNumber" class="col-sm-4 col-form-label"> Policy
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder="Policy Number" name="txtPolNumber" id="txtPolNumber"
                                                       value="${searchPolicyNo}"
                                                >
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label for="txtChassisNo" class="col-sm-4 col-form-label"> Chassis
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder=" Chassis Number" name="txtChassisNo"
                                                       id="txtChassisNo" value="${searchChassisNo}"
                                                >
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtEngineNo" class="col-sm-4 col-form-label"> Engine
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder=" Engine Number" name="txtEngineNo" id="txtEngineNo"
                                                       value="${searchEngineNo}"
                                                >
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtPolicyStatus" class="col-sm-4 col-form-label">Policy
                                                Status </label>
                                            <div class="col-sm-8">
                                                <select name="txtPolicyStatus" id="txtPolicyStatus"
                                                        class="form-control form-control-sm">
                                                    <option value="0">All</option>
                                                    <option value="INF">INFORCE</option>
                                                    <option value="EXP">EXPIRED</option>
                                                    <option value="PRP">PROPOSAL</option>
                                                    <option value="LAP">LAPSED</option>
                                                    <option value="CAN">CANCEL</option>
                                                    <option value="QOT">QUOTATION</option>
                                                    <option value="USED">USED</option>
                                                </select>
                                                <script>
                                                    $("#txtPolicyStatus").val("${searchClaimStatus==null?'0':searchClaimStatus}");
                                                </script>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">

                                        <div class="form-group row">
                                            <label for="txtRefNumber" class="col-sm-4 col-form-label"> Cover Note
                                                Number </label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder="Cover Note Number" name="coverNoteNo"
                                                       id="txtRefNumber" value="${searchRefNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtInsuredName" class="col-sm-4 col-form-label"> Insured
                                                Name </label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder="Insured Name" name="txtInsuredName"
                                                       id="txtInsuredName" value="${searchInsuredName}"
                                                >
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="txtInsuredNic" class="col-sm-4 col-form-label"> Insured
                                                NIC </label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control form-control-sm"
                                                       placeholder="Insured NIC" name="txtInsuredNic" id="txtInsuredNic"
                                                       value="${searchInsuredNic}"
                                                >
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="policyChannelType" class="col-sm-4 col-form-label">Policy
                                                Channel
                                                Type </label>
                                            <div class="col-sm-8">
                                                <select name="policyChannelType" id="policyChannelType"
                                                        class="form-control form-control-sm">
                                                    <option value="">Please Select</option>
                                                    <option value="TAKAFUL">TAKAFUL</option>
                                                    <option value="CONVENTIONAL">CONVENTIONAL</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label for="accidentDate" class="col-sm-4 col-form-label"> Accident </label>
                                            <div class="col-sm-8">
                                                <input name="accidentDate" id="accidentDate" type="text"
                                                       class="form-control form-control-sm"
                                                       placeholder="Accident">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 text-right">
                                                <c:if test="${TYPE!=4 and TYPE!=5}">
                                                    <button class="btn btn-primary" type="button" name="cmdCvrNote"
                                                            id="cmdCvrNote" onclick="pageSubmit('CvrNote')">
                                                        Cover Note
                                                    </button>
                                                </c:if>

                                                <button class="btn btn-primary" type="button" name="cmdSearch"
                                                        id="cmdSearch" onclick="search()">
                                                    Search
                                                </button>
                                                <c:if test="${TYPE!=4}">
                                                    <a class="btn btn-secondary" type="button" name="cmdClose"
                                                       id="cmdClose"
                                                       href="${pageContext.request.contextPath}/welcome.do">Close
                                                    </a>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="but_cont" style="float:right">
                    <input type="hidden" name="txtClickPos" id="txtClickPos"/>
                </div>
                <div class="card mt-3">
                    <div class="card-body table-bg">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col">
                                        <p class="mb-1 float-left"><span
                                                class="badge badge-pill badge-success  border">&nbsp;</span>
                                            &nbsp;&nbsp;Inforce Policy</p>
                                    </div>
                                    <div class="col">
                                        <p class="mb-1 float-left"><span
                                                class="badge badge-pill badge-primary border ">&nbsp;</span> &nbsp;&nbsp;
                                            Proposal </p>
                                    </div>
                                    <div class="col">
                                        <p class="mb-1 float-left"><span class="badge badge-pill badge-danger border ">&nbsp;</span>
                                            &nbsp;&nbsp; Policy Cancelled </p>
                                    </div>
                                    <div class="col">
                                        <p class="mb-1 float-left"><span
                                                class="badge badge-pill badge-warning border ">&nbsp;</span>&nbsp;&nbsp;
                                            Policy Expired </p>
                                    </div>
                                    <div class="col">
                                        <p class="mb-1 float-left"><span class="badge badge-pill badge-info border">&nbsp;</span>&nbsp;&nbsp;
                                            Policy Lapsed </p>
                                    </div>
                                    <div class="col">
                                        <p class="mb-1 float-left"><span
                                                class="badge badge-pill badge-dark">&nbsp;</span>&nbsp;&nbsp;
                                            Policy Suspended </p>
                                    </div>
                                    <div class="col">
                                        <p class="mb-1 float-left"><span
                                                class="badge badge-pill badge-secondary">&nbsp;</span>&nbsp;&nbsp;
                                            Third Party </p>
                                    </div>

                                </div>
                                <hr class="my-2">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mt-2 ">
                                            <table id="demo-dt-basic" class="table table-sm table-hover" cellspacing="0"
                                                   style="cursor:pointer" width="100%">
                                                <thead>
                                                <tr>
                                                    <th>ref no</th>
                                                    <th>No</th>
                                                    <th class="min-mobile">Policy No</th>
                                                    <th>Policy Channel Type</th>
                                                    <th>Renewal Count</th>
                                                    <th>Endorsement Count</th>
                                                    <th>Vehicle No</th>
                                                    <th>Chassis Number</th>
                                                    <th>Cover Note Number</th>
                                                    <th>Customer Name</th>
                                                    <th>Latest Intimation Date</th>
                                                    <th>Inception Date</th>
                                                    <th>Expire Date</th>
                                                    <th>Policy Status</th>
                                                    <th class="min-mobile"></th>
                                                </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Modal -->
                            <div class="modal fade bd-example-modal-lg" id="dialog" tabindex="-1" role="dialog"
                                 aria-labelledby="exampleModalLabel" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h6 class="modal-title text-warning" id="exampleModalLabel">Warning <i
                                                    class="fa fa-info-circle text-warning"></i></h6>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body text-center">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h5 id="dialog-email" class="mt-2 text-muted"></h5>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" onclick="okButton()"
                                                    data-dismiss="modal">OK
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/resources/js/custom/claimhandler/policy-datatables.js?v5"></script>

</body>
</html>
