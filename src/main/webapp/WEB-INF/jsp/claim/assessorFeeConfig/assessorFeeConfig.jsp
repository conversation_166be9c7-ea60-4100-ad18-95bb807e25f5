<%--
  Created by IntelliJ IDEA.
  User: HP
  Date: 6/19/2024
  Time: 11:40 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<html>
<head>
    <title>Title</title>
    <link href="${pageContext.request.contextPath}/resources/imageviewer/jquery.magnify.css" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/resources/file-upload/css/jquery.fileupload.css">
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
</head>
<body style="padding: 10px" onload="hideLoader()">

<div id="accordion" class="accordion">
    <div class="card">
        <div class="card-header" id="headingOne">
            <h5 class="mb-0">
                <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                   aria-expanded="true" aria-controls="collapseOne">
                    Search Here <i class="fa fa-search"></i>
                </a>
            </h5>
        </div>
        <div id="collapseOne" class="collapse show p-3" aria-labelledby="headingOne"
             data-parent="#accordion">

            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" data-toggle="modal"
                            data-target="#addModal" id="addBtn"><i class="fa fa-plus"></i> Add Assessor Fee
                    </button>
                </div>
            </div>


            <div class="row mt-3">
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">ID</label>
                        <div class="col-sm-8">
                            <input type="number" class="form-control form-control-sm "
                                   id="txtId"
                                   placeholder="ID"
                                   min="1"
                                   name="txtId">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Day Type</label>
                        <div class="col-sm-8">
                            <select class="form-control"
                                    placeholder="Day Type"
                                    id="txtDayType"
                                    name="txtDayType" required>
                                <option value="">-- Select Day Type --</option>
                                <c:forEach var="item" items="${dayTypeList}">
                                    <option value="${item.value}">${item.label}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="form-group row">
                        <label class="col-sm-4 col-form-label">Inspection Type</label>
                        <div class="col-sm-8">
                            <select class="form-control"
                                    placeholder="Inspection Type"
                                    id="txtInspectionType"
                                    name="txtInspectionType" required>
                                <option value="">-- Select Inspection Type --</option>
                                <c:forEach var="item" items="${inspectionTypeList}">
                                    <option value="${item.value}">${item.label}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-lg-12">
                    <button type="button" class="btn btn-primary float-right" id="searchBtn">Search</button>
                    <button type="button" class="btn btn-secondary float-right mr-1" id="clearBtn">Clear</button>
                </div>
            </div>

            <!-- Edit Modal -->
            <%--<div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editModalLabel">Edit Holiday Type</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editForm">
                                <div class="form-group">
                                    <label>Holiday Type Name</label>
                                    <input type="text" class="form-control">
                                </div>

                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control"></textarea>
                                </div>
                                <button type="button" class="btn btn-primary float-right" id="saveChanges">Update
                                </button>
                                <button type="button" class="btn btn-secondary float-right mr-1">Cancel
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>--%>

            <!-- Add Modal -->
            <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
                 aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addModalLabel">Add Claim Exceptional</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="addForm">
                                <input type="hidden" class="form-control" id="assessorFeeDetailId"
                                       name="assessorFeeDetailId">

                                <div class="form-group">
                                    <label for="dayTypeId">Day Type</label>
                                    <select class="form-control" id="dayTypeId" name="dayTypeId" required>
                                        <option value="">-- Select Day Type --</option>
                                        <c:forEach var="item" items="${dayTypeList}">
                                            <option value="${item.value}">${item.label}</option>
                                        </c:forEach>
<%--                                        <option value="1">Week Days</option>--%>
<%--                                        <option value="2">Saturday/Sunday/Mercantile Holiday</option>--%>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="inspectionTypeId">Inspection Type</label>
                                    <select class="form-control" id="inspectionTypeId" name="inspectionTypeId" required>
                                        <option value="">-- Select Inspection Type --</option>

                                        <!-- Loop over inspectionTypeList from request scope -->
                                        <c:forEach var="item" items="${inspectionTypeList}">
                                            <option value="${item.value}">${item.label}</option>
                                        </c:forEach>

<%--                                        <option value="1">ARI</option>--%>
<%--                                        <option value="2">Test Inspection</option>--%>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>Permanent Staff Fee</label>
                                    <input type="number" class="form-control"
                                           id="permanentStaffFee"
                                           min="0"
                                           name="permanentStaffFee"
                                           placeholder="Permanent Staff Fee"
                                           required
                                    <%--                                           maxlength="225"--%>
                                    <%--                                           oninput="validateMaxLength(this, 255)"--%>
                                    >
                                    <small id="holidayTypeNameError" class="text-danger" style="display: none;">Maximum
                                        length is 255 characters.</small>
                                </div>

                                <div class="form-group">
                                    <label>Hybrid Staff Fee</label>
                                    <input class="form-control"
                                           type="number"
                                           id="hybridStaffFee"
                                           name="hybridStaffFee"
                                           min="0"
                                           placeholder="Hybrid Staff Fee"
                                           required
                                    <%--                                              maxlength="225"--%>
                                    <%--                                              oninput="validateMaxLength(this, 255)"--%>
                                    >
                                    </input>
                                    <small id="claimTypeError" class="text-danger" style="display: none;">Maximum
                                        length is 255 characters.</small>
                                </div>

                                <%-- <div class="form-group">
                                     <label>Remark</label>
                                     <textarea class="form-control"
                                               id="remark"
                                               name="remark"
                                               placeholder="Remark"
                                               required
                                     &lt;%&ndash;                                              maxlength="225"&ndash;%&gt;
                                     &lt;%&ndash;                                              oninput="validateMaxLength(this, 255)"&ndash;%&gt;
                                     >
                                     </textarea>
                                     <small id="holidayTypeDescError" class="text-danger" style="display: none;">Maximum
                                         length is 255 characters.</small>
                                 </div>--%>

                                <div class="form-group">
                                    <label for="fromTime">From Time</label>
                                    <input type="time" class="form-control" id="fromTime" name="fromTime" required
                                           step="1">
                                </div>

                                <div class="form-group">
                                    <label for="toTime">To Time</label>
                                    <input type="time" class="form-control" id="toTime" name="toTime" required step="1">
                                </div>


                                <button type="button" class="btn btn-primary float-right" id="saveButton">Save</button>
                                <button type="button" class="btn btn-primary float-right" id="updateButton"
                                        style="display: none;">Update
                                </button>
                                <button type="button" class="btn btn-secondary float-right mr-1" id="cancelButton">
                                    Cancel
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card mt-3">
        <div class="card-body table-bg">
            <div class="row">
                <div class="col-lg-12 pl-0 pr-0">
                    <table id="example" class="table table-sm table-striped table-bordered" style="width:100%">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Inspection Type</th>
                            <th>Day Type</th>
                            <th>From Time</th>
                            <th>To Time</th>
                            <th>Permanent Staff Fee</th>
                            <th>Hybrid Staff Fee</th>
                            <th>Created User</th>
                            <th>Created Date/Time</th>
                            <th>Modified User</th>
                            <th>Modified Date/Time</th>
                            <th>Delete</th>
                            <th>Edit</th>
                        </tr>
                        </thead>
                        <%-- <tbody>
                         <tr>
                             <td>HD001</td>
                             <td>Poya</td>
                             <td>Holiday</td>
                             <td style="text-align: center">
                                 <button class="btn btn-danger btn-sm delete-btn" style="width: auto">
                                     <i class="fa fa-trash"></i>
                                 </button>
                             </td>
                             <td style="text-align: center">
                                 <button class="btn btn-primary btn-sm edit-btn" data-toggle="modal"
                                         data-target="#editModal" style="width: auto">
                                     <i class="fa fa-edit"></i>
                                 </button>
                             </td>

                         </tr>
                         </tbody>--%>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    let isSearch = 0;

    $(document).ready(function () {

        fetchAllAssessorFeeDetails();

        $('#example').on('click', '.edit-btn', function () {
            const currentRow = $(this).closest('tr');
            const taskName = currentRow.find('td:eq(4)').text();
            $('#editModal #taskName').val(taskName);
        });

        $('#example').on('click', '.delete-btn', function () {
            const assessorFeeDetailId = $(this).data('id');
            bootbox.confirm({
                // message: "<p>Are you sure you want to delete this holiday Type?</p><textarea id='delete-reason' placeholder='Delete Reason' rows='4' style='width: 100%'></textarea>",
                message: "<p>Are you sure you want to delete this assessor fee detail?</p>",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-danger'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-secondary'
                    }
                },
                callback: function (result) {
                    if (result) {
                        const reason = $('#delete-reason').val();
                        // Perform the delete action here, optionally using the reason
                        deleteAssessorFeeDetailById(assessorFeeDetailId, reason)
                    } else {
                        console.log('Delete canceled');
                    }
                }
            });
        });

        // Save button click event
        $('#saveButton').click(function () {
            if (validateForm()) {
                const permanentStaffFee = $('#permanentStaffFee').val();
                const fromTime = $('#fromTime').val();
                const toTime = $('#toTime').val();
                const hybridStaffFee = $('#hybridStaffFee').val();
                const inspectionTypeId = $('#inspectionTypeId').val();
                const dayTypeId = $('#dayTypeId').val();

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/assessorFeeController/saveAssessorFee',
                    data: {
                        inspectionTypeId: inspectionTypeId,
                        dayTypeId: dayTypeId,
                        permanentStaffFee: permanentStaffFee,
                        hybridStaffFee: hybridStaffFee,
                        fromTime: fromTime,
                        toTime: toTime
                    },
                    success: function (response) {
                        if (typeof response === 'string' && response?.trim() === '"Saved Successfully"') {
                            notify('Assessor fee detail saved successfully.', "success");
                            $('#addModal').modal('hide');
                            fetchAllAssessorFeeDetails();
                        } else {
                            // notify('Cannot be saved. ', "danger");
                            notify(response.message, "danger");
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        notify('An error occurred while saving assessor fee details: ' + error, "danger");
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }

        });


        // Update button click event
        $('#updateButton').click(function () {
            if (validateForm()) {
                const permanentStaffFee = $('#permanentStaffFee').val();
                const fromTime = $('#fromTime').val();
                const toTime = $('#toTime').val();
                const hybridStaffFee = $('#hybridStaffFee').val();
                const assessorFeeDetailId = $('#assessorFeeDetailId').val();
                const inspectionTypeId = $('#inspectionTypeId').val();
                const dayTypeId = $('#dayTypeId').val();

                $.ajax({
                    type: 'POST',
                    url: '${pageContext.request.contextPath}/assessorFeeController/updateAssessorFee',
                    data: {
                        assessorFeeDetailId: assessorFeeDetailId,
                        inspectionTypeId: inspectionTypeId,
                        dayTypeId: dayTypeId,
                        permanentStaffFee: permanentStaffFee,
                        hybridStaffFee: hybridStaffFee,
                        fromTime: fromTime,
                        toTime: toTime
                    },
                    success: function (response) {
                        if (typeof response === 'string' && response?.trim() === '"Saved Successfully"') {
                            notify('Assessor fee details updated successfully.', "success");
                            $('#addModal').modal('hide');
                            fetchAllAssessorFeeDetails();
                        } else {
                            // notify('Cannot be saved. ', "danger");
                            notify(response.message, "danger");
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        alert('An error occurred while updating assessor fee details: ' + error);
                    }
                });
            } else {
                notify('Please Submit All Required Data', "danger");
            }
        });


        function deleteAssessorFeeDetailById(assessorFeeDetailId, deleteReason) {
            $.ajax({
                type: 'post',
                url: '${pageContext.request.contextPath}/assessorFeeController/deleteAssessorFee',
                data: {
                    assessorFeeDetailId: assessorFeeDetailId
                },
                success: function (response) {
                    if (typeof response === 'string' && response?.trim() === '"SUCCESS"') {
                        notify('Assessor fee details deleted successfully.', "success");
                        $('#addModal').modal('hide');
                        fetchAllAssessorFeeDetails();
                    } else {
                        notify(response.message, "danger");
                    }
                },
                error: function (error) {
                    console.log(error);
                    alert('An error occurred while fetching assessor fee details: ' + error);
                }
            });
        }

    });

    function convertTo24HourFormat(timeStr) {
        const [time, modifier] = timeStr.split(' ');
        let [hours, minutes, seconds] = time.split(':').map(Number);

        if (modifier === 'PM' && hours !== 12) {
            hours += 12;
        } else if (modifier === 'AM' && hours === 12) {
            hours = 0;
        }

        // Pad hours, minutes, seconds to 2 digits
        hours = String(hours).padStart(2, '0');
        minutes = String(minutes).padStart(2, '0');
        seconds = String(seconds).padStart(2, '0');

        console.log("hours:", hours);
        console.log("minutes:", minutes);
        console.log("seconds:", seconds);

        let finalTime = hours + ':' + minutes + ':' + seconds;
        console.log("final time :", finalTime);

        return finalTime;
        <%--return `${hours}:${minutes}:${seconds}`;--%>
    }

    function getAssessorFeeDetailById(assessorFeeDetailId) {
        $.ajax({
            type: 'GET',
            url: '${pageContext.request.contextPath}/assessorFeeController/viewAssessorFee',
            data: {assessorFeeDetailId: assessorFeeDetailId},
            success: function (response) {
                $('#assessorFeeDetailId').val(response.assessorFeeDetailId);
                $('#permanentStaffFee').val(response.permanentStaffFee);
                $('#hybridStaffFee').val(response.hybridStaffFee);

                const fromTime24 = convertTo24HourFormat(response.fromTime); // "06:00:00"
                const toTime24 = convertTo24HourFormat(response.toTime);     // "16:00:00"

                console.log("fromTime:", fromTime24);
                console.log("toTime:", toTime24);

                // Convert 12-hour time to 24-hour format for input[type="time"]
                $('#fromTime').val(fromTime24);
                $('#toTime').val(toTime24);

                $('#inspectionTypeId').val(response.inspectionTypeId);
                $('#dayTypeId').val(response.dayTypeId);
                $('#saveButton').hide();
                $('#updateButton').show();
                $('#addModal').modal('show');
                $('#addModalLabel').text("Update Assessor Fee");

                removeValidateClass();
            },
            error: function (error) {
                console.log(error);
                alert('An error occurred while fetching claim exceptional case details: ' + error);
            }
        });
    }

    function fetchAllAssessorFeeDetails() {
        $('#example').DataTable({
            serverSide: true,
            ajax: {
                url: '${pageContext.request.contextPath}/assessorFeeController/viewAssessorFeeList',
                type: 'POST',
                "data": function (d) {
                    d.assessorFeeDetailId = $("#txtId").val();
                    d.inspectionTypeId = $("#txtInspectionType").val();
                    d.dayTypeId = $("#txtDayType").val();
                }
            },
            "bDestroy": true,
            destroy: true,
            searching: false,
            columns: [
                {data: 'assessorFeeDetailId'},
                {
                    data: 'inspectionTypeDescription',
                    defaultContent: "",
                    orderable: false
                },
                {
                    data: 'dayTypeDescription',
                    defaultContent: "",
                    orderable: false
                },
                {
                    data: 'fromTime',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'toTime',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'permanentStaffFee',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'hybridStaffFee',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'inputUser',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'inputDateTime',
                    orderable: false,
                    render: function (data, type, row) {
                        // Format inputDateTime from '7/8/2024, 12:09:27 PM' to '2024-07-08 12:09:27'
                        return data ? formatDate(data) : "";
                    }
                    /*render: function (data, type, row) {
                        // Format inputDateTime from '2024-07-08T12:09:27' to '2024-07-08 12:09:27'
                        return data ? new Date(data).toLocaleString() : ""; // Adjust locale as needed
                    }*/
                },
                {
                    data: 'lastModifiedUser',
                    orderable: false,
                    defaultContent: ""
                },
                {
                    data: 'lastModifiedDateTime',
                    orderable: false,
                    render: function (data, type, row) {
                        // Format inputDateTime from '7/8/2024, 12:09:27 PM' to '2024-07-08 12:09:27'
                        return data ? formatDate(data) : "";
                    }
                },
                {
                    data: 'assessorFeeDetailId',
                    orderable: false,
                    render: function (data, type, row) {
                        return '<button class="btn btn-danger btn-sm delete-btn" data-id="' + data + '"> <i class="fa fa-trash"></i></button>';
                    }
                },
                {
                    data: 'assessorFeeDetailId',
                    orderable: false,
                    render: function (data, type, row) {
                        return '<button class="btn btn-primary btn-sm edit-btn" onclick="getAssessorFeeDetailById(' + data + ')" data-id="' + data + '" ><i class="fa fa-edit"></i></button>';
                    }
                }
            ],
            order: [[0, 'asc']]
        });
    }

    function formatDate(dateString) {
        let date = new Date(dateString);
        let formattedDate = date.getFullYear() + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + ('0' + date.getDate()).slice(-2);
        let formattedTime = ('0' + date.getHours()).slice(-2) + ':' + ('0' + date.getMinutes()).slice(-2) + ':' + ('0' + date.getSeconds()).slice(-2);
        return formattedDate + ' ' + formattedTime;
    }

    $('#searchBtn').click(function () {
        fetchAllAssessorFeeDetails();
    });

    $('#clearBtn').click(function () {
        $('#txtId').val('');
        $('#txtInspectionType').val('');
        $('#txtDayType').val('');

        fetchAllAssessorFeeDetails();
    });

    $('#addBtn').click(function () {
        $('#assessorFeeDetailId').val('');
        $('#permanentStaffFee').val('');
        $('#hybridStaffFee').val('');
        $('#fromTime').val('');
        $('#toTime').val('');
        $('#inspectionTypeId').val('');
        $('#dayTypeId').val('');
        $('#saveButton').show();
        $('#updateButton').hide();
        $('#addModalLabel').text("Add Assessor Fee");

        removeValidateClass();
    });

    $('#cancelButton').click(function () {
        $('#addModal').modal('hide');
        removeValidateClass();
    });

    // Validate form fields before save or update
    function validateForm() {
        let isValid = true;
        $('#addForm').find('input, textarea, select, time').each(function () {
            if ($(this).prop('required') && $(this).val() === '') {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        return isValid;
    }

    function removeValidateClass() {
        $('#addForm').find('input, textarea').each(function () {
            $(this).removeClass('is-invalid');
        });
    }

    function validateMaxLength(element, maxLength) {
        const errorElement = document.getElementById(element.id + "Error");
        if (element.value.length > maxLength) {
            errorElement.style.display = "block";
        } else {
            errorElement.style.display = "none";
        }
    }

</script>
</body>
</html>
