<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="reminder_letter" columnCount="2" pageWidth="595" pageHeight="842" columnWidth="277" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="5" whenResourceMissingType="Error">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="discharge_stamp_duty" class="java.lang.String" isForPrompting="false"/>
	<parameter name="return_letter_stamp_duty" class="java.lang.String" isForPrompting="false"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="V_VEHICLE_NO" class="java.lang.String"/>
	<field name="D_LATEST_CLM_LOSS_DATE" class="java.lang.String"/>
	<field name="V_CLIENT_NAME" class="java.lang.String"/>
	<field name="V_ADDRESS_LINE1" class="java.lang.String"/>
	<field name="V_ADDRESS_LINE2" class="java.lang.String"/>
	<field name="V_ADDRESS_LINE3" class="java.lang.String"/>
	<field name="N_REF_NO" class="java.lang.Integer"/>
	<field name="N_CLIM_NO" class="java.lang.Integer"/>
	<field name="D_ACCID_DATE" class="java.lang.String"/>
	<field name="POL_NUMBER" class="java.lang.String"/>
	<field name="V_REMIN_DESC" class="java.lang.String"/>
	<field name="N_REMIN_ID" class="java.lang.Integer"/>
	<variable name="vat" class="java.lang.Double">
		<variableExpression><![CDATA[($F{N_REMIN_ID}==10)? 20.00 : 0.00]]></variableExpression>
	</variable>
	<variable name="remin_des" class="java.lang.String">
		<variableExpression><![CDATA[($F{N_REMIN_ID}==12)? $F{V_REMIN_DESC}.concat(" "+$P{discharge_stamp_duty}) : ($F{N_REMIN_ID}==13)? $F{V_REMIN_DESC}.concat(" "+$P{return_letter_stamp_duty}):($F{N_REMIN_ID}==17)? $F{V_REMIN_DESC}.concat(" "):$F{V_REMIN_DESC}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="51" splitType="Stretch">
			<image>
				<reportElement x="182" y="1" width="158" height="50"/>
				<imageExpression><![CDATA["logo.gif"]]></imageExpression>
			</image>
		</band>
	</title>
	<pageHeader>
		<band height="302" splitType="Stretch">
			<line>
				<reportElement x="9" y="5" width="536" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="9" y="34" width="534" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="198" y="9" width="140" height="14"/>
				<textElement textAlignment="Center">
					<font fontName="Arial Black" size="8" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<text><![CDATA[AVIVA NDB INSURANCE PLC]]></text>
			</staticText>
			<staticText>
				<reportElement x="339" y="14" width="20" height="10"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[(PQ 18)]]></text>
			</staticText>
			<staticText>
				<reportElement x="133" y="23" width="380" height="11"/>
				<textElement>
					<font fontName="Arial Black" size="5"/>
				</textElement>
				<text><![CDATA[Head Office 75 Kumaran Rathnam Road Colombo 2 Sri Lanka  Tel (+94 11) 231 0310  Fax (+94 11) 231 0447  E-mail <EMAIL>  Web www.avivandb.com]]></text>
			</staticText>
			<staticText>
				<reportElement x="24" y="268" width="526" height="15"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[If we do receive a response from you within 14 day of this letter, we shall presume that you do not wish to pursue]]></text>
			</staticText>
			<staticText>
				<reportElement x="210" y="40" width="172" height="20"/>
				<textElement>
					<font fontName="Arial Black" size="12" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[WITHOUT PREJUDICE]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="60" width="24" height="20"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[To]]></text>
			</staticText>
			<staticText>
				<reportElement x="265" y="60" width="52" height="20"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[DATE]]></text>
			</staticText>
			<staticText>
				<reportElement x="265" y="80" width="70" height="20"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[POLICY NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="266" y="102" width="69" height="20"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[CLAIM NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="265" y="122" width="117" height="20"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[YOUR LETTER DATED]]></text>
			</staticText>
			<staticText>
				<reportElement x="266" y="142" width="89" height="20"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[YOUR REF NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="162" width="97" height="13"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<text><![CDATA[Dear Sir / Madam,]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="183" width="43" height="12"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<text><![CDATA[INSURED]]></text>
			</staticText>
			<staticText>
				<reportElement x="215" y="182" width="79" height="12"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<text><![CDATA[DATE OF LOSS]]></text>
			</staticText>
			<staticText>
				<reportElement x="392" y="182" width="69" height="12"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<text><![CDATA[VEHICLE NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="199" width="534" height="13"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[With refrence to the above claimDto, we would like to draw your attention to the following :-]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="223" width="19" height="20"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="6" y="225" width="16" height="15"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[A]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="246" width="19" height="20"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="268" width="19" height="20"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="6" y="248" width="16" height="15"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[B]]></text>
			</staticText>
			<staticText>
				<reportElement x="6" y="270" width="16" height="15"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[C]]></text>
			</staticText>
			<staticText>
				<reportElement x="25" y="224" width="223" height="17"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[Your letter reffered to above has been received]]></text>
			</staticText>
			<staticText>
				<reportElement x="25" y="248" width="137" height="15"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[Our advice will follow shortly]]></text>
			</staticText>
			<staticText>
				<reportElement x="25" y="283" width="257" height="15"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[this claimDto, and shall close the file treating it as 'No Claim'.]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy h.mm a">
				<reportElement x="391" y="60" width="148" height="12"/>
				<textElement>
					<font fontName="Courier New" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="461" y="181" width="89" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_VEHICLE_NO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="35" y="60" width="164" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_CLIENT_NAME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="35" y="74" width="162" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_ADDRESS_LINE1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="35" y="88" width="162" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_ADDRESS_LINE2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="35" y="102" width="162" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_ADDRESS_LINE3}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="392" y="82" width="147" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{POL_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="392" y="102" width="147" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{N_CLIM_NO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="392" y="143" width="147" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{N_REF_NO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="47" y="183" width="163" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_CLIENT_NAME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="294" y="182" width="100" height="12"/>
				<textElement>
					<font fontName="Courier New"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{D_ACCID_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="33" splitType="Stretch">
			<rectangle>
				<reportElement mode="Transparent" x="2" y="2" width="30" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="31" y="2" width="251" height="30"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="6" y="7" width="20" height="20"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_COUNT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="35" y="5" width="247" height="27"/>
				<textElement>
					<font fontName="Arial"/>
					<paragraph firstLineIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{remin_des}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="86" splitType="Stretch">
			<staticText>
				<reportElement x="4" y="3" width="229" height="10"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[* The final bills from repaires should be drawn in the name of the ]]></text>
			</staticText>
			<staticText>
				<reportElement x="346" y="3" width="175" height="10"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[and VAT should be shown separately therein]]></text>
			</staticText>
			<rectangle>
				<reportElement x="7" y="16" width="540" height="26"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="29" y="69" width="100" height="12"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<text><![CDATA[Authorized Signature]]></text>
			</staticText>
			<line>
				<reportElement x="10" y="67" width="136" height="1"/>
				<graphicElement>
					<pen lineStyle="Dotted"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="233" y="10" width="111" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Dotted"/>
				</graphicElement>
			</line>
			<textField pattern="###,###,##0.00">
				<reportElement x="247" y="2" width="39" height="10"/>
				<textElement>
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{vat}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="233" y="2" width="13" height="10"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Rs.]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
