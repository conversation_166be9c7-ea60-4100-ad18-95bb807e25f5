<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="audit_report" pageWidth="1296" pageHeight="822" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1296" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Empty" isIgnorePagination="true">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="44"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="N_CLIM_NO" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="V_POL_BRANCH" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="V_POL_TYPE" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="N_POL_NUMBER" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="V_VEHICLE_NO" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="V_MAIN_FUNC_NAME" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="V_SUB_FUNC_NAME" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="V_IS_EXCEED_SERVICE" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="V_INPUT_USER" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="N_FUNC_TARGET_TIME" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="N_ACTUAL_TIME_MINUTE" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<title>
		<band height="18">
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="1296" height="18" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="0" width="139" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Claim No]]></text>
			</staticText>
			<staticText>
				<reportElement x="139" y="0" width="121" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Branch Code]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="0" width="110" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Policy Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="0" width="78" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Policy No]]></text>
			</staticText>
			<staticText>
				<reportElement x="448" y="0" width="110" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Vehicle No]]></text>
			</staticText>
			<staticText>
				<reportElement x="558" y="0" width="141" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Main Func.]]></text>
			</staticText>
			<staticText>
				<reportElement x="699" y="0" width="203" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Sub Func.]]></text>
			</staticText>
			<staticText>
				<reportElement x="902" y="0" width="82" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[IS Exceeded]]></text>
			</staticText>
			<staticText>
				<reportElement x="984" y="0" width="100" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[User]]></text>
			</staticText>
			<staticText>
				<reportElement x="1084" y="0" width="100" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Target Time]]></text>
			</staticText>
			<staticText>
				<reportElement x="1184" y="0" width="112" height="18">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="10" bottomPadding="0" rightPadding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[Actual Time]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isBlankWhenNull="true">
				<reportElement x="139" y="0" width="121" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{V_POL_BRANCH}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="139" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{N_CLIM_NO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="260" y="0" width="110" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{V_POL_TYPE}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="370" y="0" width="78" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{N_POL_NUMBER}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="448" y="0" width="110" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{V_VEHICLE_NO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="558" y="0" width="141" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{V_MAIN_FUNC_NAME}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="699" y="0" width="203" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{V_SUB_FUNC_NAME}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="902" y="0" width="82" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{V_IS_EXCEED_SERVICE}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="984" y="0" width="100" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{V_INPUT_USER}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1084" y="0" width="100" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{N_FUNC_TARGET_TIME}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1184" y="0" width="112" height="20"/>
				<box leftPadding="10">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{N_ACTUAL_TIME_MINUTE}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
