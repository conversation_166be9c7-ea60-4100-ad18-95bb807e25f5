<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.5.0-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="supply_order" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="5" whenResourceMissingType="Empty" isIgnorePagination="true">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="client_name" class="java.lang.String" isForPrompting="false"/>
	<parameter name="address_line1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="address_line2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="address_line3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="phone_number" class="java.lang.String" isForPrompting="false"/>
	<parameter name="policy_number" class="java.lang.String" isForPrompting="false"/>
	<parameter name="claim_number" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="your_letter_date" class="java.lang.String" isForPrompting="false"/>
	<parameter name="ref_no" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="accid_date" class="java.lang.String" isForPrompting="false"/>
	<parameter name="vehicle_no" class="java.lang.String" isForPrompting="false"/>
	<parameter name="remin_des" class="java.util.List" isForPrompting="false"/>
	<parameter name="remin_id" class="java.lang.String" isForPrompting="false"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="V_SUPPLIER_NAME" class="java.lang.String"/>
	<field name="N_ITEM_QTY" class="java.lang.String"/>
	<field name="N_ORDER_SERIAL_NO" class="java.lang.String"/>
	<field name="V_VEHICLE_NO" class="java.lang.String"/>
	<field name="N_ITEM_PRICE" class="java.lang.String"/>
	<field name="D_YEARMAKE" class="java.lang.String"/>
	<field name="D_ACCIDENT" class="java.lang.String"/>
	<field name="V_INSURED" class="java.lang.String"/>
	<field name="V_DELIVERY_NAME" class="java.lang.String"/>
	<field name="V_DELIVERY_ADDRESS1" class="java.lang.String"/>
	<field name="V_DELIVERY_ADDRESS2" class="java.lang.String"/>
	<field name="V_DELIVERY_ADDRESS3" class="java.lang.String"/>
	<field name="V_DELIVERY_CONTACT_NO" class="java.lang.String"/>
	<field name="N_CLAIM_NO" class="java.lang.String"/>
	<field name="V_ITEM_DES" class="java.lang.String"/>
	<field name="D_CREATE_DATE_TIME" class="java.lang.String"/>
	<field name="V_CHASSIS_NO" class="java.lang.String"/>
	<field name="V_MODEL_DES" class="java.lang.String"/>
	<field name="V_STATUS" class="java.lang.String"/>
	<field name="V_REMARK" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="132" splitType="Stretch">
			<image>
				<reportElement x="480" y="7" width="74" height="81"/>
				<imageExpression><![CDATA["logo.gif"]]></imageExpression>
			</image>
			<image>
				<reportElement x="13" y="90" width="227" height="42"/>
				<imageExpression><![CDATA[($F{V_STATUS}.equalsIgnoreCase("R"))?"finalrejected.jpg":"blank.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="56" y="7" width="415" height="13"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[JANASHAKTHI GENERAL INSURANCE LIMITED]]></text>
			</staticText>
			<staticText>
				<reportElement x="56" y="20" width="415" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Company No: PB 5179]]></text>
			</staticText>
			<staticText>
				<reportElement x="56" y="32" width="415" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Corporate Office: 55/72, Vauxhall Lane, Colombo 02, Sri Lanka. P.O. Box 2202,]]></text>
			</staticText>
			<staticText>
				<reportElement x="56" y="56" width="415" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Insurance Office: No.46, Muttiah Road, Colombo 02, Sri Lanka. P.O. Box 2202 F: +94 11 2334864]]></text>
			</staticText>
			<staticText>
				<reportElement x="56" y="68" width="415" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Full Option Centre: No 24, Staples Street, Colombo 02, Sri Lanka. P.O. Box 2202 F: +94 11 2445735]]></text>
			</staticText>
			<staticText>
				<reportElement x="56" y="44" width="415" height="12"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[T: +94 11 2303300 F: +94 11 7309299 E: <EMAIL> W: www.janashakthi.com]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="126" splitType="Stretch">
			<staticText>
				<reportElement x="210" y="3" width="148" height="20"/>
				<textElement>
					<font fontName="Arial Black" size="12" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[SUPPLY ORDER]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="26" width="85" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Name of the Supplier :]]></text>
			</staticText>
			<staticText>
				<reportElement x="49" y="40" width="47" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Vehicle No :]]></text>
			</staticText>
			<staticText>
				<reportElement x="36" y="55" width="60" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Make & Model :]]></text>
			</staticText>
			<staticText>
				<reportElement x="56" y="83" width="40" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Claim No :]]></text>
			</staticText>
			<staticText>
				<reportElement x="27" y="97" width="70" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Date of Accident :]]></text>
			</staticText>
			<staticText>
				<reportElement x="324" y="26" width="35" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Insured :]]></text>
			</staticText>
			<staticText>
				<reportElement x="303" y="39" width="55" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Year of Make :]]></text>
			</staticText>
			<staticText>
				<reportElement x="269" y="53" width="92" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Supply order Serial No :]]></text>
			</staticText>
			<staticText>
				<reportElement x="19" y="111" width="305" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Please supply / repair the following items and submit the bill for settlement.]]></text>
			</staticText>
			<textField>
				<reportElement x="98" y="25" width="150" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_SUPPLIER_NAME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="98" y="82" width="150" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{N_CLAIM_NO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="362" y="25" width="150" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_INSURED}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="98" y="40" width="150" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_VEHICLE_NO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="98" y="96" width="150" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{D_ACCIDENT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="362" y="53" width="150" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{N_ORDER_SERIAL_NO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="362" y="39" width="150" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{D_YEARMAKE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="46" y="68" width="50" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Chassis No :]]></text>
			</staticText>
			<textField>
				<reportElement x="98" y="68" width="100" height="13"/>
				<textElement>
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_CHASSIS_NO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="286" y="67" width="75" height="12"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Create Date/Time  :]]></text>
			</staticText>
			<textField pattern="MM/dd/yyyy">
				<reportElement x="362" y="68" width="111" height="13"/>
				<textElement>
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{D_CREATE_DATE_TIME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="98" y="54" width="165" height="13"/>
				<textElement>
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_MODEL_DES}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="21">
			<staticText>
				<reportElement mode="Opaque" x="118" y="1" width="200" height="20" backcolor="#CCCCCC"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[ITEMS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="324" y="1" width="100" height="20" backcolor="#CCCCCC"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[QTY]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="434" y="1" width="100" height="20" backcolor="#CCCCCC"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[PRICE (Rs.)]]></text>
			</staticText>
			<rectangle>
				<reportElement mode="Opaque" x="89" y="1" width="25" height="20" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
		</band>
	</columnHeader>
	<detail>
		<band height="16" splitType="Stretch">
			<textField>
				<reportElement x="118" y="2" width="200" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_ITEM_DES}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="2" width="13" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_COUNT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="364" y="2" width="13" height="13"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{N_ITEM_QTY}]]></textFieldExpression>
			</textField>
			<textField pattern="###,###,##0.00">
				<reportElement x="463" y="2" width="50" height="13"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{N_ITEM_PRICE}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="280" splitType="Stretch">
			<staticText>
				<reportElement x="11" y="83" width="44" height="14"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Thank you]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="97" width="62" height="14"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Yours faithfully]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="145" width="90" height="14"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Authorized Signatory]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="159" width="60" height="14"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Motor Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="250" width="530" height="14"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[AIA General Insurance Lanka Limited (Co.No.PB 5178)]]></text>
			</staticText>
			<staticText>
				<reportElement x="264" y="87" width="63" height="16"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Delivery Name :]]></text>
			</staticText>
			<staticText>
				<reportElement x="255" y="106" width="72" height="15"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Delivery Address :]]></text>
			</staticText>
			<staticText>
				<reportElement x="258" y="137" width="69" height="15"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Delivery Contact :]]></text>
			</staticText>
			<textField>
				<reportElement x="327" y="87" width="100" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_DELIVERY_NAME}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="327" y="105" width="86" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_DELIVERY_ADDRESS1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="412" y="105" width="102" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_DELIVERY_ADDRESS2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="327" y="120" width="102" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_DELIVERY_ADDRESS3}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="327" y="137" width="102" height="13"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Courier New" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_DELIVERY_CONTACT_NO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="78" y="7" width="386" height="72"/>
				<textElement verticalAlignment="Top">
					<font fontName="Courier New" size="8"/>
					<paragraph firstLineIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{V_REMARK}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="12" y="7" width="60" height="11"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Special Note :]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
