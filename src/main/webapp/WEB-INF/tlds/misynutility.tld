<?xml version="1.0" encoding="UTF-8"?>
<taglib version="2.1" xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-jsptaglibrary_2_1.xsd">
    <tlib-version>1.0</tlib-version>
    <short-name>misynutility</short-name>
    <uri>http://www.mi-synergy.com/misyn/utility</uri>

    <function>
        <name>getCusormerDateFormat</name>
        <function-class>com.misyn.mcms.utility.Utility</function-class>
        <function-signature>java.lang.String getCustomDateFormat(java.lang.String,java.lang.String,java.lang.String)
        </function-signature>
    </function>

    <function>
        <name>getDateTimeValue</name>
        <function-class>com.misyn.mcms.utility.Utility</function-class>
        <function-signature>java.lang.String getDateTimeValue(java.lang.String)</function-signature>
    </function>

    <function>
        <name>getUserPermissionName</name>
        <function-class>com.misyn.mcms.claim.redis.RedisService</function-class>
        <function-signature>java.lang.String getFieldValue(java.lang.String,java.lang.String)</function-signature>
    </function>


</taglib>
