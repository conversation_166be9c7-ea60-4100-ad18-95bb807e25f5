<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee https://jakarta.ee/xml/ns/jakartaee/web-app_5_0.xsd"
         version="5.0">
    <jsp-config>
        <jsp-property-group>
            <url-pattern>*.jsp</url-pattern>
            <trim-directive-whitespaces>true</trim-directive-whitespaces>
        </jsp-property-group>
    </jsp-config>

    <listener>
        <description>HttpSessionListener</description>
        <listener-class>com.misyn.mcms.claim.listener.SessionListener</listener-class>
    </listener>
    <listener>
        <description>ServletContextListener</description>
        <listener-class>com.misyn.mcms.claim.listener.CmsContextListener</listener-class>
    </listener>
    <listener>
        <description>ServletContextListener</description>
        <listener-class>com.misyn.mcms.claim.listener.ReserveAdjustmentSchedulerListener</listener-class>
    </listener>
    <session-config>
        <session-timeout>
            30
        </session-timeout>
    </session-config>

</web-app>
