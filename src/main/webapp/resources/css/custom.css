/*global*/
.has-feedback small { color: #E92514; font-size: 90%; }
/*.has-feedback .form-control {border: 1px solid #e92514;}*/

.help-block { width: 100%; }
.has-feedback.has-error small { color: #E92514; font-size: 90%; }
.has-feedback.has-error { color: #E92514; }
.chosen-container-single { margin-bottom: 0px !important; }
.chosen-single div { padding: 7px 0 0 5px; }
.chosen-single span { font-size: 13px !important; }
.chosen-container-single .chosen-single { border-radius: 3px !important; }
body { font-size: 12px; font-weight: 500; }
form { margin: 0 }
.sideMenutitle { font-size: 15px; }
.dataLeftview .form-group { margin-bottom: 5px; }
.form-group { margin-bottom: 10px; }
table .form-group { margin-bottom: 0; }
.no-margin-firmgrp .form-group { margin-bottom: 0; }
.bg-dark { background-color: #E9ECEF !important; }
.fa { font-size: 16px }
.bg-info a { color: #FFFFFF; text-decoration: underline; }
.form-control { font-size: 12px; }
.form-control:focus { box-shadow: none; }
.text-mute, [readonly] { color: #999999 !important; }
.label_Value { font-weight: 700; margin-left: 5px; font-size: 13px; color: #007AFC; float: right; }
.input-view { display: block; width: 100%; padding: 5px 10px 5px 0; font-size: 13px; }
.input-group > .input-view { position: relative; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; width: 1%; margin-bottom: 0; }
/*:focus { outline: none; }*/
.pointer { cursor: pointer; }
.btn { padding: 3px 10px; box-shadow: none !important; line-height: inherit; border-radius: 3px; font-size: 14px; }
.btn-xs { padding: 4px 8px; font-size: 12px; width: auto !important; }
td .btn-multipl { width: auto !important; }
.input-group > .btn:not(:first-child) { border-top-left-radius: 0; border-bottom-left-radius: 0; }
.btn-group,
.btn-group > .btn { width: 100%; }
.btn-primary { background-color: #005AAA; border-color: #015AAA; }
.card .card-header a[data-toggle="collapse"][aria-expanded="false"]:after { content: ""; font-weight: bold; float: right; border-top: .3em solid; border-right: .3em solid transparent; border-left: .3em solid transparent; margin-top: 10px; position: absolute; right: 20px; }
.card .card-header a[data-toggle="collapse"][aria-expanded="false"]:after { content: ""; font-weight: bold; float: right; border-top: .3em solid; border-right: .3em solid transparent; border-left: .3em solid transparent; margin-top: 10px; position: absolute; right: 20px; }
.card .card-header a[data-toggle="collapse"][aria-expanded="true"]:after { content: ""; font-weight: bold; float: right; border-bottom: .3em solid; border-right: .3em solid transparent; border-left: .3em solid transparent; margin-top: 10px; position: absolute; right: 20px; }
/*.card a[data-toggle="collapse"][aria-expanded="false"] .card-header:after { content: ""; font-weight: bold; float: right; border-top: .3em solid; border-right: .3em solid transparent; border-left: .3em solid transparent; margin-top: 10px; position: absolute; right: 20px; top: 10px; }*/
/*.card a[data-toggle="collapse"][aria-expanded="true"] .card-header:after { content: ""; font-weight: bold; float: right; border-bottom: .3em solid; border-right: .3em solid transparent; border-left: .3em solid transparent; margin-top: 10px; position: absolute; right: 20px; top: 10px; }*/
.card { box-shadow: 0 0 5px 0 #CED4DA; }
.accordion .card-header { padding: 0; }
.card-header a { padding: 10px; text-align: left; width: 100%; }
.form-control-feedback { top: 8px; right: 10px; }
.error { border-color: #E98686 !important; background: #FFECED !important }
.error[type="radio"] ~ .custom-control-description { color: #E92514 !important; }
.done { border-color: #83B746 !important; background: rgba(131, 183, 70, 0.2) !important }
.done[type="radio"] ~ .custom-control-description { color: #83B746 !important; }
.tab-pane > fieldset.border { border-top: 0 !important; }
.viewVehicleModel .btn { border-bottom-left-radius: 0; border-top-left-radius: 0; }
a.bg-dark:focus, a.bg-dark:hover, button.bg-dark:focus, button.bg-dark:hover { background-color: #ffffffde  !important; }
.warning-mg { color: #E92514; }
.special-mg { color: #003366; }
.ui-dialog { padding: 0; }
.ui-dialog .ui-dialog-titlebar { border: 0; border-radius: 0; }
.ui-dialog .ui-dialog-buttonpane { padding: .3em; }
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset button { margin: 0; margin-right: 5px; display: inline-block; font-weight: 400; text-align: center; white-space: nowrap; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: 1px solid transparent; border-radius: .25rem; transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out; padding: 3px 10px; box-shadow: none !important; line-height: inherit; font-size: 14px; outline: 0; }
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset button:last-child { margin-right: 0; }
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset button:hover { background: #CCCCCC; }
.alert { z-index: 999999 !important; }
.alert-box { position: absolute; top: 10px; z-index: 999; right: 10px; min-width: 500px; padding: 10px 20px; border-radius: 5px; box-shadow: 0 0 20px 5px #0000004D; }
.log-left { width: 100px; height: 500px; float: left; border-radius: 50%; }
.log-left .name { visibility: hidden; transform: translate(28%, 3%); text-shadow: 0 0 5px #333333; }
.log-left .name::first-letter { visibility: visible; }
.log-right {
    margin-left: 20px;
    width: calc(100% - 100px);
}
.badge.border { border: 1px solid #8D8D8D !important; }
.badge { font-size: 12px; font-weight: 400; }
.badge .fa { font-size: 12px; font-weight: 400; }

/*.ui-resizable-handle { height: 45px; background: #DDDDDD; }*/
.ui-resizable-e { width: 20px; right: -20px; }
.ui-resizable-e:after { content: "\f0da"; width: 50px; height: 10px; display: inline-block; font: normal normal normal 14px/1 FontAwesome; text-rendering: auto; -webkit-font-smoothing: antialiased; font-size: 25px; padding: 10px 6px; }
.ui-resizable-w { width: 20px; left: -20px; }
.ui-resizable-w:after { content: "\f0d9"; width: 50px; height: 10px; display: inline-block; font: normal normal normal 14px/1 FontAwesome; text-rendering: auto; -webkit-font-smoothing: antialiased; font-size: 25px; padding: 10px 5px; }
/*Choosen*/
.chosen-disabled { opacity: 1 !important; }
.chosen-disabled > a { background-color: #E9ECEF !important; color: #999999 !important; }
/*Choosen*/

/* Rating Star Widgets Style */
.rating-stars ul { list-style-type: none; padding: 0; -moz-user-select: none; -webkit-user-select: none; }
.rating-stars ul > li.star { display: inline-block; }
/* Idle State of the stars */
.rating-stars ul > li.star > i.fa { font-size: 2.5em; /* Change the size of the stars */ color: #CCCCCC; /* Color on idle state */ }
/* Hover state of the stars */
.rating-stars ul > li.star.hover > i.fa { color: #FFCC36; }
/* Selected state of the stars */
.rating-stars ul > li.star.selected > i.fa { color: #FF912C; }
/* Hide the browser's default checkbox */
.check-container {
    position: relative;
    margin-left: 15px
}

.check-container2 {
    position: relative;
    margin-left: 35%
}
.check-container.first-container { margin-left: 10px }
.check-container .custom-control-description { margin-left: 10px; word-wrap: normal; }
.check-container input { position: absolute; opacity: 0; cursor: pointer; }
.checkmark { position: absolute; top: 5px; left: 0; height: 25px; width: 25px; background-color: #EEEEEE; border: 1px solid #AAAAAA; }
/* Create a custom checkbox */
.check-container:hover input ~ .checkmark { background-color: #CCCCCC; }
/* On mouse-over, add a grey background color */
.check-container input:checked ~ .checkmark { background-color: #2196F3; }
/* When the checkbox is checked, add a blue background */
.checkmark:after { content: ""; position: absolute; display: none; }
/* Create the checkmark/indicator (hidden when not checked) */
.check-container input:checked ~ .checkmark:after { display: block; }
/* Show the checkmark when checked */
.check-container .checkmark:after { left: 8px; top: 3px; width: 8px; height: 13px; border: solid white; border-width: 0 3px 3px 0; -webkit-transform: rotate(45deg); -ms-transform: rotate(45deg); transform: rotate(45deg); }
/* Style the checkmark/indicator */

.radiomark { position: absolute; top: 5px; left: 0; height: 25px; width: 25px; background-color: #EEEEEE; border-radius: 50%; border: 1px solid #AAAAAA; }
/* Create a custom radio button */
.check-container:hover input ~ .radiomark { background-color: #CCCCCC; }
/* On mouse-over, add a grey background color */
.check-container input:checked ~ .radiomark { background-color: #2196F3; }
/* When the radio button is checked, add a blue background */
.radiomark:after { content: ""; position: absolute; display: none; }
/* Create the indicator (the dot/circle - hidden when not checked) */
.check-container input:checked ~ .radiomark:after { display: block; }
/* Show the indicator (dot/circle) when checked */
.check-container .radiomark:after { top: 7px; left: 7px; width: 9px; height: 9px; border-radius: 50%; background: white; }
/* Style the indicator (dot/circle) */


.dynacheckheader {
    top: 5px;
    left: 0px;
    height: 15px;
    width: 15px;
}

.dynacheckheader:after {
    left: 4px !important;
    top: 1px !important;
    width: 6px !important;
    height: 9px !important;
}

.dynacheck {
    top: 5px;
    left: 0;
    height: 15px;
    width: 15px;
}

.dynaradio {
    top: 5px;
    left: 15%;
    height: 15px;
    width: 15px;
}

.dynacheck:after {
    left: 4px !important;
    top: 1px !important;
    width: 6px !important;
    height: 9px !important;
}

.dynaradio:after {
    left: 3px !important;
    top: 3px !important;
    width: 7px !important;
    height: 7px !important;
}

.sfont thead tr th {
    font-size: 0.6rem;
}

.sfont tbody tr td {
    font-size: 0.7rem;
}

.dynacheckcontainer {
    margin-left: 33%;
}

.dynaradiocontainer {
    margin-left: 20%;
}

/*custom switch*/
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 20px;
    margin: 0;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.customSlider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.customSlider:before {
    position: absolute;
    content: "";
    height: 12px;
    width: 24px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

#check:checked + .customSlider {
    background-color: #005AAA;
}

#check:focus + .customSlider {
    box-shadow: 0 0 1px #005AAA;
}

#check:checked + .customSlider:before {
    -webkit-transform: translateX(18px);
    -ms-transform: translateX(18px);
    transform: translateX(18px);
}
/*custom switch*/

/*table*/
#demo-dt-basic_wrapper { padding: 0; }
.table td, .table th { border-color: #FFFFFF; font-size: 13px; border-right: 1px solid #FFFFFF; vertical-align: middle; }
.table.table-sm td, .table.table-sm th { font-size: 12px; }
.table-xs td, .table-xs th { border-color: #FFFFFF; font-size: 11px; border-right: 1px solid #FFFFFF; padding: 3px 5px; font-weight: 500 }
.table-xs td .form-control { padding: 2px; height: auto !important; }
td { padding: 5px 10px; vertical-align: middle; }
td .btn { width: 100%; }
table { background: #FFFFFF; }

table thead th {
    padding: 8px;
    color: #FFFFFF;
    background: #025AAA;
}
tr.tbl_row_green { background: #EDFFD9; }
tr.tbl_row_yellow { background: #FFFDE7; }
tr.tbl_row_selected { transition: all 0.2s; background: transparent; cursor: pointer; }
.trClick { margin: 0 3px; transition: all 0.5s; }
.trClick:hover { background: #CCCCCC; cursor: pointer; border-radius: 2px; }
.trClickPos { background: #999999; color: #FFFFFF; border-radius: 2px; box-shadow: 0 0 5px 2px #CCCCCC; transition: all 0.5s; }
.vertical-bottom { vertical-align: bottom; }
.table-hover tbody tr:hover td { background-color: rgba(0, 0, 0, .05); }
.tire-con-table th { padding: 0 10px !important; }
.tire-con-table td { padding: 0 !important; }
.table-bg .badge-success { background-color: #28A745 !important; }
.table-bg .badge-warning { background-color: #FFC107 !important; }
.table-bg .badge-danger { background-color: #DC3545 !important; }
.table-bg .badge-info { background-color: #9C27B0 !important; }
.table-bg .badge-secondary { background-color: #6C757D !important; }
.table-bg .badge-primary { background-color: #007BFF !important; }
.table-bg .badge-dark { background-color: #343A40 !important; }
.table-striped tbody tr:nth-of-type(odd) { background-color: rgba(0, 0, 0, 0.1); }
/*header-bg*/

.bg-badge-succes,
.header-bg .badge-success,
.table-bg .table .badge-success { color: #28A745; background-color: rgba(46, 228, 88, 0.2) !important; }
.bg-badge-warning,
.header-bg .badge-warning,
.table-bg .table .badge-warning { color: #FF9800; background-color: rgba(255, 235, 59, 0.5) !important; }
.bg-badge-danger,
.header-bg .badge-danger,
.table-bg .table .badge-danger { color: #DC3545; background-color: rgba(220, 53, 69, .5) !important; }
.bg-badge-infoo,
.header-bg .badge-infoo,
.table-bg .table .badge-infoo { color: #17A2B8; background-color: rgba(23, 162, 184, .5) !important; }
.bg-badge-info,
.header-bg .badge-info,
.table-bg .table .badge-info { color: #9C27B0; background-color: rgba(156, 39, 176, 0.3) !important; }
.header-bg .badge-secondary,
.table-bg .table .badge-secondary { color: #6C757D; background-color: rgba(108, 117, 125, .5) !important; }
.bg-opacity .badge-primary,
.bg-badge-primary,
.table-bg .table .badge-primary { color: #007BFF; background-color: rgba(0, 123, 255, 0.5) !important; }
.header-bg .badge-dark,
.table-bg .table .badge-dark { color: #343A40; background-color: rgba(52, 58, 64, 0.5) !important; }
.active { background: rgba(255, 255, 255, 0.08)
}
.navLg .ui-accordion-content-active { display: block !important; }
.ui-accordion-content-active { display: none !important; }
/*wizard*/

.badge-priority {
    color: red !important;
    background-color: #ffbf8f !important;
}

::-moz-selection { background: #015AAA; color: #FFFFFF; text-shadow: none; }
::selection { background: #015AAA; color: #FFFFFF; text-shadow: none; }
.f1 { padding: 10px; background: #FFFFFF; -moz-border-radius: 4px; -webkit-border-radius: 4px; border-radius: 4px; }
.f1 h3 { margin-top: 0; margin-bottom: 5px; text-transform: uppercase; }
.f1-steps { overflow: hidden; position: relative; margin-top: 20px; text-align: center; }
.f1-progress { position: absolute; top: 24px; left: 0; width: 100%; height: 1px; background: #DDDDDD; }
.f1-progress-line { position: absolute; top: 0; left: 0; height: 1px; background: #015AAA; }
.f1-step { position: relative; float: left; width: 20%; padding: 0 5px; }
.f1-step-icon { display: inline-block; width: 40px; height: 40px; margin-top: 4px; background: #DDDDDD; font-size: 16px; color: #FFFFFF; line-height: 40px; -moz-border-radius: 50%; -webkit-border-radius: 50%; border-radius: 50%; text-align: center; }
.f1-step.activated .f1-step-icon { background: #FFFFFF; border: 1px solid #015AAA; color: #015AAA; line-height: 38px; }
.f1-step.active .f1-step-icon { width: 48px; height: 48px; margin-top: 0; background: #015AAA; font-size: 22px; line-height: 48px; }
.f1-step p { color: #CCCCCC; }
.f1-step.activated p { color: #015AAA; }
.f1-step.active p { color: #015AAA; }
.f1-buttons { text-align: right; }
.f1 .input-error { border-color: #015AAA; }
/*wizard*/
/*global*/

/*scroll */
.scroll-x { overflow-x: auto; }
.scroll { overflow: auto; }
.scroll::-webkit-scrollbar-track { background-color: rgba(245, 245, 245, 0.06); }
.scroll::-webkit-scrollbar { width: 6px; height: 6px; background-color: transparent; }
.scroll::-webkit-scrollbar-thumb { background-color: rgba(2, 140, 255, 0.59); border-radius: 5px; }
/*scroll*/
/*global*/

/*navbar  */
/*topmenu*/
#topmenu .navbar {box-shadow: 0 0 5px 0px #e3e3e3; height: 50px; }
.navbar-expand-sm .navbar-collapse { display: -webkit-box !important; display: -ms-flexbox !important; display: flex !important; -ms-flex-preferred-size: auto; flex-basis: auto; }
.userPic { background: url("../images/user.png"); background-size: cover; border-radius: 50%; height: 30px; width: 30px; padding: 20px; margin-right: 10px; cursor: pointer; }
.userPic:after { margin-left: 1.5em; display: none; }
/*sidenav*/
.nav-lg #Sidenav { width: 280px; }
.nav-lg #mainContent { margin-left: 280px; }
#Sidenav { height: calc(100% - 50px); height: -moz-calc(100% - 50px); height: -webkit-calc(100% - 50px); width: 40px; position: absolute; z-index: 1; top: 50px; left: 0; background-color: #172554; overflow-x: hidden; transition: 0.5s; padding-top: 20px; }
.Sidenav a { padding: 10px; text-decoration: none; font-size: 15px; color: rgba(255, 255, 255, 0.9); display: block; transition: 0.3s; font-weight: 400;}
.Sidenav div a:hover { padding-left: 35px; }
/*.Sidenav div a.active { border-left: 5px solid #f97316; }*/
.Sidenav div a { padding: 10px 10px 10px 30px; }
.Sidenav a:hover { color: #FFFF; }
.Sidenav p.ui-corner-top { background: #172554; }
.Sidenav .ui-accordion-content-active { background: #143183; }
#Sidenav .pinbtn { position: absolute; top: 20px; left: 0; font-size: 20px; }
.anglrRight { position: absolute; left: 250px; /*position: absolute; top: 20px; right: 8px;*/ font-size: 20px; z-index: 999; }
.left-menu { overflow: hidden; }
#buttonContainer.divShowHide { left: -165px !important; }
#buttonContainer .showHideBtn { position: absolute; right: -20px; background: #D0D4D6; }
.Sidenav.ui-accordion{ background: #172554; }
/*navbar  */

/*welcomePage*/
.welcomePage {
    color: #172554;
    position: absolute;
    /* top: 40%; */
    transform: translate(0, 25%);
    text-align: center;
    width: 100%; }
/*welcomePage*/
/*homepage*/
#menu { height: calc(100% - 40px); height: -moz-calc(100% - 40px); height: -webkit-calc(100% - 40px) }
#menu a { background: #FFFFFF; }
#imain_frm { height: calc(100vh - 78px); height: -moz-calc(100vh - 78px); height: -webkit-calc(100vh - 78px); width: 100%; min-width: 100%; *width: 100%; }
#mainContent { height: calc(100% - 50px); margin-left: 40px; height: -moz-calc(100% - 50px); height: -webkit-calc(100% - 50px); }
.right-content { width: 100%; }
#DIV_NOTIFICATION { position: absolute; display: none; background: #172554; width: 0; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
    top: 50px;
    padding: 8px;
    border-radius: 2px;}
/*homepage*/

#errorUpload { position: absolute; top: 0; right: 0; left: 0; }
.uploadfile-delet a { width: 100px; height: 100px; overflow: hidden; }
.uploadfile-delet { float: left; position: relative; margin-bottom: 25px; }
.uploadfile-delet.imgupload { margin: 0 10px 45px; }
.uploadfile-delet a { float: left; display: block; }
.uploadfile-delet label.custom-control { position: absolute; bottom: 0; margin-left: 40px }
.uploadfile-delet.imgupload label.custom-control { bottom: -30px; right: 42%; }
.modal { display: none !important; }
.modal.show { display: block !important; }
.bootstrap-datetimepicker-widget table thead th { background: inherit; color: inherit; }
.bootbox .bootbox-close-button { display: none; }
.stamp-container { position: absolute; top: 10px; left: 30px; right: 30px; z-index: 999; }
.stamp-container-vip { position: absolute; top: 10px; right: 0px; z-index: 999; }


.stamp-container2 {
    position: absolute;
    top: 48px;
    left: 30px;
    right: 30px;
    z-index: 999;
}

.stamp-container3 {
    position: absolute;
    top: 48px;
    left: 250px;
    /* right: 31px; */
    z-index: 999;
}

.viewDocumentContainer {
    width: 42%;
    height: 90vh;
    position: fixed;
    top: 0;
    right: -50%;
    bottom: 0;
    box-shadow: 0 0 10px 5px #0000001F;
    display: none;
    z-index: 9999
}

.viewDocumentContainera {
    position: fixed;
    top: 0;
    right: -100%;
    bottom: 0;
    box-shadow: 0 0 10px 5px #0000001F;
}
.viewDocumentContainer.clayfy-box {
    position: absolute;
}
.viewDocumentContainer #viewDocument { width: 100%; height: 84vh; }

.viewDocumentContainera.right {
    right: 0;
    left: auto;
    display: block;
}
.viewDocumentContainer.right { right: 0; left: auto; display: block; }
.viewDocumentContainer.left { left: 0; right: auto; display: block; }
.note-editor { position: absolute; width: 30%; right: 0; left: auto; top: 0; }
.note-editor .btn-group { width: auto; }
.note-editor .table td, .note-editor .table th { border-color: #333333; border-right-color: #333333; }
#preview {
    position: absolute;
    border: 1px solid #CCCCCC;
    background: #333333;
    padding: 5px;
    display: none;
    color: #FFFFFF;
}
/*.viewDocumentContainer.active {right: 0; }*/
/*tharuka*/

.lineheight40px {
    line-height: 40px;
}
.lineheight32px {
    line-height: 32px;
}
.calcsheetjsp table td {
    vertical-align: top;
}
.supplierorderpage table td {
    vertical-align: top;
}
/*tharuka*/

/*/akila nilakshi/*/
.align-checkbox-center {
    left: -40px !important;
}

.align-css {
    margin-bottom: 15px;
}
/*@media*/
@media (max-width: 991px) {

    .hide-sm { display: none; }
    .right-content { width: 100%; margin-left: 30px !important; }
}
@media (max-width: 767px) {

}
/***** Media queries *****/

@media (min-width: 992px) and (max-width: 1199px) {
}
@media (min-width: 768px) and (max-width: 991px) {
}
/*@media (min-width: 1366px) {*/
/*body { font-size: 14px; }*/
/*}*/
@media (max-width: 1199px) {
    div[style="height: calc(100vh - 200px);"] { height: auto !important; }
    div[style="height: calc(100vh - 350px);"] { height: auto !important; }
    div[style="height: calc(100vh - 160px);"] { height: auto !important; }
    iframe[style="height: calc(100vh - 310px);"] { height: 100vh !important; }
    body { font-size: 12px; }
}
@media (max-width: 767px) {

    .navbar { padding-top: 0; }
    .navbar.navbar-no-bg { background: #333333; background: rgba(51, 51, 51, 0.9); }
    .navbar-brand { height: 60px; margin-left: 15px; }
    .navbar-collapse { border: 0; }
    .navbar-toggle { margin-top: 12px; }
    .top-content { padding: 40px 0 110px 0; }
    #Sidenav { height: 100vh; }
}
@media (max-width: 575px) {
    body { font-size: 12px; }
    .fa { font-size: 12px; }
    .btn { font-size: 12px }
    td { padding: 5px; }
    .anglrRight { left: 10px; }
    .form-group { margin-bottom: 0; }
    .card-body {
        padding: 0.25rem;
    }
}
@media (max-width: 415px) {
    .f1 { padding-bottom: 20px; }
    .f1-buttons button { margin-bottom: 5px; }
}
@media screen and (max-height: 450px) {
    #Sidenav { padding-top: 15px; }
    #Sidenav a { font-size: 18px; }
}
/*@media*/
@media (max-width: 575px) {
    body { font-size: 12px; }
    .btn { font-size: 12px }
    td { padding: 5px; }
    .anglrRight { left: 10px; }
    .form-group { margin-bottom: 0; }
}
@media screen and (max-height: 450px) {
    #Sidenav { padding-top: 15px; }
    #Sidenav a { font-size: 18px; }
}
.maphilighted, .map {
    background-repeat: no-repeat !important;
    background-size: cover;
}
/*image magnified selec style*/

.img-mag-selected {
    border: 3px solid #007BFF
}
.magnify-thumb {
    margin: 2px;
}
.image-set figure {
    display: inline-block;
    text-align: center;
}
figcaption {
    text-align: center;
}
.uploadfile-delet h6 {
    font-size: 12px;
}
.doc-mag-selected {
    border: 3px solid #FDC007;
}
.imagealignment{
    top: 50px!important;
    left: 100px!important;
}
.imagealignment1{
    top: -41px !important;
}

.blueheader {
    padding: 10px;
    color: #FFFFFF;
    background: #025AAA;
}

.padding5px {
    padding: 5px !important;
}

.tablestyle .table-bordered thead td, .table-bordered thead th {
    border-bottom-width: 2px !important;
}

.tablestyle .table thead th {
    vertical-align: bottom !important;
    border-top: 1px solid #dee2e6 !important;
    border-bottom: 2px solid #dee2e6 !important;
}

.colorwhite {
    background: #ffffff !important;
    color: black !important;
}

.navi-menu {
    /*margin-left: 38%;*/
    color: #0056b3;
    font-weight: 600;
    position: absolute;
    margin-left: 122px;
}

.modal-xl {
    width: 80% !important;
}

@media only screen
and (min-device-width: 768px)
and (max-device-width: 1024px)
and (orientation: landscape) {
    .navi-menu {
        margin-left: -10%;
    }

}

@media only screen
and (min-device-width: 768px)
and (max-device-width: 1024px)
and (orientation: portrait) {
    .navi-menu {
        margin-left: 10%;
    }

}



.covers-container-card, .product-container-card{
    height: calc(100vh - 128px);
    overflow-y: auto;
    overflow-x: hidden !important;
    max-width: 100% !important;
}
.prevent-select{
    padding-left: 14px;
    padding-right: 14px;
    padding-top: 12px;
}
.left-side-draggable-table thead{
    border: 1px solid #025aaa;
}
.left-side-draggable-table tbody{
    border: 1px solid #dbdbdb;
}
.left-side-draggable-table tbody tr{
    border: 1px solid #dbdbdb;
}
.left-side-draggable-table{
    box-shadow: 0 0 5px 0 #d9d9d9;
}