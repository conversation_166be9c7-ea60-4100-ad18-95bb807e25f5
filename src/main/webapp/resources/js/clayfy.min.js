!function () {
    function e(e, i) {
        function o(e) {
            return e.el[0].scrollHeight - e.el.scrollTop() === e.innerHeight
        }

        function r(e) {
            return e.el[0].scrollWidth - e.el.scrollLeft() === e.innerWidth
        }

        this.el = e instanceof $ ? e : $(e), this.draggableBox, this.x, this.y, this.dX, this.dY, this.diffDX, this.diffDY, this.history = {
            dX: [0, 0, 0],
            dY: [0, 0, 0],
            diffDX: 0,
            diffDY: 0
        }, this.actualPos, this.originalPos, this.initPos = {
            y: 0,
            x: 0,
            scrollTop: null,
            scrollLeft: null
        }, this.bounderies = {}, this.droppedTarget, this.scrollable = [], this.container = {}, this.tempContainer = $("<div>", {style: "position: absolute; top:0; left:0"}), this.droppable = {
            dragElement: [],
            dropArea: []
        }, this.status = "ready", this.settings = $.extend(!0, {}, $.clayfy.settings, i);
        var a, n, s = this, l = !1, d = !0, g = $("<div>", {style: "height:100%;width:100%;position:fixed;top:0;left:0"});
        this.contentGhost;
        var p = function () {
            s.settings.ghost ? (!0 === s.settings.ghost ? (s.draggableBox = s.el.clone(), s.draggableBox.addClass("clayfy-ghost-opacity")) : (s.draggableBox = $("<div>", {margin: s.el.css("margin")}), s.contentGhost = $('<div class="clayfy-ghost-content" style="position:absolute"></div>'), s.draggableBox.append(s.contentGhost)), s.draggableBox.css({
                position: "absolute",
                width: "100%",
                height: "100%"
            }).addClass("clayfy-ghost")) : s.draggableBox = s.el
        }, f = function (e) {
            var t = s.getPosition(s.el), i = s.settings.overflow ? s.tempContainer : s.el.parent(), o = s.el.offset(), r = s.initPos.parent ? s.initPos.parent.scrollTop() : 0,
                a = s.initPos.parent ? s.initPos.parent.scrollLeft() : 0, n = {width: s.el.width(), height: s.el.height(), top: t.y, left: t.x};
            if (!0 !== s.settings.ghost && (n = {
                    top: e.pageY - o.top + t.y + 5 - r,
                    left: e.pageX - o.left + t.x + 5 - a,
                    width: "auto",
                    height: "auto"
                }), s.settings.overflow && (n.top = o.top - r, n.left = o.left - a, !0 !== s.settings.ghost && (n.top = e.pageY - r + 5, n.left = e.pageX - a + 5)), s.draggableBox.css(n), s.draggableBox.appendTo(i), s.contentGhost) {
                s.contentGhost.html("");
                var l;
                switch (typeof s.settings.ghost) {
                    case"string":
                        l = s.settings.ghost;
                        break;
                    case"function":
                        l = s.settings.ghost()
                }
                if (s.contentGhost.append(l), s.container.offset) {
                    var d = s.container.offset.innerBottom, g = s.container.offset.innerRight, p = s.draggableBox.offset(), f = p.top + s.contentGhost.outerHeight() - d,
                        c = p.left + s.contentGhost.outerWidth() - g;
                    f > 0 && s.draggableBox.css({top: e.pageY - o.top + t.y + 5 - f}), c > 0 && s.draggableBox.css({left: e.pageX - o.left + t.x + 5 - c})
                }
            }
        }, c = function (e) {
            if ("canceling" !== s.status && s.settings.move) {
                var t = s.getPosition(), i = s.draggableBox.offset();
                s.contentGhost ? s.contentGhost : s.draggableBox;
                if (s.container.offset) {
                    var o = s.container.offset.innerBottom, r = s.container.offset.innerRight;
                    i.top >= o - s.el.outerHeight() && (t.y += o - s.el.outerHeight() - i.top), i.left >= r - s.el.outerWidth() && (t.x += r - s.el.outerWidth() - i.left)
                }
                if (s.el.css({top: t.y, left: t.x}), "clayfy-dropinside" === e.type) {
                    var o = e.area.offset.innerBottom, r = e.area.offset.innerRight;
                    i.top >= o - s.el.outerHeight() && (t.y += o - s.el.outerHeight() - i.top), i.left >= r - s.el.outerWidth() && (t.x += r - s.el.outerWidth() - i.left), s.el.css({
                        top: t.y,
                        left: t.x
                    })
                }
            }
            s.draggableBox.detach()
        }, h = function () {
            var e = s.settings.container;
            e instanceof t ? s.container = e : s.settings.container && (s.container = new t(s.el, e))
        }, y = function (e) {
            27 === e.keyCode && s.cancel(e)
        };
        this.cancel = function (e) {
            s.status = "canceling", s.draggableBox.animate({top: s.initPos.y, left: s.initPos.x}, 100, function () {
                s.draggableBox.trigger("mouseup"), s.status = "ready"
            }), null !== s.initPos.scrollTop && s.initPos.parent.animate({scrollTop: s.initPos.scrollTop}, 100), null !== s.initPos.scrollLeft && s.initPos.parent.animate({scrollLeft: s.initPos.scrollLeft}, 100)
        }, s.scrollables = [];
        var u, b, v, m, x = function () {
            $(s.settings.scrollable).each(function () {
                var e = $(this);
                if (!e.length) return !0;
                var t = w(e);
                if (!t.x && !t.y) return !0;
                var i = e[0].getBoundingClientRect(), o = z(e), r = parseInt(e.css("border-top-width")), a = parseInt(e.css("border-left-width")), n = {
                    el: e,
                    top: i.top + r,
                    bottom: i.top + o.innerHeight + r,
                    left: i.left + a,
                    right: i.left + o.innerWidth + a,
                    innerHeight: o.innerHeight,
                    innerWidth: o.innerWidth,
                    interval: {top: !1, bottom: !1, left: !1, right: !1},
                    isParent: !s.settings.overflow && s.el.offsetParent().is(e),
                    isBody: e.is("body")
                };
                if (n.isBody) {
                    var l = $(window);
                    n.top = 0, n.left = 0, n.bottom = l.height(), n.right = l.width(), n.innerHeight = l.height(), n.innerWidth = l.width()
                }
                s.scrollables.push(n)
            })
        }, w = function (e) {
            var t = !1, i = !1, o = $(window), r = $("body");
            return e.is("body") ? (r.height() > o.height() && (i = !0), r.width() > o.width() && (t = !0)) : (e[0].scrollHeight > e.height() && (i = !0), e[0].scrollWidth > e.width() && (t = !0)), {
                x: t,
                y: i
            }
        }, z = function (e) {
            var t, i, o, r, a, n, s, l = e instanceof $ ? e : $(e);
            return l.length ? (t = l[0].style.position, "static" === l.css("position") && l.css({position: "relative"}), o = $("<div>", {style: "position:absolute;top:0;left:0;bottom:0;right:0"}), i = $("<div>", {style: "position:absolute;top:0;left:0;width:100%;height:100%"}), o.append(i), l.append(o), r = i.width(), a = i.height(), n = parseInt(i.css("border-top-width")), s = parseInt(i.css("border-left-width")), o.remove(), l[0].style.position = t, {
                innerWidth: r,
                innerHeight: a,
                innerOffset: {top: n, left: s, bottom: a + n, right: r + s}
            }) : {width: 0, height: 0}
        }, B = function (e) {
            var t = s.contentGhost ? s.contentGhost : s.draggableBox, i = s.draggableBox[0].getBoundingClientRect(), a = t.offset(),
                n = {top: i.top, bottom: i.top + t.outerHeight(), left: i.left, right: i.left + t.outerWidth(), x: 0, y: 0};
            n.x = (n.right - n.left) / 2 + n.left, n.y = (n.bottom - n.top) / 2 + n.top, s.history.diffDY > 0 && u && (u = !1, s.y = Math.min(a.top + t.outerHeight(), e.pageY)), s.history.diffDY < 0 && b && (b = !1, s.y = Math.max(a.top, e.pageY)), s.history.diffDX > 0 && m && (m = !1, s.x = Math.min(a.left + t.outerWidth(), e.pageX)), s.history.diffDX < 0 && v && (v = !1, s.x = Math.max(a.left, e.pageX));
            for (var l = 0, d = s.scrollables.length; l < d; l++) {
                var g = s.scrollables[l], p = 0,
                    f = {top: n.top - g.top, bottom: g.bottom - n.bottom, left: n.left - g.left, right: g.right - n.right, x: n.x < g.right && n.x > g.left, y: n.y < g.bottom && n.y > g.top};
                if (!o(g) && f.bottom < 6 && (f.bottom > -6 || g.isBody) && f.x ? (P(e, g, "bottom"), D(g, "top"), p++) : (D(g, "bottom"), g.el.scrollTop() && f.top < 6 && (f.top > -6 || g.isBody) && f.x ? (P(e, g, "top"), p++) : D(g, "top")), g.el.scrollLeft() && f.left < 6 && (f.left > -6 || g.isBody) && f.y ? (P(e, g, "left"), D(g, "right"), p++) : (D(g, "left"), !r(g) && f.right < 6 && (f.right > -6 || g.isBody) && f.y ? (P(e, g, "right"), p++) : D(g, "right")), p) break
            }
        }, P = function (e, t, i) {
            t.interval[i] && clearInterval(t.interval[i]);
            var o = function (e) {
            };
            switch (i) {
                case"bottom":
                    o = function (i) {
                        i = i || 10, t.el.scrollTop(t.el.scrollTop() + i), t.isParent && (s.x = e.pageX, s.setBounderies(), s.updateDropArea(), u = !0)
                    };
                    break;
                case"top":
                    o = function (i) {
                        i = i || 10, t.el.scrollTop(t.el.scrollTop() - i), t.isParent && (s.x = e.pageX, s.setBounderies(), s.updateDropArea(), b = !0)
                    };
                    break;
                case"left":
                    o = function (i) {
                        i = i || 10, t.el.scrollLeft(t.el.scrollLeft() - i), t.isParent && (s.y = e.pageY, s.setBounderies(), s.updateDropArea(), v = !0)
                    };
                    break;
                case"right":
                    o = function (i) {
                        i = i || 10, t.el.scrollLeft(t.el.scrollLeft() + i), t.isParent && (s.y = e.pageY, s.setBounderies(), s.updateDropArea(), m = !0)
                    }
            }
            o(3), t.isParent || (t.interval[i] = setInterval(o, 50))
        }, D = function (e, t) {
            if (t) e.interval[t] && (clearInterval(e.interval[t]), e.interval[t] = !1); else for (var i in s.scrollables) {
                var o = s.scrollables[i].interval;
                for (var r in o) o[r] && (clearInterval(o[r]), o[r] = !1)
            }
        };
        this.appendTo = function (e, t) {
            if (t = t || s.el, (e = e instanceof $ ? e : $(e)).length) {
                var i = t.offset(), o = e.offset(),
                    r = {top: i.top - o.top - parseInt(e.css("border-top-width")) + e.scrollTop(), left: i.left - o.left - parseInt(e.css("border-left-width")) + e.scrollLeft()};
                "static" === e.css("position") && e.css("position", "relative"), t.appendTo(e).css(r)
            }
        };
        var S = function () {
            s.el.on("clayfy-dragstart", s.updateDragElement), s.el.on("clayfy-dragstart", s.updateDropArea), s.el.on("clayfy-drag", H), s.el.on("clayfy-drop", W), s.settings.ghost && (s.el.on("clayfy-dropinside", c), s.el.on("clayfy-dropoutside", c)), s.el.on("clayfy-dragstart", function () {
                X() || (s.el.removeClass("clayfy-dropinside"), s.draggableBox.removeClass("clayfy-dropinside"))
            }), s.el.on("clayfy-dragenter", function (e) {
                s.el.addClass("clayfy-dragenter"), s.draggableBox.addClass("clayfy-dragenter"), e.droparea.addClass("clayfy-dragenter"), s.el[0].id && e.droparea.addClass("clayfy-dragenter-" + s.el[0].id)
            }), s.el.on("clayfy-dragleave", function (e) {
                s.el.removeClass("clayfy-dropinside"), s.draggableBox.removeClass("clayfy-dropinside"), e.droparea.removeClass("clayfy-dropinside"), s.el[0].id && e.droparea.removeClass("clayfy-dropinside-" + s.el[0].id)
            }), s.el.on("clayfy-dragleave clayfy-drop", function (e) {
                s.el.removeClass("clayfy-dragenter"), s.draggableBox.removeClass("clayfy-dragenter"), $(".clayfy-dragenter").removeClass("clayfy-dragenter"), s.el[0].id && $(".clayfy-dragenter-" + s.el[0].id).removeClass("clayfy-dragenter-" + s.el[0].id)
            }), s.el.on("clayfy-dropinside", function (e) {
                s.el.addClass("clayfy-dropinside"), s.draggableBox.addClass("clayfy-dropinside"), e.droparea.addClass("clayfy-dropinside"), s.el[0].id && e.droparea.addClass("clayfy-dropinside-" + s.el[0].id), s.settings.migrate && s.appendTo(e.droparea)
            })
        };
        this.updateDropArea = function (e) {
            s.droppable.dropArea = [];
            var t = s.settings.droppable instanceof $ ? s.settings.droppable : $(s.settings.droppable);
            s.addDroppable(t)
        }, this.updateDragElement = function () {
            s.droppable.dragElement = [], s.droppable.dragElement = {
                originalPos: s.getPosition(),
                id: s.el[0].id,
                originalDropArea: null,
                width: s.draggableBox.width(),
                height: s.draggableBox.height(),
                x: 0,
                y: 0
            };
            var e = s.droppable.dragElement;
            e.setCenter = function () {
                var t = s.draggableBox.offset();
                e.x = t.left + e.width / 2, e.y = t.top + e.height / 2
            }, e.setCenter(), e.originalDropArea = X()
        }, this.resetDroppable = function (e) {
            e && (s.settings.droppable = e), s.updateDragElement(), s.updateDropArea()
        }, this.addDroppable = function (e) {
            (e instanceof $ ? e : $(e)).each(function () {
                var e = $(this), t = e.offset(), i = e.outerHeight(), o = e.outerWidth(), r = parseInt(e.css("border-top-width")), a = parseInt(e.css("border-left-width")), n = $.clayfy.getInner(e);
                s.droppable.dropArea.push({
                    el: e,
                    id: this.id,
                    left: t.left,
                    top: t.top,
                    width: o,
                    height: i,
                    innerWidth: n.innerWidth,
                    innerHeight: n.innerHeight,
                    offset: {innerTop: t.top + r, innerLeft: t.left + a, innerBottom: n.innerHeight + t.top + r, innerRight: n.innerWidth + t.left + a},
                    right: t.left + o,
                    bottom: t.top + i,
                    active: !1,
                    triggered: !1
                })
            })
        };
        var H = function (e) {
            var t = s.droppable.dragElement;
            t.setCenter();
            for (var i = 0, o = s.droppable.dropArea.length; i < o; i++) {
                var r = s.droppable.dropArea[i];
                r && (t.x > r.left && t.x < r.right && t.y > r.top && t.y < r.bottom ? r.active = !0 : r.active = !1, !r.triggered && r.active ? (r.triggered = !0, s.el.trigger($.Event("clayfy-dragenter", {
                    target: r.el[0],
                    droparea: r.el
                }))) : r.triggered && !r.active && (r.triggered = !1, s.el.trigger($.Event("clayfy-dragleave", {target: r.el[0], droparea: r.el, area: r}))))
            }
        }, X = function () {
            var e = s.droppable.dragElement;
            e.setCenter();
            for (var t = !1, i = 0, o = s.droppable.dropArea.length; i < o; i++) {
                var r = s.droppable.dropArea[i];
                r && (e.x > r.left && e.x < r.right && e.y > r.top && e.y < r.bottom && (r.active = !0, r.triggered = !0, t = r))
            }
            return t
        }, Y = function (e) {
            var t = s.contentGhost ? s.contentGhost : s.draggableBox, i = {}, o = s.el.offset(),
                r = {top: o.top, left: o.left, right: s.droppable.dragElement.width + o.left, bottom: s.droppable.dragElement.height + o.top};
            t.outerWidth() < e.innerWidth && (r.right > e.offset.innerRight && (i.left = parseInt(t.css("left")) + e.offset.innerRight - r.right), r.left < e.offset.innerLeft && (i.left = parseInt(t.css("left")) + e.offset.innerLeft - r.left)), t.outerHeight() < e.innerHeight && (r.bottom > e.offset.innerBottom && (i.top = parseInt(t.css("top")) + e.offset.innerBottom - r.bottom), r.top < e.offset.innerTop && (i.top = parseInt(s.el.css("top")) + e.offset.innerTop - r.top)), s.el.css(i)
        }, A = function () {
            var e = s.droppable.dragElement.originalDropArea;
            e && s.el.trigger($.Event("clayfy-dropinside", {
                target: e.el[0],
                droparea: e.el
            })), s.settings.overflow && !s.settings.ghost ? s.el.css({
                left: s.initPos.x - s.initPos.parent.offset().left - parseInt(s.initPos.parent.css("border-left-width")) + s.initPos.scrollLeft,
                top: s.initPos.y - s.initPos.parent.offset().top - parseInt(s.initPos.parent.css("border-top-width")) + s.initPos.scrollTop
            }) : s.el.css({left: s.initPos.x, top: s.initPos.y})
        }, W = function () {
            for (var e, t = 0, i = s.droppable.dropArea.length; t < i; t++) s.droppable.dropArea[t].active && (e = s.droppable.dropArea[t]);
            if ("canceling" === s.status) {
                if (e && (e.active = !1, e.triggered = !1, s.el.trigger($.Event("clayfy-dragleave", {target: e.el[0], droparea: e.el}))), !(e = s.droppable.dragElement.originalDropArea)) return;
                e.active = !0, e.triggered = !0
            }
            e ? (s.el.trigger($.Event("clayfy-dropinside", {
                target: e.el[0],
                droparea: e.el,
                area: e
            })), s.settings.fit && Y(e)) : (s.el.trigger("clayfy-dropoutside"), s.settings.dropoutside || A(), s.settings.dropoutside && s.settings.migrate && s.settings.overflow && s.appendTo(s.tempContainer)), e && (s.droppedTarget = e.el[0])
        }, T = function (e) {
            var t = e.pageX - s.el.offset().left, i = e.pageY - s.el.offset().top, o = $("<div>", {style: "position:absolute;left:0;top:0;width:100%;height:100%"});
            s.el.append(o);
            var r = o.width(), a = o.height();
            return o.remove(), !(t > r) && !(i > a)
        }, C = function (e) {
            n.is(e.target) || s.el.has(e.target).length && !s.settings.propagate || (isTouchDevice() || void 0 === e.which || 1 === e.which) && (e.preventDefault(), T(e) && (s.settings.coverScreen && E(), l = !0, document.body.style.cursor = a, s.settings.dragstart.call(s, e), s.el.trigger("clayfy-dragstart"), $(document).on("mousemove touchmove", I).on("mouseup touchend", R)))
        }, R = function (e) {
            if (l) {
                e.preventDefault(), l = !1, d = !0, document.body.style.cursor = "", s.settings.overflow && (s.appendTo(s.initPos.parent, s.draggableBox), s.appendTo(s.initPos.parent)), s.settings.coverScreen && k(), s.settings.drop.call(s);
                var t = $.Event("clayfy-drop", {pageX: e.pageX, pageY: e.pageY, screenX: e.screenX, screenY: e.screenY});
                s.el.trigger(t), $(document).off("mousemove touchmove", I).off("mouseup touchend", R)
            }
        }, I = function (e) {
            if (l) {
                if (e.preventDefault(), e.originalEvent.touches && 1 == e.originalEvent.touches.length) var e = e.originalEvent.touches[0] || e.originalEvent.changedTouches[0];
                if (d) {
                    d = !1;
                    var t = s.el.parent();
                    s.initPos.parent = t, s.settings.overflow && s.appendTo(s.tempContainer, s.draggableBox), s.settings.ghost && f(e), s.x = e.pageX, s.y = e.pageY, s.setBounderies();
                    var i = s.getPosition(s.el);
                    s.initPos = {x: i.x, y: i.y, scrollLeft: t.scrollLeft(), scrollTop: t.scrollTop(), parent: t}, s.history = {dX: [0, 0, 0], dY: [0, 0, 0], diffDX: 0, diffDY: 0}
                }
                s.dX = e.pageX - s.x, s.dY = e.pageY - s.y, $.clayfy.dX = s.dX, $.clayfy.dY = s.dY, s.history.diffDX = (s.history.dX[0] + s.history.dX[1] - (s.history.dX[2] + s.dX)) / 2, s.history.diffDY = (s.history.dY[0] + s.history.dY[1] - (s.history.dY[2] + s.dY)) / 2, s.history.dX = [s.history.dX[1], s.history.dX[2], s.dX], s.history.dY = [s.history.dY[1], s.history.dY[2], s.dY], s.fixDeltasWithBounderies(), (s.settings.move || s.settings.ghost) && s.move(), s.settings.drag.call(s, e);
                var o = $.Event("clayfy-drag", {
                    shiftKey: e.shiftKey,
                    pageX: e.pageX,
                    pageY: e.pageY,
                    clientX: e.clientX,
                    clientY: e.clientY,
                    screenX: e.screenX,
                    screenY: e.screenY,
                    altKey: e.altKey
                });
                s.el.trigger(o)
            }
        }, E = function () {
            $("body").append(g)
        }, k = function () {
            g.detach()
        };
        this.getContainerBounderies = function () {
            if (!s.container.type) return !1;
            var e, t, i = s.draggableBox.offset(), o = {};
            return s.container.update(), e = s.contentGhost ? s.contentGhost.outerWidth() : s.draggableBox.outerWidth(), t = s.contentGhost ? s.contentGhost.outerHeight() : s.draggableBox.outerHeight(), o = {
                top: i.top - s.container.offset.innerTop,
                right: s.container.offset.innerRight - i.left - e,
                bottom: s.container.offset.innerBottom - i.top - t,
                left: i.left - s.container.offset.innerLeft
            }, isNaN(o.top) && (o = {top: 1e13, right: 1e13, bottom: 1e13, left: 1e13}), o
        }, this.setBounderies = function () {
            var e = s.settings.bounderies;
            s.actualPos = s.getPosition(), s.bounderies = {top: -e[0], right: e[1], bottom: e[2], left: -e[3]};
            var t = s.getContainerBounderies();
            t && (s.bounderies = {
                top: Math.max(-t.top, s.bounderies.top),
                right: Math.min(t.right, s.bounderies.right),
                bottom: Math.min(t.bottom, s.bounderies.bottom),
                left: Math.max(-t.left, s.bounderies.left)
            })
        }, this.move = function () {
            s.draggableBox.css({top: s.actualPos.y + $.clayfy.dY, left: s.actualPos.x + $.clayfy.dX})
        }, this.getPosition = function (e) {
            var t, i = s.el.offsetParent(), o = (t = void 0 === e ? s.draggableBox || s.el : e).position();
            return {y: o.top + i.scrollTop(), x: o.left + i.scrollLeft()}
        }, this.fixDeltasWithBounderies = function () {
            $.clayfy.dX > s.bounderies.right && ($.clayfy.dX = s.bounderies.right), $.clayfy.dX < s.bounderies.left && ($.clayfy.dX = s.bounderies.left), $.clayfy.dY < s.bounderies.top && ($.clayfy.dY = s.bounderies.top), $.clayfy.dY > s.bounderies.bottom && ($.clayfy.dY = s.bounderies.bottom), s.settings.moveX || ($.clayfy.dX = 0), s.settings.moveY || ($.clayfy.dY = 0)
        }, this.destroy = function () {
        }, function () {
            s.originalPos = s.getPosition(), s.actualPos = s.originalPos, h(), p(), s.el.addClass("clayfy-box"), s.settings.move || s.el.addClass("clayfy-not-move"), a = s.el.css("cursor"), n = $(s.settings.not), s.settings.overflow && $("body").append(s.tempContainer), s.el.on("mousedown touchstart", C), $("body").on("mouseup touchend", R), s.settings.escape && (s.el.on("clayfy-dragstart", function (e) {
                e.stopPropagation(), $(window).on("keydown", y)
            }), s.el.on("clayfy-drop", function () {
                $(window).off("keydown", y)
            })), !1 !== s.settings.scrollable && "node" === s.container.type && (s.settings.container instanceof t || ("string" == typeof s.settings.scrollable ? s.settings.scrollable = s.settings.scrollable ? s.settings.scrollable + " , " + s.settings.container : s.settings.container : s.settings.scrollable instanceof $ && (s.settings.scrollable = s.settings.scrollable.add(s.settings.container)))), s.el.on("clayfy-dragstart", x), s.el.on("clayfy-drag", B), s.el.on("clayfy-drop", D), 0 != s.settings.droppable ? (s.updateDragElement(), s.updateDropArea(), S()) : s.settings.ghost && s.el.on("clayfy-drop", c)
        }()
    }

    function t(e, t) {
        this.draggableEl = e instanceof $ ? e : $(e), this.values, this.el, this.type, this.originalDraggable, this.width = 0, this.height = 0, this.innerHeight = 0, this.innerWidth = 0, this.offset = {
            top: 0,
            left: 0,
            innerBottom: 0,
            innerRight: 0,
            innerLeft: 0,
            innerTop: 0
        };
        var i = this, o = function () {
            var e = a(i.el);
            i.width = i.el.width(), i.height = i.el.height(), i.innerWidth = e.innerWidth, i.innerHeight = e.innerHeight, i.offset = i.el.offset(), i.offset.innerTop = i.offset.top + parseInt(i.el.css("border-top-width")), i.offset.innerLeft = i.offset.left + parseInt(i.el.css("border-left-width")), i.offset.innerBottom = i.offset.innerTop + i.innerHeight, i.offset.innerRight = i.offset.innerLeft + i.innerWidth
        }, r = function () {
            var e = i.getDraggableValues();
            i.offset = {
                top: e.offset.top - (e.position.top - i.originalDraggable.position.top) - i.values[0],
                left: e.offset.left - (e.position.left - i.originalDraggable.position.left) - i.values[3]
            }, i.width = i.originalDraggable.outerWidth + i.values[3] + i.values[1], i.height = i.originalDraggable.outerHeight + i.values[0] + i.values[2], i.innerWidth = i.width, i.innerHeight = i.height, i.offset.innerTop = i.offset.top, i.offset.innerLeft = i.offset.left, i.offset.innerBottom = i.offset.top + i.height, i.offset.innerRight = i.offset.left + i.width
        }, a = function (e) {
            var t, i, o, r, a, n = e instanceof $ ? e : $(e);
            return n.length ? (t = n[0].style.position, "static" === n.css("position") && n.css({position: "relative"}), o = $("<div>", {style: "position:absolute;top:0;left:0;bottom:0;right:0"}), i = $("<div>", {style: "position:absolute;top:0;left:0;width:100%;height:100%"}), o.append(i), n.append(o), r = i.width(), a = i.height(), o.remove(), n[0].style.position = t, {
                innerWidth: r,
                innerHeight: a
            }) : {width: 0, height: 0}
        };
        this.getDraggableValues = function () {
            var e = i.draggableEl.offset(), t = i.draggableEl.offsetParent();
            return {
                position: {top: i.draggableEl.position().top + t.scrollTop(), left: i.draggableEl.position().left + t.scrollLeft()},
                offset: e,
                outerWidth: i.draggableEl.outerWidth(),
                outerHeight: i.draggableEl.outerHeight()
            }
        }, function () {
            "string" == typeof t || t instanceof $ ? (i.el = t instanceof $ ? t : $(t), i.type = "node", "static" === i.el.css("position") && i.el.css("position", "relative"), i.update = o) : (i.values = t, i.type = "object", i.update = r, i.originalDraggable = i.getDraggableValues()), i.update()
        }()
    }

    function i(t, i) {
        this.el = t instanceof $ ? t : $(t), this.originalSize = {}, this.initSize = {}, this.handlers = [], this.actualSize, this.newSize, this.draggable, this.preserveAspectRatio = !1, this.shift = !1, this.status = "ready", this.touchableDevice;
        var r = $.extend(!0, {}, $.clayfy.settings, {
            callbacks: {
                resizestart: function () {
                }, resize: function () {
                }, resizeend: function () {
                }
            }
        });
        this.settings = $.extend(!0, {}, r, i);
        var a, n = this, s = !1, l = function (e) {
            27 === e.keyCode && n.cancel()
        }, d = function () {
            var t = ["top left", "top right", "bottom left", "bottom right", "left", "right", "top", "bottom"];
            n.touchableDevice && (t = ["bottom right"]), "static" === n.el.css("position") && n.el.css("position", "relative"), n.cssPosition = n.el.css("position");
            var i = {container: n.settings.container, not: ".clayfy-handler", escape: !1, droppable: n.settings.droppable};
            i = $.extend(!0, {}, n.settings, i), n.settings.not && (i.not += "," + n.settings.not), n.settings.move && "relative" !== n.cssPosition || (i.move = !1), n.draggable = new e(n.el, i);
            for (var r = 0; r < t.length; r++) {
                var a = t[r].split(" "), s = !0;
                for (var l in a) n.settings.hasOwnProperty(a[l]) && n.settings[a[l]] || (s = !1);
                s && n.handlers.push(new o(t[r], n))
            }
            n.touchableDevice && n.el.addClass("clayfy-touch-device")
        };
        this.getSize = function () {
            n.parent = n.el.offsetParent();
            var e = n.parent, t = n.el.position();
            return {width: n.el.width(), height: n.el.height(), left: t.left + e.scrollLeft(), top: t.top + e.scrollTop(), outerWidth: n.el.outerWidth(), outerHeight: n.el.outerHeight()}
        }, this.getNewSize = function () {
            var e = n.el.outerHeight(), t = n.el.outerWidth(), i = n.el.position(), o = i.left + n.parent.scrollLeft(), r = i.top + n.parent.scrollTop();
            return {outerWidth: t, outerHeight: e, top: r, left: o, right: o + t, bottom: r + e, width: n.el.width(), height: n.el.height()}
        }, this.resize = {
            left: function () {
                n.el.width(n.actualSize.width - $.clayfy.dX), "relative" !== n.cssPosition && n.el.css({left: n.actualSize.left + $.clayfy.dX})
            }, top: function () {
                n.el.height(n.actualSize.height - $.clayfy.dY), "relative" !== n.cssPosition && n.el.css({top: n.actualSize.top + $.clayfy.dY})
            }, bottom: function () {
                n.el.height(n.actualSize.height + $.clayfy.dY)
            }, right: function () {
                n.el.width(n.actualSize.width + $.clayfy.dX)
            }
        }, this.hideHandlers = function () {
            "ready" === n.status && ($.each(n.handlers, function (e, t) {
                t.el.css("display", "none")
            }), s = !1)
        }, this.showHandlers = function () {
            s || "ready" !== n.status && !n.touchableDevice || ($.each(n.handlers, function (e, t) {
                t.el.css("display", "block")
            }), s = !0, n.updateHandlersPosition())
        }, this.updateHandlersPosition = function () {
            n.newSize = n.getNewSize(), $.each(n.handlers, function (e, t) {
                t.updatePosition()
            })
        }, this.cancel = function () {
            console.log("cancelled"), n.status = "ready", n.hideHandlers(), n.status = "canceling", $("body").trigger("mouseup");
            var e = "relative" !== n.cssPosition ? n.initSize : {width: n.initSize.width, height: n.initSize.height};
            n.el.animate(e, 100, function () {
                n.status = "ready", n.el.is(":hover") && n.showHandlers(), n.el.trigger("clayfy-cancel")
            })
        }, function () {
            n.touchableDevice = isTouchDevice(), n.originalSize = n.getSize(), n.actualSize = n.originalSize, n.newSize = n.getNewSize(), d(), n.preserveAspectRatio = n.settings.preserveAspectRatio, n.el.on("clayfy-resizestart", function (e) {
                n.initSize = n.getNewSize(), $(window).on("keydown", l), n.status = "resizing"
            }), n.el.on("clayfy-resizeend", function () {
                $(window).off("keydown", l), n.status = "ready"
            }), n.el.on("clayfy-dragstart", function (e) {
                e.stopPropagation(), n.initSize = n.getSize(), n.status = "dragging"
            }), n.el.on("clayfy-drop", function (e) {
                e.stopPropagation(), n.status = "ready"
            }), n.el.on("clayfy-resize clayfy-drag", n.updateHandlersPosition), $(window).on("resize", n.updateHandlersPosition), n.el.on("clayfy-dragstart", function (e) {
                e.stopPropagation(), $(window).on("keydown", l)
            }), n.el.on("clayfy-drop", function (e) {
                e.stopPropagation(), $(window).off("keydown", l)
            });
            var e = n.el;
            $.each(n.handlers, function (t, i) {
                n.hideHandlers(), e = e.add(i.el)
            }), e.on("mouseover", function () {
                a && clearTimeout(a), n.showHandlers()
            }), e.on("mouseout", function () {
                a = setTimeout(n.hideHandlers, 20)
            }), n.el.on("clayfy-resizeend clayfy-drop", function (e) {
                e.stopPropagation(), n.el.parent().find(":hover").length || n.touchableDevice || (s = !1, n.el.trigger("mouseout"))
            }), n.touchableDevice && (e.on("touchstart", function () {
                a && clearTimeout(a), n.showHandlers(), a = setTimeout(n.hideHandlers, 4e3)
            }), n.el.on("clayfy-resizeend clayfy-drop", function () {
                n.el.trigger("click")
            }))
        }()
    }

    function o(t, i) {
        this.el = $("<div>", {class: "clayfy-handler clayfy-" + t, style: "position: absolute"}), this.resizable = i, this.position = t, this.draggable;
        var o = this, r = !1;
        this.updatePosition = function () {
            var e = i.newSize;
            switch (o.position) {
                case"left":
                    o.el.css({width: 5, left: e.left, top: e.top, height: e.outerHeight});
                    break;
                case"right":
                    o.el.css({width: 5, left: e.right - 5, top: e.top, height: e.outerHeight});
                    break;
                case"top":
                    o.el.css({height: 5, left: e.left, top: e.top, width: e.outerWidth});
                    break;
                case"bottom":
                    o.el.css({height: 5, left: e.left, top: e.bottom - 5, width: e.outerWidth});
                    break;
                case"top left":
                    o.el.css({width: 8, height: 8, left: e.left, top: e.top});
                    break;
                case"top right":
                    o.el.css({width: 8, height: 8, left: e.right - 8, top: e.top});
                    break;
                case"bottom left":
                    o.el.css({width: 8, height: 8, left: e.left, top: e.bottom - 8});
                    break;
                case"bottom right":
                    o.resizable.touchableDevice ? o.el.css({width: 18, height: 18, left: e.right - 20, top: e.bottom - 20}) : o.el.css({width: 8, height: 8, left: e.right - 8, top: e.bottom - 8})
            }
        }, this.setBounderies = function (e) {
            var t, i, r, a, n = e || [1e5, 1e5, 1e5, 1e5], s = [];
            o.resizable.actualSize = o.resizable.getSize(), t = o.resizable.actualSize, i = o.resizable.settings, r = o.resizable.originalSize.width / o.resizable.originalSize.height, (a = o.draggable.getContainerBounderies()) || (a = {
                top: 1e13,
                right: 1e13,
                bottom: 1e13,
                left: 1e13
            });
            for (var l = 0, d = i.maxSize.length; l < d; l++) null === i.maxSize[l] && (i.maxSize[l] = 1e13);
            $.clayfy.getInner(o.draggable.el);
            "left" !== o.position && "top" !== o.position && "top left" !== o.position || (n[1] = t.outerWidth - i.minSize[0], n[3] = i.maxSize[0] - t.outerWidth, n[2] = t.outerHeight - i.minSize[1], n[0] = i.maxSize[1] - t.outerHeight, o.draggable.settings.bounderies = n, s[3] = Math.min(a.left, n[3], a.top * r, n[0] * r), s[0] = Math.min(a.top, n[0], a.left / r, n[3] / r), s[1] = Math.min(a.right, n[1], a.bottom * r + n[2], n[2] * r), s[2] = Math.min(a.bottom, n[2], a.right / r + n[1], n[1] / r)), "right" !== o.position && "bottom" !== o.position && "bottom right" !== o.position || (n[3] = t.outerWidth - i.minSize[0], n[1] = i.maxSize[0] - t.outerWidth, n[0] = t.outerHeight - i.minSize[1], n[2] = i.maxSize[1] - t.outerHeight, o.draggable.settings.bounderies = n, s[1] = Math.min(a.right, n[1], a.bottom * r, n[2] * r), s[2] = Math.min(a.bottom, n[2], a.right / r, n[1] / r), s[3] = Math.min(a.left, n[3], a.top * r + n[0], n[0] * r), s[0] = Math.min(a.top, n[0], a.left / r + n[3], n[3] / r)), "bottom left" === o.position && (n[0] = t.outerHeight - i.minSize[1], n[1] = t.outerWidth - i.minSize[0], n[2] = i.maxSize[1] - t.outerHeight, n[3] = i.maxSize[0] - t.outerWidth, o.draggable.settings.bounderies = n, s[3] = parseInt(Math.min(a.left, n[3], a.bottom * r, n[2] * r)), s[2] = parseInt(Math.min(a.bottom, n[2], a.left / r, n[3] / r)), s[0] = parseInt(Math.min(a.top, n[0], a.right / r + n[1], n[1] / r)), s[1] = parseInt(Math.min(a.right, n[1], a.top * r + n[0], n[0] * r))), "top right" === o.position && (n[0] = i.maxSize[1] - t.outerHeight, n[1] = i.maxSize[0] - t.outerWidth, n[2] = t.outerHeight - i.minSize[1], n[3] = t.outerWidth - i.minSize[0], o.draggable.settings.bounderies = n, s[0] = parseInt(Math.min(a.top, n[0], a.right / r, n[1] / r)), s[1] = parseInt(Math.min(a.right, n[1], a.top * r, n[0] * r)), s[3] = parseInt(Math.min(a.left, n[3], a.bottom * r + n[2], n[2] * r)), s[2] = parseInt(Math.min(a.bottom, n[2], a.left / r + n[3], n[3] / r))), o.originalBounderies = {
                top: -n[0],
                right: n[1],
                bottom: n[2],
                left: -n[3]
            }, o.aspectRatioBounderies = {top: -s[0], right: s[1], bottom: s[2], left: -s[3]}, o.draggable.bounderies = o.resizable.preserveAspectRatio ? o.aspectRatioBounderies : o.originalBounderies
        }, this.fixDeltas = function () {
            var e = $.clayfy;
            if (o.resizable.preserveAspectRatio) t = o.resizable.originalSize.width / o.resizable.originalSize.height;
            if (!o.resizable.preserveAspectRatio && o.resizable.shiftKey) var t = o.resizable.actualSize.width / o.resizable.actualSize.height;
            (o.resizable.preserveAspectRatio || o.resizable.shiftKey) && ("right" === o.position && (e.dY = e.dX / t), "bottom" === o.position && (e.dX = e.dY * t), "left" === o.position && (e.dY = e.dX / t), "top" === o.position && (e.dX = e.dY * t), "top left" === o.position && (e.dY = e.dX / t), "top right" === o.position && (e.dY = -e.dX / t), "bottom left" === o.position && (e.dY = -e.dX / t), "bottom right" === o.position && (e.dY = e.dX / t))
        }, function () {
            i.settings.className && o.el.addClass(i.settings.className), o.updatePosition(), o.resizable.el.after(o.el), o.draggable = new e(o.el, {
                move: !1,
                container: i.draggable.container,
                scroll: !1,
                escape: !1
            }), o.draggable.el.on("clayfy-drop", function (e) {
                i.el.trigger("clayfy-resizeend"), i.settings.callbacks.resizeend()
            }), o.draggable.el.on("clayfy-dragstart", function (e) {
                e.stopPropagation(), o.resizable.preserveAspectRatio || (o.resizable.originalSize = o.resizable.getSize()), i.el.trigger("clayfy-beforeresize"), o.setBounderies(), i.el.trigger("clayfy-resizestart"), i.settings.callbacks.resizestart(), r = !1
            }), o.draggable.el.on("clayfy-drag", function (e) {
                e.shiftKey && !i.preserveAspectRatio && (i.shiftKey = !0), e.shiftKey || (i.shiftKey = !1), !r || e.shiftKey || i.preserveAspectRatio || (console.log("Desactivate: preserve aspect ratio"), o.draggable.bounderies = o.originalBounderies, r = !1), r || !e.shiftKey || i.preserveAspectRatio || (console.log("Activate: preserve aspect ratio"), o.draggable.bounderies = o.aspectRatioBounderies, r = !0), i.preserveAspectRatio && !i.shiftKey && (o.draggable.bounderies = o.aspectRatioBounderies)
            }), t.indexOf("left") > -1 && o.draggable.el.on("clayfy-drag", function (e) {
                (i.preserveAspectRatio || i.shiftKey) && (o.fixDeltas(), "left" === t && i.resize.top()), i.resize.left()
            }), t.indexOf("top") > -1 && o.draggable.el.on("clayfy-drag", function (e) {
                (i.preserveAspectRatio || i.shiftKey) && (o.fixDeltas(), "top" === t && i.resize.left()), i.resize.top()
            }), t.indexOf("right") > -1 && o.draggable.el.on("clayfy-drag", function (e) {
                (i.preserveAspectRatio || i.shiftKey) && (o.fixDeltas(), "right" === t && i.resize.bottom()), i.resize.right()
            }), t.indexOf("bottom") > -1 && o.draggable.el.on("clayfy-drag", function (e) {
                (i.preserveAspectRatio || i.shiftKey) && (o.fixDeltas(), "bottom" === t && i.resize.right()), i.resize.bottom()
            }), o.draggable.el.on("clayfy-drag", function (e) {
                i.el.trigger("clayfy-resize"), i.settings.callbacks.resize()
            }), o.resizable.touchableDevice && o.el.addClass("clayfy-touch-device")
        }()
    }

    function r(t, i) {
        this.el = t instanceof $ ? t : $(t), this.draggableBox, this.dropArea = $("<div>", {class: "clayfy-sort-droparea"}), this.draggable, this.droppable, this.droppableParent, this.index, this.indexRelative, this.parent, this.settings = $.extend(!0, {}, $.clayfy.settings, i);
        var o, r = this, a = function (e) {
            27 === e.keyCode && r.cancel()
        }, n = function () {
            r.draggableBox = r.el.clone(), r.draggableBox.css({position: "absolute", width: "100%", height: "100%"}).addClass("clayfy-sort-dragging");
            var e = r.el.parent();
            "static" === e.css("position") && e.css("position", "relative")
        }, s = function () {
            f(), c(), r.index = r.droppable.index(r.el), r.parent = r.el.parent(), r.indexRelative = r.parent.find(r.droppable).index(r.el), r.draggableBox.css({
                width: r.el.outerWidth(),
                height: r.el.outerHeight(),
                top: r.el.position().top,
                left: r.el.position().left
            }), l(), r.draggableBox.appendTo(r.parent), r.el.css({visibility: "hidden"})
        }, l = function () {
            r.dropArea.appendTo(r.el.parent()), r.dropArea.css({
                position: "absolute",
                width: r.el.outerWidth(),
                height: r.el.outerHeight(),
                top: r.el.position().top + parseInt(r.el.css("margin-top")) - parseInt(r.dropArea.css("border-top-width")),
                left: r.el.position().left + parseInt(r.el.css("margin-left")) - parseInt(r.dropArea.css("border-left-width"))
            })
        }, d = function (e) {
            var t = r.parent.find(r.droppable);
            if (r.parent.is($(r.droppedTarget).parent())) {
                var i = t.index(r.el);
                r.indexRelative < i ? t.eq(r.indexRelative).before(r.el) : t.eq(r.indexRelative).after(r.el)
            } else t.length ? t.eq(r.indexRelative).before(r.el) : r.parent.append(r.el);
            l()
        }, g = function (e) {
            f(), (!1 === r.el.triggerHandler("validateChange") || !r.parent.is($(r.droppedTarget).parent()) && !r.settings.export || o) && d();
            var t = r.dropArea.parent().offset(), i = r.draggableBox.parent().offset(), a = r.el.position().left + (t.left - i.left), n = r.el.position().top + (t.top - i.top);
            return r.draggableBox.animate({top: n, left: a}, 200, function () {
                r.dropArea.detach(), r.el[0].style.visibility = "", r.draggableBox.detach(), f();
                var e = r.droppable.index(r.el);
                e != r.index && (r.index = e, r.el.trigger($.Event("clayfy-changeorder", {index: r.index, order: r.droppable}))), r.el.parent().find(".clayfy-sort-helper").remove()
            }), o = !1, !1
        }, p = function (e) {
            if (!r.el.is(e.target)) {
                f();
                var t = r.droppable.index(e.target), i = r.droppable.index(r.el);
                r.droppedTarget = e.target, t > i ? $(e.target).after(r.el) : $(e.target).before(r.el), l(), r.draggable.updateDropArea(), r.parent.find(r.droppable).length < 2 ? r.parent.find(".clayfy-sort-helper").length || r.parent.append('<div class="clayfy-sort-helper" style="position: absolute; width: 100%; height: 100%; top: 0; left:0"></div>') : r.parent.find(".clayfy-sort-helper").remove(), $(".clayfy-sort-helper").each(function () {
                    var e = $(this);
                    e.parent().is(r.droppableParent) && r.draggable.addDroppable(e)
                })
            }
        }, f = function () {
            r.settings.siblings ? r.droppable = r.settings.siblings instanceof $ ? r.settings.siblings : $(r.settings.siblings) : r.droppable = r.el.siblings().andSelf()
        }, c = function () {
            r.droppableParent || (r.droppableParent = r.el.parent()), r.droppable.each(function () {
                r.droppableParent = r.droppableParent.add($(this).parent())
            })
        };
        this.cancel = function () {
            o = !0, $("body").trigger("mouseup")
        }, function () {
            f(), c(), n(), r.el.on("mousedown touchstart", function (e) {
                "mousedown" === e.type && 1 !== e.which || (s(), r.draggableBox.trigger($.Event(e.type, e)))
            });
            var t = $.extend(!0, {}, r.settings, {droppable: r.droppable, escape: !1, dropoutside: !0});
            r.draggable = new e(r.draggableBox, t), r.draggableBox.on("clayfy-drop", g), r.draggableBox.on("clayfy-dropoutside", function (e) {
                return !1
            }), r.draggableBox.on("clayfy-dragenter", p), r.draggableBox.on("clayfy-dragstart", function () {
                r.draggable.resetDroppable(r.droppable), $(".clayfy-sort-helper").each(function () {
                    var e = $(this);
                    e.parent().is(r.droppableParent) && r.draggable.addDroppable(e)
                })
            }), r.draggableBox.on("clayfy-dragstart", function (e) {
                e.stopPropagation(), $(window).on("keydown", a)
            }), r.draggableBox.on("clayfy-drop", function () {
                $(window).off("keydown", a)
            })
        }()
    }

    $.clayfy = {
        dX: 0,
        dY: 0,
        container: function (e, i) {
            return new t(e, i)
        },
        settings: {
            type: "draggable",
            bounderies: [1e7, 1e7, 1e7, 1e7],
            container: "",
            moveX: !0,
            moveY: !0,
            move: !0,
            not: "",
            ghost: !1,
            coverScreen: !0,
            scrollable: "",
            droppable: "",
            fit: !0,
            dropoutside: !1,
            migrate: !1,
            overflow: !1,
            escape: !0,
            propagate: !0,
            preserveAspectRatio: !1,
            maxSize: [500, 200],
            minSize: [100, 50],
            left: !0,
            top: !0,
            right: !0,
            bottom: !0,
            className: "",
            siblings: "",
            export: !0,
            dragstart: function (e) {
            },
            drag: function (e) {
            },
            drop: function (e) {
            }
        },
        getInner: function (e) {
            var t, i, o, r, a, n = e instanceof $ ? e : $(e);
            return n.length ? (t = n[0].style.position, "static" === n.css("position") && n.css({position: "relative"}), o = $("<div>", {style: "position:absolute;top:0;left:0;bottom:0;right:0"}), i = $("<div>", {style: "position:absolute;top:0;left:0;width:100%;height:100%"}), o.append(i), n.append(o), r = i.width(), a = i.height(), o.remove(), n[0].style.position = t, {
                innerWidth: r,
                innerHeight: a
            }) : {width: 0, height: 0}
        }
    };
    var a;
    $.fn.clayfy = function (t) {
        var o = arguments;
        if (void 0 === t || "object" == typeof t) {
            var n = $.clayfy.settings.type;
            switch (void 0 !== t && void 0 !== t.type && (n = t.type), n) {
                case"draggable":
                    a = e;
                    break;
                case"resizable":
                    a = i;
                    break;
                case"sortable":
                    a = r
            }
            return this.each(function () {
                $.data(this, "clayfy") || $.data(this, "clayfy", new a(this, t))
            })
        }
        if ("string" == typeof t && "_" !== t[0] && "init" !== t) {
            if ("instance" === t) return this.length ? $.data(this[0], "clayfy") : null;
            if (0 == Array.prototype.slice.call(o, 1).length && -1 != $.inArray(t, $.fn.clayfy.getters)) {
                var s = $.data(this[0], "clayfy");
                return s[t].apply(s, Array.prototype.slice.call(o, 1))
            }
            return this.each(function () {
                var e = $.data(this, "clayfy");
                "function" == typeof e[t] && e[t].apply(e, Array.prototype.slice.call(o, 1))
            })
        }
    }, $.fn.clayfy.getters = ["getPosition"]
}(jQuery);
isTouchDevice = function () {
    return "ontouchstart" in window || navigator.maxTouchPoints
};