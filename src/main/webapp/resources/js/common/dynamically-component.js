function createTextBox(itemIndex, dtoName, propertyName, className, placeHolder, value, isReadOnly) {
    var readOnly = '';
    if (isReadOnly) {
        readOnly = "readonly=true";
    }
    var textBox = "<input type='text' class='form-control " + className + "' id='" + dtoName + "[" + itemIndex + "]." + propertyName + "'  name='" + dtoName + "[" + itemIndex + "]." + propertyName + "' " + readOnly + "  placeholder='" + placeHolder + "' value='" + value + "'/>";
    textBox = "<div class='form-group'>" + textBox + "</div>";
    return textBox;
}

function createHiddenBox(itemIndex, dtoName, propertyName, className, placeHolder, value, isReadOnly) {
    var readOnly = '';
    if (isReadOnly) {
        readOnly = "readonly=true";
    }
    var textBox = "<input type='hidden' class='form-control " + className + "' id='" + dtoName + "[" + itemIndex + "]." + propertyName + "'  name='" + dtoName + "[" + itemIndex + "]." + propertyName + "' value='" + value + "'/>";
    return textBox;
}

function createSelectBox(itemIndex, dtoName, propertyName, className, items, value, isReadOnly) {
    var readOnly = '';
    if (isReadOnly) {
        readOnly = "readonly=true";
    }
    var selectBox = "<select class='form-control " + className + "' name='" + dtoName + "[" + itemIndex + "]." + propertyName + "' id='" + dtoName + "[" + itemIndex + "]." + propertyName + "' " + readOnly + ">" + items + "</select>";
    selectBox = "<div class='form-group'>" + selectBox + "</div>";
    return selectBox;
}

function createRadioBox(itemIndex, dtoName, propertyName, className, isChecked, value, isReadOnly) {
    var readOnly = '';
    var checked = '';
    if (isReadOnly) {
        readOnly = "readonly=true";
    }
    if (isChecked) {
        checked = 'checked';
    }
    var radioBox = "<input type='radio' class='align-middle " + className + "' name='" + dtoName + "[" + itemIndex + "]." + propertyName + "' " + readOnly + "  ' " + checked + " ' value='" + value + "'/>";
    radioBox = "<div class='form-group'>"
        + "<label class='custom-control custom-checkbox mb-2 mr-sm-2 mb-sm-0 pl-3 col col-form-label check-container dynaradiocontainer'>" + radioBox
        + "<span class='radiomark dynaradio'></span>"
        + "<span class='custom-control-description'></span>"
        + "</label>"
        + "</div>";
    return radioBox;
}

function createCheckBox(itemIndex, dtoName, propertyName, className, isChecked, value, isReadOnly) {
    var readOnly = '';
    var checked = '';
    if (isReadOnly) {
        readOnly = "readonly=true";
    }
    if (isChecked) {
        checked = 'checked';
    }
    var checkBox = "<input type='checkbox' class='align-middle " + className + "' name='" + dtoName + "[" + itemIndex + "]." + propertyName + "' " + readOnly + "  ' " + checked + " ' value='" + value + "'/>";
    checkBox = "<div class='form-group'>"
        + "<label class='custom-control custom-checkbox check-container dynacheckcontainer'>" + checkBox
        + "<span class='checkmark dynacheck'></span>"
        + "<span class='custom-control-description'></span>"
        + "</label>"
        + "</div>";
    return checkBox;
}

function createButton(itemIndex, dtoName, propertyName, className, isReadOnly, uniqueTableName) {

    var readOnly = '';
    if (isReadOnly) {
        readOnly = " disabled ";
    }

    var button = "<button type='button'  class='btn btn-default " + className + "' name='" + dtoName + "[" + itemIndex + "]." + propertyName + "' " + readOnly + "  ' ' onclick='deleteRow(" + uniqueTableName + "," + itemIndex + ")' ><i class='fa fa-minus'></i></button>";
    button = "<span class='input-group-btn'>"
        + button
        + "</span>";
    return button;
}


function createChosenBox(itemIndex, dtoName, propertyName, className, items, value, isReadOnly) {
    var readOnly = '';
    if (isReadOnly) {
        readOnly = "readonly=true";
    }
    var selectBox = "<select class='form-control disable " + className + "' name='" + dtoName + "[" + itemIndex + "]." + propertyName + "' id='" + dtoName + "[" + itemIndex + "]." + propertyName + "' " + readOnly + ">" + items + "</select>";
    selectBox = "<div class='form-group'>" + selectBox + "</div>";
    return selectBox;
}


