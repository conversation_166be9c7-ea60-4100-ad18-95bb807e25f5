/**
 * <AUTHOR>
 */

var table;

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#team-table').DataTable({
        "autoWidth": false,
        "processing": true,
        "serverSide": true,
        "columnDefs": [
            {"targets": 1, "orderable": false},
            {"targets": 2, "orderable": false},
            {"targets": 3, "orderable": false},
            {"targets": 4, "orderable": false},
            {"targets": 5, "orderable": false},
            {"targets": 6, "orderable": false},
            {"width": "25%", "targets": 0},
            {"width": "25%", "targets": 1},
            {"width": "25%", "targets": 2},
            {"width": "25%", "targets": 3}
        ],
        "order": [[0, "desc"]],
        searching: false,
        responsive: true,
        "ajax": {
            "url": contextPath + "/UserManagementController/teamList",
            type: 'POST',
        },
        "columns": [
            {
                "data": "index", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
            },
            {
                "data": "teamName", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
            },
            {
                "data": "channelCount", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
            },
            {
                "data": "memberCount", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
            },
            {
                "data": "createdDate", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
            },
            {
                "data": "updatedDate", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
            },
            {
                "data": "teamId", "render": function (data, type, obj, meta) {
                    data = "<button class='btn-primary btn' type='button' title='View Team' onclick='viewTeam(" + data + ")' ><i class='fa fa-eye'></i></button>";
                    return data;
                }
            }
        ],
    })
});

function search() {
    table.ajax.reload();
}


