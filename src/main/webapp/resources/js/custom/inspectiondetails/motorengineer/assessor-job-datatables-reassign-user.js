/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;
$("#assignUsers,#assignUserBulkReassign").chosen({
    no_results_text: "No results found!",
    width: "100%"
});

function viewClaimDetails(claimNo, assignUser, refNo, inspectionId, statusId) {
    loadEngineersLevelWise(inspectionId, statusId);
    $("#panelUser").modal({
        backdrop: 'static',
        keyboard: false
    });

    $('#claimNo1').val(claimNo);
    $('#refNo').val(refNo);
    $('#claimNo').val(claimNo);
    $('#assignUsers').val(assignUser).trigger("chosen:updated");

}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 11}],
        searching: false,

        //  "scrollY":        "50vh",
        //  "scrollCollapse": true,
        // "scrollX": true,
        "order": [[0, "desc"]],

        /*"ajax": {
         "url": contextPath+"/CallCenterController",
         "type": "GET"
         },*/

        "ajax": {
            "url": contextPath + "/ClaimAssignUserReassignController/jobList",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtV_status = $("#txtV_status").val();
                d.txtJobNumber = $("#txtJobNumber").val();
                d.assignUser = $("#assignUser").val();
                d.txtInspectionType = $("#txtInspectionType").val();
            }
        },
        "columns": [
            {"data": "refNo"},
            {
                'data': null,
                'render': function (data, type, obj, row) {
                    var checkBox = '';
                    if (obj.statusId == '80' && $('#txtV_status').val() != 80) {
                        checkBox = "";
                    } else {
                        checkBox = "<input type='checkbox' class='checkBtn' id='RRB" + data.claimNo + " - " + data.refNo + "' name='checkBox' onclick='enableReassignBtn()'/>";
                        checkBox = checkBox + "<input type='hidden'  id='RRB" + data.claimNo + " - " + data.jobNo + "' name='ClaimNoAndJobNoBox' />";
                        checkBox = checkBox + "<input type='hidden' name='refNo' id='" + data.refNo + "' />";
                    }
                    return checkBox;
                }
            },
            {
                "data": "index", "render": function (data, type, obj, meta) {


                    if (obj.intimationType == '2') {
                        data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                    } else if (obj.intimationType == '1') {
                        data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                    } else {
                        data = "<span style='padding-left: 5px;'  ></span>" + data;
                    }


                    if (obj.excessType == 1) {
                        data += " <i class='fa fa-history text-warning'></i>";
                    } else if (obj.excessType == 2) {
                        data += " <i class='fa fa-history text-danger'></i>";

                    }
                    //
                    // if (obj.excessType == '2') {
                    //     data =data+ "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                    // } else if (obj.intimationType == '1') {
                    //     data = data + "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                    // } else {
                    //     data = data+"<span style='padding-left: 5px;'  ></span>" + data;
                    // }
                    // <i class='fa fa-star-o'></i>fa-history
                    // if (obj.followCallAgnetServiceRate > 0 && obj.followCallAssessorServiceRate > 0 && obj.followCallUserId != "") {
                    //     data += " <i class='fa fa-star' title='Follow-Up Call Done'></i>";
                    // } else {
                    //     data += " <i class='fa fa-star-o text-mute' title='Follow-Up Call Pending'></i>";
                    // }
                    return data;
                }
            },
            {"data": "jobNo"},
            {"data": "claimNo"},
            {"data": "vehicleNo"},
            {"data": "inspectionType"},
            {
                "data": "assignToRteDateTime", "render": function (data, type, obj, meta) {
                    if (obj.statusId == '8'
                        || obj.statusId == '9'
                        || obj.statusId == '10'
                        || obj.statusId == '33'
                        || obj.statusId == '14') {
                        return data;
                    } else if (obj.statusId == '29' || obj.statusId == '34') {
                        return obj.assignDateTime;
                    } else {
                        return "";
                    }
                }
            },
            {
                "data": "jobStatus", "className": "text-center", "render": function (data, type, obj, meta) {

                    if (obj.jobStatus == 'COMPLETED') {
                        data = "<span class='badge font-weight-bold badge-success'>" + data + "</span>";
                    } else {
                        data = "<span class='badge font-weight-bold badge-danger'>" + data + "</span>";
                    }
                    return data;
                }
            },
            {
                "data": "statusId", "render": function (data, type, obj, meta) {
                    if (obj.statusId == '29') {
                        data = "<span class='badge font-weight-bold badge-warning'>ASSIGNED</span>";
                    } else if (obj.statusId == '7') {
                        data = "<span class='badge font-weight-bold badge-secondary'>ATTENDED</span>";
                    } else if (obj.statusId == '10') {
                        data = "<span class='badge font-weight-bold badge-danger'>CLAIM CHANGE REQUESTED</span>";
                    } else if (obj.statusId == '8') {
                        data = "<span class='badge font-weight-bold badge-primary'>SUBMITTED</span>";
                    } else if (obj.statusId == '9') {
                        data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                    } else if (obj.statusId == '14') {
                        data = "<span class='badge font-weight-bold badge-danger'>INSPECTION CHANGE REQUESTED</span>";
                    } else if (obj.statusId == '2') {
                        data = "<span class='badge font-weight-bold badge-info'>FORWARDED</span>";
                    } else if (obj.statusId == '33') {
                        data = "<span class='badge font-weight-bold badge-pill'>FORWARD FOR INFORM GARAGE & CUSTOMER</span>";
                    } else if (obj.statusId == '34') {
                        data = "<span class='badge font-weight-bold badge-light'>RETURNED DESKTOP</span>";
                    } else if (obj.statusId == '80') {
                        data = "<span class='badge font-weight-bold badge-info'>INSPECTION FORWARDED</span>";
                    }

                    return data;
                }
            },
            {
                "data": "estimationApprStatus", "className": "text-center", "render": function (data, type, obj, meta) {

                    if (obj.estimationApprStatus == 'A') {
                        data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                    } else if (obj.estimationApprStatus == 'R') {
                        data = "<span class='badge font-weight-bold badge-danger'>REJECTED</span>";
                    } else {
                        data = "<span class='badge font-weight-bold badge-warning'>PENDING</span>";
                    }
                    return data;
                }
            },
            {
                "data": "assFeeAprStatus", "className": "text-center", "render": function (data, type, obj, meta) {

                    if (obj.assFeeAprStatus == 'A') {
                        data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                    } else if (obj.assFeeAprStatus == 'R') {
                        data = "<span class='badge font-weight-bold badge-danger'>REJECTED</span>";
                    } else {
                        if (obj.inspectionTypeId == '8') {
                            data = "<span class='badge font-weight-bold badge-secondary'>N/A</span>";
                        } else {
                            data = "<span class='badge font-weight-bold badge-warning'>PENDING</span>";
                        }


                    }
                    return data;
                }
            },

            {
                "data": "claimNo", "render": function (data, type, obj, meta) {
                    data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.claimNo + ",\"" + obj.assignUser + "\"," + obj.refNo + ",\"" + obj.inspectionTypeId + "\"," + "\"" + obj.statusId + "\")' ><i class='fa fa-edit'></i></button>";
                    return data;
                }
            }

        ],
        // "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {
        //
        //     if (obj.isDoubt == "Y") {//DR
        //         $(nRow).addClass('badge-danger');
        //     } else if (obj.isOnSiteOffer == "Y") {//FW
        //         $(nRow).addClass('badge-warning');
        //     } else if (obj.claimStatus == "3") {//AS
        //         $(nRow).addClass('badge-success');
        //     } else if (obj.claimStatus == "32") {//RE
        //         $(nRow).addClass('badge-danger');
        //     } else if (obj.claimStatus == "30") {//AS PE
        //         $(nRow).addClass('badge-warning');
        //     } else if (obj.claimStatus == "31") {//DRAFT & ASSIGN
        //         $(nRow).addClass('badge-dark');
        //     }else if (obj.v_status == "30") {//AS
        //         $(nRow).addClass('badge-secondary');
        //     } else {
        //         // $(nRow).addClass('badge-danger');
        //     }
        // }
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
     var data = table.row(this).data();
     var id = data['n_ref_no'];
     $("#P_N_REF_NO").val(id);
     document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
     document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function enableReassignBtn() {

    var elements = document.getElementsByName('checkBox');

    for (var i = 0; i < elements.length; i++) {
        var id = elements[i].getAttribute('id');

        if (document.getElementById(id).checked && ($('#assignUser').val() != '' || ($('#txtInspectionType').val() == 1 || $('#txtInspectionType').val() == 2))) {
            $("#rteReassignDiv").show();
            return;
        } else {
            $("#rteReassignDiv").hide();
        }
    }
}

function search() {
    table.ajax.reload();
    $(".checkBtn").prop("checked", false);
    $("#selectAll").prop("checked", false);
    $("#rteReassignDiv").hide();
    $('#jobNoListDiv').html('');
    $('#jobNoListTxt').val('');
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
     // $( table.cells().nodes() ).removeClass( 'badge-success' );
     for (colIdx = 1; colIdx <= noColumns; colIdx++) {
     $(table.column(colIdx).nodes()).addClass(cssClass);

     }*/
}
