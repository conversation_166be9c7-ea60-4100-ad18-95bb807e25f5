///**
// * Created by a<PERSON><PERSON> on 5/2/18.
// */
//
//
//$(document).ready(function () {
//
//
//    // FORM VALIDATION FEEDBACK ICONS
//    // =================================================================
//
//
//    //tableTypes
//
//    // =================================================================
//
//
//    $('#frmMain')
//        .formValidation({
//            framework: 'bootstrap',
//            excluded: ':disabled',
//            icon: {
//                valid: 'glyphicon glyphicon-ok',
//                invalid: 'glyphicon glyphicon-remove',
//                validating: 'glyphicon glyphicon-refresh'
//            },
//            fields: {
//
//                customerName: {
//                    validators: {
//                        notEmpty: {
//                            message: 'This field is required and cannot be empty.'
//                        }
//                    }
//                }, contactNo: {
//                    validators: {
//                        notEmpty: {
//                            message: 'This field is required and cannot be empty.'
//                        },
//                        callback :numericValidationCallback
//                    }
//                }, address1: {
//                    validators: {
//                        notEmpty: {
//                            message: 'This field is required and cannot be empty.'
//                        }
//                    }
//                }, address2: {
//                    validators: {
//                        notEmpty: {
//                            message: 'This field is required and cannot be empty.'
//                        }
//                    }
//                }, thirdPartyInvoled: {
//                    validators: {
//                        notEmpty: {
//                            message: 'This field cannot be empty.'
//                        }
//                    }
//                }
//
//
//            }
//        }).on('success.form.fv', function (e) {
//        // Prevent form submission
//        e.preventDefault();
//
//        var $form = $(e.target),
//            fv = $form.data('formValidation');
//        document.frmMain.action = contextPath+"/MotorEngineerController/saveAri";
//        document.frmMain.submit();
//    });
//
//
//});
//
//
//
