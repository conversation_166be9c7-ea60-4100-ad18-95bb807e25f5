/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewClaimDetails(polRefNo, jobNo, claimNo, refNo) {
    showLoader();
    $("#P_N_CLIM_NO").val(claimNo);
    $("#P_N_JOB_NO").val(jobNo);
    $("#P_POL_N_REF_NO").val(polRefNo);
    $("#P_N_REF_NO").val(refNo);
    document.getElementById('frmForm').action = contextPath + "/MotorEngineerController/viewEdit";
    document.getElementById('frmForm').submit();

}

$(window).on('load', function () {

    setOfferTypeSearchFunction();

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;

    if ('70' == $('#type').val()) {
        table = $('#demo-dt-basic').DataTable({
            "lengthMenu": [50, 100, 150, 250, 500],
            "processing": true,
            "serverSide": true,
            "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
                "orderable": false,
                "targets": 1
            }, {"orderable": false, "targets": 11}],
            searching: false,
            responsive: true,
            //  "scrollY":        "50vh",
            //  "scrollCollapse": true,
            // "scrollX": true,
            "order": [[0, "desc"]],

            /*"ajax": {
             "url": contextPath+"/CallCenterController",
             "type": "GET"
             },*/

            "ajax": {
                "url": contextPath + "/MotorEngineerController/jobList",
                type: 'POST',
                //  data: this.params
                "data": function (d) {
                    d.txtFromDate = $("#txtFromDate").val();
                    d.txtToDate = $("#txtToDate").val();
                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.txtRefNumber = $("#txtRefNumber").val();
                    d.txtPolNumber = $("#txtPolNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtV_status = $("#txtV_status").val();
                    d.txtJobNumber = $("#txtJobNumber").val();
                    d.txtAssessmentApprStatus = $("#txtAssessmentApprStatus").val();
                    d.txtInspectionType = $("#txtInspectionType").val();
                    d.txtAssessorApprStatus = $("#txtAssessorApprStatus").val();
                    d.txtOfferType = $("#txtOfferType").val();
                }
            },
            "columns": [
                {"data": "refNo"},
                {
                    "data": "index", "render": function (data, type, obj, meta) {


                    if (obj.intimationType == '2') {
                        data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                    } else if (obj.intimationType == '1') {
                        data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                    } else {
                        data = "<span style='padding-left: 5px;'  ></span>" + data;
                    }


                    if (obj.excessType == 1) {
                        data += " <i class='fa fa-history text-warning'></i>";
                    } else if (obj.excessType == 2) {
                        data += " <i class='fa fa-history text-danger'></i>";

                    }

                    return data;
                }
                },
                {"data": "jobNo"},
                {"data": "claimNo"},
                {"data": "vehicleNo"},
                {
                    "data": "inspectionType", "className": "text-center", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
                },
                {"data": "approveAssignRteUser"},
                {"data": "approveAssignRteDateTime"},
                {
                    "data": "jobStatus", "className": "text-center", "render": function (data, type, obj, meta) {
                    if (obj.inspectionTypeId == '8') {
                        data = "<span class='badge font-weight-bold badge-secondary'>N/A</span>";
                    } else if (obj.jobStatus == 'COMPLETED') {
                        data = "<span class='badge font-weight-bold badge-success'>" + data + "</span>";
                    } else {
                        data = "<span class='badge font-weight-bold badge-danger'>" + data + "</span>";
                    }
                    return data;
                }
                },
                {
                    "data": "statusId", "render": function (data, type, obj, meta) {
                    if (obj.statusId == '29') {
                        data = "<span class='badge font-weight-bold badge-warning'>ASSIGNED</span>";
                    } else if (obj.statusId == '7') {
                        data = "<span class='badge font-weight-bold badge-secondary'>ATTENDED</span>";
                    } else if (obj.statusId == '10') {
                        data = "<span class='badge font-weight-bold badge-danger'>CLAIM CHANGE REQUESTED</span>";
                    } else if (obj.statusId == '8') {
                        data = "<span class='badge font-weight-bold badge-primary'>SUBMITTED</span>";
                    } else if (obj.statusId == '9') {
                        data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                    } else if (obj.statusId == '14') {
                        data = "<span class='badge font-weight-bold badge-danger'>INSPECTION CHANGE REQUESTED</span>";
                    } else if (obj.statusId == '80') {
                        data = "<span class='badge font-weight-bold badge-info'>INSPECTION FORWARDED</span>";
                    } else if (obj.statusId == '33') {
                        data = "<span class='badge font-weight-bold badge-pill'>FORWARD FOR INFORM GARAGE & CUSTOMER</span>";
                    } else if (obj.statusId == '34') {
                        data = "<span class='badge font-weight-bold badge-light'>RETURNED DESKTOP</span>";
                    }

                    return data;
                }
                },
                {
                    "data": "estimationApprStatus",
                    "className": "text-center",
                    "render": function (data, type, obj, meta) {

                        if (obj.estimationApprStatus == 'A') {
                            data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                        } else if (obj.estimationApprStatus == 'R') {
                            data = "<span class='badge font-weight-bold badge-danger'>REJECTED</span>";
                        } else {
                            data = "<span class='badge font-weight-bold badge-warning'>PENDING</span>";
                        }
                        return data;
                    }
                },
                {
                    "data": "assFeeAprStatus", "className": "text-center", "render": function (data, type, obj, meta) {
                    console.log(obj.assFeeAprStatus)
                    if (obj.assFeeAprStatus == 'A') {
                        data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                    } else if (obj.assFeeAprStatus == 'R') {
                        data = "<span class='badge font-weight-bold badge-danger'>REJECTED</span>";
                    } else if (obj.assFeeAprStatus == 'H') {
                        data = "<span class='badge font-weight-bold badge-danger'>HOLD</span>";
                    } else {
                        if (obj.inspectionTypeId == '8') {
                            data = "<span class='badge font-weight-bold badge-secondary'>N/A</span>";
                        } else {
                            data = "<span class='badge font-weight-bold badge-warning'>PENDING</span>";
                        }
                    }
                    return data;
                }
                },

                {
                    "data": "claimNo", "render": function (data, type, obj, meta) {
                    data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.polRefNo + "," + obj.jobNo + "," + obj.claimNo + "," + obj.refNo + ")' ><i class='fa fa-eye'></i></button>";
                    return data;
                }
                }

            ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {
                if (obj.priority == 'HIGH') {
                    $(nRow).addClass('badge-priority');
                }
            }

        });
    } else {
        table = $('#demo-dt-basic').DataTable({
            "lengthMenu": [50, 100, 150, 250, 500],
            "processing": true,
            "serverSide": true,
            "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
                "orderable": false,
                "targets": 1
            }, {"orderable": false, "targets": 11}],
            searching: false,
            responsive: true,
            //  "scrollY":        "50vh",
            //  "scrollCollapse": true,
            // "scrollX": true,
            "order": [[0, "desc"]],

            /*"ajax": {
             "url": contextPath+"/CallCenterController",
             "type": "GET"
             },*/

            "ajax": {
                "url": contextPath + "/MotorEngineerController/jobList",
                type: 'POST',
                //  data: this.params
                "data": function (d) {
                    d.txtFromDate = $("#txtFromDate").val();
                    d.txtToDate = $("#txtToDate").val();
                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.txtRefNumber = $("#txtRefNumber").val();
                    d.txtPolNumber = $("#txtPolNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtV_status = $("#txtV_status").val();
                    d.txtJobNumber = $("#txtJobNumber").val();
                    d.txtAssessmentApprStatus = $("#txtAssessmentApprStatus").val();
                    d.txtInspectionType = $("#txtInspectionType").val();
                    d.txtAssessorApprStatus = $("#txtAssessorApprStatus").val();
                    d.txtOfferType = $("#txtOfferType").val();
                }
            },
            "columns": [
                {"data": "refNo"},
                {
                    "data": "index", "render": function (data, type, obj, meta) {


                    if (obj.intimationType == '2') {
                        data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                    } else if (obj.intimationType == '1') {
                        data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                    } else {
                        data = "<span style='padding-left: 5px;'  ></span>" + data;
                    }


                    if (obj.excessType == 1) {
                        data += " <i class='fa fa-history text-warning'></i>";
                    } else if (obj.excessType == 2) {
                        data += " <i class='fa fa-history text-danger'></i>";

                    }

                    return data;
                }
                },
                {"data": "jobNo"},
                {"data": "claimNo"},
                {"data": "vehicleNo"},
                {
                    "data": "inspectionType", "className": "text-center", "render": function (data, type, obj, meta) {
                    return "<span class='badge font-weight-bold'>" + data + "</span>";
                }
                },
                {"data": "assignUser"},
                {
                    "data": "assignToRteDateTime", "render": function (data, type, obj, meta) {
                    if (obj.statusId == '8'
                        || obj.statusId == '9'
                        || obj.statusId == '10'
                        || obj.statusId == '33'
                        || obj.statusId == '14'
                        || obj.statusId == '80') {
                        return data;
                    } else if (obj.statusId == '29' || obj.statusId == '34') {
                        return obj.assignDateTime;
                    } else {
                        return "";
                    }
                }
                },
                {
                    "data": "jobStatus", "className": "text-center", "render": function (data, type, obj, meta) {
                    if (obj.inspectionTypeId == '8') {
                        data = "<span class='badge font-weight-bold badge-secondary'>N/A</span>";
                    } else if (obj.jobStatus == 'COMPLETED') {
                        data = "<span class='badge font-weight-bold badge-success'>" + data + "</span>";
                    } else {
                        data = "<span class='badge font-weight-bold badge-danger'>" + data + "</span>";
                    }
                    return data;
                }
                },
                {
                    "data": "statusId", "render": function (data, type, obj, meta) {
                    if (obj.statusId == '29') {
                        data = "<span class='badge font-weight-bold badge-warning'>ASSIGNED</span>";
                    } else if (obj.statusId == '7') {
                        data = "<span class='badge font-weight-bold badge-secondary'>ATTENDED</span>";
                    } else if (obj.statusId == '10') {
                        data = "<span class='badge font-weight-bold badge-danger'>CLAIM CHANGE REQUESTED</span>";
                    } else if (obj.statusId == '8') {
                        data = "<span class='badge font-weight-bold badge-primary'>SUBMITTED</span>";
                    } else if (obj.statusId == '9') {
                        data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                    } else if (obj.statusId == '14') {
                        data = "<span class='badge font-weight-bold badge-danger'>INSPECTION CHANGE REQUESTED</span>";
                    } else if (obj.statusId == '80') {
                        data = "<span class='badge font-weight-bold badge-info'>INSPECTION FORWARDED</span>";
                    } else if (obj.statusId == '33') {
                        data = "<span class='badge font-weight-bold badge-pill'>FORWARD FOR INFORM GARAGE & CUSTOMER</span>";
                    } else if (obj.statusId == '34') {
                        data = "<span class='badge font-weight-bold badge-light'>RETURNED DESKTOP</span>";
                    }

                    return data;
                }
                },
                {
                    "data": "estimationApprStatus",
                    "className": "text-center",
                    "render": function (data, type, obj, meta) {

                        if (obj.estimationApprStatus == 'A') {
                            data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                        } else if (obj.estimationApprStatus == 'R') {
                            data = "<span class='badge font-weight-bold badge-danger'>REJECTED</span>";
                        } else {
                            data = "<span class='badge font-weight-bold badge-warning'>PENDING</span>";
                        }
                        return data;
                    }
                },
                {
                    "data": "assFeeAprStatus", "className": "text-center", "render": function (data, type, obj, meta) {
                    console.log(obj.assFeeAprStatus)
                    if (obj.assFeeAprStatus == 'A') {
                        data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                    } else if (obj.assFeeAprStatus == 'R') {
                        data = "<span class='badge font-weight-bold badge-danger'>REJECTED</span>";
                    } else if (obj.assFeeAprStatus == 'H') {
                        data = "<span class='badge font-weight-bold badge-danger'>HOLD</span>";
                    } else {
                        if (obj.inspectionTypeId == '8') {
                            data = "<span class='badge font-weight-bold badge-secondary'>N/A</span>";
                        } else {
                            data = "<span class='badge font-weight-bold badge-warning'>PENDING</span>";
                        }
                    }
                    return data;
                }
                },

                {
                    "data": "claimNo", "render": function (data, type, obj, meta) {
                    data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.polRefNo + "," + obj.jobNo + "," + obj.claimNo + "," + obj.refNo + ")' ><i class='fa fa-eye'></i></button>";
                    return data;
                }
                }

            ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {
                if (obj.priority == 'HIGH') {
                    $(nRow).addClass('badge-priority');
                }
            }

        });

    }


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});
var TYPE = $('#type').val();
if (TYPE == 30) {
    var claimNo = $('#pendingInspectionClaimNo').val();
    $("#txtClaimNumber").val(claimNo);
    $("#txtAssessmentApprStatus").val(2);
    $("#txtV_status").val(0)
    search();
}

function search() {
    table.ajax.reload();
    return false;
}

$('#txtV_status').change(function () {
    setOfferTypeSearchFunction();
});

function setOfferTypeSearchFunction() {
    var status = $('#txtV_status').val();
    if (status == '8' || status == '9' || status == '10' || status == '70') {
        $('#offerTypeDiv').show();
    } else {
        $('#offerTypeDiv').hide();
    }
    if (status == '8') {
        if ($('#txtOfferType').val() == '3') {
            $('#txtOfferType').val('0')
        }
        $("#txtOfferType option[value*='3']").prop('disabled', true);
    } else {
        $("#txtOfferType option[value*='3']").prop('disabled', false);
    }
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
     // $( table.cells().nodes() ).removeClass( 'badge-success' );
     for (colIdx = 1; colIdx <= noColumns; colIdx++) {
     $(table.column(colIdx).nodes()).addClass(cssClass);

     }*/
}
