/**
 * Created by a<PERSON><PERSON> on 5/2/18.
 */


$(document).ready(function () {


    // FORM VALIDATION FEEDBACK ICONS
    // =================================================================


    //tableTypes

    // =================================================================
    var onsiteReviewChecked = $('#onsiteReviewCheck').is(':checked') ? 'Y' : 'N';
    var cosPart = {
            selector: '.cospart',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                },
                callback: currencyValidationCallback

            }

        }, costLabour = {
            selector: '.costlabour',
            validators: {
                callback: currencyValidationCallback

            }

        }, excess = {
            selector: '.excess',
            validators: {
                callback: currencyValidationCallback

            }

        }, acr = {
            selector: '.acr',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                },
                callback: currencyValidationCallback

            }
        }, boldTirePenalty = {
            selector: '.boldtyrepenalty',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, boldPercent = {
            selector: '.boldpercent',
            validators: {
                between: {
                    min: 0,
                    max: 100,
                    message: 'The percentage must be between 0 and 100'
                }
            }
        }, boldTirePenaltyAmount = {
            selector: '.boldtirepenaltyamount',
            validators: {
                callback: currencyValidationCallback

            }
        }, underInsuradPenalty = {
            selector: '.underinsuradpenalty',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, underPenaltyPercent = {
            selector: '.underpenaltypercent',
            validators: {
                between: {
                    min: 0,
                    max: 100,
                    message: 'The percentage must be between 0 and 100'
                }
            }
        }, underPenaltyAmount = {
            selector: '.underpenaltyamount',
            validators: {
                callback: currencyValidationCallback

            }
        }, provideOffer = {
            selector: '.provideoffer',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, payableAmount = {
            selector: '.payableamount',
            validators: {
                callback: currencyValidationCallback
            }

        }, advancedAmount = {
            selector: '.advancedamount',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                },
                callback: currencyValidationCallback
            }

        }, numberOfDatesRepairs = {
            selector: '.numberofdatesrepairs',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                },
                callback: numericValidationCallback
            }
        }, offerAmount = {
            selector: '.offeramount',
            validators: {
                callback: currencyValidationCallback
            }
        }, policeRequested = {
            selector: '.policerequested',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, investigateClaim = {
            selector: '.investigate',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, ariAndSalvage = {
            selector: '.arisalvage',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, preAccidentValue = {
            selector: '.preaccidentvalue',
            validators: {
                callback: currencyValidationCallback
            }
        }, professionalFee = {
            selector: '.professionalfee',
            validators: {
                callback: currencyValidationCallback
            }
        }, miles = {
            selector: '.miles',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                },
                callback: decimalValidationCallback
            }
        }, telephoneCharge = {
            selector: '.telephonecharge',
            validators: {
                callback: currencyValidationCallback
            }
        }, otherChage = {
            selector: '.otherchage',
            validators: {
                callback: currencyValidationCallback
            }
        }, specialDeduction = {
            selector: '.specialdeduction',
            validators: {
                callback: currencyValidationCallback
            }
        }, totalCharge = {
            selector: '.totalcharge',
            validators: {
                callback: currencyValidationCallback
            }
        }, sumInsured = {
            selector: '.suminsured',
            validators: {
                callback: currencyValidationCallback
            }
        }, ariInOrder = {
            selector: '.ariinorder',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, salvageInOrder = {
            selector: '.salvageinorder',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        }, desktopTopOffer = {
            selector: '.desktopoffer',
            validators: {
                notEmpty: {
                    message: 'The field is required'
                }
            }
        },
        jobType = {
            selector: '.jobtype',
            validators: {
                callback: dropDownValidation
            }
        }, offerType = {
            selector: '.offerType',
            validators: {
                callback: dropDownValidation
            }
        }, settlementMethod = {
            selector: '.settlementMethod',
            validators: {
                callback: dropDownValidation
            }
        }


    $('#frmMain')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                makeConfirm: {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        }
                    }
                }, modelConfirm: {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        }
                    }
                }, yearMakeConfirm: {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        }
                    }
                }, engNoConfirm: {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        }
                    }
                },
                pav: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty'
                        },
                        callback: currencyValidationCallback
                    }
                }, damagePart: {
                    validators: {
                        notEmpty: {
                            message: 'This field cannot be empty'
                        }

                    }
                }, genuineOfAccident: {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        }
                    }
                }, firstStatementReq: {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        }
                    }
                }, investReq: {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        }
                    }
                }, 'onSiteInspectionDetailsDto.acr': {
                    validators: {
                        notEmpty: {
                            message: 'The field is required'
                        },
                        callback: currencyValidationCallback
                    }
                }, firstStatementReqReason: {
                    validators: {
                        callback: {
                            message: 'The field is required',
                            callback: function (value, validator, $field) {
                                if ("Yes" == $("input[name='firstStatementReq']:checked").val()) {
                                    if (0 < value) {
                                        return true;
                                    } else {
                                        return false;
                                    }
                                } else {
                                    return true;
                                }
                            }
                        }
                    }
                },
                deductions: {
                    validators: {
                        callback: currencyValidationCallback
                    }
                },
                reasonOfDeduction: {
                    validators: {
                        callback: {
                            message: 'The field is required',
                            callback: function (value, validator, $field) {
                                var deductionVal = $('#deductions').val();
                                var reasonDeduction = $('#reasonOfDeduction').val().trim();
                                if (deductionVal > 0) {
                                    if (reasonDeduction.length > 0) {
                                        return true;
                                    } else {
                                        return false;
                                    }
                                } else {
                                    return true;
                                }
                            }
                        }
                    }
                },


                cosPart: cosPart,
                costLabour: costLabour,
                inExcess: excess,
                inAcr: acr,
                boldTirePenalty: boldTirePenalty,
                boldPercent: boldPercent,
                boldTirePenaltyAmount: boldTirePenaltyAmount,
                underInsuradPenalty: underInsuradPenalty,
                underPenaltyPercent: underPenaltyPercent,
                underPenaltyAmount: underPenaltyAmount,
                provideOffer: provideOffer,
                payableAmount: payableAmount,
                advancedAmount: advancedAmount,
                numberOfDatesRepairs: numberOfDatesRepairs,
                offerAmount: offerAmount,
                policeRequested: policeRequested,
                investigateClaim: investigateClaim,
                ariAndSalvage: ariAndSalvage,
                preAccidentValue: preAccidentValue,
                professionalFee: professionalFee,
                miles: miles,
                telephoneCharge: telephoneCharge,
                otherChage: otherChage,
                specialDeduction: specialDeduction,
                totalCharge: totalCharge,
                sumInsured: sumInsured,
                ariInOrder: ariInOrder,
                salvageInOrder: salvageInOrder,
                desktopOffer: desktopTopOffer,
                jobType: jobType,
                offerType: offerType,
                settlementMethod: settlementMethod


            }
        }).on('success.form.fv', function (e) {
        // Prevent form submission
        e.preventDefault();

        var $form = $(e.target),
            fv = $form.data('formValidation'),
            button = $form.data('formValidation').getSubmitButton();

        if (button.attr('value') == 'assessorAuthorize') {
            calAssessorFee();
            authorizeAssessorFee();
        } else if (button.attr('value') == 'forward') {
            // forwardToNextUser();
            checkAcrAndAuthorize("Y");
        } else if (button.attr('value') == 'return') {
            returnByApproveAssignRte();
        } else {
            checkAcrAndAuthorize("N");
        }

    })
        .on('change', '[name="firstStatementReq"]', function (e) {
            $('#frmMain').formValidation('revalidateField', 'firstStatementReqReason');
        });


    $('#tpForm')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {

                itemType: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
//                remark: {
//                    validators: {
//                        notEmpty: {
//                            message: 'This field is required and cannot be empty.'
//                        }
//                    }
//                },
//                vehicleNo: {
//                    validators: {
//                        notEmpty: {
//                            message: 'This field is required and cannot be empty.'
//                        }
//                    }
//                },
                intendClaim: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required.'
                        }
                    }
                },
                lossType: {
                    validators: {
                        callback: dropDownValidation
                    }
                }

            }
        }).on('success.form.fv', function (e) {

        e.preventDefault();
        var $form = $(e.target);
        fv = $form.data('formValidation');
        addThirdParty();
    });


    $('#deductions').change(function () {
        $('#frmMain').formValidation('revalidateField', 'reasonOfDeduction');
        // alert('das');
    });

});






