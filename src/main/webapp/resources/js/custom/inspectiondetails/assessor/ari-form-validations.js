/**
 * Created by a<PERSON><PERSON> on 5/2/18.
 */



$(document).ready(function () {

    $('#frmRequested')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {

                customerName: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                }, contactNo: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        },
                        callback: numericValidationCallback
                    }
                }, address1: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                }, address2: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                }, address3: {
                    validators: {
                        notEmpty: {
                            message: 'This field cannot be empty.'
                        }
                    }
                }


            }
        }).on('success.form.fv', function (e) {
        // Prevent form submission
        e.preventDefault();
        var $form = $(e.target),
            fv = $form.data('formValidation');

        const isUpload = $("#isUploaded").val();

        if (isUpload == 'true') {
            var formData = $('#frmRequested').serialize();
            if ($('#customerName').val() != '' && $('#contactNo').val() != '' && $('#address1').val() != '' && $('#address2').val() != '') {
                $.ajax({
                    url: contextPath + "/RequestAriController/updateAri",
                    type: 'POST',
                    data: formData,
                    success: function (result) {
                        var obj = JSON.parse(result);

                        if (obj == "SUCCESS") {
                            notify("Successfully updated", "success");
                            // specialRemarksDiv();
                            // logdetails();
                        } else {
                            notify("Can not be updated", "danger");
                        }

                    }
                });
            }
        } else {
            notify("Please Upload Required Documents prior to Submitting Customer Details", "danger");
        }
        // document.frmRequest.action = contextPath+"/RequestAriController/updateAri";
        // document.frmRequest.submit();
    });



});



