$(document).ready(function () {

    $.fn.DataTable.ext.pager.numbers_length = 5;
    var url = contextPath + "/AssessorPaymentDetailsController/loadPaginatedAssessorPayments";

    var isApprovalTeam = $("#allCheckBox").val();

    if (isApprovalTeam) {
        $('#approvaltable').DataTable({
            "lengthMenu": [500, 1000, 1500, 2000, 5000], "processing": true, "serverSide": true, "columnDefs": [

                {
                    "orderable": false, "targets": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
                }, {
                    "targets": 1, "visible": false, "orderable": false,
                },],

            responsive: true, searching: false,

            ajax: {
                url: url, type: 'POST', data: function (d) {
                    d.txtFromDate = $("#txtFromDate").val();
                    d.txtToDate = $("#txtToDate").val();
                    d.txtInspectionType = $("#txtInspectionType").val();
                    d.paymentType = $("#paymentType").val();
                    d.status = $("#status").val();
                    d.txtJobNumber = $("#txtJobNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.claimType = $("#claimType").val();
                    d.rteCode = $("#rteCode").val();
                    d.assessorName = $("#assessorName").val();
                    d.policyChannelType = $("#policyChannelType").val();
                },
            },

            'columns': [{
                "data": "txtId", "className": "text-center", "render": function (data, type, obj, meta) {
                    var id = data;
                    var txtId = "";
                    if (obj.is_approval_team) {

                        if (obj.paymentStatus === 'Pending') {
                            if (obj.isfPending) {
                            } else {
                                txtId = '<div class="text-left"><input type="checkbox" id="' + id + '" name="checkBoxs" class="allchecks check-align"/></div>';
                            }

                        }
                    }


                    return txtId;
                }
            }, {'data': 'refNumber'}, {'data': 'jobNumber'}, {'data': 'policyNumber'}, {'data': 'isfClaimNumber'}, {'data': 'claimNumber'}, {'data': 'vehicleNumber'}, {'data': 'dateOfInspection'}, {'data': 'inspectionType'}, {'data': 'placeOfInspection'}, {'data': 'assessorName'}, {'data': 'allocatedProfessionalFee'}, {'data': 'approvedProfessionalFee'}, {'data': 'mileage'}, {'data': 'travelFee'}, {'data': 'costOfCall'}, {'data': 'otherFee'}, {'data': 'deductions'}, {'data': 'totalFee'}, {
                "data": "action", "className": "text-center", "render": function (data, type, obj, meta) {
                    var txtId = obj.txtId;

                    if (obj.is_approval_team && obj.paymentStatus === 'Pending') {
                        var approveButton;
                        if (obj.isfPending) {
                            approveButton = "<div class=\"col-6\"></div>";
                        } else {
                            approveButton = "<div class=\"col-6\">\n" + "    <button class='btn-success btn btn-sm float-left btn-xs approveBtn' type='button' title='Send To Finance' onclick='approve(" + txtId + ")' >\n" + "        <i class='fa fa-thumbs-up'></i>\n" + "    </button>\n" + "</div>";
                        }

                        var rejectButton = "<div class=\"col-6\">\n" + "    <button class='btn-danger btn btn-sm float-right btn-xs' type='button' title='Reject Payment' onclick='showRejectBox(" + txtId + ")' >\n" + "        <i class='fa fa-thumbs-down'></i>\n" + "    </button>\n" + "</div>";

                        data = "<div class=\"row\" style=\"width: 100px\">\n" + approveButton + rejectButton + "</div>";
                    } else {
                        data = "";
                    }
                    return data;
                }
            },


            ],
            'footerCallback': function (row, data, start, end, display) {
                var totalAmount = data.reduce(function (sum, row) {
                    return sum + row.totalFee;
                }, 0);

                var formattedTotalAmount = formatNumber(totalAmount, '#,##0.00'); // Adjust the format pattern as needed

                console.log('Total Amount:', formattedTotalAmount);
                $('#totalAmount1').text(formattedTotalAmount);
                $('#totalAmount2').text(formattedTotalAmount);
            }

        });

    } else {
        $('#approvaltable').DataTable({
            "lengthMenu": [500, 1000, 1500, 2000, 5000], "processing": true, "serverSide": true, "columnDefs": [

                {
                    "targets": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "orderable": false,
                }, {
                    "targets": 0, "visible": false, "orderable": false,
                },

            ],

            responsive: true, searching: false,

            ajax: {
                url: url, type: 'POST', data: function (d) {
                    d.txtFromDate = $("#txtFromDate").val();
                    d.txtToDate = $("#txtToDate").val();
                    d.txtInspectionType = $("#txtInspectionType").val();
                    d.paymentType = $("#paymentType").val();
                    d.status = $("#status").val();
                    d.txtJobNumber = $("#txtJobNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.claimType = $("#claimType").val();
                    d.rteCode = $("#rteCode").val();
                    d.assessorName = $("#assessorName").val();
                    d.policyChannelType = $("#policyChannelType").val();
                },
            },

            'columns': [{'data': 'refNumber'}, {'data': 'jobNumber'}, {'data': 'policyNumber'}, {'data': 'isfClaimNumber'}, {'data': 'claimNumber'}, {'data': 'vehicleNumber'}, {'data': 'dateOfInspection'}, {'data': 'inspectionType'}, {'data': 'placeOfInspection'}, {'data': 'assessorName'}, {'data': 'allocatedProfessionalFee'}, {'data': 'approvedProfessionalFee'}, {'data': 'mileage'}, {'data': 'travelFee'}, {'data': 'costOfCall'}, {'data': 'otherFee'}, {'data': 'deductions'}, {'data': 'totalFee'},],
            'footerCallback': function (row, data, start, end, display) {
                var totalAmount = data.reduce(function (sum, row) {
                    return sum + row.totalFee;
                }, 0);

                var formattedTotalAmount = formatNumber(totalAmount, '#,##0.00'); // Adjust the format pattern as needed

                console.log('Total Amount:', formattedTotalAmount);
                $('#totalAmount1').text(formattedTotalAmount);
                $('#totalAmount2').text(formattedTotalAmount);
            }

        });

    }


    table.destroy();


});

function formatNumber(value, format) {
    var formatter = new Intl.NumberFormat('en-US', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
    return formatter.format(value);
}