/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -

$("#txtRte,#txtAssessor,#txtAssignedBy").chosen({
    no_results_text: "No results found!",
    width: "100%"
});

var table;

function viewClaimDetails(polRefNo, jobNo, claimNo, refNo) {
    showLoader();
    $("#P_N_CLIM_NO").val(claimNo);
    $("#P_N_JOB_NO").val(jobNo);
    $("#P_POL_N_REF_NO").val(polRefNo);
    $("#P_N_REF_NO").val(refNo);
    document.getElementById('frmForm').action = contextPath + "/InspectionDetailsController/viewEdit";
    document.getElementById('frmForm').submit();

}

$(window).on('load', function () {
    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;

    if ($('#type').val() == 2) {
        table = $('#demo-dt-basic').DataTable({
            "lengthMenu": [50, 100, 150, 250, 500],
            "processing": true,
            "serverSide": true,
            "columnDefs": [
                {"visible": false, "targets": 0, "orderable": false},
                {"orderable": false, "targets": 1},
                {"orderable": false, "targets": 10}
            ],
            searching: false,
            responsive: true,

            //  "scrollY":        "50vh",
            //  "scrollCollapse": true,
            // "scrollX": true,
            "order": [[0, "desc"]],

            /*"ajax": {
             "url": contextPath+"/CallCenterController",
             "type": "GET"
             },*/

            "ajax": {
                "url": contextPath + "/InspectionDetailsController/jobList?type=" + $('#type').val(),
                type: 'POST',
                //  data: this.params
                "data": function (d) {
                    d.txtFromDate = $("#txtFromDate").val();
                    d.txtToDate = $("#txtToDate").val();
                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.txtRefNumber = $("#txtRefNumber").val();
                    d.txtPolNumber = $("#txtPolNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtV_status = $("#txtV_status").val();
                    d.txtJobNumber = $("#txtJobNumber").val();
                    d.txtRte = $('#txtRte').val();
                    d.txtAssessor = $('#txtAssessor').val();
                    d.txtAssignedBy = $('#txtAssignedBy').val();
                    d.type = $('#type').val();
                    d.inspectionType = $('#txtInspectionType').val();
                }
            },
            "columns": [
                {"data": "refNo"},
                {
                    "data": "index", "render": function (data, type, obj, meta) {


                        if (obj.intimationType == '2') {
                            data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                        } else if (obj.intimationType == '1') {
                            data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                        } else {
                            data = "<span style='padding-left: 5px;'  ></span>" + data;
                        }


                        if (obj.excessType == 1) {
                            data += " <i class='fa fa-history text-warning'></i>";
                        } else if (obj.excessType == 2) {
                            data += " <i class='fa fa-history text-danger'></i>";

                        }
                        return data;
                    }
                },
                {"data": "jobNo"},
                {"data": "claimNo"},
                {"data": "vehicleNo"},
                {"data": "inspectionType"},
                {"data": "ccAssignedBy"},
                {"data": "assignUser"},
                {"data": "assignDateTime"},
                {"data": "assignRteUser"},
                {"data": "assignRteDateTime"},
                {
                    "data": "jobStatus", "className": "text-center", "render": function (data, type, obj, meta) {

                        if (obj.jobStatus == 'COMPLETED') {
                            data = "<span class='badge font-weight-bold badge-success'>" + data + "</span>";
                        } else {
                            data = "<span class='badge font-weight-bold badge-danger'>" + data + "</span>";
                        }
                        return data;
                    }
                },
                {
                    "data": "statusId", "className": "text-center", "render": function (data, type, obj, meta) {
                        if (obj.statusId == '29') {
                            data = "<span class='badge font-weight-bold badge-warning'>ASSIGNED</span>";
                        } else if (obj.statusId == '7') {
                            data = "<span class='badge font-weight-bold badge-secondary'>ATTENDED</span>";
                        } else if (obj.statusId == '10') {
                            data = "<span class='badge font-weight-bold badge-danger'>CLAIM CHANGE REQUESTED</span>";
                        } else if (obj.statusId == '8') {
                            data = "<span class='badge font-weight-bold badge-primary'>SUBMITTED</span>";
                        } else if (obj.statusId == '9') {
                            data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                        } else if (obj.statusId == '14') {
                            data = "<span class='badge font-weight-bold badge-danger'>INSPECTION CHANGE REQUESTED</span>";
                        } else if (obj.statusId == '69') {
                            data = "<span class='badge font-weight-bold badge-danger'>REVOKED</span>";
                        } else if (obj.statusId == '80') {
                            data = "<span class='badge font-weight-bold badge-info'>INSPECTION FORWARDED</span>";
                        } else if (obj.statusId == '33') {
                            data = "<span class='badge font-weight-bold badge-pill'>FORWARD FOR INFORM GARAGE & CUSTOMER</span>";
                        } else if (obj.statusId == '34') {
                            data = "<span class='badge font-weight-bold badge-light'>RETURNED DESKTOP</span>";
                        }

                        return data;
                    }
                }
            ]


        });
    } else {
        table = $('#demo-dt-basic').DataTable({
            "lengthMenu": [50, 100, 150, 250, 500],
            "processing": true,
            "serverSide": true,
            "columnDefs": [
                {"visible": false, "targets": 0, "orderable": false},
                {"orderable": false, "targets": 1},
                {"orderable": false, "targets": 10},
            ],
            searching: false,
            responsive: true,

            //  "scrollY":        "50vh",
            //  "scrollCollapse": true,
            // "scrollX": true,
            "order": [[0, "desc"]],

            /*"ajax": {
             "url": contextPath+"/CallCenterController",
             "type": "GET"
             },*/

            "ajax": {
                "url": contextPath + "/InspectionDetailsController/jobList?type=" + $('#type').val(),
                type: 'POST',
                //  data: this.params
                "data": function (d) {
                    d.txtFromDate = $("#txtFromDate").val();
                    d.txtToDate = $("#txtToDate").val();
                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.txtRefNumber = $("#txtRefNumber").val();
                    d.txtPolNumber = $("#txtPolNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtV_status = $("#txtV_status").val();
                    d.txtJobNumber = $("#txtJobNumber").val();
                }
            },
            "columns": [
                {"data": "refNo"},
                {
                    "data": "index", "render": function (data, type, obj, meta) {


                        if (obj.intimationType == '2') {
                            data = "<span class='fa fa-warning text-danger' title='Late' style='padding: 5px 5px;'  ></span>" + data;
                        } else if (obj.intimationType == '1') {
                            data = "<span class='fa fa-warning text-warning' title='On Site' style='padding: 5px 5px;'  ></span>" + data;
                        } else {
                            data = "<span style='padding: 5px 5px;'  ></span>" + data;
                        }


                        if (obj.excessType == 1) {
                            data += " <i class='fa fa-history text-warning' style='padding: 5px 5px;'></i>";
                        } else if (obj.excessType == 2) {
                            data += " <i class='fa fa-history text-danger' style='padding: 5px 5px;'></i>";

                        }
                        if (obj.isOnlineInspection == 'Y') {
                            data += " <i class='fa fa-mobile fa-2x' style='padding: 5px 5px;'></i>";
                        }

                        //
                        // if (obj.excessType == '2') {
                        //     data =data+ "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                        // } else if (obj.intimationType == '1') {
                        //     data = data + "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                        // } else {
                        //     data = data+"<span style='padding-left: 5px;'  ></span>" + data;
                        // }
                        // <i class='fa fa-star-o'></i>fa-history
                        // if (obj.followCallAgnetServiceRate > 0 && obj.followCallAssessorServiceRate > 0 && obj.followCallUserId != "") {
                        //     data += " <i class='fa fa-star' title='Follow-Up Call Done'></i>";
                        // } else {
                        //     data += " <i class='fa fa-star-o text-mute' title='Follow-Up Call Pending'></i>";
                        // }
                        return data;
                    }
                },
                {"data": "jobNo"},
                {"data": "claimNo"},
                {"data": "vehicleNo"},
                {"data": "inspectionType"},
                {"data": "ccAssignedBy"},
                {"data": "assignUser"},
                {"data": "assignDateTime"},
                {
                    "data": "jobStatus", "className": "text-center", "render": function (data, type, obj, meta) {

                        if (obj.jobStatus == 'COMPLETED') {
                            data = "<span class='badge font-weight-bold badge-success'>" + data + "</span>";
                        } else {
                            data = "<span class='badge font-weight-bold badge-danger'>" + data + "</span>";
                        }
                        return data;
                    }
                },
                {
                    "data": "statusId", "className": "text-center", "render": function (data, type, obj, meta) {
                        if (obj.statusId == '29') {
                            data = "<span class='badge font-weight-bold badge-warning'>ASSIGNED</span>";
                        } else if (obj.statusId == '7') {
                            data = "<span class='badge font-weight-bold badge-secondary'>ATTENDED</span>";
                        } else if (obj.statusId == '10') {
                            data = "<span class='badge font-weight-bold badge-danger'>CLAIM CHANGE REQUESTED</span>";
                        } else if (obj.statusId == '8') {
                            data = "<span class='badge font-weight-bold badge-primary'>SUBMITTED</span>";
                        } else if (obj.statusId == '9') {
                            data = "<span class='badge font-weight-bold badge-success'>APPROVED</span>";
                        } else if (obj.statusId == '80') {
                            data = "<span class='badge font-weight-bold badge-info'>INSPECTION FORWARDED</span>";
                        } else if (obj.statusId == '14') {
                            data = "<span class='badge font-weight-bold badge-danger'>INSPECTION CHANGE REQUESTED</span>";
                        } else if (obj.statusId == '69') {
                            data = "<span class='badge font-weight-bold badge-danger'>REVOKED</span>";
                        }

                        return data;
                    }
                }, {
                    "data": "claimNo", "render": function (data, type, obj, meta) {

                        data = "<button class='btn-primary text_center_btn btn' type='button' onclick='viewClaimDetails(" + obj.polRefNo + "," + obj.jobNo + "," + obj.claimNo + "," + obj.refNo + ")' ><i class='fa fa-eye'></i></button>";
                        return data;
                    }
                }


            ]

            // , "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {
            //     if (obj.claimStatus == "1") {//DR
            //         $(nRow).addClass('badge-light');
            //     } else if (obj.claimStatus == "2") {//FW
            //         $(nRow).addClass('badge-secondary');
            //     } else if (obj.claimStatus == "3") {//AS
            //         $(nRow).addClass('badge-success');
            //     } else if (obj.claimStatus == "23") {//RE
            //         $(nRow).addClass('badge-danger');
            //     } else if (obj.claimStatus == "30") {//AS PE
            //         $(nRow).addClass('badge-warning');
            //     } else if (obj.v_status == "30") {//AS
            //         $(nRow).addClass('badge-secondary');
            //     } else {
            //         // $(nRow).addClass('badge-danger');
            //     }
            //
            // }
        });
    }


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
     var data = table.row(this).data();
     var id = data['n_ref_no'];
     $("#P_N_REF_NO").val(id);
     document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
     document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
     // $( table.cells().nodes() ).removeClass( 'badge-success' );
     for (colIdx = 1; colIdx <= noColumns; colIdx++) {
     $(table.column(colIdx).nodes()).addClass(cssClass);

     }*/
}

let TYPE = $('#type').val();
if (TYPE == 1) {
    var claimNo = $('#pendingInspectionClaimNo').val();
    $("#txtClaimNumber").val(claimNo);
    $("#txtV_status").val(29)
    search();
}
