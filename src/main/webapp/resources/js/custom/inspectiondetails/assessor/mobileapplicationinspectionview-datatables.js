/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;


$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],

        "ajax": {
            "url": contextPath + "/MobileApplicationRequestController/searchAllInspection",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.filterdBy = $("#txtFilteredBy").val();
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNo").val();
                d.txtJobNumber = $("#txtJobNumber").val();
                d.txtInspectionType = $("#txtInspectionType").val();
                d.assignUser = $("#txtAssignUser").val();
            }
        },
        "columns": [
            {"data": "txnId"},
            {"data": "index"},
            {"data": "claimNo"},
            {"data": "inspectionType"},
            {"data": "jobId"},
            {"data": "assignUserId"},
            {"data": "vehicleNo"},
            {"data": "accidentDate"},
            {"data": "notifyDatetime"},
            {"data": "readDatetime"},
            {
                "data": "isMobileRead", "className": "text-center", "render": function (data, type, obj, meta) {
                    if (obj.isMobileRead == 'Y') {
                        data = "Yes";
                    } else {
                        data = "No";

                    }

                    return data;
                }
            }
        ]
    });

    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });
});


function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
