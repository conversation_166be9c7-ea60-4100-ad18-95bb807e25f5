/**
 * Created by a<PERSON><PERSON> on 7/24/18.
 */

$(document).ready(function () {
    var example = $('#approvaltable').DataTable({
        paging: false,
        searching: false,
        columnDefs: [{
            orderable: false,
            targets: 0,

        }],
        // select: {
        //     style: 'multi',
        //     selector: 'td:first-child'
        // },
        // order: [
        //     [1, 'asc']
        // ]
    });
    // example.on("click", "th.select-checkbox", function () {
    //     if ($("th.select-checkbox").hasClass("selected")) {
    //         example.rows().deselect();
    //         $("th.select-checkbox").removeClass("selected");
    //     } else {
    //         example.rows().select();
    //         $("th.select-checkbox").addClass("selected");
    //     }
    // }).on("select deselect", function () {
    //     ("Some selection or deselection going on")
    //     if (example.rows({
    //             selected: true
    //         }).count() !== example.rows().count()) {
    //         $("th.select-checkbox").removeClass("selected");
    //     } else {
    //         $("th.select-checkbox").addClass("selected");
    //     }
    // });
});