/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;


$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "paging": true,
        "columnDefs": [
            {"visible": false, "targets": 0, "orderable": false},
            {"orderable": false, "targets": 5},
            {"orderable": false, "targets": 6}
        ],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],
        "ajax": {
            // "url": contextPath + "/MobileApplicationRequestController/searchAllrequest",
            "url": contextPath + "/MobileApplicationRequestController/searchNotification",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                // d.txtClaimNumber = $("#txtClaimNo").val();
                // d.txtJobNumber = $("#txtJobNumber").val();
                // d.txtInspectionType = $("#txtInspectionType").val();
                d.filterdBy = $("#txtFilteredBy").val();
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.notificationStatus = $("#notificationStatus").val();
                d.notificationReadStatus = $("#notificationReadStatus").val();
                d.assignUser = $("#assignUser").val();
            },

        },
        "columns": [
            {"data": "index"},
            {"data": "claimNo"},
            {"data": "vehicleNo"},
            {"data": "assignUserId"},
            {"data": "notifyDate"},
            {"data": "readStatus"},
            {"data": "mobileReadStatus"},
        ]
    });

    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });
    $('#claimNoDiv').hide();
    $("#jobNumberDiv").hide();
    $("#inspectionTypeDiv").hide();
    // fileredBy();
});

// function fileredBy() {
//     // var value = $('#txtFilteredBy').val();
//     // if ('0' == value) {
//     // table.column(3).visible(false);
//     // table.column(5).visible(false);
//     // table.column(8).visible(false);
//     // table.column(9).visible(false);
//     // $('#claimNoDiv').hide();
//     // $("#jobNumberDiv").hide();
//     // $("#inspectionTypeDiv").hide();
//     // }
//     // else {
//     //     table.column(3).visible(true);
//     //     table.column(5).visible(true);
//     //     table.column(8).visible(true);
//     //     table.column(9).visible(true);
//     //     $('#claimNoDiv').show();
//     //     $("#jobNumberDiv").show();
//     //     $("#inspectionTypeDiv").show();
//     // }
//     // search();
// }

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
