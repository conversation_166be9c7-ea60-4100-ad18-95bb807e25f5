/**
 * Created by a<PERSON><PERSON> on 5/2/18.
 */


$(document).ready(function () {

    // $("#offerType,#cityId,#assessorId,#InspectionType,#inspectionReasonId,#rejectReasonId,#reassigningId").chosen({
    //     no_results_text: "No results found!",
    //     width: "100%"
    // });


});


function addValidation(id1, id2, formId) {

    if (id1 != '') {
        $('#' + formId).data('formValidation').enableFieldValidators(id1, true);
        $("#" + id1).prop('disabled', false);
    }

    if (id2 != '') {
        $('#' + formId).data('formValidation').enableFieldValidators(id2, true);
        $("#" + id2).prop('disabled', false);
    }


}

function removeValidation(id1, id2, formId) {

    if (id1 != '') {
        $('#' + formId).data('formValidation').enableFieldValidators(id1, false);
        $("#" + id1).prop('disabled', true);
    }

    if (id2 != '') {
        $('#' + formId).data('formValidation').enableFieldValidators(id2, false);
        $("#" + id2).prop('disabled', true);
    }


}








