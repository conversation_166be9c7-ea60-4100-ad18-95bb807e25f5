/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewClaimDetails(txnNo, claimNo) {
    showLoader();
    $("#P_N_CLIM_NO").val(claimNo);
    document.getElementById('frmForm').action = contextPath + "/DashboardController/viewSuperDashboard";
    document.getElementById('frmForm').submit();
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;
    var url=contextPath + "/DashboardController/claimList";


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 14}, {"orderable": false, "targets": 11}, {"orderable": false, "targets": 12}, {"orderable": false, "targets": 13}, {"orderable": false, "targets": 17}, {"orderable": false, "targets": 18}],
        responsive: true,
        searching: false,

        //  "scrollY":        "50vh",
        //  "scrollCollapse": true,
        // "scrollX": true,
        "order": [[0, "desc"]],

        /*"ajax": {
            "url": contextPath+"/CallCenterController",
            "type": "GET"
        },*/

        "ajax": {
            "url":url,
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtLocation = $("#txtLocation").val();
                d.txtV_status = $("#txtV_status").val();
                d.txtFileStatus = $("#txtFileStatus").val();
                d.txtLiabilityStatus = $("#txtLiabilityStatus").val();
            }
        },
        "columns": [
            {"data": "txnId"},
            {
                "data": "index", "render": function (data, type, obj, meta) {


                    if (obj.fileStore == 'Y') {
                        data = data + " <span class='fa fa-file text-info'  style='padding-left: 5px;'  ></span>";
                    }
                    return data;
                }
            },
            {
                "data": "claimNo","render":function (data, type, obj, meta) {


                    if (obj.closeStatus == 'CLOSE') {
                        return data + " <span class='fa fa-close text-danger'  style='padding-left: 5px;'  ></span>";
                    } else if (obj.closeStatus == 'REOPEN') {
                        return data + " <span class='fa fa-repeat text-warning'  style='padding-left: 5px;'  ></span>";
                    }
                    return data;
                }
            },
            {"data": "vehicleNo"},
            {"data": "policyNumberValue"},
            {
                "data": "claimStatusDesc","className":"text-center", "render": function (data, type, obj, meta) {
                    return data;
                }
            },
            {"data": "accidentDate"},
            {
                "data": "assignUser", "className": "text-left", "render": function (data, type, obj, meta) {
                    if (obj.assignUser != '') {
                        data = "<span class='fa fa-user text-success font-weight-bold' title='File Assign User' style='padding-right: 5px;'  ></span>" + obj.assignUser;
                    } else {
                        data = "<span class='fa fa-user text-danger font-weight-bold' title='' style='padding-right: 5px;'  ></span>" + obj.assignUser;
                    }
                    return data;
                }
            },
            {"data": "assignDateTime"},
            {"data": "liabilityAssignUser"},
            {"data": "liabilityAssignDatetime"},
            {"data": "intLiabilityAssignUser"},
            {"data": "intLiabilityAssignDatetime"},
            {
                "data": "documentStatus","className":"text-center", "render": function (data, type, obj, meta) {


                    if (obj.documentStatus == 'Y') {
                        data = "<span class='fa fa-check text-success font-weight-bold' title='Document Complete' style='padding-left: 5px;'  ></span>";
                    } else {
                        data = "<span class='fa fa-times text-warning font-weight-bold' title='Document Pending' style='padding-left: 5px;'  ></span>";
                    }
                    return data;
                }
            },
            {
                "data": "documentCheck","className":"text-center", "render": function (data, type, obj, meta) {


                    if (obj.documentCheck == 'Y') {
                        data = "<span class='fa fa-check text-success font-weight-bold' title='Document Complete' style='padding-left: 5px;'  ></span>";
                    } else {
                        data = "<span class='fa fa-times text-warning font-weight-bold' title='Document Pending' style='padding-left: 5px;'  ></span>";
                    }
                    return data;
                }
            },
            {
                "data": "liabilityStatus","className":"text-center", "render": function (data, type, obj, meta) {


                    if (obj.liabilityStatus == 'A') {
                        data = "<span class='fa fa-check text-success font-weight-bold' title='Document Complete' style='padding-left: 5px;'  ></span>";
                    } else {
                        data = "<span class='fa fa-times text-warning font-weight-bold' title='Document Pending' style='padding-left: 5px;'  ></span>";
                    }
                    return data;
                }
            },
            {
                "data": "finalizeStatus","className":"text-center", "render": function (data, type, obj, meta) {

                    if (obj.closeStatus == 'CLOSE') {
                        data = "<span>Closed</span>";
                    } else if (obj.closeStatus == 'REOPEN') {
                        data = "<span>Reopened</span>";
                    } else if (obj.closeStatus == 'SETTLE') {
                        data = "<span>Settled</span>";
                    } else if (obj.closeStatus == 'SETTLE_PENDING') {
                        data = "<span>Settle Pending</span>"
                    } else {
                        data = "<span>Pending</span>"
                    }
                    return data;
                }
            },
            {"data": "acr"},
            {"data": "presentReverseAmount"},
            {
                "data": "partialLoss","className":"text-center", "render": function (data, type, obj, meta) {

                    if (obj.partialLoss == '2') {
                        return "<span class='fa fa-car text-danger' title='Total Loss' style='padding-left: 5px;'  ></span>";
                    } else if (obj.partialLoss == '1') {
                        return "<span class='fa fa-car text-dark' title='Partial loss' style='padding-left: 5px;'  ></span>";
                    }
                    return '';

                }
            },
            {
                "data": "claimNo", "render": function (data, type, obj, meta) {

                    data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.txnId + "," + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                    return data;
                }
            }
        ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {

            if (obj.claimStatus == "1") {//DR
                validateRow(nRow, obj, 'badge-light');
            } else if (obj.claimStatus == "2") {//FW
                validateRow(nRow, obj, 'badge-secondary');
            } else if (obj.claimStatus == "3") {//AS
                validateRow(nRow, obj, 'badge-success');
            } else if (obj.claimStatus == "32") {//RE
                validateRow(nRow, obj, 'badge-danger');
            } else if (obj.claimStatus == "30") {//AS PE
                validateRow(nRow, obj, 'badge-warning');
            } else if (obj.claimStatus == "31") {//DRAFT & ASSIGN
                validateRow(nRow, obj, 'badge-dark');
            }else if (obj.v_status == "30") {//AS
                validateRow(nRow, obj, 'badge-secondary');
            } else {
                validateRow(nRow, obj, '');
                // $(nRow).addClass('badge-danger');
            }

        }
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function validateRow(nRow, obj, className) {
    console.log('validate');
    if (obj.priority == 'HIGH') {
        $(nRow).addClass('badge-priority');
    } else {
        $(nRow).addClass(className);
    }
}

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
