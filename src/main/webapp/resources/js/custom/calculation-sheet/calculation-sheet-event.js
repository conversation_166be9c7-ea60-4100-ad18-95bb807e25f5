//Onchange

$(document).ready(function () {

    $("#calSheetType").change(function () {
        showHideSupOrderIdField($(this).val());
        changeHeader($(this).val());
        calculationTypeFormValidateCode();
        var selePayeeType = 9;
        var itemIndex = 1;
        var uniqueTableName = 3000;
        loadBranchList(itemIndex, uniqueTableName, false);
        $("#supplierDiv").hide();
        $('.payeeDescEvent-1-3000').html("").trigger("chosen:updated");// when change calculation type Reset on Payee Name
        if ($("#calSheetType").val() == 3 || $("#calSheetType").val() == 8) {
            loadPayeeTypeList(3, itemIndex, uniqueTableName, false, '');
            $("#supplierDiv").show();
        } else {
            loadPayeeTypeList(0, itemIndex, uniqueTableName, false, '');
        }
    });

    $("#supplierOrderId").change(function () {
        var selePayeeType = 9;
        var itemIndex = 1;
        var uniqueTableName = 3000;
        loadPayeeList(selePayeeType, itemIndex, uniqueTableName, false, '', $('#payeeName').val(), $('#P_CLAIM_NO').val());
        calculationTypeFormValidateCode();
    });

    $("#lossType,#causeOfLoss,#paymentType").change(function () {
        calculationTypeFormValidateCode();
    });


    ///
    if ('Y' == IS_EX_GRATIA) {
        $('#paymentType > option').each(function () {
            if (3 == $(this).val()) {
                $(this).prop('disabled', false);
                $(this).prop('selected', true);
            } else {
                $(this).prop('disabled', true);
            }
        });
    } else {
        $('#paymentType > option').each(function () {
            if (3 == $(this).val()) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        });
    }


    showHideSupOrderIdField(CAL_SHEET_TYPE);
    changeHeader(CAL_SHEET_TYPE);
    $(".load").hide();

});


function formatCurrency(amount) {
    return amount.toLocaleString('en', {minimumFractionDigits: 2});
}

function formatCurrencyWithoutGrouping(amount) {
    return amount.toLocaleString('en', {minimumFractionDigits: 2, useGrouping: false});
}