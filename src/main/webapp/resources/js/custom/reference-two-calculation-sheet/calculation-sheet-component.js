var indexReplacement = REPLACEMENT_INDEX;
var indexLabour = LABOUR_INDEX;

var oaList = OA_LIST;
var nbtRateList = NBT_RATE_LIST;
var vatRateList = VAT_RATE_LIST;
var REPLACEMENT_UNIQUE_TABLE_NAME = 1000;
var LABOUR_UNIQUE_TABLE_NAME = 2000;

const LABOUR_TABLE_NAME = 'labour';
const REPLACEMENT_TABLE_NAME = 'replacement';

$(document).ready(function () {
    $("#cmdReplacement").click(function () {
        var index = (++indexReplacement);
        var dtoName = 'claimCalculationSheetDetailReplacementDtos';
        addRow(dtoName, REPLACEMENT_UNIQUE_TABLE_NAME, "tblBody1", index);
        registerEvent(REPLACEMENT_UNIQUE_TABLE_NAME, index);
        totalReplacementItem++;


    });
    $("#cmdLabour").click(function () {
        var index = (++indexLabour);
        var dtoName = 'claimCalculationSheetDetailLabourDtos';
        addRow(dtoName, LABOUR_UNIQUE_TABLE_NAME, "tblBody2", index);
        registerEvent(LABOUR_UNIQUE_TABLE_NAME, index);
        // indexLabour++
        totalLabourItem++;
    });

    $('#underInsuranceRate').on("focusout", function () {
        //   calculateUnderInsurancePenaltyAmount();
        calculatePayableAmount();
    });
    $('#baldTyreRate').on("focusout", function () {
        // calculateBaldTyrePenaltyAmount();
        calculatePayableAmount();
    });

    $('#specialVatAmount,#specialNbtAmount,#specialDeductions').on("focusout", function () {
        calculatePayableAmount();
    });

    $('#adjustVatAmount,#adjustNbtAmount').on("focusout", function () {
        calculatePayableAmount();
    });

    $('#isAdjustVatAmount,#isAdjustNbtAmount').change(function () {
        calculatePayableAmount();
    });
});

function disableBillChecked() {
    if (LOGGED_USER == SPECIAL_TEAM_ASSIGN_USER && '63' == CAL_SHEET_STATUS) {
        $('.billchecked').attr('disabled', false);
    } else {
        $('.billchecked').attr('disabled', true);
    }
}

function billCheck(id) {
    var isChecked;
    if ($('.' + id).prop('checked')) {
        isChecked = 'Y'
    } else {
        isChecked = 'N'
    }

    var dataObj = {
        id: id,
        isChecked: isChecked
    };
    var calculatedAmount = 0;
    var URL = contextPath + "/ReferenceTwoCalculationSheetController/updateBillCheck";
    $.ajax({
        url: URL,
        type: 'POST',
        data: dataObj,
        success: function (result) {
            if (null != result) {
                const obj = JSON.parse(result);
                if (obj.code == 'SUCCESS') {
                    notify(obj.msg, "success");
                } else {
                    notify(obj.msg, "danger");
                }
            }


        }
    });
}

function addRow(dtoName, uniqueTableName, tableBodyId, index) {
    $("#" + tableBodyId).append(getRowItem(index, dtoName, uniqueTableName));
    disableBillChecked();
}

function deleteRow(uniqueTableName, index) {
    $("#tr-" + uniqueTableName + "-" + index).remove();
    if (uniqueTableName == 3000) {
        calculateTotalPayeeAmount();
        totalPayeeItem--;
    } else {
        calculate(uniqueTableName);
    }

    if (uniqueTableName == REPLACEMENT_UNIQUE_TABLE_NAME) {
        totalReplacementItem--;
    } else if (uniqueTableName == LABOUR_UNIQUE_TABLE_NAME) {
        totalLabourItem--;
    }

}

function getRowItem(itemIndex, dtoName, uniqueTableName) {

    var row = "<tr id='tr-" + uniqueTableName + "-" + itemIndex + "'>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'itemNo', 'item-no-class indexEvent-' + itemIndex + '-' + uniqueTableName + '', 'Item No', (itemIndex), true) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'approvedAmount', 'text-right approvedAmountEvent-' + itemIndex + '-' + uniqueTableName + '', 'Approved Amount', '', false) + "</td>" +
        "<td class=\"parentDiv\">" + createSelectBox(itemIndex, dtoName, 'removeVatRate', 'form-control-sm removeVatRateEvent-' + itemIndex + '-' + uniqueTableName + '', vatRateList, '', false) + "</td>" +
        "<td class=\"parentDiv\">" + createCheckBox(itemIndex, dtoName, 'isRemoveVat', (dtoName == 'claimCalculationSheetDetailReplacementDtos' ? 'isReplacementRemoveVat ' : 'isLabourRemoveVat ') + 'align-middle ' + 'isRemoveVatEvent-' + itemIndex + '-' + uniqueTableName, false, 'Y', false) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'amountWithoutVat', 'text-right amountWithoutVatEvent-' + itemIndex + '-' + uniqueTableName + '', 'Amount Without VAT', '', true) + "</td>" +
        "<td class=\"parentDiv\">" + createSelectBox(itemIndex, dtoName, 'oa', 'form-control-sm oaEvent-' + itemIndex + '-' + uniqueTableName + '', oaList, '', false) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'oaAmount', 'text-right oaAmountEvent-' + itemIndex + '-' + uniqueTableName + '', 'O/A Amount', '', true) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'totalAmountAfterOa', 'text-right totalAmountAfterOaEvent-' + itemIndex + '-' + uniqueTableName + '', 'Total Amount', '', true) + "</td>" +
        "<td class=\"parentDiv\">" + createSelectBox(itemIndex, dtoName, 'nbtRate', 'form-control-sm nbtRateEvent-' + itemIndex + '-' + uniqueTableName + '', nbtRateList, '', false) + "</td>" +
        "<td class=\"parentDiv\">" + createCheckBox(itemIndex, dtoName, 'isAddNbt', (dtoName == 'claimCalculationSheetDetailReplacementDtos' ? 'isReplacementAddNbt ' : 'isLabourAddNbt ') + 'align-middle ' + 'isAddNbtEvent-' + itemIndex + '-' + uniqueTableName, false, 'Y', false) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'nbtAmount', 'text-right nbtAmountEvent-' + itemIndex + '-' + uniqueTableName + '', 'NBT Amount', '', true) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'amountWithNbt', 'text-right amountWithNbtEvent-' + itemIndex + '-' + uniqueTableName + '', 'Amount With NBT', '', true) + "</td>" +
        "<td class=\"parentDiv\">" + createSelectBox(itemIndex, dtoName, 'vatRate', 'form-control-sm vatRateEvent-' + itemIndex + '-' + uniqueTableName + '', vatRateList, '', false) + "</td>" +
        "<td class=\"parentDiv\">" + createRadioBox(itemIndex, dtoName, 'addVatType', (dtoName == 'claimCalculationSheetDetailReplacementDtos' ? 'replacementAddVatWithoutNbt ' : 'labourAddVatWithoutNbt ') + ' addVatTypeEvent-' + itemIndex + '-' + uniqueTableName, true, 'WITHOUTNBT', false) + "</td>" +
        "<td class=\"parentDiv\">" + createRadioBox(itemIndex, dtoName, 'addVatType', (dtoName == 'claimCalculationSheetDetailReplacementDtos' ? 'replacementAddVatWithNbt ' : 'labourAddVatWithNbt ') + ' addVatTypeEvent-' + itemIndex + '-' + uniqueTableName, false, 'WITHNBT', false) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'vatAmount', 'text-right vatAmountEvent-' + itemIndex + '-' + uniqueTableName + '', 'VAT Amount', '', true) + "</td>" +
        "<td class=\"parentDiv\">" + createTextBox(itemIndex, dtoName, 'totalAmount', 'text-right totalAmountEvent-' + itemIndex + '-' + uniqueTableName + '', 'Total Amount', '', true) + "</td>" +
        "<td class=\"parentDiv\">" + createCheckBox(itemIndex, dtoName, 'billChecked', ' billchecked', false, 'Y', false) + "</td>" +
        "<td class=\"parentDiv\">" + createButton(itemIndex, dtoName, 'removeButton', '', false, uniqueTableName) + "</td>" +
        "</tr>";
    return row;
}

function registerEvent(uniqueTableName, index) {

    // keyCode
    // left = 37
    // up = 38
    // right = 39
    // down = 40

    var classId = ".indexEvent-" + index + '-' + uniqueTableName;
    $(classId).keydown(function (e) {
        if (e.keyCode == 38) {
            $(".indexEvent-" + (index - 1) + '-' + uniqueTableName).focus();
        }
        // if (e.keyCode == 39) {
        //     $(".approvedAmountEvent-" + (index) + '-' + uniqueTableName).focus();
        // }
        if (e.keyCode == 40) {
            $(".indexEvent-" + (index + 1) + '-' + uniqueTableName).focus();
        }
    });


    classId = ".approvedAmountEvent-" + index + '-' + uniqueTableName;
    //approveKeyUp-1-1000
    $(classId).on("focusout", function () {
        calculate(uniqueTableName);
    });

    $(classId).keydown(function (e) {
        // if (e.keyCode == 37) {
        //     $(".indexEvent-" + (index) + '-' + uniqueTableName).focus();
        // }
        if (e.keyCode == 38) {
            $(".approvedAmountEvent-" + (index - 1) + '-' + uniqueTableName).focus();
        }
        // if (e.keyCode == 39) {
        //     $(".removeVatRateEvent-" + (index) + '-' + uniqueTableName).focus();
        // }
        if (e.keyCode == 40) {
            $(".approvedAmountEvent-" + (index + 1) + '-' + uniqueTableName).focus();
        }
    });

    classId = ".removeVatRateEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        calculate(uniqueTableName);
    });

    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".approvedAmountEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".isRemoveVatEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    // });


    classId = ".isRemoveVatEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        calculate(uniqueTableName);
    });

    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".removeVatRateEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 38) {
    //         $(".isRemoveVatEvent-" + (index - 1) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".amountWithoutVatEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 40) {
    //         $(".isRemoveVatEvent-" + (index + 1) + '-' + uniqueTableName).focus();
    //     }
    // });

    // classId = ".amountWithoutVatEvent-" + index + '-' + uniqueTableName;
    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".isRemoveVatEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 38) {
    //         $(".amountWithoutVatEvent-" + (index - 1) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".oaEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 40) {
    //         $(".amountWithoutVatEvent-" + (index + 1) + '-' + uniqueTableName).focus();
    //     }
    // });


    classId = ".oaEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        calculate(uniqueTableName);
    });

    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".amountWithoutVatEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".oaAmountEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    // });

    // classId = ".oaAmountEvent-" + index + '-' + uniqueTableName;
    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".amountWithoutVatEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 38) {
    //         $(".oaAmountEvent-" + (index - 1) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".totalAmountAfterOaEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 40) {
    //         $(".oaAmountEvent-" + (index + 1) + '-' + uniqueTableName).focus();
    //     }
    // });


    // classId = ".totalAmountAfterOaEvent-" + index + '-' + uniqueTableName;
    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".oaAmountEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 38) {
    //         $(".totalAmountAfterOaEvent-" + (index - 1) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".nbtRateEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 40) {
    //         $(".totalAmountAfterOaEvent-" + (index + 1) + '-' + uniqueTableName).focus();
    //     }
    // });


    classId = ".nbtRateEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        calculate(uniqueTableName);
    });

    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".totalAmountAfterOaEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".isAddNbtEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    // });

    classId = ".isAddNbtEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        calculate(uniqueTableName);
    });

    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".nbtRateEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 38) {
    //         $(".isAddNbtEvent-" + (index - 1) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".nbtAmountEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 40) {
    //         $(".isAddNbtEvent-" + (index + 1) + '-' + uniqueTableName).focus();
    //     }
    // });
    //
    // classId = ".nbtAmountEvent-" + index + '-' + uniqueTableName;
    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".isAddNbtEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 38) {
    //         $(".nbtAmountEvent-" + (index - 1) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".amountWithNbtEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 40) {
    //         $(".nbtAmountEvent-" + (index + 1) + '-' + uniqueTableName).focus();
    //     }
    // });


    // classId = ".amountWithNbtEvent-" + index + '-' + uniqueTableName;
    // $(classId).keydown(function (e) {
    //     if (e.keyCode == 37) {
    //         $(".nbtAmountEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 38) {
    //         $(".amountWithNbtEvent-" + (index - 1) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 39) {
    //         $(".vatRateEvent-" + (index) + '-' + uniqueTableName).focus();
    //     }
    //     if (e.keyCode == 40) {
    //         $(".amountWithNbtEvent-" + (index + 1) + '-' + uniqueTableName).focus();
    //     }
    // });


    classId = ".vatRateEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        calculate(uniqueTableName);
    });

    // $(classId).keydown(function (e) {
    //         if (e.keyCode == 37) {
    //             $(".amountWithNbtEvent-" + (index) + '-' + uniqueTableName).focus();
    //         }
    //         if (e.keyCode == 39) {
    //             $(".addVatWithoutNbtEvent-" + (index) + '-' + uniqueTableName).focus();
    //         }
    //     });

    classId = ".addVatTypeEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        calculate(uniqueTableName);
    });
}

function calculateReplacement() {
    var $form = $('#detailform');
    var URL = contextPath + "/ReferenceTwoCalculationSheetController/calculateReplacement";
    $.ajax({
        url: URL,
        type: 'POST',
        async: false,
        data: $form.serialize(),
        success: function (data) {
            var obj = JSON.parse(data);
            var list = obj.claimCalculationSheetDetailReplacementDtos;
            $.each(list, function () {
                // $(".nbtAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.nbtAmount, 2, "", "."));
                // $(".vatAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.vatAmount, 2, "", "."));
                // $(".oaAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.oaAmount, 2, "", "."));
                // $(".totalAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.totalAmount, 2, "", "."));

                $(".amountWithoutVatEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.amountWithoutVat, 2, "", "."));
                $(".oaAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.oaAmount, 2, "", "."));
                $(".totalAmountAfterOaEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.totalAmountAfterOa, 2, "", "."));
                $(".nbtAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.nbtAmount, 2, "", "."));
                $(".amountWithNbtEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.amountWithNbt, 2, "", "."));
                $(".vatAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.vatAmount, 2, "", "."));
                $(".totalAmountEvent-" + this.itemNo + '-' + REPLACEMENT_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.totalAmount, 2, "", "."));
            });

            $("#replacementTotalApproveAmount").html(accounting.formatNumber(obj.approvedTotalAmount, 2, "", "."));
            $("#replacementNbtAmount").html(accounting.formatNumber(obj.nbtTotalAmount, 2, "", "."));
            $("#replacementVatAmount").html(accounting.formatNumber(obj.vatTotalAmount, 2, "", "."));
            $("#replacementOaAmount").html(accounting.formatNumber(obj.oaTotalAmount, 2, "", "."));
            $("#replacementTotalAmount").html(accounting.formatNumber(obj.totalAmount, 2, "", "."));

            $("#replacementTotalAmountWithoutVat").html(accounting.formatNumber(obj.totalAmountWithoutVat, 2, "", "."));
            $("#replacementTotalAmountAfterOa").html(accounting.formatNumber(obj.totalAmountAfterOa, 2, "", "."));
            $("#replacementTotalAmountWithNbt").html(accounting.formatNumber(obj.totalAmountWithNbt, 2, "", "."));

            $("#replacementNbt").val(accounting.formatNumber(obj.nbtTotalAmount, 2, "", "."));
            $("#replacementVat").val(accounting.formatNumber(obj.vatTotalAmount, 2, "", "."));
            $("#replacementOa").val(accounting.formatNumber(obj.oaTotalAmount, 2, "", "."));

            $("#partsLabel").html(accounting.formatNumber(obj.approvedTotalAmount, 2, ",", "."));
            $('#parts').val(accounting.formatNumber(obj.totalAmount, 2, "", "."));
            calculatePayableAmount();
        }
    });
}

function calculateLabour() {
    var $form = $('#detailform');
    var URL = contextPath + "/ReferenceTwoCalculationSheetController/calculateLabour";
    $.ajax({
        url: URL,
        type: 'POST',
        async: false,
        data: $form.serialize(),
        success: function (data) {
            var obj = JSON.parse(data);
            var list = obj.claimCalculationSheetDetailLabourDtos;
            $.each(list, function () {
                $(".amountWithoutVatEvent-" + this.itemNo + '-' + LABOUR_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.amountWithoutVat, 2, "", "."));
                $(".oaAmountEvent-" + this.itemNo + '-' + LABOUR_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.oaAmount, 2, "", "."));
                $(".totalAmountAfterOaEvent-" + this.itemNo + '-' + LABOUR_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.totalAmountAfterOa, 2, "", "."));
                $(".nbtAmountEvent-" + this.itemNo + '-' + LABOUR_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.nbtAmount, 2, "", "."));
                $(".amountWithNbtEvent-" + this.itemNo + '-' + LABOUR_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.amountWithNbt, 2, "", "."));
                $(".vatAmountEvent-" + this.itemNo + '-' + LABOUR_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.vatAmount, 2, "", "."));
                $(".totalAmountEvent-" + this.itemNo + '-' + LABOUR_UNIQUE_TABLE_NAME).val(accounting.formatNumber(this.totalAmount, 2, "", "."));
            });

            $("#labourTotalApproveAmount").html(accounting.formatNumber(obj.approvedTotalAmount, 2, "", "."));
            $("#labourNbtAmount").html(accounting.formatNumber(obj.nbtTotalAmount, 2, "", "."));
            $("#labourVatAmount").html(accounting.formatNumber(obj.vatTotalAmount, 2, "", "."));
            $("#labourOaAmount").html(accounting.formatNumber(obj.oaTotalAmount, 2, "", "."));
            $("#labourTotalAmount").html(accounting.formatNumber(obj.totalAmount, 2, "", "."));

            $("#labourTotalAmountWithoutVat").html(accounting.formatNumber(obj.totalAmountWithoutVat, 2, "", "."));
            $("#labourTotalAmountAfterOa").html(accounting.formatNumber(obj.totalAmountAfterOa, 2, "", "."));
            $("#labourTotalAmountWithNbt").html(accounting.formatNumber(obj.totalAmountWithNbt, 2, "", "."));

            $("#labourNbt").val(accounting.formatNumber(obj.nbtTotalAmount, 2, "", "."));
            $("#labourVat").val(accounting.formatNumber(obj.vatTotalAmount, 2, "", "."));
            $("#labourOa").val(accounting.formatNumber(obj.oaTotalAmount, 2, "", "."));

            $("#labourLabel").html(accounting.formatNumber(obj.approvedTotalAmount, 2, ",", "."));
            $('#labour').val(accounting.formatNumber(obj.totalAmount, 2, "", "."));
            calculatePayableAmount();


        }
    });
}

function calculate(uniqueTableName) {
    if (uniqueTableName == REPLACEMENT_UNIQUE_TABLE_NAME) {
        calculateReplacement();
    } else if (uniqueTableName == LABOUR_UNIQUE_TABLE_NAME) {
        calculateLabour();
    }
    isPayItemFormValidate(uniqueTableName);
}

function calculationTypeFormValidateCode() {

    var result = 0;
    var calSheetType = $("#calSheetType").val();
    var lossType = $("#lossType").val();
    var causeOfLoss = $("#causeOfLoss").val();
    var paymentType = $("#paymentType").val();
    var supplierOrderId = $("#supplierOrderId").val();

    $("#calSheetType").removeClass("is-valid");
    $("#lossType").removeClass("is-valid");
    $("#causeOfLoss").removeClass("is-valid");
    $("#paymentType").removeClass("is-valid");
    $("#supplierOrderId").removeClass("is-valid");

    $("#calSheetType").removeClass("is-invalid");
    $("#lossType").removeClass("is-invalid");
    $("#causeOfLoss").removeClass("is-invalid");
    $("#paymentType").removeClass("is-invalid");
    $("#supplierOrderId").removeClass("is-invalid");


    $("#calSheetType").addClass("is-valid");
    $("#lossType").addClass("is-valid");
    $("#causeOfLoss").addClass("is-valid");
    $("#paymentType").addClass("is-valid");
    $("#supplierOrderId").addClass("is-valid");


    if (calSheetType == 0) {
        $("#calSheetType").addClass("is-invalid");
        result = 1;
    }
    if (calSheetType == 3 && supplierOrderId == 0) {
        $("#supplierOrderId").addClass("is-invalid");
        result = 2;
    }

    if (lossType == 0) {
        $("#lossType").addClass("is-invalid");
        result = 3;
    }
    if (causeOfLoss == 0) {
        $("#causeOfLoss").addClass("is-invalid");
        result = 4;
    }
    if (paymentType == 0) {
        $("#paymentType").addClass("is-invalid");
        result = 5;
    }
    return result;

}

function isPayItemFormValidate(uniqueTableName) {
    var result = true;
    var count = 0;
    if (uniqueTableName == REPLACEMENT_UNIQUE_TABLE_NAME) {
        count = indexReplacement;
    } else if (uniqueTableName == LABOUR_UNIQUE_TABLE_NAME) {
        count = indexLabour;
    }
    for (index = 1; index <= count; index++) {
        var classId = ".approvedAmountEvent-" + index + '-' + uniqueTableName;
        var value = $(classId).val();
        if (value !== undefined) {
            $(classId).removeClass("is-valid");
            $(classId).removeClass("is-invalid");
            if ($.isNumeric(value) == false) {
                $(classId).addClass("is-invalid");
                result = false;
            } else {
                $(classId).addClass("is-valid");
            }
        }

    }
    return result;
}


function calculatePayableAmount() {

    var totalReplacement = parseFloat(isNaN($('#parts').val()) || $('#parts').val() == '' ? 0.00 : $('#parts').val());
    var totalLabour = parseFloat(isNaN($('#labour').val()) || $('#labour').val() == '' ? 0.00 : $('#labour').val());
    var specialVatAmount = parseFloat(isNaN($('#specialVatAmount').val()) || $('#specialVatAmount').val() == '' ? 0.00 : $('#specialVatAmount').val());
    var specialNbtAmount = parseFloat(isNaN($('#specialNbtAmount').val()) || $('#specialNbtAmount').val() == '' ? 0.00 : $('#specialNbtAmount').val());

    var labourNbt = parseFloat(isNaN($('#labourNbt').val()) || $('#labourNbt').val() == '' ? 0.00 : $('#labourNbt').val());
    var labourVat = parseFloat(isNaN($('#labourVat').val()) || $('#labourVat').val() == '' ? 0.00 : $('#labourVat').val());
    var labourOa = parseFloat(isNaN($('#labourOa').val()) || $('#labourOa').val() == '' ? 0.00 : $('#labourOa').val());

    var replacementNbt = parseFloat(isNaN($('#replacementNbt').val()) || $('#replacementNbt').val() == '' ? 0.00 : $('#replacementNbt').val());
    var replacementVat = parseFloat(isNaN($('#replacementVat').val()) || $('#replacementVat').val() == '' ? 0.00 : $('#replacementVat').val());
    var replacementOa = parseFloat(isNaN($('#replacementOa').val()) || $('#replacementOa').val() == '' ? 0.00 : $('#replacementOa').val());

    var totalVatAmount = parseFloat(labourVat + replacementVat);
    var totalNbtAmount = parseFloat(labourNbt + replacementNbt);

    $("#totalVatAmount").html(accounting.formatNumber((totalVatAmount), 2, ",", "."));
    $("#totalNbtAmount").html(accounting.formatNumber((totalNbtAmount), 2, ",", "."));
    $("#totalOaAmount").html(accounting.formatNumber((labourOa + replacementOa), 2, ",", "."));

    //Calculate Total Amount
    var totalAmount = parseFloat(totalLabour + totalReplacement + specialVatAmount + specialNbtAmount);
    if ($('#isAdjustVatAmount').prop('checked')) {
        var adjustVatAmount = parseFloat(isNaN($('#adjustVatAmount').val()) || $('#adjustVatAmount').val() == '' ? 0.00 : $('#adjustVatAmount').val());
        totalAmount = parseFloat((totalAmount - totalVatAmount) + adjustVatAmount);
    }

    if ($('#isAdjustNbtAmount').prop('checked')) {
        var adjustNbtAmount = parseFloat(isNaN($('#adjustNbtAmount').val()) || $('#adjustNbtAmount').val() == '' ? 0.00 : $('#adjustNbtAmount').val());
        totalAmount = parseFloat((totalAmount - totalNbtAmount) + adjustNbtAmount);
    } else {

    }


    var formattedTotal = formatCurrency(totalAmount);

    $('#totalLabourAndParts').val(totalAmount);
    $('#totalLabourAndPartsLabel').text(formattedTotal);

    calculateUnderInsurancePenaltyAmount();
    calculateBaldTyrePenaltyAmount();

    //Calculate total deduction
    var totalDeduction = parseFloat($('#underInsurance').val())
        + parseFloat($('#baldTyre').val())
        + parseFloat(isNaN($('#specialDeductions').val()) || '' == $('#specialDeductions').val() ? 0.00 : $('#specialDeductions').val())
        + parseFloat(isNaN($('#policyExcess').val()) || '' == $('#policyExcess').val() ? 0.00 : $('#policyExcess').val());
    $('#totalDeductions').val(totalDeduction.toFixed(2));
    $('#totalDeductionsLabel').text(formatCurrency(totalDeduction));

    var totalAfterDeductions = totalAmount - totalDeduction;
    $('#totalAfterDeductions').val(totalAfterDeductions.toFixed(2));
    $('#totalAfterDeductionsLabel').text(formatCurrency(totalAfterDeductions));


    var totalPayable = parseFloat(totalAmount - totalDeduction);

    if ("1" == $("#calSheetType").val() || "5" == $("#calSheetType").val()) { //1 - Full And Final
        totalPayable = totalPayable - parseFloat(TOTAL_PAID_ADVANCE_AMOUNT_FOR_CLAIM);
    }

    var formattedTotal = formatCurrency(totalPayable);
    $('#payableAmount').val(totalPayable.toFixed(2));
    $('#payableAmountLabel').text(formattedTotal);

}


function calculateUnderInsurancePenaltyAmount() {
    var calculatedAmount = calculatePercentageAmount(isNaN($("#underInsuranceRate").val()) || '' == $("#underInsuranceRate").val() ? 0.00 : $("#underInsuranceRate").val(), $("#totalLabourAndParts").val());
    $("#underInsurance").val(calculatedAmount);
    $("#underInsuranceLabel").text(formatCurrency(calculatedAmount));
    //  calculatePayableAmount();
}

function calculateBaldTyrePenaltyAmount() {
    var calculatedAmount = calculatePercentageAmount(isNaN($("#baldTyreRate").val()) || '' == $("#baldTyreRate").val() ? 0.00 : $("#baldTyreRate").val(), $("#totalLabourAndParts").val());
    $("#baldTyre").val(calculatedAmount);
    $("#baldTyreLabel").text(formatCurrency(calculatedAmount));
    // calculatePayableAmount();
}


function calculatePercentageAmount(rate, amount) {
    var dataObj = {
        amount: amount,
        rate: rate
    };
    var calculatedAmount = 0;
    var URL = contextPath + "/ReferenceTwoCalculationSheetController/calculate?CAL_TYPE=AmountPercentage";
    $.ajax({
        url: URL,
        type: 'POST',
        async: false,
        data: dataObj,
        success: function (result) {
            calculatedAmount = result.trim();
        }
    });
    return calculatedAmount;
}

function policyExcessAdd() {
    $('#policyExcess').val(POLICY_EXCESS);
    calculatePayableAmount();
}

function policyExcessZero() {
    $('#policyExcess').val('0.00');
    calculatePayableAmount();

}

//Common Function
function changeHeader(calSheetType) {
    if (calSheetType == '2') { // 3 --> D/O
        $('#replacementLabel').text('Advance');
        $('#labourLabelVal').text('Advance');
    } else {
        $('#replacementLabel').text('Replacement');
        $('#labourLabelVal').text('Labour');
    }
}

function showHideSupOrderIdField(calSheetType) {

    if (calSheetType == '3') { // 3 --> D/O
        $('#supplierOrderIdDiv').show();
        $('#supplierDiv').show();
    } else {
        $('#supplierOrderIdDiv').hide();
        $('#supplierDiv').hide();
    }
}

function selectAllRemoveVatCheckBoxes(uniqueTableName) {

    if (uniqueTableName == REPLACEMENT_TABLE_NAME) {
        if (document.getElementById('allReplacementRemoveVat').checked) {
            $('.isReplacementRemoveVat').prop('checked', true);
        } else {
            $('.isReplacementRemoveVat').prop('checked', false);
        }
        calculate(REPLACEMENT_UNIQUE_TABLE_NAME);
    } else if (uniqueTableName == LABOUR_TABLE_NAME) {
        if (document.getElementById('allLabourRemoveVat').checked) {
            $('.isLabourRemoveVat').prop('checked', true);
        } else {
            $('.isLabourRemoveVat').prop('checked', false);
        }
        calculate(LABOUR_UNIQUE_TABLE_NAME);
    }

}

function selectAllAddNbtCheckBoxes(tableName) {

    if (tableName == REPLACEMENT_TABLE_NAME) {
        if (document.getElementById('allReplacementAddNbt').checked) {
            $('.isReplacementAddNbt').prop('checked', true);
        } else {
            $('.isReplacementAddNbt').prop('checked', false);
        }
        calculate(REPLACEMENT_UNIQUE_TABLE_NAME);
    } else if (tableName == LABOUR_TABLE_NAME) {
        if (document.getElementById('allLabourAddNbt').checked) {
            $('.isLabourAddNbt').prop('checked', true);
        } else {
            $('.isLabourAddNbt').prop('checked', false);
        }
        calculate(LABOUR_UNIQUE_TABLE_NAME);
    }

}

function selectAllAddVatWithoutNbtCheckBoxes(tableName) {

    if (tableName == REPLACEMENT_TABLE_NAME) {
        if (document.getElementById('allReplacementAddVatWithoutNbt').checked) {
            $('.replacementAddVatWithoutNbt').prop('checked', true);
            $('#allReplacementAddVatWithNbt').prop('checked', false);
        }
        calculate(REPLACEMENT_UNIQUE_TABLE_NAME);
    } else if (tableName == LABOUR_TABLE_NAME) {
        if (document.getElementById('allLabourAddVatWithoutNbt').checked) {
            $('.labourAddVatWithoutNbt').prop('checked', true);
            $('#allLabourAddVatWithNbt').prop('checked', false);
        }
        calculate(LABOUR_UNIQUE_TABLE_NAME);
    }

}

function selectAllAddVatWithNbtCheckBoxes(tableName) {

    if (tableName == REPLACEMENT_TABLE_NAME) {
        if (document.getElementById('allReplacementAddVatWithNbt').checked) {
            $('.replacementAddVatWithNbt').prop('checked', true);
            $('#allReplacementAddVatWithoutNbt').prop('checked', false);
        }
        calculate(REPLACEMENT_UNIQUE_TABLE_NAME);
    } else if (tableName == LABOUR_TABLE_NAME) {
        if (document.getElementById('allLabourAddVatWithNbt').checked) {
            $('.labourAddVatWithNbt').prop('checked', true);
            $('#allLabourAddVatWithoutNbt').prop('checked', false);
        }
        calculate(LABOUR_UNIQUE_TABLE_NAME);
    }

}



