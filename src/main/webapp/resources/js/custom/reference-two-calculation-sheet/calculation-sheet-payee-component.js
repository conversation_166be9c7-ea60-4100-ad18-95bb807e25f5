var indexPayee = 0;// REPLACEMENT_INDEX;
var PAYEE_UNIQUE_TABLE_NAME = 3000;

var payeeTypeList = PAYEE_TYPE_LIST;
var payeeDescList = "<option value='0'>Please select one</option>";
var eftList = "<option value='N'>No</option><option value='Y'>Yes</option>";
var branchList = '';
var claimNum = '';

$(document).ready(function () {
    $("#cmdPayee").click(function () {
        addPayee(false, $('#payeeName').val(), claimNum);
    });
});

function addPayee(isReadOnly, custName, claimNo) {
    var index = (++indexPayee);
    var dtoName = 'payeeDtos';
    addPayeeRow(dtoName, PAYEE_UNIQUE_TABLE_NAME, "tblBody3", index, isReadOnly);
    registerPayeeEvent(PAYEE_UNIQUE_TABLE_NAME, index, custName, claimNo);
    initChosen(index, PAYEE_UNIQUE_TABLE_NAME);
    totalPayeeItem++;
}


function addPayeeRow(dtoName, uniqueTableName, tableBodyId, index, isReadOnly) {
    $("#" + tableBodyId).append(getPayeeRowItem(index, dtoName, uniqueTableName, isReadOnly));
}

function deletePayeeRow(uniqueTableName, index) {
    $("#tr-" + uniqueTableName + "-" + index).remove();

}

function getPayeeRowItem(itemIndex, dtoName, uniqueTableName, isReadOnly) {
    var row = "<tr id='tr-" + uniqueTableName + "-" + itemIndex + "'>" +
        "<td><div class='form-group'>" + itemIndex + "</div>" + createHiddenBox(itemIndex, dtoName, 'itemNo', 'item-no-class', 'Item No', (itemIndex), true) + "</td>" +
        "<td>" + createSelectBox(itemIndex, dtoName, 'payeeId', 'form-control-sm payeeIdEvent-' + itemIndex + '-' + uniqueTableName + '', payeeTypeList, '', isReadOnly) + "</td>" +
        "<td>" + createChosenBox(itemIndex, dtoName, 'payeeDesc', 'form-control-sm  payeeDescEvent-' + itemIndex + '-' + uniqueTableName + '', payeeDescList, '', isReadOnly) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'amount', 'item-no-class text-right amountEvent-' + itemIndex + '-' + uniqueTableName + '', 'Amount', '', isReadOnly) + "</td>" +
        "<td>" + createSelectBox(itemIndex, dtoName, 'isEftPayment', 'form-control-sm isEftPaymentEvent-' + itemIndex + '-' + uniqueTableName + '', eftList, '', isReadOnly) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'accountNo', 'item-no-class accountNoEvent-' + itemIndex + '-' + uniqueTableName + '', 'Account No', '', isReadOnly) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'bankName', 'item-no-class bankNameEvent-' + itemIndex + '-' + uniqueTableName + '', 'Bank Name', '', isReadOnly) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'bankCode', 'item-no-class bankCodeEvent-' + itemIndex + '-' + uniqueTableName + '', 'Bank Code', '', isReadOnly) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'branch', 'item-no-class branchEvent-' + itemIndex + '-' + uniqueTableName + '', 'Branch', '', false) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'contactNo', 'item-no-class contactNoEvent-' + itemIndex + '-' + uniqueTableName + '', 'Contact No', '', isReadOnly) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'emailAddress', 'item-no-class emailAddressEvent-' + itemIndex + '-' + uniqueTableName + '', 'Email Address', '', isReadOnly) + "</td>" +
        "<td>" + createTextBox(itemIndex, dtoName, 'voucherNo', 'item-no-class voucherNoEvent-' + itemIndex + '-' + uniqueTableName + '', 'Voucher No', '', true) + "</td>" +
        "<td>" + createChosenBox(itemIndex, dtoName, 'branchDetailDto.branchCode', 'form-control-sm checkDispatchEvent-' + itemIndex + '-' + uniqueTableName + '', branchList, '', false) + "</td>" +
        "<td>" + createButton(itemIndex, dtoName, 'removeButton', '', isReadOnly, uniqueTableName) + "</td>" +
        "</tr>";
    return row;

}


function initChosen(itemIndex, uniqueTableName) {
    $('.payeeDescEvent-' + itemIndex + '-' + uniqueTableName).chosen({
        no_results_text: "No results found!",
        width: "200px;"
    });

    $('.checkDispatchEvent-' + itemIndex + '-' + uniqueTableName).chosen({
        no_results_text: "No results found!",
        width: "150px;"
    });
}

function loadBranchList(index, uniqueTableName, isReadonly) {
    var option = '<option value=0>Please Select</option>';
    for (var j = 0; j < branchList.length; j++) {
        option = option + '<option value=' + branchList[j].branchCode + '>' + branchList[j].branchName + '</option>'
    }
    if (option != '') {
        $('.checkDispatchEvent-' + index + '-' + uniqueTableName).html("").trigger("chosen:updated").append(option);
    }
}

function loadPayeeList(selePayeeType, itemIndex, uniqueTableName, isReadonly, selectPayeeDesc, custName, claimNo) {
    var disabledOption = '';
    if (isReadonly) {
        disabledOption = ' disabled';
    }
    var supplierOrderId = $('#supplierOrderId').val();
    $(".load").show();
    var selectClass = '.payeeDescEvent-' + itemIndex + '-' + uniqueTableName;

    $(selectClass).html("").trigger("chosen:updated");
    var URL = contextPath + "/ReferenceTwoCalculationSheetController/loadPayeeList";
    $.ajax({
        url: URL,
        type: 'POST',
        async: false,
        data: {
            payeeType: selePayeeType,
            supplierOrderId: supplierOrderId,
            isReadonly: isReadonly,
            selectPayeeDesc: selectPayeeDesc,
            custName,
            claimNo
        },
        success: function (data) {
            var obj = JSON.parse(data);
            $.each(obj, function () {
                /*if(isReadonly && selectPayeeDesc!=this.value){
                    $(selectClass).append($('<option'+disabledOption+'>').val(this.value).html(this.lable)).trigger("chosen:updated");
                }else {
                    $(selectClass).append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");
                }*/
                $(selectClass).append($('<option>').val(this.value).html(this.lable)).trigger("chosen:updated");

                $('#supplierTotalAmount').html(this.optionalValue1);
                $('#supplierOaAmount').html(this.optionalValue2);
                $('#supplierOtherDeduction').html(this.optionalValue3);
                $('#supplierPolicyExcess').html(this.optionalValue4);
                $('#supplierFinalAmount').html(this.optionalValue5);

                if (this.optionalValue7 == 'ADD') {
                    $('#vat').addClass('bg-badge-warning');
                    $('#vatLbl').html("VAT (Added)");
                    $('#supplierVat').html(this.optionalValue6 + "%");
                } else {
                    $('#vat').addClass('bg-badge-danger');
                    $('#vatLbl').html("VAT (Removed)");
                    $('#supplierVat').html(this.optionalValue6 + "%");
                }

            });
            $(".load").hide();
        }
    });
}

function loadPayeeTypeList(calSheetType, itemIndex, uniqueTableName, isReadonly, selectPayeeType) {
    var disabledOption = '';
    if (isReadonly) {
        disabledOption = ' disabled';
    }
    var selectClass = '.payeeIdEvent-' + itemIndex + '-' + uniqueTableName;
    var payeeDescEventClass = '.payeeDescEvent-' + itemIndex + '-' + uniqueTableName;
    var URL = contextPath + "/ReferenceTwoCalculationSheetController/loadPayeeTypeList";
    $.ajax({
        url: URL,
        type: 'POST',
        async: false,
        data: {calSheetType: calSheetType, isReadonly: isReadonly, selectPayeeType: selectPayeeType},
        success: function (data) {
            //   $(payeeDescEventClass).html("").trigger("chosen:updated");// when change calculation type Reset on Payee Name
            var obj = JSON.parse(data);
            $(selectClass).find('option').remove();
            $.each(obj, function () {
                /*if(isReadonly && selectPayeeType!=this.value){
                    $(selectClass).append('<option value='+this.value+' '+disabledOption+'>'+this.lable+'</option>');
                }else{
                    $(selectClass).append('<option value='+this.value+'>'+this.lable+'</option>');
                }*/
                $(selectClass).append('<option value=' + this.value + '>' + this.lable + '</option>');

            });
            $(".load").hide();

        }
    });

}

function registerPayeeEvent(uniqueTableName, index, custName, claimNo) {
    var classId = ".payeeIdEvent-" + index + '-' + uniqueTableName;
    $(classId).on("change", function () {
        payeeFormValidateCode(PAYEE_UNIQUE_TABLE_NAME);
        loadPayeeList(this.value, index, uniqueTableName, false, '', custName, claimNo);
    });

    classId = ".amountEvent-" + index + '-' + uniqueTableName;
    $(classId).on("focusout", function () {
        payeeFormValidateCode(PAYEE_UNIQUE_TABLE_NAME);
        calculateTotalPayeeAmount();
    });

    loadBranchList(index, uniqueTableName, false);
}

function loadPayeeDetails(payeeDtoJson, branchDtoJson, uniqueTableName, historyRecord, calSheetStatus, custName, claimNo) {
    var isReadonly = false;//This attribute used for html components disable
    var calSheetType = $('#calSheetType').val();
    var selectPayeeType = 0;
    var selectPayeeDesc = 0;
    var dispatchLocation = 0;
    var index = 1;
    var obj = JSON.parse(payeeDtoJson);
    if (historyRecord == 'Y'
        || calSheetStatus == '63'
        || calSheetStatus == '64'
        || calSheetStatus == '65'
        || calSheetStatus == '67'
        || calSheetStatus == '70') {
        isReadonly = true;
    }

    branchList = JSON.parse(branchDtoJson);
    claimNum = claimNo;

    for (var i = 0; i < obj.length; i++) {
        selectPayeeType = obj[i].payeeId;
        selectPayeeDesc = obj[i].payeeDesc;

        if (null != obj[i].branchDetailDto && undefined != obj[i].branchDetailDto.branchCode && obj[i].branchDetailDto.branchCode != null) {
            dispatchLocation = obj[i].branchDetailDto.branchCode;
        }

        addPayee(isReadonly, custName, claimNo);
        loadPayeeList(selectPayeeType, index, uniqueTableName, isReadonly, selectPayeeDesc, custName, claimNo);
        loadPayeeTypeList(calSheetType, index, uniqueTableName, isReadonly, selectPayeeType);
        loadBranchList(index, uniqueTableName, isReadonly);
        $('.payeeIdEvent-' + index + '-' + uniqueTableName).val(selectPayeeType);
        $('.payeeDescEvent-' + index + '-' + uniqueTableName).val(selectPayeeDesc).trigger("chosen:updated");
        $('.amountEvent-' + index + '-' + uniqueTableName).val(accounting.formatNumber(obj[i].amount, 2, "", "."));
        $('.isEftPaymentEvent-' + index + '-' + uniqueTableName).val(obj[i].isEftPayment);
        $('.accountNoEvent-' + index + '-' + uniqueTableName).val(obj[i].accountNo);
        $('.bankNameEvent-' + index + '-' + uniqueTableName).val(obj[i].bankName);
        $('.bankCodeEvent-' + index + '-' + uniqueTableName).val(obj[i].bankCode);
        $('.branchEvent-' + index + '-' + uniqueTableName).val(obj[i].branch);
        $('.contactNoEvent-' + index + '-' + uniqueTableName).val(obj[i].contactNo);
        $('.emailAddressEvent-' + index + '-' + uniqueTableName).val(obj[i].emailAddress);
        $('.voucherNoEvent-' + index + '-' + uniqueTableName).val(obj[i].voucherNo);
        $('.checkDispatchEvent-' + index + '-' + uniqueTableName).val(dispatchLocation).trigger("chosen:updated");

        $('#payeeDtos_' + index + '__payeeDesc_chosen').prop('title', $('.payeeDescEvent-' + index + '-' + uniqueTableName).text());
        // $('#payeeDtos_' + index + '__payeeDesc_chosen').prop('style', 'width: 100%;');

        index++;
    }
}


function calculateTotalPayeeAmount() {
    var uniqueTableName = PAYEE_UNIQUE_TABLE_NAME;
    var total = 0;
    for (i = 1; i <= indexPayee; i++) {
        var classId = ".amountEvent-" + i + '-' + uniqueTableName;
        var value = $(classId).val();
        if (value !== undefined) {
            total = total + parseFloat(isNaN(value) || value == '' ? 0.00 : value);
        }
    }
    var formattedTotal = formatCurrency(total);
    $('#totalPayeeAmount').text(formattedTotal);
    $('#totalPayeeAmountValue').val(total.toFixed(2));
    checkAcrAndPayable(total);

}


function payeeFormValidateCode(uniqueTableName) {
    var result = 0;
    var count = indexPayee;
    for (index = 1; index <= count; index++) {
        var classId = ".amountEvent-" + index + '-' + uniqueTableName;
        var value = $(classId).val();
        if (value !== undefined) {
            $(classId).removeClass("is-valid");
            $(classId).removeClass("is-invalid");
            if ($.isNumeric(value) == false) {
                $(classId).addClass("is-invalid");
                result = 1;//Amount Required
            } else {
                $(classId).addClass("is-valid");
            }
        }

        classId = ".payeeIdEvent-" + index + '-' + uniqueTableName;
        value = $(classId).val();
        $(classId).removeClass("is-valid");
        $(classId).removeClass("is-invalid");
        if (value !== undefined) {
            if (value == 0) {
                $(classId).addClass("is-invalid");
                result = 2;//Payee Type Required
            } else {
                $(classId).addClass("is-valid");
            }
        }

        classId = ".payeeDescEvent-" + index + '-' + uniqueTableName;
        value = $(classId).val();
        $(classId).removeClass("is-valid");
        $(classId).removeClass("is-invalid");
        if (value !== undefined) {
            if (value == 0) {
                $(classId).addClass("is-invalid").trigger("chosen:updated");
                result = 3;//Payee Desc Required
            } else {
                $(classId).addClass("is-valid").trigger("chosen:updated");
            }
        }

        classId = ".checkDispatchEvent-" + index + '-' + uniqueTableName;
        value = $(classId).val();
        $(classId).removeClass("is-valid");
        $(classId).removeClass("is-invalid");
        if (value != undefined) {
            if (value == 0) {
                $(classId).addClass("is-invalid").trigger("chosen:updated");
                result = 4;//Dispatch Location Required
            } else {
                $(classId).addClass("is-valid").trigger("chosen-updated");
            }
        }

    }
    return result;
}

function addStyle(classId) {

}


