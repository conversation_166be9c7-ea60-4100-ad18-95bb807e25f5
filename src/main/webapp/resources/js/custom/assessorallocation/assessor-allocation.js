//function initValidation(){
$(document).ready(function () {
    // $('.datepicker').datepicker();

    $("#districtId,#cityId,#assessorId,#InspectionType,#inspectionReasonId,#rejectReasonId,#reassigningId,#rteCode").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });

    $("#districtId").on('change', function (evt, params) {
        var id = $(this).val();
        $.ajax({
            url: contextPath + "/AssessorAllocationController/citylist?divisonCode=" + id

        }).success(function (data) {

            var obj = JSON.parse(data);
            $("#cityId").html("").trigger("chosen:updated");//.append($('<option>').val(0).html("Please Select"));
            $("#cityId").append($('<option>').val("0").html("Please Select One")).trigger("chosen:updated");
            for (var i = 0; i < obj.length; i++) {
                $("#cityId").append($('<option>').val(obj[i].gramaCode).html(obj[i].gramaName)).trigger("chosen:updated");
            }
            // $.ajax({
            //     url: contextPath + "/AssessorAllocationController/assessorlist?divisonCode=" + id
            // }).success(function (data) {
            //     var obj = JSON.parse(data);
            //     $("#assessorId").html("");//.append($('<option>').val(0).html("Please Select"));
            //     $("#assessorId").append($('<option>').val("").html("Please Select One")).trigger("chosen:updated");
            //     for (var i = 0; i < obj.length; i++) {
            //         $("#assessorId").append($('<option>').val(obj[i].code).html(obj[i].name)).trigger("chosen:updated");
            //     }
            //
            //
            // });


        });
    });


    $("#assessorId").on('change', function (evt, params) {
        var id = $(this).val();
        $.ajax({
            url: contextPath + "/AssessorAllocationController/assessorConatct?assessorCode=" + id

        }).success(function (data) {
            var obj = JSON.parse(data);
                $('#contactNo').val(obj);
        });
    });






});



