$(document).ready(function () {


    $('#tpForm')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {

                itemType: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                remark: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                },
                vehicleNo: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                },
                intendClaim: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                },
                lossType: {
                    validators: {
                        callback: dropDownValidation
                    }
                }

            }
        })
        .on('success.form.fv', function (e) {
            // Prevent form submission
            e.preventDefault();
            var $form = $(e.target);     // Form instance
            // Get the clicked button
            var $button = $form.data('formValidation').getSubmitButton();
            // alert($button.attr('value'));
            processThirdParty('S', '0');
        });
});

