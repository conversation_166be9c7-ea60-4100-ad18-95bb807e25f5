/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

//Discontinued with New ARI Process
/*function sendMail(id) {
    $('#N_REQ_REF_ID').val(id);
    $('#emailConform').modal('show');
}*/

function viewClaim(claimNo) {
    showLoader();
    $("#P_N_CLIM_NO").val(claimNo);
    document.getElementById('frmForm').action = contextPath + "/CallCenter/viewClaim?P_TAB_INDEX=7&TYPE=" + type;
    document.getElementById('frmForm').submit();
}

function viewClaimFile(claimNo) {
    showLoader();
    $("#P_N_CLIM_NO").val(claimNo);
    document.getElementById('frmForm').action = contextPath + "/MotorEngineerController/viewSupplyOrderCheck";
    document.getElementById('frmForm').submit();
}

function viewClaimDetails(id, status, remark) {

    $('#N_REQ_REF_ID').val(id);
    $("#dialogView").modal('show');
    if (status == 'R') {
        $('#remark').val(remark);
        $("#remark").attr("disabled", "disabled");
        $("#revokeBtn").attr("disabled", "disabled");
    }

    // $("#P_POL_N_REF_NO").val(polRefNo);
    // $("#P_N_CLIM_NO").val(claimNo);
    // document.getElementById('frmForm').action = contextPath + "/RequestAriController/viewEdit";
    // document.getElementById('frmForm').submit();
}

function viewRemark(id) {
    showLoader();
    $('#N_ARI_ID').val(id);
    document.getElementById('frmForm').action = contextPath + "/RequestAriController/remarkView";
    document.getElementById('frmForm').submit();
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    var URL = contextPath + "/RequestAriController/ariList";

    if (type == 4) {
        table = $('#demo-dt-basic').DataTable({
            "lengthMenu": [50, 100, 150, 250, 500],
            "processing": true,
            "serverSide": true,
            "columnDefs": [
                {"visible": false, "targets": 0, "orderable": false},
                {"orderable": false, "targets": 1},
                {"orderable": false, "targets": 15},
                {"orderable": false, "targets": 16},
                {"orderable": false, "targets": 17}
            ],
            responsive: true,
            searching: false,

            //  "scrollY":        "50vh",
            //  "scrollCollapse": true,
            // "scrollX": true,
            "order": [[0, "desc"]],

            /*"ajax": {
             "url": contextPath+"/CallCenterController",
             "type": "GET"
             },*/

                "ajax": {
                    "url": URL,
                    type: 'POST',
                    //  data: this.params
                    "data": function (d) {

                        d.txtClaimNumber = $("#txtClaimNumber").val();
                        d.txtVehicleNumber = $("#txtVehicleNumber").val();
                        d.txtV_status = $("#txtV_status").val();
                        d.txtV_requestUser = $("#txtV_requestedUser").val();
                        d.txtRequestDate = $("#txtRequestDate").val();

                    }
                },
                "columns": [
                    {"data": "id"},
                    {
                        "data": "index", "render": function (data, type, obj, meta) {


                            if (obj.intimationType == '2') {
                                data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                            } else if (obj.intimationType == '1') {
                                data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                            } else {
                                data = "<span style='padding-left: 5px;'  ></span>" + data;
                            }
                            // <i class='fa fa-star-o'></i>
                            if (obj.isFollowupCallDone == "Y") {
                                data += " <i class='fa fa-star' title='Follow-Up Call Done'></i>";
                            } else {
                                data += " <i class='fa fa-star-o text-mute' title='Follow-Up Call Pending'></i>";
                            }
                            return data;
                        }
                    },
                    {"data": "claimNo"},
                    {"data": "vehicleNo"},
                    {"data": "customerName"},
                    {"data": "contactNo"},
                    {"data": "accidentDate"},
                    {"data": "requestedUser"},
                    {"data": "requestedDate"},
                    {"data": "assignedAssessorCode"},
                    {"data": "assignedAssessor"},
                    {"data": "assigningDateTime"},
                    {"data": "assessorSubmittedDate"},
                    {"data": "daysFromAssignment", "className": "text-right"},
                    {
                        "data": "status", "render": function (data, type, obj, meta) {
                            if (obj.status == 'R') {
                                return "<span class='badge badge-danger'>Revoked</span>"
                            } else if (obj.status == 'C') {
                                return "<span class='badge badge-warning'>Pending</span>"
                            } else if (obj.status == 'S') {
                                return "<span class='badge badge-success'>Submitted</span>"
                            } else if (obj.status == 'PR') {
                                return "<span class='badge badge-success'>Partially Reviewed</span>"
                            } else if (obj.status == 'FR') {
                                return "<span class='badge badge-success'>Fully Reviewed</span>"
                            } else {
                                return "<span class='badge badge-secondary'>Hold</span>"
                            }


                        }
                    },
                    {
                        "data": "claimNo", "render": function (data, type, obj, meta) {
                            if (obj.status == 'P' || obj.status == 'C' || obj.status == 'U') {
                                data = "<button class='btn-primary btn' type='button' title='Revoke' onclick='viewClaimDetails(" + obj.id + ",\"" + obj.status + "\",\"" + obj.remark + "\" )' ><i class='fa fa-close'></i></button>";
                            } else {
                                data = "<button class='btn-primary btn' type='button' disabled><i class='fa fa-close'></i></button>";
                            }
                            return data;
                        }
                    },
                    {
                        "data": "claimNo", "render": function (data, type, obj, meta) {

                            data = "<button class='btn-primary btn' type='button' title='Add Remark' onclick='viewRemark(" + obj.id + ")' ><i class='fa fa-pencil'></i></button>";
                            return data;
                        }
                    },
                    {
                        "data": "claimNo", "render": function (data, type, obj, meta) {

                            data = "<button class='btn-primary btn' type='button' title='View Claim File' onclick='viewClaimFile(" + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                            return data;
                        }
                    }

                ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {

                    /*if (obj.claimStatus == "1") {//DR
                        $(nRow).addClass('badge-light');
                    } else if (obj.claimStatus == "2") {//FW
                        $(nRow).addClass('badge-secondary');
                    } else if (obj.claimStatus == "3") {//AS
                        $(nRow).addClass('badge-success');
                    } else if (obj.claimStatus == "32") {//RE
                        $(nRow).addClass('badge-danger');
                    } else if (obj.claimStatus == "30") {//AS PE
                        $(nRow).addClass('badge-warning');
                    } else if (obj.claimStatus == "31") {//DRAFT & ASSIGN
                        $(nRow).addClass('badge-dark');
                    } else if (obj.v_status == "30") {//AS
                        $(nRow).addClass('badge-secondary');
                    } else {
                        // $(nRow).addClass('badge-danger');
                    }*/

                if (obj.priority == 'HIGH') {
                    $(nRow).addClass('badge-priority');
                }

            }
        });
    } else if (type == 5) {
        table = $('#demo-dt-basic').DataTable({
            "lengthMenu": [50, 100, 150, 250, 500],
            "processing": true,
            "serverSide": true,
            "columnDefs": [
                {"visible": false, "targets": 0, "orderable": false},
                {"orderable": false, "targets": 1},
                {"orderable": false, "targets": 10}
            ],
            responsive: true,
            searching: false,

            //  "scrollY":        "50vh",
            //  "scrollCollapse": true,
            // "scrollX": true,
            "order": [[0, "desc"]],

            /*"ajax": {
             "url": contextPath+"/CallCenterController",
             "type": "GET"
             },*/

            "ajax": {
                "url": URL,
                type: 'POST',
                //  data: this.params
                "data": function (d) {

                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtV_status = $("#txtV_status").val();
                    d.txtV_requestUser = $("#txtV_requestedUser").val();
                    d.txtRequestDate = $("#txtRequestDate").val();

                }
            },
            "columns": [
                {"data": "id"},
                {
                    "data": "index", "render": function (data, type, obj, meta) {


                        if (obj.intimationType == '2') {
                            data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                        } else if (obj.intimationType == '1') {
                            data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                        } else {
                            data = "<span style='padding-left: 5px;'  ></span>" + data;
                        }
                        // <i class='fa fa-star-o'></i>
                        if (obj.isFollowupCallDone == "Y") {
                            data += " <i class='fa fa-star' title='Follow-Up Call Done'></i>";
                        } else {
                            data += " <i class='fa fa-star-o text-mute' title='Follow-Up Call Pending'></i>";
                        }
                        return data;
                    }
                },
                {"data": "claimNo"},
                {"data": "vehicleNo"},
                {"data": "customerName"},
                {"data": "contactNo"},
                {"data": "accidentDate"},
                {"data": "requestedUser"},
                {"data": "requestedDate"},
                {"data": "daysFromRequest", "className": "text-right"},
                {
                    "data": "claimNo", "render": function (data, type, obj, meta) {
                        data = "<button class='btn-primary btn' type='button' title='Arrange ARI' onclick='viewClaim(" + obj.claimNo + ")' ><i class='fa fa-edit'></i></button>";
                        return data;
                    }
                },
                {
                    "data": "claimNo", "render": function (data, type, obj, meta) {
                        data = "<button class='btn-primary btn' type='button' title='View Claim File' onclick='viewClaimFile(" + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                        return data;
                    }
                },
                {
                    "data": "status", "render": function (data, type, obj, meta) {
                        if (obj.status == 'P') {
                            return "<span class='badge badge-warning m-auto'>Hold</span>"
                        } else if (obj.status == 'U') {
                            return "<span class='badge badge-warning m-auto'>Uploaded</span>"
                        } else {
                            return "<span class='badge badge-warning m-auto'>Pending</span>"
                        }
                    }
                }

            ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {

                /*if (obj.claimStatus == "1") {//DR
                    $(nRow).addClass('badge-light');
                } else if (obj.claimStatus == "2") {//FW
                    $(nRow).addClass('badge-secondary');
                } else if (obj.claimStatus == "3") {//AS
                    $(nRow).addClass('badge-success');
                } else if (obj.claimStatus == "32") {//RE
                    $(nRow).addClass('badge-danger');
                } else if (obj.claimStatus == "30") {//AS PE
                    $(nRow).addClass('badge-warning');
                } else if (obj.claimStatus == "31") {//DRAFT & ASSIGN
                    $(nRow).addClass('badge-dark');
                } else if (obj.v_status == "30") {//AS
                    $(nRow).addClass('badge-secondary');
                } else {
                    // $(nRow).addClass('badge-danger');
                }*/

                if (obj.priority == 'HIGH') {
                    $(nRow).addClass('badge-priority');
                }

            }
        });
    } else {
        table = $('#demo-dt-basic').DataTable({
            "lengthMenu": [50, 100, 150, 250, 500],
            "processing": true,
            "serverSide": true,
            "columnDefs": [
                {"visible": false, "targets": 0, "orderable": false},
                {"orderable": false, "targets": 1},
                {"orderable": false, "targets": 10},
                {"orderable": false, "targets": 11},
                {"orderable": false, "targets": 12}
            ],
            responsive: true,
            searching: false,

            //  "scrollY":        "50vh",
            //  "scrollCollapse": true,
            // "scrollX": true,
            "order": [[0, "desc"]],

            /*"ajax": {
             "url": contextPath+"/CallCenterController",
             "type": "GET"
             },*/

            "ajax": {
                "url": URL,
                type: 'POST',
                //  data: this.params
                "data": function (d) {

                    d.txtClaimNumber = $("#txtClaimNumber").val();
                    d.txtVehicleNumber = $("#txtVehicleNumber").val();
                    d.txtV_status = $("#txtV_status").val();
                    d.txtV_requestUser = $("#txtV_requestedUser").val();
                    d.txtRequestDate = $("#txtRequestDate").val();
                }
            },
            "columns": [
                {"data": "id"},
                {
                    "data": "index", "render": function (data, type, obj, meta) {


                        if (obj.intimationType == '2') {
                            data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                        } else if (obj.intimationType == '1') {
                            data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                        } else {
                            data = "<span style='padding-left: 5px;'  ></span>" + data;
                        }
                        // <i class='fa fa-star-o'></i>
                        if (obj.isFollowupCallDone == "Y") {
                            data += " <i class='fa fa-star' title='Follow-Up Call Done'></i>";
                        } else {
                            data += " <i class='fa fa-star-o text-mute' title='Follow-Up Call Pending'></i>";
                        }
                        return data;
                    }
                },
                {"data": "claimNo"},
                {"data": "vehicleNo"},
                {"data": "customerName"},
                {"data": "contactNo"},
                {"data": "accidentDate"},
                {"data": "requestedUser"},
                {"data": "requestedDate"},
                {"data": "daysFromRequest", "className": "text-right"},
                {
                    "data": "status", "render": function (data, type, obj, meta) {
                        if (obj.status == 'P') {
                            return "<span class='badge badge-warning m-auto'>Hold</span>"
                        } else if (obj.status == 'U') {
                            return "<span class='badge badge-warning m-auto'>Uploaded</span>"
                        } else {
                            return "<span class='badge badge-warning m-auto'>Pending</span>"
                        }
                    }
                },
                {
                    "data": "claimNo", "render": function (data, type, obj, meta) {
                        data = "<button class='btn-primary btn' type='button' title='Arrange ARI' onclick='viewClaim(" + obj.claimNo + ")' ><i class='fa fa-edit'></i></button>";
                        return data;
                    }
                },
                {
                    "data": "claimNo", "render": function (data, type, obj, meta) {
                        data = "<button class='btn-primary btn' type='button' title='View Claim File' onclick='viewClaimFile(" + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                        return data;
                    }
                }

            ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {

                // if (obj.claimStatus == "1") {//DR
                //     $(nRow).addClass('badge-light');
                // } else if (obj.claimStatus == "2") {//FW
                //     $(nRow).addClass('badge-secondary');
                // } else if (obj.claimStatus == "3") {//AS
                //     $(nRow).addClass('badge-success');
                // } else if (obj.claimStatus == "32") {//RE
                //     $(nRow).addClass('badge-danger');
                // } else if (obj.claimStatus == "30") {//AS PE
                //     $(nRow).addClass('badge-warning');
                // } else if (obj.claimStatus == "31") {//DRAFT & ASSIGN
                //     $(nRow).addClass('badge-dark');
                // } else if (obj.v_status == "30") {//AS
                //     $(nRow).addClass('badge-secondary');
                // } else {
                //     // $(nRow).addClass('badge-danger');
                // }

                if (obj.priority == 'HIGH') {
                    $(nRow).addClass('badge-priority');
                }
            }
        });
    }


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
