/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewClaimDetails(polRefNo, claimNo) {
    showLoader();
    $("#P_POL_N_REF_NO").val(polRefNo);
    $("#P_N_CLIM_NO").val(claimNo);
    document.getElementById('frmForm').action = contextPath + "/claim/callcenter/claim_multiple_view.jsp";
    document.getElementById('frmForm').submit();
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 10}],
        responsive: true,
        searching: false,

        //  "scrollY":        "50vh",
        //  "scrollCollapse": true,
        // "scrollX": true,
        "order": [[0, "desc"]],

        /*"ajax": {
            "url": contextPath+"/CallCenterController",
            "type": "GET"
        },*/

        "ajax": {
            "url": contextPath + "/CallCenterController/claimlist",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtV_status = $("#txtV_status").val();
                d.txtLocation = $("#txtLocation").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txt3rdVehicleNumber = $("#txt3rdVehicleNumber").val();
            }
        },
        "columns": [
            {"data": "n_ref_no"},
            {
                "data": "index", "render": function (data, type, obj, meta) {
                if (obj.v_intim_type == 'NFT') {
                    data = "<span class='fa fa-warning text-danger' title='Current Intimation Type -> Normal Fast Track'  style='padding-left: 5px;color: black;'  ></span>" + data ;
                } else if (obj.v_intim_type == 'OSFT') {
                    data = "<span class='fa fa-warning text-warning' title='Current Intimation Type -> On Site Fast Track'  style='padding-left: 5px;color: black;'  ></span>"+ data ;
                } else {
                    data = "<span style='padding-left: 5px;color: black;'  ></span>" + data ;
                }

                return data;
            }
            },
            {
                "data": "index", "render": function (data, type, obj, meta) {
                if (obj.v_old_intim_type == 'NFT') {
                    data = "<span class='fa fa-warning text-danger' title='Old Intimation Type -> Normal Fast Track'  style='padding-left: 5px;color: black;'  ></span>" + data ;
                } else if (obj.v_old_intim_type == 'OSFT') {
                    data = "<span class='fa fa-warning text-warning' title='Old Intimation Type -> On Site Fast Track'  style='padding-left: 5px;color: black;'  ></span>"+ data ;
                } else {
                    data = "<span style='padding-left: 5px;color: black;'  ></span>" + data ;
                }

                return data;
            }
            },
            {"data": "v_vehicle_number"},
            {"data": "v_call_user"},
            {"data": "d_date_of_call"},
            {"data": "v_cover_note_no"},
            {"data": "v_name_informer"},
            {"data": "v_pol_no_desc"},
            {"data": "n_clim_no"},
            {"data": "d_accid_date"},
            {"data": "v_place_of_accid"},
            {"data": "v_status_desc"},
            {
                "data": "n_clim_no", "render": function (data, type, obj, meta) {

                data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.n_pol_ref_no + "," + obj.n_clim_no + ")' >View</button>";
                return data;
            }
            }
        ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {
            if (obj.v_status == "DR") {
                $(nRow).addClass('tbl_row_red');
            } else if (obj.v_status == "FW") {
                $(nRow).addClass('tbl_row_green');
            } else if (obj.v_status == "AS") {
                $(nRow).addClass('tbl_row_yellow');
            } else {
                //$(nRow).addClass('badge-danger');
            }

        }
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
