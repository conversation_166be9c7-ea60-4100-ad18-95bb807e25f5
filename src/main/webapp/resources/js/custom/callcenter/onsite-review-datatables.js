console.log("onsite claim js loading")
var table;

$(document).ready(function () {

    $.fn.DataTable.ext.pager.numbers_length = 5;

    table = $('#onsite_review_tbl').DataTable({
        lengthMenu: [50, 100, 150, 250, 500],
        processing: true,
        serverSide: true,
        responsive: true,
        searching: false,
        order: [[0, "desc"]],
        ajax: {
            url: contextPath + "/CallCenter/onsiteReviewList", type: "POST", data: function (d) {
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtStatus = $("#txtStatus").val();
            }, error: function (xhr, error, thrown) {
                console.log("Error loading data: " + thrown);
            }
        },
        columns: [{data: "id"}, {data: "inspectionId"}, {data: "inspectionType"}, {data: "claimNo"}, {data: "rteUserId"}, {data: "callCenterUserId"}, {data: "assessorId"}, {data: "isOnsiteReviewApply"}, {data: "status"}, {data: "submittedDatetime"}, {data: "submittedUser"}, {data: "lastUpdatedDatetime"}, {
            data: "lastUpdatedUser",
            defaultContent: ""
        }, {
            "data": "claimNo", "render": function (data, type, obj, meta) {
                const claimNo = `"${obj.claimNo}"`;
                const status = `"${obj.status}"`; // Boolean.parseBoolean will work only if this is "true"/"false"
                const inspectionId = obj.inspectionId;

                return `<button class='btn btn-primary' type='button'
                    onclick='viewClaimDetails(${claimNo}, ${inspectionId}, ${status})'>
                    <i class='fa fa-eye'></i></button>`;
            }
        }

        ],
        fnRowCallback: function (nRow, obj) {
            if (obj.status === "P") {
                $(nRow).addClass("table-warning");
            } else if (obj.status === "A") {
                $(nRow).addClass("table-info");
            } else if (obj.status === "C") {
                $(nRow).addClass("table-success");
            }
        }
    });

    // Fix selector to match your table ID
    $('#onsite_review_tbl tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            table.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }
    });
});

function search() {
    table.ajax.reload();
    return false;
}

function viewClaimDetails(claimNo, requestedInspectionId, status) {
    const isOnsiteReview = (status === "PENDING" || status === "ASSIGNED");

    // alert("claimNo: " + claimNo + "\nrequestedInspectionId: " + requestedInspectionId + "\nonsiteReview: " + isOnsiteReview + "\nstatus: " + status);
    showLoader();
    $("#P_N_CLIM_NO").val(claimNo);

    // Set or create hidden input for requestedInspectionId
    if ($("#requestedInspectionId").length === 0) {
        $('<input>').attr({
            type: 'hidden', id: 'requestedInspectionId', name: 'requestedInspectionId', value: requestedInspectionId
        }).appendTo('#frmForm');
    } else {
        $("#requestedInspectionId").val(requestedInspectionId);
    }

    // Set or create hidden input for onsiteReview
    if ($("#onsiteReview").length === 0) {
        $('<input>').attr({
            type: 'hidden', id: 'onsiteReview', name: 'onsiteReview', value: isOnsiteReview
        }).appendTo('#frmForm');
    } else {
        $("#onsiteReview").val(isOnsiteReview);
    }

    document.getElementById('frmForm').action = contextPath + "/CallCenter/viewOnsiteReview";
    document.getElementById('frmForm').submit();
}
