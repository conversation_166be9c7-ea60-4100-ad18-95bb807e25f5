/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewClaimDetails(polRefNo, claimNo) {
    showLoader();
    $("#P_POL_N_REF_NO").val(polRefNo);
    $("#P_N_CLIM_NO").val(claimNo);
    document.getElementById('frmForm').action = contextPath + "/CallCenter/viewClaim";
    document.getElementById('frmForm').submit();
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 10}],
        responsive: true,
        searching: false,

        //  "scrollY":        "50vh",
        //  "scrollCollapse": true,
        // "scrollX": true,
        "order": [[0, "desc"]],

        /*"ajax": {
            "url": contextPath+"/CallCenterController",
            "type": "GET"
        },*/

        "ajax": {
            "url": contextPath + "/CallCenter/claimList",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtInsuredName = $("#txtInsuredName").val();
                d.txtEngineNo = $("#txtEngineNo").val();
                d.txtInsuredNic = $("#txtInsuredNic").val();
                d.txtChassisNo = $("#txtChassisNo").val();
                d.txtLocation = $("#txtLocation").val();
                d.txtV_status = $("#txtV_status").val();
                d.txtFollowupCallDone = $("#txtFollowupCallDone").val();
                d.txtCallUserName = $("#txtCallUserName").val();
                d.txt3rdVehicleNumber = $("#txt3rdVehicleNumber").val();
                d.txtCliNumber = $("#txtCliNumber").val();
                // d.txtOtherContactNumber = $("#txtOtherContactNumber").val();
                d.txtISFClaimNumber = $("#txtISFClaimNumber").val();
                d.cmbpolicyChannelType = $("#cmbpolicyChannelType").val();

            }
        },
        "columns": [
            {"data": "refNo"},
            {
                "data": "index", "render": function (data, type, obj, meta) {


                    if (obj.intimationType == '2') {
                        data = "<span class='fa fa-warning text-danger' title='Late' style='padding-left: 5px;'  ></span>" + data;
                    } else if (obj.intimationType == '1') {
                        data = "<span class='fa fa-warning text-warning' title='On Site' style='padding-left: 5px;'  ></span>" + data;
                    } else {
                        data = "<span style='padding-left: 5px;'  ></span>" + data;
                    }
                    // <i class='fa fa-star-o'></i>
                    if (obj.isFollowupCallDone == "Y") {
                        data += " <i class='fa fa-star' title='Follow-Up Call Done'></i>";
                    } else {
                        data += " <i class='fa fa-star-o text-mute' title='Follow-Up Call Pending'></i>";
                    }
                    return data;
                }
            },
            {"data": "claimNo"},
            {"data": "isfClaimNo"},
            {"data": "cliNo"},
            {"data": "policyNumber"},
            {"data": "vehicleNo"},
            {"data": "chassisNo"},
            {"data": "callUser"},
            {"data": "accidDate"},
            {"data": "accidTime"},
            {"data": "coverNoteNo"},
            {"data": "reporterName"},
            {"data": "dateOfReport"},
            {"data": "timeOfReport"},
            {"data": "placeOfAccid"},
            {"data": "policyChannelType"},
            {"data": "claimStatusDesc"},
            // {"data": "otherContNo"},
            {
                "data": "claimNo", "render": function (data, type, obj, meta) {

                    data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.polRefNo + "," + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                    return data;
                }
            }
        ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {

            if (obj.claimStatus == "1") {//DR
                validateRow(nRow, obj, 'badge-light');
            } else if (obj.claimStatus == "2") {//FW
                validateRow(nRow, obj, 'badge-secondary');
            } else if (obj.claimStatus == "3") {//AS
                validateRow(nRow, obj, 'badge-success');
            } else if (obj.claimStatus == "32") {//RE
                validateRow(nRow, obj, 'badge-danger');
            } else if (obj.claimStatus == "30") {//AS PE
                validateRow(nRow, obj, 'badge-warning');
            } else if (obj.claimStatus == "31") {//DRAFT & ASSIGN
                validateRow(nRow, obj, 'badge-dark');
            }else if (obj.v_status == "30") {//AS
                validateRow(nRow, obj, 'badge-secondary');
            } else {
                // $(nRow).addClass('badge-danger');
            }

        }
    });

    function validateRow(nRow, obj, className) {
        if (obj.priority == 'HIGH') {
            $(nRow).addClass('badge-priority');
        } else {
            $(nRow).addClass(className);
        }
    }


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
