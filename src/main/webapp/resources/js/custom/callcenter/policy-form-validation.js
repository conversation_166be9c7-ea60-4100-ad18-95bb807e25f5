$(document).ready(function () {


    $('#frmForm')
        .formValidation({
            framework: 'bootstrap',
            //  excluded: ':disabled',
            // excluded: ':disabled,input:hidden',
            excluded: ':disabled',
            disable: false,
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {

                followCallContactPersonName: {
                    validators: {
                        regexp: {
                            regexp: /^[A-Za-z\s]+$/,
                            message: 'Numbers and Special Characters are not allowed'
                        }
                    }
                },
         /*       damageRemark: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },*/
                accidDateTime: {
                    validators: {
                        date: {
                            format: 'YYYY-MM-DD h:m',
                            message: 'The value is not a valid date',
                        }
                        // callback: dropDownValidation
                    }
                },
                dateTimeOfReport: {
                    validators: {
                        date: {
                            format: 'YYYY-MM-DD h:m',
                            message: 'The value is not a valid date',
                        }
                        // callback: dropDownValidation
                    }
                },
                reporterTitle: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
          /*      driverTitle: {
                    validators: {
                        callback: dropDownValidation
                    }
                },*/
                driverStatus: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                releshipInsurd: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
           /*     driverReleshipInsurd: {
                    validators: {
                        callback: dropDownValidation
                    }
                },*/

                reporterName: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        },
                        regexp: {
                            regexp: /^[A-Za-z\s]+$/,
                            message: 'Numbers and Special Characters are not allowed'
                        }
                    }
                },
                reporterId: {
                    validators: {
                        regexp: {
                            regexp: /^(([0-9]{9}[vVxX])|([0-9]{12}))$/i,
                            message: 'Invalid NIC No format (E.g. 123456789V or 123456789123).'
                        }
                    }
                },

                cliNo: {
                    validators: {
                        regexp: {
                            regexp: /^[0-9]{10}$/i,
                            message: 'Invalid CLI No (E.g. 0712345678/0112345678).- (Only Numaric Value)'
                        },
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                },
                driverName: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },
                driverNic: {
                    validators: {
                        regexp: {
                            regexp: /^(([0-9]{9}[vVxX])|([0-9]{12}))$/i,
                            message: 'Invalid NIC No format (E.g. 123456789V or 123456789123).'
                        }
                    }
                },
                dlNo: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },
                causeOfLoss: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                otherContNo: {
                    validators: {
                        regexp: {
                            regexp: /^(\d{10},)*\d{10}$/,
                            message: 'Invalid Contact No (E.g. 0712345678,0112345678)'
                        }
                    }
                },
                catEventCode: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                accidDesc: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },
                placeOfAccid: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        },
                        callback: ValidationForSpecialChar
                    }
                },
     /*           hugeRemark: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },*/
                // isDoubtRemark: {
                //     validators: {
                //         callback: ValidationForSpecialChar
                //     }
                // },
                /*districtCode: {

                    validators: {
                        callback: dropDownValidation
                    }
                },*/
                lateIntimateReason: {

                    validators: {
                        callback: dropDownValidation
                    }
                },
                /*nearestCity: {
                    validators: {
                        callback: dropDownValidation
                    }
                },*/
                nearPoliceStation: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                firstStatementReqReason: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                vehClsId: {
                    validators: {
                        callback: dropDownValidation
                    }
                },
                inspectionTypeReason: {

                    validators: {
                        notEmpty: {
                            message: 'The Field is required'
                        }
                    }
                },
                // reporterRemark: {
                //     validators: {
                //         callback: ValidationForSpecialChar
                //     }
                // },
                // firstStatementRemark: {
                //     validators: {
                //         callback: ValidationForSpecialChar
                //     }
                // },
                followCallContactNumber: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },
                currentLocation: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                },
                followCallUserId: {
                    validators: {
                        callback: ValidationForSpecialChar
                    }
                }
            }
        })
        .on('err.form.fv', function (e) {
        })
        .on('err.field.fv', function (e, data) {
            if (data.fv.getSubmitButton()) {
                data.fv.disableSubmitButtons(false);
            }
        })
        .on('success.field.fv', function (e, data) {
            if (data.fv.getSubmitButton()) {
                data.fv.disableSubmitButtons(false);
            }
        })
        .on('success.form.fv', function (e) {
            // Prevent form submission
            e.preventDefault();
            var $form = $(e.target);     // Form instance
            // Get the clicked button
            var $button = $form.data('formValidation').getSubmitButton();
            // alert($button.attr('value'));
            saveConfirm($button.attr('value'));
        });
});


function valdateDamageDetails() {
    var chkCnt = 0;
    var b = false;
    var i = 0;
    var listData = getDamageBodyPartsList();
    var listSplitDataResult = listData.split(",");

    try {
        for (x = 0; x < listSplitDataResult.length - 1; x++) {
            i = listSplitDataResult[x];
            b = false;
            if (document.getElementById("chkDamageParts" + i).checked) {
                chkCnt++;
                document.getElementById("div_head" + i).style.backgroundColor = "#F90";
                document.getElementById("div_sub" + i).style.backgroundColor = "#F90";
                if (document.getElementById("rdoDamageParts1" + i).checked) {
                    b = true;
                    continue;
                }
                if (document.getElementById("rdoDamageParts2" + i).checked) {
                    b = true;
                    continue;
                }
                if (document.getElementById("rdoDamageParts3" + i).checked) {
                    b = true;
                    continue;
                }
                if (document.getElementById("rdoDamageParts4" + i).checked) {
                    b = true;
                    continue;
                }
                if (document.getElementById("rdoDamageParts5" + i).checked) {
                    b = true;
                    continue;
                }
                document.getElementById("rdoDamageParts5" + i).checked = true;
                // document.getElementById("div_head" + i).style.backgroundColor = "#F00";
                // document.getElementById("div_sub" + i).style.backgroundColor = "#F00";


            }
            b = true;
            /*if (b == false) {

                b == true;
                $.notify({title: '<b>Alert !</b><br>', message: "Please Select  Damage type.."}, {type: 'danger'});
                return b;
            }*/
        }

        if (chkCnt == 0) {
            $.notify({title: '<b>Alert !</b><br>', message: "Please Select Damage Vehicle Parts"}, {type: 'danger'});
            b = false;
            return b;
        }
    } catch (e) {
        alert("error :" + e);
        b = false;
    }

    return b;
}

function getDamageBodyPartsList() {
    var list = "";
    checkRedioList = "";
    var z = document.getElementById("txtDamagePartCount").value;
    for (i = 1; i <= z; i++) {
        if (document.getElementById("chkDamageParts" + i).checked) {
            list = list + "" + i + ",";

            var x = 0;
            if (document.getElementById("rdoDamageParts1" + i).checked) {
                x = 1;
            } else if (document.getElementById("rdoDamageParts2" + i).checked) {
                x = 2;
            } else if (document.getElementById("rdoDamageParts3" + i).checked) {
                x = 3;
            } else if (document.getElementById("rdoDamageParts4" + i).checked) {
                x = 4;
            } else if (document.getElementById("rdoDamageParts5" + i).checked) {
                x = 5;
            }
            checkRedioList = checkRedioList + "" + x + ",";
        }
    }
    return list;
}