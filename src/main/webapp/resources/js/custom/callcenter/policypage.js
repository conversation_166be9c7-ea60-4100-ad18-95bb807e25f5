var accessUserType = 1;
//BODY PARTS START
var vehicleModelURL = "";

var CLAIM_DRAFT_ASSIGN_STATUS = 31;
var CLAIM_ASSIGN_STATUS = 3;

function updatePolicy() {
    $.ajax({
        url: contextPath + "/CallCenter/replaceMapPolicy",
        type: 'post',
        success: function (result) {
            var obj = JSON.parse(result);
            if (obj.errorCode == '200') {
                notify(obj.message, 'success');
                setTimeout(function () {
                    back(4);
                }, 1500);

            } else {
                notify(obj.message, 'danger');

            }
        }
    });
}

function getAccidentDateResponse(accidentDate, policyRefNo) {
    var coverNoteNo = $('#coverNoteNoVal').val().trim();
    var policyNumber = $('#policyNumberVal').val().trim();

    if (policyNumber != coverNoteNo) {

        $.ajax({
            url: contextPath + "/CallCenter/checkAccidentDate",
            type: 'POST',
            data: {
                policyRefNo: policyRefNo,
                accidentDate: accidentDate
            },
            success: function (result) {
                if (result == 'NO') {
                    $('#accidentDateAlert').show()
                } else {
                    $('#accidentDateAlert').hide()
                }
            }
        });
    } else {
        $('#accidentDateAlert').hide()
    }
}

function setURL(vehicleClass) {
    document.getElementById("SpanViewVehicleModel").style.display = "none";
    if (vehicleClass == 1) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Car Model Image";
        vehicleModelURL = contextPath + '/CallCenter/viewVehicleModel?vehClsId' + vehicleClass;
    } else if (vehicleClass == 2) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Van Model Image";
        vehicleModelURL = contextPath + '/CallCenter/viewVehicleModel?vehClsId' + vehicleClass;
    } else if (vehicleClass == 3) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Jeep Model Image";
        vehicleModelURL = 'vehicle_models/jeep_model_image.jsp';
    } else if (vehicleClass == 4) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Bus Model Image";
        vehicleModelURL = 'vehicle_models/bus_model_image.jsp';
    } else if (vehicleClass == 5) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Double Cab Model Image";
        vehicleModelURL = 'vehicle_models/doublecab_model_image.jsp';
    } else if (vehicleClass == 6) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Moter Bike Model Image";
        vehicleModelURL = 'vehicle_models/motorbike_model_image.jsp';
    } else if (vehicleClass == 7) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Lorry Model Image";
        vehicleModelURL = 'vehicle_models/lorry_model_image.jsp';
    } else if (vehicleClass == 8) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "THREE WEELER Model Image";
        vehicleModelURL = 'vehicle_models/threeweeler_model_image.jsp';
    } else if (vehicleClass == 9) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Tractor Model Image";
        vehicleModelURL = 'vehicle_models/tractor_model_image.jsp';
    } else if (vehicleClass == 10) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Single Cab Model Image";
        vehicleModelURL = 'vehicle_models/doublecab_model_image.jsp';
    } else if (vehicleClass == 11) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Crew Cab Model Image";
        vehicleModelURL = 'vehicle_models/doublecab_model_image.jsp';
    } else if (vehicleClass == 12) {
        document.getElementById("SpanViewVehicleModel").style.display = "block";
        document.getElementById("cmdViewImage").value = "Hybrid Car Model Image";
        vehicleModelURL = contextPath + '/CallCenter/viewVehicleModel?vehClsId' + vehicleClass;
    }
    vehicleModelURL = contextPath + '/CallCenter/viewVehicleModel?vehClsId=' + vehicleClass;

}


function viewVehicleModelImage() {

//           showLoader();
    //Color Box=================
    $(document).ready(function () {
        //Examples of how to assign the ColorBox event to elements

        $(".viewVehicleModel").colorbox({

            width: "100%", height: "100%", iframe: true, href: "" + vehicleModelURL + "",
            onCleanup: function () {

            }
        });


    });
}

var checkRedioList = "";

function getDamageBodyPartsList() {
    var list = "";
    checkRedioList = "";
    var z = document.getElementById("txtDamagePartCount").value;
    for (i = 1; i <= z; i++) {
        if (document.getElementById("chkDamageParts" + i).checked) {
            list = list + "" + i + ",";

            var x = 0;
            if (document.getElementById("rdoDamageParts1" + i).checked) {
                x = 1;
            } else if (document.getElementById("rdoDamageParts2" + i).checked) {
                x = 2;
            } else if (document.getElementById("rdoDamageParts3" + i).checked) {
                x = 3;
            } else if (document.getElementById("rdoDamageParts4" + i).checked) {
                x = 4;
            } else if (document.getElementById("rdoDamageParts5" + i).checked) {
                x = 5;
            }
            checkRedioList = checkRedioList + "" + x + ",";
        }
    }
    return list;
}

function getCheckRedioList() {
    return checkRedioList;
}


function toggleGroup(id, val) {
    $("#vehicalParts").hide();
    if (!val) {
        document.getElementById("rdoDamageParts1" + id).style.display = "none";
        document.getElementById("rdoDamageParts1" + id).checked = false;

        document.getElementById("rdoDamageParts2" + id).style.display = "none";
        document.getElementById("rdoDamageParts2" + id).checked = false;

        document.getElementById("rdoDamageParts3" + id).style.display = "none";
        document.getElementById("rdoDamageParts3" + id).checked = false;

        document.getElementById("rdoDamageParts4" + id).style.display = "none";
        document.getElementById("rdoDamageParts4" + id).checked = false;

        document.getElementById("rdoDamageParts5" + id).style.display = "none";
        document.getElementById("rdoDamageParts5" + id).checked = false;

        document.getElementById("txtOther5" + id).style.display = "none";
        document.getElementById("txtOther5" + id).value = "";

        document.getElementById("div_head" + id).style.backgroundColor = "#F2F2F2";
        document.getElementById("div_sub" + id).style.backgroundColor = "#F9F9F9";

        document.getElementById("chkDamageParts" + id).checked = false;

    } else {
        document.getElementById("rdoDamageParts1" + id).style.display = "block";
        document.getElementById("rdoDamageParts2" + id).style.display = "block";
        document.getElementById("rdoDamageParts3" + id).style.display = "block";
        document.getElementById("rdoDamageParts4" + id).style.display = "block";
        document.getElementById("rdoDamageParts5" + id).style.display = "block";
        document.getElementById("txtOther5" + id).style.display = "block";

        document.getElementById("div_head" + id).style.backgroundColor = "#F90";
        document.getElementById("div_sub" + id).style.backgroundColor = "#F90";
        document.getElementById("chkDamageParts" + id).checked = true;
    }
}


function focusToTab(e, nextTabId) {
    var KeyID = (window.event) ? event.keyCode : e.keyCode;
    switch (KeyID) {
        case 13:
            //pageSubmit("Search");
            break;
        case 9:
            $('#tabs a[href="' + '#' + nextTabId + '"]').tab('show');
            break;
        case 37:
            break;
        case 38:
            break;
        case 39:
            break;
        case 40:
            break;
    }
    // alert(nextTabId)
}

function addRemarks(departmentId, sectionName, textAreaId) {
    var remark = $("#" + textAreaId).val().trim();
    if (remark.length > 0) {
        var URL = contextPath + "/Claim/addRemarks";
        showLoader();
        $.ajax({
            url: URL,
            type: 'POST',
            data: {
                departmentId: departmentId,
                sectionName: sectionName,
                remark: remark
            },
            success: function (result) {
                hideLoader();
                if (result.errorCode != 200) {
                    //set error msg
                    $.notify({title: '<b>Alert !</b><br>', message: result.message}, {type: 'danger'});
                } else {
                    $.notify({title: '<b>Alert !</b><br>', message: result.message}, {type: 'success'});
                    // setTimeout(function () {
                    viewSpecialRemarks();
                    // }, 500);
                    $("#" + textAreaId).val("");
                }
            },
            error: function (request, status, error) {
                hideLoader();
                $.notify({title: '<b>Alert !</b><br>', message: "Error occurred"}, {type: 'danger'});
            }
        });
    } else {
        $.notify({title: '<b>Alert !</b><br>', message: "Special Remark can not be empty."}, {type: 'danger'});
    }
}


function submitPage(type) {
    var URL = contextPath + "/Claim/save?type=" + type;
    var damagePartResponse = true;
    var isDamaged = $('#chkIsNoDamage').is(':checked');
    var isHugeDamage = $('#chkIsHugeDamage').is(':checked');
    var chkDamageNotGiven = $('#chkDamageNotGiven').is(':checked');

    var reportMessage;
    var currentClaimStatus = $("#claimStatus").val();

    switch (type) {

        case 'FW':
            if (currentClaimStatus == CLAIM_DRAFT_ASSIGN_STATUS) {
                $("#claimStatus").val(CLAIM_ASSIGN_STATUS);
            } else {
                $("#claimStatus").val('2');
            }
            if (!isDamaged && !isHugeDamage && !chkDamageNotGiven) {
                damagePartResponse = valdateDamageDetails();
            }
            reportMessage = 'Successfully Saved & Forwarded To Assessor Department';
            break;
        case 'DR':
            if (currentClaimStatus == CLAIM_DRAFT_ASSIGN_STATUS) {
                $("#claimStatus").val(currentClaimStatus);
            } else {
                $("#claimStatus").val('1');
            }
            reportMessage = 'Successfully Saved as Draft';
            break;
        case 'PS':
            if (currentClaimStatus == CLAIM_DRAFT_ASSIGN_STATUS) {
                $("#claimStatus").val(CLAIM_ASSIGN_STATUS);
            } else {
                $("#claimStatus").val('30');
            }
            if (!isDamaged && !isHugeDamage && !chkDamageNotGiven) {
                damagePartResponse = valdateDamageDetails();
            }
            reportMessage = 'Successfully Claim Intimated';
            break;
        case 'REJ':
            $("#claimStatus").val('32');
            reportMessage = 'Successfully Rejected';
            break;
        case 'FUP':
            URL = contextPath + "/Claim/followUpCallUpdate";
            reportMessage = 'Successfully Follow Up Call Updated';
            break;
        case 'ISF':
            URL = contextPath + "/CallCenter/updateISF?type=" + type;
            reportMessage = 'Successfully ISF Updated';
            break;
    }

    if (damagePartResponse == false) {
        $('#tabs a[href="#tabs-2"]').tab('show');
        $("#vehicalParts").show();
        return;
    }
    showLoader();
    var $form = $('#frmForm');
    $.ajax({
        url: URL,
        type: 'POST',
        data: $form.serialize(),
        success: function (result) {
            if (result.errorCode != 200) {
                //set error msg
                hideLoader();
                $.notify({title: '<b>Alert !</b><br>', message: result.message}, {type: 'danger'});
                //fuction for focus related field
                if (result.dtoFieldName == "accidTime") {
                    $("[name='accidTimeHour'],[name='accidTimeMins'],[name='accidTimeMeridiem']").focus().addClass('error');
                } else if (result.dtoFieldName == "timeOfReport") {
                    $("[name='timeOfReportHour'],[name='timeOfReportMins'],[name='timeOfReportMeridiem']").focus().addClass('error');
                } else {
                    $("[name='" + result.dtoFieldName + "']").focus().addClass('error');

                }


            } else {
                $('.btndisable .btn').prop("disabled", true)
                $.notify({title: '<b>Alert !</b><br>', message: reportMessage}, {type: 'success'});
                setTimeout(function () {
                    hideLoader();
                    switch (type) {
                        case 'FW':
                        case 'DR':
                            back(2); //viewReportAccidentList
                            break;
                        case 'PS':
                            document.frmForm.action = contextPath + "/CallCenter/viewClaim?TYPE=2&P_N_CLIM_NO=" + result.dtoFieldName;
                            document.frmForm.submit();
                            // window.location.href = contextPath + "/CallCenter/viewClaim?P_N_CLIM_NO=" + result.dtoFieldName;
                            break;
                        case 'REJ':
                            back(2);
                            break;
                    }
                }, 1500);
            }
            hideLoader();

        },
        error: function (request, status, error) {
            hideLoader();
            $.notify({title: '<b>Alert !</b><br>', message: "Error occurred"}, {type: 'danger'});
        }
    });


}

$(document).ready(function () {
    $("#releshipInsurd,#driverReleshipInsurd,#lateIntimateReason" +
        ",#causeOfLoss" +
        ",#districtCode,#nearestCity,#nearPoliceStation" +
        ",#firstStatementReqReason,#inspectionTypeReason,#draftReason,#vehClsId" +
        ",#ncbReason,#lomoReason,#driverStatus").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });
});

function saveConfirm(type) {

    var currentClaimStatus = $("#claimStatus").val();
    var message = "Do you want to draft intimation?";
    switch (type) {
        case "DR":
            message = "Do you want to draft intimation?";
            break;
        case "FW":
            message = "Do you want to forward to the assessor department?";
            if (currentClaimStatus == CLAIM_DRAFT_ASSIGN_STATUS) {
                message = "<span style='color: #ba7908;font-weight: bold;'>Record in Assessor Assigned State</span>, Do you want to Forward Claim?";
            }
            break;
        case "PS":
            message = "Do you want to intimate?";
            if (currentClaimStatus == CLAIM_DRAFT_ASSIGN_STATUS) {
                message = "<span style='color: #ba7908;font-weight: bold;'>Record in Assessor Assigned State</span>, Do you want to Intimate Claim?";
            }
            break;
        case "ISF":
            message = "Do you want to Update ISF?";
            // if (currentClaimStatus == CLAIM_DRAFT_ASSIGN_STATUS) {
            //     message = "<span style='color: #ba7908;font-weight: bold;'>Record in Assessor Assigned State</span>, Do you want to Intimate Claim?";
            // }
            break;

    }

    var accidentDate = $("#accidDateTime").val();
    var policyRefNo = $('#PolRefNumber').val();
    var coverNoteNo = $('#coverNoteNoVal').val().trim();
    var policyNumber = $('#policyNumberVal').val().trim();
    var latestIntimateDateStatus = $('#latestIntimateDateStatus').val();
    var latestIntimateCallUser = $('#latestIntimateCallUser').val();
    var latestClmIntimDate = $('#latestClmIntimDate').val();

    if ("0" == latestIntimateDateStatus) {
        message = "This claim has been already intimated by " + latestIntimateCallUser + ", today. " + message;
    } else if ("1" == latestIntimateDateStatus) {
        message = "This claim has been already intimated by " + latestIntimateCallUser + ", on " + latestClmIntimDate + ". " + message;
    }

    if (policyNumber != coverNoteNo) {
        $.ajax({
            url: contextPath + "/CallCenter/checkAccidentDate",
            type: 'POST',
            data: {
                policyRefNo: policyRefNo,
                accidentDate: accidentDate
            },
            success: function (result) {
                if (result == 'NO') {
                    bootbox.alert("Accident Date is not with in Policy Valid Period.");

                } else {
                    bootbox.confirm({
                        message: message,
                        buttons: {
                            cancel: {
                                label: 'No',
                                className: 'btn-secondary float-right'
                            },
                            confirm: {
                                label: 'Yes',
                                className: 'btn-primary'
                            }
                        },
                        callback: function (result) {
                            if (result == true) {
                                submitPage(type);
                            }
                        }
                    });
                }
            }
        });

    } else {
        bootbox.confirm({
            message: message,
            buttons: {
                cancel: {
                    label: 'No',
                    className: 'btn-secondary float-right'
                },
                confirm: {
                    label: 'Yes',
                    className: 'btn-primary'
                }
            },
            callback: function (result) {
                if (result == true) {
                    submitPage(type);
                }
            }
        });

    }


};

function backConfirm(type) {
    bootbox.confirm({
        message: "Are you sure you want to close this Page?",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-primary'
            },
            cancel: {
                label: 'No',
                className: 'btn-secondary float-right'
            }

        },
        callback: function (result) {
            if (result == true) {
                back(type);
            }
        }
    });
};

function mappingConfirm() {
    bootbox.confirm({
        message: "Are you sure you want to Map this policy details?",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-primary'
            },
            cancel: {
                label: 'No',
                className: 'btn-secondary float-right'
            }

        },
        callback: function (result) {
            if (result == true) {
                updatePolicy();
            }
        }
    });
};

function back(type) {
    parent.document.getElementById("loading").style.display = "block";
    parent.document.getElementById("cell1").style.display = "block";
    if (type == 1 || type == 5) {
        document.frmForm.action = contextPath + "/CallCenter/viewReportAccidentList?TYPE=" + type;
        document.frmForm.submit();
    } else if (type == 2 || type == 3 || type == 4) {
        document.frmForm.action = contextPath + "/CallCenter/viewReportedClaimList?TYPE=" + type;
        document.frmForm.submit();
    } else if (type == 5) {
        document.getElementById('frmForm').action = contextPath + "/RequestAriController/ariListView?TYPE=" + type;
        document.getElementById('frmForm').submit();
    } else if (type == 6) {
        document.getElementById('frmForm').action = contextPath + "/RequestAriController/ariListView?TYPE=" + type;
        document.getElementById('frmForm').submit();
    } else if (type ==7)  {
        document.frmForm.action = contextPath + "/DashboardController/viewReportAccidentList?TYPE=" + 1;
        document.frmForm.submit();
    } else if (type ==8){
        document.frmForm.action = contextPath + "/MotorEngineerController/jobView?TYPE=1";
        document.frmForm.submit();
    }
}


function setVisibilityForEmpanelLomo(vehiClassId) {
    var lomo1 = document.getElementById("tab-51");
    var lomo2 = document.getElementById("tabs-51");
    lomo1.style.display = "none";
    lomo2.style.display = "none";
    // alert(lomo);
    switch (vehiClassId) {//car 1, van 2,jeep3,5 double cab, 11 crew cab
        case '1':
        case '2':
        case '5':
        case '3':
        case '11':
            lomo1.style.display = "block";
            lomo2.style.display = "block";
            break;
        default:
            lomo1.style.display = "none";
            lomo2.style.display = "none";
    }
    $('#tabs a[href="#tabs-31"]').tab('show')

}






