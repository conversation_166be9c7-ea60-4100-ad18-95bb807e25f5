/**
 * Created by a<PERSON><PERSON> on 5/2/18.
 */


$(document).ready(function () {

    alert('here');


    // FORM VALIDATION FEEDBACK ICONS
    // =================================================================


    //tableTypes

    // =================================================================


    $('#frmRemark')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                remark: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                }

            }
        }).on('success.form.fv', function (e) {
        // Prevent form submission
        var $form = $(e.target);
        e.preventDefault();

        var $button = $form.data('formValidation').getSubmitButton();
        var type = $button.attr('value');

        var remark = $('#remark').val();
        var formData = $('#frmRemark').serialize();
        if (remark != '') {
            $.ajax({
                url: contextPath + "/ClaimHandlerController/saveRemark?remarkType=" + type,
                type: 'POST',
                data: formData,
                success: function (result) {
                    var obj = JSON.parse(result);

                    if (obj != "") {
                        notify(obj, "success");
                        specialRemarksDiv();
                        // specialRemarksDiv();
                        // logdetails();
                    } else {
                        notify("Can not be updated", "danger");
                    }

                }
            });
        }

    });


});





