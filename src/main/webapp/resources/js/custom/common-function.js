/**
 * Created by a<PERSON><PERSON> on 4/25/18.
 */


var disableFormInputs = function (form) {
    $(form + " input").prop("readonly", true);
    $(form + ' input[type=radio], ' + form + ' input[type=checkbox]').prop("disabled", true);
    $(form + " textarea").prop("readonly", true);
    $(form + " input[type=button]").prop("readonly", false);
    $(form + " input[type=submit]").prop("readonly", false);
    $(form + " input[type=reset]").prop("readonly", false);
    $("#paidAdvanceAmount").prop("readonly", true);
    $("#balanceAdvanceAmount").prop("readonly", true);
    $("#totalApprovedAdvanceAmount").prop("readonly", true);
    $("#currentProvision").prop("readonly", true);
    try {
        $(form + " select").prop("disabled", true).trigger("chosen:updated");
    } catch (e) {
    }
};

var removeDisableFormInputs = function (form) {
    $(form + " input").prop("readonly", false);
    $(form + ' input[type=radio], ' + form + ' input[type=checkbox]').prop("disabled", false);
    $(form + " textarea").prop("readonly", false);
    $(form + " input[type=button]").prop("readonly", true);
    $(form + " input[type=submit]").prop("readonly", true);
    $(form + " input[type=reset]").prop("readonly", true);
    $("#paidAdvanceAmount").prop("readonly", true);
    $("#balanceAdvanceAmount").prop("readonly", true);
    $("#totalApprovedAdvanceAmount").prop("readonly", true);
    $("#currentProvision").prop("readonly", true);
    try {
        $(form + " select").prop("disabled", false).trigger("chosen:updated");
        $(form + " input").prop("disabled", false);
    } catch (e) {
    }
};



//alerts
var title = '<b>Alert !</b><br>';

function AlertErrorTirdprty(msg, type) {
    $.notify({title: title, message: msg}, {type: type, delay: 10000});
}


function assesoraddRemark(msg, type) {
    $.notify({title: title, message: msg}, {type: type});
}

function notify(msg, type) {
    $.notify({title: title, message: msg}, {type: type, delay: 10000});
}

function getEnumValue(val) {
    switch (val) {
        case 'Y':
            return 'YES';
            break;
        case 'N':
            return 'NO';
            break;
        case 'WD':
            return 'Want to decide';
            break;
    }

}
var ValidationForSpecialChar = {
    message: 'Special Characters are not allowed',
    callback: function (value, validator, $field) {
        var regex = /^[a-zA-Z0-9\s!@#$%^&*()_+.,\-=~:;"'{}<>?/\\[\]]*$/;
        return regex.test(value);
    }
};


var dropDownValidation = {
    message: 'This field is required.&nbsp;&nbsp;',
    callback: function (value, validator, $field) {
        return parseInt(value) > 0;
    }
};

var dropDownValidationForText = {
    message: 'This field is required.',
    callback: function (value, validator, $field) {
        return value != "";
    }
};


var currencyValidationCallback = {
    message: 'Invalid Currency Format (###.##)',
    callback: function (value, validator, $field) {
        if (value.length === 0)
            return true;
        var number =/^-?(\d+|\d+\.\d+)$/;
        var r = value.match(number);
        if (r === null)
            return false;
        if (r)
            return true;
        else
            return false;
    }
};

var numericValidationCallback = {
    message: 'Only numbers are allowed.',
    callback: function (value, validator, $field) {
        if (value.length === 0)
            return true;
        var number = /^-?[0-9]+$/;
        var r = value.match(number);
        if (r === null)
            return false;
        if (r)
            return true;
        else
            return false;
    }
};


function focusTab(tabNo) {
    $('#tabs a[href="#tabs-' + tabNo + '"]').tab('show');
}

$(document).on("keypress", 'form', function (e) {
    var code = e.keyCode || e.which;
    if (code == 13) {
        e.preventDefault();
        return false;
    }
});

var decimalValidationCallback = {
    message: 'Invalid Format',
    callback: function (value, validator, $field) {
        if (value.length === 0)
            return true;
        var number = /^(\d+|\d+\.\d+)$/;
        var r = value.match(number);
        if (r === null)
            return false;
        if (r)
            return true;
        else
            return false;
    }
};

;



