var table;
var draggedIte;
var parentProductId;

$(document).ready(function(){
    populateProductDetail();
});
function populateProductDetail() {
    $.ajax({
        type: "GET",
        url: contextPath + "/UserManagementController/getProductDetail",
        success: function (data) {
            var parsedData = JSON.parse(data);
            console.log(parsedData)
            var coverList = parsedData.coverData;
            var serviceFactorDetailDtoList = parsedData.serviceFactorData;
            var conditionAndExclusionDtoList = parsedData.conditionData;
            var benefitDetailDtoList = parsedData.benefitData;
            var specialPackagesData = parsedData.specialPackagesData;

            console.log(conditionAndExclusionDtoList)
            console.log(coverList)
            console.log(serviceFactorDetailDtoList)
            console.log(benefitDetailDtoList)

            populateServiceFactorTable('serviceFactorTable', serviceFactorDetailDtoList);
            populateBenefitTable('benefitTable', benefitDetailDtoList);
            populateTableCoverTable('coverTable', coverList);
            populateTablecAndETable('cAndETable', conditionAndExclusionDtoList);
            populateTableSpecialPackageTable('specialPackagesTable', specialPackagesData);
        },
        error: function (error) {
            notify("System Error!", "danger")
        }
    });
}



function populateBenefitTable(tableId, data) {
    var tableBody = $('#' + tableId + ' tbody');
    tableBody.empty();
    var row;
    console.log(tableId);
    console.log(data);
    $.each(data, function(index, item) {
        row = $('<tr id="' + item.id + '" className="draggable_tr card"></tr>');
        row.append('<td className="hidden" >' + index + '</td>');
        row.append('<td>' + item.code + '</td>');
        row.append('<td >' + item.benefitName + '</td>');
        tableBody.append(row);
    });
}
function populateTableCoverTable(tableId, data) {
    var tableBody = $('#' + tableId + ' tbody');
    tableBody.empty();
    var row;
    console.log(tableId);
    console.log(data);
    $.each(data, function(index, item) {
        row = $('<tr id="' + item.id + '" className="draggable_tr card"></tr>');
        row.append('<td className="hidden" >' + index + '</td>');
        row.append('<td>' + item.code + '</td>');
        row.append('<td >' + item.coverName + '</td>');
        tableBody.append(row);
    });
}
function populateServiceFactorTable(tableId, data) {
    var tableBody = $('#' + tableId + ' tbody');
    tableBody.empty();
    var row;
    console.log(tableId);
    console.log(data);
    $.each(data, function(index, item) {
        row = $('<tr id="' + item.id + '" className="draggable_tr card"></tr>');
        row.append('<td className="hidden" >' + index + '</td>');
        row.append('<td>' + item.code + '</td>');
        row.append('<td >' + item.serviceFactorName + '</td>');
        tableBody.append(row);
    });
}

function populateTableSpecialPackageTable(tableId, data) {
    var tableBody = $('#' + tableId + ' tbody');
    tableBody.empty();
    var row;
    console.log(tableId);
    console.log(data);
    $.each(data, function(index, item) {
        row = $('<tr id="' + item.id + '" className="draggable_tr card"></tr>');
        row.append('<td className="hidden" >' + index + '</td>');
        row.append('<td>' + item.code + '</td>');
        row.append('<td >' + item.name + '</td>');
        tableBody.append(row);
    });
}
function populateTablecAndETable(tableId, data) {

    var tableBody = $('#' + tableId + ' tbody');
    tableBody.empty();
    var row;
    console.log(tableId);
    console.log(data);
    $.each(data, function(index, item) {
        row = $('<tr id="' + item.id + '" className="draggable_tr card"></tr>');
        row.append('<td className="hidden" >' + index + '</td>');
        row.append('<td>' + item.code + '</td>');
        row.append('<td >' + item.cAndEName + '</td>');
        tableBody.append(row);
    });
}

$(document).ready(function () {
    hideLoader()
});


$("#serviceFactorTable tbody").sortable({
    helper: "clone",
    opacity: 0.5,
    cursor: "move",
    connectWith: ".draggable-table tbody",
    start: function (event, ui) {
        $(this).closest(".col-lg-12").find(".add-div").show();
    },
    stop: function (event, ui) {
        $(this).closest(".col-lg-12").find(".add-div").hide();
    }
});

$(".draggable-table tbody").not("#serviceFactorTable tbody").sortable({
    helper: "clone",
    opacity: 0.5,
    cursor: "grabbing",
    connectWith: ".draggable-table tbody",
    start: function (event, ui) {
        $(this).closest(".col-lg-12").find(".add-div").show();
    },
    stop: function (event, ui) {
        $(this).closest(".col-lg-12").find(".add-div").hide();
    }
});
$(".draggable-table tbody").not("#serviceFactorTable tbody").on("sortreceive", function (event, ui) {
    if (ui.sender && ui.sender[0] === this) {
        $(ui.sender).sortable("cancel");
    }
});


function getTableData(tableId) {
    var table = document.getElementById(tableId);
    var data = [];
    var rowData;
    if(!table.querySelector('#newRowId')){
        for (var i = 1; i < table.rows.length; i++) {
            var row = table.rows[i];
            rowData = {
                "id":row.cells[0].textContent,
                "code":row.cells[1].textContent,
                "name":row.cells[2].textContent
            };
            data.push(rowData);
        }
    }
    return data;
}

function submitChanges() {
    showLoader()
    var serviceFactorDataList = getTableData('serviceFactorTable');
    var benefitDataList = getTableData('benefitTable');
    var coverDataList = getTableData('coverTable');
    var conditionDataList = getTableData('cAndETable');
    var specialPackageDataList = getTableData('specialPackagesTable');


    var combinedData = {
        serviceFactorData: serviceFactorDataList,
        benefitData: benefitDataList,
        coverData: coverDataList,
        conditionData: conditionDataList,
        specialPackageData: specialPackageDataList
    };

    var combinedDataJSON = JSON.stringify(combinedData);

    console.log(combinedDataJSON);

    $.ajax({
        type: "POST",
        url: contextPath + "/UserManagementController/saveCovers",
        data: {
            ProductDetail: combinedDataJSON
        },
        success: function(data) {
            let result = JSON.parse(data);
            hideLoader()
            if (result == "SUCCESS") {
                notify("Saved Successfully!", "success")
                setTimeout(reloadPage, 1000);
            }else if (result == "FAIL") {
                notify("Failed to Update", 'danger')
            } else {
                notify("System Error", 'danger');
            }
        },
    });

}
function reloadPage() {
    console.log(contextPath+"/UserManagementController/salientManage");
    location.reload();
}

function addEmptyRowSF(tableBody) {
    const newRow = document.createElement('tr');
    newRow.id = 'newRowId';
    newRow.innerHTML = `
        <td style="display: none;"></td>
        <td style="display: none;"></td>
       
        <td colspan="3">
            <div style="display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        align-content: center;
                        width: 100%;
                        height: 100px;
                        border: 1px solid #eaeaea;
                        margin-top: 4px;
                        margin-bottom: 4px;">
                <i class="fa fa-plus" aria-hidden="true" style="margin-bottom: 2px; color: #cbcbcb; font-size: 24px;"></i>
                <span style="color: #ababab; font-size: 16px; font-weight: 600;">Drag Here</span>
            </div>
        </td>
    `;

    tableBody.appendChild(newRow);
}
function removeElementIfTableHasRows(parentMenu) {
    if (parentMenu) {
        const childElement = parentMenu.querySelector('#newRowId');
        if (childElement) {
            parentMenu.removeChild(childElement);
        }
    }
}


function setupObserverServiceFactorTable() {
    const serviceFactorTableBody = document.getElementById('serviceFactorTableBody');
    const serviceFactorTableBodyObserver = new MutationObserver(function (mutationsList) {
        const isEmpty = serviceFactorTableBody.childElementCount === 0;
        if (isEmpty) {
            addEmptyRowSF(serviceFactorTableBody);
        }
        if (serviceFactorTableBody.childElementCount === 2) {
            for (let i = 0; i < serviceFactorTableBody.childElementCount; i++) {
                if (serviceFactorTableBody.children[i].id === "newRowId") {
                    removeElementIfTableHasRows(serviceFactorTableBody);
                }
            }
        }
    });
    const serviceFactorTableBodyConfig = { childList: true };
    serviceFactorTableBodyObserver.observe(serviceFactorTableBody, serviceFactorTableBodyConfig);
}
function setupObserverConditionTable() {
    const cAndETableBodyTableBody = document.getElementById('cAndETableBody');
    const cAndETableBodyTableBodyObserver = new MutationObserver(function (mutationsList) {
        const isEmpty = cAndETableBodyTableBody.childElementCount === 0;
      if(isEmpty){
          addEmptyRowSF(cAndETableBodyTableBody)
        }
        if (cAndETableBodyTableBody.childElementCount === 2) {
            for (let i = 0; i < cAndETableBodyTableBody.childElementCount; i++) {
                if (cAndETableBodyTableBody.children[i].id === "newRowId") {
                removeElementIfTableHasRows(cAndETableBodyTableBody);
                }

            }
        }
    });
    const cAndETableBodyTableBodyConfig = { childList: true };
    cAndETableBodyTableBodyObserver.observe(cAndETableBodyTableBody, cAndETableBodyTableBodyConfig);
}

function setupObserverSpecialPackageTable() {
    const specialPackagesTableBody = document.getElementById('specialPackagesTableBody');
    const specialPackagesTableBodyObserver = new MutationObserver(function (mutationsList) {
        const isEmpty = specialPackagesTableBody.childElementCount === 0;
      if(isEmpty){
          addEmptyRowSF(specialPackagesTableBody)
        }
        if (specialPackagesTableBody.childElementCount === 2) {
            for (let i = 0; i < specialPackagesTableBody.childElementCount; i++) {
                if (specialPackagesTableBody.children[i].id === "newRowId") {
                removeElementIfTableHasRows(specialPackagesTableBody);
                }

            }
        }
    });
    const specialPackagesTableBodyConfig = { childList: true };
    specialPackagesTableBodyObserver.observe(specialPackagesTableBody, specialPackagesTableBodyConfig);
}
function setupObserverCoverTable() {
    const coverTableBody = document.getElementById('coverTableBody');
    const coverTableBodyObserver = new MutationObserver(function (mutationsList) {
        const isEmpty = coverTableBody.childElementCount === 0;
        if(isEmpty){
            addEmptyRowSF(coverTableBody)
        }

        if (coverTableBody.childElementCount === 2) {
            for (let i = 0; i < coverTableBody.childElementCount; i++) {
                if (coverTableBody.children[i].id === "newRowId") {
                    removeElementIfTableHasRows(coverTableBody);
                }
            }
        }
    });
    const coverTableBodyConfig = { childList: true };
    coverTableBodyObserver.observe(coverTableBody, coverTableBodyConfig);
}
function setupObserverBenefitTable() {
    const benefitTableTableBody = document.getElementById('benefitTableTableBody');
    const benefitTableTableBodyObserver = new MutationObserver(function (mutationsList) {
        const isEmpty = benefitTableTableBody.childElementCount === 0;
        if(isEmpty){
            addEmptyRowSF(benefitTableTableBody)
        }
        if (benefitTableTableBody.childElementCount === 2) {
            for (let i = 0; i < benefitTableTableBody.childElementCount; i++) {
                if (benefitTableTableBody.children[i].id === "newRowId") {
                    removeElementIfTableHasRows(benefitTableTableBody);
                }
            }
        }
    });
    const benefitTableTableBodyConfig = { childList: true };
    benefitTableTableBodyObserver.observe(benefitTableTableBody, benefitTableTableBodyConfig);
}

setupObserverServiceFactorTable();
setupObserverConditionTable();
setupObserverCoverTable();
setupObserverBenefitTable();
setupObserverSpecialPackageTable();


function searchMainTableDetail(){

    switch ($("#coversContainer .active").attr('href')) {
        case "#chargesMain":getMasterTableData("chargesMasterTable");
            break;
        case "#cweMain":getMasterTableData("cweMasterTable");
            break;
        case "#benefitMain":getMasterTableData("benefitCoverLoadingDetailTable");
            break;
        case "#otherSecMain":getMasterTableData("OtherSecTable");
            break;
        default:console.log("default")
            break;

    }
}

function getMasterTableData(tableName){
    let name = document.getElementById('nameSearch').value;
    let code = document.getElementById('codeSearch').value;
    $.ajax({
        type: "GET",
        url: contextPath + "/UserManagementController/getSalientMasterDetail?code="+code+"&name="+name+"&tableName="+tableName,
        success: function(data) {
            let result = JSON.parse(data);
            hideLoader()
            if (result instanceof Object) {
                setMasterTableData(tableName,result)
            }else if (result == "FAIL") {
                notify("Failed to Search", 'danger')
            } else if(result == "EMPTY"){
                notify("Couldn't find any result", 'info');
            }else{
                notify("System Error", 'danger');
            }
        },
    });
}


function setMasterTableData(tableId, data){
    let tableBody = $('#' + tableId + 'Body');
    tableBody.empty();
    console.log(tableBody)
    console.log(data)
    let row;
    $.each(data, function(index, item) {
        let color = item.status == 'Y' ? 'aliceblue' : 'antiquewhite';
        row = $('<tr id="' + item.id + '" className="draggable_tr card" style="background:'+color+'"></tr>');
        row.append('<td>' + item.id + '</td>');
        row.append('<td>' + item.code + '</td>');
        row.append('<td>' + item.coverName + '</td>');
        row.append('<td>' + item.insertDate + '</td>');
        tableBody.append(row);
    });
}