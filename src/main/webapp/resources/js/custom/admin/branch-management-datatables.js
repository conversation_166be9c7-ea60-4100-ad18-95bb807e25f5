var table;

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [
            {"orderable": false, "targets": 0},
            {"orderable": false, "targets": 1},
            {"orderable": false, "targets": 7}],
        responsive: true,
        searching: false,

        "ajax": {
            "url": contextPath + "/UserManagementController/searchAllBranchDetails",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.branchCode = $("#branchCode").val();
                d.branchCity = $("#branchCity").val();
                d.branchName = $("#branchName").val();
            }
        },
        "columns": [
            {
                'data': null,
                'render': function (data, type, obj, row) {
                    var checkBox = "<input type='checkbox' class='checkBtn' id='BRL-" + data.branchCode + "' name='checkBox' onclick='enableDeleteBtn()'/>";
                    return checkBox;
                }
            },
            {"data": "index"},
            {"data": "branchCode"},
            {"data": "branchName"},
            {"data": "branchCity"},
            {"data": "inputUser"},
            {"data": "inputDatetime"},
            {
                "data": "branchCode", "render": function (data, type, obj, meta) {
                    data = '<button class="btn-primary btn" type="button" onclick="viewBranchDetails(\'' + obj.branchCode + '\',\'' + 'Update' + '\');" ><i class="fa fa-edit"></i></button>';
                    return data;
                }
            }


        ]
    });

    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function viewBranchDetails(branchCode, action) {
    if (action == 'Update') {
        $.ajax({
            type: 'POST',
            url: contextPath + "/UserManagementController/viewBranchDetails?branchCode=" + branchCode,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {

                var obj = JSON.parse(data);
                $("#panelBranch").modal({
                    backdrop: 'static',
                    keyboard: false
                }).on('shown.bs.modal', function (e) {
                    initializeValidation();

                });

                $('#pnlBranchCode').val(branchCode);
                $('#pnlBranchName').val(obj.branchName);
                $('#pnlBranchCity').val(obj.branchCity);

                $('#pnlBranchCode').prop('readonly', true);

                $("#btnSubmit").prop('value', 'Update');

            },
            error: function (response) {
                swal(response.responseText);
            }

        });
    } else {
        $('#pnlBranchCode').val('');
        $('#pnlBranchName').val('');
        $('#pnlBranchCity').val('');

        $("#panelBranch").modal({
            backdrop: 'static',
            keyboard: false
        }).on('shown.bs.modal', function (e) {
            initializeValidation();

        });

        $('#pnlBranchCode').prop('readonly', false);

        $("#btnSubmit").prop('value', 'Save');
    }

}

function initializeValidation() {
    $('#frmBranchDetails')
        .formValidation({
            framework: 'bootstrap',
            excluded: 'disabled',
            icon: {
                valid: 'fa fa-ok',
                invalid: 'fa fa-remove',
                validating: 'fa fa-refresh'
            },
            fields: {

                branchCode: {
                    // object with has 'myClass' class.
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                },
                branchName: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                },
                branchCity: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required and cannot be empty.'
                        }
                    }
                }
            }
        })
        .on('success.form.fv', function (e) {
            $(e.target).data('formValidation').disableSubmitButtons(false);
            updateBranchDetails();

        });
}

function destroyValidation() {
    $('#frmBranchDetails').data("formValidation").destroy();
}

var timer = null;
$('#pnlBranchCode').keydown(function () {
    clearTimeout(timer);
    timer = setTimeout(isCheckBranchCodeDuplicate, 500)
});

function isCheckBranchCodeDuplicate() {
    if ('Save' == $('#btnSubmit').val()) {
        $('#btnSubmit').attr('disabled', true);
        var branchCode = $('#pnlBranchCode').val();
        $.ajax({
            type: 'POST',
            url: contextPath + "/UserManagementController/isDuplicateBranchCode?branchCode=" + branchCode,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                var obj = JSON.parse(data);
                if (obj != null) {
                    if ('Y' == obj) {
                        $('#errorDiv').show();
                        $('#pnlBranchName').attr('readOnly', true);
                        $('#pnlBranchCity').attr('readOnly', true);
                    } else {
                        $('#errorDiv').hide();
                        $('#btnSubmit').attr('disabled', false);
                        $('#pnlBranchName').attr('readOnly', false);
                        $('#pnlBranchCity').attr('readOnly', false);
                    }

                }
            },
            error: function (response) {
                swal(response.responseText);
            }

        });
    }
}

function search() {
    table.ajax.reload();
    enableDeleteBtn();
    return false;
}

function selectAllCheckBoxes() {
    if (document.getElementById('selectAll').checked) {
        $('.checkBtn').prop('checked', true);
    } else {
        $('.checkBtn').prop('checked', false);
    }
    enableDeleteBtn();

}

function enableDeleteBtn() {

    var elements = document.getElementsByName('checkBox')

    for (var i = 0; i < elements.length; i++) {
        var id = elements[i].getAttribute('id');

        if (document.getElementById(id).checked) {
            $("#claimReassignDiv").show();
            $("#btnDelete").prop("disabled", false);
            return;
        } else {
            $("#btnDelete").prop("disabled", true);
        }
    }
}
