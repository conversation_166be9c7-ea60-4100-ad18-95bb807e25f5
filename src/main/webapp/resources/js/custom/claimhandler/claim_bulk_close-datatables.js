var table;

function viewClaimDetails(txnNo, claimNo) {
    showLoader();
    var url = contextPath + "/ClaimHandlerController/viewEdit?TYPE=30";
    $("#P_N_CLIM_NO").val(claimNo);
    if (type == 4 || type == 5) {
        url = contextPath + "/MotorEngineerController/viewSupplyOrderCheck";
    }
    document.getElementById('frmForm').action = url;
    document.getElementById('frmForm').submit();
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;
    var url = contextPath + "/ClaimHandlerController/closeClaimList";

    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [
            {"orderable": false,"targets": 0},
            {"visible": false, "targets": 1, "orderable": false},
            {"orderable": false,"targets": 13,"visible":false},
            {"orderable": false,"targets": 14,"visible":false},
            {"orderable": false,"targets": 18}

        ],
        responsive: true,
        searching: false,

        //  "scrollY":        "50vh",
        //  "scrollCollapse": true,
        // "scrollX": true,
        "order": [[0, "desc"]],

        /*"ajax": {
            "url": contextPath+"/CallCenterController",
            "type": "GET"
        },*/

        "ajax": {
            "url": url,
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtLocation = $("#txtLocation").val();
                d.txtV_status = $("#txtV_status").val();
                d.txtFileStatus = $("#txtFileStatus").val();
                d.txtLiabilityStatus = $("#txtLiabilityStatus").val();
                d.txtFinalizedStatus = $("#txtFinalizedStatus").val();
                d.txtSupplierOrderStatus = $("#txtSupplierOrderStatus").val();
                d.noOfDays = $("#cmbNoOfDays").val();
                d.txtCalsheetStatus = $("#txtCalsheetStatus").val();
                d.txtLossType = $("#txtLossType").val();
            }
        },
        "columns": [
            {
                'data': null,
                'render': function (data, type, obj, row) {
                    if (obj.closeStatus == 'CLOSE') {
                        return " <span class='fa fa-close text-danger'  style='padding-left: 5px;'  ></span>";
                    } else {
                        var checkBox = "<input type='checkbox' class='checkBtn' id='CCB" + data.claimNo + "' name='checkBox' onclick='enableCloseBtn()'/>";
                        return checkBox;
                    }

                }
            },
            {"data": "txnId"},
            {
                "data": "index", "render": function (data, type, obj, meta) {


                    if (obj.fileStore == 'Y') {
                        data = data + " <span class='fa fa-file text-info'  style='padding-left: 5px;'  ></span>";
                    }
                    return data;
                }
            },
            {
                "data": "claimNo", "render": function (data, type, obj, meta) {


                    if (obj.closeStatus == 'CLOSE') {
                        return data + " <span class='fa fa-close text-danger'  style='padding-left: 5px;'  ></span>";
                    } else if (obj.closeStatus == 'REOPEN') {
                        return data + " <span class='fa fa-repeat text-warning'  style='padding-left: 5px;'  ></span>";
                    }
                    return data;
                }
            },
            {"data": "vehicleNo"},
            {"data": "policyNumberValue"},
            {
                "data": "claimStatusDesc", "className": "text-center", "render": function (data, type, obj, meta) {
                    if (obj.investigationStatus == 'C') {
                        data = data + " <span class='fa fa fa-check-circle text-success'  style='padding-left: 5px;' title='Investigation completed' ></span>";
                    } else if (obj.investigationStatus != 'N') {
                        data = data + " <span class='fa fa fa-question-circle text-warning'  style='padding-left: 5px;' title='Investigation arranged' ></span>";

                    }

                    return data;
                }
            },
            {"data": "calSheetStatus"},
            {"data": "accidentDate"},
            {
                "data": "assignUser", "className": "text-left", "render": function (data, type, obj, meta) {
                    if (obj.assignUser != '') {
                        data = "<span class='fa fa-user text-success font-weight-bold' title='File Assign User' style='padding-right: 5px;'  ></span>" + obj.assignUser;
                    } else {
                        data = "<span class='fa fa-user text-danger font-weight-bold' title='' style='padding-right: 5px;'  ></span>" + obj.assignUser;
                    }
                    return data;
                }
            },
            {"data": "assignDateTime"},
            {
                "data": "liabilityAssignUser",
                "className": "text-left",
                "render": function (data, type, obj, meta) {
                    if (obj.liabilityAssignUser != '') {
                        data = "<span class='fa fa-user text-success font-weight-bold' title='Liability Approval Assign User' style='padding-right: 5px;'  ></span>" + obj.liabilityAssignUser;
                    } else {
                        data = "<span class='fa fa-user text-danger font-weight-bold' title='' style='padding-right: 5px;'  ></span>" + obj.liabilityAssignUser;
                    }
                    return data;
                }
            },
            {"data": "liabilityAssignDatetime"},
            {
                "data": "intLiabilityAssignUser",
                "className": "text-left",
                "render": function (data, type, obj, meta) {
                    if (obj.intLiabilityAssignUser != '') {
                        data = "<span class='fa fa-user text-success font-weight-bold' title='Initial Liability Approval Assign User' style='padding-right: 5px;'  ></span>" + obj.intLiabilityAssignUser;
                    } else {
                        data = "<span class='fa fa-user text-danger font-weight-bold' title='' style='padding-right: 5px;'  ></span>" + obj.intLiabilityAssignUser;
                    }
                    return data;
                }
            },
            {"data": "intLiabilityAssignDatetime"},
            // {
            //     "data": "documentStatus", "className": "text-center", "render": function (data, type, obj, meta) {
            //
            //
            //         if (obj.documentStatus == 'Y') {
            //             data = "<span class='fa fa-check text-success font-weight-bold' title='Document Complete' style='padding-left: 5px;'  ></span>";
            //         } else {
            //             data = "<span class='fa fa-times text-warning font-weight-bold' title='Document Pending' style='padding-left: 5px;'  ></span>";
            //         }
            //         return data;
            //     }
            // },
            // {
            //     "data": "documentCheck", "className": "text-center", "render": function (data, type, obj, meta) {
            //
            //
            //         if (obj.documentCheck == 'Y') {
            //             data = "<span class='fa fa-check text-success font-weight-bold' title='Document Check Completed' style='padding-left: 5px;'  ></span>";
            //         } else {
            //             data = "<span class='fa fa-times text-warning font-weight-bold' title='Document Check Pending' style='padding-left: 5px;'  ></span>";
            //         }
            //         return data;
            //     }
            // },
            // {
            //     "data": "liabilityStatus", "className": "text-center", "render": function (data, type, obj, meta) {
            //
            //
            //         if (obj.liabilityStatus == 'A') {
            //             data = "<span class='fa fa-check text-success font-weight-bold' title='Liability Approved' style='padding-left: 5px;'  ></span>";
            //         } else {
            //             data = "<span class='fa fa-times text-warning font-weight-bold' title='Liability Approval Pending' style='padding-left: 5px;'  ></span>";
            //         }
            //         return data;
            //     }
            // },
            // {
            //     "data": "finalizeStatus", "className": "text-center", "render": function (data, type, obj, meta) {
            //
            //
            //         if (obj.finalizeStatus == 'Y') {
            //             data = "<span class='fa fa-check text-success font-weight-bold' title='Document Upload Complete' style='padding-left: 5px;'  ></span>";
            //         } else {
            //             data = "<span class='fa fa-times text-warning font-weight-bold' title='Document Upload Pending' style='padding-left: 5px;'  ></span>";
            //         }
            //         return data;
            //     }
            // },
            {
                "data": "acr", "className": "text-right", "render": function (data, type, obj, meta) {
                    var val = accounting.formatNumber(data, 2, ",", ".");
                    return val;

                }
            },
            {
                "data": "presentReverseAmount",
                "className": "text-right",
                "render": function (data, type, obj, meta) {
                    var val = accounting.formatNumber(data, 2, ",", ".");
                    return val;

                }
            },
            {
                "data": "partialLoss", "className": "text-center", "render": function (data, type, obj, meta) {

                    if (obj.partialLoss == '2') {
                        return "<span class='fa fa-car text-danger' title='Total Loss' style='padding-left: 5px;'  ></span>";
                    } else if (obj.partialLoss == '1') {
                        return "<span class='fa fa-car text-dark' title='Partial loss' style='padding-left: 5px;'  ></span>";
                    }
                    return '';

                }
            },
            {
                "data": "claimNo", "render": function (data, type, obj, meta) {

                    data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.txnId + "," + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                    return data;
                }
            }
        ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {

            if (obj.isDoubt == "Y") {//DR
                $(nRow).addClass('badge-danger');
            } else if (obj.isOnSiteOffer == "Y") {//FW
                $(nRow).addClass('badge-warning');
            } else if (obj.claimStatus == "3") {//AS
                $(nRow).addClass('badge-success');
            } else if (obj.claimStatus == "32") {//RE
                $(nRow).addClass('badge-danger');
            } else if (obj.claimStatus == "30") {//AS PE
                $(nRow).addClass('badge-warning');
            } else if (obj.claimStatus == "31") {//DRAFT & ASSIGN
                $(nRow).addClass('badge-dark');
            } else if (obj.v_status == "30") {//AS
                $(nRow).addClass('badge-secondary');
            } else {
                // $(nRow).addClass('badge-danger');
            }

        }
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function enableCloseBtn() {

    var elements = document.getElementsByName('checkBox')

    for (var i = 0; i < elements.length; i++) {
        var id = elements[i].getAttribute('id');

        if (document.getElementById(id).checked) {
            $("#claimCloseDiv").show();
            return;
        } else {
            $("#claimCloseDiv").hide();
        }
    }
}

function search() {
    table.ajax.reload();
    $(".checkBtn").prop("checked", false);
    $("#selectAll").prop("checked", false);
    $("#claimCloseDiv").hide();
    $("#N_DAYS").val($("#cmbNoOfDays").val());
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
