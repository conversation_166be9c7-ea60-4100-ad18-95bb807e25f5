/**
 * Created by a<PERSON><PERSON> on 5/2/18.
 */


$(document).ready(function () {


    // FORM VALIDATION FEEDBACK ICONS
    // =================================================================


    //tableTypes

    // =================================================================


    $('#frmFinancialInterest')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {

                financialInterest: {
                    validators: {
                        callback: dropDownValidationForText
                    }
                }, leasingRefNo: {
                    validators: {
                        callback: dropDownValidation
                    }
                }

            }
        })

        .on('success.form.fv', function (e) {
            // Prevent form submission
            e.preventDefault();

            var claimNo = $('#claimNo').val();
            var financial = $('#financialInterest').val();
            var leasingRefNo = $('#leasingRefNo').val();

            if (financial != "0" && leasingRefNo != "") {
                $.ajax({
                    url: contextPath + "/ClaimHandlerController/updateFinancialInfo?claimNo=" + claimNo + "&financialInterest=" + financial + "&leasingRefNo=" + leasingRefNo,
                    type: 'POST',
                    success: function (result) {
                        var obj = JSON.parse(result);

                        if (obj != "") {
                            notify(obj, "success");
                            specialRemarksDiv();
                            logdetails();
                        } else {
                            notify("Can not be updated", "danger");
                        }

                    }
                });
            }
        });

    //
    // $('#frmRemark')
    //     .formValidation({
    //         framework: 'bootstrap',
    //         excluded: ':disabled',
    //         icon: {
    //             valid: 'glyphicon glyphicon-ok',
    //             invalid: 'glyphicon glyphicon-remove',
    //             validating: 'glyphicon glyphicon-refresh'
    //         },
    //         fields: {
    //             remark: {
    //                 validators: {
    //                     notEmpty: {
    //                         message: 'This field is required and cannot be empty.'
    //                     }
    //                 }
    //             }
    //
    //         }
    //     }).on('success.form.fv', function (e) {
    //     // Prevent form submission
    //     var $form = $(e.target);
    //     e.preventDefault();
    //
    //     var $button = $form.data('formValidation').getSubmitButton();
    //     var type = $button.attr('value');
    //
    //     var remark = $('#remark').val();
    //     var formData = $('#frmRemark').serialize();
    //     if (remark != '') {
    //         $.ajax({
    //             url: contextPath + "/ClaimHandlerController/saveRemark?remarkType=" + type,
    //             type: 'POST',
    //             data: formData,
    //             success: function (result) {
    //                 var obj = JSON.parse(result);
    //
    //                 if (obj != "") {
    //                     notify(obj, "success");
    //                     specialRemarksDiv();
    //                     logdetails();
    //                 } else {
    //                     notify("Can not be updated", "danger");
    //                 }
    //
    //             }
    //         });
    //     }
    //
    // });


    $('#frmLiabilityCheck')
        .formValidation({
            framework: 'bootstrap',
            excluded: ':disabled',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                isLcChk1: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required .'
                        }
                    }
                }, isLcChk2: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required.'
                        }
                    }
                }, isLcChk3: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required .'
                        }
                    }
                }, isLcChk4: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required .'
                        }
                    }
                }, isLcChk5: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required .'
                        }
                    }
                }, isLcChk6: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required .'
                        }
                    }
                }, isLcChk7: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required .'
                        }
                    }
                }, isLcChk8: {
                    validators: {
                        notEmpty: {
                            message: 'This field is required .'
                        }
                    }
                }

            }
        }).on('success.form.fv', function (e) {
        // Prevent form submission
        var $form = $(e.target);
        e.preventDefault();

        var $button = $form.data('formValidation').getSubmitButton();
        var type = $button.attr('value');

        updateLiabilityCheckList();


    });

});





