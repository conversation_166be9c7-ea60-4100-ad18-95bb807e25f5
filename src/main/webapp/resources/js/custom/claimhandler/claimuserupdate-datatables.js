/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;
var isSearch = 0;

function viewClaimDetails(txnNo, assignUser, refNo) {
    console.log(assignUser + "55");
    $.ajax({
        type: 'POST',
        url: contextPath + "/LiabilityUserUpdateController/search?txnId=" + txnNo,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
            var obj = JSON.parse(data);
            $("#panelUser").modal({
                backdrop: 'static',
                keyboard: false
            });

            $('#txnId').val(refNo);
            $('#claimNo').val(obj.claimNo);
            $('#claimNos').val($('#claimNo').val());
            $('#currentUser').val(assignUser);

            // var chk = assignUser;
            //
            // $("#assignUser option").filter(function () {
            //     return $.trim($(this).text()) == chk
            // }).remove().trigger("chosen:updated");

        },
        error: function (response) {
            swal(response.responseText);
        }

    });
}

$(window).on('load', function () {

    $("#assignUserName").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });
    $("#assignUser").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });

    // loadList();

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;
    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 5}, {"orderable": false, "targets": 8}],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],

        "ajax": {
            "url": contextPath + "/LiabilityUserUpdateController/claimHandlerUserList",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.assignUserType = $("#liabilityType").val();
                d.assignUserName = $("#assignUserName").val();
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtLocation = $("#txtLocation").val();
                d.txtV_status = $("#txtV_status").val();
                d.txtFileStatus = $("#txtFileStatus").val();
                d.txtLiabilityStatus = $("#txtLiabilityStatus").val();
                d.txtFinalizedStatus = $("#txtFinalizedStatus").val();
                d.isSearch = isSearch;

            }
        },
        "columns": [
            {"data": "refNo"},
            {"data": "index"},
            {"data": "claimNo"},
            {"data": "vehicleNo"},
            {"data": "policyNo"},
            // {"data": "claimStatus"},
            {"data": "assignUser"},
            {"data": "assignDateTime"},
            {"data": "claimStatusDesc"},
            {
                "data": "claimNo", "render": function (data, type, obj, meta) {

                    data = '<button class="btn-primary btn" type="button" onclick="viewClaimDetails(\'' + obj.claimNo + '\',\'' + obj.assignUser + '\',\'' + obj.refNo + '\')" ><i class="fa fa-edit"></i></button>';
                    return data;
                }
            }
        ]
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    isSearch = 1;
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
