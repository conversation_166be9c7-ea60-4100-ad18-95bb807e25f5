/**
 * <AUTHOR>
 */

var table;

function viewPanelMembers(claimNo) {
    $('#lblClaimNo').text(claimNo);
    $('#panelMembers').html('');

    var appendDiv =
        "<table style=\"width: 100%;\" class=\"table table-sm table-hover table-responsive\">\n" +
        "<thead>\n" +
        "<tr>\n" +
        "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">ID</th>\n" +
        "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 8%;\">User Name\n" +
        "    </th>\n" +
        "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Last Updated\n" +
        "    </th>\n" +
        "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Status\n" +
        "    </th>\n" +
        "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Status Updated Time\n" +
        "    </th>\n" +
        "    <th scope=\"col\" class=\"tbl_row_header text-center\" style=\"width: 6%;\">Action\n" +
        "    </th>\n" +
        "</tr>\n" +
        "</thead>\n" +
        "<tbody>";
    $.ajax({
        url: contextPath + "/ClaimHandlerController/getPanelUsers?N_CLAIM_NO=" + claimNo,
        type: 'POST',
        success: function (result) {
            var panelMembersList = JSON.parse(result);

            for (i = 0; i < panelMembersList.length; i++) {
                var id = panelMembersList[i].id;
                var userName = panelMembersList[i].userName;
                var lastUpdated = panelMembersList[i].lastUpdated;
                var isAssigned = panelMembersList[i].isAssigned;
                var status = panelMembersList[i].status == 'P' ? 'PENDING' :
                    panelMembersList[i].status == 'A' ? 'APPROVED' :
                        panelMembersList[i].status == 'R' ? 'REJECTED' :
                            panelMembersList[i].status == 'D' ? 'RETURNED' : 'N/A';
                var actionDateTime = panelMembersList[i].actionDateTime;
                var userStat = panelMembersList[i].userStatus;

                if (isAssigned) {
                    appendDiv+= userStat == 'A' ? "<tr><td class=\"text-center\">" + id + "</td>\n" +
                        "<td class=\"text-center\">" + userName + "</td>\n" +
                        "<td class=\"text-center\">" + lastUpdated + "</td>\n" +
                        "<td class=\"text-center\">" + status + "</td>\n" +
                        "<td class=\"text-center\">" + actionDateTime + "</td>\n" +
                        "<td class=\"text-center\"><label class='switch'><input id='check' value='" + userName + "' type='checkbox' checked onclick='markPanelMember(this)'><span class='customSlider'></span></label></td></tr>" :
                        "<tr><td class=\"text-center\">" + id + "</td>\n" +
                        "<td class=\"text-center\">" + userName + "</td>\n" +
                        "<td class=\"text-center\">" + lastUpdated + "</td>\n" +
                        "<td class=\"text-center\">" + status + "</td>\n" +
                        "<td class=\"text-center\">" + actionDateTime + "</td>\n" +
                        "<td class=\"text-center\"><label class='switch'><input id='check' value='" + userName + "' type='checkbox' checked disabled onclick='markPanelMember(this)'><span class='customSlider'></span></label></td></tr>"
                } else {
                    appendDiv+= userStat == 'A' ? "<tr><td class=\"text-center\">" + id + "</td>\n" +
                        "<td class=\"text-center\">" + userName + "</td>\n" +
                        "<td class=\"text-center\">" + lastUpdated + "</td>\n" +
                        "<td class=\"text-center\">" + status + "</td>\n" +
                        "<td class=\"text-center\">" + actionDateTime + "</td>\n" +
                        "<td class=\"text-center\"><label class='switch'><input id='check' value='" + userName + "' type='checkbox' onclick='markPanelMember(this)'><span class='customSlider'></span></label></td></tr>" :
                        "<tr><td class=\"text-center\">" + id + "</td>\n" +
                        "<td class=\"text-center\">" + userName + "</td>\n" +
                        "<td class=\"text-center\">" + lastUpdated + "</td>\n" +
                        "<td class=\"text-center\">" + status + "</td>\n" +
                        "<td class=\"text-center\">" + actionDateTime + "</td>\n" +
                        "<td class=\"text-center\"><label class='switch'><input id='check' value='" + userName + "' type='checkbox' disabled onclick='markPanelMember(this)'><span class='customSlider'></span></label></td></tr>"
                }
            }

            appendDiv += "</tbody>"
            $('#panelMembers').append(appendDiv);

            $('#panelUsers').modal({
                backdrop: 'static',
                keyboard: false
            });
        }
    });
}

$(window).on('load', function () {
    $.fn.DataTable.ext.pager.numbers_length = 5;

    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [
            {
                "visible": false,
                "targets": 0,
                "orderable": false
            }
        ],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],

        "ajax": {
            "url": contextPath + "/ClaimHandlerController/mainPanelList",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.userId = $("#inputUserId").val();
                d.txtRequestDate = $("#inputDateTime").val();
                d.txtClaimNumber = $("#txtClaimNo").val();
            }
        },
        "columns": [
            {"data": "index"},
            {"data": "claimNo", "className": "text-center"},
            {"data": "forwardedUser", "className": "text-center"},
            {"data": "forwardedDateTime", "className": "text-center"},
            {"data": "lastUpdatedDateTime", "className": "text-center"},
            {"data": "assignedPanelMembers", "className": "text-center"},
            {
                "data": "status", "className": "text-center", "render": function (data, type, obj, meta) {
                    if (obj.status == 'P') {
                        data = "<span class='badge font-weight-bold badge-warning'>PENDING</span>";
                    } else if (obj.status == 'A'){
                        data = "<span class='badge font-weight-bold badge-primary'>APPROVED</span>";
                    }else if (obj.status == 'R'){
                        data = "<span class='badge font-weight-bold badge-danger'>REJECTED</span>";
                    }else if (obj.status == 'D'){
                        data = "<span class='badge font-weight-bold badge-secondary'>RETURNED</span>";
                    }
                    return data;
                }
            },
            {
                // "data": "userId", "className": "text-center", "render": function (data, type, obj, meta) {
                "data": "claimNo", "className": "text-center", "render": function (data, type, obj, meta) {
                    data = "<button class='btn-primary btn' type='button' onclick='viewPanelMembers(" + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                    /*if (obj.status == "A") {
                        data = "<label class='switch'><input id='check' type='checkbox' checked onclick='changeActiveStatus(" + obj.userCode + ", this.checked)'><span class='customSlider'></span></label>";
                    } else {
                        data = "<label class='switch'><input id='check' type='checkbox' onclick='changeActiveStatus(" + obj.userCode + ", this.checked)'><span class='customSlider'></span></label>";
                    }*/
                    return data;
                }
            }

        ]
    });
});

/*
function changeActiveStatus(userCode, checkedStatus) {
    var status = '';
    if (checkedStatus == true) {
        status = 'ACTIVE';
    } else {
        status = 'INACTIVE';
    }
    bootbox.confirm({
        message: "Do you want to mark this user as " + status,
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-primary'
            },
            cancel: {
                label: 'No',
                className: 'btn-secondary'
            }
        },
        callback: function (result) {
            if (result == true) {
                $.ajax({
                    url: contextPath + "/ClaimHandlerController/markMainPanel",
                    data: {
                        N_USRCODE: userCode,
                        IS_ACTIVE: checkedStatus,
                    },
                    type: 'POST',
                    success: function (result) {
                        var response = JSON.parse(result);
                        if (response == "SUCCESS") {
                            notify("Status Changed Successfully", "success");
                        } else {
                            notify("Failed to Change Status", "danger");
                        }
                        table.ajax.reload();
                    }
                })
            } else {
                table.ajax.reload();
            }
        }
    });
}*/
