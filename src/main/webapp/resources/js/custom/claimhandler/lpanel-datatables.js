/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/05/2021.
 */


var table;

function viewClaimDetails(txnNo, claimNo) {
    showLoader();
    var url = contextPath + "/ClaimHandlerController/viewEdit?TYPE=103";
    $("#P_N_CLIM_NO").val(claimNo);

    document.getElementById('frmForm').action = url;
    document.getElementById('frmForm').submit();
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"orderable": false, "targets": 0}],
        responsive: true,
        searching: false,

        "order": [[1, "desc"]],

        "ajax": {
            "url": contextPath + "/ClaimHandlerController/lPanel",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtRejectionPanelType = $("#txtRejectionPanelType").val();
                d.txtDmaker = $("#txtDmaker").val();
                d.txtRejection = $("#txtRejection").val();
                d.txtLPanelStatus = $("#txtLPanelStatus").val();

            }
        },
        "columns": [
            {"data": "index"},
            {"data": "claimNo"},
            {"data": "vehicleNo"},
            {"data": "isfClaimNo"},
            {"data": "policyNumberValue"},
            {"data": "policyChannelType"},
            {"data": "rejectedReason"},
            {"data": "accidentDate"},
            {"data": "dMakingAssignUid"},
            {"data": "assignDateTime"},
            {"data": "liabilityAssignUser"},
            {"data": "liabilityAssignDatetime"},
            {"data": "intLiabilityAssignUser"},
            {"data": "intLiabilityAssignDatetime"},
            {"data": "acr"},
            {"data": "isRejectionAttached"},
            {
                "data": "claimNo", "render": function (data, type, obj, meta) {

                    data = "<button class='btn-primary btn' type='button' onclick='viewClaimDetails(" + obj.txnId + "," + obj.claimNo + ")' ><i class='fa fa-eye'></i></button>";
                    return data;
                }
            }
        ]
    });

    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });
});

function search() {
    if ($('#txtFromDate').val() === '' || $('#txtToDate').val() === ''  ){
        var today = new Date();
        var dd = String(today.getDate()).padStart(2, '0');
        var mm = String(today.getMonth() + 1).padStart(2, '0');
        var yyyy = today.getFullYear();
        $('#txtFromDate').val(yyyy+'-'+mm+'-'+dd+' '+'00:00');
        $('#txtToDate').val(yyyy+'-'+mm+'-'+dd+' '+'23:59');
    }
    table.ajax.reload();
    return false;
}
