/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewLeaveDetails(userId) {
    $.ajax({
        type: 'POST',
        url: contextPath + "/ClaimUserLeaveController/search?userId=" + userId,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
            var obj = JSON.parse(data);
            $("#toDateTime").on("dp.change", function (e) {
                $('#frmClaimPanelUser').formValidation('revalidateField', 'toDate');
            });
            $("#fromDateTime").on("dp.change", function (e) {
                $('#frmClaimPanelUser').formValidation('revalidateField', 'fromDate');
            });
            // alert(data);
            $("#fromDateTime").datetimepicker({
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#toDateTime").datetimepicker({
                format: 'YYYY-MM-DD HH:mm',
                icons: {
                    time: "fa fa-clock-o",
                    date: "fa fa-calendar",
                    up: "fa fa-arrow-up",
                    down: "fa fa-arrow-down"
                }
            });

            $("#fromDateTime").data('DateTimePicker').widgetPositioning(
                {
                    horizontal: 'auto',
                    vertical: 'bottom'
                }
            );

            $("#toDateTime").data('DateTimePicker').widgetPositioning(
                {
                    horizontal: 'auto',
                    vertical: 'bottom'
                }
            );

            $("#panelUser").modal({
                backdrop: 'static',
                keyboard: false
            });
            $('#userId').val(userId);
            $('#fromDateTime').val(obj.fromDateTime);
            $('#toDateTime').val(obj.toDateTime);
            $('#leaveType').val(obj.leaveType);

        },
        error: function (response) {
            swal(response.responseText);
        }

    });
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;

    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [
            {
                "visible": false,
                "targets": 0,
                "orderable": false
            },
            {
                "orderable": false,
                "targets": 1
            },
            {
                "orderable": false,
                "targets": 5
            },
            {
                "orderable": false,
                "targets": 9
            }
        ],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],

        "ajax": {
            "url": contextPath + "/ClaimUserLeaveController/searchAllLeave",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.userId = $("#userIds").val();
                d.leaveType = $("#leaveTypes").val();
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.type = $("#type").val();
            }
        },
        "columns": [
            // {"data": "id"},

            {"data": "userId"},
            {"data": "index"},
            {"data": "userId"},
            {"data": "fromDateTime"},
            {"data": "toDateTime"},
            {"data": "leaveType"},
            {"data": "inputUser"},
            {"data": "inputDateTime"},
            {
                "data": "firstName", "render": function (data, type, obj) {
                return "<div>" + obj.firstName + " " + obj.lastName + "</div>"
            }
            },
            {
                "data": "userId", "render": function (data, type, obj, meta) {
                data = '<button class="btn-primary btn" type="button" onclick="viewLeaveDetails(\'' + obj.userId + '\');" ><i class="fa fa-edit"></i></button>';
                return data;
            }
            }

        ]
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
