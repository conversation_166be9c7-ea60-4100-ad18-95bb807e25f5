/**
 * Created by Tharaka on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewLeaveDetails(supplierId) {
    $.ajax({
        type: 'POST',
        url: contextPath + "/SupplierDetailsController/searchSupplierDetails?supplierId=" + supplierId,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
            var obj = JSON.parse(data);
            $("#exampleModal").modal({
                backdrop: 'static',
                keyboard: false
            });
            $('#supplierId').val(supplierId);
            $('#supplerName').val(obj.supplerName);
            $('#supplierAddressLine1').val(obj.supplierAddressLine1);
            $('#supplierAddressLine2').val(obj.supplierAddressLine2);
            $('#supplierAddressLine3').val(obj.supplierAddressLine3);
            $('#contactNo').val(obj.contactNo);
            $('#contactPerson').val(obj.contactPerson);
            $('#email').val(obj.email);
            $('#recordStatus').val(obj.recordStatus);
            $('#inputUserId').val(obj.inputUserId);

        },
        error: function (response) {
            swal(response.responseText);
        }

    });
}

var isSearch = 0;

$(window).on('load', function () {

    // $.ajax({
    //     type: 'POST',
    //     url: contextPath + "/SparePartDatabaseController/SupplierList",
    //     cache: false,
    //     contentType: false,
    //     processData: false,
    //     success: function (data) {
    //
    //     },
    //     error: function (response) {
    //         swal(response.responseText);
    //     }
    //
    // });


    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 5}],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],

        "ajax": {
            "url": contextPath + "/SupplierDetailsController/searchAllsparePart?",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.supplerName = $("#supplerName1").val();
                d.isSearch = isSearch;
            }
        },
        "columns": [
            // {"data": "id"},

            {"data": "supplierId"},
            {"data": "index"},
            {"data": "supplerName"},
            {
                "data": "supplierAddressLine1", "render": function (data, type, obj) {
                    return "<div>" + obj.supplierAddressLine1 + " " + obj.supplierAddressLine2 + " " + obj.supplierAddressLine3 + "</div>"
                }
            },

            {"data": "contactNo"},
            {"data": "contactPerson"},
            {"data": "email"},
            {"data": "recordStatus"},
            {"data": "inputUserId"},
            {
                "data": "supplierId", "render": function (data, type, obj, meta) {
                    data = '<button class="btn-primary btn" type="button" onclick="viewLeaveDetails(\'' + obj.supplierId + '\');" ><i class="fa fa-edit"></i></button>';
                    return data;
                }
            }

        ]
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });

});

function search() {
    isSearch = 1;
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
