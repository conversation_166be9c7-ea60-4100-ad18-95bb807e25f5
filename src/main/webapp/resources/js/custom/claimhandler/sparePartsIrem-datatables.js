/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewSparePart(id) {
    // alert(id);
    $.ajax({
        type: 'POST',
        url: contextPath + "/SparePartItemController/searchSparePartItem?refNo=" + id,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
            var obj = JSON.parse(data);
            $("#panelUser").modal({
                backdrop: 'static',
                keyboard: false
            });

            $('#sparePartRefNo').val(id);
            $('#sparePartName').val(obj.sparePartName);
            $('#recordStatus').val(obj.recordStatus);
            $('#inputUserId ').val(obj.inputUserId);
            $('#inputDateTime').val(obj.inputDateTime);

            $('#frmSparePartItem').data('formValidation').enableFieldValidators('sparePartName', false);
        },
        error: function (response) {
            swal(response.responseText);
        }

    });
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 5},
            {"orderable": false, "targets": 6}],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],

        "ajax": {
            "url": contextPath + "/SparePartItemController/searchAllSparePartItem",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.recordsStatus = $("#recordsStatus").val();
                d.sparePartsName = $("#sparePartsName").val();
            }
        },
        "columns": [
            {"data": "sparePartRefNo"},
            {"data": "index"},
            {"data": "sparePartName"},
            {
                "data": "recordStatus", "render": function (data, type, obj, meta) {
                    if (obj.recordStatus == 'A') {
                        return 'Active'
                    } else {
                        return 'Remove'
                    }
                }
            },
            {"data": "inputUserId"},
            {"data": "inputDateTime"},

            {
                "data": "sparePartRefNo", "render": function (data, type, obj, meta) {

                    data = "<button class='btn-primary btn' type='button' onclick='viewSparePart(" + obj.sparePartRefNo + ")' ><i class='fa fa-edit'></i></button>";
                    return data;
                }
            }

        ]
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
