/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;
var isSearch = 0;
$("#liabilityType,#assignUserName,#assignUser,#assignUserBulkReassign").chosen({
    no_results_text: "No results found!",
    width: "100%"
});
function viewClaimDetails(txnNo, assignUser, refNo) {
    $.ajax({
        type: 'POST',
        url: contextPath + "/LiabilityUserUpdateController/search?txnId=" + txnNo,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
            var obj = JSON.parse(data);
            $("#panelUser").modal({
                backdrop: 'static',
                keyboard: false
            });

            $('#txnId').val(refNo);
            $('#claimNo').val(obj.claimNo);
            $('#claimNos').val($('#claimNo').val());
            $('#currentUser').val(assignUser);

            // var chk = assignUser;
            //
            // $("#assignUser option").filter(function () {
            //     return $.trim($(this).text()) == chk
            // }).remove().trigger("chosen:updated");

        },
        error: function (response) {
            swal(response.responseText);
        }

    });
}

function viewMofaReassignDetails(txnNo, assignUser, refNo , claimStatus) {
    var levelReassign = $("#txtV_status").val() == 57;
    $.ajax({
        url: contextPath + "/LiabilityUserUpdateController/searchMofa",
        data: {
            assignUser,
            txnNo,
            levelReassign,
            claimStatus
        },
        type: 'POST',
        success: function (result) {
            var response = JSON.parse(result);
            if (response == "NOT FOUND") {
                notify('No Users Found to Reassign File', 'danger');
                return;
            }
            if (response.length > 1) {
                $("#mofaLevel").val(response[response.length - 2]);
                $("#mofalevelNo").val(response[response.length - 1]);
            } else {
                notify('No Users Found to Reassign File', 'danger');
                return;
            }
            $("#panelUser").modal({
                backdrop: 'static',
                keyboard: false,
            });
            $("#assignUser option").remove();
            var selOpts = "<option value='0'>Please Select</option>";
            for (i = 0; i < response.length - 2; i++) {
                var val = response[i];

                selOpts += "<option value='" + val + "'>" + val + "</option>";
            }

            $('#assignUser').append(selOpts).trigger("chosen:updated");
            $('#txnId').val(refNo);
            $('#claimNo').val(txnNo);
            $('#claimNos').val($('#claimNo').val());
            $('#currentUser').val(assignUser);
        }
    });
}

function viewBulkClaimDetails(txnNo, assignUser, refNo) {
    $.ajax({
        type: 'POST',
        url: contextPath + "/LiabilityUserUpdateController/search?txnId=" + txnNo,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
            var obj = JSON.parse(data);
            $("#BulReassignpanelUser").modal({
                backdrop: 'static',
                keyboard: false
            });

            $('#txnId').val(refNo);
            $('#claimNo').val(obj.claimNo);
            $('#claimNos').val($('#claimNo').val());
            $('#currentUser').val(assignUser);

            // var chk = assignUser;
            //
            // $("#assignUser option").filter(function () {
            //     return $.trim($(this).text()) == chk
            // }).remove().trigger("chosen:updated");

        },
        error: function (response) {
            swal(response.responseText);
        }

    });
}

$(window).on('load', function () {

    $("#assignUserName").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });
    $("#assignUser").chosen({
        no_results_text: "No results found!",
        width: "100%"
    });

    var URL = contextPath + "/LiabilityUserUpdateController/claimHandlerUserList";

    var viewType = type;

    if (viewType == 200) {
        URL = contextPath + "/LiabilityUserUpdateController/mofaUserList";
    }

    // loadList();

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;
    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 1, "orderable": false}, {
            "orderable": false,
            "targets": 2
        }, {"orderable": false, "targets": 6}, {"orderable": false, "targets": 7}, {
            "orderable": false,
            "targets": 0
        }, {"orderable": false, "targets": 9}],
        responsive: true,
        searching: false,
        "order": [[1, "desc"]],

        "ajax": {
            "url": URL,
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.assignUserType = $("#liabilityType").val();
                d.assignUserName = $("#assignUserName").val();
                d.txtFromDate = $("#txtFromDate").val();
                d.txtToDate = $("#txtToDate").val();
                d.txtClaimNumber = $("#txtClaimNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtLocation = $("#txtLocation").val();
                d.txtV_status = $("#txtV_status").val();
                d.txtFileStatus = $("#txtFileStatus").val();
                d.txtLiabilityStatus = $("#txtLiabilityStatus").val();
                d.txtFinalizedStatus = $("#txtFinalizedStatus").val();
                d.txtCalsheetStatus = $("#txtCalsheetStatus").val();
                d.txtSupplierOrderStatus = $("#txtSupplierOrderStatus").val();
                d.assignUserLevel = $("#selMofaLevel").val();
                d.isSearch = isSearch;


            }
        },
        "columns": [
            {
                'data': null,
                'render': function (data, type, obj, row) {
                    var checkBox = "<input type='checkbox' class='checkBtn' id='CRB" + data.claimNo + " - " + data.refNo + "' name='checkBox' onclick='enableReassignBtn()'/>";
                    checkBox = checkBox + "<input type='hidden'  id='CRB" + data.claimNo + " - " + data.assignUser + "' name='ClaimNoAndAssignUserBox' />";
                    checkBox = checkBox + "<input type='hidden' name='claimNo' id='" + data.claimNo + "' />";
                    return checkBox;
                }
            },
            {"data": "refNo"},
            {"data": "index"},
            {"data": "claimNo"},
            {"data": "vehicleNo"},
            {"data": "policyNo"},
            {"data": "calSheetStatus"},
            // {"data": "claimStatus"},
            {"data": "assignUser"},
            {"data": "assignDateTime"},
            {"data": "claimStatusDesc"},
            {
                "data": "finalizeStatus",
                "className": "text-center",
                "render": function (data, type, obj, meta) {

                    if (obj.closeStatus == 'CLOSE') {
                        data = "<span>Closed</span>";
                    } else if (obj.closeStatus == 'REOPEN') {
                        data = "<span>Reopened</span>";
                    } else if (obj.closeStatus == 'SETTLE') {
                        data = "<span>Settled</span>";
                    } else if (obj.closeStatus == 'SETTLE_PENDING') {
                        data = "<span>Settle Pending</span>"
                    } else {
                        data = "<span>Pending</span>"
                    }
                    return data;
                }
            },
            {
                "data": "claimNo",
                "render": function (data, type, obj, meta) {
                    if (viewType == 200) {
                        data = '<button class="btn-primary btn" type="button" onclick="viewMofaReassignDetails(\'' + obj.claimNo + '\',\'' + obj.assignUser + '\',\'' + obj.refNo + '\',\'' + obj.claimStatus + '\')" ><i class="fa fa-edit"></i></button>';
                    } else {
                        data = '<button class="btn-primary btn" type="button" onclick="viewClaimDetails(\'' + obj.claimNo + '\',\'' + obj.assignUser + '\',\'' + obj.refNo + '\')" ><i class="fa fa-edit"></i></button>';
                    }
                    return data;
                }
            }
        ]
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});


function enableReassignBtn() {

    var elements = document.getElementsByName('checkBox')

    for (var i = 0; i < elements.length; i++) {
        var id = elements[i].getAttribute('id');

        if (document.getElementById(id).checked) {
            $("#claimReassignDiv").show();
            return;
        } else {
            $("#claimReassignDiv").hide();
        }
    }
}

function search() {
    if ($('#txtV_status').val() == 57) {
        $('#divMofaLevel').hide();
    } else {
        $('#divMofaLevel').show();
    }
    if (type == 200) {
       if ((undefined == $('#assignUserName').val() || $('#assignUserName').val()=='' || $('#assignUserName').val()==0) && (undefined != $('#selMofaLevel').val() && "" != $('#selMofaLevel').val() && $('#selMofaLevel').val()!=0) ){
           notify("Please Select The Assign User","danger");
           return;
       }
    }
        isSearch = 1;
        table.ajax.reload();
        $(".checkBtn").prop("checked", false);
        $("#selectAll").prop("checked", false);
        $("#claimReassignDiv").hide();
        $('#claimNoListDiv').html('');
        $('#claimNoListTxt').val('');
        return false;


}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
