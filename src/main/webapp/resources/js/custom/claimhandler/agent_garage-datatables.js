/**
 * Created by Tharaka on 2/16/2016.
 */


// Tables-DataTables.js
// ====================================================================
// This file should not be included in your project.
// This is just a sample how to initialize plugins or components.
//
// - ThemeOn.net -


var table;

function viewGarage(id) {
    showLoader();
    // alert(id);
    $.ajax({
        type: 'POST',
        url: contextPath + "/GarageListController/searchGarage?id=" + id,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
            var obj = JSON.parse(data);
            $("#panelUser").modal({
                backdrop: 'static',
                keyboard: false
            });

            $('#id').val(id);
            $('#gargName').val(obj.gargName);
            $('#gargCode').val(obj.gargCode);
            $('#address1 ').val(obj.address1);
            $('#address2').val(obj.address2);

            $('#address3').val(obj.address3);
            $('#conPerson').val(obj.conPerson);
            $('#conNumber').val(obj.conNumber);
            $('#genTelNo').val(obj.genTelNo);
            $('#status').val(obj.status);

              $('#frmGarage').data('formValidation').enableFieldValidators('garageName', false);
        },
        error: function (response) {
            swal(response.responseText);
        }

    });
}

$(window).on('load', function () {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 5}],
        responsive: true,
        searching: false,
        "order": [[0, "desc"]],

        "ajax": {
            "url": contextPath + "/GarageListController/searchAllGarage",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                //  d.txtFromDate = $("#txtFromDate").val();
            }
        },
        "columns": [
            {"data": "id"},
            {"data": "index"},
            {"data": "gargCode"},
            {"data": "gargName"},

            {
                "data": "Address", "render": function (data, type, obj) {
                    return "<div>" + obj.address1 +" "+ obj.address2 +" "+ obj.address3 + "</div>"
                }
            },
            {"data": "conPerson"},
            {"data": "conNumber"},
            {"data": "genTelNo"},
            {"data": "status"},

            {
                "data": "sparePartRefNo", "render": function (data, type, obj, meta) {

                    data = "<button class='btn-primary btn' type='button' onclick='viewGarage(" + obj.id + ")' ><i class='fa fa-edit'></i></button>";
                    return data;
                }
            }

        ]
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
         var data = table.row(this).data();
         var id = data['n_ref_no'];
         $("#P_N_REF_NO").val(id);
         document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
         document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });


});

function search() {
    table.ajax.reload();
    return false;
}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
    // $( table.cells().nodes() ).removeClass( 'badge-success' );
    for (colIdx = 1; colIdx <= noColumns; colIdx++) {
        $(table.column(colIdx).nodes()).addClass(cssClass);

    }*/
}
