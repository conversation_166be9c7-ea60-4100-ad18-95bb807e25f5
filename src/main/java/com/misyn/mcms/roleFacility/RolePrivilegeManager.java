/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.roleFacility;

import com.misyn.mcms.admin.User;
import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class RolePrivilegeManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(RolePrivilegeManager.class);
    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static RolePrivilegeManager rolePrivilegeManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    /**
     * Default constructor
     */
    public RolePrivilegeManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized RolePrivilegeManager getInstance() {
        if (rolePrivilegeManager == null) {
            rolePrivilegeManager = new RolePrivilegeManager();
        }
        return rolePrivilegeManager;
    }

    //===================RolePrivilege Insert Method=======================================
    private synchronized int insertRolePrivilege(RolePrivilege rolePrivilege) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "INSERT INTO prev_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?)";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, rolePrivilege.getN_usrtype());
            ps.setInt(2, rolePrivilege.getN_prgid());
            ps.setInt(3, rolePrivilege.getN_comid());
            ps.setInt(4, rolePrivilege.getN_homepage());
            ps.setInt(5, rolePrivilege.getN_mnuid());
            ps.setInt(6, rolePrivilege.getN_itmid());
            ps.setString(7, rolePrivilege.getV_view());
            ps.setString(8, rolePrivilege.getV_input());
            ps.setString(9, rolePrivilege.getV_modify());
            ps.setString(10, rolePrivilege.getV_delete());
            ps.setString(11, rolePrivilege.getV_auth1());
            ps.setString(12, rolePrivilege.getV_auth2());
            ps.setString(13, rolePrivilege.getV_grant());
            ps.setString(14, rolePrivilege.getRecieve1());
            ps.setString(15, rolePrivilege.getV_inpstat());
            ps.setString(16, rolePrivilege.getV_inpuser());
            ps.setString(17, rolePrivilege.getD_inptime());
            ps.setString(18, rolePrivilege.getV_auth1stat());
            ps.setString(19, rolePrivilege.getV_auth1user());
            ps.setString(20, rolePrivilege.getD_auth1time());
            ps.setString(21, rolePrivilege.getV_auth2stat());
            ps.setString(22, rolePrivilege.getV_auth2user());
            ps.setString(23, rolePrivilege.getD_auth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    private synchronized int updateRolePrivilege(RolePrivilege rolePrivilege) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "UPDATE prev_mst SET "
                + "n_usrtype=?,"
                + "n_prgid=?,"
                + "n_comid=?,"
                + "n_homepage=?,"
                + "n_mnuid=?,"
                + "n_itmid=?,"
                + "v_view=?,"
                + "v_input=?,"
                + "v_modify=?,"
                + "v_delete=?,"
                + "v_auth1=?,"
                + "v_auth2=?,"
                + "v_grant=?,"
                + "recieve1=?,"
                + "v_inpstat=?,"
                + "v_inpuser=?,"
                + "d_inptime=?,"
                + "v_auth1stat=?,"
                + "v_auth1user=?,"
                + "d_auth1time=?,"
                + "v_auth2stat=?,"
                + "v_auth2user=?,"
                + "d_auth2time=? "
                + "WHERE "
                + "n_usrtype=? "
                + "AND "
                + "n_prgid=? "
                + "AND "
                + "n_comid=? "
                + "AND "
                + "n_mnuid=? "
                + "AND "
                + "n_itmid=?";


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, rolePrivilege.getN_usrtype());
            ps.setInt(2, rolePrivilege.getN_prgid());
            ps.setInt(3, rolePrivilege.getN_comid());
            ps.setInt(4, rolePrivilege.getN_homepage());
            ps.setInt(5, rolePrivilege.getN_mnuid());
            ps.setInt(6, rolePrivilege.getN_itmid());
            ps.setString(7, rolePrivilege.getV_view());
            ps.setString(8, rolePrivilege.getV_input());
            ps.setString(9, rolePrivilege.getV_modify());
            ps.setString(10, rolePrivilege.getV_delete());
            ps.setString(11, rolePrivilege.getV_auth1());
            ps.setString(12, rolePrivilege.getV_auth2());
            ps.setString(13, rolePrivilege.getV_grant());
            ps.setString(14, rolePrivilege.getRecieve1());
            ps.setString(15, rolePrivilege.getV_inpstat());
            ps.setString(16, rolePrivilege.getV_inpuser());
            ps.setString(17, rolePrivilege.getD_inptime());
            ps.setString(18, rolePrivilege.getV_auth1stat());
            ps.setString(19, rolePrivilege.getV_auth1user());
            ps.setString(20, rolePrivilege.getD_auth1time());
            ps.setString(21, rolePrivilege.getV_auth2stat());
            ps.setString(22, rolePrivilege.getV_auth2user());
            ps.setString(23, rolePrivilege.getD_auth2time());

            ps.setInt(24, rolePrivilege.getN_usrtype());
            ps.setInt(25, rolePrivilege.getN_prgid());
            ps.setInt(26, rolePrivilege.getN_comid());
            ps.setInt(27, rolePrivilege.getN_mnuid());
            ps.setInt(28, rolePrivilege.getN_itmid());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveRolePrivilege(List<RolePrivilege> rolePrivilegesList) {
        int result = 0;
        RolePrivilege rolePrivilege = null;
        int n_usrtype = -1;
        int n_comid = -1;

        int updateCountResult = -1;
        try {

            for (int i = 0; i < rolePrivilegesList.size(); i++) {
                rolePrivilege = rolePrivilegesList.get(i);
                n_usrtype = rolePrivilege.getN_usrtype();
                n_comid = rolePrivilege.getN_comid();

                if (!dbRecordCommonFunction.
                        isRecExists("prev_mst", ""
                                + "n_usrtype=" + n_usrtype + " "
                                + "AND n_prgid=" + rolePrivilege.getN_prgid() + " "
                                + "AND n_comid=" + rolePrivilege.getN_comid() + " "
                                + "AND n_mnuid=" + rolePrivilege.getN_mnuid() + " "
                                + "AND n_itmid=" + rolePrivilege.getN_itmid())) {
                    if (!dbRecordCommonFunction.isIsErrorExsist()) {

                        result = insertRolePrivilege(rolePrivilege);

                    }
                } else {

                    result = updateRolePrivilege(rolePrivilege);
                }

                if (result > 0) {
                    updateCountResult++;
                }
            }
            if (result > 0) {
                int c = updateUserPrivilege(n_comid, n_usrtype);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return updateCountResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteRolePrivilege(List<RolePrivilege> rolePrivilegesList) {
        int result = 0;
        RolePrivilege rolePrivilege = null;
        int n_usrtype = -1;
        int n_comid = -1;

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "DELETE FROM prev_mst WHERE n_usrtype=? AND n_comid=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < rolePrivilegesList.size(); i++) {
                setMsg("");
                rolePrivilege = rolePrivilegesList.get(i);
                n_usrtype = rolePrivilege.getN_usrtype();
                n_comid = rolePrivilege.getN_comid();


                if (!dbRecordCommonFunction.
                        isRecExists("userPrev_mst", ""
                                + "n_usrtype=" + rolePrivilege.getN_usrtype())) {
                    if (!dbRecordCommonFunction.isIsErrorExsist()) {
                        ps = conn.prepareStatement(strSQL);
                        ps.setInt(1, n_usrtype);
                        ps.setInt(2, n_comid);
                        result = ps.executeUpdate();
                        if (result > 0) {
                            updateCountResult++;
                        }
                    }
                    setMsg("Record Delete Successful");
                } else {
                    setMsg("Can not delete " + n_usrtype + ": The user type is in use by the following table -> usr_mst");
                    try {
                        if (conn != null) {
                            conn.rollback();
                        }
                        conn.setAutoCommit(true);
                    } catch (Exception e1) {
                    }
                    return 0;
                }
            }

            conn.commit();
            conn.setAutoCommit(true);


        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private int updateUserPrivilege(int n_comid, int n_usrtype) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "SELECT n_usrcode FROM userprev_mst "
                + "WHERE n_comid=? "
                + "AND "
                + "n_usrtype=? GROUP BY n_usrcode";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, n_comid);
            ps.setInt(2, n_usrtype);

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                strSQL = "DELETE FROM userprev_mst "
                        + "WHERE "
                        + "n_usrcode=? "
                        + "AND "
                        + "n_usrtype=?";
                ps = conn.prepareStatement(strSQL);

                ps.setInt(1, rs.getInt("n_usrcode"));
                ps.setInt(2, n_usrtype);
                result = ps.executeUpdate();

                strSQL = "INSERT INTO userprev_mst ("
                        + "SELECT "
                        + "?, "//n_usrcode
                        + "t1.n_usrtype, "
                        + "t1.n_prgid, "
                        + "t1.n_comid, "
                        + "t1.n_mnuid, "
                        + "t1.n_itmid, "
                        + "t1.v_view, "
                        + "t1.v_input, "
                        + "t1.v_modify, "
                        + "t1.v_delete, "
                        + "t1.v_auth1, "
                        + "t1.v_auth2, "
                        + "t1.v_grant, "
                        + "t1.recieve1, "
                        + "t1.v_inpstat, "
                        + "t1.v_inpuser, "
                        + "t1.d_inptime, "
                        + "t1.v_auth1stat, "
                        + "t1.v_auth1user, "
                        + "t1.d_auth1time, "
                        + "t1.v_auth2stat, "
                        + "v_auth2user, "
                        + "t1.d_auth2time "
                        + "FROM prev_mst as t1 "
                        + "WHERE n_comid=? AND n_usrtype=? AND "
                        + "(t1.v_view='checked' "
                        + "OR "
                        + "t1.v_input='checked' "
                        + "OR "
                        + "t1.v_modify='checked' "
                        + "OR "
                        + "t1.v_delete='checked' "
                        + "OR "
                        + "t1.v_auth1='checked' "
                        + "OR "
                        + "t1.v_auth2='checked' "
                        + "OR "
                        + "t1.v_grant='checked'))";

                ps = conn.prepareStatement(strSQL);

                ps.setInt(1, rs.getInt("n_usrcode"));
                ps.setInt(2, n_comid);
                ps.setInt(3, n_usrtype);
                result = ps.executeUpdate();
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    private synchronized RolePrivilege getRolePrivilege_New_For_Super_User(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {


            rolePrivilege.setN_mnuid(rs.getInt("t2.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t3.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view("");
            rolePrivilege.setV_input("");
            rolePrivilege.setV_modify("");
            rolePrivilege.setV_delete("");
            rolePrivilege.setV_auth1("");
            rolePrivilege.setV_auth2("");
            rolePrivilege.setV_grant("");


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    private synchronized RolePrivilege getRolePrivilege_New(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {

            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrtype"));
            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));
            rolePrivilege.setN_homepage(rs.getInt("t1.n_homepage"));

            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view("");
            rolePrivilege.setV_input("");
            rolePrivilege.setV_modify("");
            rolePrivilege.setV_delete("");
            rolePrivilege.setV_auth1("");
            rolePrivilege.setV_auth2("");
            rolePrivilege.setV_grant("");

            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpstat"));
            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpuser"));
            rolePrivilege.setD_inptime(rs.getString("t1.d_inptime"));
            rolePrivilege.setV_auth1stat(rs.getString("t1.v_auth1stat"));
            rolePrivilege.setV_auth1user(rs.getString("t1.v_auth1user"));
            rolePrivilege.setD_auth1time(rs.getString("t1.d_auth1time"));
            rolePrivilege.setV_auth2stat(rs.getString("t1.v_auth2stat"));
            rolePrivilege.setV_auth2user(rs.getString("t1.v_auth2user"));
            rolePrivilege.setD_auth2time(rs.getString("t1.d_auth2time"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    private synchronized RolePrivilege getRolePrivilege_Modify(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {

            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrtype"));
            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));
            rolePrivilege.setN_homepage(rs.getInt("t1.n_homepage"));

            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view(rs.getString("t1.v_view"));
            rolePrivilege.setV_input(rs.getString("t1.v_input"));
            rolePrivilege.setV_modify(rs.getString("t1.v_modify"));
            rolePrivilege.setV_delete(rs.getString("t1.v_delete"));
            rolePrivilege.setV_auth1(rs.getString("t1.v_auth1"));
            rolePrivilege.setV_auth2(rs.getString("t1.v_auth2"));
            rolePrivilege.setV_grant(rs.getString("t1.v_grant"));

            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpstat"));
            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpuser"));
            rolePrivilege.setD_inptime(rs.getString("t1.d_inptime"));
            rolePrivilege.setV_auth1stat(rs.getString("t1.v_auth1stat"));
            rolePrivilege.setV_auth1user(rs.getString("t1.v_auth1user"));
            rolePrivilege.setD_auth1time(rs.getString("t1.d_auth1time"));
            rolePrivilege.setV_auth2stat(rs.getString("t1.v_auth2stat"));
            rolePrivilege.setV_auth2user(rs.getString("t1.v_auth2user"));
            rolePrivilege.setD_auth2time(rs.getString("t1.d_auth2time"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    public synchronized List<RolePrivilege> getRolePrivilege_New_List(User input_User) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        if (input_User.getN_accessusrtype() == 1) //super Admin accessess Level
        {
            strSql = "SELECT "
                    + "t2.n_mnuid,"
                    + "t3.n_itmid,"
                    + "t2.v_mnuname,"
                    + "t3.v_itmname "
                    + "from mnu_mst t2,"
                    + "itm_mst t3 "
                    + "WHERE "
                    + "t2.n_mnuid=t3.n_mnuid "
                    + "GROUP BY t3.n_mnuid,t3.n_itmid";


        } else if (input_User.getN_accessusrtype() == 2
                || input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //Admin,Internal Admin AND External Admin accessess Level
        {
            strSql = "SELECT "
                    + "t2.v_mnuname,"
                    + "t3.v_itmname,"
                    + "t1.* "
                    + "FROM "
                    + "prev_mst t1,"
                    + "mnu_mst t2,"
                    + "itm_mst t3,"
                    + "userprev_mst t4  "
                    + "WHERE "
                    + "t1.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t1.n_itmid=t3.n_itmid "
                    + "AND "
                    + "t2.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t1.n_mnuid=t4.n_mnuid "
                    + "AND "
                    + "t1.n_itmid=t4.n_itmid "
                    + "AND "
                    + "t4.n_usrcode=" + input_User.getN_usrcode() + " "
                    + "AND "
                    + "t4.v_grant='checked' "
                    + "GROUP BY t1.n_mnuid,t1.n_itmid";
        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                if (input_User.getN_accessusrtype() == 1) {
                    m_RolePrivilege.add(getRolePrivilege_New_For_Super_User(rs));
                } else {
                    m_RolePrivilege.add(getRolePrivilege_New(rs));
                }
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    public synchronized List<RolePrivilege> getRolePrivilege_Modify_List(User input_User, int n_searchUserType, int n_searchUserComId) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        if (input_User.getN_accessusrtype() == 1) //super Admin accessess Level
        {
            strSql = "SELECT "
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_mnuname,"
                    + "t1.v_itmname "
                    + "from mnu_mst t2,"
                    + "itm_mst t1 "
                    + "WHERE "
                    + "t1.n_mnuid=t2.n_mnuid "
                    + "GROUP BY t1.n_mnuid,t1.n_itmid";

            strSql = "SELECT "
                    + "t2.n_mnuid,"
                    + "t3.n_itmid,"
                    + "t2.v_mnuname,"
                    + "t3.v_itmname "
                    + "from mnu_mst t2,"
                    + "itm_mst t3 "
                    + "WHERE "
                    + "t2.n_mnuid=t3.n_mnuid "
                    + "GROUP BY t3.n_mnuid,t3.n_itmid";


        } else if (input_User.getN_accessusrtype() == 2) //Admin Level
        {
            strSql = "SELECT "
                    + "t2.v_mnuname,"
                    + "t3.v_itmname,"
                    + "t1.* "
                    + "FROM "
                    + "prev_mst t1,"
                    + "mnu_mst t2,"
                    + "itm_mst t3,"
                    + "userprev_mst t4  "
                    + "WHERE "
                    + "t1.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t1.n_itmid=t3.n_itmid "
                    + "AND "
                    + "t2.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t1.n_mnuid=t4.n_mnuid "
                    + "AND "
                    + "t1.n_itmid=t4.n_itmid "
                    + "AND "
                    + "t4.n_usrcode=" + input_User.getN_usrcode() + " "
                    + "AND "
                    + "t4.v_grant='checked' "
                    + "GROUP BY t1.n_mnuid,t1.n_itmid";
        } else if (input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5)//Internal Admin AND External Admin accessess Level
        {
            strSql = "SELECT "
                    + "t2.v_mnuname,"
                    + "t3.v_itmname,"
                    + "t1.* "
                    + "FROM "
                    + "prev_mst t1,"
                    + "mnu_mst t2,"
                    + "itm_mst t3,"
                    + "userprev_mst t4  "
                    + "WHERE "
                    + "t1.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t1.n_itmid=t3.n_itmid "
                    + "AND "
                    + "t2.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t1.n_mnuid=t4.n_mnuid "
                    + "AND "
                    + "t1.n_itmid=t4.n_itmid "
                    + "AND "
                    + "t4.n_usrcode=" + input_User.getN_usrcode() + " "
                    + "AND "
                    + "t4.v_grant='checked' "
                    + "GROUP BY t1.n_mnuid,t1.n_itmid";
        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs1 = ps.executeQuery();

            while (rs1.next()) {
                if (input_User.getN_accessusrtype() == 1) {
                    strSql = "SELECT "
                            + "t1.*,"
                            + "t2.v_mnuname,"
                            + "t3.v_itmname "
                            + "FROM prev_mst "
                            + "t1,mnu_mst t2,"
                            + "itm_mst t3 "
                            + "WHERE "
                            + "t1.n_mnuid=" + rs1.getInt("t2.n_mnuid") + " "
                            + "AND "
                            + "t1.n_itmid=" + rs1.getInt("t3.n_itmid") + " "
                            + "AND "
                            + "t2.n_mnuid=" + rs1.getInt("t2.n_mnuid") + " "
                            + "AND "
                            + "t3.n_mnuid=" + rs1.getInt("t2.n_mnuid") + " "
                            + "AND "
                            + "t3.n_itmid=" + rs1.getInt("t3.n_itmid") + " "
                            + "AND "
                            + "t1.n_usrtype=" + n_searchUserType + " "
                            + "AND "
                            + "t1.n_comid=" + n_searchUserComId + " "
                            + "GROUP BY t1.n_mnuid,t1.n_itmid ";
                } else {
                    //modify user Privileges List
                    strSql = "SELECT "
                            + "t1.*,"
                            + "t2.v_mnuname,"
                            + "t3.v_itmname "
                            + "FROM prev_mst "
                            + "t1,mnu_mst t2,"
                            + "itm_mst t3 "
                            + "WHERE "
                            + "t1.n_mnuid=" + rs1.getInt("t1.n_mnuid") + " "
                            + "AND "
                            + "t1.n_itmid=" + rs1.getInt("t1.n_itmid") + " "
                            + "AND "
                            + "t2.n_mnuid=" + rs1.getInt("t1.n_mnuid") + " "
                            + "AND "
                            + "t3.n_mnuid=" + rs1.getInt("t1.n_mnuid") + " "
                            + "AND "
                            + "t3.n_itmid=" + rs1.getInt("t1.n_itmid") + " "
                            + "AND "
                            + "t1.n_usrtype=" + n_searchUserType + " "
                            + "AND "
                            + "t1.n_comid=" + n_searchUserComId + " "
                            + "GROUP BY t1.n_mnuid,t1.n_itmid ";
                }


                ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ResultSet rs2 = ps.executeQuery();
                if (!rs2.next()) {

                    if (input_User.getN_accessusrtype() == 1) {
                        m_RolePrivilege.add(getRolePrivilege_New_For_Super_User(rs1));
                    } else {
                        m_RolePrivilege.add(getRolePrivilege_New(rs1));
                    }
                } else {
                    m_RolePrivilege.add(getRolePrivilege_Modify(rs2));
                }
                rs2.close();
                //rs2=null;
            }
            rs1.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    private synchronized RolePrivilege getRolePrivilege_For_ViewList(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {
            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrtype"));
            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));
            rolePrivilege.setV_comCode(rs.getString("t2.v_comcode"));
            rolePrivilege.setV_usrtype_name(rs.getString("t3.v_name"));

            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    public synchronized List<RolePrivilege> getRolePrivilegeViewList(User input_User, String searchKey, String limitSearchKey) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        searchKey = searchKey.trim();
        if (!searchKey.equalsIgnoreCase("")) {
            searchKey = " AND " + searchKey;
        }

        if (input_User.getN_accessusrtype() == 1) //super Admin accessess Level
        {
            strSql = "SELECT "
                    + "t1.n_usrtype,"
                    + "t1.n_prgid,"
                    + "t1.n_comid,"
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_comcode,"
                    + "t3.v_name "
                    + "FROM "
                    + "prev_mst t1,"
                    + "company_mst t2,"
                    + "usrtype_mst t3 "
                    + "WHERE "
                    + "t1.n_comid=t2.n_comid "
                    + "AND "
                    + "t1.n_prgid=" + input_User.getN_prgid() + " "
                    + "AND "
                    + "t1.n_usrtype=t3.n_usrtype "
                    + searchKey + " "
                    + "group by t1.n_usrtype " + limitSearchKey;


        } else if (input_User.getN_accessusrtype() == 2) //Admin accessess Level
        {
            strSql = "SELECT "
                    + "t1.n_usrtype,"
                    + "t1.n_prgid,"
                    + "t1.n_comid,"
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_comcode,"
                    + "t3.v_name "
                    + "FROM "
                    + "prev_mst t1,"
                    + "company_mst t2,"
                    + "usrtype_mst t3 "
                    + "WHERE "
                    + "t1.n_comid=t2.n_comid "
                    + "AND "
                    + "t1.n_prgid=" + input_User.getN_prgid() + " "
                    + "AND "
                    + "t1.n_usrtype=t3.n_usrtype "
                    + "AND "
                    + "t3.n_accessusrtype NOT IN(1,2) "
                    + searchKey + " "
                    + "group by t1.n_usrtype " + limitSearchKey;
        } else if (input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //Agent admin,Internal Admin AND External Admin accessess Level
        {
            String sql = "";
            if (input_User.getN_accessusrtype() == 3) {
                sql = " AND t3.n_accessusrtype IN(6) ";
            } else if (input_User.getN_accessusrtype() == 4) {
                sql = " AND t3.n_accessusrtype IN(7) ";
            } else if (input_User.getN_accessusrtype() == 5) {
                sql = " AND t3.n_accessusrtype IN(8) ";
            }
            strSql = "SELECT "
                    + "t1.n_usrtype,"
                    + "t1.n_prgid,"
                    + "t1.n_comid,"
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_comcode,"
                    + "t3.v_name "
                    + "FROM "
                    + "prev_mst t1,"
                    + "company_mst t2,"
                    + "usrtype_mst t3 "
                    + "WHERE "
                    + "t1.n_comid=t2.n_comid "
                    + "AND "
                    + "t1.n_comid=" + input_User.getN_comid() + " "
                    + "AND "
                    + "t1.n_prgid=" + input_User.getN_prgid() + " "
                    + "AND "
                    + "t1.n_usrtype=t3.n_usrtype "
                    + sql + " "
                    + searchKey + " "
                    + "group by t1.n_usrtype " + limitSearchKey;
        }
        // SystemMessage.getInstance().writeMessage("DEBUG :-->getRolePrivilegeViewList--> " + strSql);
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_RolePrivilege.add(getRolePrivilege_For_ViewList(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
