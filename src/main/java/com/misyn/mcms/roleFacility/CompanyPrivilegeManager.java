/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.roleFacility;

import com.misyn.mcms.admin.Company;
import com.misyn.mcms.admin.User;
import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class CompanyPrivilegeManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(CompanyPrivilegeManager.class);

    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static CompanyPrivilegeManager companyPrivilegeManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    /**
     * Default constructor
     */
    public CompanyPrivilegeManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized CompanyPrivilegeManager getInstance() {
        if (companyPrivilegeManager == null) {
            companyPrivilegeManager = new CompanyPrivilegeManager();
        }
        return companyPrivilegeManager;
    }

    //===================Company Privilege Insert Method=======================================
    private synchronized int insertCompanyPrivilege(RolePrivilege rolePrivilege, Company company) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "INSERT INTO comprev_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?)";
        try {
            rolePrivilege.setN_comid(company.getN_comid());
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);

            ps.setInt(1, rolePrivilege.getN_prgid());
            ps.setInt(2, rolePrivilege.getN_comid());
            ps.setInt(3, rolePrivilege.getN_mnuid());
            ps.setInt(4, rolePrivilege.getN_itmid());
            ps.setString(5, rolePrivilege.getV_view());
            ps.setString(6, rolePrivilege.getV_input());
            ps.setString(7, rolePrivilege.getV_modify());
            ps.setString(8, rolePrivilege.getV_delete());
            ps.setString(9, rolePrivilege.getV_auth1());
            ps.setString(10, rolePrivilege.getV_auth2());
            ps.setString(11, rolePrivilege.getV_grant());
            ps.setString(12, rolePrivilege.getRecieve1());
            ps.setString(13, rolePrivilege.getV_inpstat());
            ps.setString(14, rolePrivilege.getV_inpuser());
            ps.setString(15, rolePrivilege.getD_inptime());
            ps.setString(16, rolePrivilege.getV_auth1stat());
            ps.setString(17, rolePrivilege.getV_auth1user());
            ps.setString(18, rolePrivilege.getD_auth1time());
            ps.setString(19, rolePrivilege.getV_auth2stat());
            ps.setString(20, rolePrivilege.getV_auth2user());
            ps.setString(21, rolePrivilege.getD_auth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    private synchronized int updateCompanyPrivilege(RolePrivilege rolePrivilege) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "UPDATE comprev_mst SET "
                + "n_prgid=?,"
                + "n_comid=?,"
                + "n_mnuid=?,"
                + "n_itmid=?,"
                + "v_view=?,"
                + "v_input=?,"
                + "v_modify=?,"
                + "v_delete=?,"
                + "v_auth1=?,"
                + "v_auth2=?,"
                + "v_grant=?,"
                + "recieve1=?,"
                + "v_inpstat=?,"
                + "v_inpuser=?,"
                + "d_inptime=?,"
                + "v_auth1stat=?,"
                + "v_auth1user=?,"
                + "d_auth1time=?,"
                + "v_auth2stat=?,"
                + "v_auth2user=?,"
                + "d_auth2time=? "
                + "WHERE "
                + "n_prgid=? "
                + "AND "
                + "n_comid=? "
                + "AND "
                + "n_mnuid=? "
                + "AND "
                + "n_itmid=?";


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, rolePrivilege.getN_prgid());
            ps.setInt(2, rolePrivilege.getN_comid());
            ps.setInt(3, rolePrivilege.getN_mnuid());
            ps.setInt(4, rolePrivilege.getN_itmid());
            ps.setString(5, rolePrivilege.getV_view());
            ps.setString(6, rolePrivilege.getV_input());
            ps.setString(7, rolePrivilege.getV_modify());
            ps.setString(8, rolePrivilege.getV_delete());
            ps.setString(9, rolePrivilege.getV_auth1());
            ps.setString(10, rolePrivilege.getV_auth2());
            ps.setString(11, rolePrivilege.getV_grant());
            ps.setString(12, rolePrivilege.getRecieve1());
            ps.setString(13, rolePrivilege.getV_inpstat());
            ps.setString(14, rolePrivilege.getV_inpuser());
            ps.setString(15, rolePrivilege.getD_inptime());
            ps.setString(16, rolePrivilege.getV_auth1stat());
            ps.setString(17, rolePrivilege.getV_auth1user());
            ps.setString(18, rolePrivilege.getD_auth1time());
            ps.setString(19, rolePrivilege.getV_auth2stat());
            ps.setString(20, rolePrivilege.getV_auth2user());
            ps.setString(21, rolePrivilege.getD_auth2time());


            ps.setInt(22, rolePrivilege.getN_prgid());
            ps.setInt(23, rolePrivilege.getN_comid());
            ps.setInt(24, rolePrivilege.getN_mnuid());
            ps.setInt(25, rolePrivilege.getN_itmid());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveCompanyPrivilege(List<RolePrivilege> rolePrivilegesList, Company company) {
        int result = 0;
        RolePrivilege rolePrivilege = null;


        int updateCountResult = -1;
        try {

            for (int i = 0; i < rolePrivilegesList.size(); i++) {
                rolePrivilege = rolePrivilegesList.get(i);

                if (!dbRecordCommonFunction.
                        isRecExists("comprev_mst", ""
                                + "n_prgid=" + rolePrivilege.getN_prgid() + " "
                                + "AND n_comid=" + rolePrivilege.getN_comid() + " "
                                + "AND n_mnuid=" + rolePrivilege.getN_mnuid() + " "
                                + "AND n_itmid=" + rolePrivilege.getN_itmid())) {
                    if (!dbRecordCommonFunction.isIsErrorExsist()) {
                        result = insertCompanyPrivilege(rolePrivilege, company);

                    }
                } else {
                    result = updateCompanyPrivilege(rolePrivilege);
                }

                if (result > 0) {
                    updateCountResult++;
                }
            }
//            if (result > 0) {
//                int c = updateUserPrivilege(n_comid, n_usrtype);
//                SystemMessage.getInstance().writeMessage("DEBUG: RolePrivilegeManager.updateUserPrivilege() - Count-> " + c);
//            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return updateCountResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteRolePrivilege(List<RolePrivilege> rolePrivilegesList) {
        int result = 0;
        RolePrivilege rolePrivilege = null;
        int n_usrtype = -1;
        int n_comid = -1;

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "DELETE FROM comprev_mst WHERE n_comid=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < rolePrivilegesList.size(); i++) {
                setMsg("");
                rolePrivilege = rolePrivilegesList.get(i);
                n_comid = rolePrivilege.getN_comid();
                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, n_comid);
                result = ps.executeUpdate();
            }
            conn.commit();
            conn.setAutoCommit(true);


        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }


    private synchronized RolePrivilege getRolePrivilege_New_For_Super_User(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {
            rolePrivilege.setN_mnuid(rs.getInt("t2.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t3.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view("");
            rolePrivilege.setV_input("");
            rolePrivilege.setV_modify("");
            rolePrivilege.setV_delete("");
            rolePrivilege.setV_auth1("");
            rolePrivilege.setV_auth2("");
            rolePrivilege.setV_grant("");


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    private synchronized RolePrivilege getRolePrivilege_New(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {


            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));


            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view("");
            rolePrivilege.setV_input("");
            rolePrivilege.setV_modify("");
            rolePrivilege.setV_delete("");
            rolePrivilege.setV_auth1("");
            rolePrivilege.setV_auth2("");
            rolePrivilege.setV_grant("");

            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpstat"));
            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpuser"));
            rolePrivilege.setD_inptime(rs.getString("t1.d_inptime"));
            rolePrivilege.setV_auth1stat(rs.getString("t1.v_auth1stat"));
            rolePrivilege.setV_auth1user(rs.getString("t1.v_auth1user"));
            rolePrivilege.setD_auth1time(rs.getString("t1.d_auth1time"));
            rolePrivilege.setV_auth2stat(rs.getString("t1.v_auth2stat"));
            rolePrivilege.setV_auth2user(rs.getString("t1.v_auth2user"));
            rolePrivilege.setD_auth2time(rs.getString("t1.d_auth2time"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    private synchronized RolePrivilege getRolePrivilege_Modify(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {

            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));


            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view(rs.getString("t1.v_view"));
            rolePrivilege.setV_input(rs.getString("t1.v_input"));
            rolePrivilege.setV_modify(rs.getString("t1.v_modify"));
            rolePrivilege.setV_delete(rs.getString("t1.v_delete"));
            rolePrivilege.setV_auth1(rs.getString("t1.v_auth1"));
            rolePrivilege.setV_auth2(rs.getString("t1.v_auth2"));
            rolePrivilege.setV_grant(rs.getString("t1.v_grant"));

            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpstat"));
            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpuser"));
            rolePrivilege.setD_inptime(rs.getString("t1.d_inptime"));
            rolePrivilege.setV_auth1stat(rs.getString("t1.v_auth1stat"));
            rolePrivilege.setV_auth1user(rs.getString("t1.v_auth1user"));
            rolePrivilege.setD_auth1time(rs.getString("t1.d_auth1time"));
            rolePrivilege.setV_auth2stat(rs.getString("t1.v_auth2stat"));
            rolePrivilege.setV_auth2user(rs.getString("t1.v_auth2user"));
            rolePrivilege.setD_auth2time(rs.getString("t1.d_auth2time"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    public synchronized List<RolePrivilege> getRolePrivilege_New_List(User input_User) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        if (input_User.getN_accessusrtype() == 1
                || input_User.getN_accessusrtype() == 2
                || input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //super Admin accessess Level
        {
            strSql = "SELECT "
                    + "t2.n_mnuid,"
                    + "t3.n_itmid,"
                    + "t2.v_mnuname,"
                    + "t3.v_itmname "
                    + "from mnu_mst t2,"
                    + "itm_mst t3 "
                    + "WHERE "
                    + "t2.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t3.v_apptype='Y' "
                    + "GROUP BY t3.n_mnuid,t3.n_itmid";


        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                //if (input_User.getN_accessusrtype() == 1) {
                m_RolePrivilege.add(getRolePrivilege_New_For_Super_User(rs));
//                } else {
//                    m_RolePrivilege.add(getRolePrivilege_New(rs));
//                }
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    public synchronized List<RolePrivilege> getRolePrivilege_Modify_List(User input_User, int n_searchUserComId) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        if (input_User.getN_accessusrtype() == 1
                || input_User.getN_accessusrtype() == 2
                || input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) {


            strSql = "SELECT "
                    + "t2.n_mnuid,"
                    + "t3.n_itmid,"
                    + "t2.v_mnuname,"
                    + "t3.v_itmname "
                    + "from mnu_mst t2,"
                    + "itm_mst t3 "
                    + "WHERE "
                    + "t2.n_mnuid=t3.n_mnuid "
                    + "AND "
                    + "t3.v_apptype='Y' "
                    + "GROUP BY t3.n_mnuid,t3.n_itmid";



        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs1 = ps.executeQuery();

            while (rs1.next()) {
                strSql = "SELECT "
                        + "t1.*,"
                        + "t2.v_mnuname,"
                        + "t3.v_itmname "
                        + "FROM "
                        + "comprev_mst t1,"
                        + "mnu_mst t2,"
                        + "itm_mst t3 "
                        + "WHERE "
                        + "t1.n_mnuid=" + rs1.getInt("t2.n_mnuid") + " "
                        + "AND "
                        + "t1.n_itmid=" + rs1.getInt("t3.n_itmid") + " "
                        + "AND "
                        + "t2.n_mnuid=" + rs1.getInt("t2.n_mnuid") + " "
                        + "AND "
                        + "t3.n_mnuid=" + rs1.getInt("t2.n_mnuid") + " "
                        + "AND "
                        + "t3.n_itmid=" + rs1.getInt("t3.n_itmid") + " "
                        + "AND "
                        + "t1.n_comid=" + n_searchUserComId + " "
                        + "GROUP BY t1.n_mnuid,t1.n_itmid ";


                ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ResultSet rs2 = ps.executeQuery();
                if (!rs2.next()) {

                    if (input_User.getN_accessusrtype() == 1) {
                        m_RolePrivilege.add(getRolePrivilege_New_For_Super_User(rs1));
                    } else {
                        m_RolePrivilege.add(getRolePrivilege_New(rs1));
                    }
                } else {
                    m_RolePrivilege.add(getRolePrivilege_Modify(rs2));
                }
                rs2.close();
                //rs2=null;
            }
            rs1.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    private synchronized RolePrivilege getRolePrivilege_For_ViewList(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {
            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrtype"));
            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));
            rolePrivilege.setV_comCode(rs.getString("t2.v_comcode"));
            rolePrivilege.setV_usrtype_name(rs.getString("t3.v_name"));

            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    public synchronized List<RolePrivilege> getRolePrivilegeViewList(User input_User, String searchKey, String limitSearchKey) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        searchKey = searchKey.trim();
        if (!searchKey.equalsIgnoreCase("")) {
            searchKey = " AND " + searchKey;
        }

        if (input_User.getN_accessusrtype() == 1
                || input_User.getN_accessusrtype() == 2
                || input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) {
            strSql = "SELECT "
                    + "t1.n_usrtype,"
                    + "t1.n_prgid,"
                    + "t1.n_comid,"
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_comcode,"
                    + "t3.v_name "
                    + "FROM "
                    + "prev_mst t1,"
                    + "company_mst t2,"
                    + "usrtype_mst t3 "
                    + "WHERE "
                    + "t1.n_comid=t2.n_comid "
                    + "AND "
                    + "t1.n_prgid=" + input_User.getN_prgid() + " "
                    + "AND "
                    + "t1.n_usrtype=t3.n_usrtype "
                    + searchKey + " "
                    + "group by t1.n_usrtype " + limitSearchKey;

        }
        // SystemMessage.getInstance().writeMessage("DEBUG :-->getRolePrivilegeViewList--> " + strSql);
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_RolePrivilege.add(getRolePrivilege_For_ViewList(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
