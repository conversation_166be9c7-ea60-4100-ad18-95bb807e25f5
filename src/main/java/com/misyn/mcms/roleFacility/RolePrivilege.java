/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.roleFacility;

import java.io.Serializable;
public class RolePrivilege implements Serializable {

    private int n_usrcode = 0;
    private int n_usrtype = 0;
    private int n_prgid = 1;
    private int n_comid = 0;
    private int n_homepage = 0;
    private int n_mnuid = 0;
    private String v_mnuname = "";
    private int n_itmid = 0;
    private String v_itmname = "";
    private String v_view = "";
    private String v_input = "";
    private String v_modify = "";
    private String v_delete = "";
    private String v_auth1 = "";
    private String v_auth2 = "";
    private String v_grant = "";
    private String recieve1 = "";
    private String v_inpstat = "I";
    private String v_inpuser = "";
    private String d_inptime = "1900-01-01 12:00:00";
    private String v_auth1stat = "P";
    private String v_auth1user = "";
    private String d_auth1time = "1900-01-01 12:00:00";
    private String v_auth2stat = "P";
    private String v_auth2user = "";
    private String d_auth2time = "1900-01-01 12:00:00";

    private String v_usrid = "";
    private String v_usrtype_name = "";
    private String v_comCode = "";

    private boolean isDeleteChecked = false;

    private boolean isMainMenu = false;

    public int getN_usrcode() {
        return n_usrcode;
    }

    public void setN_usrcode(int n_usrcode) {
        this.n_usrcode = n_usrcode;
    }

    public String getV_usrid() {
        return v_usrid;
    }

    public void setV_usrid(String v_usrid) {
        this.v_usrid = v_usrid;
    }


    public String getD_auth1time() {
        return d_auth1time;
    }

    public void setD_auth1time(String d_auth1time) {
        this.d_auth1time = d_auth1time;
    }

    public String getD_auth2time() {
        return d_auth2time;
    }

    public void setD_auth2time(String d_auth2time) {
        this.d_auth2time = d_auth2time;
    }

    public String getD_inptime() {
        return d_inptime;
    }

    public void setD_inptime(String d_inptime) {
        this.d_inptime = d_inptime;
    }

    public int getN_comid() {
        return n_comid;
    }

    public void setN_comid(int n_comid) {
        this.n_comid = n_comid;
    }

    public int getN_homepage() {
        return n_homepage;
    }

    public void setN_homepage(int n_homepage) {
        this.n_homepage = n_homepage;
    }

    public int getN_itmid() {
        return n_itmid;
    }

    public void setN_itmid(int n_itmid) {
        this.n_itmid = n_itmid;
    }

    public int getN_mnuid() {
        return n_mnuid;
    }

    public void setN_mnuid(int n_mnuid) {
        this.n_mnuid = n_mnuid;
    }

    public int getN_prgid() {
        return n_prgid;
    }

    public void setN_prgid(int n_prgid) {
        this.n_prgid = n_prgid;
    }

    public int getN_usrtype() {
        return n_usrtype;
    }

    public void setN_usrtype(int n_usrtype) {
        this.n_usrtype = n_usrtype;
    }

    public String getRecieve1() {
        return recieve1;
    }

    public void setRecieve1(String recieve1) {
        this.recieve1 = recieve1;
    }

    public String getV_auth1() {
        // if(v_auth1.equals("Y")) return "checked";
        // else return "";
        return v_auth1;
    }

    public void setV_auth1(String v_auth1) {
        if (v_auth1 == null) this.v_auth1 = "";
        else this.v_auth1 = v_auth1;
    }

    public String getV_auth1stat() {
        return v_auth1stat;
    }

    public void setV_auth1stat(String v_auth1stat) {
        this.v_auth1stat = v_auth1stat;
    }

    public String getV_auth1user() {
        return v_auth1user;
    }

    public void setV_auth1user(String v_auth1user) {
        this.v_auth1user = v_auth1user;
    }

    public String getV_auth2() {
//        if(v_auth2.equals("Y")) return "checked";
//        else return "";
        return v_auth2;
    }

    public void setV_auth2(String v_auth2) {
        if (v_auth2 == null) this.v_auth2 = "";
        else this.v_auth2 = v_auth2;
    }

    public String getV_auth2stat() {
        return v_auth2stat;
    }

    public void setV_auth2stat(String v_auth2stat) {
        this.v_auth2stat = v_auth2stat;
    }

    public String getV_auth2user() {
        return v_auth2user;
    }

    public void setV_auth2user(String v_auth2user) {
        this.v_auth2user = v_auth2user;
    }

    public String getV_delete() {
//        if(v_delete.equals("Y")) return "checked";
//        else return "";
        return v_delete;
    }

    public void setV_delete(String v_delete) {
        if (v_delete == null) this.v_delete = "";
        else this.v_delete = v_delete;
    }

    public String getV_grant() {
//        if(v_grant.equals("Y")) return "checked";
//        else return "";
        return v_grant;
    }

    public void setV_grant(String v_grant) {
        if (v_grant == null) this.v_grant = "";
        else this.v_grant = v_grant;
    }

    public String getV_inpstat() {
        return v_inpstat;
    }

    public void setV_inpstat(String v_inpstat) {
        this.v_inpstat = v_inpstat;
    }

    public String getV_inpuser() {
        return v_inpuser;
    }

    public void setV_inpuser(String v_inpuser) {
        this.v_inpuser = v_inpuser;
    }

    public String getV_input() {
//        if(v_input.equals("Y")) return "checked";
//        else return "";
        return v_input;
    }

    public void setV_input(String v_input) {
        if (v_input == null) this.v_input = "";
        else this.v_input = v_input;
    }

    public String getV_itmname() {
        return v_itmname;
    }

    public void setV_itmname(String v_itmname) {
        this.v_itmname = v_itmname;
    }

    public String getV_mnuname() {
        return v_mnuname;
    }

    public void setV_mnuname(String v_mnuname) {
        this.v_mnuname = v_mnuname;
    }

    public String getV_modify() {
//        if(v_modify.equals("Y")) return "checked";
//        else return "";
        return v_modify;
    }

    public void setV_modify(String v_modify) {
        if (v_modify == null) this.v_modify = "";
        else this.v_modify = v_modify;
    }

    public String getV_view() {
//        if(v_view.equals("Y")) return "checked";
//        else return "";
        return v_view;
    }

    public void setV_view(String v_view) {
        if (v_view == null) this.v_view = "";
        else this.v_view = v_view;
    }

    public boolean isIsMainMenu() {
        return isMainMenu;
    }

    public void setIsMainMenu(boolean isMainMenu) {
        this.isMainMenu = isMainMenu;
    }

    public String getV_comCode() {
        return v_comCode;
    }

    public void setV_comCode(String v_comCode) {
        this.v_comCode = v_comCode;
    }

    public String getV_usrtype_name() {
        return v_usrtype_name;
    }

    public void setV_usrtype_name(String v_usrtype_name) {
        this.v_usrtype_name = v_usrtype_name;
    }

    public boolean isIsDeleteChecked() {
        return isDeleteChecked;
    }

    public void setIsDeleteChecked(boolean isDeleteChecked) {
        this.isDeleteChecked = isDeleteChecked;
    }


}
