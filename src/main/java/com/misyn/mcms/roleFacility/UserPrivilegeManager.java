/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.roleFacility;

import com.misyn.mcms.admin.User;
import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class UserPrivilegeManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserPrivilegeManager.class);
    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static UserPrivilegeManager userPrivilegeManager = null;
    private ConnectionPool cp = null;

    public UserPrivilegeManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized UserPrivilegeManager getInstance() {
        if (userPrivilegeManager == null) {
            userPrivilegeManager = new UserPrivilegeManager();
        }
        return userPrivilegeManager;
    }

    //===================UserPrivilege Insert Method=======================================
    private synchronized int insertUserPrivilege(RolePrivilege rolePrivilege) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "INSERT INTO userprev_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?)";
        try {

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, rolePrivilege.getN_usrcode());
            ps.setInt(2, rolePrivilege.getN_usrtype());
            ps.setInt(3, rolePrivilege.getN_prgid());
            ps.setInt(4, rolePrivilege.getN_comid());
            ps.setInt(5, rolePrivilege.getN_mnuid());
            ps.setInt(6, rolePrivilege.getN_itmid());
            ps.setString(7, rolePrivilege.getV_view());
            ps.setString(8, rolePrivilege.getV_input());
            ps.setString(9, rolePrivilege.getV_modify());
            ps.setString(10, rolePrivilege.getV_delete());
            ps.setString(11, rolePrivilege.getV_auth1());
            ps.setString(12, rolePrivilege.getV_auth2());
            ps.setString(13, rolePrivilege.getV_grant());
            ps.setString(14, rolePrivilege.getRecieve1());
            ps.setString(15, rolePrivilege.getV_inpstat());
            ps.setString(16, rolePrivilege.getV_inpuser());
            ps.setString(17, rolePrivilege.getD_inptime());
            ps.setString(18, rolePrivilege.getV_auth1stat());
            ps.setString(19, rolePrivilege.getV_auth1user());
            ps.setString(20, rolePrivilege.getD_auth1time());
            ps.setString(21, rolePrivilege.getV_auth2stat());
            ps.setString(22, rolePrivilege.getV_auth2user());
            ps.setString(23, rolePrivilege.getD_auth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    private synchronized int updateUserPrivilege(RolePrivilege rolePrivilege) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "UPDATE userprev_mst SET "
                + "n_usrcode=?,"
                + "n_usrtype=?,"
                + "n_prgid=?,"
                + "n_comid=?,"
                + "n_mnuid=?,"
                + "n_itmid=?,"
                + "v_view=?,"
                + "v_input=?,"
                + "v_modify=?,"
                + "v_delete=?,"
                + "v_auth1=?,"
                + "v_auth2=?,"
                + "v_grant=?,"
                + "recieve1=?,"
                + "v_inpstat=?,"
                + "v_inpuser=?,"
                + "d_inptime=?,"
                + "v_auth1stat=?,"
                + "v_auth1user=?,"
                + "d_auth1time=?,"
                + "v_auth2stat=?,"
                + "v_auth2user=?,"
                + "d_auth2time=? "
                + "WHERE "
                + "n_usrcode=? "
                + "AND "
                + "n_usrtype=? "
                + "AND "
                + "n_mnuid=? "
                + "AND "
                + "n_itmid=?";


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, rolePrivilege.getN_usrcode());
            ps.setInt(2, rolePrivilege.getN_usrtype());
            ps.setInt(3, rolePrivilege.getN_prgid());
            ps.setInt(4, rolePrivilege.getN_comid());
            ps.setInt(5, rolePrivilege.getN_mnuid());
            ps.setInt(6, rolePrivilege.getN_itmid());
            ps.setString(7, rolePrivilege.getV_view());
            ps.setString(8, rolePrivilege.getV_input());
            ps.setString(9, rolePrivilege.getV_modify());
            ps.setString(10, rolePrivilege.getV_delete());
            ps.setString(11, rolePrivilege.getV_auth1());
            ps.setString(12, rolePrivilege.getV_auth2());
            ps.setString(13, rolePrivilege.getV_grant());
            ps.setString(14, rolePrivilege.getRecieve1());
            ps.setString(15, rolePrivilege.getV_inpstat());
            ps.setString(16, rolePrivilege.getV_inpuser());
            ps.setString(17, rolePrivilege.getD_inptime());
            ps.setString(18, rolePrivilege.getV_auth1stat());
            ps.setString(19, rolePrivilege.getV_auth1user());
            ps.setString(20, rolePrivilege.getD_auth1time());
            ps.setString(21, rolePrivilege.getV_auth2stat());
            ps.setString(22, rolePrivilege.getV_auth2user());
            ps.setString(23, rolePrivilege.getD_auth2time());

            ps.setInt(24, rolePrivilege.getN_usrcode());
            ps.setInt(25, rolePrivilege.getN_usrtype());
            ps.setInt(26, rolePrivilege.getN_mnuid());
            ps.setInt(27, rolePrivilege.getN_itmid());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveUserPrivilege(List<RolePrivilege> rolePrivilegesList) {
        int result = 0;
        RolePrivilege rolePrivilege = null;
        int n_usrcode = -1;

        Connection conn = null;
        PreparedStatement ps = null;

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();

            for (int i = 0; i < rolePrivilegesList.size(); i++) {
                rolePrivilege = rolePrivilegesList.get(i);
                n_usrcode = rolePrivilege.getN_usrcode();


                ps = conn.prepareStatement("UPDATE userprev_mst SET v_view='',v_input='',v_modify='',v_delete='',v_auth1='',v_auth2='',v_grant='' "
                        + "WHERE "
                        + "n_usrcode=?  AND n_mnuid=? AND n_itmid=?");
                ps.setInt(1, n_usrcode);
                //ps.setInt(2, rolePrivilege.getN_usrtype());
                ps.setInt(2, rolePrivilege.getN_mnuid());
                ps.setInt(3, rolePrivilege.getN_itmid());

                result = ps.executeUpdate();

                if (!dbRecordCommonFunction.
                        isRecExists("userprev_mst", ""
                                + "n_usrcode=" + n_usrcode + " "
                                + "AND n_usrtype=" + rolePrivilege.getN_usrtype() + " "
                                + "AND n_mnuid=" + rolePrivilege.getN_mnuid() + " "
                                + "AND n_itmid=" + rolePrivilege.getN_itmid())) {
                    if (!dbRecordCommonFunction.isIsErrorExsist()) {

                        result = insertUserPrivilege(rolePrivilege);


                    }
                } else {
                    result = updateUserPrivilege(rolePrivilege);

                }

                if (result > 0) {
                    updateCountResult++;
                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized RolePrivilege getRolePrivilege_New(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {
            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrcode"));
            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrtype"));
            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));


            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view("");
            rolePrivilege.setV_input("");
            rolePrivilege.setV_modify("");
            rolePrivilege.setV_delete("");
            rolePrivilege.setV_auth1("");
            rolePrivilege.setV_auth2("");
            rolePrivilege.setV_grant("");

            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpstat"));
            rolePrivilege.setV_inpstat(rs.getString("t1.v_inpuser"));
            rolePrivilege.setD_inptime(rs.getString("t1.d_inptime"));
            rolePrivilege.setV_auth1stat(rs.getString("t1.v_auth1stat"));
            rolePrivilege.setV_auth1user(rs.getString("t1.v_auth1user"));
            rolePrivilege.setD_auth1time(rs.getString("t1.d_auth1time"));
            rolePrivilege.setV_auth2stat(rs.getString("t1.v_auth2stat"));
            rolePrivilege.setV_auth2user(rs.getString("t1.v_auth2user"));
            rolePrivilege.setD_auth2time(rs.getString("t1.d_auth2time"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    private synchronized RolePrivilege getRolePrivilege_Modify(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {
            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrcode"));
            rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrtype"));
            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));


            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));

            rolePrivilege.setV_mnuname(rs.getString("t2.v_mnuname"));
            rolePrivilege.setV_itmname(rs.getString("t3.v_itmname"));

            rolePrivilege.setV_view(rs.getString("v_view"));
            rolePrivilege.setV_input(rs.getString("v_input"));
            rolePrivilege.setV_modify(rs.getString("v_modify"));
            rolePrivilege.setV_delete(rs.getString("v_delete"));
            rolePrivilege.setV_auth1(rs.getString("v_auth1"));
            rolePrivilege.setV_auth2(rs.getString("v_auth2"));
            rolePrivilege.setV_grant(rs.getString("v_grant"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    public synchronized List<RolePrivilege> getUserPrivilege_List(User input_User, int n_searchUsrCode) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";


        strSql = "SELECT "
                + "t2.v_mnuname,"
                + "t3.v_itmname,"
                + "t1.* "
                + "FROM "
                + "userprev_mst t1,"
                + "mnu_mst t2,"
                + "itm_mst t3 "
                + "WHERE "
                + "t1.n_mnuid=t3.n_mnuid "
                + "AND "
                + "t1.n_itmid=t3.n_itmid "
                + "AND "
                + "t2.n_mnuid=t3.n_mnuid "
                + "AND "
                + "t1.n_usrcode=? "
                + "AND "
                + "t1.v_grant='checked' "
                + "GROUP BY t1.n_mnuid,t1.n_itmid";


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setInt(1, input_User.getN_usrcode());
            ResultSet rs1 = ps.executeQuery();
            while (rs1.next()) {
                strSql = "SELECT t1.n_prgid,"
                        + "t1.n_usrcode,"
                        + "t1.n_comid,"
                        + "t1.n_usrtype,"
                        + "t1.n_mnuid,"
                        + "t1.n_itmid,"
                        + "t2.v_mnuname,"
                        + "t3.v_itmname,"
                        + "ifnull((SELECT v_view From userprev_mst where v_view='checked' AND n_mnuid=t1.n_mnuid AND n_itmid=t1.n_itmid AND n_usrcode=t1.n_usrcode group by t1.n_mnuid,t1.n_itmid,t1.n_usrcode),'') AS v_view,"
                        + "ifnull((SELECT v_input From userprev_mst where v_input='checked' AND n_mnuid=t1.n_mnuid AND n_itmid=t1.n_itmid AND n_usrcode=t1.n_usrcode group by t1.n_mnuid,t1.n_itmid,t1.n_usrcode),'') AS v_input,"
                        + "ifnull((SELECT v_modify From userprev_mst where v_modify='checked' AND n_mnuid=t1.n_mnuid AND n_itmid=t1.n_itmid AND n_usrcode=t1.n_usrcode group by t1.n_mnuid,t1.n_itmid,t1.n_usrcode),'') AS v_modify,"
                        + "ifnull((SELECT v_delete From userprev_mst where v_delete='checked' AND n_mnuid=t1.n_mnuid AND n_itmid=t1.n_itmid AND n_usrcode=t1.n_usrcode group by t1.n_mnuid,t1.n_itmid,t1.n_usrcode),'') AS v_delete,"
                        + "ifnull((SELECT v_auth1 From userprev_mst where v_auth1='checked' AND n_mnuid=t1.n_mnuid AND n_itmid=t1.n_itmid AND n_usrcode=t1.n_usrcode group by t1.n_mnuid,t1.n_itmid,t1.n_usrcode),'') AS v_auth1,"
                        + "ifnull((SELECT v_auth2 From userprev_mst where v_auth2='checked' AND n_mnuid=t1.n_mnuid AND n_itmid=t1.n_itmid AND n_usrcode=t1.n_usrcode group by t1.n_mnuid,t1.n_itmid,t1.n_usrcode),'') AS v_auth2,"
                        + "ifnull((SELECT v_grant From userprev_mst where v_grant='checked' AND n_mnuid=t1.n_mnuid AND n_itmid=t1.n_itmid AND n_usrcode=t1.n_usrcode group by t1.n_mnuid,t1.n_itmid,t1.n_usrcode),'') AS v_grant "
                        + "FROM "
                        + "userprev_mst AS t1,"
                        + "mnu_mst as t2,"
                        + "itm_mst as t3 "
                        + "WHERE "
                        + "t1.n_usrcode=" + n_searchUsrCode + " "
                        + "AND "
                        + "t1.n_mnuid=" + rs1.getInt("t1.n_mnuid") + " "
                        + "AND "
                        + "t1.n_itmid=" + rs1.getInt("t1.n_itmid") + " "
                        + "AND "
                        + "t2.n_mnuid=" + rs1.getInt("t1.n_mnuid") + " "
                        + "AND "
                        + "t3.n_mnuid=" + rs1.getInt("t1.n_mnuid") + " "
                        + "AND "
                        + "t3.n_itmid=" + rs1.getInt("t1.n_itmid") + " "
                        + "GROUP BY t1.n_mnuid,t1.n_itmid,t1.n_usrcode;";


                ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ResultSet rs2 = ps.executeQuery();
                if (!rs2.next()) {
                    continue;
                } else {
                    m_RolePrivilege.add(getRolePrivilege_Modify(rs2));
                }
                rs2.close();
            }
            rs1.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    private synchronized RolePrivilege getUserPrivilege_For_ViewList(ResultSet rs) {
        RolePrivilege rolePrivilege = new RolePrivilege();
        try {
            rolePrivilege.setN_usrcode(rs.getInt("t1.n_usrcode"));
            //rolePrivilege.setN_usrtype(rs.getInt("t1.n_usrtype"));
            rolePrivilege.setN_prgid(rs.getInt("t1.n_prgid"));
            rolePrivilege.setN_comid(rs.getInt("t1.n_comid"));
            rolePrivilege.setV_comCode(rs.getString("t2.v_comcode"));
            rolePrivilege.setV_usrid(rs.getString("t3.v_usrid"));
            rolePrivilege.setV_usrtype_name(rs.getString("t3.v_usrtype_desc"));

            rolePrivilege.setN_mnuid(rs.getInt("t1.n_mnuid"));
            rolePrivilege.setN_itmid(rs.getInt("t1.n_itmid"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rolePrivilege;
    }

    public synchronized List<RolePrivilege> getUserPrivilegeViewList(User input_User, String searchKey, String limitSearchKey) {
        List<RolePrivilege> m_RolePrivilege = new LinkedList<RolePrivilege>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        searchKey = searchKey.trim();
        if (!searchKey.equalsIgnoreCase("")) {
            searchKey = " AND " + searchKey;
        }

        if (input_User.getN_accessusrtype() == 1) //super Admin accessess Level
        {
            strSql = "SELECT "
                    + "t1.n_usrcode,"
                    + "t1.n_prgid,"
                    + "t1.n_comid,"
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_comcode,"
                    + "t3.v_usrid,"
                    + "t3.v_usrtype_desc "
                    + "FROM "
                    + "userprev_mst t1,"
                    + "company_mst t2,"
                    + "usr_mst t3 "
                    + "WHERE "
                    + "t1.n_comid=t2.n_comid "
                    + "AND "
                    + "t1.n_prgid=" + input_User.getN_prgid() + " "
                    + "AND "
                    + "t1.n_usrcode=t3.n_usrcode "
                    + searchKey + " "
                    + "group by t1.n_usrcode " + limitSearchKey;


        } else if (input_User.getN_accessusrtype() == 2) //Admin accessess Level
        {
            strSql = "SELECT "
                    + "t1.n_usrcode,"
                    + "t1.n_prgid,"
                    + "t1.n_comid,"
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_comcode,"
                    + "t3.v_usrid,"
                    + "t3.v_usrtype_desc "
                    + "FROM "
                    + "userprev_mst t1,"
                    + "company_mst t2,"
                    + "usr_mst t3 "
                    + "WHERE "
                    + "t1.n_comid=t2.n_comid "
                    + "AND "
                    + "t1.n_prgid=" + input_User.getN_prgid() + " "
                    + "AND "
                    + "t1.n_usrcode=t3.n_usrcode "
                    + "AND "
                    + "t3.n_accessusrtype NOT IN(1,2) "
                    + searchKey + " "
                    + "group by t1.n_usrcode " + limitSearchKey;
        } else if (input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //Agent admin,Internal Admin AND External Admin accessess Level
        {
            String sql = "";
            if (input_User.getN_accessusrtype() == 3) {
                sql = " AND t3.n_accessusrtype IN(6) ";
            } else if (input_User.getN_accessusrtype() == 4) {
                sql = " AND t3.n_accessusrtype IN(7) ";
            } else if (input_User.getN_accessusrtype() == 5) {
                sql = " AND t3.n_accessusrtype IN(8) ";
            }
            strSql = "SELECT "
                    + "t1.n_usrcode,"
                    + "t1.n_prgid,"
                    + "t1.n_comid,"
                    + "t1.n_mnuid,"
                    + "t1.n_itmid,"
                    + "t2.v_comcode,"
                    + "t3.v_usrid,"
                    + "t3.v_usrtype_desc "
                    + "FROM "
                    + "userprev_mst t1,"
                    + "company_mst t2,"
                    + "usr_mst t3 "
                    + "WHERE "
                    + "t1.n_comid=t2.n_comid "
                    + "AND "
                    + "t1.n_prgid=" + input_User.getN_prgid() + " "
                    + "AND "
                    + "t1.n_usrcode=t3.n_usrcode "
                    + "AND "
                    + "t1.n_comid=" + input_User.getN_comid() + " "
                    + sql + " "
                    + searchKey + " "
                    + "group by t1.n_usrcode " + limitSearchKey;
        }
        // SystemMessage.getInstance().writeMessage("DEBUG :-->getRolePrivilegeViewList--> " + strSql);
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_RolePrivilege.add(getUserPrivilege_For_ViewList(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_RolePrivilege;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
