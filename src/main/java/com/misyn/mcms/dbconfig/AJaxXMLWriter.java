/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.dbconfig;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
public class AJaxXMLWriter {
    private static final Logger LOGGER = LoggerFactory.getLogger(AJaxXMLWriter.class);

    private static AJaxXMLWriter aJaxXMLWriter = null;
    private ConnectionPool cp = null;
    private String xmlReply = "";

    public AJaxXMLWriter() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized AJaxXMLWriter getInstance() {
        if (aJaxXMLWriter == null) {
            aJaxXMLWriter = new AJaxXMLWriter();
        }
        return aJaxXMLWriter;
    }

    public synchronized void writeXMLAll_2Key(String tblName, String outFieldName1, String outFieldName2,
                                              String searchKey1, int viewLstCount, boolean isMstTbl) {

        Connection conn = null;
        PreparedStatement ps_tmp = null;
        PreparedStatement ps_mst = null;


        StringBuilder sb = new StringBuilder();

        String strSQL = "";
        String filed1 = "";
        String filed2 = "";

        int row = 1;
        if (isMstTbl == true) {
            if (searchKey1.equals("")) {
                strSQL = "SELECT  distinct(" + outFieldName1 + ") as field1,CONCAT(" + outFieldName2 + ") as field2 FROM " + tblName + " ORDER BY " + outFieldName1;
            } else {
                strSQL = "SELECT  distinct(" + outFieldName1 + ") as field1,CONCAT(" + outFieldName2 + ") as field2 FROM " + tblName + " "
                        + " WHERE " + searchKey1 + " ORDER BY " + outFieldName1;
            }
        } else {
            if (searchKey1.equals("")) {
                strSQL = "SELECT  distinct(" + outFieldName1 + ") as field1,CONCAT(" + outFieldName2 + ") as field2 FROM " + tblName + "_tmp ORDER BY " + outFieldName1;
            } else {
                strSQL = "SELECT  distinct(" + outFieldName1 + ") as field1,CONCAT(" + outFieldName2 + ") as field2 FROM " + tblName + "_tmp"
                        + " WHERE " + searchKey1 + " ORDER BY " + outFieldName1;
            }
        }


        try {
            conn = getJDBCConnection();

            ps_tmp = conn.prepareStatement(strSQL);

            ResultSet rs = ps_tmp.executeQuery();

            sb.append("<?xml version='1.0' encoding='ISO-8859-1'?>");
            sb.append("<reply>");

            if (isMstTbl == true) {
                while (rs.next()) {
                    if (rs.getString("field1").trim().equalsIgnoreCase("")) {
                        continue;
                    }
                    filed1 = rs.getString("field1");
                    filed2 = rs.getString("field2");
                    if (filed1.indexOf("&") > -1) {
                        filed1 = filed1.replace("&", "&amp;");
                    }
                    if (filed2.indexOf("&") > -1) {
                        filed2 = filed2.replace("&", "&amp;");
                    }

                    sb.append("<inputtext").append(row).append(">").append(filed1).append("</inputtext").append(row).append(">");
                    sb.append("<inputtext").append(row).append(">").append(filed2).append("</inputtext").append(row).append(">");
                    //}
                    if (viewLstCount > 0) {
                        if (row >= (viewLstCount + 1)) {
                            break;
                        }
                    }
                    row++;
                }
                rs.close();
                rs = null;
            }


            sb.append("<recCount>").append(row - 1).append("</recCount>");
            sb.append("</reply>");
            this.setXmlReply(sb.toString());
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());

        } finally {
            try {
                if (ps_tmp != null) {
                    ps_tmp.close();
                    ps_tmp = null;
                }
                if (ps_mst != null) {
                    ps_mst.close();
                    ps_mst = null;
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
    }





    public String getXmlReply() {
        return xmlReply;
    }

    public void setXmlReply(String xmlReply) {
        this.xmlReply = xmlReply;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }
}
