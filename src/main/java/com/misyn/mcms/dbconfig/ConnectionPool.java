
package com.misyn.mcms.dbconfig;

import com.misyn.mcms.utility.Parameters;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */

public class ConnectionPool implements DataSource {
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(ConnectionPool.class);
    private static ConnectionPool connectionPool = null;
    private final HikariDataSource hikariDataSource;

    private ConnectionPool() {
        HikariConfig hc = new HikariConfig();
        hc.setDriverClassName(Parameters.getDbDriver());
        hc.setJdbcUrl(Parameters.getDbURL());
        hc.setUsername(Parameters.getDbUsername());
        hc.setPassword(Parameters.getDbPassword());
        // Pool size configurations
        hc.setMinimumIdle(Parameters.getMinimumIdle()); // Minimum number of idle connections in the pool
        hc.setMaximumPoolSize(Parameters.getMaximumPoolSize()); // Maximum number of connections in the pool
        hc.setIdleTimeout(Parameters.getIdleTimeout()); // 5 minutes
        hc.setMaxLifetime(Parameters.getMaxLifetime()); // 30 minutes
        hc.setConnectionTimeout(Parameters.getConnectionTimeout()); // 30 seconds
        hc.setLeakDetectionThreshold(Parameters.getLeakDetectionThreshold()); // 2 seconds

        // Additional configurations
        hc.setConnectionTestQuery("SELECT 1");
        hc.setPoolName("HikariCP-Pool");

        // Advanced settings
        hc.setInitializationFailTimeout(-1); // If set to a positive number, it will wait for the specified number of milliseconds for a connection to be successfully established. Otherwise, if set to 0 or negative, it will wait indefinitely.
        hc.setValidationTimeout(Parameters.getValidationTimeout()); // 5 seconds

        // Monitoring and Metrics
        hc.setMetricRegistry(null); // Set your metric registry if you have one


        hikariDataSource = new HikariDataSource(hc);
    }

    public synchronized static ConnectionPool getInstance() {
        if (connectionPool == null) {
            connectionPool = new ConnectionPool();
        }
        return connectionPool;
    }

    @Override
    public Connection getConnection() throws SQLException {
        return hikariDataSource.getConnection();
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        return hikariDataSource.getConnection(username, password);
    }

    @Override
    public PrintWriter getLogWriter() throws SQLException {
        return hikariDataSource.getLogWriter();
    }

    @Override
    public void setLogWriter(PrintWriter out) throws SQLException {
        hikariDataSource.setLogWriter(out);
    }

    @Override
    public int getLoginTimeout() throws SQLException {
        return hikariDataSource.getLoginTimeout();
    }

    @Override
    public void setLoginTimeout(int seconds) throws SQLException {
        hikariDataSource.setLoginTimeout(seconds);
    }

    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return hikariDataSource.getParentLogger();
    }

    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        return hikariDataSource.unwrap(iface);
    }

    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return hikariDataSource.isWrapperFor(iface);
    }


    public synchronized void beginTransaction(Connection connection) {
        try {
            if (null != connection) {
                connection.setAutoCommit(false);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public synchronized void commitTransaction(Connection connection) {
        try {
            if (null != connection) {
                connection.commit();
                connection.setAutoCommit(true);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public synchronized void rollbackTransaction(Connection connection) {
        try {
            if (null != connection) {
                connection.rollback();
                connection.setAutoCommit(true);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


}
