/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.dbconfig;

import com.misyn.mcms.claim.dto.PopupItemDto;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
public class DbRecordCommonFunction implements Serializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(DbRecordCommonFunction.class);
    private static DbRecordCommonFunction dbRecordCommonFunction = null;
    private static boolean isErrorExsist = false;
    private ConnectionPool cp = null;
    private String message = "";
    private ArrayList<ArrayList> rowArrayLst;

    public DbRecordCommonFunction() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static DbRecordCommonFunction getInstance() {
        if (dbRecordCommonFunction == null) {
            dbRecordCommonFunction = new DbRecordCommonFunction();
        }
        return dbRecordCommonFunction;
    }

    private void init() {
        try {
            cp = ConnectionPool.getInstance();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public boolean isIsErrorExsist() {
        return isErrorExsist;
    }

    public static void setIsErrorExsist(boolean isErrorExsist) {
        DbRecordCommonFunction.isErrorExsist = isErrorExsist;
    }

    public int executeUpdate(Connection conn, String strSQL) {
        int result = -1;
        PreparedStatement ps = null;

        try {
            ps = conn.prepareStatement(strSQL);
            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }

            } catch (Exception ex) {
            }
        }

        return result;
    }

    public int executeUpdate(Connection conn, String strSQL, int neglectdErrCode) {
        int result = -1;
        PreparedStatement ps = null;

        try {
            ps = conn.prepareStatement(strSQL);
            result = ps.executeUpdate();
        } catch (Exception e) {
            if (neglectdErrCode == 0) {
                LOGGER.error(e.getMessage());
            } else {
                LOGGER.error(e.getMessage());
            }

        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }

            } catch (Exception ex) {
            }
        }

        return result;
    }

    public ResultSet executeQuary(Connection conn, String tblName, String searchKey1) {

        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT * FROM " + tblName;
        if (!searchKey1.equals("".trim())) {
            strSQL = "SELECT * FROM " + tblName + " WHERE " + searchKey1;
        }
        try {

            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            rs = ps.executeQuery();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return rs;
    }

    public ResultSet executeQuary(String tblName, String searchKey1) {

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String strSQL = "SELECT * FROM " + tblName;

        if (!searchKey1.equals("".trim())) {
            strSQL = "SELECT * FROM " + tblName + " WHERE " + searchKey1;
        }

        try {
            if (conn == null) {
                if (cp == null) {
                    init();
                }
                conn = getJDBCConnection();
            } else {
                if (conn.isClosed()) {
                    if (cp == null) {
                        init();
                    }
                    conn = getJDBCConnection();
                }
            }
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_UPDATABLE);

            rs = ps.executeQuery();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return rs;
    }

    public int executeUpdate(String strSQL) {
        int result = -1;
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            if (conn == null) {
                if (cp == null) {
                    init();
                }
                conn = getJDBCConnection();
            } else {
                if (conn.isClosed()) {
                    if (cp == null) {
                        init();
                    }
                    conn = getJDBCConnection();
                }
            }

            ps = conn.prepareStatement(strSQL);
            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }



    public boolean isRecExists(String tblName, String searchKey1) {
        boolean b = false;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String strSQL = "SELECT 1 FROM " + tblName;

        if (!searchKey1.equals("".trim())) {
            strSQL = "SELECT 1 FROM " + tblName + " WHERE " + searchKey1;
        }
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            if (rs.next()) {
                b = true;
            }
        } catch (Exception e) {
            setIsErrorExsist(true);
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return b;
    }

    public boolean isRecExists(Connection conn, String tblName, String searchKey1) {
        boolean b = false;

        PreparedStatement ps = null;
        ResultSet rs = null;

        String strSQL = "SELECT 1 FROM " + tblName;

        if (!searchKey1.equals("".trim())) {
            strSQL = "SELECT 1 FROM " + tblName + " WHERE " + searchKey1;
        }
        try {
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            if (rs.next()) {
                b = true;
            }
        } catch (Exception e) {
            setIsErrorExsist(true);
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }
            } catch (Exception ex) {
            }
        }
        return b;
    }

    public String findRecord(String tblName, String outField, String searchKey1) {
        String strval = "Invalid";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName;

        if (!searchKey1.trim().equals("")) {
            strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName + " WHERE " + searchKey1;
        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            if (rs.next()) {
                strval = rs.getString(outField);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);

        }
        return strval;
    }

    public String findRecord(Connection conn, String tblName, String outField, String searchKey1) {
        String strval = "Invalid";

        PreparedStatement ps = null;
        ResultSet rs = null;

        String strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName;

        if (!searchKey1.trim().equals("")) {
            strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName + " WHERE " + searchKey1;
        }

        try {
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            if (rs.next()) {
                strval = rs.getString(outField);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
        }
        return strval;
    }



    public String getPopupList(String tblName, String valueField, String lableField,
                               String searchKey, String searchKeyString) {
        StringBuilder sb = new StringBuilder();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;
        try {

            if (!searchKey.equalsIgnoreCase(AppConstant.STRING_EMPTY) && !searchKeyString.equalsIgnoreCase(AppConstant.STRING_EMPTY)) {
                strSQL = "SELECT " + valueField + "," + lableField + " FROM " + tblName + " WHERE " + searchKey + ""
                        + " ORDER BY (CASE " + valueField + " WHEN " + searchKeyString + " then 0 else 1 end)";

            } else if (searchKey.equalsIgnoreCase(AppConstant.STRING_EMPTY) && !searchKeyString.equalsIgnoreCase(AppConstant.STRING_EMPTY)) {
                strSQL = "SELECT " + valueField + "," + lableField + " FROM " + tblName + " " + searchKeyString;
            } else if (!searchKey.equalsIgnoreCase(AppConstant.STRING_EMPTY) && searchKeyString.equalsIgnoreCase(AppConstant.STRING_EMPTY)) {
                strSQL = "SELECT " + valueField + "," + lableField + " FROM " + tblName + " WHERE " + searchKey + " ORDER BY " + valueField;

            } else {
                strSQL = "SELECT " + valueField + "," + lableField + " FROM " + tblName + " ORDER BY " + valueField;

            }
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            while (rs.next()) {

                sb.append("<option value=").append("\"");
                sb.append(rs.getString(valueField)).append("\"");
                sb.append(">");
                sb.append(rs.getString(lableField));
                sb.append("</option>\n");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return sb.toString();
    }

    public String getPopupList(String tblName, String valueField, String lableField, String searchKey) {
        StringBuilder sb = new StringBuilder();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;

        try {

            strSQL = "SELECT " + valueField + "," + lableField + " FROM " + tblName + " " + searchKey;

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            while (rs.next()) {

                sb.append("<option value=").append("\"");
                sb.append(rs.getString(valueField)).append("\"");
                sb.append(">");
                sb.append(rs.getString(lableField));
                sb.append("</option>\n");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return sb.toString();
    }


    public String getPopupList(String tblName, String valueField, String lableField) {
        StringBuilder sb = new StringBuilder();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;
        try {
            strSQL = "SELECT " + valueField + "," + lableField + " FROM " + tblName;
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                sb.append("<option value=").append("'");
                sb.append(rs.getString(valueField)).append("'");
                sb.append(">");
                sb.append(rs.getString(lableField));
                sb.append("</option>\n");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return sb.toString();
    }

    public List<PopupItemDto> getPopupItemDtoList(String tblName, String valueField, String lableField, String searchKey) {
        List<PopupItemDto> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;
        try {
            if (searchKey.isEmpty()) {
                strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName);
            } else {
                strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName).concat(" WHERE  ").concat(searchKey);
            }
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                PopupItemDto popupItemDto = new PopupItemDto();
                popupItemDto.setValue(rs.getString(valueField));
                popupItemDto.setLabel(rs.getString(lableField));
                list.add(popupItemDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return list;
    }


    public PopupItemDto getPopupItemDto(Connection connection, String tblName, String valueField, String lableField, String searchKey) {
        List<PopupItemDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;
        PopupItemDto popupItemDto = null;
        try {
            if (searchKey.isEmpty()) {
                strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName);
            } else {
                strSQL = "SELECT ".concat(valueField).concat(",").concat(lableField).concat(" FROM ").concat(tblName).concat(" WHERE  ").concat(searchKey);
            }
            ps = connection.prepareStatement(strSQL);
            rs = ps.executeQuery();
            if (rs.next()) {
                popupItemDto = new PopupItemDto();
                popupItemDto.setValue(rs.getString(valueField));
                popupItemDto.setLabel(rs.getString(lableField));
                return popupItemDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }

        }

        return popupItemDto;

    }
    public String getValueIdString(String tblName, String valueField, String searchFiled, String searchValue) {
        String value = "";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;
        try {
            strSQL = "SELECT " + valueField + " FROM " + tblName + " WHERE " + searchFiled + " = '" + searchValue + "'";
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            rs = ps.executeQuery();
            if (rs.next()) {
                value = rs.getString(valueField);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return value;
    }

    public String getValue(String tblName, String valueField, String searchFiled, String searchValue) {
        String value = "";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL;
        try {
            strSQL = "SELECT " + valueField + " FROM " + tblName + " WHERE " + searchFiled + " = " + searchValue;
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            rs = ps.executeQuery();
            if (rs.next()) {
                value = rs.getString(valueField);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return value;
    }

    public int getRecordCount(String countField, String searchKey, String tblName) {
        int result = 0;

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String strSQL = "SELECT COUNT(" + countField + ") AS CNT FROM " + tblName;

        if (!searchKey.equals("".trim())) {
            strSQL = "SELECT COUNT(" + countField + ") AS CNT FROM " + tblName + " WHERE " + searchKey;
        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            if (rs.next()) {
                result = rs.getInt("CNT");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return result;

    }

    public int getRecordCount_(String strSQL) {
        int result = 0;

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            if (rs.next()) {
                result = rs.getInt("CNT");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return result;

    }

    public int getRecordCount(String strSQL) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            rs.last();
            result = rs.getRow();
            rs.beforeFirst();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int getNextID(String tblName, String idFieldName, String searchKey) {
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(" + idFieldName + ") as txnID from " + tblName + " " + searchKey;
        int maxid = 0;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    maxid = rs.getInt("txnID");
                }
                maxid++;
            }

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return maxid;
    }

    public synchronized int getNextID(Connection conn, String tblName, String idFieldName, String searchKey) {
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(" + idFieldName + ") as txnID from " + tblName + " WHERE " + searchKey;
        int maxid = 0;
        try {
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    maxid = rs.getInt("txnID");
                }
                maxid++;
            }

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
            // releaseJDBCConnection(conn);
        }
        return maxid;
    }

    public synchronized ArrayList<ArrayList> getRecordRow(String tblName, String serchKey1) {
        rowArrayLst = new ArrayList<ArrayList>();
        Connection conn = null;
        Statement st = null;
        ResultSet rs = null;
        ResultSetMetaData rsmd = null;
        String strSQL = "";
        int index = 0;
        if (!serchKey1.equals("".trim())) {
            strSQL = "SELECT * FROM " + tblName + " WHERE " + serchKey1;
        } else {
            strSQL = "SELECT * FROM " + tblName;
        }

        try {
            conn = getJDBCConnection();
            st = conn.createStatement();
            rs = st.executeQuery(strSQL);
            rsmd = rs.getMetaData();

            while (rs.next()) {
                ArrayList<String> rowArray = new ArrayList<String>();
                for (int cnt = 1; cnt <= rsmd.getColumnCount(); cnt++) {
                    rowArray.add(cnt - 1, rs.getString(cnt));
                }
                rowArrayLst.add(index, rowArray);
                rowArray = null;
                index++;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (st != null) {
                    st.close();
                    st = null;
                }
                if (rs != null) {
                    rs.close();
                    rs = null;
                }

            } catch (Exception e) {
            }
            releaseJDBCConnection(conn);

        }

        return rowArrayLst;
    }


    /**
     * Get a database connection from the connection pool
     */
    private Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
