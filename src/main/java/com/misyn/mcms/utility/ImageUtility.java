package com.misyn.mcms.utility;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.imageio.stream.MemoryCacheImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Iterator;
public class ImageUtility {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageUtility.class);

    public static void constrain(String srcFilename, String destFilename,
                                 int height, int width) {
        try {
            FileInputStream fis = new FileInputStream(srcFilename);
            MemoryCacheImageOutputStream mos = new MemoryCacheImageOutputStream(
                    new FileOutputStream(destFilename));
            constrain(fis, mos, height, width);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static byte[] constrain(String srcFilename, int height, int width) {
        try {
            FileInputStream fis = new FileInputStream(srcFilename);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            MemoryCacheImageOutputStream mos = new MemoryCacheImageOutputStream(baos);
            constrain(fis, mos, height, width);
            return baos.toByteArray();
            //ByteArrayInputSteam bais = new ByteArrayInputStream(
            // baos.toByteArray() );
            //return bais;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return new byte[]{};
    }

    public static byte[] constrain(ByteArrayInputStream bais, int height, int width) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            MemoryCacheImageOutputStream mos = new MemoryCacheImageOutputStream(baos);
            constrain(bais, mos, height, width);
            return baos.toByteArray();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return new byte[]{};
    }

    public static byte[] constrain(FileInputStream fis, int height, int width) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            MemoryCacheImageOutputStream mos = new MemoryCacheImageOutputStream(baos);
            constrain(fis, mos, height, width);
            return baos.toByteArray();
            //ByteArrayInputSteam bais = new ByteArrayInputStream(
            // baos.toByteArray() );
            //return bais;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return new byte[]{};
    }

    public static byte[] constrain_input(InputStream fis, int height, int width) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            MemoryCacheImageOutputStream mos = new MemoryCacheImageOutputStream(baos);
            constrain(fis, mos, height, width);
            return baos.toByteArray();
            //ByteArrayInputSteam bais = new ByteArrayInputStream(
            // baos.toByteArray() );
            //return bais;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return new byte[]{};
    }

    public static void constrain(String srcFilename, OutputStream os, int height, int width) {
        try {
            FileInputStream fis = new FileInputStream(srcFilename);
            MemoryCacheImageOutputStream mos = new MemoryCacheImageOutputStream(os);
            constrain(fis, mos, height, width);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static void constrain(InputStream is, ImageOutputStream os, int imageHeight, int imageWidth) {
        try {
            // Read the source file
            BufferedImage input = ImageIO.read(is);

            // Get the original size of the image
            int srcHeight = input.getHeight();
            int srcWidth = input.getWidth();

            // Constrain the thumbnail to a predefined box size
            int height = imageHeight;
            int width = imageWidth;
            if (srcHeight > srcWidth) {
                width = (int) (((float) height / (float) srcHeight) * (float) srcWidth);
            } else if (srcWidth > srcHeight) {
                height = (int) (((float) width / (float) srcWidth) * (float) srcHeight);
            }

            // Create a new thumbnail BufferedImage
            BufferedImage thumb = new BufferedImage(width, height, BufferedImage.TYPE_USHORT_565_RGB);
            Graphics g = thumb.getGraphics();
            g.drawImage(input, 0, 0, width, height, null);

            // Get Writer and set compression
            Iterator<ImageWriter> iter = ImageIO.getImageWritersByFormatName("JPG");
            if (iter.hasNext()) {
                ImageWriter writer = (ImageWriter) iter.next();
                ImageWriteParam iwp = writer.getDefaultWriteParam();
                iwp.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                iwp.setCompressionQuality(1.0f);
                writer.setOutput(os);
                IIOImage image = new IIOImage(thumb, null, null);
                writer.write(null, image, iwp);
            }

            Iterator<ImageWriter> iter2 = ImageIO.getImageWritersByFormatName("JIF");
            if (iter2.hasNext()) {
                ImageWriter writer = (ImageWriter) iter2.next();
                ImageWriteParam iwp = writer.getDefaultWriteParam();
                iwp.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                iwp.setCompressionQuality(1.0f);
                writer.setOutput(os);
                IIOImage image = new IIOImage(thumb, null, null);
                writer.write(null, image, iwp);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


}
