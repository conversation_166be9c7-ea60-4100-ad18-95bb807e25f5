package com.misyn.mcms.utility;

import com.misyn.mcms.claim.service.AbstractBaseService;
import jakarta.jms.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
public class ActiveMQEmail extends AbstractBaseService<ActiveMQEmail> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActiveMQEmail.class);

    private static final String EMAIL_LOG_ID = "EMAIL_LOG_ID";
    private static final String FROM_ADDRESS_KEY = "FROM_ADDRESS";
    private static final String TO_ADDRESS_KEY = "TO_ADDRESS";
    private static final String CC_ADDRESS_KEY = "CC_ADDRESS";
    private static final String SUBJECT_KEY = "SUBJECT";
    private static final String BODY_KEY = "BODY";
    private static final String FILE_NAME_LIST_KEY = "FILE_NAME_LIST";

    private String jmsQueueName = "LOLC-MCMS-Email-Queue";

    public void sendFromEmail(Email email) throws JMSException {
        Connection connection = null;
        Session session = null;
        try {
            connection = getActiveMQConnection();
            session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);

            Destination destination = session.createQueue(jmsQueueName);
            MessageProducer producer = session.createProducer(destination);
            producer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
            MapMessage mapMessage = session.createMapMessage();
            mapMessage.setObject(EMAIL_LOG_ID, email.getSaveId());
            mapMessage.setObject(FROM_ADDRESS_KEY, email.getFromAddress());
            mapMessage.setObject(TO_ADDRESS_KEY, email.getToAddresses());
            mapMessage.setObject(CC_ADDRESS_KEY, email.getCcAddresses());// save CC Address
            mapMessage.setObject(SUBJECT_KEY, email.getSubject());// save Subject
            mapMessage.setObject(BODY_KEY, email.getEmailMassege());// save Body
            mapMessage.setObject(FILE_NAME_LIST_KEY, email.getAttachmentNameList());
            Map<String, byte[]> map = email.getAttachmentMap();
            for (Map.Entry<String, byte[]> entry : map.entrySet()) {
                String key = entry.getKey();
                byte[] value = entry.getValue();
                mapMessage.setBytes(key, value);
            }
            producer.send(mapMessage);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            assert session != null;
            session.close();
            closeActiveMQConnection(connection);

        }

    }

}
