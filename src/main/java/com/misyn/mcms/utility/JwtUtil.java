package com.misyn.mcms.utility;


import com.misyn.mcms.claim.dto.JWTClaimDto;
import jakarta.json.Json;
import jakarta.json.JsonObject;
import jakarta.json.JsonReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.StringReader;
import java.util.Base64;

public class JwtUtil { // Use a secure key
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtUtil.class);


    public static JWTClaimDto decodeJwt(String token) {
        try {
            String[] parts = token.split("\\.");
            if (parts.length < 2) {
                throw new IllegalArgumentException("Invalid JWT token");
            }
            String payloadJson = new String(Base64.getUrlDecoder().decode(parts[1]));

            try (JsonReader jsonReader = Json.createReader(new StringReader(payloadJson))) {
                JsonObject claims = jsonReader.readObject();
                JWTClaimDto jwtClaimDto = new JWTClaimDto();
                jwtClaimDto.setId(claims.getString("sub"));
                jwtClaimDto.setUsername(claims.getString("preferred_username"));
                jwtClaimDto.setEmail(claims.getString("email"));
                LOGGER.debug("JWT Claims: {}", claims);
                return jwtClaimDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }


}
