package com.misyn.mcms.utility;

import jakarta.servlet.ServletException;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.ClientBuilder;
import jakarta.ws.rs.client.Invocation;
import jakarta.ws.rs.client.WebTarget;
import jakarta.ws.rs.core.Response;

import java.io.IOException;
import java.io.InputStream;

@WebServlet("/view-image")
public class ImageProxyServlet extends HttpServlet {
   // private static final String IMAGE_API_URL = "http://localhost:9090/api/v1/file/image?objectName=xyz/1.jpg"; // Spring Boot API URL
    private static final String IMAGE_API_URL = "http://localhost:9090/api/v1/file/pdf?objectName=claim-documents/500000677/documents/202503061248542_admin.pdf"; // Spring Boot API URL

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(IMAGE_API_URL);
            Invocation.Builder request = target.request();

            try (Response apiResponse = request.get()) {
                if (apiResponse.getStatus() == Response.Status.OK.getStatusCode()) {
                    resp.setContentType(apiResponse.getHeaderString("Content-Type")); // Set image type
                    ServletOutputStream outStream = resp.getOutputStream();

                    try (InputStream inputStream = apiResponse.readEntity(InputStream.class)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outStream.write(buffer, 0, bytesRead);
                        }
                        outStream.flush();
                    }
                } else {
                    resp.sendError(HttpServletResponse.SC_BAD_REQUEST, "Failed to fetch image");
                }
            }
        } catch (Exception e) {
            throw new ServletException("Error fetching image", e);
        }
    }


    private void streamContent(HttpServletResponse resp, String apiUrl) throws ServletException, IOException {
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(apiUrl);
            Invocation.Builder request = target.request();

            try (Response apiResponse = request.get()) {
                if (apiResponse.getStatus() == Response.Status.OK.getStatusCode()) {
                    String contentType = apiResponse.getHeaderString("Content-Type");
                    resp.setContentType(contentType);

                    try (ServletOutputStream outStream = resp.getOutputStream();
                         InputStream inputStream = apiResponse.readEntity(InputStream.class)) {

                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outStream.write(buffer, 0, bytesRead);
                        }
                        outStream.flush();
                    }
                } else {
                    resp.sendError(HttpServletResponse.SC_BAD_REQUEST, "Failed to fetch content");
                }
            }
        } catch (Exception e) {
            throw new ServletException("Error fetching content", e);
        }
    }
}
