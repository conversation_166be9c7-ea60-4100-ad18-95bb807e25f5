package com.misyn.mcms.utility;

import jakarta.servlet.ServletException;
import jakarta.ws.rs.client.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.io.InputStream;

public class DocumentApiClientUtil {
    public static void uploadFile(byte[] fileBytes,String objectName) {
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(Parameters.getDocumentServiceApiUrl().concat("/v1/file/upload/byte-array"));
            Invocation.Builder request = target
                    .queryParam("objectName", objectName) // Add objectName as a query parameter
                    .request(MediaType.APPLICATION_JSON);
            try (Response response = request.post(Entity.entity(fileBytes, MediaType.APPLICATION_OCTET_STREAM))) {
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    System.out.println("File uploaded successfully: " + response.readEntity(String.class));
                } else {
                    System.out.println("Failed to upload file. Status: " + response.getStatus());
                }
            }
        }
    }

    public static void deleteFile(String objectName) {
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(Parameters.getDocumentServiceApiUrl().concat("/v1/file/delete"));
            Invocation.Builder request = target
                    .queryParam("objectName", objectName) // Add objectName as a query parameter
                    .request(MediaType.APPLICATION_JSON);
            try (Response response = request.delete()) {
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    System.out.println("File deleted successfully: " + response.readEntity(String.class));
                } else {
                    System.out.println("Failed to delete file. Status: " + response.getStatus());
                }
            }
        }
    }

    public static  InputStream streamContent(String apiUrl) throws ServletException {
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(apiUrl);
            Invocation.Builder request = target.request();

            Response apiResponse = request.get(); // Keep response open until stream is read

            if (apiResponse.getStatus() == Response.Status.OK.getStatusCode()) {
                return apiResponse.readEntity(InputStream.class);
            } else {
                throw new ServletException("Failed to fetch content. HTTP Status: " + apiResponse.getStatus());
            }
        } catch (Exception e) {
            throw new ServletException("Error fetching content", e);
        }
    }
}
