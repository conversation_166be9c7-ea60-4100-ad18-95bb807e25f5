/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.utility;

import java.io.Serializable;
public class ListBoxItem implements Serializable {

    private Object value = new Object();
    private String lable = AppConstant.EMPTY_STRING;
    private String optionalValue1 = AppConstant.EMPTY_STRING;
    private String optionalValue2 = AppConstant.EMPTY_STRING;
    private String optionalValue3 = AppConstant.EMPTY_STRING;
    private String optionalValue4 = AppConstant.EMPTY_STRING;
    private String optionalValue5 = AppConstant.EMPTY_STRING;
    private String optionalValue6 = AppConstant.EMPTY_STRING;
    private String optionalValue7 = AppConstant.EMPTY_STRING;

    public ListBoxItem(Object value, String lable) {
        this.value = value;
        this.lable = lable;
    }

    public ListBoxItem(Object value, String lable, String optionalValue1, String optionalValue2, String optionalValue3, String optionalValue4, String optionalValue5, String optionalValue6, String optionalValue7) {
        this.value = value;
        this.lable = lable;
        this.optionalValue1 = optionalValue1;
        this.optionalValue2 = optionalValue2;
        this.optionalValue3 = optionalValue3;
        this.optionalValue4 = optionalValue4;
        this.optionalValue5 = optionalValue5;
        this.optionalValue6 = optionalValue6;
        this.optionalValue7 = optionalValue7;
    }

    public String getLable() {
        return lable;
    }

    public void setLable(String lable) {
        this.lable = lable;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
