package com.misyn.mcms.utility;

import java.io.File;
public class Mail {

    public String fromAddress = "";
    public String toAddress = "";
    public String ccAddress = "";
    public String bccAddress = "";
    public String subject = "";
    public String body = "";
    public File file = null;

    /**
     * Constructor
     *
     * @param <tt>fromAddress</tt> From Address
     * @param <tt>toAddress</tt>   To Address
     * @param <tt>ccAddress</tt>   Cc Address
     * @param <tt>bccAddress</tt>  Bcc Address
     * @param <tt>subject</tt>     Subject
     * @param <tt>body</tt>        Body
     */
    public Mail(String fromAddress, String toAddress, String ccAddress, String bccAddress, String subject,
                String body) {
        this.fromAddress = fromAddress;
        this.toAddress = toAddress;
        this.ccAddress = ccAddress;
        this.bccAddress = bccAddress;
        this.subject = subject;
        this.body = body;
    }

    /**
     * Constructor
     *
     * @param <tt>fromAddress</tt> From Address
     * @param <tt>toAddress</tt>   To Address
     * @param <tt>ccAddress</tt>   Cc Address
     * @param <tt>bccAddress</tt>  Bcc Address
     * @param <tt>subject</tt>     Subject
     * @param <tt>body</tt>        Body
     * @param <tt>file</tt>        Attachment
     */
    public Mail(String fromAddress, String toAddress, String ccAddress, String bccAddress, String subject,
                String body, File file) {
        this.fromAddress = fromAddress;
        this.toAddress = toAddress;
        this.ccAddress = ccAddress;
        this.bccAddress = bccAddress;
        this.subject = subject;
        this.body = body;
        this.file = file;
    }
}
