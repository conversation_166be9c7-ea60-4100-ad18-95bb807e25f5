/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.misyn.mcms.utility;

import java.math.BigInteger;
public class DataValidate {

    /**
     * Checks whether the String "input" has only digits
     *
     * @param <tt>input</tt> Input string
     * @return Result as <tt>boolean</tt>
     */
    public static boolean isDigit(String input) {
        boolean isDigit = true;
        int len = input.length();
        char[] tempChar = new char[len];
        input.getChars(0, len, tempChar, 0);
        for (int i = 0; i < len; i++) {
            if (!Character.isDigit(tempChar[i])) {
                isDigit = false;
                break;
            }
        }
        return isDigit;
    }

    /**
     * Validate the input String for numeric
     *
     * @param <tt>input</tt> Input
     * @return Result as <tt>boolean</tt>
     */
    public static boolean isNumeric(String input) {
        try {
            BigInteger temp = new BigInteger(input);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Checks whether the String "input" is a Integer
     *
     * @param <tt>String</tt> Input
     * @return Result as <tt>boolean</tt>
     */
    public static boolean isInt(String input) {
        try {
            Integer i = Integer.valueOf(input);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Checks whether the String "input" exceeds the given length
     *
     * @param <tt>input</tt>  Input string
     * @param <tt>length</tt> String Length
     * @return Result as <tt>boolean</tt>
     */
    public static boolean checkLengthExceeds(String input, int length) {
        int result = input.trim().length();
        if (result > length) return true;
        else return false;
    }

    /**
     * Checks whether a given string is blank ( empty String or Spaces )
     *
     * @param <tt>input</tt> Input string
     * @return Result as <tt>boolean</tt>
     */
    public static boolean isBlank(String input) {
        input = input.trim();
        if ((input == null) || (input.compareTo("") == 0)) return true;
        else return false;
    }

    /**
     * Validates e-amil field: checks for "@" and "." characters
     *
     * @param <tt>email</tt> Email
     * @return Result as <tt>boolean</tt>
     */
    public static boolean validateEmail(String mail) {
        if ((mail.indexOf('@') == -1) || (mail.indexOf('.') == -1)) return false;
        else return true;
    }

}
