package com.misyn.mcms.utility;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
public class Email {

    private static final long serialVersionUID = 1000L;
    private static final String EMPTY_STRING = "";
    List<File> fileList = new ArrayList<>();
    private int saveId;
    private String fromAddress;
    private String toAddresses;
    private String ccAddresses;
    private String subject;
    private String emailMassege;
    private String phoneNo;
    private int smsId;
    private String smslMassege;
    private long templateEmailId;
    private long templateSmsId;
    private String tagName;
    private String jmsAttachment;
    private int jmsRetryAttempts;
    private String jmsSentTime;
    private String mailAttachment;
    private int mailRetryAttempts;
    private String mailSentTime;
    private String status;
    private int upId;
    private ArrayList<String> parameterEmail = new ArrayList<>();
    private ArrayList<String> parameterSms = new ArrayList<>();
    private List<String> attachmentNameList;
    private Map<String, byte[]> attachmentMap;

    public Email() {

    }

    public Email(int saveId, String fromAddress, String toAddresses, String ccAddresses, String subject, String emailMassege, String phoneNo, String smslMassege) {
        this.saveId = saveId;
        this.fromAddress = fromAddress;
        this.toAddresses = toAddresses;
        this.ccAddresses = ccAddresses;
        this.subject = subject;
        this.emailMassege = emailMassege;
        this.phoneNo = phoneNo;
        this.smslMassege = smslMassege;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public static String getEmptyString() {
        return EMPTY_STRING;
    }

    public int getSaveId() {
        return saveId;
    }

    public void setSaveId(int saveId) {
        this.saveId = saveId;
    }

    public String getFromAddress() {
        return fromAddress;
    }

    public void setFromAddress(String fromAddress) {
        this.fromAddress = fromAddress;
    }

    public String getToAddresses() {
        return toAddresses;
    }

    public void setToAddresses(String toAddresses) {
        this.toAddresses = toAddresses;
    }

    public String getCcAddresses() {
        return ccAddresses;
    }

    public void setCcAddresses(String ccAddresses) {
        this.ccAddresses = ccAddresses;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getEmailMassege() {
        return emailMassege;
    }

    public void setEmailMassege(String emailMassege) {
        this.emailMassege = emailMassege;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public int getSmsId() {
        return smsId;
    }

    public void setSmsId(int smsId) {
        this.smsId = smsId;
    }

    public String getSmslMassege() {
        return smslMassege;
    }

    public void setSmslMassege(String smslMassege) {
        this.smslMassege = smslMassege;
    }

    public long getTemplateEmailId() {
        return templateEmailId;
    }

    public void setTemplateEmailId(long templateEmailId) {
        this.templateEmailId = templateEmailId;
    }

    public long getTemplateSmsId() {
        return templateSmsId;
    }

    public void setTemplateSmsId(int templateSmsId) {
        this.templateSmsId = templateSmsId;
    }

    public void setTemplateSmsId(long templateSmsId) {
        this.templateSmsId = templateSmsId;
    }

    public ArrayList<String> getParameterEmail() {
        return parameterEmail;
    }

    public void setParameterEmail(ArrayList<String> parameterEmail) {
        this.parameterEmail = parameterEmail;
    }

    public ArrayList<String> getParameterSms() {
        return parameterSms;
    }

    public void setParameterSms(ArrayList<String> parameterSms) {
        this.parameterSms = parameterSms;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getJmsAttachment() {
        return jmsAttachment;
    }

    public void setJmsAttachment(String jmsAttachment) {
        this.jmsAttachment = jmsAttachment;
    }

    public int getJmsRetryAttempts() {
        return jmsRetryAttempts;
    }

    public void setJmsRetryAttempts(int jmsRetryAttempts) {
        this.jmsRetryAttempts = jmsRetryAttempts;
    }

    public String getJmsSentTime() {
        return jmsSentTime;
    }

    public void setJmsSentTime(String jmsSentTime) {
        this.jmsSentTime = jmsSentTime;
    }

    public String getMailAttachment() {
        return mailAttachment;
    }

    public void setMailAttachment(String mailAttachment) {
        this.mailAttachment = mailAttachment;
    }

    public int getMailRetryAttempts() {
        return mailRetryAttempts;
    }

    public void setMailRetryAttempts(int mailRetryAttempts) {
        this.mailRetryAttempts = mailRetryAttempts;
    }

    public String getMailSentTime() {
        return mailSentTime;
    }

    public void setMailSentTime(String mailSentTime) {
        this.mailSentTime = mailSentTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getUpId() {
        return upId;
    }

    public void setUpId(int upId) {
        this.upId = upId;
    }

    public List<String> getAttachmentNameList() {
        return attachmentNameList;
    }

    public void setAttachmentNameList(List<String> attachmentNameList) {
        this.attachmentNameList = attachmentNameList;
    }

    public Map<String, byte[]> getAttachmentMap() {
        return attachmentMap;
    }

    public void setAttachmentMap(Map<String, byte[]> attachmentMap) {
        this.attachmentMap = attachmentMap;
    }

    public List<File> getFileList() {
        return fileList;
    }

    public void setFileList(List<File> fileList) {
        this.fileList = fileList;
    }
}
