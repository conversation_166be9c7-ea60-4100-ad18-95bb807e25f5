package com.misyn.mcms.utility;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
public class ShaHashGenerator {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShaHashGenerator.class);

    private ShaHashGenerator() {
    }

    private static byte[] getShaByte(String value, String algorithm) {
        MessageDigest digest;
        byte[] encodedhash = null;
        try {
            digest = MessageDigest.getInstance(algorithm);
            encodedhash = digest.digest(
                    value.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return encodedhash;
    }

    private static String bytesToHex(byte[] hash) {
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < hash.length; i++) {
            String hex = Integer.toHexString(0xff & hash[i]);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static String getSha256HashValue(String value) {
        String bytesToHex = null;
        byte[] encodedhash = getShaByte(value, "SHA-256");
        if (null != encodedhash)
            bytesToHex = bytesToHex(encodedhash);
        return bytesToHex;
    }

    public static String getSha512HashValue(String value) {
        String bytesToHex = null;
        byte[] encodedhash = getShaByte(value, "SHA-512");
        if (null != encodedhash)
            bytesToHex = bytesToHex(encodedhash);
        return bytesToHex;
    }

}
