/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.utility;

import com.misyn.mcms.claim.exception.MisynAppException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.ResourceBundle;

public class Parameters implements Serializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(Parameters.class);

    private static Parameters parameters = null;
    private static String dbDriver = "com.mysql.jdbc.Driver";
    private static String dbURL = "";
    private static String dbUsername = "";
    private static String dbPassword = "";

    private static int minimumIdle;
    private static int maximumPoolSize;
    private static long idleTimeout;
    private static long maxLifetime;
    private static long connectionTimeout;
    private static long leakDetectionThreshold;
    private static long validationTimeout;


    private static String emailAddress = "";
    private static String path = "D:/Projects/UAL/General/";


    private static int noRecordPerPage = 0;
    private static String companyTitle = "";
    private static String tempFileDirectory = "";

    private static String activeMQBrokerUrl = "";
    private static String activeMQUser = "";
    private static String activeMQPassword = "";
    private static String mailSendUser = "";
    private static String appUrl = "";
    private static String syncAppUrl = "";
    private static Integer theftClaimPeriod;
    private static double imageResizePercent = 0.65d;
    private static float imageCompressionQualityFactor = 0.5f;
    private static String internalAppUrl;

    private static Integer documentNotificationTimeout = 0;
    private static String profile = AppConstant.STRING_EMPTY;
    private static Integer ariNotificationTimeout = 0;
    private static String authServiceLogoutUrl = AppConstant.STRING_EMPTY;
    private static String documentServiceApiUrl = AppConstant.STRING_EMPTY;
    private static String claimDocumentDirectory = AppConstant.STRING_EMPTY;
    private static String keycloakServerUrl = AppConstant.STRING_EMPTY;
    private static String keycloakRealm = AppConstant.STRING_EMPTY;
    private static String keycloakClientId = AppConstant.STRING_EMPTY;
    private static String keycloakClientSecret = AppConstant.STRING_EMPTY;
    private static String redisHost = AppConstant.STRING_EMPTY;
    private static String tokenContentType = AppConstant.STRING_EMPTY;
    private static String tokenClientId = AppConstant.STRING_EMPTY;
    private static String tokenClientSecret = AppConstant.STRING_EMPTY;
    private static String tokenGrantType = AppConstant.STRING_EMPTY;
    private static String tokenUsername = AppConstant.STRING_EMPTY;
    private static String tokenPassword = AppConstant.STRING_EMPTY;
    private static String tokenUrl = AppConstant.STRING_EMPTY;
    private static String saveEndpointUrl = AppConstant.STRING_EMPTY;
    private static String saveEndpointContentType = AppConstant.STRING_EMPTY;
    private static String adminEndpointUrl = AppConstant.STRING_EMPTY;


    private Parameters() {
        try {
            LOGGER.info("Loading application properties");
            setProperties();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynAppException(e.getMessage(), e);
        }
    }

    public static String getKeycloakRealm() {
        return keycloakRealm;
    }

    public static String getKeycloakClientId() {
        return keycloakClientId;
    }

    public static String getKeycloakClientSecret() {
        return keycloakClientSecret;
    }

    public static synchronized Parameters getInstance() {
        if (null == parameters) {
            parameters = new Parameters();
        }
        return parameters;
    }

    public static String getDocumentServiceApiUrl() {
        return documentServiceApiUrl;
    }


    public static String getEmailAddress() {
        return emailAddress;
    }


    public static String getPath() {
        return path;
    }


    public static int getNoRecordPerPage() {
        return noRecordPerPage;
    }


    public static String getCompanyTitle() {
        return companyTitle;
    }

    public static String getKeycloakServerUrl() {
        return keycloakServerUrl;
    }

    public static String getTempFileDirectory() {
        return tempFileDirectory;
    }


    public static String getActiveMQBrokerUrl() {
        return activeMQBrokerUrl;
    }


    public static String getActiveMQUser() {
        return activeMQUser;
    }


    public static String getActiveMQPassword() {
        return activeMQPassword;
    }


    public static String getMailSendUser() {
        return mailSendUser;
    }


    public static String getAppUrl() {
        return appUrl;
    }


    public static String getSyncAppUrl() {
        return syncAppUrl;
    }

    public static Integer getTheftClaimPeriod() {
        return theftClaimPeriod;
    }

    public static double getImageResizePercent() {
        return imageResizePercent;
    }

    public static float getImageCompressionQualityFactor() {
        return imageCompressionQualityFactor;
    }

    public static String getInternalAppUrl() {
        return internalAppUrl;
    }


    public static Integer getDocumentNotificationTimeout() {
        return documentNotificationTimeout;
    }


    public static String getProfile() {
        return profile;
    }

    public static void setProfile(String profile) {
        Parameters.profile = profile;
    }

    public static Integer getAriNotificationTimeout() {
        return ariNotificationTimeout;
    }


    public static String getDbDriver() {
        return dbDriver;
    }

    public static String getDbURL() {
        return dbURL;
    }

    public static String getDbUsername() {
        return dbUsername;
    }

    public static String getDbPassword() {
        return dbPassword;
    }

    public static String getAuthServiceLogoutUrl() {
        return authServiceLogoutUrl;
    }

    public static int getMinimumIdle() {
        return minimumIdle;
    }

    public static int getMaximumPoolSize() {
        return maximumPoolSize;
    }

    public static long getIdleTimeout() {
        return idleTimeout;
    }

    public static long getMaxLifetime() {
        return maxLifetime;
    }

    public static long getConnectionTimeout() {
        return connectionTimeout;
    }

    public static long getLeakDetectionThreshold() {
        return leakDetectionThreshold;
    }

    public static String getClaimDocumentDirectory() {
        return claimDocumentDirectory;
    }

    public static long getValidationTimeout() {
        return validationTimeout;
    }

    public static String getRedisHost() {
        return redisHost;
    }

    public static void setRedisHost(String redisHost) {
        Parameters.redisHost = redisHost;
    }


    public static String getTokenContentType() {
        return tokenContentType;
    }

    public static String getTokenClientId() {
        return tokenClientId;
    }

    public static String getTokenClientSecret() {
        return tokenClientSecret;
    }

    public static String getTokenGrantType() {
        return tokenGrantType;
    }

    public static String getTokenUsername() {
        return tokenUsername;
    }

    public static String getTokenPassword() {
        return tokenPassword;
    }

    public static String getTokenUrl() {
        return tokenUrl;
    }

    public static String getSaveEndpointUrl() {
        return saveEndpointUrl;
    }

    public static String getSaveEndpointContentType() {
        return saveEndpointContentType;
    }

    public static String getAdminEndpointUrl() {
        return adminEndpointUrl;
    }
    private void setProperties() {
        try {
            ResourceBundle rb = ResourceBundle.getBundle("application");
            keycloakServerUrl = rb.getString("KeycloakServerUrl").trim();
            keycloakRealm = rb.getString("KeycloakRealm").trim();
            keycloakClientId = rb.getString("KeycloakClientId").trim();
            keycloakClientSecret = rb.getString("KeycloakClientSecret").trim();
            authServiceLogoutUrl = rb.getString("AuthServiceLogoutUrl").trim();
            documentServiceApiUrl = rb.getString("DocumentServiceApiUrl").trim();
            claimDocumentDirectory = rb.getString("ClaimDocumentDirectory").trim();
            dbDriver = rb.getString("DatabaseDriver").trim();
            dbURL = rb.getString("DatabaseURL").trim();
            dbUsername = rb.getString("DbUserName").trim();
            dbPassword = rb.getString("DbPassword").trim();
            minimumIdle = Integer.parseInt(rb.getString("MinimumIdle").trim());
            maximumPoolSize = Integer.parseInt(rb.getString("MaximumPoolSize").trim());
            idleTimeout = Long.parseLong(rb.getString("IdleTimeout").trim());
            maxLifetime = Long.parseLong(rb.getString("MaxLifetime").trim());
            connectionTimeout = Long.parseLong(rb.getString("ConnectionTimeout").trim());
            leakDetectionThreshold = Long.parseLong(rb.getString("LeakDetectionThreshold").trim());
            validationTimeout = Long.parseLong(rb.getString("ValidationTimeout").trim());
            path = rb.getString("Path");
            tempFileDirectory = rb.getString("TempFileDirectory");
            emailAddress = rb.getString("MailAddress").trim();
            noRecordPerPage = Integer.parseInt(rb.getString("NoRecordPerPage").trim());
            companyTitle = rb.getString("CompanyTitle").trim();

            activeMQBrokerUrl = rb.getString("ActiveMQBroker");
            activeMQUser = rb.getString("ActiveMQUser");
            activeMQPassword = rb.getString("ActiveMQPassword");
            mailSendUser = rb.getString("MailSendUser");
            appUrl = rb.getString("AppUrl");
            internalAppUrl = rb.getString("InternalAppUrl");
            syncAppUrl = rb.getString("SyncAppUrl");
            theftClaimPeriod = Integer.parseInt(rb.getString("TheftClaimPeriod"));
            imageResizePercent = Double.parseDouble(rb.getString("ImageResizePercent"));
            imageCompressionQualityFactor = Float.parseFloat(rb.getString("ImageCompressionQualityFactor"));
            documentNotificationTimeout = Integer.parseInt(rb.getString("DocumentNotificationTimeout"));
            ariNotificationTimeout = Integer.parseInt(rb.getString("AriNotificationTimeout"));
            profile = rb.getString("Profile");
            tokenContentType = rb.getString("TokenContentType").trim();
            tokenClientId = rb.getString("TokenClientId").trim();
            tokenClientSecret = rb.getString("TokenClientSecret").trim();
            tokenGrantType = rb.getString("TokenGrantType").trim();
            tokenUsername = rb.getString("TokenUsername").trim();
            tokenPassword = rb.getString("TokenPassword").trim();
            tokenUrl = rb.getString("TokenUrl").trim();
            saveEndpointUrl = rb.getString("SaveEndpointUrl").trim();
            saveEndpointContentType = rb.getString("SaveEndpointContentType").trim();
            adminEndpointUrl = rb.getString("AdminEndpointUrl").trim();
            redisHost = rb.getString("redis-host");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new MisynAppException(e.getMessage(), e);
        }

    }
}
