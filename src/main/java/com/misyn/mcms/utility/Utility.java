package com.misyn.mcms.utility;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class Utility implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(Utility.class);



    private static final String EMAIL_PATTERN
            = "^[_A-Za-z0-9-+]+(\\.[_A-Za-z0-9-]+)*@"
            + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";
    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);
    private static int daysInMonth[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    private static String loanList[] = {"1AF", "1PL", "1ST", "2AF", "3AF", "IL"};
    //private static String depositList[] = {"1AF","1PL","1ST","2AF","3AF","IL"};
    private static String strMonths[] = {"January", "February", "March", "April", "May", "June", "July",
            "August", "September", "October", "November", "December"};
    private static String dayStr[] = {"st", "nd", "rd", "th", "th",
            "th", "th", "th", "th", "th",
            "th", "th", "rd", "th", "th",
            "th", "th", "th", "th", "th",
            "st", "nd", "rd", "th", "th",
            "th", "th", "th", "th", "th",
            "st"};
    private static Matcher matcher;

    public static boolean isValidateEmail(final String hex) {
        matcher = pattern.matcher(hex);
        return !matcher.matches();

    }


    public static int getBankCode() {
        return 7214;
    }


    public static int getBankCode2() {
        return 7214;
    }


    public static int getBranchCode() {
        return 900;
    }


    public static String getLanguage(int languageID) {
        switch (languageID) {
            case 1:
                return "ENGLISH";
            case 2:
                return "SINHALA";
            case 3:
                return "TAMIL";
            default:
                return "ENGLISH";
        }
    }


    public static int getLanguageID(char languageCode) {
        switch (languageCode) {
            case 'E':
                return 1;
            case 'S':
                return 2;
            case 'T':
                return 3;
            default:
                return 1;
        }
    }


    public static int getNumericMonth(String month) {
        String months[] = {"JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL",
                "AUG", "SEP", "OCT", "NOV", "DEC"};
        int i = 0;
        while (i < 12) {
            if (months[i].equalsIgnoreCase(month)) {
                return (i + 1);
            }
            i++;
        }
        return 0;
    }


    public static String getMonthName(int month) {
        String strMonths[] = {"JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL",
                "AUG", "SEP", "OCT", "NOV", "DEC"};
        return strMonths[month - 1];
    }


    public static String getMonthFullName(int month, int language) {
        return strMonths[month - 1];
    }


    public static String getMonthFullName(String month) {
        return getMonthFullName(Integer.parseInt(month));
    }


    public static String getMonthFullName(int month) {
        return strMonths[month - 1];
    }


    public static int getLastDayOfMonth(int year, int month) {
        if (month == 2) {
            return ((0 == year % 4) && (0 != (year % 100)))
                    || (0 == year % 400) ? 29 : 28;
        } else {
            return daysInMonth[month - 1];
        }
    }


    public static String addZeroRJ(String str, int length) {
        return str.length() < length ? addZeroRJ("0" + str, length) : str;
    }


    public static double formatCurrencyToDouble(String curruncyValue) {
        String s2 = curruncyValue.replaceAll("[,]", "");
        double d = 0.0;
        try {
            d = Double.parseDouble(s2);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return d;
    }


    public static String formatCurrency(String value) {
        BigDecimal bg = new BigDecimal(value);
        return formatCurrency(bg.doubleValue());
    }


    public static String formatCurrency(double value) {
        DecimalFormat df = new DecimalFormat("###,##0.00;(###,##0.00)");
        return df.format(value);
    }

    public static String formatCurrency(BigDecimal value) {
        DecimalFormat df = new DecimalFormat("###,##0.00;(###,##0.00)");
        return df.format(value);
    }

    public static String formatNumber(long value, String format) {
        DecimalFormat df = new DecimalFormat(format);
        return df.format(value);
    }

    public static String formatCurrency(String value, String delim) {
        BigDecimal bg = new BigDecimal(value);
        return formatCurrency(bg.doubleValue(), delim);
    }

    public static String formatCurrency(double value, String delim) {
        DecimalFormat df = new DecimalFormat("###,##0.00;" + delim + "###,##0.00");
        return df.format(value);
    }

    public static String formatCurrency(double value, String delim, String decimal) {
        DecimalFormat df = new DecimalFormat("###,##0." + decimal + ";" + delim + "###,##0." + decimal + "");
        return df.format(value);
    }

    public static String formatCurrency(String value, String delim, String decimal) {
        BigDecimal bg = new BigDecimal(value);
        return formatCurrency(bg.doubleValue(), delim, decimal);
    }


    public static String formatCurrency2(String value, String decimal) {
        return formatCurrency2(Double.parseDouble(value), decimal);
    }


    public static String formatCurrency2(double value, String decimal) {
        DecimalFormat df = new DecimalFormat("#####0." + decimal + ";-#####0." + decimal + "");
        return df.format(value);
    }


    public static String formatNumber(int value) {
        DecimalFormat df = new DecimalFormat("###,##0;(###,##0)");
        return df.format(value);
    }


    public static String formatNumber(String value) {
        BigInteger bi = new BigInteger(value);
        return formatNumber(bi.intValue());
    }


    public static String formatNumber(long value) {
        BigInteger bi = new BigInteger(String.valueOf(value));
        return formatNumber(bi.intValue());
    }

    public static String formatNumber(double value) {
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    public static String formatMinuts(int value) {
        DecimalFormat df = new DecimalFormat("00");
        return df.format(value);
    }


    public static float Round(double Rval, int Rpl) {
        float p = (float) Math.pow(10, Rpl);
        Rval = Rval * p;
        float tmp = Math.round(Rval);
        return (float) tmp / p;
    }

    public static BigDecimal roundBigDecimal(final BigDecimal input) {
        return input.round(
                new MathContext(
                        input.toBigInteger().toString().length(),
                        RoundingMode.HALF_UP
                )
        );
    }


    public static String toOracleDate(String inputDate) {
        try {
            return inputDate.substring(8, 10) + "-" + getMonthName(Integer.parseInt(inputDate.substring(5, 7))) + "-" + inputDate.substring(0, 4);
        } catch (Exception e) {
            return "";
        }
    }


    public static String formatHTMLText(String text) {
        StringBuffer buf = new StringBuffer();
        int length = text.length();
        for (int i = 0; i < length; i++) {
            switch (text.charAt(i)) {
                case '&':
                    buf.append("and");
                    break;
                case '<':
                    buf.append("&lt;");
                    break;
                case '>':
                    buf.append("&gt;");
                    break;
                case '\"':
                    buf.append("&quot;");
                    break;
                case '\'':
                    buf.append("&quot;");
                    break;
                default:
                    buf.append(text.charAt(i));
                    break;
            }
        }
        return buf.toString();
    }


    public static String formatURL(String url) {
        StringBuffer buf = new StringBuffer();
        int len = url.length();
        for (int i = 0; i < len; i++) {
            switch (url.charAt(i)) {
                case ' ':
                    buf.append("%20");
                    break;
                case ':':
                    buf.append("%3a");
                    break;
                case '/':
                    buf.append("%2f");
                    break;
                default:
                    buf.append(url.charAt(i));
                    break;
            }
        }
        return buf.toString();
    }


    public static String formatFileUploadURL(String url) {
        StringBuffer buf = new StringBuffer();
        int len = url.length();
        for (int i = 0; i < len; i++) {
            switch (url.charAt(i)) {
                case '\\':
                    buf.append("/");
                    break;
                default:
                    buf.append(url.charAt(i));
                    break;
            }
        }
        return buf.toString();
    }


    private static String javaDate(String fmt) {
        String dd;
        TimeZone gmt530 = TimeZone.getTimeZone("GMT");
        gmt530.setRawOffset((11 * 30) * 60 * 1000);
        SimpleDateFormat formatter = new SimpleDateFormat(fmt);
        formatter.setTimeZone(gmt530);
        dd = formatter.format(new java.util.Date());
        return dd;
    }

    public static String getCustomDateFormat(String date, String customDateFormat) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(customDateFormat);
        String mDate = AppConstant.DEFAULT_DATE;
        try {
            mDate = dateFormat.format(dateFormat.parse(date));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return mDate;
    }

    private static String getCustomDateFormat_(String getDate, String getFormat, String putFormat) {
        DateFormat formatter = new SimpleDateFormat(getFormat);
        SimpleDateFormat parse = new SimpleDateFormat(putFormat);
        Date date = null;
        try {
            date = formatter.parse(getDate);
        } catch (ParseException e) {
            LOGGER.error(e.getMessage());
        }
        String parsed = parse.format(date);
        return (parsed);
    }

    public static String getCustomDateFormat(String getDate, String getFormat, String putFormat) {
        String toString = AppConstant.STRING_EMPTY;
        if (getDate == null) {
            return AppConstant.STRING_EMPTY;
        } else {
            if (getDate.trim().equals(AppConstant.STRING_EMPTY) || getDate.startsWith(AppConstant.DEFAULT_DATE)) {
                return AppConstant.STRING_EMPTY;
            }
        }
        try {
            toString = getCustomDateFormat_(getDate, getFormat, putFormat);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return toString;
    }

    public static int getDateIntValue(String date, String replaceStr) {
        String toString = date;
        int dateVal = 0;

        try {
            toString = toString.replaceAll(replaceStr, "");
            dateVal = Integer.parseInt(toString);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return dateVal;
    }

    public static boolean isValidDate(String date, String cust_date_format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(cust_date_format);
        boolean b = false;
        String m_date = "";
        try {
            m_date = dateFormat.format(dateFormat.parse(date));
            b = true;
        } catch (Exception e) {
            b = false;
        }
        return b;
    }


    public static String sysDate() {
        return javaDate(AppConstant.DATE_FORMAT);
    }

    public static String getDateString(Date date) {
        return new SimpleDateFormat(AppConstant.DATE_TIME_FORMAT).format(date);
    }


    public static String sysDate(String dateFormat) {
        return javaDate(dateFormat);
    }


    public static String sysTime() {
        return javaDate(AppConstant.TIME_FORMAT);
    }


    public static long getNoDaysDateDiff(String passDate, String date_format) {
        long days = 0;
        SimpleDateFormat df = new SimpleDateFormat(date_format);
        Date date_current;
        Date date_passs;

        try {
            date_current = new Date();
            date_passs = df.parse(passDate);
            long diff = date_current.getTime() - date_passs.getTime();
            days = (diff / (1000 * 60 * 60 * 24));
        } catch (ParseException e) {
            LOGGER.error(e.getMessage());
        }
        return days;

    }

    public static long getNoDaysDiff_1(String startDate, String endDate) {
        long diffDays = 0;
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        if ((startDate.length() < 10) || (endDate.length() < 10)) {

            return -1;
        }
        try {
            calendar1.set(Integer.parseInt(startDate.substring(0, 4)), Integer.parseInt(startDate.substring(5, 7)), Integer.parseInt(startDate.substring(8, 10)));
            calendar2.set(Integer.parseInt(endDate.substring(0, 4)), Integer.parseInt(endDate.substring(5, 7)), Integer.parseInt(endDate.substring(8, 10)));
            long milliseconds1 = calendar1.getTimeInMillis();
            long milliseconds2 = calendar2.getTimeInMillis();
            long diff;
            diff = milliseconds2 - milliseconds1;
            diffDays = diff / (24 * 60 * 60 * 1000);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return diffDays;

    }

    public static long getNoMiniutsTimeDiff(String passTime, String time_format) {
        long times = 0;
        SimpleDateFormat df = new SimpleDateFormat(time_format);
        Date date_current;
        Date date_passs;

        try {
            date_current = new Date();

            date_passs = df.parse(passTime);
            long diff = date_current.getTime() - date_passs.getTime();
            times = (diff) / (1000 * 60);
        } catch (ParseException e) {
            LOGGER.error(e.getMessage());
        }
        return times;

    }

    public static long getNoMiniutsTimeDiff(String startDate, String endDate, String time_format) {
        long times = 0;
        SimpleDateFormat df = new SimpleDateFormat(time_format);
        Date date_end;
        Date date_start;

        try {
            date_start = df.parse(startDate);
            date_end = df.parse(endDate);
            long diff = date_end.getTime() - date_start.getTime();
            times = (diff) / (1000 * 60);
        } catch (ParseException e) {
            LOGGER.error(e.getMessage());
        }
        return times;

    }

    public static long getNoSecondsTimeDiff(String passTime, String time_format) {
        long times = 0;
        SimpleDateFormat df = new SimpleDateFormat(time_format);
        Date date_current = null;
        Date date_passs = null;

        try {
            date_current = new Date();

            date_passs = df.parse(passTime);
            long diff = date_current.getTime() - date_passs.getTime();
            times = (diff) / (1000);
        } catch (ParseException e) {
            LOGGER.error(e.getMessage());
        }
        return times;

    }

    public static synchronized boolean isValiedFileName(String fileName, String fileExt[]) {
        String name = fileName.toLowerCase().trim();

        int i = -1;
        for (int x = 0; x < fileExt.length; x++) {
            i = name.lastIndexOf(fileExt[x]);
            if (i > 0) {
                return true;
            }
        }
        return false;

    }


    public static String getTextValue(String val) {
        String s = val;
        s = val.trim().equalsIgnoreCase("Invalid") ? "" : s;
        return s;
    }

    public static String getDateValue(String val) {
        String s = val;
        s = val.trim().equalsIgnoreCase("1900-01-01") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1900") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1980") ? "" : s;
        s = val.trim().equalsIgnoreCase("1/Jan/1980") ? "" : s;
        s = val.trim().equalsIgnoreCase("1900-01-01 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("1900-01-01 12:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 12:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 00:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1900 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1980 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1980 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1/Jan/1980 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1/Jan/1900 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1 Jan 1980 at 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1 Jan 1900 at 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1980 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1900 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1900-01-01 00:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1900 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 00:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("1/Jan/1980 12:00 PM") ? "" : s;
        return s;
    }

    public static String getTimeValue(String val) {
        String s = val;
        s = val.trim().equalsIgnoreCase("00:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("00:00:00.0") ? "" : s;
        return s;
    }

    public static String getDateTimeValue(String val) {
        String s = val;
        s = val.trim().equalsIgnoreCase("1900-01-01 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("1900-01-01 12:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 12:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 00:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1900 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1980 12:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1980 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1/Jan/1980 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1/Jan/1900 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1 Jan 1980 at 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1 Jan 1900 at 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1980 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1900 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1900-01-01 00:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("01/Jan/1900 12:00 AM") ? "" : s;
        s = val.trim().equalsIgnoreCase("1980-01-01 00:00:00.0") ? "" : s;
        s = val.trim().equalsIgnoreCase("1/Jan/1980 12:00 PM") ? "" : s;
        s = val.trim().equalsIgnoreCase("00:00:00") ? "" : s;
        s = val.trim().equalsIgnoreCase("00:00:00.0") ? "" : s;

        return s;
    }


    public static String getIntValue(int val) {
        String s = Integer.toString(val);
        s = s.trim().equalsIgnoreCase("-1") ? "" : s;
        return s;
    }

    public static String getDoubleValue(double val) {
        String s = Double.toString(val);
        s = s.trim().equalsIgnoreCase("-1.0") ? "" : s;
        return s;
    }

    public static boolean isNumeric(String input) {
        try {
            Double.parseDouble(input);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String getSplitText(String text) {
        StringBuilder splitSB = new StringBuilder();
        String s[] = null;

        s = text.split(",");
        for (int i = 0; i < s.length; i++) {
            //splitSB.append("'");
            splitSB.append(s[i]);
            // splitSB.append("'");
            splitSB.append(",");

        }

        return splitSB.toString().substring(0, (splitSB.toString().length() - 1));
    }

    public static String getSplitText_(String text) {
        StringBuilder splitSB = new StringBuilder();
        String s[] = null;

        s = text.split(",");
        for (int i = 0; i < s.length; i++) {
            splitSB.append("'");
            splitSB.append(s[i]);
            splitSB.append("'");
            splitSB.append(",");

        }

        return splitSB.toString().substring(0, (splitSB.toString().length() - 1));
    }

    public static synchronized boolean isValiedFileName(String fileName) {
        String name = fileName.toLowerCase().trim();
        String fileExt[] = {".jpg", ".jpeg", ".gif", ".bmp", ".png"};

        int i = -1;
        for (int x = 0; x < fileExt.length; x++) {
            i = name.lastIndexOf(fileExt[x]);
            if (i > 0) {
                return true;
            }
        }

        return false;

    }

    public static String get24HoursClockTime(int h, int m, String timePrd) {
        String result = "00:00";
        int m_h = 0;
        if (timePrd.equalsIgnoreCase("AM")) {
            if (h == 12) {
                m_h = 0;
            } else {
                m_h = h;
            }
        } else {
            m_h = h + 12;
            if (h == 12) {
                m_h = h;
            }
        }
        result = formatMinuts(m_h) + ":" + formatMinuts(m);
        return result;

    }

    public static String getHHmmAmPm(String time, String returnType_HH_MM_AMPM) {//12:00
        String result = "";
        int m_h = 0;
        int m_m = 0;
        String clockType = "AM";

        try {
            m_h = Integer.parseInt(time.substring(0, 2));
            m_m = Integer.parseInt(time.substring(3, 5));
        } catch (Exception e) {
        }

        if (m_h >= 12)//PM
        {
            clockType = "PM";
            if (m_h == 12) {
                m_h = 12;
            } else {
                m_h = m_h - 12;
            }

        } else {
            clockType = "AM";
            if (m_h == 0) {
                m_h = 12;
            }

        }
        if (returnType_HH_MM_AMPM.equalsIgnoreCase("HH")) {
            result = Integer.toString(m_h);
        } else if (returnType_HH_MM_AMPM.equalsIgnoreCase("MM")) {
            result = formatMinuts(m_m);
        } else if (returnType_HH_MM_AMPM.equalsIgnoreCase("AMPM")) {
            result = clockType;
        }

        return result;
    }


    public static String getListBoxItems(List<ListBoxItem> list) {
        StringBuilder sb = new StringBuilder();
        try {
            for (ListBoxItem boxItem : list) {
                sb.append("<option value=").append("\"");
                sb.append(boxItem.getValue()).append("\"");
                sb.append(">");
                sb.append(boxItem.getLable());
                sb.append("</option>\n");
            }
        } catch (Exception e) {
        }
        return sb.toString();
    }

    public static List<ListBoxItem> getListBoxSelectdItem(List<ListBoxItem> list, Object value) {

        List<ListBoxItem> list_temp = new ArrayList<ListBoxItem>(list.size());

        try {
            for (ListBoxItem boxItem : list) {
                if (boxItem.getValue().equals(value)) {
                    list_temp.add(boxItem);
                }

            }
        } catch (Exception e) {
        }
        return list_temp;
    }

    public static String getCustomDateHtml(String date) {
        //2011-10-02
        String m_date = "";//02nd of October 2011
        String dataArray[] = date.split("-");
        int m_day = 0;

        try {
            m_day = Integer.parseInt(dataArray[2]);
            m_date = dataArray[2] + "<sup>" + dayStr[m_day - 1] + "</sup> of " + Utility.getMonthFullName(dataArray[1]) + " " + dataArray[0];
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return m_date;
    }

    public static String getCustomDateHtml1(String date) {
        //2011-10-02
        String m_date = "";//02nd  October 2011
        String dataArray[] = date.split("-");
        int m_day = 0;

        try {
            m_day = Integer.parseInt(dataArray[2]);
            m_date = "<span style=\"clear:left;font-size: 11px;font-weight: normal;\">" + dataArray[2] + "<sup>" + dayStr[m_day - 1] + "</sup> " + Utility.getMonthFullName(dataArray[1]) + " " + dataArray[0] + "</span>";
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return m_date;
    }

    public static String getCustomDateTimeHtml(String date, String time) {
        //2011-10-02
        String m_date = "";//02nd  October 2011
        String dataArray[] = date.split("-");
        int m_day = 0;

        try {
            m_day = Integer.parseInt(dataArray[2]);
            m_date = "<span style=\"clear:left;font-size: 11px;font-weight: normal;\">" + dataArray[2] + "<sup>" + dayStr[m_day - 1] + "</sup> " + Utility.getMonthFullName(dataArray[1]) + " " + dataArray[0] + " at " + time + "</span>";
        } catch (Exception e) {
        }
        return m_date;
    }

    public static String getCustomDateWithoutHtml(String date) {
        //2011-10-02
        String m_date = "";//02nd  October 2011
        String dataArray[] = date.split("-");
        int m_day = 0;

        try {
            m_day = Integer.parseInt(dataArray[2]);
            m_date = dataArray[2] + " " + dayStr[m_day - 1] + " " + Utility.getMonthFullName(dataArray[1]) + " " + dataArray[0];
        } catch (Exception e) {
        }
        return m_date;
    }

    public static long[] getDayHoursMinSecondDifference(String startDate, String endDate, String DATE_FORMAT) {
        if (DATE_FORMAT.equals("")) {
            DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
        }
        Date d1 = null;
        Date d2 = null;

        long[] result = new long[5];
        Calendar cal = Calendar.getInstance();

        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        try {
            d1 = format.parse(startDate);
            d2 = format.parse(endDate);
        } catch (Exception e) {
            e.printStackTrace();
        }

        cal.setTimeZone(TimeZone.getTimeZone("UTC"));
        assert d1 != null;
        cal.setTime(d1);

        long t1 = cal.getTimeInMillis();
        cal.setTime(d2);

        long diff = Math.abs(cal.getTimeInMillis() - t1);
        final int ONE_DAY = 1000 * 60 * 60 * 24;
        final int ONE_HOUR = ONE_DAY / 24;
        final int ONE_MINUTE = ONE_HOUR / 60;
        final int ONE_SECOND = ONE_MINUTE / 60;

        long d = diff / ONE_DAY;
        diff %= ONE_DAY;

        long h = diff / ONE_HOUR;
        diff %= ONE_HOUR;

        long m = diff / ONE_MINUTE;
        diff %= ONE_MINUTE;

        long s = diff / ONE_SECOND;
        long ms = diff % ONE_SECOND;
        result[0] = d;
        result[1] = h;
        result[2] = m;
        result[3] = s;
        result[4] = ms;

        return result;
    }

    public static long[] getDayHoursMinSecondDifferenceForWorkingHours(long totalMinute) {
        long[] result = new long[5];
        try {
            long diff = totalMinute * 60 * 1000;
            final int ONE_DAY = 1000 * 60 * 60 * 24;
            final int ONE_HOUR = ONE_DAY / 24;
            final int ONE_MINUTE = ONE_HOUR / 60;
            final int ONE_SECOND = ONE_MINUTE / 60;

            long d = diff / ONE_DAY;
            diff %= ONE_DAY;

            long h = diff / ONE_HOUR;
            diff %= ONE_HOUR;

            long m = diff / ONE_MINUTE;
            diff %= ONE_MINUTE;

            long s = diff / ONE_SECOND;
            long ms = diff % ONE_SECOND;
            result[0] = d;
            result[1] = h;
            result[2] = m;
            result[3] = s;
            result[4] = ms;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return result;
    }

    public static long[] getDayHoursMinSecondDifference(long totalMinute) {
        long[] result = new long[5];
        long diff = totalMinute * 60 * 1000;
        final int ONE_DAY = 1000 * 60 * 60 * 24;
        final int ONE_HOUR = ONE_DAY / 24;
        final int ONE_MINUTE = ONE_HOUR / 60;
        final int ONE_SECOND = ONE_MINUTE / 60;

        long d = diff / ONE_DAY;
        diff %= ONE_DAY;

        long h = diff / ONE_HOUR;
        diff %= ONE_HOUR;

        long m = diff / ONE_MINUTE;
        diff %= ONE_MINUTE;

        long s = diff / ONE_SECOND;
        long ms = diff % ONE_SECOND;
        result[0] = d;
        result[1] = h;
        result[2] = m;
        result[3] = s;
        result[4] = ms;

        return result;
    }

    public static String getDecimalValue(double value) {
        BigDecimal decimalValue;
        try {
            DecimalFormat df = new DecimalFormat("0.00");
            decimalValue = new BigDecimal(df.format(value));
            //  SystemMessage.getInstance().writeMessage("DEBUG: Claim_AS400_Manager.getDecimalValue() -Value " + decimalValue);
        } catch (Exception e) {
            decimalValue = new BigDecimal("0.00");
            LOGGER.error(e.getMessage());
        }
        return decimalValue.toPlainString();
    }

    public static String addDate(String date, int add_days) {
        String dt = date;  // Start date
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        try {
            c.setTime(sdf.parse(dt));
            c.add(Calendar.DATE, add_days);  // number of days to add
            dt = sdf.format(c.getTime());
        } catch (ParseException ex) {
            LOGGER.error(ex.getMessage());;
        }
        return dt;
    }

    public static boolean isWeekEnd(String date) {
        boolean b = false;
        String dt = date;  // Start date
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();

        try {
            c.setTime(sdf.parse(dt));
            int day = c.get(Calendar.DAY_OF_WEEK);
            if (day == Calendar.SATURDAY || day == Calendar.SUNDAY) {
                b = true;
            }
        } catch (ParseException ex) {
            LOGGER.error(ex.getMessage());;
        }
        return b;
    }

    public static int compareToDate(String date, String currentDate, String dateFormat) {
        int r = -2;
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        try {
            Date date1 = sdf.parse(date);
            Date date2 = sdf.parse(currentDate);
            if (date1.compareTo(date2) > 0) {
                r = 1;
            } else if (date1.compareTo(date2) < 0) {
                r = -1;
            } else if (date1.compareTo(date2) == 0) {
                r = 0;
            }

        } catch (ParseException ex) {
            LOGGER.error(ex.getMessage());;
        }

        return r;
    }

    public static String defaultDateFormating(String dateTime) {
        String text = dateTime;
        if (dateTime.startsWith("1980") || dateTime.startsWith("1900")) {
            text = "<span style=\"font-size: 12px;font-family:Arial;font-weight: bold;color: #D50000;\">N/A</span>";
        }
        return text;
    }

    public static String getStringFromInputStream(InputStream is) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();

        String line;
        try {

            br = new BufferedReader(new InputStreamReader(is));
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }

        } catch (IOException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }

        return sb.toString();

    }

    public static String sysDateTime() {
        return sysDate() + " " + sysTime();
    }


    public static String getCombinedTimeString24hours(String hours, String mins, String meridiem) throws Exception {
        SimpleDateFormat displayFormat = new SimpleDateFormat("HH:mm:ss");
        SimpleDateFormat parseFormat = new SimpleDateFormat("hh:mm a");
        Date date = parseFormat.parse(hours + ":" + mins + " " + meridiem);
        return displayFormat.format(date);
    }

    public static String[] getSeparateTimeString12hours(String time24Hours) throws Exception {
        SimpleDateFormat displayFormat = new SimpleDateFormat("HH:mm:ss");
        SimpleDateFormat parseFormat = new SimpleDateFormat("hh:mm a");
        Date date = displayFormat.parse(time24Hours);
        String format = parseFormat.format(date);
        return new String[]{format.substring(0, 2), format.substring(3, 5), format.substring(6, 8)};
    }

    public static String[] getSeparateCurruntSysTimeString12hours() throws Exception {
        SimpleDateFormat parseFormat = new SimpleDateFormat("hh:mm a");
        Date date = new Date();
        String format = parseFormat.format(date);
        return new String[]{format.substring(0, 2), format.substring(3, 5), format.substring(6, 8)};
    }

    public static String getDate(String dateTime, String dateFormat) {
        SimpleDateFormat sdfDate = new SimpleDateFormat(dateFormat);
        Date now = null;
        try {
            now = sdfDate.parse(dateTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return sdfDate.format(now);

    }

    public static String getDate(String dateTime, String sourceDateFormat, String targetDateFormat) {
        SimpleDateFormat sdfSourceDate = new SimpleDateFormat(sourceDateFormat);
        SimpleDateFormat sdfTargetDate = new SimpleDateFormat(targetDateFormat);
        Date now = null;
        try {
            now = sdfSourceDate.parse(dateTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return sdfTargetDate.format(now);

    }

    public static String addMinute(String date, int minute) {
        String dt = date;  // Start date
        SimpleDateFormat sdf = new SimpleDateFormat(AppConstant.DATE_TIME_FORMAT);
        Calendar c = Calendar.getInstance();
        try {
            c.setTime(sdf.parse(dt));
            c.add(Calendar.MINUTE, minute);  // number of days to add
            dt = sdf.format(c.getTime());
        } catch (ParseException ex) {
            LOGGER.error(ex.getMessage());;
        }
        return dt;
    }

    public static String getBeforeNoOfDays(Integer noOfDays) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        String add = "";
        calendar.add(Calendar.DAY_OF_MONTH, -noOfDays);
        Date beforeDate = calendar.getTime();
        String beforeDateString = getDateString(beforeDate);
        return getDate(beforeDateString, AppConstant.DATE_FORMAT);
    }


}
