package com.misyn.mcms.utility;


import jakarta.ws.rs.client.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;

public class OciTest {


    public OciTest()  {

    }

    public static void main(String[] args) throws IOException {
        String apiUrl = "http://localhost:9090/api/v1/file/upload/byte-array";
        File file = new File("D:\\data\\edoc\\IM70520720xxx11.pdf");
        // Path to the file you want to upload // Replace with the actual file path
        byte[] fileBytes = Files.readAllBytes(file.toPath()); // Read the file content as byte array

        String objectName = "IM70520720xxx11zzz.pdf";  // Replace with the actual object name

        // Send the request using Jakarta Client
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(apiUrl);

            // Create the POST request
            Invocation.Builder request = target
                    .queryParam("objectName", objectName) // Add objectName as a query parameter
                    .request(MediaType.APPLICATION_JSON);

            // Send the file as byte[] in the request body
            try (Response response = request.post(Entity.entity(fileBytes, MediaType.APPLICATION_OCTET_STREAM))) {

                // Handle the response
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    System.out.println("File uploaded successfully: " + response.readEntity(String.class));
                } else {
                    System.out.println("Failed to upload file. Status: " + response.getStatus());
                }
            }
        }
    }

    public static void main1(String[] args) {
        String apiUrl = "http://localhost:9090/api/v1/file/upload1"; // Spring Boot API URL
        File file = new File("D:\\data\\output.png"); // Change to your file path
        uploadFile(apiUrl, file,"output.png");
    }

    public static void uploadFile(String url, File file, String objectName) {
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(url);

            // Read file as byte array
            byte[] fileBytes = Files.readAllBytes(file.toPath());
            InputStream fileInputStream = new FileInputStream(file);
            // Create multipart form data
            FormDataMultiPart formDataMultiPart = new FormDataMultiPart();

            // Add file as byte array
            FormDataBodyPart filePart = new FormDataBodyPart("file", fileInputStream, MediaType.APPLICATION_OCTET_STREAM_TYPE);
            formDataMultiPart.bodyPart(filePart);

            // Add objectName as a parameter
            formDataMultiPart.field("objectName", objectName);

            // Send the POST request with the file and objectName
            Invocation.Builder request = target.request(MediaType.APPLICATION_OCTET_STREAM);
            Response response = request.post(Entity.entity(formDataMultiPart, MediaType.MULTIPART_FORM_DATA));

            // Handle response
            if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                System.out.println("File uploaded successfully: " + response.readEntity(String.class));
            } else {
                System.err.println("File upload failed: " + response.getStatus() + " - " + response.readEntity(String.class));
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void fetchFile(String url, String token, String fileName) {
        try (Client client = ClientBuilder.newClient()) {
            WebTarget target = client.target(url);
            Invocation.Builder request = target.request()
                    .header("Authorization", "Bearer " + token);

            try (Response response = request.get()) { // Change to GET request
                if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                    String contentType = response.getHeaderString("Content-Type");

                    // Check if response is a file (PDF or Image)
                    if (contentType != null && (contentType.startsWith("image/") || contentType.equals("application/pdf"))) {
                      //  saveToFile(response.readEntity(InputStream.class), fileName);
                        System.out.println(fileName + " downloaded successfully!");
                    } else {
                        System.out.println("Unexpected content type: " + contentType);
                    }
                } else {
                    System.err.println("Request failed: " + response.getStatus() + " - " + response.readEntity(String.class));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Error fetching file", e);
        }
    }





}
