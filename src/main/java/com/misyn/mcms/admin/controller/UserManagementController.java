package com.misyn.mcms.admin.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.misyn.mcms.admin.admin.dto.*;
import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.admin.admin.service.impl.UserManagementServiceImpl;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@WebServlet(name = "UserManagementController", urlPatterns = "/UserManagementController/*")
public class UserManagementController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserManagementController.class);
    private UserManagementService userManagementService = new UserManagementServiceImpl();
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

        try {
            switch (pathInfo) {
                case "/getChannelsForTeam":
                    getChannelsForTeam(request, response);
                    break;
                case "/viewTeams":
                    viewClaimTeams(request, response);
                    break;
                case "/viewTeam":
                    viewClaimTeam(request, response);
                    break;
                case "/userList":
                    userList(request, response);
                    break;
                case "/fetchSpecialTeam":
                    fetchSpecialTeam(request, response);
                    break;
                case "/fetchApprovalUserList":
                    fetchApprovalUserList(request, response);
                    break;
                case "/channelView":
                    channelView(request, response);
                    break;
                case "/newTeam":
                    newTeamView(request, response);
                    break;
                case "/saveNewTeam":
                    saveNewTeam(request, response);
                    break;
                case "/addChannelsForTeam":
                    addChannelsForTeam(request, response);
                    break;
                case "/channelList":
                    channelList(request, response);
                    break;
                case "/updateTeam":
                    updateTeam(request, response);
                    break;
                case "/branchList":
                    branchList(request, response);
                    break;
                case "/viewBranchDetails":
                    viewBranchDetails(request, response);
                    break;
                case "/searchAllBranchDetails":
                    searchAllBranchDetails(request, response);
                    break;
                case "/updateBranchDetails":
                    updateBranchDetails(request, response);
                    break;
                case "/saveBranchDetails":
                    saveBranchDetails(request, response);
                    break;
                case "/deleteSelectedBranches":
                    deleteSelectedBranches(request, response);
                    break;
                case "/isDuplicateBranchCode":
                    isDuplicateBranchCode(request, response);
                    break;
                case "/salientManage":
                    productManage(request, response);
                    break;
                case "/getProductDetail":
                    getProductDetail(request, response);
                    break;
                case "/saveCovers":
                    saveCoverDetail(request, response);
                    break;
                case "/getSalientMasterDetail":
                    getSalientMasterDetail(request, response);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void isDuplicateBranchCode(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        String isDuplicateBranchCode = AppConstant.STRING_EMPTY;
        try {
            String branchCode = null == request.getParameter(AppConstant.BRANCH_CODE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.BRANCH_CODE);

            if (userManagementService.isAlreadySavedBranch(branchCode)) {
                isDuplicateBranchCode = AppConstant.YES;
            } else {
                isDuplicateBranchCode = AppConstant.NO;
            }
            json = gson.toJson(isDuplicateBranchCode);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson(isDuplicateBranchCode);
            printWriter(request, response, json);
        }
    }

    private void deleteSelectedBranches(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            setClaimPanelUserPopupListValues(request);
            String selectedBranchesCode = null == request.getParameter("selectedBranchesCode") ? AppConstant.STRING_EMPTY : request.getParameter("selectedBranchesCode");

            userManagementService.deleteSelectedBranches(selectedBranchesCode, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void saveBranchDetails(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            String branchCode = null == request.getParameter(AppConstant.BRANCH_CODE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.BRANCH_CODE);
            String branchName = null == request.getParameter(AppConstant.BRANCH_NAME) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.BRANCH_NAME);
            String branchCity = null == request.getParameter(AppConstant.BRANCH_CITY) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.BRANCH_CITY);

            if (userManagementService.isAlreadySavedBranch(branchCode)) {
                json = gson.toJson("DUPLICATE_CODE");
                printWriter(request, response, json);
            } else {
                userManagementService.saveBranchDetails(branchCode, branchName, branchCity, user);

                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void updateBranchDetails(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            String branchCode = null == request.getParameter(AppConstant.BRANCH_CODE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.BRANCH_CODE);
            String branchName = null == request.getParameter(AppConstant.BRANCH_NAME) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.BRANCH_NAME);
            String branchCity = null == request.getParameter(AppConstant.BRANCH_CITY) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.BRANCH_CITY);

            userManagementService.updateBranchDetails(branchCode, branchCity, branchName, user);

            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void searchAllBranchDetails(HttpServletRequest request, HttpServletResponse response) {

        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);
            DataGridDto data = userManagementService.getBranchDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void viewBranchDetails(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;

        try {
            String branchCode = null == request.getParameter("branchCode") ? AppConstant.STRING_EMPTY : request.getParameter("branchCode");
            BranchDetailDto branchDetailDto = userManagementService.getBranchDetail(branchCode);
            json = gson.toJson(branchDetailDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void branchList(HttpServletRequest request, HttpServletResponse response) {
        requestDispatcher(request, response, "/admin/user_claim/branchList.jsp");
    }

    private void updateTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer teamId = null == request.getParameter("teamId") || request.getParameter("teamId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("teamId"));
        String teamName = null == request.getParameter("teamNameUpdate") ? AppConstant.STRING_EMPTY : request.getParameter("teamNameUpdate");
        String channelDesc = null == request.getParameter("channelDescUpdate") ? AppConstant.STRING_EMPTY : request.getParameter("channelDescUpdate");
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            if (userManagementService.checkTeamName(teamName)) {
                json = gson.toJson("DUPLICATE");
            } else {
                ChannelTeamDto channelTeamDto = new ChannelTeamDto();
                channelTeamDto.setTeamId(teamId);
                channelTeamDto.setTeamName(teamName);
                channelTeamDto.setChannelDesc(channelDesc);
                userManagementService.updateTeamDetails(channelTeamDto);
                json = gson.toJson("SUCCESS");
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void channelList(HttpServletRequest request, HttpServletResponse response) {
        Integer teamId = null == request.getParameter("teamId") || request.getParameter("teamId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("teamId"));
        try {
            List<ClaimChannelDto> channels = userManagementService.getChannelsForTeam(teamId);
            request.setAttribute(AppConstant.CHANNEL_LIST, channels);
            requestDispatcher(request, response, "/admin/user_claim/channelList.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void addChannelsForTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer teamId = request.getParameter("teamId") == null || request.getParameter("teamId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("teamId"));
        String channelDesc = request.getParameter("selectedIds") == null || request.getParameter("selectedIds").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("selectedIds");
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            List<Integer> selectedList = userManagementService.getSelectedList("CCB", channelDesc);
            userManagementService.addChannelsForTeam(teamId, selectedList);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void saveNewTeam(HttpServletRequest request, HttpServletResponse response) {
        String teamName = request.getParameter("teamName") == null || request.getParameter("teamName").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("teamName");
        String channelDesc = request.getParameter("channelDesc") == null || request.getParameter("channelDesc").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("channelDesc");
        String ids = request.getParameter("selectedIds");
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        try {
            List<Integer> selectedList = null;
            if (ids != AppConstant.STRING_EMPTY) {
                selectedList = userManagementService.getSelectedList("CCB", ids);
            }
            if (userManagementService.checkTeamName(teamName)) {
                json = gson.toJson("DUPLICATE");
            } else {
                userManagementService.saveNewTeam(teamName, channelDesc, selectedList);
                json = gson.toJson("SUCCESS");
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void newTeamView(HttpServletRequest request, HttpServletResponse response) {
        try {
            requestDispatcher(request, response, "/admin/user_claim/createTeam.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void channelView(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = null;
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            List<ClaimChannelDto> channelsNotInTeams = userManagementService.getChannelsNotInTeams();
            request.setAttribute(AppConstant.CHANNEL_LIST_NOT_IN_TEAMS, channelsNotInTeams);
            requestDispatcher(request, response, "/admin/user_claim/addChannelsForTeam.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
        }
    }

    private void userList(HttpServletRequest request, HttpServletResponse response) {
        Integer teamId = null == request.getParameter("teamId") || request.getParameter("teamId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("teamId"));
        try {
            List<UserDto> users = userManagementService.getMembersForTeam(teamId);
            request.setAttribute(AppConstant.CHANNEL_USERS, users);
            requestDispatcher(request, response, "/admin/user_claim/teamMemberList.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void fetchSpecialTeam(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<ListItemDto> timeSlotsByInspectionTypeId = userManagementService
                    .fetchSpecialTeam();

            String json = new Gson().toJson(timeSlotsByInspectionTypeId);
            response.getWriter().write(json);

        } catch (Exception ex) {
            LOGGER.error("Error fetching time slots: " + ex.getMessage(), ex);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Unable to fetch time slots.");
            } catch (IOException ioEx) {
                LOGGER.error("Error writing error response: " + ioEx.getMessage(), ioEx);
            }
        }
    }

    private void fetchApprovalUserList(HttpServletRequest request, HttpServletResponse response) {

        Integer claimNo = Integer.valueOf(request.getParameter("claimNo"));

        try {
            List<ListItemDto> timeSlotsByInspectionTypeId = userManagementService
                    .fetchApprovalUserList(claimNo);

            String json = new Gson().toJson(timeSlotsByInspectionTypeId);
            response.getWriter().write(json);

        } catch (Exception ex) {
            LOGGER.error("Error fetching time slots: " + ex.getMessage(), ex);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Unable to fetch time slots.");
            } catch (IOException ioEx) {
                LOGGER.error("Error writing error response: " + ioEx.getMessage(), ioEx);
            }
        }
    }

    private void viewClaimTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer teamId = null == request.getParameter("teamId") || request.getParameter("teamId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("teamId"));
        String successCode = null == request.getParameter("successCode") ? AppConstant.STRING_EMPTY : request.getParameter("successCode");
        try {
            ChannelTeamDto channelTeamDto = userManagementService.getTeamInfo(teamId);
            if (!successCode.isEmpty()) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Team Updated Successfully");
            }
            request.setAttribute(AppConstant.CHANNEL_TEAM, channelTeamDto);
            requestDispatcher(request, response, "/admin/user_claim/claimTeam.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewClaimTeams(HttpServletRequest request, HttpServletResponse response) {
        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
        try {
            List<ChannelTeamGridDto> teamList = userManagementService.getTeamList();
            request.setAttribute(AppConstant.CHANNEL_TEAM_LIST, teamList);
            requestDispatcher(request, response, "/admin/user_claim/claimTeamList.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void getChannelsForTeam(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        Integer teamId = Integer.valueOf(request.getParameter("team"));
        List<ClaimChannelDto> claimChannels = null;
        try {
            claimChannels = userManagementService.getChannelsForTeam(teamId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(claimChannels);
            printWriter(request, response, json);
        }
    }

    private void productManage(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<CoverDetailMstDto> benefitCoverLoadingDetail = userManagementService.getBenefitCoverLoadingDetail(AppConstant.EMPTY_STRING, AppConstant.EMPTY_STRING);
            List<CoverDetailMstDto> cweDetail = userManagementService.getCWEDetail(AppConstant.EMPTY_STRING, AppConstant.EMPTY_STRING);
            List<CoverDetailMstDto> chargesAndDiscountDetail = userManagementService.getChargesAndDiscountDetail(AppConstant.EMPTY_STRING, AppConstant.EMPTY_STRING);
            List<CoverDetailMstDto> srccTcDetail = userManagementService.getSrccTcDetail(AppConstant.EMPTY_STRING, AppConstant.EMPTY_STRING);
            request.setAttribute(AppConstant.BENEFIT_COVER_LOADING_LIST, benefitCoverLoadingDetail);
            request.setAttribute(AppConstant.CWE_LIST, cweDetail);
            request.setAttribute(AppConstant.CHARGES_DISCOUNT_LIST, chargesAndDiscountDetail);
            request.setAttribute(AppConstant.SRCC_TC_LIST, srccTcDetail);
            requestDispatcher(request, response, "/admin/user_admin/salientDetailManageView.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void getProductDetail(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            ProductDetailListDto productDetailById = userManagementService.getProductDetailById();
            json = gson.toJson(productDetailById);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void saveCoverDetail(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        ObjectMapper objectMapper = new ObjectMapper();
        String json;
        try {
            json = request.getParameter("ProductDetail").replace("&#34;", "\"");
            Map<String, Object> productDetailMap = objectMapper.readValue(json, Map.class);
            userManagementService.saveProductDetail(productDetailMap);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void getSalientMasterDetail(HttpServletRequest request, HttpServletResponse response) {
        String coverCode = null == request.getParameter("code") ? AppConstant.STRING_EMPTY : request.getParameter("code");
        String coverName = null == request.getParameter("name") ? AppConstant.STRING_EMPTY : request.getParameter("name");
        String tableName = null == request.getParameter("tableName") ? AppConstant.STRING_EMPTY : request.getParameter("tableName");
        Gson gson = new Gson();
        String json;
        try {
            List<CoverDetailMstDto> salientMasterDetail = userManagementService.getSalientMasterDetail(coverCode, coverName, tableName);
            if (null == salientMasterDetail || salientMasterDetail.isEmpty()) {
                json = gson.toJson("EMPTY");
            } else {
                json = gson.toJson(salientMasterDetail);
            }
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }
}
