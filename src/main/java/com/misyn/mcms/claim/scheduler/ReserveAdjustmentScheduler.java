package com.misyn.mcms.claim.scheduler;

import com.misyn.mcms.claim.dao.McmsClaimOfflineReserveClaimDao;
import com.misyn.mcms.claim.dao.impl.CallCenterDaoImpl;
import com.misyn.mcms.claim.dao.impl.McmsClaimOfflineReserveClaimDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.impl.ClaimHandlerServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDate;
import java.util.*;

public class ReserveAdjustmentScheduler extends AbstractBaseService<Object> implements Runnable {

    private static final String SQL_SELECT_SPECIAL_CASE_CLAIMS = "SELECT claim_no FROM claim_special_case_type WHERE record_status = 'A'";
    private static final String SQL_SELECT_ADJUSTMENT_TYPES = "SELECT a.*, " +
            "c.amount_min AS category_amount_min, c.amount_max AS category_amount_max " +
            "FROM claim_reserve_adjustment_type a " +
            "JOIN reserve_category c ON a.category_id = c.category_id WHERE a.record_status = 'A'";
    private static final String SQL_SELECT_RESERVE_PERIODS = "SELECT period_id, months_count, label FROM reserve_period";
    private static final String SQL_SELECT_CLAIM_INSPECTION_TYPES = "SELECT n_inspection_type FROM claim_inspection_info_main WHERE n_claim_no = ?";
    private static final String SQL_CHECK_CLAIM_IN_TABLE = "SELECT 1 FROM change_request_detail WHERE claim_no = ? LIMIT 1";
    private static final String SQL_SELECT_ACCIDENT_DATE = "SELECT d_accid_date FROM claim_claim_info_main WHERE N_CLIM_NO = ?";
    private static final String SQL_UPDATE_RESERVE_DETAILS =
            "UPDATE claim_assign_claim_handler SET N_RESERVE_AMOUNT = ?, N_RESERVE_AMOUNT_AFTER_APRV = ? WHERE n_claim_no = ?";
    private static final String SQL_UPSERT_NEXT_PROCESS_DATE =
            "INSERT INTO claim_next_process (claim_no, next_process_date, run_count) VALUES (?, ?, 1) " +
                    "ON DUPLICATE KEY UPDATE next_process_date = VALUES(next_process_date), run_count = run_count + 1";
    private static final String SQL_SELECT_CLAIMS_AND_RESERVES =
            "SELECT c.n_claim_no, c.n_reserve_amount, c.n_reserve_amount_after_aprv, np.next_process_date FROM claim_assign_claim_handler c " +
                    "LEFT JOIN claim_next_process np ON c.n_claim_no = np.claim_no " +
                    "WHERE (np.next_process_date IS NULL OR np.next_process_date = CURDATE()) " +
                    "AND (c.v_close_status IS NULL OR c.v_close_status <> 'CLOSE')";

    private final ClaimHandlerServiceImpl claimHandlerService = new ClaimHandlerServiceImpl();
    private final CallCenterDaoImpl callCenterDaoImpl = new CallCenterDaoImpl();
    private final McmsClaimOfflineReserveClaimDao offlineReserveClaimDao = new McmsClaimOfflineReserveClaimDaoImpl();

    @Override
    public void run() {
        try (Connection conn = getJDBCConnection()) {

            // Load all adjustment types
            List<ClaimReserveAdjustmentTypeDto> adjustmentTypeList = loadAdjustmentTypes(conn);

            // Load reserve period configuration
            Map<Integer, ReservePeriodDto> periodList = loadReservePeriods(conn);

            // Load all claims with their reserve and reserve-after-aprv amounts
            Map<Integer, Double> claimReserveMap = new HashMap<>();
            Map<Integer, Double> claimReserveAfterAprvMap = new HashMap<>();
            Set<Integer> allClaimNos = loadClaimsAndReserves(conn, claimReserveMap, claimReserveAfterAprvMap);

            // Filter to moving / non-moving claims based on inspection types
            Set<Integer> nonMovingList = new HashSet<>();
            Set<Integer> movingList = new HashSet<>();
            groupClaimsByType(conn, allClaimNos, nonMovingList, movingList);

            // Load special case claims
            Set<Integer> specialCaseClaims = loadSpecialCaseClaims(conn);
            nonMovingList.removeAll(specialCaseClaims);
            movingList.removeAll(specialCaseClaims);

            System.out.println("NonMoving  " + nonMovingList);
            System.out.println(" Moving : " + movingList);
            Map<Integer, ReserveCategoryDto> categoryMap = loadReserveCategories(conn);
            // For non-moving claims, process adjustment logic
            processClaims(conn, nonMovingList, periodList, adjustmentTypeList, claimReserveMap, claimReserveAfterAprvMap, true, categoryMap);

            // For moving claims, process adjustment logic
            processClaims(conn, movingList, periodList, adjustmentTypeList, claimReserveMap, claimReserveAfterAprvMap, false, categoryMap);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private List<ClaimReserveAdjustmentTypeDto> loadAdjustmentTypes(Connection conn) throws Exception {
        List<ClaimReserveAdjustmentTypeDto> adjustmentTypeList = new ArrayList<>();

        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SQL_SELECT_ADJUSTMENT_TYPES)) {
            while (rs.next()) {
                ClaimReserveAdjustmentTypeDto type = new ClaimReserveAdjustmentTypeDto();
                type.setClaimReserveAdjustmentId(rs.getInt("claim_reserve_adjustment_id"));
                type.setPeriodId(rs.getInt("period_id"));
                type.setCategoryId(rs.getInt("category_id"));
                type.setAmountMin(rs.getBigDecimal("category_amount_min"));
                type.setAmountMax(rs.getBigDecimal("category_amount_max"));
                type.setAmount(rs.getString("amount"));
                type.setRecordStatus(rs.getString("record_status"));
                type.setInputDateTime(rs.getString("input_date_time"));
                type.setInputUser(rs.getString("input_user"));
                type.setLastModifiedDateTime(rs.getString("last_modified_date_time"));
                type.setLastModifiedUser(rs.getString("last_modified_user"));
                adjustmentTypeList.add(type);
            }
        }
        return adjustmentTypeList;
    }

    private Map<Integer, ReservePeriodDto> loadReservePeriods(Connection conn) throws Exception {
        Map<Integer, ReservePeriodDto> periodMap = new HashMap<>();
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(SQL_SELECT_RESERVE_PERIODS)) {
            while (rs.next()) {
                int periodId = rs.getInt("period_id");
                int monthsCount = rs.getInt("months_count");
                String label = rs.getString("label");
                periodMap.put(periodId, new ReservePeriodDto(periodId, monthsCount, label));
            }
        }
        return periodMap;
    }

    private Map<Integer, ReserveCategoryDto> loadReserveCategories(Connection conn) throws Exception {
        Map<Integer, ReserveCategoryDto> categoryMap = new HashMap<>();
        String SQL = "SELECT category_id, description, amount_min, amount_max FROM reserve_category WHERE record_status = 'A'";
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SQL)) {
            while (rs.next()) {
                ReserveCategoryDto dto = new ReserveCategoryDto();
                dto.setCategoryId(rs.getInt("category_id"));
                dto.setDescription(rs.getString("description"));
                dto.setAmountMin(rs.getBigDecimal("amount_min"));
                dto.setAmountMax(rs.getBigDecimal("amount_max"));
                categoryMap.put(dto.getCategoryId(), dto);
            }
        }
        return categoryMap;
    }

    private Set<Integer> loadClaimsAndReserves(Connection conn, Map<Integer, Double> claimReserveMap, Map<Integer, Double> claimReserveAfterAprvMap) throws Exception {
        Set<Integer> allClaimNos = new HashSet<>();

        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SQL_SELECT_CLAIMS_AND_RESERVES)) {
            while (rs.next()) {
                int claimNo = rs.getInt("n_claim_no");
                double reserveAmount = rs.getDouble("n_reserve_amount");
                double reserveAmountAfterAprv = rs.getDouble("n_reserve_amount_after_aprv");
                allClaimNos.add(claimNo);
                claimReserveMap.put(claimNo, reserveAmount);
                claimReserveAfterAprvMap.put(claimNo, reserveAmountAfterAprv);
            }
        }
        return allClaimNos;
    }

    private void groupClaimsByType(Connection conn, Set<Integer> allClaimNos, Set<Integer> nonMovingList, Set<Integer> movingList) throws Exception {
        try (PreparedStatement ps = conn.prepareStatement(SQL_SELECT_CLAIM_INSPECTION_TYPES);
             PreparedStatement psExist = conn.prepareStatement(SQL_CHECK_CLAIM_IN_TABLE)) {
            for (int claimNo : allClaimNos) {
                Set<Integer> types = new HashSet<>();
                ps.setInt(1, claimNo);
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        types.add(rs.getInt("n_inspection_type"));
                    }
                }
                if (!types.isEmpty() && types.stream().allMatch(t -> t == 1 || t == 2)) {
                    // Before adding to nonMovingList, check if claim exists in change_request_detail table
                    psExist.setInt(1, claimNo);
                    try (ResultSet rsExist = psExist.executeQuery()) {
                        if (rsExist.next()) {
                            movingList.add(claimNo);
                        } else {
                            nonMovingList.add(claimNo);
                        }
                    }
                } else {
                    movingList.add(claimNo);
                }
            }
        }
    }

    private Set<Integer> loadSpecialCaseClaims(Connection conn) throws Exception {
        Set<Integer> specialCases = new HashSet<>();

        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SQL_SELECT_SPECIAL_CASE_CLAIMS)) {
            while (rs.next()) {
                specialCases.add(rs.getInt("claim_no"));
            }
        }
        return specialCases;
    }

    private void processClaims(
            Connection conn,
            Set<Integer> claimNos,
            Map<Integer, ReservePeriodDto> periodMap,
            List<ClaimReserveAdjustmentTypeDto> adjustmentTypeList,
            Map<Integer, Double> claimReserveMap,
            Map<Integer, Double> claimReserveAfterAprvMap,
            boolean isNonMoving,
            Map<Integer, ReserveCategoryDto> categoryMap
    ) throws Exception {

        // Sort periods by months_count descending for matching
        List<ReservePeriodDto> sortedPeriods = new ArrayList<>(periodMap.values());
        sortedPeriods.sort(Comparator.comparingInt(p -> -p.getMonthsCount()));

        try (PreparedStatement accidPs = conn.prepareStatement(SQL_SELECT_ACCIDENT_DATE)) {
            for (int claimNo : claimNos) {
                accidPs.setInt(1, claimNo);
                try (ResultSet accidRs = accidPs.executeQuery()) {
                    if (accidRs.next()) {
                        java.sql.Date accidDate = accidRs.getDate("d_accid_date");
                        if (accidDate != null) {
                            long millisDiff = System.currentTimeMillis() - accidDate.getTime();
                            long days = millisDiff / (24 * 60 * 60 * 1000);

                            // Find the matching periodId
                            int matchedPeriodId = -1;

                            for (ReservePeriodDto p : sortedPeriods) {

                                if (days >= p.getMonthsCount()) {
                                    matchedPeriodId = p.getPeriodId();
                                    break;
                                }
                            }
                            // If not matched (younger than the smallest period), use the smallest period
                            if (matchedPeriodId == -1 && !sortedPeriods.isEmpty()) {
                                ReservePeriodDto first = sortedPeriods.get(sortedPeriods.size() - 1);
                                matchedPeriodId = first.getPeriodId();
                            }

                            // Find reserve Amount for this claim
                            double reserveAmount = claimReserveMap.getOrDefault(claimNo, 0.0);

                            int categoryId = -1;

                            if (isNonMoving) {
                                categoryId = 4;
                            } else {
                                for (ReserveCategoryDto category : categoryMap.values()) {

                                    int catId = category.getCategoryId();
                                    double min = category.getAmountMin().doubleValue();
                                    double max = category.getAmountMax().doubleValue();

                                    if (catId == 1) { // ACR>2.5M — only min check
                                        if (reserveAmount >= min) {
                                            categoryId = catId;
                                            break;
                                        }
                                    } else {
                                        // Other categories — check range
                                        if (reserveAmount >= min && reserveAmount < max) {
                                            categoryId = catId;
                                            break;
                                        }
                                    }
                                }
                            }

                            if (matchedPeriodId != -1) {
                                processClaimAdjustment(
                                        conn,
                                        claimNo,
                                        matchedPeriodId,
                                        categoryId,
                                        reserveAmount,
                                        adjustmentTypeList,
                                        claimReserveAfterAprvMap,
                                        categoryMap.get(categoryId)
                                );
                            }
                        } else {
                            System.out.println("Claim No: " + claimNo + " has no accident date.");
                        }
                    } else {
                        System.out.println("Claim No: " + claimNo + " not found in claim_claim_info_main.");
                    }
                }
            }
        }
    }

    private void processClaimAdjustment(
            Connection connection,
            int claimNo,
            int matchedPeriodId,
            int categoryId,
            double reserveAmount,
            List<ClaimReserveAdjustmentTypeDto> adjustmentTypeList,
            Map<Integer, Double> claimReserveAfterAprvMap,
            ReserveCategoryDto categoryDto) {

        ClaimReserveAdjustmentTypeDto adjType = findAdjustmentByPeriodAndAmount(adjustmentTypeList, matchedPeriodId, categoryId, reserveAmount);
        String percentageString = "0";
        if (adjType != null) {
            percentageString = adjType.getAmount();
        }

        // Check if the adjustment requires the claim to be closed
        if (percentageString != null && percentageString.trim().equalsIgnoreCase("Closed")) {
            UserDto systemUser = new UserDto();
            systemUser.setUserId("system");
            try {
                claimHandlerService.updateClaimClose(claimNo, systemUser, "Auto closed by scheduler due to reserve adjustment.");
                updateAdjustedReserveAmountsAndNextProcessDate(claimNo, 0, 0);
                ClaimsDto claimsDto = callCenterDaoImpl.searchMaster(connection, claimNo);

                offlineReserveClaim(connection, claimsDto, 1, systemUser, true);
            } catch (Exception e) {
                System.err.println("Failed to close claim: " + claimNo + ", reason: " + e.getMessage());
            }
            return;
        }
        double percent = parsePercentage(percentageString);

        double reserveAmountAfterAprv = claimReserveAfterAprvMap.getOrDefault(claimNo, 0.0);

        double adjustedReserveAmount = reserveAmount * (1 - percent);
        double adjustedReserveAmountAfterAprv = reserveAmountAfterAprv * (1 - percent);

        System.out.println("Claim No: " + claimNo);
        if (categoryDto != null) {
            System.out.println("Category: "
                    + " - " + categoryDto.getDescription()
                    + " [Min: " + categoryDto.getAmountMin()
                    + ", Max: " + categoryDto.getAmountMax() + "]");
        }
        System.out.println("  - % Used: " + percentageString);
        System.out.println("  - Original Reserve: " + reserveAmount + ", Adjusted: " + adjustedReserveAmount);
        System.out.println("  - Reserve After APRV: " + reserveAmountAfterAprv + ", Adjusted: " + adjustedReserveAmountAfterAprv);

        updateAdjustedReserveAmountsAndNextProcessDate(claimNo, adjustedReserveAmount, adjustedReserveAmountAfterAprv);
    }

    private void offlineReserveClaim(Connection connection, ClaimsDto claimsDto, int lossType, UserDto user, boolean theftAndFound) throws Exception {
        try {
            McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto = new McmsClaimOfflineReserveClaimDto();
            String lossCode = commonUtilDao.findOne(connection, "claim_loss_type", "V_LOSS_CODE", "N_ID=" + lossType);
            if (null != claimsDto) {
                mcmsClaimOfflineReserveClaimDto.setOvClaimNo(claimsDto.getIsfClaimNo());
                mcmsClaimOfflineReserveClaimDto.setOnBillAmount(claimsDto.getTotalAcr());
                mcmsClaimOfflineReserveClaimDto.setOnAllowedAmount(claimsDto.getTotalAcr());
                mcmsClaimOfflineReserveClaimDto.setOnDepPer(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOnPaEstimateAmount(claimsDto.getTotalAcr());
                mcmsClaimOfflineReserveClaimDto.setOvReportType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationNo(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAppointment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvPanelCategory(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvPanelType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvLossType(null != lossCode ? lossCode : AppConstant.LOSS_TYPE);
                mcmsClaimOfflineReserveClaimDto.setCvRequired(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvRiskNo(Integer.toString(1));
                mcmsClaimOfflineReserveClaimDto.setDInsertDateTime(Utility.sysDateTime());
                mcmsClaimOfflineReserveClaimDto.setNRetryAttempt(AppConstant.ZERO_INT);
                mcmsClaimOfflineReserveClaimDto.setVIsfsUpdateStat(AppConstant.NO);
                mcmsClaimOfflineReserveClaimDto.setDIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssesSub(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setClaimNo(claimsDto.getClaimNo());
                mcmsClaimOfflineReserveClaimDto.setPolicyChannelType(claimsDto.getPolicyDto().getPolicyChannelType());
                offlineReserveClaimDao.insertMaster(connection, mcmsClaimOfflineReserveClaimDto);
                saveClaimsLogs(connection, claimsDto.getClaimNo(), user, theftAndFound ? "Claim Reserve Changed - Theft and Found" : "Initial Claim Reserve - Call Center Intimation", "Total ACR Amount : " + claimsDto.getTotalAcr().toEngineeringString());
            }
        } catch (Exception e) {
//            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }


    }

    private void updateAdjustedReserveAmountsAndNextProcessDate(int claimNo, double adjustedReserve, double adjustedReserveAfterAprv) {
        try (Connection conn = getJDBCConnection()) {
            // bUpdate reserve amounts in claim_assign_claim_handler
            try (PreparedStatement ps = conn.prepareStatement(SQL_UPDATE_RESERVE_DETAILS)) {
                ps.setDouble(1, adjustedReserve);
                ps.setDouble(2, adjustedReserveAfterAprv);
                ps.setInt(3, claimNo);
                ps.executeUpdate();
            }

            // Insert or update next process date in claim_next_process
            LocalDate nextDate = LocalDate.now().plusMonths(1).plusDays(1);
            try (PreparedStatement ps2 = conn.prepareStatement(SQL_UPSERT_NEXT_PROCESS_DATE)) {
                ps2.setInt(1, claimNo);
                ps2.setDate(2, java.sql.Date.valueOf(nextDate));
                ps2.executeUpdate();
            }
        } catch (Exception ex) {
            System.err.println("Failed to update reserve amounts or next_process_date for claim " + claimNo + ": " + ex.getMessage());
        }
    }

    private ClaimReserveAdjustmentTypeDto findAdjustmentByPeriodAndAmount(
            List<ClaimReserveAdjustmentTypeDto> adjustmentTypeList, int periodId, int categoryId, double reserveAmount) {
        for (ClaimReserveAdjustmentTypeDto dto : adjustmentTypeList) {
            if (dto.getPeriodId() == periodId && dto.getCategoryId() == categoryId) {
                Double min = dto.getAmountMin() != null ? dto.getAmountMin().doubleValue() : null;
                Double max = dto.getAmountMax() != null ? dto.getAmountMax().doubleValue() : null;

                // Special logic for category "ACR>2.5M" (id = 1)
                if (categoryId == 1) {
                    if (min != null && reserveAmount >= min) {
                        return dto;
                    }
                }
                if (categoryId != 1) {
                    if ((min == null || reserveAmount >= min) &&
                            (max == null || reserveAmount < max)) {
                        return dto;
                    }
                }
            }
        }
        return null;
    }

    private double parsePercentage(String percentString) {
//        if (percentString == null || percentString.trim().equalsIgnoreCase("Closed")) {
//            return 0.0;
//        }
        try {
            return Double.parseDouble(percentString) / 100.0;
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

}
