/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.controller.common;


import com.misyn.mcms.admin.admin.dto.BankDetailsDto;
import com.misyn.mcms.admin.fileupload.FileUploadListener;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimDocumentDto;
import com.misyn.mcms.claim.dto.DocumentHistoryDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.enums.FileTypeEnum;
import com.misyn.mcms.claim.exception.MisynAppException;
import com.misyn.mcms.claim.service.BankDetailsService;
import com.misyn.mcms.claim.service.DocumenthistoryService;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.claim.service.impl.BankDetailsServiceImpl;
import com.misyn.mcms.claim.service.impl.StorageServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.fileupload2.core.DiskFileItem;
import org.apache.commons.fileupload2.core.DiskFileItemFactory;
import org.apache.commons.fileupload2.jakarta.JakartaServletDiskFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;


/**
 * <AUTHOR>
 */
@WebServlet(name = "DocumentUploadController", urlPatterns = "/DocumentUploadController")
public class DocumentUploadController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentUploadController.class);
    private static final Object LOCK = new Object();
    private static AtomicLong INDEX = new AtomicLong();
    private static boolean isInvalid = false;
    private final BankDetailsService bankDetailsService = new BankDetailsServiceImpl();

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        try {
            HttpSession session = request.getSession();
            doFileUpload(session, request, response);

        } finally {
            out.close();
        }
    }

    private void doFileUpload(HttpSession session, HttpServletRequest request, HttpServletResponse response) throws IOException {

        StorageService storageService = (StorageService)
                session.getAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE);

        if (storageService == null) {
            storageService = new StorageServiceImpl();
            session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
        }

        DocumenthistoryService documenthistoryService = getDocumentHistoryServiceBySession(request);

        UserDto user = (UserDto) session.getAttribute(AppConstant.SESSION_USER);
        int documentTypeId = AppConstant.ZERO_INT;
        Integer claimNo = AppConstant.ZERO_INT;
        int cardId = AppConstant.ZERO_INT;
        int jobRefNo = AppConstant.ZERO_INT;
        int departmentId = AppConstant.ZERO_INT;
        long requestFormId = 0L;
        String inputUserId = user.getUserId();
        String instrumentType = AppConstant.EMPTY_STRING;
        String payeeType = AppConstant.EMPTY_STRING;
        String payeeName = AppConstant.EMPTY_STRING;
        boolean hasUpload = false;
        try {
            FileUploadListener listener = new FileUploadListener(request.getContentLength());
            session.removeAttribute("FILE_UPLOAD_STATS");
            session.setAttribute("FILE_UPLOAD_STATS", listener.getFileUploadStats());
            if (listener.getFileUploadStats().getTotalSize() / (1024 * 1024) > 1000) {
                listener.getFileUploadStats().setCurrentStatus("File Size Exceeded");
                listener.getFileUploadStats().setUploadPresentage("");
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            DiskFileItemFactory factory = DiskFileItemFactory.builder().get();
            JakartaServletDiskFileUpload upload = new JakartaServletDiskFileUpload(factory);
            List<DiskFileItem> items = upload.parseRequest(request);


            for (DiskFileItem fileItem : items) {
                if (fileItem.isFormField()) {
                    switch (fileItem.getFieldName()) {
                        case AppConstant.DOCUMENT_TYPE_ID:
                            fileItem.getString();
                            documentTypeId = Integer.parseInt(fileItem.getString());
                            break;
                        case AppConstant.CLAIM_NO:
                            fileItem.getString();
                            claimNo = Integer.parseInt(fileItem.getString());
                            break;
                        case AppConstant.JOB_REF_NO:
                            fileItem.getString();
                            jobRefNo = Integer.parseInt(fileItem.getString());
                            break;
                        case AppConstant.DEPARTMENT_ID:
                            fileItem.getString();
                            departmentId = Integer.parseInt(fileItem.getString());
                            break;
                        case AppConstant.REQUEST_FORM_ID:
                            fileItem.getString();
                            requestFormId = Long.parseLong(fileItem.getString().isEmpty() ? AppConstant.ZERO : fileItem.getString());
                            break;
                        case AppConstant.BANK_DETAILS_CARD_ID:
                            fileItem.getString();
                            cardId = Integer.parseInt(fileItem.getString());
                            break;
                        case AppConstant.BANK_DETAILS_INSTRUMENT_TYPE:
                            fileItem.getString();
                            instrumentType = fileItem.getString();
                            break;
                        case AppConstant.BANK_DETAILS_PAYEE_TYPE:
                            fileItem.getString();
                            payeeType = fileItem.getString();
                            break;
                        case AppConstant.BANK_DETAILS_PAYEE_NAME:
                            fileItem.getString();
                            payeeName = fileItem.getString();
                            break;
                    }

                }
            }


            for (DiskFileItem fileItem : items) {
                if (!fileItem.isFormField()) {

                    switch (fileItem.getContentType()) {
                        case "image/png":
                        case "image/jpeg":
                        case "application/pdf":
                        case "audio/mpeg":
                        case "audio/mp3":
                        case "audio/ogg":
                        case "audio/wav":
                        case "audio/x-wav":
                        case "audio/x-m4a":
                            checkDoctype(response, documentTypeId, fileItem.getContentType());
                            break;
                        default:
                            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                            response.getWriter().write("{" +
                                    "\"errors\": [\n" +
                                    "    {\n" +
                                    "      \"status\": \"500\",\n" +
                                    "      \"detail\": \"Invalid document type\"\n" +
                                    "    }\n" +
                                    "  ]}");
                            return;
                    }

                    if (!fileItem.getName().trim().equalsIgnoreCase(AppConstant.STRING_EMPTY)) {
                        String fileExtension = fileItem.getName().substring(fileItem.getName().lastIndexOf(AppConstant.STRING_DOT) + 1).toLowerCase();
                        String fileName = Utility.sysDate(AppConstant.DATE_TIME_FORMAT_INT)
                                .concat(String.valueOf(INDEX.incrementAndGet())).concat(AppConstant.STRING_UNDERSCORE_SIGN)
                                .concat(inputUserId)
                                .concat(AppConstant.STRING_DOT)
                                .concat(fileExtension);

                        ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
                        claimDocumentDto.setClaimNo(claimNo);
                        claimDocumentDto.setJobRefNo(jobRefNo);
                        claimDocumentDto.setDocumentName(fileName);
                        claimDocumentDto.setInputStream(fileItem.getInputStream());
                        claimDocumentDto.setFileExtension(fileExtension);
                        claimDocumentDto.setDocumentTypeId(documentTypeId);
                        claimDocumentDto.setDepartmentId(departmentId);
                        claimDocumentDto.setInpUser(user.getUserId());
                        claimDocumentDto.setInpDateTime(Utility.sysDateTime());
                        claimDocumentDto.setIsBankDetails(cardId > 0 ? AppConstant.YES : AppConstant.NO);

                        switch (fileExtension) {
                            case AppConstant.PDF:
                                claimDocumentDto.setFileTypeEnum(FileTypeEnum.PDF);
                                break;
                            case AppConstant.PNG:
                            case AppConstant.JPEG:
                            case AppConstant.JPG:
                            case AppConstant.GIF:
                                claimDocumentDto.setFileTypeEnum(FileTypeEnum.IMAGE);
                                break;
                            case "mp3":
                            case "wav":
                            case "ogg":
                            case "m4a":
                            case "aac":
                                claimDocumentDto.setFileTypeEnum(FileTypeEnum.AUDIO);
                                break;
                            case AppConstant.JFIF:
                                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                                response.getWriter().write("{" +
                                        "\"errors\": [\n" +
                                        "    {\n" +
                                        "      \"status\": \"500\",\n" +
                                        "      \"detail\": \"Invalid document type \"\n" +
                                        "    }\n" +
                                        "  ]}");
                                return;
                            default:
                                claimDocumentDto.setFileTypeEnum(FileTypeEnum.DEFAULT);
                        }

                        DocumentHistoryDto documentHistoryDto = new DocumentHistoryDto();
                        documentHistoryDto.setJobRefNo(claimDocumentDto.getJobRefNo());
                        documentHistoryDto.setClaimNo(claimNo);
                        documentHistoryDto.setName(fileItem.getName());
                        documentHistoryDto.setType(claimDocumentDto.getDocumentTypeId().toString());

                        synchronized (LOCK) {
                            if (!documenthistoryService.isDocumentDuplicated(documentHistoryDto)) {
                                ClaimDocumentDto claimDocumentDto1 = storageService.uploadDocument(requestFormId, claimDocumentDto, user);
                                documentHistoryDto.setDocRefNo(claimDocumentDto1.getRefNo());
                                documenthistoryService.insert(documentHistoryDto);
                                if (cardId != 0) {
                                    BankDetailsDto bankDetailsDto = new BankDetailsDto();
                                    bankDetailsDto.setDocRefNo(claimDocumentDto1.getRefNo());
                                    bankDetailsDto.setId(cardId);
                                    bankDetailsDto.setClaimNo(claimNo);
                                    bankDetailsDto.setInstrumentTypeDesc(instrumentType);
                                    bankDetailsDto.setPayeeTypeDesc(payeeType);
                                    bankDetailsDto.setPayeeNameDesc(payeeName);
                                    bankDetailsService.updateDocRefNo(bankDetailsDto, user);
                                }
                            }
                            hasUpload = true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{" +
                    "\"errors\": \n" +
                    "    {\n" +
                    "      \"status\": \"500\",\n" +
                    "      \"detail\": \"Document Upload Failed\"\n" +
                    "    }\n" +
                    "  }");
        } finally {

            if (hasUpload) {
                response.setStatus(HttpServletResponse.SC_OK);
                response.getWriter().write("{" +
                        "\"errors\": \n" +
                        "    {\n" +
                        "      \"status\": \"200\",\n" +
                        "      \"detail\": \"Success\"\n" +
                        "    }\n" +
                        "  }");
            } else if (isInvalid) {
                response.setStatus(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE);
                response.getWriter().write("{" +
                        "\"errors\": \n" +
                        "    {\n" +
                        "      \"status\": \"415\",\n" +
                        "      \"detail\": \"Unsupported Media Type\"\n" +
                        "    }\n" +
                        "  }");
            } else {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{" +
                        "\"errors\": \n" +
                        "    {\n" +
                        "      \"status\": \"500\",\n" +
                        "      \"detail\": \"Document Upload Failed\"\n" +
                        "    }\n" +
                        "  }");
            }

        }
    }

    private void checkDoctype(HttpServletResponse response, int documentTypeId, String docType) throws IOException {

        switch (docType) {
            case "image/png":
            case "image/jpeg":
            case "application/pdf":
                if ((documentTypeId == AppConstant.REJECTION_AUDIO_TYPE)) {
                    response.setStatus(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE);
                    isInvalid = true;
                    throw new MisynAppException("Unsupported Media Type!...");
                }
                break;
            case "audio/mpeg":
            case "audio/mp3":
            case "audio/ogg":
            case "audio/wav":
            case "audio/x-wav":
            case "audio/x-m4a":
                if (documentTypeId != AppConstant.REJECTION_AUDIO_TYPE) {
                    response.setStatus(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE);
                    isInvalid = true;
                    throw new MisynAppException("Unsupported Media Type!...");
                }
        }
    }


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    @Override
    public String getServletInfo() {
        return "Document Upload";
    }


}
