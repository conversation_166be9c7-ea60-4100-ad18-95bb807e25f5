package com.misyn.mcms.claim.controller.assessor;

import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "TestController", urlPatterns = "/TestController/*")
public class TestController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(TestController.class);
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        int type = 0;
        try {
            switch (pathInfo) {
                case "/bill":
                    request.setAttribute("refNo", "197");
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/pdfBillCheckViewerAll.jsp");
                    break;
                case "/docUpload":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/documentUpload.jsp");
                    break;
                case "/documentViewer":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/documentView.jsp");
                    break;
                case "/rejDocument":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/rejDocument.jsp");
                    break;
                case "/docUploadSingale":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/docUpload.jsp");
                    break;
                case "/pdfView":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/pdfViewer.jsp");
                    break;
                case "/imgCom":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/photoComparison.jsp");
                    break;
                case "/claimHandler":
                    request.setAttribute(AppConstant.SESSION_CLAIM_DTO, new ClaimsDto());
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimHandlerView.jsp");
                    break;
                case "/dashbord":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/superDashboard.jsp");
                    break;
                case "/addUser":
//                    request.setAttribute(AppConstant.SESSION_CLAIM_DTO, new ClaimsDto());
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/systemParameter/claimPanelUserView.jsp");
                    break;
                case "/fileUpload":
                    response.setStatus(HttpServletResponse.SC_OK);
                    response.getWriter().write("{" +
                            "\"errors\": [\n" +
                            "    {\n" +
                            "      \"status\": \"200\",\n" +
                            "      \"detail\": \"Success\"\n" +
                            "    }\n" +
                            "  ]}");
                    break;

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


}
