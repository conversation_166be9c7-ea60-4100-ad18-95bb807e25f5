package com.misyn.mcms.claim.controller.common;

import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimImageDto;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.claim.service.impl.StorageServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 */

@WebServlet(name = "AudioDownloadController", urlPatterns = "/AudioDownloadController")
public class AudioDownloadController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentDownloadController.class);
    private static final int DEFAULT_BUFFER_SIZE = 1024;


    private static void close(Closeable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        }
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) {
    InputStream is = null;
    OutputStream os = null;
    HttpSession session = request.getSession();
    StorageService storageService = (StorageService)
            session.getAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE);
        if (storageService == null) {
        storageService = new StorageServiceImpl();
        session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
    }
        try {
        Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
        ClaimImageDto claimImageDto = storageService.getClaimImageDto(refNo);
        request.setAttribute("refNo", refNo);
        if (claimImageDto != null) {

            is = storageService.viewUploadAudio(refNo);
            String contentType = "audio/mpeg";
            response.reset();
            response.setBufferSize(DEFAULT_BUFFER_SIZE);
            response.setHeader("Content-type", contentType);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + claimImageDto.getDocumentName() + "\"");

            int read;
            byte[] bytes = new byte[1024];
            os = response.getOutputStream();
            while ((read = is.read(bytes)) != -1) {
                os.write(bytes, 0, read);
            }
            os.flush();
            os.close();
        }

    } catch (Exception e) {
        LOGGER.error(e.getMessage());
    } finally {
        close(is);
        close(os);

    }
}

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    @Override
    public String getServletInfo() {
        return "Document Upload";
    }

}

