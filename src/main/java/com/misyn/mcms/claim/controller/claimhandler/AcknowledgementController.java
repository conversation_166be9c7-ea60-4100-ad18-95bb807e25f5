package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.AcknowledgementService;
import com.misyn.mcms.claim.service.ClaimWiseDocumentService;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "AcknowledgementController", urlPatterns = "/AcknowledgementController/*")
public class AcknowledgementController extends BaseController {


    private static final Logger LOGGER = LoggerFactory.getLogger(AcknowledgementController.class);
    private AcknowledgementService acknowledgementService;
    private ClaimWiseDocumentService claimWiseDocumentService;
    private int draw = 1;

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        acknowledgementService = getAcknowledgementService(request);
        claimWiseDocumentService = getClaimWiseDocumentServiceBySession(request);
        ClaimUserTypeDto claimUserTypeDto;

        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/claimViewList":
                    claimViewList(request, response);
                    break;
                case "/claimList":
                    claimList(request, response);
                    break;
                case "/docList":
                    claimList(request, response);
                    break;
                case "/claimDocList":
                    claimDocList(request, response);
                    break;
                case "/save":
                    save(request, response);
                    break;
                case "/previousList":
                    previousList(request, response);
                    break;


            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    private void claimList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        HttpSession session = request.getSession();
        boolean liablityUser = (boolean) session.getAttribute(AppConstant.IS_INIT_LIABILITY_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER);
        boolean claimUser = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        boolean decisionMakerUser = (boolean) session.getAttribute(AppConstant.IS_DECISION_MAKER);
        String userId = getSessionUser(request).getUserId();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                this.addFieldParameter("t2.V_IS_FILE_STORE", fileStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (liablityUser) {
                this.addFieldParameter("t2.V_INIT_LIABILITY_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }

           /* if (2 == type && decisionMakerUser) {
                this.addFieldParameter("t2.V_DECISION_MAKING_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            } else if (claimUser) {
                this.addFieldParameter("t2.V_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }*/

            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;

            }
            DataGridDto data = acknowledgementService.getClaimHandlerDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void claimViewList(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,84)");
        try {

            int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
            removeSessionType(request, response);
            updateSessionType(request, response, type);
            removeSessionClaimDetails(request, response);
            request.setAttribute("statusList", popupItemDtoList);
            request.setAttribute("IS_SHOW", AppConstant.NO);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/acknowledgementClaimList.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void claimDocList(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,84)");
        List<ClaimWiseDocumentDto> claimWiseDocumentDtos;
        try {

            int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
            int claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            removeSessionType(request, response);
            updateSessionType(request, response, type);
            removeSessionClaimDetails(request, response);
            request.setAttribute("statusList", popupItemDtoList);
            claimWiseDocumentDtos = acknowledgementService.searchDocumentsByClaimNo(claimId);
            request.setAttribute("docList", claimWiseDocumentDtos);
            request.setAttribute("IS_SHOW", AppConstant.YES);
            request.setAttribute("P_N_CLIM_NO", claimId);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/acknowledgementClaimList.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void save(HttpServletRequest request, HttpServletResponse response) {
        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,84)");
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {

            String remark = request.getParameter("remark");
            String customerRemark = request.getParameter("customerRemark");
            String ids = request.getParameter("selectedIds");
            int claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            AcknowledgementSummaryDto acknowledgementSummaryDto = new AcknowledgementSummaryDto();
            acknowledgementSummaryDto.setAcknowledgementRemark(customerRemark);
            acknowledgementSummaryDto.setSpecialRemark(remark);
            acknowledgementSummaryDto.setClaimNo(claimId);
            AcknowledgementSummaryDto acknowledgementSummary = acknowledgementService.insert(acknowledgementSummaryDto, ids, user);
            json = gson.toJson(acknowledgementSummary);
            printWriter(request, response, json);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("ERROR");
            printWriter(request, response, json);

        }
    }

    private void previousList(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,84)");
        List<ClaimWiseDocumentDto> claimWiseDocumentDtos;
        try {

            int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
            int claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            removeSessionType(request, response);
            updateSessionType(request, response, type);
            removeSessionClaimDetails(request, response);
            request.setAttribute("statusList", popupItemDtoList);
            List<AcknowledgementSummaryDto> previousAcknowledgementList = acknowledgementService.getPreviousAcknowledgementList(claimId);
            request.setAttribute("previousList", previousAcknowledgementList);
            request.setAttribute("IS_PREVIOUS_SHOW", AppConstant.YES);
            request.setAttribute("P_N_CLIM_NO", claimId);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/acknowledgementClaimList.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


}
