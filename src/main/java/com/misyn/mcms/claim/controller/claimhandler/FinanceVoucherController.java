package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.FinanceReasonUpdateDetailsDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.dto.VoucherDetailsDto;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.service.FinanceVoucherDetailsService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "FinanceVoucherController", urlPatterns = "/FinanceVoucherController/*")
public class FinanceVoucherController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(FinanceVoucherController.class);
    private FinanceVoucherDetailsService financeVoucherDetailsService;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String pathInfo = request.getPathInfo();
        financeVoucherDetailsService = getFinanceVoucherDetailsServiceBySession(request);
        try {
            switch (pathInfo) {
                case "/viewVoucherDetails":
                    viewList(request, response, user);
                    break;
                case "/updateFinanceReason":
                    updateFinanceReason(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void updateFinanceReason(HttpServletRequest request, HttpServletResponse response) {
        String json;
        UserDto user = getSessionUser(request);
        String voucherNo = request.getParameter("voucherNo");
        String financeReasonType = request.getParameter("financeReasonType");
        Gson gson = new Gson();
        try {
            FinanceReasonUpdateDetailsDto financeReasonUpdateDetailsDto = new FinanceReasonUpdateDetailsDto();
            financeReasonUpdateDetailsDto.setRecordStatus("A");
            financeReasonUpdateDetailsDto.setReasonId(Integer.parseInt(financeReasonType));
            financeReasonUpdateDetailsDto.setVoucherNo(voucherNo);
            financeReasonUpdateDetailsDto.setInputId(user.getUserId());
            financeReasonUpdateDetailsDto.setInputDateTime(Utility.sysDateTime());
            if (financeVoucherDetailsService.saveFinanceReason(financeReasonUpdateDetailsDto) > 0) {
                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void viewList(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        String fromDate = null == request.getParameter("txtFromDate") ? AppConstant.STRING_EMPTY : request.getParameter("txtFromDate");
        String toDate = null == request.getParameter("txtToDate") ? AppConstant.STRING_EMPTY : request.getParameter("txtToDate");
        String voucherStatus = null == request.getParameter("voucherStatus") ? "All" : request.getParameter("voucherStatus");
//        String vehicleNumber = null == request.getParameter("vehicleNumber") ? AppConstant.STRING_EMPTY : request.getParameter("vehicleNumber");
        String voucherNumber = null == request.getParameter("voucherNumber") ? AppConstant.STRING_EMPTY : request.getParameter("voucherNumber");
        String claimNo = null == request.getParameter("claimNo") || AppConstant.STRING_EMPTY.equals(request.getParameter("claimNo"))
                ? AppConstant.ZERO : request.getParameter("claimNo");

        List<VoucherDetailsDto> list = new ArrayList<>();
        try {
            String policyChannelType = !AppConstant.ZERO.equals(claimNo) &&
                    null != financeVoucherDetailsService.getPolicyChannelType(Integer.valueOf(claimNo)) &&
                    financeVoucherDetailsService.getPolicyChannelType(Integer.valueOf(claimNo))
                            .equals(PolicyChannelType.TAKAFUL.name())
                    ? AppConstant.URL_TYPE_TAKAFUL : AppConstant.URL_TYPE_CONVENTIONAL;

            if (!voucherNumber.isEmpty()) {
                fromDate = "";
                toDate = "";
                voucherStatus = "All";
                List<VoucherDetailsDto> list1;
                List<VoucherDetailsDto> list2;
                list1 = financeVoucherDetailsService.getVoucherDetailsDtoListByVoucherNo(voucherNumber, AppConstant.URL_TYPE_CONVENTIONAL);
                list2 = financeVoucherDetailsService.getVoucherDetailsDtoListByVoucherNo(voucherNumber, AppConstant.URL_TYPE_TAKAFUL);
                list.addAll(list1);
                list.addAll(list2);
            } else if (!claimNo.equals(AppConstant.ZERO)) {
                fromDate = "";
                toDate = "";
                voucherStatus = "All";
                list = financeVoucherDetailsService.getVoucherDetailsDtoListByClaimNo(claimNo, policyChannelType);
            } else if (!fromDate.isEmpty() && !toDate.isEmpty()) {
                List<VoucherDetailsDto> list1;
                List<VoucherDetailsDto> list2;
                if (!voucherStatus.equalsIgnoreCase("All")) {
                    list1 = financeVoucherDetailsService.getVoucherDetailsDtoListByFromDateAndToDateAndStatus(fromDate, toDate, voucherStatus, AppConstant.URL_TYPE_CONVENTIONAL);
                    list2 = financeVoucherDetailsService.getVoucherDetailsDtoListByFromDateAndToDateAndStatus(fromDate, toDate, voucherStatus, AppConstant.URL_TYPE_TAKAFUL);
                } else {
                    list1 = financeVoucherDetailsService.getVoucherDetailsDtoListByFromDateAndToDate(fromDate, toDate, AppConstant.URL_TYPE_CONVENTIONAL);
                    list2 = financeVoucherDetailsService.getVoucherDetailsDtoListByFromDateAndToDate(fromDate, toDate, AppConstant.URL_TYPE_TAKAFUL);
                }
                list.addAll(list1);
                list.addAll(list2);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        request.setAttribute(AppConstant.VOUCHER_LIST, list);
        request.setAttribute(AppConstant.TXT_FROM_DATE, fromDate);
        request.setAttribute(AppConstant.TXT_TO_DATE, toDate);
        request.setAttribute(AppConstant.VOUCHER_STATUS, voucherStatus);
        request.setAttribute(AppConstant.VOUCHER_NUMBER, voucherNumber);
        request.setAttribute(AppConstant.CLAIM_NO, AppConstant.ZERO.equals(claimNo) ? AppConstant.STRING_EMPTY : claimNo);
        request.setAttribute(AppConstant.FINANCE_REASON_LIST, financeVoucherDetailsService.getFinanceReasonList());
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/financeView/financeVoucherViewList.jsp");
    }

}
