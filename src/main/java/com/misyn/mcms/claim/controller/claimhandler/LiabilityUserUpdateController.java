package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.service.CalculationSheetService;
import com.misyn.mcms.claim.service.ClaimUserUpdateService;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@WebServlet(name = "LiabilityUserUpdateController", urlPatterns = "/LiabilityUserUpdateController/*")
public class LiabilityUserUpdateController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(LiabilityUserUpdateController.class);

    private ClaimUserUpdateService claimUserUpdateService;
    private CalculationSheetService calculationSheetService;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        claimUserUpdateService = getClaimUserUpdateServiceBySession(request);
        calculationSheetService = getCalculationSheetServiceBySession(request);
        ClaimUserTypeDto claimUserTypeDto;
        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/claimHandlerList":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimHandlerUserUpdate.jsp");
                    break;
                case "/update":
                    updateClaimHandlerUser(request, response);
                    break;
                case "/search":
                    searchClaimHandlerUser(request, response);
                    break;
                case "/claimHandlerUserList":
                    ClaimHandlerUserList(request, response);
                    break;
                case "/getUserList":
                    getUserList(request, response);
                    break;
                case "/claimHandlerBulkReassignList":
                    List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(47,35,36,37,38,39,40" +
                            ",41,42,43,44,45,46,47,48,49,17,50,52,53,54,55,56,57,68,5,84)");
                    Integer type = null == request.getParameter(AppConstant.TYPE) || request.getParameter(AppConstant.TYPE).isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter(AppConstant.TYPE));
                    if (type == 200) {
                        request.setAttribute("mofaLevels", claimUserUpdateService.getMofaLevels());
                    }
                    removeSessionClaimDetails(request, response);
                    request.setAttribute("statusList", popupItemDtoList);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimHandlerBulkUserUpdate.jsp");
                    break;
                case "/selectClaimNoAndAssignUsers":
                    setIdsToArray(request, response);
                    break;
                case "/updateBulkReassign":
                    updateBulkReassign(request, response);
                    break;
                case "/mofaUserList":
                    mofaUserList(request, response);
                    break;
                case "/searchMofa":
                    searchMofa(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchMofa(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("txnNo") || request.getParameter("txnNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("txnNo"));
        String assignUser = null == request.getParameter("assignUser") || request.getParameter("assignUser").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");
        Boolean levelReassign = null != request.getParameter("levelReassign") && !request.getParameter("levelReassign").isEmpty() && Boolean.parseBoolean(request.getParameter("levelReassign"));
        Integer Status = Integer.valueOf(null == request.getParameter("claimStatus") || request.getParameter("claimStatus").isEmpty() ? AppConstant.ZERO : request.getParameter("claimStatus"));
        Gson gson = new Gson();
        String json;
        try {
            List<String> users = calculationSheetService.getNextAssignUsersByCurrentUserName(assignUser, claimNo, levelReassign, Status);
            json = gson.toJson(null == users || users.isEmpty() ? "NOT FOUND" : users);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            json = gson.toJson("NOT FOUND");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void mofaUserList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");
        String assignUserType = request.getParameter(AppConstant.ASSIGN_USER_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSIGN_USER_TYPE);
        String assignUserName = request.getParameter(AppConstant.ASSIGN_USER_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSIGN_USER_NAME);
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.ZERO : request.getParameter(AppConstant.TXT_V_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String calsheetStatus = request.getParameter(AppConstant.TXT_CALSHEET_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CALSHEET_STATUS);
        String assignUserLevel = request.getParameter(AppConstant.ASSIGN_USER_LEVEL) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSIGN_USER_LEVEL);
        HttpSession session = request.getSession();
        boolean liablityUser = (boolean) session.getAttribute(AppConstant.IS_INIT_LIABILITY_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER);
        boolean claimUser = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        boolean decisionMakerUser = (boolean) session.getAttribute(AppConstant.IS_DECISION_MAKER);
        String userId = getSessionUser(request).getUserId();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            switch (orderColumnName) {
                case "refNo":
                    orderColumnName = "t1.N_TXN_NO";
                    break;
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = getAssignDateColumName(assignUserType);
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                case "policyNo":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
            }

            this.addFieldParameter("N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equals(liabilityStatus)) {
                this.addFieldParameter("t1.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t1.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.STRING_EMPTY.equals(finalizedStatus)) {
                this.addFieldParameter("t1.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }


            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }

            if (liablityUser) {
                this.addFieldParameter("t2.V_INIT_LIABILITY_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }

            boolean isSearch = request.getParameter("isSearch") == null
                    ? false
                    : (request.getParameter("isSearch").equalsIgnoreCase("1") ? true : false);

            request.getSession().setAttribute(AppConstant.ASSIGN_USER_TYPE, assignUserType);
            DataGridDto data = claimUserUpdateService.getMofaDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, assignUserType, assignUserName, isSearch, calsheetStatus, status, assignUserLevel);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updateBulkReassign(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            setClaimPanelUserPopupListValues(request);
            String claimNoAndTxnIds = request.getParameter("claimNoAndTxnIds") == null ? AppConstant.STRING_EMPTY : request.getParameter("claimNoAndTxnIds");
            String assignUser = request.getParameter("assignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");
            String assignUserType = request.getParameter(AppConstant.ASSIGN_USER_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSIGN_USER_TYPE);
            String claimNoAndAlreadyAssignUser = request.getParameter("claimNoAndAlreadyAssignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("claimNoAndAlreadyAssignUser");
            Integer level = null == request.getParameter("level") || request.getParameter("level").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("level"));
            Integer status = null == request.getParameter("status") || request.getParameter("status").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("status"));

            HashMap<String, String> claimWithTxnIdMap = claimUserUpdateService.setClaimNoandTxnIdToMap(claimNoAndTxnIds);
            HashMap<String, String> claimWithAlreadyAssignUserMap = claimUserUpdateService.setClaimNoandAlreadyAssignUserMap(claimNoAndAlreadyAssignUser);
            if (null != claimWithTxnIdMap) {
                for (String claimNo : claimWithTxnIdMap.keySet()) {
                    String txnId = claimWithTxnIdMap.get(claimNo);
                    String alreadyAssignUser = claimWithAlreadyAssignUserMap.get(claimNo);
                    claimUserUpdateService.updateClaimnUser(Integer.parseInt(txnId), "", assignUser, user.getUserId(), Integer.parseInt(claimNo), assignUserType, alreadyAssignUser, level, status);
                }
            }
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void setIdsToArray(HttpServletRequest request, HttpServletResponse response) {
        try {
            String selectClaimNoAndAssignUsers = request.getParameter(AppConstant.SELECT_CLAIM_NO_AND_ASSIGN_USERS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.SELECT_CLAIM_NO_AND_ASSIGN_USERS);
//            String selectTxnIds = request.getParameter(AppConstant.SELECT_TXN_IDS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.SELECT_TXN_IDS);
//            String selectAssignUsers = request.getParameter(AppConstant.SELECT_ASSIGN_USERS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.SELECT_ASSIGN_USERS);

            List<String> list = claimUserUpdateService.getIdsToArray(selectClaimNoAndAssignUsers);

            Gson gson = new Gson();
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getUserList(HttpServletRequest request, HttpServletResponse response) {
        try {
            String userType = AppConstant.STRING_EMPTY;
            Integer type = null == request.getParameter(AppConstant.TYPE) || request.getParameter(AppConstant.TYPE).isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter(AppConstant.TYPE));
            Integer user = null == request.getParameter("userType") || request.getParameter("userType").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("userType"));
            Integer assignUserType = request.getParameter(AppConstant.ASSIGN_USER_TYPE) == null ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter(AppConstant.ASSIGN_USER_TYPE));
            Integer level = null == request.getParameter("level") || request.getParameter("level").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("level"));

            if (type == 200) {
                getUserListByLevel(request, response, user, level);
            } else {
                switch (assignUserType) {
                    case 1:
                        userType = "41";
                        break;
                    case 2:
                        userType = "45";
                        break;
                    case 3:
                        userType = "47";
                        break;
                    case 4:
                        userType = "48";
                        break;
                    case 5:
                        userType = "43";
                        break;
                    case 6:
                        userType = "42";
                        break;
                    case 7:
                        userType = "40";
                        break;
                    case 8:
                        userType = "46";
                        break;
                    case 9:
                        userType = "61";
                        break;
                    case 10:
                        userType = "60";
                        break;
                    case 11:
                        userType = "63";
                        break;
                    case 12:
                        userType = "62";
                        break;
                    case 13:
                    case 14:
                        userType = "27";
                        break;
                    case 15:
                    case 16:
                        userType = "28";
                        break;

                }
                UserDto sessionUser = getSessionUser(request);
                getUserListByAccessUserType(request, response, userType,sessionUser);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getUserListByLevel(HttpServletRequest request, HttpServletResponse response, Integer assignUserType, Integer level) {
        Gson gson = new Gson();
        String json;
        try {
            List<String> usersByLevel = calculationSheetService.getUserListByLevel(assignUserType, level);
            json = gson.toJson(null != usersByLevel && !usersByLevel.isEmpty() ? usersByLevel : "NOT FOUND");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void ClaimHandlerUserList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");
        String assignUserType = request.getParameter(AppConstant.ASSIGN_USER_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSIGN_USER_TYPE);
        String assignUserName = request.getParameter(AppConstant.ASSIGN_USER_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSIGN_USER_NAME);
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.ZERO : request.getParameter(AppConstant.TXT_V_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String calsheetStatus = request.getParameter(AppConstant.TXT_CALSHEET_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CALSHEET_STATUS);
        String supplierStatus = request.getParameter(AppConstant.TXT_SUPPLIER_STAUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_SUPPLIER_STAUS);
        String assignUserLevel = request.getParameter(AppConstant.ASSIGN_USER_LEVEL) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSIGN_USER_LEVEL);
        HttpSession session = request.getSession();
        boolean liablityUser = (boolean) session.getAttribute(AppConstant.IS_INIT_LIABILITY_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER);
        boolean claimUser = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        boolean decisionMakerUser = (boolean) session.getAttribute(AppConstant.IS_DECISION_MAKER);
        UserDto sessionUser = getSessionUser(request);
        String userId = sessionUser.getUserId();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);
//            String orderColumnName = "";
            if ("refNo".equalsIgnoreCase(orderColumnName)) {
                switch (assignUserType) {
                    case "1":
                    case "7":
                    case "8":
                    case "9":
                    case "10":
                    case "2":
                    case "13":
                    case "15":
                        orderColumnName = "t3.n_ref_id";
                        break;
                    case "3":
                    case "4":
                    case "5":
                    case "11":
                    case "6":
                    case "12":
                    case "14":
                    case "16":
                        orderColumnName = "t1.N_TXN_NO";
                        break;
                    default:
                        break;
                }
            } else {
                switch (orderColumnName) {
                    case "txnId":
                        orderColumnName = "t2.N_TXN_NO";
                        break;
                    case "claimNo":
                        orderColumnName = "N_CLIM_NO";
                        break;
                    case "policyNumberValue":
                        orderColumnName = "t1.V_POL_NUMBER";
                        break;
                    case "vehicleNo":
                        orderColumnName = "V_VEHICLE_NO";
                        break;
                    case "callUser":
                        orderColumnName = "t1.V_CALL_USER";
                        break;
                    case "dateOfReport":
                        orderColumnName = "t1.D_DATE_OF_REPORT";
                        break;
                    case "timeOfReport":
                        orderColumnName = "t1.T_TIME_OF_REPORT";
                        break;
                    case "coverNoteNo":
                        orderColumnName = "t1.V_COVER_NOTE_NO";
                        break;
                    case "reporterName":
                        orderColumnName = "t1.V_REPORTER_NAME";
                        break;
//                    case "assignDateTime":
//                        orderColumnName = "t1.D_ACCID_DATE";
//                        break;
                    case "assignDateTime":
                        orderColumnName = getAssignDateColumName(assignUserType);
                        break;
                    case "accidTime":
                        orderColumnName = "t1.T_ACCID_TIME";
                        break;
                    case "acr":
                        orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                        break;
                    case "claimStatusDesc":
                        orderColumnName = "t3.v_status_desc";
                        break;
                    case "presentReverseAmount":
                        orderColumnName = "t2.N_RESERVE_AMOUNT";
                        break;
                    case "liabilityAssignUser":
                        orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                        break;
                    case "liabilityAssignDatetime":
                        orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                        break;
                    case "intLiabilityAssignUser":
                        orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                        break;
                    case "intLiabilityAssignDatetime":
                        orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                        break;
//                case "refNo":
//                    orderColumnName = "t1.N_REF_NO";
//                    break;
                    case "policyNo":
                        orderColumnName = "t1.V_POL_NUMBER";
                        break;

                }
            }

            switch (assignUserType) {
                case "13":
                case "15":
                    if (!"0".equals(supplierStatus)) {
                        String staus = AppConstant.EMPTY_STRING;
                        switch (supplierStatus) {
                            case "1":
                                staus = "'p'";
                                break;
                            case "2":
                                staus = "'R'";
                                break;
                            case "3":
                                staus = "'SCRUTINIZING-F', 'SPC-A', 'SCRUTINIZING-R', 'A'";
                                break;
                            case "4":
                                staus = "'SCRUTINIZING-A'";
                                break;
                            case "5":
                                staus = "'CH-F'";
                                break;
                            case "6":
                                staus = "'CH-A'";
                                break;
                            case "7":
                                staus = "'RTE-F'";
                                break;
                            case "8":
                                staus = "'RTE-A'";
                                break;
                            case "9":
                                staus = "'G'";
                                break;
                            case "10":
                                staus = "'U'";
                                break;
                            case "11":
                                staus = "'SPC-F', 'SPC-R'";
                                break;
                        }
                        this.addFieldParameter(assignUserType.equals("13") ? "t5.v_supply_order_status" : "t4.v_supply_order_status", staus, FieldParameterDto.SearchType.IN, parameterList);
                    }
            }

            this.addFieldParameter("N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);
            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equals(liabilityStatus)) {
                if ("6".equals(assignUserType) || "12".equals(assignUserType) || "5".equals(assignUserType) || "11".equals(assignUserType) || "4".equals(assignUserType) || "3".equals(assignUserType)) {
                    this.addFieldParameter("t1.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
                } else {
                    this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
                }
            }
            boolean isRejectedClaim = false;
            if ("2".equals(assignUserType)) {
                isRejectedClaim = true;
                if (!AppConstant.ZERO.equals(status)) {
                    this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
                }
//                else {
//                    this.addFieldParameter("t2.N_CLAIM_STATUS", AppConstant.ALL_DECISION_MAKER_STATUS, FieldParameterDto.SearchType.IN, parameterList);
//                }
            } else if (!AppConstant.ZERO.equals(status)) {
                if ("6".equals(assignUserType) || "12".equals(assignUserType) || "5".equals(assignUserType) || "11".equals(assignUserType) || "4".equals(assignUserType) || "3".equals(assignUserType)) {
                    this.addFieldParameter("t1.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
                } else {
                    this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
                }
            }

            if (!AppConstant.STRING_EMPTY.equals(finalizedStatus)) {
                if ("6".equals(assignUserType) || "12".equals(assignUserType) || "5".equals(assignUserType) || "11".equals(assignUserType) || "4".equals(assignUserType) || "3".equals(assignUserType)) {
                    this.addFieldParameter("t1.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
                } else {
                    this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
                }
            }


            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }

            if (liablityUser) {
                this.addFieldParameter("t2.V_INIT_LIABILITY_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (2 == type && decisionMakerUser) {
                this.addFieldParameter("t2.V_DECISION_MAKING_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            } else if (claimUser) {
                this.addFieldParameter("t2.V_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }


            boolean isSearch = request.getParameter("isSearch") == null
                    ? false
                    : (request.getParameter("isSearch").equalsIgnoreCase("1") ? true : false);

            request.getSession().setAttribute(AppConstant.ASSIGN_USER_TYPE, assignUserType);
            DataGridDto data = claimUserUpdateService.getClaimHandlerDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, assignUserType, assignUserName, isSearch, isRejectedClaim, calsheetStatus, assignUserLevel, sessionUser);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private String getAssignDateColumName(String assignUserType) {
        String orderColumnName = "";
        switch (assignUserType) {
            case "1":
            case "9":
                orderColumnName = "t2.D_ASSIGN_DATE_TIME";
                break;
            case "2":
                orderColumnName = "D_DECISION_MAKING_ASSIGN_DATE_TIME";
                break;
            case "3":
            case "4":
                orderColumnName = "t2.D_INPUT_DATETIME";
                break;
            case "5":
            case "11":
                orderColumnName = "t2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME";
                break;
            case "6":
            case "12":
                orderColumnName = "t2.D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME";
                break;
            case "7":
            case "10":
                orderColumnName = "V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                break;
            case "8":
                orderColumnName = "D_ASSIGN_DATE_TIME";
                break;
            case "13":
                orderColumnName = "t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME";
                break;
            case "14":
                orderColumnName = "t2.D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME";
                break;
            case "15":
                orderColumnName = "t2.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME";
                break;
        }
        return orderColumnName;
    }

    private void searchClaimHandlerUser(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        ClaimHandlerDto claimHandlerDto;
        try {
            setClaimPanelUserPopupListValues(request);
            Integer claimNoToSearch = request.getParameter("txnId") == null ? 0 : Integer.parseInt(request.getParameter("txnId"));
            claimHandlerDto = claimUserUpdateService.searchUsingTxnId(claimNoToSearch);
            json = gson.toJson(claimHandlerDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updateClaimHandlerUser(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        String message;
        boolean successUpdate;
        UserDto user = getSessionUser(request);
        try {
            setClaimPanelUserPopupListValues(request);
            Integer txnId = request.getParameter("txnId") == null ? 0 : Integer.parseInt(request.getParameter("txnId"));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            String assignUser = request.getParameter("assignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");
            String liabilityAssignUser = request.getParameter("liabilityAssignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("liabilityAssignUser");
            String assignUserType = request.getParameter("assignUserType") == null ? AppConstant.STRING_EMPTY : request.getParameter("assignUserType");
            String alreadyAssignUser = request.getParameter("alreadyAssignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("alreadyAssignUser");
            Integer level = null == request.getParameter("level") || request.getParameter("level").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("level"));
            Integer status = null == request.getParameter("status") || request.getParameter("status").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("status"));

            successUpdate = claimUserUpdateService.updateClaimnUser(txnId, assignUser, liabilityAssignUser, user.getUserId(), claimNo, assignUserType, alreadyAssignUser, level, status);
            if (successUpdate) {
                message = "1";
            } else {
                message = "2";
            }
            json = gson.toJson(message);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

}
