package com.misyn.mcms.claim.controller.admin;

import com.google.gson.Gson;
import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.misyn.mcms.admin.admin.dto.AccessUserTypeDto;
import com.misyn.mcms.admin.admin.dto.UserTypeDto;
import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.admin.admin.service.impl.UserManagementServiceImpl;
import com.misyn.mcms.claim.controller.ClaimUserLeaveController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.AssessorAllocationService;
import com.misyn.mcms.claim.service.UserService;
import com.misyn.mcms.claim.service.impl.AssessorAllocationServiceImpl;
import com.misyn.mcms.claim.service.impl.UserServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "ClaimUserLeaveController", urlPatterns = "/ClaimUserLeaveController/*")
public class UserController extends HttpServlet {
    private UserManagementService userManagementService = new UserManagementServiceImpl();
    private AssessorAllocationService assessorAllocationService = new AssessorAllocationServiceImpl();
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveController.class);
    private int draw = 1;
    private UserService userService = null;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void addFieldParameter(String dbFieldName, String value, FieldParameterDto.SearchType searchType, List<FieldParameterDto> parameterList) {
        if (!AppConstant.STRING_EMPTY.equals(value)) {
            FieldParameterDto fieldParameterDTO = new FieldParameterDto();
            fieldParameterDTO.setDbFieldName(dbFieldName);
            fieldParameterDTO.setFieldValue(value);
            fieldParameterDTO.setStringType(!searchType.equals(FieldParameterDto.SearchType.NOT_IN) && !searchType.equals(FieldParameterDto.SearchType.IN));
            fieldParameterDTO.setSearchType(searchType);
            parameterList.add(fieldParameterDTO);
        }
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        userService = new UserServiceImpl();
        try {
            switch (pathInfo) {
                case "/save":
                    saveClaimUser(request, response);
                    break;
                case "/update":
                    updateClaimUser(request, response);
                    break;
                case "/getAll":
                    searchClaimUsers(request, response);
                    break;
                case "/load":
                    loadCreateUserScreen(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchClaimUsers(HttpServletRequest request, HttpServletResponse response) {
        Integer type = null == request.getParameter("type") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("type"));
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        String userRole = request.getParameter("userRole") == null ? AppConstant.STRING_EMPTY : request.getParameter("userRole");
        String userStatus = request.getParameter("userStatus") == null ? AppConstant.STRING_EMPTY : request.getParameter("userStatus");
        String userFirstName = request.getParameter("userFirstName") == null ? AppConstant.STRING_EMPTY : request.getParameter("userFirstName");
        String userMobile = request.getParameter("userMobile") == null ? AppConstant.STRING_EMPTY : request.getParameter("userMobile");
        String userID = request.getParameter(AppConstant.TXT_USER_ID) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_USER_ID);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("v_usrid", userID, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("n_accessusrtype", userRole.equals(AppConstant.ZERO)  ? AppConstant.STRING_EMPTY : userRole, FieldParameterDto.SearchType.Equal, parameterList);
            this.addFieldParameter("v_firstname", userFirstName, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("v_mobile", userMobile, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("v_usrstatus", userStatus, FieldParameterDto.SearchType.Equal, parameterList);

            switch (orderColumnName) {
                case "userStatus":
                    orderColumnName = "v_usrstatus";
                    break;
                case "firstName":
                    orderColumnName = "v_firstname";
                    break;
                case "lastName":
                    orderColumnName = "v_lastname";
                    break;
                case "accessUserTypeDesc":
                    orderColumnName = "a.v_accessusrtype";
                    break;
                case "userId":
                    orderColumnName = "v_usrid";
                    break;
                case "userCode":
                    orderColumnName = "n_usrcode";
                    break;
            }

            DataGridDto data = userService.getUserDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, type);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void updateClaimUser(HttpServletRequest request, HttpServletResponse response){
        // Read the JSON body from the request
        StringBuilder jsonBuffer = new StringBuilder();
        String line;
        try {
        BufferedReader reader = request.getReader();

        while ((line = reader.readLine()) != null) {
            jsonBuffer.append(line);
        }

        // Convert JSON string to JSONObject
        String jsonString = jsonBuffer.toString();
        JSONObject jsonObject = new JSONObject(jsonString);

        // Extract values from the JSON object
        String company = jsonObject.optString("company");
        String loginName = jsonObject.optString("loginName");
        String department = jsonObject.optString("department");
        String userRole = jsonObject.optString("userRole");
        String password = jsonObject.optString("password");
        String confirmPassword = jsonObject.optString("confirmPassword");
        String userStatus = jsonObject.optString("userStatus");
        String title = jsonObject.optString("title");

        String reserveLimitOnly = jsonObject.optString("reserveLimitOnly", null);
        String teamId = jsonObject.optString("teamId", null);
        String liabilityLimit = jsonObject.optString("liabilityLimit", null);
        String paymentLimit = jsonObject.optString("paymentLimit", null);
        String paymentAuthLimit = jsonObject.optString("paymentAuthLimit", null);
        String reserveLimit = jsonObject.optString("reserveLimit", null);
        String reportingTo = jsonObject.optString("reportingTo", null);
        String assessorCode = jsonObject.optString("assessorCode", null);
        String assessorType = jsonObject.optString("assessorType", null);
        String assignDistrict = jsonObject.optString("assignDistrict", null);
        String assessorReportingTo = jsonObject.optString("assessorReportingTo", null);

        // Do your DB update logic here

        // Send response
        response.setContentType("application/json");
        response.getWriter().write("{\"status\":\"success\"}");
    } catch (Exception e) {
        throw new RuntimeException(e);
    }
    }


    private void saveClaimUser(HttpServletRequest request, HttpServletResponse response) {
        // Read the JSON body from the request
        StringBuilder jsonBuffer = new StringBuilder();
        String line;
        BufferedReader reader = null;
        UserMasterDto userMasterDto = new UserMasterDto();
        try {
            reader = request.getReader();


            while ((line = reader.readLine()) != null) {
                jsonBuffer.append(line);
            }

            // Convert JSON string to JSONObject
            String jsonString = jsonBuffer.toString();
            JSONObject jsonObject = new JSONObject(jsonString);

            // Extract values from the JSON object
            userMasterDto.setCompanyCode(jsonObject.optString("company"));
            userMasterDto.setUserId(jsonObject.optString("loginName"));
            userMasterDto.setAccessUserType(jsonObject.optInt("department"));
            JSONArray userRolesArray = jsonObject.optJSONArray("userRole");
            if (userRolesArray != null) {
                List<String> roles = new ArrayList<>();
                for (int i = 0; i < userRolesArray.length(); i++) {
                    roles.add(userRolesArray.getString(i));
                }
                String rolesCommaSeparated = String.join(",", roles);
                userMasterDto.setUserTypes(rolesCommaSeparated);
            }
            userMasterDto.setPassword(jsonObject.optString("password"));
            userMasterDto.setUserStatus(jsonObject.optString("userStatus"));
            userMasterDto.setTitle(jsonObject.optString("title"));

            userMasterDto.setReserveLimit(jsonObject.optDouble("reserveLimitOnly", 0)) ;
            userMasterDto.setTeamId(jsonObject.optInt("teamId", 0)); ;
            userMasterDto.setLiabilityLimit(jsonObject.optDouble("liabilityLimit", 0)) ;
            userMasterDto.setPaymentLimit(jsonObject.optDouble("paymentLimit", 0)); ;
            userMasterDto.setPaymentAuthLimit(jsonObject.optDouble("paymentAuthLimit", 0));;
            userMasterDto.setReserveLimit(jsonObject.optDouble("reserveLimit", 0)); ;
            userMasterDto.setReportingTo(jsonObject.optString("reportingTo", null)); ;
            userMasterDto.setPaymentLimit(jsonObject.optDouble("paymentLimit", 0));
            userMasterDto.setPaymentAuthLimit(jsonObject.optDouble("paymentAuthLimit", 0));
            userMasterDto.setReserveLimit(jsonObject.optDouble("reserveLimit", 0)); // Assuming you have a string setter too
            userMasterDto.setReportingTo(jsonObject.optString("reportingTo", null));
//            userMasterDto.setAsS(jsonObject.optString("assessorCode", null));
            userMasterDto.setAssessorType(jsonObject.optString("assessorType", null));
            userMasterDto.setDistrictCode(jsonObject.optString("assignDistrict", null));
            userMasterDto.setReportingToName(jsonObject.optString("assessorReportingTo", null));

            userService.saveClaimUser(userMasterDto);

            // Send response
            response.setContentType("application/json");
            response.getWriter().write("{\"status\":\"success\"}");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void loadCreateUserScreen(HttpServletRequest request, HttpServletResponse response) {
        Integer teamId = null == request.getParameter("teamId") || request.getParameter("teamId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("teamId"));
        try {
            List<AccessUserTypeDto> accessUserTypeList = userManagementService.getAccessUserTypeList();
            List<UserTypeDto> userTypeDtos = userManagementService.getUserTypeList();
            List<DistrictDto> distictList = userManagementService.getDistictList();
            List<UserDto> rteList = assessorAllocationService.getRTEList();
            request.setAttribute("accessUserTypeListMain", accessUserTypeList);
            request.setAttribute("userTypeListMain", userTypeDtos);
            request.setAttribute("districtListMain", distictList);
            request.setAttribute("rteListMain", rteList);
            requestDispatcher(request, response, "/admin/user_claim/userAll.jsp");

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected void requestDispatcher(HttpServletRequest request, HttpServletResponse response, String url) {
        try {
            RequestDispatcher rd = request.getRequestDispatcher(url);
            rd.forward(request, response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
