package com.misyn.mcms.claim.controller.callcenter;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.controller.callcenter.validator.CallCenterValidator;
import com.misyn.mcms.claim.controller.callcenter.validator.ThirdPartyValidator;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.service.CallCenterService;
import com.misyn.mcms.claim.service.impl.CallCenterServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@WebServlet(name = "ClaimController", urlPatterns = "/Claim/*")
public class ClaimController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimController.class);
    CallCenterValidator callCenterValidator = new CallCenterValidator();
    ThirdPartyValidator thirdPartyValidator = new ThirdPartyValidator();

    CallCenterService callCenterService = new CallCenterServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();

        try {
            switch (pathInfo) {
                case "/validate":
//                    callCenterValidator.validateCallCenter(request, response);
                    break;
                case "/save":
                    saveClaim(request, response);
                    break;
                case "/thirdPartySave":
                    saveThirdParty(request, response);
                    break;
                case "/thirdPartyRemove":
                    removeFromThirdParty(request, response);
                    break;
                case "/thirdPartyUpdate":
                    updateThirdParty(request, response);
                    break;
                case "/followUpCallUpdate":
                    setFollowUpcallDetails(request, response);
                    break;
                case "/addRemarks":
                    addSpeacialRemarks(request, response);
                    break;
                case "/thirdPartyVehicle":
                    thirdPartyVehicleCheck(request, response);
                    break;
                case "/updateDoubtClaim":
                    updateDoubtClaim(request, response);
                    break;
                case "/driverUpdate":
                    updateDriverDetails(request, response);
                    break;
                case "/getPolicyValidity":
                    checkPolicyValidity(request, response);
                    break;
                case "/mainPanelView":
                    mainPanelView(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void mainPanelView(HttpServletRequest request, HttpServletResponse response) {
        try {
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/mainPanelView.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void checkPolicyValidity(HttpServletRequest request, HttpServletResponse response) {
        String policyNo = null == request.getParameter("V_POL_NO") ? AppConstant.STRING_EMPTY : request.getParameter("V_POL_NO");
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            PolicyWarningMessageDto messageDto = callCenterService.checkPolicyValidity(policyNo);
            json = gson.toJson(messageDto);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void updateDoubtClaim(HttpServletRequest request, HttpServletResponse response) {
        String json = "Fail";
        Gson gson = new Gson();
        boolean updated;
        UserDto user = getSessionUser(request);
        SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
        try {
            Integer jobRefNo = Integer.valueOf(request.getParameter("jobRefNo"));
            String departmentId = request.getParameter("departmentId");
            String sectionName = request.getParameter("sectionName");
            String status = request.getParameter("status");
            String remark = request.getParameter("remark");
            Integer claimNo = Integer.valueOf(request.getParameter("claimNo"));
            setSpecialRemark(specialRemarkDto, claimNo, remark, sectionName, departmentId, user);
            updated = callCenterService.updateDoubtClaim(status, remark, claimNo, jobRefNo, user, specialRemarkDto);
            if (updated) {
                json = "SUCCESS";
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            json = gson.toJson(json);
            printWriter(request, response, json);
        }
    }

    private void setSpecialRemark(SpecialRemarkDto specialRemarkDto, Integer claimNo, String remark, String sectionName, String departmentId, UserDto user) {
        specialRemarkDto.setClaimNo(claimNo);
        specialRemarkDto.setRemark(remark);
        specialRemarkDto.setSectionName(sectionName);
        specialRemarkDto.setDepartmentId(Integer.parseInt(departmentId));
        specialRemarkDto.setInputUserId(user.getUserId());
        specialRemarkDto.setInputDateTime(Utility.sysDateTime());
    }

    private void addSpeacialRemarks(HttpServletRequest request, HttpServletResponse response) {
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        ClaimsDto sessionClaimDetails = getSessionClaimDetails(request, response);
        UserDto sessionUser = getSessionUser(request);
        try {
            if (sessionClaimDetails.getClaimNo() > 0) {
                String departmentId = request.getParameter("departmentId");
                String sectionName = request.getParameter("sectionName");
                String remark = request.getParameter("remark");
                SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
                specialRemarkDto.setClaimNo(sessionClaimDetails.getClaimNo());
                specialRemarkDto.setRemark(remark);
                specialRemarkDto.setSectionName(sectionName);
                specialRemarkDto.setDepartmentId(Integer.parseInt(departmentId));
                errorMessageDto = callCenterService.saveSpecialRemark(specialRemarkDto, sessionUser);
                sessionClaimDetails.getRemarkList().add(0, specialRemarkDto);
                updateSessionClaimDetails(request, response, sessionClaimDetails);
            } else {
                errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                errorMessageDto.setMessage("Claim not initialized");
            }


        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage("Remark add failed");
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
        returnJson(errorMessageDto, response);
    }


    private void updateThirdParty(HttpServletRequest request, HttpServletResponse response) {
        ThirdPartyDto thirdPartyDto = new ThirdPartyDto();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        Integer index = Integer.parseInt(request.getParameter("index"));
        try {
            BeanUtils.populate(thirdPartyDto, request.getParameterMap());
            errorMessageDto = thirdPartyValidator.validateCallCenter(thirdPartyDto);
            if (errorMessageDto.getErrorCode().equals(AppConstant.NO_ERRORS_CODE)) {
                ClaimsDto sessionClaimDetails = getSessionClaimDetails(request, response);
                Map<Integer, ThirdPartyDto> thirdPartyDtoMap = sessionClaimDetails.getThirdPartyDtoMap();
                thirdPartyDtoMap.put(index, thirdPartyDto);
                errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                errorMessageDto.setMessage("Update Success!");
            }
        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
        returnJson(errorMessageDto, response);
    }

    private void removeFromThirdParty(HttpServletRequest request, HttpServletResponse response) {
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        Integer index = Integer.parseInt(request.getParameter("index"));
        try {
            ClaimsDto sessionClaimDetails = getSessionClaimDetails(request, response);
            Map<Integer, ThirdPartyDto> thirdPartyDtoMap = sessionClaimDetails.getThirdPartyDtoMap();
            thirdPartyDtoMap.remove(index);
            errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
            errorMessageDto.setMessage("Remove Success!");
        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
        returnJson(errorMessageDto, response);
    }

    private void saveThirdParty(HttpServletRequest request, HttpServletResponse response) {
        ThirdPartyDto thirdPartyDto = new ThirdPartyDto();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        String index = request.getParameter("index");
        try {
            BeanUtils.populate(thirdPartyDto, request.getParameterMap());
            errorMessageDto = thirdPartyValidator.validateCallCenter(thirdPartyDto);
            if (errorMessageDto.getErrorCode().equals(AppConstant.NO_ERRORS_CODE)) {
                ClaimsDto sessionClaimDetails = getSessionClaimDetails(request, response);
                Map<Integer, ThirdPartyDto> thirdPartyDtoMap = sessionClaimDetails.getThirdPartyDtoMap();
                if (index == null || index.isEmpty())
                    thirdPartyDtoMap.put(thirdPartyDtoMap.size() + 1, thirdPartyDto);
                else
                    thirdPartyDtoMap.put(Integer.parseInt(index), thirdPartyDto);
                sessionClaimDetails.setThirdPartyDtoMap(thirdPartyDtoMap);
                //  updateSessionClaimDetails(request,response,sessionClaimDetails);
            }
            returnJson(errorMessageDto, response);
        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void setFollowUpcallDetails(HttpServletRequest request, HttpServletResponse response) {
        String type = (String) request.getParameter("type");
        ClaimsDto claimsDto = getSessionClaimDetails(request, response);
        UserDto sessionUser = getSessionUser(request);
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(claimsDto, request.getParameterMap());
            claimsDto.setFollowCallUserId(sessionUser.getUserId());
            claimsDto.setFollowCallDoneDateTime(Utility.sysDateTime());

            if (null == claimsDto.getFollowCallContactPersonName() || claimsDto.getFollowCallContactPersonName().isEmpty()) {
                errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                errorMessageDto.setMessage("Call user can not be empty");
                errorMessageDto.setDtoFieldName("name");
            } else if (null == claimsDto.getFollowCallContactPersonTitle() || claimsDto.getFollowCallContactPersonTitle() == 0) {
                errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                errorMessageDto.setMessage("Call user can not be empty");
                errorMessageDto.setDtoFieldName("title");
            } else if (null == claimsDto.getFollowCallContactNumber() || claimsDto.getFollowCallContactNumber().isEmpty()) {
                errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                errorMessageDto.setMessage("Contact No can not be empty");
                errorMessageDto.setDtoFieldName("contactNo");
            } else {
                Integer res = callCenterService.updateFollowUpCallInfo(claimsDto, sessionUser);
                if (res > 0) {
                    errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                    errorMessageDto.setMessage("Successfully Saved");
                }
            }
        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage("Save failed, Error Occurred!");
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }

        returnJson(errorMessageDto, response);
    }

    private ClaimsDto getSanitizedClaimsDto(ClaimsDto claimsDto) {
        List<String> fieldsToSanitize = Arrays.asList(
                "placeOfAccid",
                "dlNo",
                "driverNic",
                "driverName",
                "accidDesc",
                "currentLocation",
                "reporterRemark",
                "remark",
                "damageRemark",
                "firstStatementRemark",
                "ncbRemark",
                "hugeRemark",
                "empRemark",
                "isDoubtRemark",
                "draftRemark",
                "totLostRemark",
                "polRptWaveOffRemark",
                "ncbCusCallBackRemark",
                "liableRemark",
                "notLiableRemark"
        );

        fieldsToSanitize.stream()
                .map(fieldName -> getFieldByName(claimsDto, fieldName))
                .filter(field -> field != null && String.class.isAssignableFrom(field.getType()))
                .forEach(field -> {
                    try {
                        field.setAccessible(true);
                        String fieldValue = (String) field.get(claimsDto);
                        if (fieldValue != null) {
                            String sanitizedValue = fieldValue.replaceAll("[<>&'\\\\\"/]", "-");
                            field.set(claimsDto, sanitizedValue);
                        }
                    } catch (IllegalAccessException e) {
                        LOGGER.error(e.getMessage());
                    }
                });

        return claimsDto;
    }

    // Helper method to get the field by name from the ClaimsDto class
    private Field getFieldByName(ClaimsDto claimsDto, String fieldName) {
        try {
            return claimsDto.getClass().getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            LOGGER.error(e.getMessage());
            return null;
        }
    }

    private void saveClaim(HttpServletRequest request, HttpServletResponse response) {
        String type = (String) request.getParameter("type");
        String isOnsiteReview = request.getParameter("isOnsiteReview");

        // Optional: convert to boolean if needed
        boolean onsiteReview = "Y".equalsIgnoreCase(isOnsiteReview);
        ClaimsDto claimsDtoPre = getSessionClaimDetails(request, response);
        UserDto sessionUser = getSessionUser(request);
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        ClaimsDto savedClaim = new ClaimsDto();
        try {
            BeanUtils.populate(claimsDtoPre, request.getParameterMap());
            ClaimsDto claimsDto = getSanitizedClaimsDto(claimsDtoPre);
            claimsDto.setAccidDate(Utility.getCustomDateFormat(claimsDto.getAccidDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT, AppConstant.DATE_FORMAT));
            claimsDto.setAccidTime(Utility.getCustomDateFormat(claimsDto.getAccidDateTime(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT, AppConstant.TIME_FORMAT));
            claimsDto.setDateOfReport(Utility.getCustomDateFormat(claimsDto.getDateTimeOfReport(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT, AppConstant.DATE_FORMAT));
            claimsDto.setTimeOfReport(Utility.getCustomDateFormat(claimsDto.getDateTimeOfReport(), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT, AppConstant.TIME_FORMAT));
            if (checkDriverDetailSubmission(claimsDto)) {
                claimsDto.setDriverDetailSubmit(AppConstant.YES);
            }
            if (!type.equals(AppConstant.STATUS_CLAIM_DRAFT) && !type.equals(AppConstant.STATUS_CLAIM_REJECT)) {
                errorMessageDto = callCenterValidator.validateCallCenter(claimsDto);
            } else {
                if ((null == claimsDto.getDraftReason() || claimsDto.getDraftReason().equals(0)) && !type.equals(AppConstant.STATUS_CLAIM_REJECT)) {
                    errorMessageDto.setErrorCode(520);
                    errorMessageDto.setMessage("Drafts reason can not be empty");
                    errorMessageDto.setDtoFieldName("draftRemark");
                    errorMessageDto.setFormFieldName("Draft Reason :");
                }
            }
            if (errorMessageDto.getErrorCode().equals(AppConstant.NO_ERRORS_CODE)) {
                if (!claimsDto.getIsNoDamage().equals(AppConstant.YES)
                        && !claimsDto.getIsHugeDamage().equals(AppConstant.YES)
                        && !claimsDto.getDamageNotGiven().equals(AppConstant.YES)) {
                    this.setDamageBodyParts(request, claimsDto);
                }
                savedClaim = callCenterService.saveClaim(claimsDto, sessionUser, type);
            }
            if (null != savedClaim.getClaimNo() && 0 < savedClaim.getClaimNo()) {
                errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                errorMessageDto.setMessage("Success!");
                errorMessageDto.setDtoFieldName(savedClaim.getClaimNo().toString());
            }
            returnJson(errorMessageDto, response);
        } catch (UserNotFoundException e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage("User not found to assign claim");
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage("failed, Error Occurred!");
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void setDamageBodyParts(HttpServletRequest request, ClaimsDto claimsDto) {
        //Start Insert Damage Body Parts
        Integer cnt = 0;
        try {
            cnt = Integer.parseInt(request.getParameter("txtDamagePartCount"));
        } catch (Exception e) {
        }
        List<DamageBodyPartDto> damageBodyPartsList = claimsDto.getDamageBodyPartDtoList();
        damageBodyPartsList.clear();
        for (int x = 1; x <= cnt; x++) {
            DamageBodyPartDto damageBodyPart = new DamageBodyPartDto();
            damageBodyPart.setClaimNo(claimsDto.getClaimNo());
            damageBodyPart.setVehClsId(claimsDto.getVehClsId());
            damageBodyPart.setPartCode(request.getParameter("txtDamageParts" + x));
            damageBodyPart.setDamegType(request.getParameter("rdoDamageParts" + x));
            damageBodyPart.setOtherText(request.getParameter("txtOther" + x));

            damageBodyPart.setBodyPartsCheck(request.getParameter("chkDamageParts" + x) != null ? true : false);

            damageBodyPartsList.add(damageBodyPart);
        }
        //End Insert Damage Body Parts
    }

    private void thirdPartyVehicleCheck(HttpServletRequest request, HttpServletResponse response) {
        try {
            String json;
            Gson gson = new Gson();
            String date = null == request.getParameter("accidentDate") ? "" : request.getParameter("accidentDate");
            ClaimsDto sessionClaimDetails = getSessionClaimDetails(request, response);
            boolean isHave = false;

            if (null != sessionClaimDetails) {
                if (null != sessionClaimDetails.getPolicyDto().getVehicleNumber()
                        && !sessionClaimDetails.getPolicyDto().getVehicleNumber().trim().isEmpty()) {
                    isHave = callCenterService.getClaimNoByVehicleNoAndAccidentDate(sessionClaimDetails.getPolicyDto().getVehicleNumber(), date);
                }
            }
            json = gson.toJson(isHave);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updateDriverDetails(HttpServletRequest request, HttpServletResponse response) {
        ClaimsDto claimsDto = new ClaimsDto();
        claimsDto.setClaimNo(null == request.getParameter("P_N_CLIM_NO") ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO")));
        claimsDto.setDriverStatus(null == request.getParameter("driverStatus") ? 0 : Integer.parseInt(request.getParameter("driverStatus")));
        claimsDto.setDriverTitle(null == request.getParameter("driverTitle") ? 0 : Integer.parseInt(request.getParameter("driverTitle")));
        claimsDto.setDriverName(request.getParameter("driverName"));
        claimsDto.setDriverReleshipInsurd(null == request.getParameter("driverReleshipInsurd") ? 0 : Integer.parseInt(request.getParameter("driverReleshipInsurd")));
        claimsDto.setDriverNic(request.getParameter("driverNic"));
        claimsDto.setDlNo(request.getParameter("dlNo"));
        claimsDto.setDriverLicenceType(request.getParameter("driverLicenceType"));
        claimsDto.setReporterRemark(request.getParameter("reporterRemark"));
        claimsDto.setDriverDetailNotRelevant(null == request.getParameter("driverDetailNotRelevant") ? AppConstant.NO : request.getParameter("driverDetailNotRelevant"));
        UserDto sessionUser = getSessionUser(request);

        if (checkDriverDetailSubmission(claimsDto)) {
            claimsDto.setDriverDetailSubmit(AppConstant.YES);
        }

        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            boolean update = callCenterService.updateDriverDetails(claimsDto, sessionUser);
            if (update) {
                errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                errorMessageDto.setMessage("Success!");
                returnJson(errorMessageDto, response);
            } else {
                errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                errorMessageDto.setMessage("Failed to Update Driver Details!");
                returnJson(errorMessageDto, response);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage("failed, Error Occurred!");
            returnJson(errorMessageDto, response);
        }
    }

    private boolean checkDriverDetailSubmission(ClaimsDto claimsDto) {
        if (AppConstant.YES.equals(claimsDto.getDriverDetailNotRelevant())) {
            return true;
        } else {
            return claimsDto.getDriverStatus() != 0
                    && claimsDto.getDriverTitle() != 0
                    && (!claimsDto.getDriverName().isEmpty() && null != claimsDto.getDriverName())
                    && claimsDto.getDriverReleshipInsurd() != 0
                    && (!claimsDto.getDriverNic().isEmpty() && null != claimsDto.getDriverNic())
                    && (!claimsDto.getDlNo().isEmpty() && null != claimsDto.getDlNo());
        }
    }
}
