package com.misyn.mcms.claim.controller.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.PolicyCoverRequestDto;
import com.misyn.mcms.claim.dto.PolicyDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.dto.list.PolicyCategoryDataList;
import com.misyn.mcms.claim.service.PolicyDetailsService;
import com.misyn.mcms.claim.service.impl.PolicyDetailsServiceImpl;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet(name = "PolicyDetailsController", urlPatterns = "/PolicyDetailsController/*")
public class PolicyDetailsController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PolicyDetailsController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final PolicyDetailsService policyDetailsService = new PolicyDetailsServiceImpl();
    private final int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();

        UserDto user = getSessionUser(request);
        try {
            if (pathInfo.equals("/policyDataList")) {
                getPolicyDataList(request, response, user);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getPolicyDataList(HttpServletRequest request, HttpServletResponse response, UserDto user) {

        try {
            BufferedReader reader = request.getReader();
            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuilder.append(line);
            }
            Gson gson = new Gson();
            String json;

            PolicyCoverRequestDto searchRequest = objectMapper.readValue(jsonBuilder.toString(), PolicyCoverRequestDto.class);

            String policyNumber = searchRequest.getPolNumber();
            String policyChannelType = searchRequest.getPolChannelType();
            int renCount = searchRequest.getRenCount();
            int endCount = searchRequest.getEndCount();
            PolicyDto policyDto = new PolicyDto();
            policyDto.setPolicyNumber(policyNumber);
            policyDto.setPolicyChannelType(policyChannelType);
            policyDto.setEndCount(endCount);
            policyDto.setRenCount(renCount);

            UserDto sessionUser = getSessionUser(request);
            if (policyNumber != null && policyChannelType != null && sessionUser != null && !policyNumber.isEmpty() && !policyChannelType.isEmpty()) {
                PolicyCategoryDataList policyCategoryDataList = policyDetailsService.getPolicyCategoryDataList(policyDto, policyChannelType);
                if (policyCategoryDataList != null) {
                    json = gson.toJson(policyCategoryDataList);
                    response.setStatus(200);
                } else {
                    json = gson.toJson("Server Side Error");
                    response.setStatus(500);
                }
            } else {
                json = gson.toJson("Bad Request");
                response.setStatus(400);
            }

            response.setContentType("application/json");
            PrintWriter out = response.getWriter();
            out.print(json);
            out.flush();

        } catch (IOException e) {
            LOGGER.error("Error occurred while processing the addPolicy request." + e.getMessage());
        }

    }

}
