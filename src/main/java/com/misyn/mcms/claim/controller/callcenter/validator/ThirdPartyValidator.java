package com.misyn.mcms.claim.controller.callcenter.validator;

import com.misyn.mcms.claim.dto.ErrorMessageDto;
import com.misyn.mcms.claim.dto.ThirdPartyDto;

public class ThirdPartyValidator implements Validator<ThirdPartyDto> {

    public ErrorMessageDto validateCallCenter(ThirdPartyDto thirdPartyDto) {
        DoValidate<ThirdPartyDto> doValidate = new DoValidate<>();
        ErrorMessageDto errorMessageDto = doValidate.validate(thirdPartyDto, 3);

        if (errorMessageDto.getErrorCode() == 200) {
            if (1 == thirdPartyDto.getItemType()) {
                if (null == thirdPartyDto.getVehicleNo() || thirdPartyDto.getVehicleNo().isEmpty()) {
                    errorMessageDto.setErrorCode(520);
                    errorMessageDto.setMessage("Vehicle no can not be empty");
                    errorMessageDto.setDtoFieldName("vehicleNo");
                    errorMessageDto.setFormFieldName("Vehicle Number");
                }
            }
        }
        return errorMessageDto;
    }

    @Override
    public void validate(ThirdPartyDto thirdPartyDto) {

    }
}
