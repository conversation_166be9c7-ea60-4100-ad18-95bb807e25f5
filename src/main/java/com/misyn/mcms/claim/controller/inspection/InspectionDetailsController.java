package com.misyn.mcms.claim.controller.inspection;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.AssessorFeeServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.ListBoxItem;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "InspectionDetailsController", urlPatterns = "/InspectionDetailsController/*")
public class InspectionDetailsController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(InspectionDetailsController.class);
    private final AssessorFeeService assessorFeeService = new AssessorFeeServiceImpl();
    private int draw = 1;
    private InspectionDetailsService inspectionDetailsService = null;
    private CallCenterService callCenterService = null;
    private AssessorAllocationService assessorAllocationService = null;
    private RequestAriService requestAriService = null;
    private StorageService stDocumentService = null;
    private ClaimHandlerService claimHandlerService = null;
    private AssessorService assessorService = null;
    private MotorEngineerService motorEngineerService = null;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        inspectionDetailsService = getInspectionDetailsBySession(request);
        callCenterService = getCallCenterServiceBySession(request);
        assessorAllocationService = getAssessorAllocationServiceServiceBySession(request);
        requestAriService = getByRequestAri(request);
        stDocumentService = getSftpDocumentService(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        assessorService = getAssessorServiceBySession(request);
        motorEngineerService = getMotorEngineerBySession(request);
        ClaimsDto claimsDto = new ClaimsDto();
        Integer claimId = AppConstant.ZERO_INT;
        String historyRecord = AppConstant.NO;
        List<SpecialRemarkDto> specialRemarkList;

        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

        try {
            switch (pathInfo) {
                case "/jobList":
                    jobList(request, response);
                    break;
                case "/jobView":
                    int type = request.getParameter(AppConstant.SESSION_TYPE) == null || request.getParameter(AppConstant.SESSION_TYPE) == AppConstant.STRING_EMPTY ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    session.setAttribute(AppConstant.SESSION_TYPE, type);
                    if (1 == type) {
                        request.setAttribute("PENDING_INSPECTION_CLAIM_NO", null == request.getParameter(AppConstant.CLAIM_NO) || request.getParameter(AppConstant.CLAIM_NO).isEmpty() ? AppConstant.ZERO_INT : request.getParameter(AppConstant.CLAIM_NO));
                    } else if (2 == type) {
                        List<UserDto> rteList = assessorAllocationService.getRTEList();
                        List<AssessorDto> assessorList = assessorService.getAssessorListByDivisionCode(AppConstant.STRING_EMPTY);
                        List<String> ccAssignedList = assessorAllocationService.getInputUserList();
                        request.setAttribute("rteList", rteList);
                        request.setAttribute("assessorList", assessorList);
                        request.setAttribute("ccAssignedList", ccAssignedList);
                    }
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/assessorAllocationInspectionList.jsp");
                    break;
                case "/viewEdit":
                    session.removeAttribute(AppConstant.RETURN_URL);
                    viewEdit(request, response);
                    break;
                case "/viewClaimHistory":
//                    removeSessionClaimDetails(request, response);
                    claimId = request.getParameter("P_N_CLIM_NO") == null || request.getParameter("P_N_CLIM_NO") == AppConstant.STRING_EMPTY ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    claimsDto = callCenterService.getViewAccidentClaimsDto(claimId);
                    //   claimsDto.setAccidentTimeFileds(Utility.getSeparateTimeString12hours(claimsDto.getAccidTime()));
                    //   claimsDto.setReportAccidentTimeFileds(Utility.getSeparateTimeString12hours(claimsDto.getTimeOfReport()));

                    //   request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_HISTORY);
                    request.setAttribute(AppConstant.HIDE_ALLOCATION, AppConstant.YES);

                    session.removeAttribute(AppConstant.CLAIM_DTO_HISTORY);
                    session.setAttribute(AppConstant.CLAIM_DTO_HISTORY, claimsDto);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;
                case "/saveDetails":
                    saveDetails(request, response);
                    break;
                case "/requestARI":
                    Integer claimNo = getInspectionDetails(request).getAssessorAllocationDto().getClaimsDto().getClaimNo() == null ? 0 : getInspectionDetails(request).getAssessorAllocationDto().getClaimsDto().getClaimNo();
                    List<RequestAriDto> list = requestAriService.searchAll(claimNo);
                    request.setAttribute(AppConstant.REQUESTED_ARI_LIST, list);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/requestARI.jsp");
                    break;
                case "/thirdPartyDetails":
                    InspectionDetailsDto inspectionDetailsDtoTP = (InspectionDetailsDto) request.getSession().getAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION");
                    request.setAttribute("inspectionDetailsDto", inspectionDetailsDtoTP);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/thirdPartyDetails.jsp");
                    break;
                case "/saveAri":
                    saveAri(request, response);
                    break;
                case "/calculateProfessionalFee":
                    calculateProfessionalFee(request, response);
                    break;
                case "/fetchTimeSlotForInspection":
                    fetchTimeSlotForInspection(request, response);
                    break;
                case "/documentUpload":
                    historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
                    claimId = request.getParameter("P_N_CLIM_NO") == null || request.getParameter("P_N_CLIM_NO") == AppConstant.STRING_EMPTY ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    Integer jobRefNo = request.getParameter("JOB_REF_NO") == null || request.getParameter("JOB_REF_NO") == AppConstant.STRING_EMPTY ? 0 : Integer.parseInt(request.getParameter("JOB_REF_NO"));
                    InspectionDetailsDto inspectionDetailsDto = getInspectionDetails(request);
                    boolean approveStatus = false;
                    approveStatus = inspectionDetailsService.isAssessorApproved(jobRefNo);

                    if (historyRecord.equals(AppConstant.YES)) {

                        AssessorAllocationDto search = assessorAllocationService.search(jobRefNo);
                        Integer inspectionId = AppConstant.ZERO_INT;
                        if (null != search && null != search.getInspectionDto()) {
                            inspectionId = search.getInspectionDto().getInspectionId();
                        }

                        List<ClaimUploadViewDto> claimUploadViewDtoList = inspectionDetailsService.getClaimUploadViewDtoList(claimId, jobRefNo, AppConstant.ASSESSOR_DEPARTMENT_ID, inspectionId);
                        request.setAttribute(AppConstant.HISTORY_CLAIM_UPLOADVIEW_DTO_LIST, claimUploadViewDtoList);
                    } else {
                        session.removeAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST);
                        if (inspectionDetailsDto != null) {
                            Integer inspectionTypeId = inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId();
                            List<ClaimUploadViewDto> claimUploadViewDtoList = inspectionDetailsService.getClaimUploadViewDtoList(inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo(), AppConstant.ASSESSOR_DEPARTMENT_ID, inspectionTypeId);
                            session.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
                        }
                    }
                    request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
                    request.setAttribute(AppConstant.APPROVE_STATUS, approveStatus);
                    request.setAttribute(AppConstant.JOB_REF_NO, jobRefNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/documentUpload.jsp");
                    break;
                case "/processThirdPartyDetails":
                    processThirdPartyDetails(request, response);
                    break;
                case "/documentViewer":
                    Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
                    jobRefNo = Integer.parseInt(request.getParameter(AppConstant.JOB_REF_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.JOB_REF_NO));
                    historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
                    List<ClaimDocumentDto> claimDocumentDtoList = inspectionDetailsService.getClaimDocumentDtoList(jobRefNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
                    if (historyRecord.equals(AppConstant.YES)) {
                        request.setAttribute(AppConstant.HISTORY_CLAIM_DOCUMENT_DTO_LIST, claimDocumentDtoList);
                    } else {
                        session.setAttribute(AppConstant.SESSION_CLAIM_DOCUMENT_DTO_LIST, claimDocumentDtoList);
                    }
                    request.setAttribute("refNo", refNo);
                    request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/pdfViewerAll.jsp");
                    break;
                case "/viewEditPrevious":
                    viewEditPrevious(request, response);
                    break;
                case "/imageUpload":
                    historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
                    claimId = request.getParameter("P_N_CLIM_NO") == null || request.getParameter("P_N_CLIM_NO") == AppConstant.STRING_EMPTY ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    jobRefNo = request.getParameter("JOB_REF_NO") == null || request.getParameter("JOB_REF_NO") == AppConstant.STRING_EMPTY ? 0 : Integer.parseInt(request.getParameter("JOB_REF_NO"));
                    inspectionDetailsDto = getInspectionDetails(request);
                    approveStatus = false;
                    approveStatus = inspectionDetailsService.isAssessorApproved(jobRefNo);
                    if (historyRecord.equals(AppConstant.YES)) {

                        List<ClaimImageDto> claimImageDtoList = inspectionDetailsService.getClaimImageDtoList(claimId, jobRefNo);
                        request.setAttribute(AppConstant.HISTORY_CLAIM_IMAGE_DTO_LIST, claimImageDtoList);

                    } else {
                        session.removeAttribute(AppConstant.SESSION_CLAIM_IMAGE_DTO_LIST);
                        if (inspectionDetailsDto != null) {
                            Integer inspectionTypeId = inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId();
                            List<ClaimImageDto> claimImageDtoList = inspectionDetailsService.getClaimImageDtoList(inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo());
                            session.setAttribute(AppConstant.SESSION_CLAIM_IMAGE_DTO_LIST, claimImageDtoList);
                        }
                    }
                    request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
                    request.setAttribute(AppConstant.APPROVE_STATUS, approveStatus);
                    request.setAttribute(AppConstant.JOB_REF_NO, jobRefNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/imageUpload.jsp");
                    break;
                case "/imageViewer":
                    inspectionDetailsDto = getInspectionDetails(request);
                    refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
                    historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
                    jobRefNo = Integer.parseInt(request.getParameter(AppConstant.JOB_REF_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.JOB_REF_NO));
                    if (historyRecord.equals(AppConstant.YES)) {
                        List<ClaimImageDto> claimImageDtoList = inspectionDetailsService.getClaimImageDtoList(inspectionDetailsDto.getClaimNo(), jobRefNo);
                        request.setAttribute(AppConstant.HISTORY_VIEWER_CLAIM_IMAGE_LIST, claimImageDtoList);
                    } else {
                        jobRefNo = inspectionDetailsDto.getRefNo();
                        List<ClaimImageDto> claimImageDtoList = inspectionDetailsService.getClaimImageDtoList(inspectionDetailsDto.getClaimNo(), jobRefNo);
                        session.setAttribute(AppConstant.SESSION_VIEWER_CLAIM_IMAGE_LIST, claimImageDtoList);
                    }
                    request.setAttribute("refNo", refNo);
                    request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/imageViewer.jsp");
                    break;
                case "/deleteImages":
                    deleteImages(request, response);
                    break;
                case "/deleteDocs":
                    docDeltete(request, response);
                    break;
                case "/calculateOnsiteInspectionValues":
                    calculateOnsiteInspectionValues(request, response);
                    break;
                case "/calculateUnderInsPenaltyPerc":
                    calculateUnderInsPenaltyPerc(request, response);
                    break;
                case "/addRemark":
                    getSpecialRemark(request, response);
                    break;
                case "/viewSpecialRemark":
                    Integer claimNos = request.getParameter("CLIM_NO") == null || request.getParameter("CLIM_NO") == AppConstant.STRING_EMPTY ? 0 : Integer.parseInt(request.getParameter("CLIM_NO"));
                    specialRemarkList = inspectionDetailsService.searchRemarksByClaimNoMultipleDepartmentId(claimNos, AppConstant.ALL_DEPARTMENT_ID_WITHOUT_CLAIM_HANDLER);
                    request.setAttribute(AppConstant.REMARK_LIST, specialRemarkList);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/specialRemarks.jsp");
                    break;
                case "/rteReassigningReasons":
                    rteReassignReasons(request, response);
                    break;
                case "/reassignReasons":
                    reassignReasons(request, response);
                case "/assignedByList":
                    assignedByList(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void assignedByList(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            List<String> assignByList = assessorAllocationService.getInputUserList();
            json = gson.toJson(assignByList);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void reassignReasons(HttpServletRequest request, HttpServletResponse response) {
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        List<ListBoxItem> reassignReasons = null;
        try {
            reassignReasons = inspectionDetailsService.getReassignReasons();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(reassignReasons);
            printWriter(request, response, json);
        }
    }


    private void rteReassignReasons(HttpServletRequest request, HttpServletResponse response) {
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        List<ListBoxItem> desktopReassignReasons = null;
        try {
            desktopReassignReasons = inspectionDetailsService.getDesktopReassignReasons();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(desktopReassignReasons);
            printWriter(request, response, json);
        }
    }

    private void jobList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String jobNo = request.getParameter(AppConstant.TXT_JOB_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String assignDateTime = request.getParameter(AppConstant.TXT_ASSIGN_DATE_TIME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSIGN_DATE_TIME);
        String jobStatus = request.getParameter(AppConstant.TXT_JOB_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_STATUS);
        String assessor = null == request.getParameter(AppConstant.TXT_ASSESSOR) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSESSOR);
        String ccAssignedBy = null == request.getParameter(AppConstant.TXT_CC_ASSIGNED_BY) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CC_ASSIGNED_BY);
        String rte = null == request.getParameter(AppConstant.TXT_RTE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_RTE);
        String inspectionType = null == request.getParameter(AppConstant.INSPECTION_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.INSPECTION_TYPE);
        String type = null == request.getParameter("type") ? AppConstant.STRING_EMPTY : request.getParameter("type");
        String empNo = user.getEmployeeNumber().trim();
        String isOnlineInspection = null == request.getParameter(AppConstant.IS_ONLINE_INSPECTION) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.IS_ONLINE_INSPECTION);

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }

            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t3.assign_datetime", assignDateTime, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t4.v_status_desc", jobStatus, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.job_id", jobNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.job_status", "23,4", FieldParameterDto.SearchType.NOT_IN, parameterList);
            this.addFieldParameter("t3.record_status", "33", FieldParameterDto.SearchType.NOT_IN, parameterList);
            if (!"2".equalsIgnoreCase(type)) {
                this.addFieldParameter("t2.inspection_type_id", "8", FieldParameterDto.SearchType.NOT_IN, parameterList);
            }
            if (user.getAccessUserType() == 20) {
                this.addFieldParameter("t3.assessor_code", empNo, FieldParameterDto.SearchType.Equal, parameterList);
                if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(isOnlineInspection)) {
                    this.addFieldParameter("t3.is_online_inspection", "isOnlineInspection", FieldParameterDto.SearchType.Equal, parameterList);
                }

            }
            if (!"0".equals(status)) {
                this.addFieldParameter("t3.record_status", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if ("2".equalsIgnoreCase(type)) {
                this.addFieldParameter("t5.v_assign_rte_user", rte, FieldParameterDto.SearchType.Equal, parameterList);
                this.addFieldParameter("t3.assessor_code", assessor, FieldParameterDto.SearchType.Equal, parameterList);
                this.addFieldParameter("t3.inp_userid", ccAssignedBy, FieldParameterDto.SearchType.Equal, parameterList);
                if (!"0".equalsIgnoreCase(inspectionType)) {
                    this.addFieldParameter("t2.inspection_type_id", inspectionType, FieldParameterDto.SearchType.Equal, parameterList);
                }
            }

            switch (orderColumnName) {
                case "refNo":
                    orderColumnName = " t3.ref_no";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumber":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "statusId":
                    orderColumnName = "t3.record_status";
                    break;
                case "jobNo":
                    orderColumnName = "t3.job_id";
                    break;
                case "inspectionType":
                    orderColumnName = "t2.inspection_type_id";
                    break;
                case "assignDateTime":
                    orderColumnName = "t3.assign_datetime";
                    break;
                case "jobStatus":
                    orderColumnName = "t4.v_status_desc";
                    break;
                case "assignUser":
                    orderColumnName = "assign_assessor";
                    break;
                case "ccAssignedBy":
                    orderColumnName = "t3.inp_userid";
                    break;

            }

            DataGridDto data;

            if ("3".equals(type)) {
                this.addFieldParameter("t6.V_REPORT_TO", user.getUserId(), FieldParameterDto.SearchType.Equal, parameterList);
                data = inspectionDetailsService.getAssessorPendingClaimDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            } else {
                data = inspectionDetailsService.getClaimDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            }

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void viewEdit(HttpServletRequest request, HttpServletResponse response) {
        InspectionDetailsDto inspectionDetailsDto = null;
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            Integer refNo = request.getParameter("P_N_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_REF_NO"));

            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            AssessorAllocationDto assessor = assessorAllocationService.search(refNo);

//            List<ListItemDto> inspectionTimeList = assessorFeeService.getTimeSlotsByInspectionTypeId(assessor.getInspectionDto().getInspectionId());
            List<ListItemDto> dayTypeDtoList = assessorFeeService.getDayTypeList();

            request.setAttribute(AppConstant.DAY_TYPE_LIST, dayTypeDtoList);
//            request.setAttribute(AppConstant.INSPECTION_TIME_LIST, inspectionTimeList);

            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            if (null == inspectionDetailsDto) {
                inspectionDetailsDto = new InspectionDetailsDto();
            }
            inspectionDetailsDto.setAssessorAllocationDto(assessor);

            if (inspectionDetailsDto.getRecordStatus() != 8 && inspectionDetailsDto.getRecordStatus() != 9) {
                if (null != claimHandlerDto && null != claimHandlerDto.getAprvTotAcrAmount() && claimHandlerDto.getAprvTotAcrAmount().compareTo(BigDecimal.ZERO) > 0) {
                    inspectionDetailsDto = inspectionDetailsService.setAcrValue(inspectionDetailsDto, claimHandlerDto.getAprvTotAcrAmount(), assessor);
                } else {
                    inspectionDetailsDto = inspectionDetailsService.setAcrValue(inspectionDetailsDto, assessor);
                }

                if (null != claimHandlerDto && (null != claimHandlerDto.getPenaltyUnderInsurce() || null != claimHandlerDto.getPenaltyBaldTyre())) {
                    inspectionDetailsDto = inspectionDetailsService.setPenaltyValue(inspectionDetailsDto, claimHandlerDto.getPenaltyUnderInsurce(), claimHandlerDto.getPenaltyUnderInsurceRate(),
                            claimHandlerDto.getPenaltyBaldTyre(), claimHandlerDto.getPenaltyBaldTyreRate(), assessor);
                } else {
                    inspectionDetailsDto = inspectionDetailsService.setPenaltyValue(inspectionDetailsDto, assessor);
                }
            }

//            if (null != claimHandlerDto && null != claimHandlerDto.getPe() && claimHandlerDto.getAprvTotAcrAmount().compareTo(BigDecimal.ZERO) > 0) {
//                inspectionDetailsDto=  inspectionDetailsService.setAcrValue(inspectionDetailsDto, claimHandlerDto.getAprvTotAcrAmount(),assessor);
//            } else {
//                inspectionDetailsDto=inspectionDetailsService.setAcrValue(inspectionDetailsDto,assessor);
//            }

            //Check whether Inspection Detail Is saved in previous to this ref no
            RequestAriDto requestAriDto = requestAriService.searchByClaimNo(claimNo);

            if (null != requestAriDto) {
                request.setAttribute(AppConstant.IS_REQUESTED, AppConstant.YES);
            }

            if (null != inspectionDetailsDto && 0 < inspectionDetailsDto.getRefNo()) {
                request.setAttribute("ACTION", "UPDATE");
                inspectionDetailsDto.setClaimNo(claimNo);
                inspectionDetailsDto.setRefNo(inspectionDetailsDto.getRefNo());
            } else {
                request.setAttribute("ACTION", "SAVE");
                //inspectionDetailsDto = new InspectionDetailsDto();
                inspectionDetailsDto.setClaimNo(claimNo);
                inspectionDetailsDto.setRefNo(refNo);
            }

            AssessorAllocationDto assessorAllocationDto;
            //Assessor Allocation Details Load From Assesor Allocation Table
            assessorAllocationDto = assessorAllocationService.search(refNo);

            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            if (null != claimsDto) {
                assessorAllocationDto.setClaimsDto(claimsDto);
            }

            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            inspectionDetailsDto.setInputUserId(getSessionUser(request).getUserId());
            inspectionDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsService.getClaimDocumentTypeDtoList(AppConstant.ASSESSOR_DEPARTMENT_ID, assessorAllocationDto.getInspectionDto().getInspectionId()));

            //Third Party Details Load From Call Center
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtos = callCenterService.getClaimThirdPartyDetailsGeneric(claimNo);
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosAssessor = inspectionDetailsService.getClaimThirdPartyDetailsGeneric(claimNo);

            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : claimThirdPartyDetailsGenericDtosAssessor) {
                if (claimThirdPartyDetailsGenericDto.getCcTpdId() > 0) {
                    claimThirdPartyDetailsGenericDto.setMappingId(claimThirdPartyDetailsGenericDto.getCcTpdId());
                    claimThirdPartyDetailsGenericDto.setMappingType("CALL_CENTER");
                }
            }

            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = new TreeMap<>((String o1, String o2) -> {
                return o2.compareTo(o1);
            });

            List<ClaimThirdPartyDetailsGenericDto> list = new ArrayList<>();
            list.addAll(claimThirdPartyDetailsGenericDtos);
            list.addAll(claimThirdPartyDetailsGenericDtosAssessor);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : list) {
                thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDto.getType() + "_" + claimThirdPartyDetailsGenericDto.getTxnId(), claimThirdPartyDetailsGenericDto);
            }

            inspectionDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);

            List<PreviousClaimsDto> previousClaimList = inspectionDetailsService.getPreviousClaimList(null == claimsDto.getVehicleNo() ? AppConstant.STRING_EMPTY : claimsDto.getVehicleNo(), claimNo);
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, refNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }

            request.setAttribute(AppConstant.REMARK_LIST, inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID));
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.NO);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        if (null != inspectionDetailsDto.getGarageInspectionDetailsDto() && inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
            try {
                if (inspectionDetailsDto.getRecordStatus() != 8 && inspectionDetailsDto.getRecordStatus() != 9) {
                    InspectionDetailsDto inspectionDetailsDtoToCheck = assessorAllocationService.getLatestUpdateOnSite(inspectionDetailsDto);
                }
                request.setAttribute(AppConstant.IS_GARAGEINSPECTION, AppConstant.YES);


            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }

        request.setAttribute(AppConstant.INSPECTION_DETAILS, inspectionDetailsDto);
        request.getSession().setAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION", inspectionDetailsDto);
        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "");
        request.setAttribute(AppConstant.ERROR_MESSAGE, "");
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/assessorAllocationInspectionDetails.jsp");
    }

    private void saveDetails(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        InspectionDetailsDto inspectionDetailsDto = (InspectionDetailsDto) request.getSession().getAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION");
        String action = request.getParameter("ACTION");
        String actionType = request.getParameter("ACTION_TYPE");
        String dispatchUrl;
        try {
            //Configure Bean Utils
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.getConvertUtils().register(false, false, 0);

            beanUtilsBean.populate(inspectionDetailsDto, request.getParameterMap());

            if (inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 5
                    && inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 6
                    && inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 7
                    && inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 9) {
                populateTyreConditionList(inspectionDetailsDto, request.getParameterMap());
            }

            inspectionDetailsDto.setActionType(actionType);
            switch (action) {
                case "SAVE":
                    inspectionDetailsService.insert(inspectionDetailsDto, sessionUser);
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Saved");
                    request.setAttribute("ACTION", "UPDATE");
                    break;
                case "UPDATE":
                    inspectionDetailsService.update(inspectionDetailsDto, sessionUser);
                    request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Updated");
                    request.setAttribute("ACTION", "UPDATE");
                    break;
            }

            if ("SUBMIT".equals(actionType)) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Submitted");
            }
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/assessor/assessorAllocationInspectionList.jsp";
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            switch (action) {
                case "SAVE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
                    request.setAttribute("ACTION", "SAVE");
                    break;
                case "UPDATE":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be updated");
                    request.setAttribute("ACTION", "UPDATE");
                    break;
            }
            if ("SUBMIT".equals(actionType)) {
                request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be submitted");
            }
            dispatchUrl = "/WEB-INF/jsp/claim/inspectiondetails/assessor/assessorAllocationInspectionDetails.jsp";
        }
        request.setAttribute(AppConstant.INSPECTION_DETAILS, inspectionDetailsDto);
        request.getSession().setAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION", inspectionDetailsDto);
        requestDispatcher(request, response, dispatchUrl);
    }

    private void populateTyreConditionList(InspectionDetailsDto inspectionDetailsDto, Map<String, String[]> parameterMap) {
        List<TireCondtionDto> tireCondtionDtoList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            TireCondtionDto tireCondtionDto = new TireCondtionDto();
            tireCondtionDto.setRefNo(inspectionDetailsDto.getRefNo());
            tireCondtionDto.setClaimsDto(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto());
            String rf = parameterMap.get("cot_" + i + "_" + 0)[0];
            String lf = parameterMap.get("cot_" + i + "_" + 1)[0];
            String rr = parameterMap.get("cot_" + i + "_" + 2)[0];
            String rl = parameterMap.get("cot_" + i + "_" + 3)[0];
            String rri = parameterMap.get("cot_" + i + "_" + 4)[0];
            String lri = parameterMap.get("cot_" + i + "_" + 5)[0];
            String other = parameterMap.get("cot_" + i + "_" + 6)[0];
            tireCondtionDto.setPosition(i);
            tireCondtionDto.setRf(rf);
            tireCondtionDto.setLf(lf);
            tireCondtionDto.setRr(rr);
            tireCondtionDto.setRl(rl);
            tireCondtionDto.setRri(rri);
            tireCondtionDto.setLri(lri);
            tireCondtionDto.setOther(other);
            tireCondtionDtoList.add(tireCondtionDto);
        }
        inspectionDetailsDto.setTireCondtionDtoList(tireCondtionDtoList);
    }

    private int getOfferOrLossType(InspectionDetailsDto inspectionDetailsDto, int lossType) {
        switch (inspectionDetailsDto.getInspectionDto().getInspectionId()) {
            case AppConstant.GARAGE_INSPECTION:
                return getGarageOfferType(inspectionDetailsDto);
            case AppConstant.DESKTOP_INSPECTION:
                return getDesktopOfferType(inspectionDetailsDto);
            case AppConstant.ON_SITE_INSPECTION:
            case AppConstant.OFF_SITE_INSPECTION:
                return getOnsiteOfferType(inspectionDetailsDto);
        }
        return lossType;
    }

    private int getGarageOfferType(InspectionDetailsDto inspectionDetailsDto) {
        if (Integer.parseInt(inspectionDetailsDto.getGarageInspectionDetailsDto().getSettlementMethod()) == AppConstant.TOTAL_LOSS_TYPE) {
            return AppConstant.TOTAL_LOSS_TYPE;
        } else if (Integer.parseInt(inspectionDetailsDto.getGarageInspectionDetailsDto().getSettlementMethod()) == AppConstant.GARAGE_OFFER_TYPE) {
            return AppConstant.GARAGE_OFFER_TYPE;
        }
        return AppConstant.PARTIAL_LOSS_TYPE;
    }

    private int getOnsiteOfferType(InspectionDetailsDto inspectionDetailsDto) {
        if (4 == inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
            return AppConstant.TOTAL_LOSS_TYPE;
        } else if (1 == inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
            return AppConstant.ONSITE_OFFER_TYPE;
        }
        return AppConstant.PARTIAL_LOSS_TYPE;
    }

    private int getDesktopOfferType(InspectionDetailsDto inspectionDetailsDto) {
        if (Integer.parseInt(inspectionDetailsDto.getDesktopInspectionDetailsDto().getSettlementMethod()) == AppConstant.TOTAL_LOSS_TYPE) {
            return AppConstant.TOTAL_LOSS_TYPE;
        }
        if (inspectionDetailsDto.getDesktopInspectionDetailsDto().getDesktopOffer() == ConditionType.Yes) {
            return AppConstant.DESKTOP_OFFER_TYPE;
        }
        return AppConstant.PARTIAL_LOSS_TYPE;
    }

    private void populateTyreConditionList(MotorEngineerDetailsDto motorEngineerDetailsDto, Map<String, String[]> parameterMap) {
        List<TireCondtionDto> tireCondtionDtoList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            TireCondtionDto tireCondtionDto = new TireCondtionDto();
            tireCondtionDto.setRefNo(motorEngineerDetailsDto.getRefNo());
            tireCondtionDto.setClaimsDto(motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto());
            String rf = parameterMap.get("cot_" + i + "_" + 0)[0];
            String lf = parameterMap.get("cot_" + i + "_" + 1)[0];
            String rr = parameterMap.get("cot_" + i + "_" + 2)[0];
            String rl = parameterMap.get("cot_" + i + "_" + 3)[0];
            String rri = parameterMap.get("cot_" + i + "_" + 4)[0];
            String lri = parameterMap.get("cot_" + i + "_" + 5)[0];
            String other = parameterMap.get("cot_" + i + "_" + 6)[0];
            tireCondtionDto.setPosition(i);
            tireCondtionDto.setRf(rf);
            tireCondtionDto.setLf(lf);
            tireCondtionDto.setRr(rr);
            tireCondtionDto.setRl(rl);
            tireCondtionDto.setRri(rri);
            tireCondtionDto.setLri(lri);
            tireCondtionDto.setOther(other);
            tireCondtionDtoList.add(tireCondtionDto);
        }
        motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtoList);
    }

    private void saveAri(HttpServletRequest request, HttpServletResponse response) {
        RequestAriDto requestAriDto = new RequestAriDto();
        try {
            InspectionDetailsDto inspectionDetailsDto = getInspectionDetails(request);
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.populate(requestAriDto, request.getParameterMap());
            requestAriDto.setClaimNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());

            RequestAriDto insert = requestAriService.insert(requestAriDto, getSessionUser(request));
            if (null != insert) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Updated");
            }
            List<RequestAriDto> list = requestAriService.searchAll(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            request.setAttribute(AppConstant.REQUESTED_ARI_LIST, list);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
        }

        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/requestARI.jsp");
    }

    private void fetchTimeSlotForInspection(HttpServletRequest request, HttpServletResponse response) {
        try {
            String inspectionTypeId = request.getParameter("inspectionTypeId");
            String jobType = request.getParameter("jobType");

            List<ListItemDto> timeSlotsByInspectionTypeId = assessorFeeService
                    .getTimeSlotsByInspectionTypeId(Integer.valueOf(inspectionTypeId), Integer.valueOf(jobType));

            String json = new Gson().toJson(timeSlotsByInspectionTypeId);
            response.getWriter().write(json);

        } catch (Exception ex) {
            LOGGER.error("Error fetching time slots: " + ex.getMessage(), ex);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Unable to fetch time slots.");
            } catch (IOException ioEx) {
                LOGGER.error("Error writing error response: " + ioEx.getMessage(), ioEx);
            }
        }
    }

    private void calculateProfessionalFee(HttpServletRequest request, HttpServletResponse response) {
        try {

            String refNo = request.getParameter("refNo");
            String inspectionTypeId = request.getParameter("inspectionTypeId");
            String jobType = request.getParameter("jobType");
            String assessorFeeDetailId = request.getParameter("assessorFeeDetailId");
            String otherFee = getCalculableAmount(request.getParameter("otherFee"));
            String mileage = getCalculableAmount(request.getParameter("mileage"));
            String costOfCall = getCalculableAmount(request.getParameter("costOfCall"));

            BigDecimal totalFee = BigDecimal.ZERO;
            String assessorType = inspectionDetailsService.getAssessorTypeByRefNo(refNo);
            BigDecimal mileageFee;
            BigDecimal assessorFee;

            if ("1".equals(jobType)) { //WEEK DAYS
                if (AppConstant.HYBRID.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.HYBRID);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.HYBRID);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).add(assessorFee).add(new BigDecimal(costOfCall));
                } else {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.PERMANENT);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.PERMANENT);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).add(assessorFee).add(new BigDecimal(costOfCall));
                }

            } else if ("2".equals(jobType)) { //WEEKEND / HOLIDAY
                if (AppConstant.HYBRID.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.HYBRID);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.HYBRID);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).add(assessorFee).add(new BigDecimal(costOfCall));
                } else {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.PERMANENT);
                    assessorFee = inspectionDetailsService.getAssessorFee(Integer.valueOf(assessorFeeDetailId), AppConstant.PERMANENT);
                    totalFee = new BigDecimal(mileage).multiply(mileageFee);
                    totalFee = totalFee.add(new BigDecimal(otherFee)).add(assessorFee).add(new BigDecimal(costOfCall));
                }
            }

            try {
                response.getWriter().println(totalFee);
            } catch (IOException ex) {
                LOGGER.error(ex.getMessage());
            }

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
        }

    }

    private void processThirdPartyDetails(HttpServletRequest request, HttpServletResponse response) {
        UserDto sessionUser = getSessionUser(request);
        String type = request.getParameter("TPP_TYPE");
        if ("GET".equals(type)) {
            String key = request.getParameter("KEY");
            InspectionDetailsDto inspectionDetailsDto = (InspectionDetailsDto) request.getSession().getAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION");
            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = inspectionDetailsDto.getThirdPartyAssessorMap();
            ClaimThirdPartyDetailsGenericDto thirdPartyDetailsGenericDto = thirdPartyAssessorMap.get(key);
            Gson gson = new Gson();
            String json = gson.toJson(thirdPartyDetailsGenericDto);
            printWriter(request, response, json);
        } else if ("ADD".equals(type)) {
            String key = request.getParameter("KEY");
            InspectionDetailsDto inspectionDetailsDto = (InspectionDetailsDto) request.getSession().getAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION");
            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = inspectionDetailsDto.getThirdPartyAssessorMap();

            if (null != key && !key.isEmpty()) {
                ClaimThirdPartyDetailsGenericDto thirdPartyDetailsGenericDto = thirdPartyAssessorMap.get(key);
                if ("CALL_CENTER".equals(thirdPartyDetailsGenericDto.getType())) {
                    try {
                        Map<String, String[]> parameterMap = request.getParameterMap();
                        ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDtoNew = new ClaimThirdPartyDetailsGenericDto();
                        BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                            @Override
                            public Object convert(String value, Class clazz) {
                                if (clazz.isEnum()) {
                                    return Enum.valueOf(clazz, value);
                                } else {
                                    return super.convert(value, clazz);
                                }
                            }
                        });
                        beanUtilsBean.populate(claimThirdPartyDetailsGenericDtoNew, parameterMap);
                        claimThirdPartyDetailsGenericDtoNew.setType("ASSESSOR");
                        claimThirdPartyDetailsGenericDtoNew.setStatus("NEW");
                        claimThirdPartyDetailsGenericDtoNew.setClaimNo(thirdPartyDetailsGenericDto.getClaimNo());
                        claimThirdPartyDetailsGenericDtoNew.setTxnId(0);
                        claimThirdPartyDetailsGenericDtoNew.setCcTpdId(thirdPartyDetailsGenericDto.getTxnId());
                        claimThirdPartyDetailsGenericDtoNew.setMappingId(thirdPartyDetailsGenericDto.getTxnId());
                        claimThirdPartyDetailsGenericDtoNew.setMappingType("CALL_CENTER");
                        claimThirdPartyDetailsGenericDtoNew.setInpUserId(sessionUser.getUserId());
                        claimThirdPartyDetailsGenericDtoNew.setInpDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                        thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDtoNew.getType() + "_" + claimThirdPartyDetailsGenericDtoNew.getTxnId(), claimThirdPartyDetailsGenericDtoNew);
                    } catch (Exception ex) {
                        LOGGER.error(ex.getMessage());
                        ;
                    }

                } else if ("ASSESSOR".equals(thirdPartyDetailsGenericDto.getType())) {
                    try {
                        Map<String, String[]> parameterMap = request.getParameterMap();
                        BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                            @Override
                            public Object convert(String value, Class clazz) {
                                if (clazz.isEnum()) {
                                    return Enum.valueOf(clazz, value);
                                } else {
                                    return super.convert(value, clazz);
                                }
                            }
                        });
                        beanUtilsBean.populate(thirdPartyDetailsGenericDto, parameterMap);
                        if (!"NEW".equals(thirdPartyDetailsGenericDto.getStatus())) {
                            thirdPartyDetailsGenericDto.setStatus("EDIT");
                        }
                        thirdPartyDetailsGenericDto.setInpUserId(sessionUser.getUserId());
                        thirdPartyDetailsGenericDto.setInpDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    } catch (Exception ex) {
                        LOGGER.error(ex.getMessage());
                        ;
                    }
                }
            } else {
                try {
                    Map<String, String[]> parameterMap = request.getParameterMap();
                    ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDtoNew = new ClaimThirdPartyDetailsGenericDto();
                    BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                        @Override
                        public Object convert(String value, Class clazz) {
                            if (clazz.isEnum()) {
                                return Enum.valueOf(clazz, value);
                            } else {
                                return super.convert(value, clazz);
                            }
                        }
                    });
                    beanUtilsBean.populate(claimThirdPartyDetailsGenericDtoNew, parameterMap);
                    claimThirdPartyDetailsGenericDtoNew.setType("ASSESSOR");
                    claimThirdPartyDetailsGenericDtoNew.setStatus("NEW");
                    claimThirdPartyDetailsGenericDtoNew.setClaimNo(inspectionDetailsDto.getClaimNo());
                    claimThirdPartyDetailsGenericDtoNew.setCcTpdId(0);
                    claimThirdPartyDetailsGenericDtoNew.setInpUserId(sessionUser.getUserId());
                    claimThirdPartyDetailsGenericDtoNew.setInpDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDtoNew.getType() + "_NEW_" + (thirdPartyAssessorMap.size() + 1), claimThirdPartyDetailsGenericDtoNew);
                } catch (Exception ex) {
                    LOGGER.error(ex.getMessage());
                    ;
                }
            }

            String json = "ADDED";
            printWriter(request, response, json);
        } else if ("LIST".equals(type)) {
            InspectionDetailsDto inspectionDetailsDto = (InspectionDetailsDto) request.getSession().getAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION");
            request.setAttribute("inspectionDetailsDto", inspectionDetailsDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/thirdPartyAssessorGrid.jsp");
        }
    }

    private void viewEditPrevious(HttpServletRequest request, HttpServletResponse response) {
        InspectionDetailsDto inspectionDetailsDto = null;
        HttpSession session = request.getSession();
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            Integer jobNo = request.getParameter("P_N_JOB_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_JOB_NO"));
            Integer polNo = request.getParameter("P_POL_N_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_N_REF_NO"));

            inspectionDetailsDto = inspectionDetailsService.search(jobNo);

            //Check whether Inspection Detail Is saved in previous to this ref no
            if (null != inspectionDetailsDto && 0 < inspectionDetailsDto.getRefNo()) {
                request.setAttribute("ACTION", "UPDATE");
            } else {
                request.setAttribute("ACTION", "SAVE");
                inspectionDetailsDto = new InspectionDetailsDto();
                inspectionDetailsDto.setClaimNo(claimNo);
                inspectionDetailsDto.setRefNo(jobNo);
            }

            AssessorAllocationDto assessorAllocationDto;
            //Assessor Allocation Details Load From Assesor Allocation Table
            assessorAllocationDto = assessorAllocationService.search(jobNo);
            assessorAllocationDto.setJobId(assessorAllocationDto.getJobId());

            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            if (null != claimsDto) {
                assessorAllocationDto.setClaimsDto(claimsDto);
            }

            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            inspectionDetailsDto.setInputUserId(getSessionUser(request).getUserId());
            inspectionDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsService.getClaimDocumentTypeDtoList(AppConstant.ASSESSOR_DEPARTMENT_ID, assessorAllocationDto.getInspectionDto().getInspectionId()));

            //Third Party Details Load From Call Center
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtos = callCenterService.getClaimThirdPartyDetailsGeneric(claimNo);
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosAssessor = inspectionDetailsService.getClaimThirdPartyDetailsGeneric(claimNo);
            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = new HashMap<>();

            List<ClaimThirdPartyDetailsGenericDto> list = new ArrayList<>();
            list.addAll(claimThirdPartyDetailsGenericDtos);
            list.addAll(claimThirdPartyDetailsGenericDtosAssessor);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : list) {
                thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDto.getType() + "_" + claimThirdPartyDetailsGenericDto.getTxnId(), claimThirdPartyDetailsGenericDto);
            }

            inspectionDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);

            List<PreviousClaimsDto> previousClaimList = inspectionDetailsService.getPreviousClaimList(null == claimsDto.getVehicleNo() ? AppConstant.STRING_EMPTY : claimsDto.getVehicleNo(), claimNo);
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, jobNo);
            request.setAttribute(AppConstant.REMARK_LIST, inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID));
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        request.setAttribute(AppConstant.PREVIOUS_INSPECTION_DTO, inspectionDetailsDto);
        request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.YES);
        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "");
        request.setAttribute(AppConstant.ERROR_MESSAGE, "");
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/assessorAllocationInspectionDetails.jsp");
    }

    private void calculateOnsiteInspectionValues(HttpServletRequest request, HttpServletResponse response) {
        String calType = request.getParameter("CAL_TYPE");
        try {
            BigDecimal totalFee;
            if ("ACR".equals(calType)) {
                String costPart = getCalculableAmount(request.getParameter("costPart"));
                String costLabour = getCalculableAmount(request.getParameter("costLabour"));
                totalFee = new BigDecimal(costPart).add(new BigDecimal(costLabour));
            } else if ("BaldTyrePenaltyAmount".equals(calType)) {
                String acr = getCalculableAmount(request.getParameter("acr"));
                String boldPercent = getCalculableAmount(request.getParameter("boldPercent"));
                totalFee = new BigDecimal(acr).multiply(new BigDecimal(boldPercent)).divide(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
            } else if ("UnderPenaltyAmount".equals(calType)) {
                String acr = getCalculableAmount(request.getParameter("acr"));
                String underPenaltyPercent = getCalculableAmount(request.getParameter("underPenaltyPercent"));
                totalFee = new BigDecimal(acr).multiply(new BigDecimal(underPenaltyPercent)).divide(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
            } else if ("PayableAmount".equals(calType)) {
                String acr = getCalculableAmount(request.getParameter("acr"));
                String excess = getCalculableAmount(request.getParameter("excess"));
                String underPenaltyAmount = getCalculableAmount(request.getParameter("underPenaltyAmount"));
                String boldTirePenaltyAmount = getCalculableAmount(request.getParameter("boldTirePenaltyAmount"));
                totalFee = new BigDecimal(acr).subtract((new BigDecimal(excess)).add(new BigDecimal(underPenaltyAmount).add(new BigDecimal(boldTirePenaltyAmount))));
            } else {
                totalFee = BigDecimal.ZERO;
            }

            try {
                BigDecimal scaled = totalFee.setScale(2, RoundingMode.HALF_UP);
                response.getWriter().println(scaled.toString());
            } catch (IOException ex) {
                LOGGER.error(ex.getMessage());
                ;
            }

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            ;
        }
    }

    private String getCalculableAmount(String parameter) {
        if (null != parameter && !parameter.isEmpty()) {
            String value = parameter.trim();
            return value.replaceAll(",", "");
        } else {
            return "0";
        }
    }

    private void deleteImages(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        UserDto sessionUser = getSessionUser(request);
        try {
            String images = request.getParameter("deleteImages");
            Integer claimNo = Integer.valueOf(request.getParameter("claimNo"));
            Integer jobRefNo = Integer.valueOf(request.getParameter("jobRefNo"));
            //  ClaimImageDto claimImageDto = stDocumentService.getClaimImageDto(refNo);
            boolean deleted = stDocumentService.deleteImages(images, claimNo, sessionUser, jobRefNo);
            if (deleted) {
                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            } else {
                json = gson.toJson("FAIL");
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void docDeltete(HttpServletRequest request, HttpServletResponse response) {

        InspectionDetailsDto inspectionDetailsDto = null;
        HttpSession session = request.getSession();
        try {
            UserDto user = getSessionUser(request);
            String docs = request.getParameter("deleteDoc");
            Integer documentTypeId = Integer.parseInt(request.getParameter("documentTypeId"));
            inspectionDetailsDto = getInspectionDetails(request);
            boolean deleted = stDocumentService.deleteDocuments(docs, documentTypeId, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo(), user);
            if (deleted) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully deleted");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Can not be deleted");
        } finally {
            if (inspectionDetailsDto != null) {
                Integer inspectionTypeId = inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId();
                List<ClaimUploadViewDto> claimUploadViewDtoList = inspectionDetailsService.getClaimUploadViewDtoList(inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo(), AppConstant.ASSESSOR_DEPARTMENT_ID, inspectionTypeId);
                session.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            }
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/documentUpload.jsp");
        }
    }

    private void calculateUnderInsPenaltyPerc(HttpServletRequest request, HttpServletResponse response) {
        BigDecimal percentage;
        try {
            String pav = getCalculableAmount(request.getParameter("pav"));
            String sumInsured = getCalculableAmount(request.getParameter("sumInsured"));

            BigDecimal bigDecimalHundred = new BigDecimal(100);

            percentage = new BigDecimal(pav).multiply(new BigDecimal(80)).divide(bigDecimalHundred)
                    .subtract(new BigDecimal(sumInsured))
                    .divide(new BigDecimal(pav).multiply(new BigDecimal(80)).divide(bigDecimalHundred), 2, RoundingMode.HALF_UP)
                    .multiply(bigDecimalHundred);

        } catch (ArithmeticException e) {
            percentage = BigDecimal.ZERO;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            ;
            percentage = BigDecimal.ZERO;
        }

        try {
            response.getWriter().println(percentage);
        } catch (IOException ex) {
            LOGGER.error(ex.getMessage());
            ;
        }

    }

    private void getSpecialRemark(HttpServletRequest request, HttpServletResponse response) {
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        String remark = request.getParameter("remark");
        String sectionName = request.getParameter("sectionName");
        ClaimsDto claimsDto = getSessionClaimDetails(request, response);
        try {
            if (null == claimsDto) {
                int claimId = null == request.getParameter("claimId") ? 0 : Integer.parseInt(request.getParameter("claimId"));
                claimsDto = callCenterService.search(claimId);

            }
            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimsDto.getClaimNo());
            specialRemarkDto.setRemark(remark);
            specialRemarkDto.setSectionName(sectionName);
            UserDto user = getSessionUser(request);


            ErrorMessageDto errorMessageDto = inspectionDetailsService.saveRemark(specialRemarkDto, user);
            if (errorMessageDto != null) {
                json = "Successfully Add Special Remark";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

    }

}
