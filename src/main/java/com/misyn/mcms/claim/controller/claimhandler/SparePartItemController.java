package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.SparePartItemService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "SparePartItemController", urlPatterns = "/SparePartItemController/*")
public class SparePartItemController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserController.class);
    private SparePartItemService sparePartItemService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        sparePartItemService = getSparePartItemServiceBySession(request);
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/saveSparePartItem":
                    saveSparePartItem(request, response, user);
                    break;
                case "/searchSparePartItem":
                    searchSparePartItem(request, response);
                    break;
                case "/searchAllSparePartItem":
                    searchAllSparePartItem(request, response);
                    break;
                case "/validateSparePartName":
                    validateSparePartName(request, response);
                    break;
                case "/viewSparePartItem":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/systemParameter/SparePartItems.jsp");
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void validateSparePartName(HttpServletRequest request, HttpServletResponse response) {
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        SparePartItemDto sparePartItemDto = new SparePartItemDto();
        Gson gson = new Gson();
        String json;
        try {
            BeanUtils.populate(sparePartItemDto, request.getParameterMap());
            String PartName = request.getParameter("sparePartName") == null ? AppConstant.STRING_EMPTY : request.getParameter("sparePartName");
//            Integer refNo = Integer.parseInt(request.getParameter("documentTypeId") == null ? AppConstant.ZERO : request.getParameter("documentTypeId"));
            String docTypeName = sparePartItemService.validateparePartName(PartName);
            if (null == docTypeName) {
                json = ("Name Already exists");
                json = gson.toJson(json);
                printWriter(request, response, json);
            } else {

            }

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void searchAllSparePartItem(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        String recordsStatus = request.getParameter(AppConstant.RECORDS_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.RECORDS_STATUS);
        String sparePartsName = request.getParameter(AppConstant.SPARE_PARTS_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.SPARE_PARTS_NAME);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("v_spare_part_name", sparePartsName, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(recordsStatus)) {
                this.addFieldParameter("v_record_status", recordsStatus, FieldParameterDto.SearchType.Like, parameterList);
            }

            switch (orderColumnName) {
                case "sparePartRefNo":
                    orderColumnName = "n_spare_part_ref_no";
                    break;
                case "sparePartName":
                    orderColumnName = "v_spare_part_name";
                    break;
                case "recordStatus":
                    orderColumnName = "v_record_status";
                    break;
                case "inputUserId":
                    orderColumnName = "v_input_user_id";
                    break;
                case "inputDateTime":
                    orderColumnName = "v_input_date_time";
                    break;

            }
            DataGridDto data = sparePartItemService.getSparePartItemDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void searchSparePartItem(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            setClaimPanelUserPopupListValues(request);
            int id = Integer.parseInt(request.getParameter("refNo"));
            SparePartItemDto sparePartItemDto = sparePartItemService.search(id);
            request.setAttribute("sparePartItem", sparePartItemDto);
            json = gson.toJson(sparePartItemDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveSparePartItem(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        SparePartItemDto sparePartItemDto = new SparePartItemDto();
        SparePartItemDto sparePartItem;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(sparePartItemDto, request.getParameterMap());
            sparePartItemDto.setInputDateTime(Utility.sysDateTime());
            sparePartItemDto.setInputUserId(user.getUserId());
            sparePartItem = sparePartItemService.insert(sparePartItemDto);

            if (null != sparePartItem) {
                json = "Data Save Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }
}
