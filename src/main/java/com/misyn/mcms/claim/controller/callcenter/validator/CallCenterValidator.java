package com.misyn.mcms.claim.controller.callcenter.validator;

import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.ErrorMessageDto;

public class CallCenterValidator implements Validator<ClaimsDto> {


    public ErrorMessageDto validateCallCenter(ClaimsDto claimsDto) {
        DoValidate<ClaimsDto> doValidate = new DoValidate<>();
        ErrorMessageDto errorMessageDto = doValidate.validate(claimsDto, 1);
//
        if (errorMessageDto.getErrorCode() == 200) {
            if (2 == claimsDto.getIntimationType()) {
                if (null == claimsDto.getLateIntimateReason() || claimsDto.getLateIntimateReason() == 0) {
                    errorMessageDto.setErrorCode(520);
                    errorMessageDto.setMessage("Reasons For Late Intimation can not be empty");
                    errorMessageDto.setDtoFieldName("lateIntimateReason");
                    errorMessageDto.setFormFieldName("Reasons For Late Intimation");
                }
            } else if ("Y".equals(claimsDto.getIsFirstStatementReq())) {
                if (null == claimsDto.getFirstStatementReqReason() || claimsDto.getFirstStatementReqReason() == 0) {
                    errorMessageDto.setErrorCode(520);
                    errorMessageDto.setMessage("Reason For First statement required can not be empty");
                    errorMessageDto.setDtoFieldName("firstStatementReqReason");
                    errorMessageDto.setFormFieldName("Reason For First statement required");
                }
            } else if ("Y".equals(claimsDto.getIsCatEvent())) {
                if (null == claimsDto.getCatEventCode() || claimsDto.getCatEventCode() == 0) {
                    errorMessageDto.setErrorCode(520);
                    errorMessageDto.setMessage("CAT event code can not be empty");
                    errorMessageDto.setDtoFieldName("catEventCode");
                    errorMessageDto.setFormFieldName("CAT event code");
                }
            }


        }

        return errorMessageDto;
    }


    @Override
    public void validate(ClaimsDto claimsDto) {

    }
}


//    Field[] fields =  claimsDto.getClass().getDeclaredFields();
//        for(Field field : fields) {
//                try {
//                String name = field.getName();
//                field.setAccessible(true);
//                Object value = field.get(claimsDto);
//                }catch (Exception e){
//
//                }
//                }
