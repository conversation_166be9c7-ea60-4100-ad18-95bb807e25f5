package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.AgentGarageDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.ErrorMessageDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.service.AgentGarageService;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "GarageListController", urlPatterns = "/GarageListController/*")
public class GarageListController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GarageListController.class);
    private AgentGarageService agentGarageService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        agentGarageService = getAgentGarageServiceBySession(request);

        try {
            switch (pathInfo) {
                case "/saveGarage":
                    saveGarage(request, response);
                    break;
                case "/searchGarage":
                    searchGarage(request, response);
                    break;
                case "/searchAllGarage":
                    searchAllGarage(request, response);
                    break;
                case "/validateGarageName":
                    validateGarageName(request, response);
                    break;
                case "/viewgarage":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/systemParameter/AgenGarage.jsp");
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void validateGarageName(HttpServletRequest request, HttpServletResponse response) {
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        AgentGarageDto agentGarageDto = new AgentGarageDto();
        Gson gson = new Gson();
        String json;
        try {
            BeanUtils.populate(agentGarageDto, request.getParameterMap());
            String PartName = request.getParameter("gargName") == null ? AppConstant.STRING_EMPTY : request.getParameter("gargName");
            String docTypeName = agentGarageService.validateGarageName(PartName);
            if (null == docTypeName) {
                json = ("Name Already exists");
                json = gson.toJson(json);
                printWriter(request, response, json);
            } else {

            }

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void searchAllGarage(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            switch (orderColumnName) {
                case "id":
                    orderColumnName = "N_ID";
                    break;
                case "gargCode":
                    orderColumnName = "V_GARG_CODE";
                    break;
                case "gargName":
                    orderColumnName = "V_GARG_NAME";
                    break;
                case "address1":
                    orderColumnName = "V_ADDRESS1";
                    break;
                case "address2":
                    orderColumnName = "V_ADDRESS2";
                    break;
                case "address3":
                    orderColumnName = "V_ADDRESS3";
                    break;
                case "conPerson":
                    orderColumnName = "V_CON_PERSON";
                    break;
                case "conNumber":
                    orderColumnName = "V_CON_NUMBER";
                    break;
                case "genTelNo":
                    orderColumnName = "V_GEN_TEL_NO";
                    break;
                case "status":
                    orderColumnName = "V_STATUS";
                    break;

            }
            DataGridDto data = agentGarageService.getgarageDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void searchGarage(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            setClaimPanelUserPopupListValues(request);
            int id = Integer.parseInt(request.getParameter("id"));
            AgentGarageDto agentGarageDto = agentGarageService.search(id);
            json = gson.toJson(agentGarageDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveGarage(HttpServletRequest request, HttpServletResponse response) {
        AgentGarageDto agentGarageDto = new AgentGarageDto();
        AgentGarageDto agentGarageDto1;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(agentGarageDto, request.getParameterMap());
            agentGarageDto1 = agentGarageService.insert(agentGarageDto);

            if (null != agentGarageDto1) {
                json = "Data Save Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }
}
