package com.misyn.mcms.claim.controller.common;

import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.JWTClaimDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.redis.RedisService;
import com.misyn.mcms.claim.service.KeycloakAccessService;
import com.misyn.mcms.claim.service.keycloak.KeycloakAuthService;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakAccessServiceImpl;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakAuthServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.JwtUtil;
import com.misyn.mcms.utility.Parameters;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Objects;

@WebServlet(name = "authController", value = "/auth")
public class AuthController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthController.class);
    private final KeycloakAuthService keycloakAuthService = new KeycloakAuthServiceImpl();
    private final KeycloakAccessService keycloakAccessService = new KeycloakAccessServiceImpl();
    private final String AUTH_LOGOUT_URL = Parameters.getAuthServiceLogoutUrl();

    @Override
    public void init() {
    }

    @Override
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            HttpSession session = request.getSession();
            request.getSession(true).setAttribute("token", request.getParameter("token"));
            JWTClaimDto token = JwtUtil.decodeJwt(request.getParameter("token"));
            if (Objects.isNull(token) || Objects.isNull(token.getUsername())) {
                request.getSession(true).invalidate();
                response.sendRedirect(AUTH_LOGOUT_URL);
                return;
            }
            UserDto user = keycloakAuthService.userLoginValidate(token.getUsername());
            if (Objects.isNull(user)) {
                LOGGER.info("User not found in MCMS User Master {}", token.getUsername());
                request.getSession(true).invalidate();
                response.sendRedirect(AUTH_LOGOUT_URL);

            } else {
                user = keycloakAuthService.getUserDto(token.getUsername());
                user.setIpaddress(request.getRemoteAddr());
                user.setSessionId(session.getId().trim());
                session.removeAttribute(AppConstant.SESSION_USER);
                session.setAttribute(AppConstant.SESSION_USER, user);
                RedisService.cacheMapData(token.getUsername() + "-permission", keycloakAccessService.getUserRoleMap(token.getId()));
                response.sendRedirect("home.do");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            request.getSession(true).invalidate();
            response.sendRedirect(AUTH_LOGOUT_URL);
        }


    }

    public void destroy() {
    }
}
