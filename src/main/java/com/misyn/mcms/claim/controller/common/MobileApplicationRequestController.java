package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.MobileApplicationRequestService;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "MobileApplicationRequestController", urlPatterns = "/MobileApplicationRequestController/*")
public class MobileApplicationRequestController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(MobileApplicationRequestController.class);
    private MobileApplicationRequestService mobileApplicationRequestService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        mobileApplicationRequestService = getMobileApplicationRequestServiceBySession(request);

        try {
            switch (pathInfo) {
                case "/searchAllrequest":
                    searchAllClaimUser(request, response);
                    break;
                case "/viewRequest":
//                    setClaimPanelUserPopupListValues(request);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/mobileApplicationRequestView.jsp");
                    break;
                case "/searchAllInspection":
                    searchAllInspection(request, response);
                    break;
                case "/viewInspection":
//                    setClaimPanelUserPopupListValues(request);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/mobileApplicationInspectionView.jsp");
                    break;
                case "/getAllAssessor":
                    getAllAssessor(request, response);
                    break;
                case "/searchNotification":
                    searchNotification(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchNotification(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        try {
            String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
            String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
            String assignUser = request.getParameter(AppConstant.TXT_ASSIGN__USER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSIGN__USER);
            String mobileReadStatus = request.getParameter(AppConstant.FILTERED_BY) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.FILTERED_BY);
            String notificationStatus = request.getParameter(AppConstant.FILTERED_BY) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.NOTIFICATION_STATUS);
            String notificationReadStatus = request.getParameter(AppConstant.FILTERED_BY) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.NOTIFICATION_READ_STATUS);

            this.addFieldParameter("t1.v_read_status", notificationReadStatus, FieldParameterDto.SearchType.Equal, parameterList);
            this.addFieldParameter("t1.v_is_mobile_read", mobileReadStatus, FieldParameterDto.SearchType.Equal, parameterList);
            this.addFieldParameter("t2.n_accessusrtype", String.valueOf(AppConstant.ACCESS_LEVEL_ASSESSOR), FieldParameterDto.SearchType.Equal, parameterList);
            if (!assignUser.equals(AppConstant.STRING_EMPTY)) {
                this.addFieldParameter("t1.v_assign_user_id", assignUser, FieldParameterDto.SearchType.Equal, parameterList);
            }
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            if (orderColumnName.equals("index")) {
                orderColumnName = "t1.n_claim_no";
            } else {
                switch (orderColumnName) {
                    case "claimNo":
                        orderColumnName = "t1.n_claim_no";
                        break;
                    case "vehicleNo":
                        orderColumnName = "t1.v_vehicle_no";
                        break;
                    case "assignUserId":
                        orderColumnName = "t1.v_assign_user_id";
                        break;
                    case "notifyDate":
                        orderColumnName = "t1.d_notify_date_time";
                        break;
                }
            }

            DataGridDto data = mobileApplicationRequestService.searchNotification(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, notificationStatus);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getAllAssessor(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            List<UserDto> allAssessor = mobileApplicationRequestService.getAllAssessor(AppConstant.ACCESS_LEVEL_ASSESSOR);
            json = gson.toJson(allAssessor);
            PrintWriter writer = response.getWriter();
            writer.print(json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchAllClaimUser(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        setClaimPanelUserPopupListValues(request);
        try {
            String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
            String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
            String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
            String inspectionType = request.getParameter(AppConstant.TXT_INSPECTION_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSPECTION_TYPE);
            String jobNo = request.getParameter(AppConstant.TXT_JOB_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
            String assignUser = request.getParameter(AppConstant.TXT_ASSIGN__USER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSIGN__USER);
            String filterdBy = request.getParameter(AppConstant.FILTERED_BY) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.FILTERED_BY);

            this.addFieldParameter("t1.claim_no", claimNumber, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t2.insepction_id", inspectionType, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.assessor_user_name", assignUser, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t2.job_id", jobNo, FieldParameterDto.SearchType.Like, parameterList);

            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            if ("0".equals(filterdBy)) {
                switch (orderColumnName) {
                    case "claimNo":
                        orderColumnName = "t1.claim_no";
                        break;
                    case "assignUserId":
                        orderColumnName = "t1.assessor_user_name";
                        break;
                    case "requestDateTime":
                        orderColumnName = "t1.input_date_time";
                        break;
                    case "requestStatus":
                        orderColumnName = "t1.request_status";
                        break;
                    default:
                        orderColumnName = "t1.txn_id";
                        break;
                }
            } else {
                switch (orderColumnName) {
                    case "refNo":
                        orderColumnName = "t2.ref_no";
                        break;
                    case "claimNo":
                        orderColumnName = "t1.claim_no";
                        break;
                    case "jobId":
                        orderColumnName = "t2.job_id";
                        break;
                    case "assignUserId":
                        orderColumnName = "t1.assessor_user_name";
                        break;
                    case "notifyDateTime":
                        orderColumnName = "t2.inp_datetime";
                        break;
                    case "requestDateTime":
                        orderColumnName = "t1.input_date_time";
                        break;
                    case "requestStatus":
                        orderColumnName = "t1.request_status";
                        break;
                    case "inspectionId":
                        orderColumnName = "t2.insepction_id";
                        break;
                    case "recordStatus":
                        orderColumnName = "t4.v_status_desc";
                        break;

                }
            }


            DataGridDto data = mobileApplicationRequestService.getMobileRequestDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, filterdBy);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void searchAllInspection(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        setClaimPanelUserPopupListValues(request);
        try {
            String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
            String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
            String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
            String inspectionType = request.getParameter(AppConstant.TXT_INSPECTION_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSPECTION_TYPE);
            String jobNo = request.getParameter(AppConstant.TXT_JOB_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
            String assignUser = request.getParameter(AppConstant.TXT_ASSIGN__USER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSIGN__USER);

            this.addFieldParameter("t1.N_CLAIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t2.insepction_id", inspectionType, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_ASSIGN_USER_ID", assignUser, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t2.job_id", jobNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t4.n_accessusrtype", "20", FieldParameterDto.SearchType.Equal, parameterList);

            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t1.N_TXN_ID";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLAIM_NO";
                    break;
                case "readDatetime":
                    orderColumnName = "t1.V_READ_DATE_TIME";
                    break;
                case "notifyDatetime":
                    orderColumnName = "t1.D_NOTIFY_DATE_TIME";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "accidentDate":
                    orderColumnName = "t1.D_ACCIDENT_DATE";
                    break;
                case "isMobileRead":
                    orderColumnName = "t1.V_IS_MOBILE_READ";
                    break;
                case "inspectionType":
                    orderColumnName = "t2.insepction_id";
                    break;
                case "jobId":
                    orderColumnName = "t2.job_id";
                    break;
                case "assignUserId":
                    orderColumnName = "t1.V_ASSIGN_USER_ID";
                    break;
            }

            DataGridDto data = mobileApplicationRequestService.getMobileInspectionDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }
}
