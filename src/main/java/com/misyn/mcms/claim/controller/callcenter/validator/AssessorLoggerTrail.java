package com.misyn.mcms.claim.controller.callcenter.validator;

import com.misyn.mcms.claim.dto.ClaimLogTrailDto;
import com.misyn.mcms.claim.dto.FormFieldDto;
import com.misyn.mcms.claim.service.FormFieldService;
import com.misyn.mcms.claim.service.impl.FormFieldServiceImpl;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class AssessorLoggerTrail<T> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorLoggerTrail.class);
    FormFieldService formFieldService = new FormFieldServiceImpl();

    public List<ClaimLogTrailDto> getLoggerTrailDetailsList(T t1, T t2, Integer formNameId) {

        List<ClaimLogTrailDto> logTrailList = new ArrayList<>();

        Field[] fields = t1.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                String name = field.getName();
                field.setAccessible(true);
                Object newValue = field.get(t1);
                Object oldValue = field.get(t2);

                if (oldValue != null && !oldValue.equals(newValue)) {
                    ClaimLogTrailDto dto = new ClaimLogTrailDto();
                    FormFieldDto fieldDetails = formFieldService.getDtoFieldRelatedField(formNameId, name);
                    if (null == fieldDetails || null == fieldDetails.getFormFieldName() || fieldDetails.getFormFieldName().isEmpty()) {
                        continue;
                    }
                    dto.setFormNameId(formNameId);
                    dto.setFieldName(fieldDetails.getFormFieldName());
                    if (newValue.toString().equals("Confirm")) {
                        newValue = "Correct";
                    }
                    dto.setFieldValue(getLoggerValue(name, newValue.toString()));
                    logTrailList.add(dto);
                }

            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return logTrailList;
    }

    private String getLoggerValue(String name, String value) {
        DbRecordCommonFunction dbFunction = new DbRecordCommonFunction();
        String returnedValue = "";
        switch (name) {
            case "settlementMethod":
                returnedValue = dbFunction.getValue("claim_settlement_method", "value", "settlement_id", value);
                break;
            case "jobType":
                returnedValue = Integer.parseInt(value) == 1 ? "DAY" : "NIGHT";
                break;
            case "offerType":
                returnedValue = Integer.parseInt(value) == 0 ? "" : dbFunction.getValue("claim_offer_type", "V_OFFER_TYPE_DESC", "N_OFFER_TYPE_ID", value);
                break;
            case "firstStatementReqReason":
                returnedValue = dbFunction.getValue("claim_first_statement_reason", "V_REASON", "n_id", value);
                break;

            default:
                returnedValue = value;
        }
        return returnedValue;
    }
}
