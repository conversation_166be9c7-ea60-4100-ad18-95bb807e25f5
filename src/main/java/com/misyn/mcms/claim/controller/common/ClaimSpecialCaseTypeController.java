package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.ClaimSpecialCaseTypeService;
import com.misyn.mcms.claim.service.impl.ClaimSpecialCaseTypeServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.LocalDateTimeAdapter;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@WebServlet(name = "ClaimSpecialCaseTypeController", urlPatterns = "/claimSpecialCaseTypeController/*")
public class ClaimSpecialCaseTypeController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimSpecialCaseTypeController.class);
    private final ClaimSpecialCaseTypeService claimSpecialCaseTypeService = new ClaimSpecialCaseTypeServiceImpl();
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/viewClaimSpecialCaseTypeUI":
                    viewClaimSpecialCaseTypeUI(request, response);
                    break;
                case "/saveClaimSpecialCaseType":
                    saveClaimSpecialCaseType(request, response, user);
                    break;
                case "/updateClaimSpecialCaseType":
                    updateClaimSpecialCaseType(request, response, user);
                    break;
                case "/viewClaimSpecialCaseType":
                    viewClaimSpecialCaseType(request, response);
                    break;
                case "/viewClaimSpecialCaseTypeList":
                    viewClaimSpecialCaseTypeList(request, response);
                    break;
                case "/deleteClaimSpecialCaseType":
                    deleteClaimSpecialCaseType(request, response, user);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewClaimSpecialCaseTypeUI(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimSpecialCaseTypeConfig/claimSpecialCaseTypeConfig.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveClaimSpecialCaseType(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto = new ClaimSpecialCaseTypeDto();
        ClaimSpecialCaseTypeDto savedDto;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(claimSpecialCaseTypeDto, request.getParameterMap());
            if (Objects.nonNull(user)) {
                claimSpecialCaseTypeDto.setInputUser(user.getUserId());
                claimSpecialCaseTypeDto.setLastModifiedUser(user.getUserId());
            }
            savedDto = claimSpecialCaseTypeService.saveClaimSpecialCaseType(claimSpecialCaseTypeDto);

            if (null != savedDto) {
                json = "Saved Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage(e.getMessage());
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void updateClaimSpecialCaseType(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto = new ClaimSpecialCaseTypeDto();
        ClaimSpecialCaseTypeDto savedDto;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(claimSpecialCaseTypeDto, request.getParameterMap());
            if (Objects.nonNull(user)) {
                claimSpecialCaseTypeDto.setLastModifiedUser(user.getUserId());
            }
            savedDto = claimSpecialCaseTypeService.updateClaimSpecialCaseType(claimSpecialCaseTypeDto);

            if (null != savedDto) {
                json = "Saved Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage(e.getMessage());
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void viewClaimSpecialCaseType(HttpServletRequest request, HttpServletResponse response) {
        Integer id = request.getParameter("id") == null || request.getParameter("id").isEmpty()
                ? 0
                : Integer.valueOf(request.getParameter("id"));

        Gson gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                .create();
        try {
            ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto = claimSpecialCaseTypeService.getClaimSpecialCaseType(id);
            String json = gson.toJson(claimSpecialCaseTypeDto);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void viewClaimSpecialCaseTypeList(HttpServletRequest request, HttpServletResponse response) {

        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                .create();
        String json;

        Integer id = request.getParameter("id") == null || request.getParameter("id").isEmpty() ? 0 : Integer.parseInt(request.getParameter("id"));
        String claimNo = request.getParameter("claimNo") == null ? AppConstant.STRING_EMPTY : request.getParameter("claimNo");
        String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("id", id.equals(0) ? "" : String.valueOf(id), FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("claim_no", claimNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("remark", remark, FieldParameterDto.SearchType.Like, parameterList);


            switch (orderColumnName) {
                case "id":
                    orderColumnName = "id";
                    break;
                case "claimNo":
                    orderColumnName = "claim_no";
                    break;
                case "remark":
                    orderColumnName = "remark";
                    break;

            }
            boolean isSearch = request.getParameter("isSearch") != null && (request.getParameter("isSearch").equalsIgnoreCase("1"));
            DataGridDto data = claimSpecialCaseTypeService.getClaimSpecialCaseTypeSDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, isSearch);

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void deleteClaimSpecialCaseType(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        Gson gson = new Gson();
        String json;
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            Integer id = request.getParameter("id") == null || request.getParameter("id").isEmpty() ? 0 : Integer.valueOf(request.getParameter("id"));
            claimSpecialCaseTypeService.deleteClaimSpecialCaseType(id, user.getUserId());
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
           /* LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);*/
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage(e.getMessage());
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }
}
