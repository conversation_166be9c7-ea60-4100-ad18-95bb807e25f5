package com.misyn.mcms.claim.controller.motorengineer;
/**
 * Created by tharaka on 11/9/18.
 */

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

@WebServlet(name = "ClaimAssignUserReassignController", urlPatterns = "/ClaimAssignUserReassignController/*")
public class ClaimAssignUserReassignController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimAssignUserReassignController.class);
    private int draw = 1;
    private InspectionDetailsService inspectionDetailsService = null;
    private MotorEngineerService motorEngineerService = null;
    private CallCenterService callCenterService = null;
    private AssessorAllocationService assessorAllocationService = null;
    private RequestAriService requestAriService = null;
    private StorageService stDocumentService = null;
    //    private ClaimWiseDocumentService claimWiseDocumentService;
//    private CalculationSheetService calculationSheetService;
    private ClaimHandlerService claimHandlerService;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        inspectionDetailsService = getInspectionDetailsBySession(request);
        motorEngineerService = getMotorEngineerBySession(request);
        callCenterService = getCallCenterServiceBySession(request);
        assessorAllocationService = getAssessorAllocationServiceServiceBySession(request);
//        requestAriService = getByRequestAri(request);
//        stDocumentService = getSftpDocumentService(request);
//        claimWiseDocumentService = getClaimWiseDocumentServiceBySession(request);
//        calculationSheetService = getCalculationSheetServiceBySession(request);
        claimHandlerService = getCallHandlerServiceBySession(request);

        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

        if (null != request.getParameter("TYPE") && (null == request.getSession().getAttribute("ME_PAGE_TYPE") || "LIST".equals(request.getSession().getAttribute("ME_PAGE_TYPE")))) {
            request.getSession().setAttribute("ME_PAGE_TYPE", request.getParameter("TYPE"));
        }
        try {
            switch (pathInfo) {
                case "/jobList":
                    jobList(request, response);
                    break;
                case "/viewEdit":
                    viewEdit(request, response);
                    break;
                case "/updateClaimAssignUser":
                    updateClaimAssignUser(request, response);
                    break;
                case "/updateSelfAssignRTEUser":
                    updateSelfAssignRTEUser(request, response);
                    break;
                case "/updateSelfAssignCallCenterUser":
                    updateSelfAssignCallCenterUser(request, response);
                    break;
                case "/updateJobBulkAssignUser":
                    updateJobBulkAssignUser(request, response);
                    break;
                case "/viewClaimList":
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionListChangeAssignUser.jsp");
                    break;
                case "/viewRteInspectionList":
                    UserDto user = getSessionUser(request);
                    session.setAttribute("userId", user.getUserId()); // replace yourUserObject with the actual user or data
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionPicker.jsp");
                    break;
                case "/getUserList":
                    getRteListWithAuthLevel(request, response);
                    break;
                case "/selectClaimNoAndJobNo":
                    setIdsToArray(request, response);
                    break;
                case "/getReassignUserList":
                    getReassignUserList(request, response);
                    break;
                case "/getReassignReportingUserList":
                    getReassignReportingUserList(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getReassignReportingUserList(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            List<PopupItemDto> rteListForReassign = motorEngineerService.getReportingRteListForReassign();
            json = gson.toJson(rteListForReassign);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void getReassignUserList(HttpServletRequest request, HttpServletResponse response) {
        String userName = null == request.getParameter("userName") ? AppConstant.STRING_EMPTY : request.getParameter("userName");
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            if (!Objects.equals(AppConstant.STRING_EMPTY, userName)) {
                List<PopupItemDto> rteListForReassign = motorEngineerService.getRteListForReassign(userName);
                json = gson.toJson(rteListForReassign);
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updateJobBulkAssignUser(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {

            String selectIds = request.getParameter("selectIds") == null ? AppConstant.STRING_EMPTY : request.getParameter("selectIds");
            String assignUser = request.getParameter("assignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");

            HashMap<String, String> claimWithJobNoMap = motorEngineerService.setClaimNoandJobNoToMap(selectIds);


            if (null != claimWithJobNoMap) {
                for (String refNo : claimWithJobNoMap.keySet()) {
                    String claimNo = claimWithJobNoMap.get(refNo);
                    motorEngineerService.updateAssignUser(Integer.parseInt(refNo), assignUser, user, Integer.parseInt(claimNo));
                }
            }

            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void setIdsToArray(HttpServletRequest request, HttpServletResponse response) {
        try {
            String selectClaimNoAndAssignUsers = request.getParameter(AppConstant.SELECT_CLAIM_NO_AND_JOB_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.SELECT_CLAIM_NO_AND_JOB_NO);

            List<String> list = motorEngineerService.getIdsToArray(selectClaimNoAndAssignUsers);

            Gson gson = new Gson();
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getRteListWithAuthLevel(HttpServletRequest request, HttpServletResponse response) {
        try {
            Gson gson = new Gson();

            List<String[]> rteListWithAuthLevel = motorEngineerService.getRteListWithAuthLevel();
            String json = gson.toJson(rteListWithAuthLevel);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    private void updateClaimAssignUser(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        String message;
        boolean successUpdate = false;
        UserDto user = getSessionUser(request);
        try {
            Integer refNo = request.getParameter("refNo") == null ? 0 : Integer.parseInt(request.getParameter("refNo"));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            String assignUser = request.getParameter("assignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");

            successUpdate = motorEngineerService.updateAssignUser(refNo, assignUser, user, claimNo);
            if (successUpdate) {
                message = "SUCCESS";
            } else {
                message = "FAIL";
            }
            json = gson.toJson(message);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }
    private void updateSelfAssignRTEUser(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            Integer refNo = request.getParameter("refNo") == null ? 0 : Integer.parseInt(request.getParameter("refNo"));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            String assignUser = request.getParameter("assignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");
            UserDto user = getSessionUser(request);

            // Call the method and get the status string
            String result = motorEngineerService.updateSelfAssignRTEUser(refNo, assignUser, user, claimNo);

            json = gson.toJson(result);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }
    private void updateSelfAssignCallCenterUser(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;

        try {
//            Integer refNo = request.getParameter("refNo") == null ? 0 : Integer.parseInt(request.getParameter("refNo"));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            String assignUser = request.getParameter("assignUser") == null ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");
            Integer requestedInspectionId = request.getParameter("requestedInspectionId") == null ? 0 : Integer.parseInt(request.getParameter("requestedInspectionId"));
            UserDto user = getSessionUser(request);

            // Updated service call with requestedInspectionId
            String result = motorEngineerService.updateSelfAssignCallCenterUser(assignUser, user, claimNo, requestedInspectionId);

            json = gson.toJson(result);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }



    private void jobList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String jobNo = request.getParameter(AppConstant.TXT_JOB_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String assignUser = request.getParameter(AppConstant.TXT_ASSIGN__USER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ASSIGN__USER);
        String inspectionId = request.getParameter(AppConstant.TXT_INSPECTION_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSPECTION_TYPE);


        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t2.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t2.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t2.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t2.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t2.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t2.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.job_id", jobNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.job_status", "23,4,29", FieldParameterDto.SearchType.NOT_IN, parameterList);

//            if (user.getN_accessusrtype() == 6) {
//                this.addFieldParameter("t3.assessor_code", empNo, FieldParameterDto.SearchType.Equal, parameterList);
//
//            }

            if (!"0".equals(inspectionId)) {
                this.addFieldParameter("t1.insepction_id ", inspectionId, FieldParameterDto.SearchType.IN, parameterList);
            }

            switch (orderColumnName) {
                case "refNo":
                    orderColumnName = " t1.ref_no";
                    break;
                case "claimNo":
                    orderColumnName = "t2.N_CLIM_NO";
                    break;
                case "policyNumber":
                    orderColumnName = "t2.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t2.V_VEHICLE_NO";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t2.V_COVER_NOTE_NO";
                    break;
                case "statusId":
                    orderColumnName = "t1.record_status";
                    break;
                case "jobNo":
                    orderColumnName = "t1.job_id";
                    break;
                case "inspectionType":
                    orderColumnName = "t3.inspection_type_id";
                    break;
                case "assignToRteDateTime":
                    orderColumnName = "t0.d_inpdatetime";
                    break;
                case "jobStatus":
                    orderColumnName = "t1.job_status";
                    break;
                case "estimationApprStatus":
                    orderColumnName = "t0.v_ass_esti_apr_status";
                    break;
                case "assFeeAprStatus":
                    orderColumnName = "t0.v_ass_fee_apr_status";
                    break;
                case "assignUser":
                    orderColumnName = "t0.v_assign_rte_user";
                    break;

            }
            String type = (String) request.getSession().getAttribute("ME_PAGE_TYPE");
            DataGridDto data = new DataGridDto();
            if ("1".equals(type)) {
                data = inspectionDetailsService.getSubmittedInspectionDetailsGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, user, assignUser, status);
            } else if ("2".equals(type)) {
                data = inspectionDetailsService.getFwdDesktopInspectionDetailsGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, user, 0);
            }

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void viewEdit(HttpServletRequest request, HttpServletResponse response) {
        InspectionDetailsDto inspectionDetailsDto = null;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        UserDto user = getSessionUser(request);
        try {
            Integer refNo = request.getParameter("P_N_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_REF_NO"));
            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            motorEngineerDetailsDto = motorEngineerService.search(refNo);

            AssessorAllocationDto assessorAllocationDto = assessorAllocationService.search(refNo);
            Integer claimNo = assessorAllocationDto.getClaimsDto().getClaimNo();


//            if (null == inspectionDetailsDto && 8 == assessorAllocationDto.getInspectionDto().getInspectionId()) {
//                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
//                motorEngineerDetailsDto.setClaimNo(claimNo);
//                motorEngineerDetailsDto.setRefNo(refNo);
//                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
//                motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//                populateInitTyreConditionList(motorEngineerDetailsDto, request.getParameterMap());
//                motorEngineerService.insertDesktopInitialRecords(motorEngineerDetailsDto, user);
//            }

            inspectionDetailsDto = inspectionDetailsService.search(refNo);
            motorEngineerDetailsDto = motorEngineerService.search(refNo);

            //Check whether Motor Engineer Detail Is saved in previous to this ref no
            if (null != motorEngineerDetailsDto && 0 < motorEngineerDetailsDto.getRefNo()) {
                request.setAttribute("ACTION", "UPDATE");
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
            } else {
                request.setAttribute("ACTION", "SAVE");
                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
                BeanUtilsBean.getInstance().copyProperties(motorEngineerDetailsDto, inspectionDetailsDto);
                cloneNestedBeans(motorEngineerDetailsDto, inspectionDetailsDto);
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
                motorEngineerDetailsDto.setClaimNo(claimNo);
                motorEngineerDetailsDto.setRefNo(refNo);
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
            }

            if (assessorAllocationDto.getInspectionDto().getInspectionId() == 8) {
                motorEngineerDetailsDto.setInspectionDto(assessorAllocationDto.getInspectionDto());
            }

            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claimNo);
            if (null != claimsDto) {
                assessorAllocationDto.setClaimsDto(claimsDto);
            }

            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
//            inspectionDetailsDto.setInputUserId(getSessionUser(request).getUserId());
            inspectionDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsService.getClaimDocumentTypeDtoList(AppConstant.ASSESSOR_DEPARTMENT_ID, assessorAllocationDto.getInspectionDto().getInspectionId()));
            motorEngineerDetailsDto.setClaimDocumentTypeDtoList(inspectionDetailsDto.getClaimDocumentTypeDtoList());

            //Third Party Details Load From Call Center
            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtos = callCenterService.getClaimThirdPartyDetailsGeneric(claimNo);

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosAssessor = inspectionDetailsService.getClaimThirdPartyDetailsGeneric(claimNo);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : claimThirdPartyDetailsGenericDtosAssessor) {
                if (claimThirdPartyDetailsGenericDto.getCcTpdId() > 0) {
                    claimThirdPartyDetailsGenericDto.setMappingId(claimThirdPartyDetailsGenericDto.getCcTpdId());
                    claimThirdPartyDetailsGenericDto.setMappingType("CALL_CENTER");
                }
            }

            List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtosMotorEngineer = motorEngineerService.getClaimThirdPartyDetailsGeneric(claimNo);

            Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = new TreeMap<>((String o1, String o2) -> {
                return o2.compareTo(o1);
            });

            List<ClaimThirdPartyDetailsGenericDto> list = new ArrayList<>();
            list.addAll(claimThirdPartyDetailsGenericDtos);
            list.addAll(claimThirdPartyDetailsGenericDtosAssessor);
            list.addAll(claimThirdPartyDetailsGenericDtosMotorEngineer);
            for (ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto : list) {
                thirdPartyAssessorMap.put(claimThirdPartyDetailsGenericDto.getType() + "_" + claimThirdPartyDetailsGenericDto.getTxnId(), claimThirdPartyDetailsGenericDto);
            }

            inspectionDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);
            motorEngineerDetailsDto.setThirdPartyAssessorMap(thirdPartyAssessorMap);

            List<PreviousClaimsDto> previousClaimList = inspectionDetailsService.getPreviousClaimList(null == claimsDto.getVehicleNo() ? AppConstant.STRING_EMPTY : claimsDto.getVehicleNo(), claimNo);
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionClaimList(claimNo, refNo);

            if (previousInspectionList != null && !previousInspectionList.isEmpty()) {
                for (PreviousClaimsDto previousClaimsDto : previousInspectionList.get(0).getList()) {
                    if ((previousClaimsDto.getInspectionTypeId() == 1 || previousClaimsDto.getInspectionTypeId() == 2)
                            && "A".equals(previousClaimsDto.getAssEstiAprStatus())
                            && "A".equals(previousClaimsDto.getAssFeeAprStatus())) {
                        request.setAttribute("PREVIOUS_PAV", previousClaimsDto.getInspectionDetailsPav());
                    }
                }
            }

            List<SpecialRemarkDto> specialRemarkDtos = inspectionDetailsService.searchRemarksByClaimNo(claimNo, AppConstant.ASSESSOR_DEPARTMENT_ID);
            specialRemarkDtos.addAll(motorEngineerService.searchRemarksByClaimNo(claimNo, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID));
            request.setAttribute(AppConstant.REMARK_LIST, specialRemarkDtos);
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, previousClaimList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, AppConstant.NO);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        if (null != motorEngineerDetailsDto.getDesktopInspectionDetailsDto() && motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
            motorEngineerDetailsDto = motorEngineerService.getLatestUpdateOnSite(motorEngineerDetailsDto);
        }
        request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.getSession().setAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);
        request.setAttribute(AppConstant.SUCCESS_MESSAGE, AppConstant.STRING_EMPTY);
        request.setAttribute(AppConstant.ERROR_MESSAGE, AppConstant.STRING_EMPTY);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/assessorAllocationInspectionDetails.jsp");
    }

    private void populateInitTyreConditionList(MotorEngineerDetailsDto motorEngineerDetailsDto, Map<String, String[]> parameterMap) {
        List<TireCondtionDto> tireCondtionDtoList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            TireCondtionDto tireCondtionDto = new TireCondtionDto();
            tireCondtionDto.setRefNo(motorEngineerDetailsDto.getRefNo());
            tireCondtionDto.setClaimsDto(motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto());
            String rf = null;
            String lf = null;
            String rr = null;
            String rl = null;
            String rri = null;
            String lri = null;
            String other = null;
            if (0 == i) {
                rf = "N/A";
                lf = "N/A";
                rr = "N/A";
                rl = "N/A";
                rri = "N/A";
                lri = "N/A";
                other = "N/A";
            } else {
                rf = "";
                lf = "";
                rr = "";
                rl = "";
                rri = "";
                lri = "";
                other = "";
            }
            tireCondtionDto.setPosition(i);
            tireCondtionDto.setRf(rf);
            tireCondtionDto.setLf(lf);
            tireCondtionDto.setRr(rr);
            tireCondtionDto.setRl(rl);
            tireCondtionDto.setRri(rri);
            tireCondtionDto.setLri(lri);
            tireCondtionDto.setOther(other);
            tireCondtionDtoList.add(tireCondtionDto);
        }
        motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtoList);
    }

    private void cloneNestedBeans(MotorEngineerDetailsDto motorEngineerDetailsDto, InspectionDetailsDto inspectionDetailsDto) throws InvocationTargetException, IllegalAccessException {

        BeanUtilsBean beanUtilsBean = BeanUtilsBean.getInstance();
        motorEngineerDetailsDto.setOnSiteInspectionDetailsDto(new OnSiteInspectionDetailsDto());
        motorEngineerDetailsDto.setGarageInspectionDetailsDto(new GarageInspectionDetailsDto());
        motorEngineerDetailsDto.setAriInspectionDetailsDto(new ARIInspectionDetailsDto());
        motorEngineerDetailsDto.setDrSuppInspectionDetailsDto(new DrSupplementaryInspectionDetailsDto());
        motorEngineerDetailsDto.setDesktopInspectionDetailsDto(new DesktopInspectionDetailsDto());

        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto(), inspectionDetailsDto.getOnSiteInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getGarageInspectionDetailsDto(), inspectionDetailsDto.getGarageInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getAriInspectionDetailsDto(), inspectionDetailsDto.getAriInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto(), inspectionDetailsDto.getDrSuppInspectionDetailsDto());
        beanUtilsBean.copyProperties(motorEngineerDetailsDto.getDesktopInspectionDetailsDto(), inspectionDetailsDto.getDesktopInspectionDetailsDto());
    }
}
