package com.misyn.mcms.claim.controller.callcenter.validator;

import com.misyn.mcms.claim.dto.AssessorAllocationDto;
import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.ErrorMessageDto;
import com.misyn.mcms.utility.AppConstant;

public class AssessorAllocationValidator implements Validator<ClaimsDto> {


    public ErrorMessageDto validateAssessorAllocation(AssessorAllocationDto assessorAllocationDto) {
        DoValidate<AssessorAllocationDto> doValidate = new DoValidate<>();
        ErrorMessageDto errorMessageDto = doValidate.validate(assessorAllocationDto, 2);

        if (null == assessorAllocationDto.getInspectionDto() || assessorAllocationDto.getInspectionDto().getInspectionId() == 0) {
            errorMessageDto.setErrorCode(520);
            errorMessageDto.setMessage("Inspection can not be empty");
            errorMessageDto.setDtoFieldName("inspectionDto");
            errorMessageDto.setFormFieldName("Inspection");
        } else if (null != assessorAllocationDto.getInspectionDto() && assessorAllocationDto.getInspectionDto().getInspectionId() == 11 && (null == assessorAllocationDto.getInspectionReasonDto() || assessorAllocationDto.getInspectionReasonDto().getReasonId() == 0)) {
            errorMessageDto.setErrorCode(520);
            errorMessageDto.setMessage("RTE can not be empty");
            errorMessageDto.setDtoFieldName("rteCode");
            errorMessageDto.setFormFieldName("RTE");
        } else if (null != assessorAllocationDto.getInspectionDto() && assessorAllocationDto.getInspectionDto().getInspectionId() == 8 && (null == assessorAllocationDto.getInspectionReasonDto() || assessorAllocationDto.getRteCode().isEmpty())) {
            errorMessageDto.setErrorCode(520);
            errorMessageDto.setMessage("Inspection Reason can not be empty");
            errorMessageDto.setDtoFieldName("inspectionReasonDto");
            errorMessageDto.setFormFieldName("Reason");

        } else if (assessorAllocationDto.getInspectionDto().getInspectionId() != 11 && assessorAllocationDto.getInspectionDto().getInspectionId() != 8 && assessorAllocationDto.getInspectionDto().getInspectionId() != 12) {
            if (assessorAllocationDto.getPlaceOfinspection().isEmpty()) {
                errorMessageDto.setErrorCode(520);
                errorMessageDto.setMessage("Place of inspection can not be empty");
                errorMessageDto.setDtoFieldName("placeOfinspection");
                errorMessageDto.setFormFieldName("Place of Inspection");

            } else if (null == assessorAllocationDto.getAssessorDto() || assessorAllocationDto.getAssessorDto().getCode().isEmpty()) {
                errorMessageDto.setErrorCode(520);
                errorMessageDto.setMessage("Assessor can not be empty");
                errorMessageDto.setDtoFieldName("assessorDto");
                errorMessageDto.setFormFieldName("Assessor");
            } else if (!assessorAllocationDto.getResponseValue().isEmpty()) {
                int responseValue = Integer.parseInt(assessorAllocationDto.getResponseValue());
                if (AppConstant.ACCEPTED == responseValue) {
                    if (assessorAllocationDto.getAssigningDate().isEmpty()) {
                        errorMessageDto.setErrorCode(520);
                        errorMessageDto.setMessage("Assign Date & Time can not be empty");
                        errorMessageDto.setDtoFieldName("assigningDate");
                        errorMessageDto.setFormFieldName("Assign Date & Time");
                    }
                } else if (AppConstant.REJECTED == responseValue) {
                    if (null == assessorAllocationDto.getRejectReasonDto() || assessorAllocationDto.getRejectReasonDto().getReasonId() == 0) {
                        errorMessageDto.setErrorCode(520);
                        errorMessageDto.setMessage("Reject reason can not be empty");
                        errorMessageDto.setDtoFieldName("rejectReasonDto");
                        errorMessageDto.setFormFieldName("Reject Reason");
                    }


                }

            }


        }


        return errorMessageDto;
    }


    @Override
    public void validate(ClaimsDto claimsDto) {

    }
}

