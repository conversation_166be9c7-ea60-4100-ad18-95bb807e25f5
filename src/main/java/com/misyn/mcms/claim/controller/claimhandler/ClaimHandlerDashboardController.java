package com.misyn.mcms.claim.controller.claimhandler;

import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimDashboardDto;
import com.misyn.mcms.claim.dto.ClaimUserTypeDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.ClaimHandlerDashboardService;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDate;

@WebServlet(name = "ClaimHandlerDashboardController", urlPatterns = "/ClaimHandlerDashboardController/*")
public class ClaimHandlerDashboardController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimHandlerDashboardController.class);
    private ClaimHandlerDashboardService claimHandlerDashboardService;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        claimHandlerDashboardService = getClaimHandlerDashboardService(request);
        ClaimUserTypeDto claimUserTypeDto;

        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/dashboard":
                    viewDashboard(request, response, user);
                    break;

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewDashboard(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        HttpSession session = request.getSession();
        LocalDate date = LocalDate.now();
        String thisMonthFirstDate = date.withDayOfMonth(1).toString();
        String todayDate = date.toString();
        try {
            String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? thisMonthFirstDate : request.getParameter(AppConstant.TXT_FROM_DATE);
            String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? todayDate :
                    request.getParameter(AppConstant.TXT_TO_DATE);

            ClaimDashboardDto claimDashboardDto = claimHandlerDashboardService.getCountFromClaimStatusDetail(fromDate, toDate);

            request.setAttribute(AppConstant.CLAIM_DASHBOARD_DTO, claimDashboardDto);
            request.setAttribute(AppConstant.TXT_FROM_DATE, fromDate);
            request.setAttribute(AppConstant.TXT_TO_DATE, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/dashboard.jsp");
    }
}
