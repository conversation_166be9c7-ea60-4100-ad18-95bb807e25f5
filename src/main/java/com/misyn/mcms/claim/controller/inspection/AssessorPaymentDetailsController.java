package com.misyn.mcms.claim.controller.inspection;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "AssessorPaymentDetailsController", urlPatterns = "/AssessorPaymentDetailsController/*")
public class AssessorPaymentDetailsController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorPaymentDetailsController.class);
    private int draw = 1;
    private InspectionDetailsService inspectionDetailsService = null;
    private CallCenterService callCenterService = null;
    private AssessorAllocationService assessorAllocationService = null;
    private RequestAriService requestAriService = null;
    private StorageService stDocumentService = null;
    private ClaimHandlerService claimHandlerService = null;
    private AssessorPaymentDetailsService assessorPaymentDetailsService = null;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        inspectionDetailsService = getInspectionDetailsBySession(request);
        callCenterService = getCallCenterServiceBySession(request);
        assessorAllocationService = getAssessorAllocationServiceServiceBySession(request);
        requestAriService = getByRequestAri(request);
        stDocumentService = getSftpDocumentService(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        assessorPaymentDetailsService = getAssessorPaymentServiceBySession(request);
        ClaimsDto claimsDto = new ClaimsDto();
        Integer claimId = AppConstant.ZERO_INT;
        String historyRecord = AppConstant.NO;


        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

        try {
            switch (pathInfo) {
                case "/assessorPayments":
                    assessorPayments(request, response);
                    break;
                case "/saveToFinnace":
                    saveToFinnace(request, response);
                    break;
                case "/reject":
                    reject(request, response);
                    break;
                case "/search":
                    search(request, response);
                    break;
                case "/loadPaginatedAssessorPayments":
                    loadPaginatedAssessorPayments(request, response);
                    break;

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    private void loadPaginatedAssessorPayments(HttpServletRequest request, HttpServletResponse response) {
        String json = "";
        Gson gson = new Gson();

        boolean isApprovalTeam = false;

        int start = Integer.parseInt(request.getParameter(AppConstant.START));
        int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
        String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
        String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
        String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

        String payemtStatus = null == request.getParameter(AppConstant.PAYMENT_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PAYMENT_TYPE);
        String status = null == request.getParameter(AppConstant.STATUS) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.STATUS);
        String name = null == request.getParameter(AppConstant.ASSESSOR_NAME) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSESSOR_NAME);
        String toDate = null == request.getParameter(AppConstant.TXT_TO_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String fromDate = null == request.getParameter(AppConstant.TXT_FROM_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String vehicleNumber = null == request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String claimNumber = null == request.getParameter(AppConstant.TXT_CLAIM_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_NUMBER);
        String jobNumber = null == request.getParameter(AppConstant.TXT_JOB_NO) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String inspectionType = null == request.getParameter(AppConstant.TXT_INSPECTION_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSPECTION_TYPE);
        String rteCode = null == request.getParameter(AppConstant.RTE_CODE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.RTE_CODE);
        String claimType = null == request.getParameter("claimType") ? AppConstant.STRING_EMPTY : request.getParameter("claimType");
        String policyChannelType = null == request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        UserDto user = getSessionUser(request);

        try {
            if (user.getAccessUserType() == 42 || user.getAccessUserType() == 43 || user.getAccessUserType() == 62 || user.getAccessUserType() == 63) {
                isApprovalTeam = true;
            }

            Map<String, Object> parameterList = new LinkedHashMap<>();
            parameterList.put("paymentStatus", payemtStatus);
            parameterList.put("status", status);
            parameterList.put("name", name);
            parameterList.put("vehicleNumber", vehicleNumber);
            parameterList.put("claimNumber", claimNumber);
            parameterList.put("jobNumber", jobNumber);
            parameterList.put("inspectionType", inspectionType);
            parameterList.put("rteCode", rteCode);
            parameterList.put("claimType", claimType);
            parameterList.put("policyChannelType", policyChannelType);
            parameterList.put("isApprovalTeam", isApprovalTeam);
            DataGridDto data = assessorPaymentDetailsService.getPaymentDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            json = gson.toJson(data);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void assessorPayments(HttpServletRequest request, HttpServletResponse response) {
        String fromDate = Utility.sysDate("yyyy-MM").concat("-01");
        String toDate = Utility.sysDate(AppConstant.DATE_FORMAT);
        UserDto user = getSessionUser(request);
        boolean isApprovalTeam = false;
        try {
            if (user.getAccessUserType() == 42 || user.getAccessUserType() == 43 || user.getAccessUserType() == 62 || user.getAccessUserType() == 63) {
                isApprovalTeam = true;
            }
//            BigDecimal totalAmount = assessorPaymentDetailsService.getTotalAmount("", "P", "", fromDate, toDate, AppConstant.STRING_EMPTY, AppConstant.STRING_EMPTY, AppConstant.STRING_EMPTY, AppConstant.STRING_EMPTY, AppConstant.STRING_EMPTY, AppConstant.STRING_EMPTY, AppConstant.STRING_EMPTY);
            request.setAttribute(AppConstant.PAYMENT_TYPE, "");
            request.setAttribute(AppConstant.STATUS, "P");
            request.setAttribute(AppConstant.TXT_TO_DATE, toDate);
            request.setAttribute(AppConstant.TXT_FROM_DATE, fromDate);
//            request.setAttribute(AppConstant.TOTAL_AMOUNT, Utility.formatCurrency(totalAmount == null ? BigDecimal.ZERO : totalAmount));
            request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
            request.setAttribute(AppConstant.POLICY_CHANNEL_TYPE, AppConstant.STRING_EMPTY);
            request.setAttribute("IS_APPROVAL_TEAM", isApprovalTeam);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/approval.jsp");
    }

    private void saveToFinnace(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String ids = request.getParameter("selectedIds");
        String errorMessage;
        String successMessage;
        boolean isApprovalTeam = false;
        String payemtStatus = null == request.getParameter(AppConstant.PAYMENT_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PAYMENT_TYPE);
        String status = null == request.getParameter(AppConstant.STATUS) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.STATUS);
        String name = null == request.getParameter(AppConstant.ASSESSOR_NAME) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSESSOR_NAME);
        String toDate = null == request.getParameter(AppConstant.TXT_TO_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String fromDate = null == request.getParameter(AppConstant.TXT_FROM_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String vehicleNumber = null == request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String claimNumber = null == request.getParameter(AppConstant.TXT_CLAIM_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_NUMBER);
        String jobNumber = null == request.getParameter(AppConstant.TXT_JOB_NO) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String inspectionType = null == request.getParameter(AppConstant.TXT_INSPECTION_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String rteCode = null == request.getParameter(AppConstant.RTE_CODE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.RTE_CODE);
        String claimType = null == request.getParameter("claimType") ? AppConstant.STRING_EMPTY : request.getParameter("claimType");
        String policyChannelType = null == request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        if (user.getAccessUserType() == 42 || user.getAccessUserType() == 43 || user.getAccessUserType() == 62 || user.getAccessUserType() == 63) {
            isApprovalTeam = true;
        }
        try {
            boolean updated = assessorPaymentDetailsService.updateStausByIdAndStatus(ids, user, fromDate, toDate);
            if (updated) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Saved");
            } else {
                request.setAttribute(AppConstant.ERROR_MESSAGE, "Faild");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
//            BigDecimal totalAmount = assessorPaymentDetailsService.getTotalAmount(payemtStatus, status, name, fromDate, toDate, vehicleNumber, claimNumber, jobNumber, inspectionType, rteCode, claimType, policyChannelType);
//            request.setAttribute(AppConstant.TOTAL_AMOUNT, Utility.formatCurrency(totalAmount == null ? BigDecimal.ZERO : totalAmount));
            request.setAttribute(AppConstant.PAYMENT_TYPE, payemtStatus);
            request.setAttribute(AppConstant.STATUS, status);
            request.setAttribute(AppConstant.ASSESSOR_NAME, name);
            request.setAttribute(AppConstant.TXT_TO_DATE, toDate);
            request.setAttribute(AppConstant.TXT_FROM_DATE, fromDate);
            request.setAttribute(AppConstant.TXT_VEHICLE_NUMBER, vehicleNumber);
            request.setAttribute(AppConstant.TXT_CLAIM_NUMBER, claimNumber);
            request.setAttribute(AppConstant.TXT_JOB_NO, jobNumber);
            request.setAttribute(AppConstant.TXT_INSPECTION_TYPE, inspectionType);
            request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
            request.setAttribute(AppConstant.SEARCH_RTE_CODE, rteCode);
            request.setAttribute(AppConstant.ASSESSOR_LIST, assessorPaymentDetailsService.getAssessorListByReportingRte(rteCode));
            request.setAttribute(AppConstant.SEARCH_ASSESSOR_NAME, name);
            request.setAttribute(AppConstant.POLICY_CHANNEL_TYPE, policyChannelType);
            request.setAttribute("IS_APPROVAL_TEAM", isApprovalTeam);
            request.setAttribute("claimType", claimType);

            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/approval.jsp");
        }

    }

    private void reject(HttpServletRequest request, HttpServletResponse response) {

        String ids = request.getParameter("selectedIds");
        String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        String errorMessage;
        String successMessage;
        boolean isApprovalTeam = false;
        String payemtStatus = null == request.getParameter(AppConstant.PAYMENT_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PAYMENT_TYPE);
        String status = null == request.getParameter(AppConstant.STATUS) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.STATUS);
        String name = null == request.getParameter(AppConstant.ASSESSOR_NAME) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSESSOR_NAME);
        String toDate = null == request.getParameter(AppConstant.TXT_TO_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String fromDate = null == request.getParameter(AppConstant.TXT_FROM_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String vehicleNumber = null == request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String claimNumber = null == request.getParameter(AppConstant.TXT_CLAIM_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_NUMBER);
        String jobNumber = null == request.getParameter(AppConstant.TXT_JOB_NO) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String inspectionType = null == request.getParameter(AppConstant.TXT_INSPECTION_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String rteCode = null == request.getParameter(AppConstant.RTE_CODE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.RTE_CODE);
        String claimType = null == request.getParameter("claimType") ? AppConstant.STRING_EMPTY : request.getParameter("claimType");
        String policyChannelType = null == request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        try {
            UserDto user = getSessionUser(request);
            assessorPaymentDetailsService.updateRejectedStausByIdAndStatus(ids, remark, user);
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully rejected");

            if (user.getAccessUserType() == 42 || user.getAccessUserType() == 43 || user.getAccessUserType() == 62 || user.getAccessUserType() == 63) {
                isApprovalTeam = true;
            }
        } catch (ErrorMsgException e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Failed");
        } finally {
//            BigDecimal totalAmount = assessorPaymentDetailsService.getTotalAmount(payemtStatus, status, name, fromDate, toDate, vehicleNumber, claimNumber, jobNumber, inspectionType, rteCode, claimType, policyChannelType);
            request.setAttribute(AppConstant.PAYMENT_TYPE, payemtStatus);
            request.setAttribute(AppConstant.STATUS, status);
            request.setAttribute(AppConstant.ASSESSOR_NAME, name);
            request.setAttribute(AppConstant.TXT_TO_DATE, toDate);
            request.setAttribute(AppConstant.TXT_FROM_DATE, fromDate);
            request.setAttribute(AppConstant.TXT_VEHICLE_NUMBER, vehicleNumber);
            request.setAttribute(AppConstant.TXT_CLAIM_NUMBER, claimNumber);
            request.setAttribute(AppConstant.TXT_JOB_NO, jobNumber);
            request.setAttribute(AppConstant.TXT_INSPECTION_TYPE, inspectionType);
//            request.setAttribute(AppConstant.TOTAL_AMOUNT, Utility.formatCurrency(totalAmount == null ? BigDecimal.ZERO : totalAmount));
            request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
            request.setAttribute(AppConstant.SEARCH_RTE_CODE, rteCode);
            request.setAttribute(AppConstant.ASSESSOR_LIST, assessorPaymentDetailsService.getAssessorListByReportingRte(rteCode));
            request.setAttribute(AppConstant.SEARCH_ASSESSOR_NAME, name);
            request.setAttribute(AppConstant.POLICY_CHANNEL_TYPE, policyChannelType);
            request.setAttribute("IS_APPROVAL_TEAM", isApprovalTeam);
            request.setAttribute("claimType", claimType);

            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/approval.jsp");
        }

    }


    private void search(HttpServletRequest request, HttpServletResponse response) {

        boolean isApprovalTeam = false;
        String payemtStatus = null == request.getParameter(AppConstant.PAYMENT_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PAYMENT_TYPE);
        String status = null == request.getParameter(AppConstant.STATUS) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.STATUS);
        String name = null == request.getParameter(AppConstant.ASSESSOR_NAME) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.ASSESSOR_NAME);
        String toDate = null == request.getParameter(AppConstant.TXT_TO_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String fromDate = null == request.getParameter(AppConstant.TXT_FROM_DATE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String vehicleNumber = null == request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String claimNumber = null == request.getParameter(AppConstant.TXT_CLAIM_NUMBER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_NUMBER);
        String jobNumber = null == request.getParameter(AppConstant.TXT_JOB_NO) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_JOB_NO);
        String inspectionType = null == request.getParameter(AppConstant.TXT_INSPECTION_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSPECTION_TYPE);
        String rteCode = null == request.getParameter(AppConstant.RTE_CODE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.RTE_CODE);
        String claimType = null == request.getParameter("claimType") ? AppConstant.STRING_EMPTY : request.getParameter("claimType");
        String policyChannelType = null == request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        UserDto user = getSessionUser(request);
        try {
            if (user.getAccessUserType() == 42 || user.getAccessUserType() == 43 || user.getAccessUserType() == 62 || user.getAccessUserType() == 63) {
                isApprovalTeam = true;
            }
//            BigDecimal totalAmount = assessorPaymentDetailsService.getTotalAmount(payemtStatus, status, name, fromDate, toDate, vehicleNumber, claimNumber, jobNumber, inspectionType, rteCode, claimType, policyChannelType);
            request.setAttribute(AppConstant.PAYMENT_TYPE, payemtStatus);
            request.setAttribute(AppConstant.STATUS, status);
            request.setAttribute(AppConstant.TXT_TO_DATE, toDate);
            request.setAttribute(AppConstant.TXT_FROM_DATE, fromDate);
            request.setAttribute(AppConstant.TXT_VEHICLE_NUMBER, vehicleNumber);
            request.setAttribute(AppConstant.TXT_CLAIM_NUMBER, claimNumber);
            request.setAttribute(AppConstant.TXT_JOB_NO, jobNumber);
            request.setAttribute(AppConstant.TXT_INSPECTION_TYPE, inspectionType);
//            request.setAttribute(AppConstant.TOTAL_AMOUNT, Utility.formatCurrency(totalAmount));
            request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
            request.setAttribute(AppConstant.SEARCH_RTE_CODE, rteCode);
            request.setAttribute(AppConstant.ASSESSOR_LIST, rteCode.isEmpty() ? null : assessorPaymentDetailsService.getAssessorListByReportingRte(rteCode));
            request.setAttribute(AppConstant.SEARCH_ASSESSOR_NAME, name);
            request.setAttribute(AppConstant.POLICY_CHANNEL_TYPE, policyChannelType);
            request.setAttribute("IS_APPROVAL_TEAM", isApprovalTeam);
            request.setAttribute("claimType", claimType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/approval.jsp");
    }

    private void searchFields(HttpServletRequest request) {
        request.setAttribute(AppConstant.PAYMENT_TYPE, "");
        request.setAttribute(AppConstant.STATUS, "P");
        request.setAttribute(AppConstant.ASSESSOR_NAME, "");
    }


}
