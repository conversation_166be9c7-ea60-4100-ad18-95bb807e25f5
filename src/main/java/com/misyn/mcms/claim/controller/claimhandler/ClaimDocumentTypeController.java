package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.report.template.*;
import com.misyn.mcms.claim.service.ClaimDocumentTypeService;
import com.misyn.mcms.claim.service.ClaimHandlerService;
import com.misyn.mcms.claim.service.ClaimWiseDocumentService;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.claim.service.impl.ClaimWiseDocumentServiceImpl;
import com.misyn.mcms.claim.service.impl.StorageServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@WebServlet(name = "ClaimDocumentTypeController", urlPatterns = "/ClaimDocumentTypeController/*")
public class ClaimDocumentTypeController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimDocumentTypeController.class);
    private ClaimHandlerService claimHandlerService;
    private StorageService storageService;
    private ClaimWiseDocumentService claimWiseDocumentService;
    private ClaimDocumentTypeService claimDocumentTypeService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        claimHandlerService = getCallHandlerServiceBySession(request);

        if (storageService == null) {
            storageService = new StorageServiceImpl();
            session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
        }
        if (claimWiseDocumentService == null) {
            claimWiseDocumentService = new ClaimWiseDocumentServiceImpl();
        }

        claimDocumentTypeService = getClaimDocumentTypeServiceBySession(request);
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/save":
                    saveClaimDocumentType(request, response, user);
                    break;
                case "/search":
                    searchClaimDocumentType(request, response);
                    break;
                case "/searchAllDocType":
                    searchAllClaimDocumentType(request, response);
                    break;
                case "/viewDocType":
//                    setClaimUserLeavePopupListValues(request);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/systemParameter/claimDocumentType.jsp");
                    break;
                case "/docTypeName":
                    setClaimDepartmentPopupListValues(request, response);
                    break;
                case "/docReqName":
                    setClaimDocumentReqFormListValues(request, response);
                    break;
                case "/validateDocTypeName":
                    validateDocTypeName(request, response);
                    break;
                case "/getLatterType":
                    getLatterType(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void validateDocTypeName(HttpServletRequest request, HttpServletResponse response) {
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            BeanUtils.populate(claimDocumentTypeDto, request.getParameterMap());
            String docName = request.getParameter("documentTypeName") == null ? AppConstant.STRING_EMPTY : request.getParameter("documentTypeName");
            Integer docId = Integer.parseInt(request.getParameter("documentTypeId") == null ? AppConstant.ZERO : request.getParameter("documentTypeId"));
            String docTypeName = claimDocumentTypeService.validateDoctypeName(docName);
            if (null == docTypeName) {
                json = ("Name Already exists");
                json = gson.toJson(json);
                printWriter(request, response, json);
            } else {

            }

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void saveClaimDocumentType(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
        ClaimDocumentTypeDto claimDocumentType;
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            BeanUtils.populate(claimDocumentTypeDto, request.getParameterMap());
            claimDocumentTypeDto.setInpDateTime(Utility.sysDateTime());
            claimDocumentTypeDto.setInpUserId(user.getUserId());
            claimDocumentType = claimDocumentTypeService.save(claimDocumentTypeDto);
            if (null != claimDocumentType) {
                json = ("Successfully Saved");
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }

    }

    private void searchClaimDocumentType(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            setClaimPanelUserPopupListValues(request);
            int id = Integer.parseInt(request.getParameter("id"));
            ClaimDocumentTypeDto claimDocumentTypeDto = claimDocumentTypeService.searchDocTypeId(id);
            request.setAttribute("claimUser", claimDocumentTypeDto);
            json = gson.toJson(claimDocumentTypeDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            //  requestDispatcher(httpServletRequest, httpServletResponse, "/WEB-INF/jsp/claim/systemParameter/claimPanelUserView.jsp");
        }
    }

    private void searchAllClaimDocumentType(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        //   setClaimPanelUserPopupListValues(request);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            //  this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);


            switch (orderColumnName) {
                case "documentTypeId":
                    orderColumnName = "N_DOC_TYPE_ID";
                    break;
                case "documentTypeName":
                    orderColumnName = "V_DOC_TYPE_NAME";
                    break;
                case "departmentId":
                    orderColumnName = "N_DEPARTMENT_ID";
                    break;
                case "isMandatory":
                    orderColumnName = "V_IS_MANDATORY";
                    break;
                case "isPartialLoss":
                    orderColumnName = "V_IS_PARTIAL_LOSS";
                    break;
                case "isTotLoss":
                    orderColumnName = "V_IS_TOT_LOSS";
                    break;
                case "isLumpSum":
                    orderColumnName = "V_IS_LUMP_SUM";
                    break;
                case "isThirdPartyPropVehicle":
                    orderColumnName = "V_IS_THIRD_PARTY_PROP_VEHICLE";
                    break;
                case "isThirdPartyDeath":
                    orderColumnName = "V_IS_THIRD_PARTY_DEATH";
                    break;
                case "isThirdPartyInjuries":
                    orderColumnName = "V_IS_THIRD_PARTY_INJURIES";
                    break;
                case "docReqFrom":
                    orderColumnName = "N_DOC_REQ_FROM";
                    break;
                case "reminderDocDisplayName":
                    orderColumnName = "V_REMIN_DOC_DISPLAY_NAME";
                    break;
                case "recStatus":
                    orderColumnName = "V_REC_STATUS";
                    break;
                case "inpUserId":
                    orderColumnName = "V_INP_USER_ID";
                    break;
                case "inpDateTime":
                    orderColumnName = "D_INP_DATE_TIME";
                    break;


            }
            DataGridDto data = claimDocumentTypeService.getUserDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    //get Rejection Letter Type to Attach an E file Panel
    private void getLatterType(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);
            PdfTemplate pdfTemplate = null;
            ByteArrayOutputStream baosPdf = null;
            UserDto user = getSessionUser(request);

            switch (claimHandlerDto.getRejectionLatterType()) {
                case 1:
                    pdfTemplate = new DrunkDriveLetterTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(claimHandlerDto, user);
                    break;
                case 2:
                    pdfTemplate = new AuthenticityLetterTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(claimHandlerDto, user);
                    break;
                case 3:
                    pdfTemplate = new DriverChangeLetterTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(claimHandlerDto, user);

                    break;
                case 4:
                    pdfTemplate = new ExternalImpacttoScopeofPolicyLetterTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(claimHandlerDto, user);
                    break;
                case 5:
                    pdfTemplate = new HireLetterTemplate();
                    baosPdf = pdfTemplate.getGenerateLetter(claimHandlerDto, user);
                    break;
            }
            setPdfByteArray(baosPdf, request, response, pdfTemplate, user);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    //convert letter type to inputsm
    void setPdfByteArray(ByteArrayOutputStream baosPdf, HttpServletRequest request, HttpServletResponse response, PdfTemplate pdfTemplate, UserDto user) {

        long requestFormId;

        Gson gson = new Gson();
        String json;
        boolean isSuccess;
        try {
            Date date = new Date();
            HttpSession session = request.getSession();
            ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
            String userid = user.getUserId();

            if (storageService == null) {
                storageService = new StorageServiceImpl();
                session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
            }
            session.getAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE);
            String fileName = Utility.sysDate(AppConstant.DATE_TIME_FORMAT_INT)
                    .concat((AppConstant.STRING_UNDERSCORE_SIGN)
                            .concat(userid)
                            .concat(AppConstant.STRING_DOT)
                            .concat(AppConstant.PDF));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            Integer documentTypeId = AppConstant.REJECTION_LETTER_TYPE_NO;
            Integer departmentId = AppConstant.CLAIM_HANDLER_DEPARTMENT;
            requestFormId = AppConstant.ZERO_VALUE;

            ByteArrayInputStream inputStream = new ByteArrayInputStream(baosPdf.toByteArray());
            claimDocumentDto.setClaimNo(claimNo);
            claimDocumentDto.setInputStream(inputStream);
            claimDocumentDto.setDocumentTypeId(documentTypeId);
            claimDocumentDto.setInpUser(user.getUserId());
            claimDocumentDto.setInpDateTime(Utility.sysDateTime());
            claimDocumentDto.setDocumentName(fileName);
            claimDocumentDto.setDepartmentId(departmentId);
            SimpleDateFormat formatter = new SimpleDateFormat(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT);
            String dateFormat = formatter.format(date);

            //save inputStream Document
            storageService.uploadDocument(requestFormId, claimDocumentDto, user);
            //Update Mandatory To Show Document On E-File Panel
            claimWiseDocumentService.updateIsMandatory(user.getUserId(), dateFormat, claimNo, documentTypeId);
            claimHandlerService.attachRejectionFileOnOtherSelect(claimNo);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }
}
