package com.misyn.mcms.claim.controller;

import com.google.gson.Gson;
import com.misyn.mcms.admin.UserRights;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.*;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class BaseController extends HttpServlet {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseController.class);
    protected DbRecordCommonFunction recordCommonFunction = DbRecordCommonFunction.getInstance();
    private ClaimLockService claimLockService = null;
    private ClaimHandlerService claimHandlerService = null;
    private SupplyOrderService supplyOrderService = new SupplyOrderServiceImpl();

    protected void addFieldParameter(String dbFieldName, String value, FieldParameterDto.SearchType searchType, List<FieldParameterDto> parameterList) {
        if (!AppConstant.STRING_EMPTY.equals(value)) {
            FieldParameterDto fieldParameterDTO = new FieldParameterDto();
            fieldParameterDTO.setDbFieldName(dbFieldName);
            fieldParameterDTO.setFieldValue(value);
            if (searchType.equals(FieldParameterDto.SearchType.NOT_IN)
                    || searchType.equals(FieldParameterDto.SearchType.IN)) {
                fieldParameterDTO.setStringType(false);

            } else {
                fieldParameterDTO.setStringType(true);
            }
            fieldParameterDTO.setSearchType(searchType);
            parameterList.add(fieldParameterDTO);
        }
    }

    protected void requestDispatcher(HttpServletRequest request, HttpServletResponse response, String url) {
        try {
            RequestDispatcher rd = request.getRequestDispatcher(url);
            rd.forward(request, response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected void printWriter(HttpServletRequest request, HttpServletResponse response, String printString) {
        try (PrintWriter out = response.getWriter()) {
            out.println(printString);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected void getCityList(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String divisinCode = request.getParameter("divisonCode");
        List<CityDto> cityListByDistrictCode = null;
        HttpSession session = request.getSession();
        CityService cityService = getCityServiceBySession(request);

        try {
            cityListByDistrictCode = cityService.getCityListByDistrictCode(divisinCode);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        json = gson.toJson(cityListByDistrictCode);
        printWriter(request, response, json);

    }

    protected void getTableValueFromGivenCriteria(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        String tableName = request.getParameter("tableName");
        String valueField = request.getParameter("valueField");
        String searchFiled = request.getParameter("searchFiled");
        String searchValue = request.getParameter("searchValue");
        String value = null;
        DbRecordCommonFunction dbRecordCommonFunction = getDbRecordCommonFunctionBySession(request);
        try {
            value = dbRecordCommonFunction.getValue(tableName, valueField, searchFiled, searchValue);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        json = gson.toJson(value);
        printWriter(request, response, json);
    }

    protected void getAssessorList(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String divisinCode = request.getParameter("divisonCode");
        List<AssessorDto> list = null;
        AssessorService assessorService = getAssessorServiceBySession(request);

        try {
            list = assessorService.getAssessorListByDivisionCode(divisinCode);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        json = gson.toJson(list);
        printWriter(request, response, json);

    }

    // create beans
    protected CityService getCityServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        CityService cityService = (CityService) session.getAttribute(AppConstant.CITY_SERVICE);
        if (null == cityService) {
            cityService = new CityServiceImpl();
            session.setAttribute(AppConstant.CITY_SERVICE, cityService);
        }
        return cityService;
    }

    protected AssessorService getAssessorServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        AssessorService assessorService = (AssessorService) session.getAttribute(AppConstant.ASSIGN_ASSESSOR);
        if (null == assessorService) {
            assessorService = new AssessorServiceImpl();
            session.setAttribute(AppConstant.ASSIGN_ASSESSOR, assessorService);
        }
        return assessorService;
    }

    protected DistrictService getDistrictServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        DistrictService districtService = (DistrictService) session.getAttribute(AppConstant.DISTRICT_SERVICE);
        if (null == districtService) {
            districtService = new DistrictServiceImpl();
            session.setAttribute(AppConstant.DISTRICT_SERVICE, districtService);
        }
        return districtService;
    }

    protected AssessorAllocationService getAssessorAllocationServiceServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        AssessorAllocationService assessorAllocationService = (AssessorAllocationService) session.getAttribute(AppConstant.ASSESSOR_ALLOCATION_SERVICE);
        if (null == assessorAllocationService) {
            assessorAllocationService = new AssessorAllocationServiceImpl(new InspectionDetailsServiceImpl());
            session.setAttribute(AppConstant.ASSESSOR_ALLOCATION_SERVICE, assessorAllocationService);
        }
        return assessorAllocationService;
    }

    protected CallCenterService getCallServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        CallCenterService callCenterService = (CallCenterService) session.getAttribute(AppConstant.CALL_CENTER_SERVICE);
        if (null == callCenterService) {
            callCenterService = new CallCenterServiceImpl();
            session.setAttribute(AppConstant.CALL_CENTER_SERVICE, callCenterService);
        }
        return callCenterService;
    }

    protected ClaimLockService getClaimLockServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimLockService claimLockService = (ClaimLockService) session.getAttribute(AppConstant.CLAIM_LOCK_SERVICE);
        if (null == claimLockService) {
            claimLockService = new ClaimLockServiceImpl();
            session.setAttribute(AppConstant.CLAIM_LOCK_SERVICE, claimLockService);
        }
        return claimLockService;
    }

    protected InspectionDetailsService getInspectionDetailsBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        InspectionDetailsService inpService = (InspectionDetailsService) session.getAttribute(AppConstant.INSPECTION_DETAILS_SERVICE);
        if (null == inpService) {
            inpService = new InspectionDetailsServiceImpl();
            session.setAttribute(AppConstant.INSPECTION_DETAILS_SERVICE, inpService);
        }
        return inpService;
    }

    protected InvestigationDetailsService getInvestigationDetailsBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        InvestigationDetailsService investigationDetailsService = (InvestigationDetailsService) session.getAttribute(AppConstant.INVESTIGATION_DETAILS_SERVICE);
        if (null == investigationDetailsService) {
            investigationDetailsService = new InvestigationDetailsServiceImpl();
            session.setAttribute(AppConstant.INVESTIGATION_DETAILS_SERVICE, investigationDetailsService);
        }
        return investigationDetailsService;
    }

    protected SupplyOrderService getSupplyOrderServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        SupplyOrderService supplyOrderService = (SupplyOrderService) session.getAttribute(AppConstant.SUPPLY_ORDER_SERVICE);
        if (null == supplyOrderService) {
            supplyOrderService = new SupplyOrderServiceImpl();
            session.setAttribute(AppConstant.SUPPLY_ORDER_SERVICE, supplyOrderService);
        }
        return supplyOrderService;
    }

    protected MotorEngineerService getMotorEngineerBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        MotorEngineerService motorEngineerService = (MotorEngineerService) session.getAttribute(AppConstant.MOTOR_ENGINEER_SERVICE);
        if (null == motorEngineerService) {
            motorEngineerService = new MotorEngineerServiceImpl();
            session.setAttribute(AppConstant.MOTOR_ENGINEER_SERVICE, motorEngineerService);
        }
        return motorEngineerService;
    }

    protected DbRecordCommonFunction getDbRecordCommonFunctionBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        DbRecordCommonFunction dbRecordCommonFunction = (DbRecordCommonFunction) session.getAttribute(AppConstant.DB_RECORD_COMMON_FUNCTION);
        if (null == dbRecordCommonFunction) {
            dbRecordCommonFunction = new DbRecordCommonFunction();
            session.setAttribute(AppConstant.DB_RECORD_COMMON_FUNCTION, dbRecordCommonFunction);
        }
        return dbRecordCommonFunction;
    }

    protected RequestAriService getByRequestAri(HttpServletRequest request) {
        HttpSession session = request.getSession();
        RequestAriService requestAriService = (RequestAriService) session.getAttribute(AppConstant.REQUEST_ARI_SERVICE);
        if (null == requestAriService) {
            requestAriService = new RequestAriServiceImpl();
            session.setAttribute(AppConstant.REQUEST_ARI_SERVICE, requestAriService);
        }
        return requestAriService;
    }

    protected AcknowledgementService getAcknowledgementService(HttpServletRequest request) {
        HttpSession session = request.getSession();
        AcknowledgementService acknowledgementService = (AcknowledgementService) session.getAttribute(AppConstant.ACKNOWLEDGEMENT_SERVICE);
        if (null == acknowledgementService) {
            acknowledgementService = new AcknowledgementServiceImpl();
            session.setAttribute(AppConstant.ACKNOWLEDGEMENT_SERVICE, acknowledgementService);
        }
        return acknowledgementService;
    }

    protected ClaimHandlerDashboardService getClaimHandlerDashboardService(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimHandlerDashboardService claimHandlerDashboardService = (ClaimHandlerDashboardService) session.getAttribute(AppConstant.CLAIM_HANDLER_DASHBOARD_SERVICE);
        if (null == claimHandlerDashboardService) {
            claimHandlerDashboardService = new ClaimHandlerDashboardServiceImpl();
            session.setAttribute(AppConstant.CLAIM_HANDLER_DASHBOARD_SERVICE, claimHandlerDashboardService);
        }
        return claimHandlerDashboardService;
    }

    protected UserDto getSessionUser(HttpServletRequest request) {
        HttpSession session = request.getSession();
        return (UserDto) session.getAttribute(AppConstant.SESSION_USER);
    }

    protected CallCenterService getCallCenterServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        CallCenterService callCenterService = (CallCenterService) session.getAttribute(AppConstant.CALL_CENTER_SERVICE);
        if (null == callCenterService) {
            callCenterService = new CallCenterServiceImpl();
            session.setAttribute(AppConstant.CALL_CENTER_SERVICE, callCenterService);
        }
        return callCenterService;
    }

    protected ClaimHandlerService getCallHandlerServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimHandlerService claimHandlerService = (ClaimHandlerService) session.getAttribute(AppConstant.CLAIM_HANDLER_SERVICE);
        if (null == claimHandlerService) {
            claimHandlerService = new ClaimHandlerServiceImpl();
            session.setAttribute(AppConstant.CLAIM_HANDLER_SERVICE, claimHandlerService);
        }
        return claimHandlerService;
    }

    protected FinanceVoucherDetailsService getFinanceVoucherDetailsServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        FinanceVoucherDetailsService financeVoucherDetailsService = (FinanceVoucherDetailsService) session.getAttribute(AppConstant.FINANCE_VOUCHER_DETAILS_SERVICE);
        if (null == financeVoucherDetailsService) {
            financeVoucherDetailsService = new FinanceVoucherDetailsServiceImpl();
            session.setAttribute(AppConstant.FINANCE_VOUCHER_DETAILS_SERVICE, financeVoucherDetailsService);
        }
        return financeVoucherDetailsService;
    }

    protected ClaimUserUpdateService getClaimUserUpdateServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimUserUpdateService claimUserUpdateService = (ClaimUserUpdateService) session.getAttribute(AppConstant.CLAIM_USER_UPDATE_SERVICE);
        if (null == claimUserUpdateService) {
            claimUserUpdateService = new ClaimUserUpdateServiceImpl();
            session.setAttribute(AppConstant.CLAIM_USER_UPDATE_SERVICE, claimUserUpdateService);
        }
        return claimUserUpdateService;
    }

    protected CalculationSheetService getCalculationSheetServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        CalculationSheetService calculationSheetService = (CalculationSheetService) session.getAttribute(AppConstant.CALCULATION_SHEET_SERVICE);
        if (null == calculationSheetService) {
            calculationSheetService = new CalculationSheetServiceImpl();
            session.setAttribute(AppConstant.CALCULATION_SHEET_SERVICE, calculationSheetService);
        }
        return calculationSheetService;
    }

    protected ReferenceTwoCalculationSheetService getReferenceTwoCalculationSheetServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ReferenceTwoCalculationSheetService calculationSheetService = (ReferenceTwoCalculationSheetService) session.getAttribute(AppConstant.REFERENCE_TWO_CALCULATION_SHEET_SERVICE);
        if (null == calculationSheetService) {
            calculationSheetService = new ReferenceTwoCalculationSheetServiceImpl();
            session.setAttribute(AppConstant.REFERENCE_TWO_CALCULATION_SHEET_SERVICE, calculationSheetService);
        }
        return calculationSheetService;
    }

    protected AssessorPaymentDetailsService getAssessorPaymentServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        AssessorPaymentDetailsService assessorPaymentDetailsService = (AssessorPaymentDetailsService) session.getAttribute(AppConstant.ASSESSOR_PAYMENT_SERVICE);
        if (null == assessorPaymentDetailsService) {
            assessorPaymentDetailsService = new AssessorPaymentDetailsServiceImpl();
            session.setAttribute(AppConstant.ASSESSOR_PAYMENT_SERVICE, assessorPaymentDetailsService);
        }
        return assessorPaymentDetailsService;
    }

    protected ClaimWiseDocumentService getClaimWiseDocumentServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimWiseDocumentService claimWiseDocumentService = (ClaimWiseDocumentService) session.getAttribute(AppConstant.CLAIM_WISE_DOCUMENT_SERVICE);
        if (null == claimWiseDocumentService) {
            claimWiseDocumentService = new ClaimWiseDocumentServiceImpl();
            session.setAttribute(AppConstant.CLAIM_WISE_DOCUMENT_SERVICE, claimWiseDocumentService);
        }
        return claimWiseDocumentService;
    }

    protected DocumenthistoryService getDocumentHistoryServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        DocumenthistoryService documenthistoryService = (DocumenthistoryService) session.getAttribute(AppConstant.DOCUMENT_HISTORY_SERVICE);
        if (null == documenthistoryService) {
            documenthistoryService = new DocumenthistoryServiceImpl();
            session.setAttribute(AppConstant.DOCUMENT_HISTORY_SERVICE, documenthistoryService);
        }
        return documenthistoryService;
    }

    protected ReminderPrintService getReminderPrintServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ReminderPrintService reminderPrintService = (ReminderPrintService) session.getAttribute(AppConstant.CLAIM_REMINDER_PRINT_SERVICE);
        if (null == reminderPrintService) {
            reminderPrintService = new ReminderPrintServiceImpl();
            session.setAttribute(AppConstant.CLAIM_REMINDER_PRINT_SERVICE, reminderPrintService);
        }
        return reminderPrintService;
    }

    protected void returnJson(Object o, HttpServletResponse response) {

        String json = new Gson().toJson(o);
        try {
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected ClaimsDto getSessionClaimDetails(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        ClaimsDto claimsDto = (ClaimsDto) session.getAttribute(AppConstant.SESSION_CLAIM_DTO);
        return claimsDto;
    }

    protected void updateSessionClaimDetails(HttpServletRequest request, HttpServletResponse response, ClaimsDto claimsDto) {
        HttpSession session = request.getSession();
        session.setAttribute(AppConstant.SESSION_CLAIM_DTO, claimsDto);
    }

    protected void removeSessionClaimDetails(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        session.removeAttribute(AppConstant.SESSION_CLAIM_DTO);
    }

    protected void updateSessionType(HttpServletRequest request, HttpServletResponse response, Integer type) {
        HttpSession session = request.getSession();
        session.setAttribute(AppConstant.SESSION_TYPE, type);
    }

    protected void removeSessionType(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        session.removeAttribute(AppConstant.SESSION_TYPE);
    }

    protected void setUserRight(HttpServletRequest request, int menuId, int itemId) {
        HttpSession session = request.getSession();
        UserRights userRight;
        Map<String, UserRights> userRightsMap;
        try {
            userRightsMap = (Map<String, UserRights>) session.getAttribute(AppConstant.SESSION_G_MNUITMS);
            userRight = userRightsMap.get(String.valueOf(menuId) + String.valueOf(itemId));
            session.setAttribute(AppConstant.SESSION_USER_RIGHT, userRight);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    protected Integer getSessionPolicyNumber(HttpServletRequest request) {
        HttpSession session = request.getSession();
        Integer polNo = (Integer) session.getAttribute(AppConstant.POL_NUMBER);
        return polNo;
    }

    protected void updateSessionPolNo(HttpServletRequest request, HttpServletResponse response, Integer polNo) {
        HttpSession session = request.getSession();
        session.setAttribute(AppConstant.POL_NUMBER, polNo);
    }

    protected InspectionDetailsDto getInspectionDetails(HttpServletRequest request) {
        HttpSession session = request.getSession();
        InspectionDetailsDto inspectionDetailsDto = (InspectionDetailsDto) session.getAttribute(AppConstant.INSPECTION_DETAILS + "_SESSION");
        return inspectionDetailsDto;
    }

    protected MotorEngineerDetailsDto getMotorEngineerDetailsDto(HttpServletRequest request) {
        HttpSession session = request.getSession();
        MotorEngineerDetailsDto motorEngineerDetailsDto = (MotorEngineerDetailsDto) session.getAttribute(AppConstant.SESSION_MOTOR_ENGINEER_DETAILS);
        return motorEngineerDetailsDto;
    }

    protected StorageService getSftpDocumentService(HttpServletRequest request) {
        HttpSession session = request.getSession();
        StorageService storageService = (StorageService) session.getAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE);
        if (storageService == null) {
            storageService = new StorageServiceImpl();
            session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
        }

        return storageService;
    }

    protected void lockedIntimation(HttpServletRequest request, Integer policyRefNo, UserDto user) {
        HttpSession session = request.getSession();
        claimLockService = getClaimLockServiceBySession(request);
        ClaimLockDto claimLockDto = new ClaimLockDto()
                .setKey(policyRefNo)
                .setInvokeUserId(user.getUserId())
                .setInvokeDateTime(Utility.sysDateTime());

        if (claimLockService.isLockIntimation(claimLockDto)) {
            request.setAttribute(AppConstant.IS_LOCK, Boolean.TRUE);
            session.setAttribute(AppConstant.SESSION_CLAIM_LOCK_DTO, claimLockService.getClaimLockDto(policyRefNo));
        } else {
            claimLockService.lockIntimation(claimLockDto);
            session.setAttribute(AppConstant.SESSION_CLAIM_LOCK_DTO, claimLockDto);
        }
    }

    protected void unlockedIntimation(HttpServletRequest request, UserDto user) {
        HttpSession session = request.getSession();
        ClaimLockDto sessionClaimLockDto = (ClaimLockDto) session.getAttribute(AppConstant.SESSION_CLAIM_LOCK_DTO);
        claimLockService = getClaimLockServiceBySession(request);
        if (null != sessionClaimLockDto) {
            claimLockService.unLockIntimation(sessionClaimLockDto, user.getUserId());
        }
    }

    protected NotificationService getNotificationServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        NotificationService notificationService = (NotificationService) session.getAttribute(AppConstant.NOTIFICATION_SERVICE);
        if (notificationService == null) {
            notificationService = new NotificationServiceImpl();
            session.setAttribute(AppConstant.NOTIFICATION_SERVICE, notificationService);
        }
        return notificationService;
    }

    protected ClaimSuperDashboardService getClaimSuperDashboardServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimSuperDashboardService claimSuperDashboardService = (ClaimSuperDashboardService) session.getAttribute(AppConstant.CLAIM_SUPER_DASHBOARD_SERVICE);
        if (claimSuperDashboardService == null) {
            claimSuperDashboardService = new ClaimSuperDashboardServiceImpl();
            session.setAttribute(AppConstant.CLAIM_SUPER_DASHBOARD_SERVICE, claimSuperDashboardService);
        }
        return claimSuperDashboardService;
    }

    protected void updateMappedPolicyNo(HttpServletRequest request, HttpServletResponse response, Integer polRefNo) {
        HttpSession session = request.getSession();
        session.setAttribute(AppConstant.MAPPED_POLICY_NO, polRefNo);
    }

    protected void setInitValues(HttpServletRequest request) {
        try {
            UserDto user = getSessionUser(request);
            boolean isTwoPanelUser = false;
            boolean isFourPanelUser = false;
            boolean isDecisionMaker = false;
            boolean isInitLiabilityUser = false;
            boolean isOfferTeamInitLiabilityUser = false;
            boolean isClaimHandlerUser = false;
            boolean isOfferTeamClaimHandlerUser = false;
            boolean isTotalLossClaimHandlerUser = false;
            boolean isSparePartsCoordinator = false;
            boolean isScrutinizingTeam = false;
            boolean isSpecialTeam = false;
            boolean isOfferTeamSpecialTeam = false;
            boolean isMofaTeam = false;
            boolean isOfferTeamMofaTeam = false;
            boolean isAssessor = false;
            boolean isRte = false;
            boolean isBranch = false;
            boolean isTechnicalCoordinator = false;
            boolean isLetterPanel = false;
            boolean isTechnicalCoordinatorAriOnly = false;

            HttpSession session = request.getSession();
            claimHandlerService = getCallHandlerServiceBySession(request);

            List<ClaimClaimPanelUserDto> claimClaimPanelUserDtos = claimHandlerService.getPanelUser(user.getUserId());
            for (ClaimClaimPanelUserDto claimClaimPanelUserDto : claimClaimPanelUserDtos) {
                if (1 == claimClaimPanelUserDto.getUserPanelId()) {
                    isTwoPanelUser = true;
                }
                if (2 == claimClaimPanelUserDto.getUserPanelId()) {
                    isFourPanelUser = true;
                }
                if (3 == claimClaimPanelUserDto.getUserPanelId()) {
                    isDecisionMaker = true;
                }
            }
            if (user.getAccessUserType() == 40) {
                isInitLiabilityUser = true;
            }
            if (user.getAccessUserType() == 41) {
                isClaimHandlerUser = true;
            }

            if (user.getAccessUserType() == 27) {
                isSparePartsCoordinator = true;
            }

            if (user.getAccessUserType() == 28) {
                isScrutinizingTeam = true;
            }

            if (user.getAccessUserType() == 43) {
                isSpecialTeam = true;
            }

            if (user.getAccessUserType() == 42) {
                isMofaTeam = true;
            }
            if (user.getAccessUserType() == 45) {
                isDecisionMaker = true;
            }
            if (user.getAccessUserType() == 46) {
                isTotalLossClaimHandlerUser = true;
            }

            if (user.getAccessUserType() == 20) {
                isAssessor = true;
            }

            if (user.getAccessUserType() == 22) {
                isRte = true;
            }

            if (user.getAccessUserType() == 100) {
                isBranch = true;
            }

            if (user.getAccessUserType() == 25) {
                isTechnicalCoordinator = true;
            }

            if (user.getAccessUserType() == 60) {
                isOfferTeamInitLiabilityUser = true;
            }
            if (user.getAccessUserType() == 61) {
                isOfferTeamClaimHandlerUser = true;
            }
            if (user.getAccessUserType() == 62) {
                isOfferTeamMofaTeam = true;
            }
            if (user.getAccessUserType() == 63) {
                isOfferTeamSpecialTeam = true;
            }
            if (user.getAccessUserType() == 103) {
                isLetterPanel = true;
            }
            if (user.getAccessUserType() == 104) {
                isTechnicalCoordinatorAriOnly = true;
            }


            session.setAttribute(AppConstant.IS_TWO_PANEL_USER, isTwoPanelUser);
            session.setAttribute(AppConstant.IS_FOUR_PANEL_USER, isFourPanelUser);
            session.setAttribute(AppConstant.IS_DECISION_MAKER, isDecisionMaker);
            session.setAttribute(AppConstant.IS_INIT_LIABILITY_USER, isInitLiabilityUser);
            session.setAttribute(AppConstant.IS_CLAIM_HANDLER_USER, isClaimHandlerUser);
            session.setAttribute(AppConstant.IS_SPARE_COORDINATOR, isSparePartsCoordinator);
            session.setAttribute(AppConstant.IS_SCRUTINIZING_COORDINATOR, isScrutinizingTeam);
            session.setAttribute(AppConstant.IS_SPECIAL_TEAM, isSpecialTeam);
            session.setAttribute(AppConstant.IS_MOFA_TEAM, isMofaTeam);
            session.setAttribute(AppConstant.IS_ASSESSOR, isAssessor);
            session.setAttribute(AppConstant.IS_RTE, isRte);
            session.setAttribute(AppConstant.IS_TOTAL_LOSS_CLAIM_HANDLER_USER, isTotalLossClaimHandlerUser);
            session.setAttribute(AppConstant.IS_TECHNICAL_COORDINATOR, isTechnicalCoordinator);
            session.setAttribute(AppConstant.IS_BRANCH, isBranch);
            session.setAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER, isOfferTeamInitLiabilityUser);
            session.setAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER, isOfferTeamClaimHandlerUser);
            session.setAttribute(AppConstant.IS_OFFER_TEAM_MOFA_TEAM, isOfferTeamMofaTeam);
            session.setAttribute(AppConstant.IS_OFFER_TEAM_SPECIAL_TEAM, isOfferTeamSpecialTeam);
            session.setAttribute(AppConstant.IS_LETTER_PANEL_USER, isLetterPanel);
            session.setAttribute(AppConstant.IS_TECHNICAL_COORDINATOR_ARI_ONLY, isTechnicalCoordinatorAriOnly);

            request.setAttribute("CLAIM_HANDLER_MESSAGE_TYPE", request.getParameter("CLAIM_HANDLER_MESSAGE_TYPE"));
            request.setAttribute("CLAIM_HANDLER_MESSAGE", request.getParameter("CLAIM_HANDLER_MESSAGE"));

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    protected ClaimUserTypeDto claimUserTypeDto(HttpServletRequest request) {
        ClaimUserTypeDto claimUserTypeDto = new ClaimUserTypeDto();
        try {
            UserDto user = getSessionUser(request);
            boolean isTwoPanelUser = false;
            boolean isFourPanelUser = false;
            boolean isDecisionMaker = false;
            boolean isInitLiabilityUser = false;
            boolean isOfferTeamInitLiabilityUser = false;
            boolean isClaimHandlerUser = false;
            boolean isOfferTeamClaimHandlerUser = false;
            boolean isTotalLossClaimHandlerUser = false;
            boolean isSparePartsCoordinator = false;
            boolean isScrutinizingTeam = false;
            boolean isSpecialTeam = false;
            boolean isOfferTeamSpecialTeam = false;
            boolean isMofaTeam = false;
            boolean isOfferTeamMofaTeam = false;
            boolean isBranch = false;
            boolean isAssessor = false;
            boolean isRte = false;
            boolean isTechnicalCoordinator = false;
            boolean isTechnicalCoordinatorAriOnly = false;


            claimHandlerService = getCallHandlerServiceBySession(request);

            List<ClaimClaimPanelUserDto> claimClaimPanelUserDtos = claimHandlerService.getPanelUser(user.getUserId());
            for (ClaimClaimPanelUserDto claimClaimPanelUserDto : claimClaimPanelUserDtos) {
                if (1 == claimClaimPanelUserDto.getUserPanelId()) {
                    isTwoPanelUser = true;
                }
                if (2 == claimClaimPanelUserDto.getUserPanelId()) {
                    isFourPanelUser = true;
                }
                if (3 == claimClaimPanelUserDto.getUserPanelId()) {
                    isDecisionMaker = true;
                }
            }
            if (user.getAccessUserType() == 40) {
                isInitLiabilityUser = true;
            }
            if (user.getAccessUserType() == 41) {
                isClaimHandlerUser = true;
            }

            if (user.getAccessUserType() == 27) {
                isSparePartsCoordinator = true;
            }

            if (user.getAccessUserType() == 28) {
                isScrutinizingTeam = true;
            }

            if (user.getAccessUserType() == 43) {
                isSpecialTeam = true;
            }

            if (user.getAccessUserType() == 42) {
                isMofaTeam = true;
            }

            if (user.getAccessUserType() == 45) {
                isDecisionMaker = true;
            }

            if (user.getAccessUserType() == 46) {
                isTotalLossClaimHandlerUser = true;
            }

            if (user.getAccessUserType() == 100) {
                isBranch = true;
            }

            if (user.getAccessUserType() == 20) {
                isAssessor = true;
            }

            if (user.getAccessUserType() == 22) {
                isRte = true;
            }

            if (user.getAccessUserType() == 25) {
                isTechnicalCoordinator = true;
            }

            if (user.getAccessUserType() == 60) {
                isOfferTeamInitLiabilityUser = true;
            }
            if (user.getAccessUserType() == 61) {
                isOfferTeamClaimHandlerUser = true;
            }
            if (user.getAccessUserType() == 62) {
                isOfferTeamMofaTeam = true;
            }
            if (user.getAccessUserType() == 63) {
                isOfferTeamSpecialTeam = true;
            }
            if (user.getAccessUserType() == 104) {
                isTechnicalCoordinatorAriOnly = true;
            }


            claimUserTypeDto.setUser(user);

            claimUserTypeDto.setTwoPanelUser(isTwoPanelUser);
            claimUserTypeDto.setFourPanelUser(isFourPanelUser);
            claimUserTypeDto.setDecisionMaker(isDecisionMaker);
            claimUserTypeDto.setInitLiabilityUser(isInitLiabilityUser);
            claimUserTypeDto.setClaimHandlerUser(isClaimHandlerUser);
            claimUserTypeDto.setTotalLossClaimHandlerUser(isTotalLossClaimHandlerUser);
            claimUserTypeDto.setSparePartsCoordinator(isSparePartsCoordinator);
            claimUserTypeDto.setScrutinizingTeam(isScrutinizingTeam);
            claimUserTypeDto.setSpecialTeam(isSpecialTeam);
            claimUserTypeDto.setMofaTeam(isMofaTeam);
            claimUserTypeDto.setBranchUser(isBranch);
            claimUserTypeDto.setAssessor(isAssessor);
            claimUserTypeDto.setTechnicalCoordinator(isTechnicalCoordinator);
            claimUserTypeDto.setRte(isRte);
            claimUserTypeDto.setOfferTeamInitLiabilityUser(isOfferTeamInitLiabilityUser);
            claimUserTypeDto.setOfferTeamClaimHandlerUser(isOfferTeamClaimHandlerUser);
            claimUserTypeDto.setOfferTeamSpecialTeam(isOfferTeamSpecialTeam);
            claimUserTypeDto.setOfferTeamMofaTeam(isOfferTeamMofaTeam);
            claimUserTypeDto.setTechnicalCoordinatorAriOnly(isTechnicalCoordinatorAriOnly);

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
        return claimUserTypeDto;
    }

    protected void setInvestigationPopupListValues(HttpServletRequest request) {
        List<PopupItemDto> investigatorList = null;
        try {
            investigatorList = recordCommonFunction.getPopupItemDtoList("claim_assessor", "N_REF_NO", "V_NAME", "V_PARA_TYPE='INV' ORDER BY V_NAME");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.INVESTIGATOR_LIST, investigatorList);
        }
    }

    protected ClaimPanelUserService getClaimPanelUserServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimPanelUserService claimPanelUserService = (ClaimPanelUserService) session.getAttribute(AppConstant.CLAIMPANEL_USER_SERVICE);
        if (null == claimPanelUserService) {
            claimPanelUserService = new ClaimPanelUserServiceImpl();
            session.setAttribute(AppConstant.CLAIMPANEL_USER_SERVICE, claimPanelUserService);
        }
        return claimPanelUserService;
    }

    protected ClaimReserveAdjustmentService getClaimReserveAdjustmentServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimReserveAdjustmentService claimReserveAdjustmentService = (ClaimReserveAdjustmentService) session.getAttribute(AppConstant.CLAIM_RESERVE_ADJUSTMENT_SERVICE);
        if (null == claimReserveAdjustmentService) {
            claimReserveAdjustmentService = new ClaimReserveAdjustmentServiceImpl();
            session.setAttribute(AppConstant.CLAIM_RESERVE_ADJUSTMENT_SERVICE, claimReserveAdjustmentService);
        }
        return claimReserveAdjustmentService;
    }


    protected MobileApplicationRequestService getMobileApplicationRequestServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        MobileApplicationRequestService mobileApplicationRequestService = (MobileApplicationRequestService) session.getAttribute(AppConstant.MOBILE_APPLICATION_REQUEST_SERVICE);
        if (null == mobileApplicationRequestService) {
            mobileApplicationRequestService = new MobileApplicationRequestServiceImpl();
            session.setAttribute(AppConstant.MOBILE_APPLICATION_REQUEST_SERVICE, mobileApplicationRequestService);
        }
        return mobileApplicationRequestService;
    }

    protected SparePartDatabaseService getCsparePartDatabaseServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        SparePartDatabaseService sparePartDatabaseService = (SparePartDatabaseService) session.getAttribute(AppConstant.SPARE_PART_DATABASE_SERVICE);
        if (null == sparePartDatabaseService) {
            sparePartDatabaseService = new SparePartDatabaseServiceImpl();
            session.setAttribute(AppConstant.SPARE_PART_DATABASE_SERVICE, sparePartDatabaseService);
        }
        return sparePartDatabaseService;
    }

    protected SupplierDetailsService getSupplierDetailsServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        SupplierDetailsService supplierDetailsService = (SupplierDetailsService) session.getAttribute(AppConstant.SUPPLIER_DETAILS_SERVICE);
        if (null == supplierDetailsService) {
            supplierDetailsService = new SupplierDetailsServiceImpl();
            session.setAttribute(AppConstant.SUPPLIER_DETAILS_SERVICE, supplierDetailsService);
        }
        return supplierDetailsService;
    }

    protected ClaimUserLeaveService getClaimUserLeaveServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimUserLeaveService claimUserLeaveService = (ClaimUserLeaveService) session.getAttribute(AppConstant.CLAIM_USER_LEAVE_SERVICE);
        if (null == claimUserLeaveService) {
            claimUserLeaveService = new ClaimUserLeaveServiceImpl();
            session.setAttribute(AppConstant.CLAIM_USER_LEAVE_SERVICE, claimUserLeaveService);
        }
        return claimUserLeaveService;
    }

    protected ClaimDocumentTypeService getClaimDocumentTypeServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimDocumentTypeService claimDocumentTypeService = (ClaimDocumentTypeService) session.getAttribute(AppConstant.CLAIM_DOCUMENT_TYPE_SERVICE);
        if (null == claimDocumentTypeService) {
            claimDocumentTypeService = new ClaimDocumentTypeServiceImpl();
            session.setAttribute(AppConstant.CLAIM_DOCUMENT_TYPE_SERVICE, claimDocumentTypeService);
        }
        return claimDocumentTypeService;
    }

    protected ClaimDocumentService getClaimDocumentServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        ClaimDocumentService claimDocumentService = (ClaimDocumentService) session.getAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE);
        if (null == claimDocumentService) {
            claimDocumentService = new ClaimDocumentServiceImpl();
            session.setAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE, claimDocumentService);
        }
        return claimDocumentService;
    }

    protected AgentGarageService getAgentGarageServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        AgentGarageService agentGarageService = (AgentGarageService) session.getAttribute(AppConstant.SPARE_PART_ITEM_SERVISE);
        if (null == agentGarageService) {
            agentGarageService = new AgentGarageServiceImpl();
            session.setAttribute(AppConstant.AGENT_GARAGE_SERVICE, agentGarageService);
        }
        return agentGarageService;
    }

    protected SparePartItemService getSparePartItemServiceBySession(HttpServletRequest request) {
        HttpSession session = request.getSession();
        SparePartItemService sparePartItemService = (SparePartItemService) session.getAttribute(AppConstant.SPARE_PART_ITEM_SERVISE);
        if (null == sparePartItemService) {
            sparePartItemService = new SparePartItemServiceImpl();
            session.setAttribute(AppConstant.CLAIM_USER_DOCUMENT_SERVICE, sparePartItemService);
        }
        return sparePartItemService;
    }

    protected void setSupplyOrderPopupListValues(HttpServletRequest request, Integer claimNo) {
        List<PopupItemDto> popupSupplierDtoList = null;
        List<PopupItemDto> popupSparePartDtoList = null;
        try {
            String policyChannelType = supplyOrderService.getPolicyChannelType(claimNo);
            popupSupplierDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main ", "N_REF_NO", "V_COMPANY_NAME", "V_INST_TYPE='WS' ORDER BY V_COMPANY_NAME ");
            popupSparePartDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("spare_part_item_mst ", "n_spare_part_ref_no", "v_spare_part_name", "v_record_status = 'A' ORDER BY v_spare_part_name ");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.SUPPLIER_LIST, popupSupplierDtoList);
            request.setAttribute(AppConstant.SPARE_PART_LIST, popupSparePartDtoList);
        }
    }

    protected void setClaimPanelUserPopupListValues(HttpServletRequest request) {
        List<PopupItemDto> popupClaimPanelUserList = null;
        List<PopupItemDto> popupClaimPanelList = null;
        try {
            popupClaimPanelUserList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "v_usrid", "v_usrid", "v_usrstatus IN('A','L','X') ORDER BY v_usrid ");
            popupClaimPanelList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_claim_panel ", "N_ID", "V_PANEL_NAME", AppConstant.STRING_EMPTY);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.CLAIM_PANEL_USER_LIST, popupClaimPanelUserList);
            request.setAttribute(AppConstant.CLAIM_PANEL_LIST, popupClaimPanelList);
        }
    }

    protected void setClaimUserLeavePopupListValues(HttpServletRequest request) {
        List<PopupItemDto> popupClaimUserLeaveList = null;
        List<PopupItemDto> popupClaimLeaveList = null;
        try {
            popupClaimUserLeaveList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("usr_mst ", "V_USER_ID", "V_USER_ID", "V_LEAVE_TYPE IN('LEAVE','HOLD') ");
            popupClaimLeaveList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_user_leave ", "V_USER_ID", "V_INPUT_USER", AppConstant.STRING_EMPTY);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.CLAIM_USER_LEAVE_LIST, popupClaimUserLeaveList);
            request.setAttribute(AppConstant.CLAIM_LEAVE_LIST, popupClaimLeaveList);
        }
    }

    protected void getUserListByAccessUserType(HttpServletRequest request, HttpServletResponse response, String accessUserTypeList,UserDto sessionUser) {
        try {
            Gson gson = new Gson();
            List<String> list = claimHandlerService.getUserListByAccessUserType(accessUserTypeList, sessionUser);
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }
    protected void getUserListByAccessUserType(HttpServletRequest request, HttpServletResponse response, String accessUserTypeList) {
        try {
            Gson gson = new Gson();
            List<String> list = claimHandlerService.getUserListByAccessUserType(accessUserTypeList);
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());;
        }
    }

    protected void setClaimDepartmentPopupListValues(HttpServletRequest request, HttpServletResponse response) {
        List<ClaimDepartmentDto> popupclaimDepartmentDtoList = null;
        List<DocReqFormDto> popupreqFormDtosList = null;
        ClaimDocumentTypeService claimDocumentTypeService = new ClaimDocumentTypeServiceImpl();
        try {
            Gson gson = new Gson();
            popupclaimDepartmentDtoList = claimDocumentTypeService.documentlist();
            String json = gson.toJson(popupclaimDepartmentDtoList);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected void setClaimDocumentReqFormListValues(HttpServletRequest request, HttpServletResponse response) {
        List<DocReqFormDto> popupreqFormDtosList = null;
        ClaimDocumentTypeService claimDocumentTypeService = new ClaimDocumentTypeServiceImpl();
        try {
            Gson gson = new Gson();
            popupreqFormDtosList = claimDocumentTypeService.documentReqFormlist();
            String json = gson.toJson(popupreqFormDtosList);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected String getVehicleNumberLastDigit(String vehicleNumber) {
        String vehicleNumberLastDigit = AppConstant.STRING_EMPTY;
        try {
            Pattern p = Pattern.compile("\\d+");
            Matcher m = p.matcher(vehicleNumber);
            while (m.find()) {
                vehicleNumberLastDigit = m.group();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return vehicleNumberLastDigit;
    }

    protected String getPolicyNumberLastDigit(String policyNumber) {
        String policyNumberLastDigit = AppConstant.STRING_EMPTY;
        try {
            Pattern p = Pattern.compile("\\d+");
            Matcher m = p.matcher(policyNumber);
            if (m.find()) {
                policyNumberLastDigit = m.group();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return policyNumberLastDigit;
    }

}
