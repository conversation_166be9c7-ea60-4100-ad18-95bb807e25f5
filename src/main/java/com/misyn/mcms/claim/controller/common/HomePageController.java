package com.misyn.mcms.claim.controller.common;

import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.service.keycloak.KeycloakAuthService;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakAuthServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

@WebServlet(name = "homePageController", value = "/home.do")
public class HomePageController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(HomePageController.class);
    private final KeycloakAuthService keycloakAuthService = new KeycloakAuthServiceImpl();


    @Override
    public void init() {
    }

    @Override
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException {
        HttpSession session = request.getSession();


        try {
            request.setAttribute(AppConstant.ERROR_MESSAGE, "");
            requestDispatcher(request, response, "/WEB-INF/jsp/admin/common/home.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public void destroy() {
    }
}
