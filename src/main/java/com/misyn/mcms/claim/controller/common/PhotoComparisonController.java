package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimDocumentDto;
import com.misyn.mcms.claim.dto.PhotoComparisionDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.enums.DocumentStatusEnum;
import com.misyn.mcms.claim.service.ClaimDocumentService;
import com.misyn.mcms.claim.service.PhotoComparisionService;
import com.misyn.mcms.claim.service.impl.ClaimDocumentServiceImpl;
import com.misyn.mcms.claim.service.impl.PhotoComparisionServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

@WebServlet(name = "PhotoComparisonController", urlPatterns = "/PhotoComparisonController/*")
public class PhotoComparisonController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PhotoComparisonController.class);


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        PhotoComparisionService photoComparisionService = (PhotoComparisionService) session.getAttribute(AppConstant.PHOTO_COMPARISION_SERVICE);
        if (photoComparisionService == null) {
            photoComparisionService = new PhotoComparisionServiceImpl();
            session.setAttribute(AppConstant.PHOTO_COMPARISION_SERVICE, photoComparisionService);
        }
        ClaimDocumentService claimDocumentService = (ClaimDocumentService) session.getAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE);
        if (claimDocumentService == null) {
            claimDocumentService = new ClaimDocumentServiceImpl();
            session.setAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE, claimDocumentService);
        }

        try {
            switch (pathInfo) {
                case "/initViewComparisonViewer":
                    String policyNumber = request.getParameter(AppConstant.POLICY_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.POLICY_NO);
                    String comparisionTabNo = request.getParameter(AppConstant.COMPARISION_TAB_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.COMPARISION_TAB_NO);

                    session.removeAttribute(AppConstant.SESSION_CLAIM_NO.concat(comparisionTabNo));
                    session.removeAttribute(AppConstant.SESSION_COMPARISION_TYPE.concat(comparisionTabNo));
                    session.removeAttribute(AppConstant.SESSION_INSPECTION_JOB_NO.concat(comparisionTabNo));
                    session.removeAttribute(AppConstant.SESSION_DOCUMENT_TYPE.concat(comparisionTabNo));
                    session.removeAttribute(AppConstant.POLICY_REF_NO);
                    session.setAttribute(AppConstant.POLICY_NO, policyNumber);

                    viewPhotoComparison(request, response, comparisionTabNo);
                    break;
                case "/viewComparisonViewer":
                    comparisionTabNo = request.getParameter(AppConstant.COMPARISION_TAB_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.COMPARISION_TAB_NO);
                    viewPhotoComparison(request, response, comparisionTabNo);
                    break;
                case "/documentViewer":
                    comparisionTabNo = request.getParameter(AppConstant.COMPARISION_TAB_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.COMPARISION_TAB_NO);
                    viewPdfViewer(request, response, comparisionTabNo);
                    break;
                case "/closeDocument":
                    comparisionTabNo = request.getParameter(AppConstant.COMPARISION_TAB_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.COMPARISION_TAB_NO);
                    closeDocumentView(request, response, comparisionTabNo);
                    break;
                case "/viewBillDocumentViewer":
                    viewBillDocumentViewer(request, response);
                    break;
                case "/updateBillCheck":
                    updateBillCheckDetails(request, response);
                    break;
                case "/cancelDocument":
                    cancelDocument(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void cancelDocument(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;

        Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
        Integer claimNo = Integer.parseInt(request.getParameter("claimNo") == null ? AppConstant.ZERO : request.getParameter("claimNo"));
        String remark = request.getParameter("remark");
        ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
        UserDto user = getSessionUser(request);
        ClaimDocumentService claimDocumentService = (ClaimDocumentService) session.getAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE);
        if (claimDocumentService == null) {
            claimDocumentService = new ClaimDocumentServiceImpl();
            session.setAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE, claimDocumentService);
        }
        try {
            claimDocumentDto.setRefNo(refNo);
            claimDocumentDto.setClaimNo(claimNo);
            claimDocumentDto.setDocumentStatus(DocumentStatusEnum.CANCELLED.getDocumentStatus());
            claimDocumentDto.setCancelUser(user.getUserId());
            claimDocumentDto.setCancelDateTime(Utility.sysDateTime());
            claimDocumentDto.setCancelRemark(remark);
            claimDocumentService.cancelDocument(claimDocumentDto, user);
            successMessage = "Bill Cancelled Successfully";
        } catch (Exception e) {
            errorMessage = "Bill Cancelling failed";
            LOGGER.error(e.getMessage());
        } finally {
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimDocumentService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/pdfBillCheckViewerAll.jsp");
        }
    }

    private void viewPhotoComparison(HttpServletRequest request, HttpServletResponse response, String comparisionTabNo) {
        HttpSession session = request.getSession();
        Integer claimNo = Integer.parseInt(request.getParameter(AppConstant.CLAIM_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.CLAIM_NO));
        String comparisionType = request.getParameter(AppConstant.COMPARISION_TYPE) == null ? AppConstant.ZERO : request.getParameter(AppConstant.COMPARISION_TYPE);
        String inspectionJobNo = request.getParameter(AppConstant.INSPECTION_JOB_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.INSPECTION_JOB_NO);
        Integer documentTypeId = Integer.parseInt(request.getParameter(AppConstant.DOCUMENT_TYPE) == null ? AppConstant.ZERO : request.getParameter(AppConstant.DOCUMENT_TYPE));
        String vehicleNo = request.getParameter(AppConstant.VEHICLE_NUMBER) == null || request.getParameter(AppConstant.VEHICLE_NUMBER).equals(AppConstant.STRING_EMPTY) ? AppConstant.NOT_AVAILABLE : request.getParameter(AppConstant.VEHICLE_NUMBER);
        String policyNumber = request.getParameter(AppConstant.POLICY_NO) == null ? AppConstant.ZERO : request.getParameter(AppConstant.POLICY_NO);

        if (comparisionType.equalsIgnoreCase(AppConstant.PHOTO)) {
            documentTypeId = AppConstant.CLAIM_IMAGE_DOCUMENT_TYPE_ID;
        } else if (comparisionType.equalsIgnoreCase(AppConstant.DOCUMENT) && documentTypeId == AppConstant.CLAIM_IMAGE_DOCUMENT_TYPE_ID) {
            documentTypeId = AppConstant.ZERO_INT;
        }

        PhotoComparisionService photoComparisionService = (PhotoComparisionService) session.getAttribute(AppConstant.PHOTO_COMPARISION_SERVICE);
        if (photoComparisionService == null) {
            photoComparisionService = new PhotoComparisionServiceImpl();
            session.setAttribute(AppConstant.PHOTO_COMPARISION_SERVICE, photoComparisionService);
        }

        try {
            PhotoComparisionDto photoComparisionDto = photoComparisionService.getPhotoComparisionDto(claimNo, policyNumber, vehicleNo, comparisionType, inspectionJobNo, documentTypeId);
            request.setAttribute(AppConstant.PHOTO_COMPARISION_DTO, photoComparisionDto);
            request.setAttribute(AppConstant.CLAIM_NO, claimNo);
            request.setAttribute(AppConstant.COMPARISION_TYPE, comparisionType);
            request.setAttribute(AppConstant.INSPECTION_JOB_NO, inspectionJobNo);
            request.setAttribute(AppConstant.DOCUMENT_TYPE, documentTypeId);
            request.setAttribute(AppConstant.COMPARISION_TAB_NO, comparisionTabNo);
            request.setAttribute(AppConstant.VEHICLE_NUMBER, vehicleNo);
            request.setAttribute(AppConstant.POLICY_NO, policyNumber);

            session.setAttribute(AppConstant.SESSION_CLAIM_NO.concat(comparisionTabNo), claimNo);
            session.setAttribute(AppConstant.SESSION_COMPARISION_TYPE.concat(comparisionTabNo), comparisionType);
            session.setAttribute(AppConstant.SESSION_INSPECTION_JOB_NO.concat(comparisionTabNo), inspectionJobNo);
            session.setAttribute(AppConstant.SESSION_DOCUMENT_TYPE.concat(comparisionTabNo), documentTypeId);
            session.setAttribute(AppConstant.VEHICLE_NUMBER, vehicleNo);
            session.setAttribute(AppConstant.POLICY_NO, policyNumber);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/photoComparison.jsp");
    }


    private void closeDocumentView(HttpServletRequest request, HttpServletResponse response, String comparisionTabNo) {
        HttpSession session = request.getSession();
        Integer claimNo = (Integer) session.getAttribute(AppConstant.SESSION_CLAIM_NO.concat(comparisionTabNo));
        String comparisionType = (String) session.getAttribute(AppConstant.SESSION_COMPARISION_TYPE.concat(comparisionTabNo));
        String inspectionJobNo = (String) session.getAttribute(AppConstant.SESSION_INSPECTION_JOB_NO.concat(comparisionTabNo));
        Integer documentTypeId = (Integer) session.getAttribute(AppConstant.SESSION_DOCUMENT_TYPE.concat(comparisionTabNo));
        PhotoComparisionService photoComparisionService = (PhotoComparisionService) session.getAttribute(AppConstant.PHOTO_COMPARISION_SERVICE);
        String vehicleNo = null == session.getAttribute(AppConstant.VEHICLE_NUMBER) ? AppConstant.NOT_AVAILABLE : (String) session.getAttribute(AppConstant.VEHICLE_NUMBER);
        String policyNo = null == session.getAttribute(AppConstant.POLICY_NO) ? AppConstant.NOT_AVAILABLE : (String) session.getAttribute(AppConstant.POLICY_NO);
        if (photoComparisionService == null) {
            photoComparisionService = new PhotoComparisionServiceImpl();
            session.setAttribute(AppConstant.PHOTO_COMPARISION_SERVICE, photoComparisionService);
        }
        try {
            PhotoComparisionDto photoComparisionDto = photoComparisionService.getPhotoComparisionDto(claimNo, policyNo, vehicleNo, comparisionType, inspectionJobNo, documentTypeId);
            request.setAttribute(AppConstant.PHOTO_COMPARISION_DTO, photoComparisionDto);
            request.setAttribute(AppConstant.CLAIM_NO, claimNo);
            request.setAttribute(AppConstant.COMPARISION_TYPE, comparisionType);
            request.setAttribute(AppConstant.INSPECTION_JOB_NO, inspectionJobNo);
            request.setAttribute(AppConstant.DOCUMENT_TYPE, documentTypeId);
            request.setAttribute(AppConstant.COMPARISION_TAB_NO, comparisionTabNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/photoComparison.jsp");
    }

    private void viewPdfViewer(HttpServletRequest request, HttpServletResponse response, String comparisionTabNo) {
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.COMPARISION_TAB_NO, comparisionTabNo);
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/pdfViewerAll.jsp");

    }

    private void viewBillDocumentViewer(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        boolean pendingInspections = false;
        try {
            ClaimDocumentService claimDocumentService = (ClaimDocumentService) session.getAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE);
            if (claimDocumentService == null) {
                claimDocumentService = new ClaimDocumentServiceImpl();
                session.setAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE, claimDocumentService);
            }
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            pendingInspections = null != request.getParameter("PENDING_INSPEC") && !request.getParameter("PENDING_INSPEC").isEmpty() && Boolean.parseBoolean(request.getParameter("PENDING_INSPEC"));
            Boolean isEngDoc = null != request.getParameter("ENG_DOC") && !request.getParameter("ENG_DOC").isEmpty() && Boolean.parseBoolean(request.getParameter("ENG_DOC"));
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimDocumentService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.PENDING_INSPECTION, pendingInspections);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            request.setAttribute("ENG_DOC", isEngDoc);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/pdfBillCheckViewerAll.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void updateBillCheckDetails(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;

        Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
        Integer claimNo = Integer.parseInt(request.getParameter("claimNo") == null ? AppConstant.ZERO : request.getParameter("claimNo"));
        String remark = request.getParameter("remark");
        ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        ClaimDocumentService claimDocumentService = (ClaimDocumentService) session.getAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE);
        if (claimDocumentService == null) {
            claimDocumentService = new ClaimDocumentServiceImpl();
            session.setAttribute(AppConstant.CLAIM_DOCUMENT_SERVICE, claimDocumentService);
        }
        try {
            claimDocumentDto.setRefNo(refNo);
            claimDocumentDto.setClaimNo(claimNo);
            claimDocumentDto.setDocumentStatus(AppConstant.DOCUMENT_CHECK_STATUS);
            claimDocumentDto.setIsCheck(AppConstant.YES);
            claimDocumentDto.setCheckUser(user.getUserId());
            claimDocumentDto.setCheckDateTime(Utility.sysDateTime());
            claimDocumentDto.setBillSummaryRemark(remark);
            claimDocumentDto.setBillSummaryCheckUser(user.getUserId());
            claimDocumentDto.setBillSummaryCheckDateTime(Utility.sysDateTime());
            claimDocumentDto.setCalculationSheetNo((Integer) request.getSession().getAttribute(AppConstant.CALCULATION_SHEET_NO));
            claimDocumentService.updateBillCheckDetails(claimDocumentDto, user);
            successMessage = "Bill checked successfully submitted";
        } catch (Exception e) {
            errorMessage = "Bill check failed";
            LOGGER.error(e.getMessage());
        } finally {
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimDocumentService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
            request.setAttribute(AppConstant.ERROR_MESSAGE, errorMessage);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/pdfBillCheckViewerAll.jsp");
        }
    }

}
