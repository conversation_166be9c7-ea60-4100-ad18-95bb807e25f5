package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.AssessorFeeService;
import com.misyn.mcms.claim.service.impl.AssessorFeeServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.LocalDateTimeAdapter;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@WebServlet(name = "AssessorFeeController", urlPatterns = "/assessorFeeController/*")
public class AssessorFeeController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorFeeController.class);
    private final AssessorFeeService assessorFeeService = new AssessorFeeServiceImpl();
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/viewAssessorFeeUI":
                    viewAssessorFeeUI(request, response);
                    break;
                case "/saveAssessorFee":
                    saveAssessorFee(request, response, user);
                    break;
                case "/updateAssessorFee":
                    updateAssessorFee(request, response, user);
                    break;
                case "/viewAssessorFee":
                    viewAssessorFee(request, response);
                    break;
                case "/viewAssessorFeeList":
                    viewAssessorFeeList(request, response);
                    break;
                case "/deleteAssessorFee":
                    deleteAssessorFee(request, response, user);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewAssessorFeeUI(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            List<ListItemDto> inspectionTypeDtoList = assessorFeeService.getInspectionTypeList();
            List<ListItemDto> dayTypeDtoList = assessorFeeService.getDayTypeList();

            request.setAttribute(AppConstant.DAY_TYPE_LIST, dayTypeDtoList);
            request.setAttribute(AppConstant.INSPECTION_TYPE_LIST, inspectionTypeDtoList);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/assessorFeeConfig/assessorFeeConfig.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveAssessorFee(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        AssessorFeeDto assessorFeeDto = new AssessorFeeDto();
        AssessorFeeDto savedDto;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(assessorFeeDto, request.getParameterMap());
            if (Objects.nonNull(user)) {
                assessorFeeDto.setInputUser(user.getUserId());
                assessorFeeDto.setLastModifiedUser(user.getUserId());
            }
            savedDto = assessorFeeService.saveAssessorFee(assessorFeeDto);

            if (null != savedDto) {
                json = "Saved Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage(e.getMessage());
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void updateAssessorFee(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        AssessorFeeDto assessorFeeDto = new AssessorFeeDto();
        AssessorFeeDto savedDto;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(assessorFeeDto, request.getParameterMap());
            if (Objects.nonNull(user)) {
                assessorFeeDto.setLastModifiedUser(user.getUserId());
            }
            savedDto = assessorFeeService.updateAssessorFee(assessorFeeDto);

            if (null != savedDto) {
                json = "Saved Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage(e.getMessage());
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void viewAssessorFee(HttpServletRequest request, HttpServletResponse response) {
        Integer id = request.getParameter("assessorFeeDetailId") == null || request.getParameter("assessorFeeDetailId").isEmpty()
                ? 0
                : Integer.valueOf(request.getParameter("assessorFeeDetailId"));

        Gson gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                .create();
        try {
            AssessorFeeDto claimSpecialCaseTypeDto = assessorFeeService.getAssessorFee(id);
            String json = gson.toJson(claimSpecialCaseTypeDto);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void viewAssessorFeeList(HttpServletRequest request, HttpServletResponse response) {

        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                .create();
        String json;

        Integer assessorFeeDetailId = request.getParameter("assessorFeeDetailId") == null || request.getParameter("assessorFeeDetailId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("assessorFeeDetailId"));
        Integer inspectionTypeId = request.getParameter("inspectionTypeId") == null || request.getParameter("inspectionTypeId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("inspectionTypeId"));
        Integer dayTypeId = request.getParameter("dayTypeId") == null || request.getParameter("dayTypeId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("dayTypeId"));
//        String claimNo = request.getParameter("claimNo") == null ? AppConstant.STRING_EMPTY : request.getParameter("claimNo");
//        String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("assessor_fee_detail_id", assessorFeeDetailId.equals(0) ? "" : String.valueOf(assessorFeeDetailId), FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("inspection_type_id", inspectionTypeId.equals(0) ? "" : String.valueOf(inspectionTypeId), FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("day_type_id", dayTypeId.equals(0) ? "" : String.valueOf(dayTypeId), FieldParameterDto.SearchType.Like, parameterList);
//            this.addFieldParameter("claim_no", claimNo, FieldParameterDto.SearchType.Like, parameterList);
//            this.addFieldParameter("remark", remark, FieldParameterDto.SearchType.Like, parameterList);


            switch (orderColumnName) {
                case "assessorFeeDetailId":
                    orderColumnName = "assessor_fee_detail_id";
                    break;
                case "inspectionTypeId":
                    orderColumnName = "inspection_type_id";
                    break;
                case "dayTypeId":
                    orderColumnName = "day_type_id";
                    break;

            }
            boolean isSearch = request.getParameter("isSearch") != null && (request.getParameter("isSearch").equalsIgnoreCase("1"));
            DataGridDto data = assessorFeeService.getAssessorFeeDetailDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, isSearch);

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void deleteAssessorFee(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        Gson gson = new Gson();
        String json;
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            Integer id = request.getParameter("assessorFeeDetailId") == null || request.getParameter("assessorFeeDetailId").isEmpty() ? 0 : Integer.valueOf(request.getParameter("assessorFeeDetailId"));
            assessorFeeService.deleteAssessorFee(id, user.getUserId());
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
           /* LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);*/
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage(e.getMessage());
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }
}
