/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.controller.common;

import com.misyn.mcms.admin.UserRights;
import com.misyn.mcms.admin.UserRightsManager;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.log.AdminLog;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@WebServlet(name = "LoginController", urlPatterns = {"/login.do", "/sidemenu.do", "/submit.do", "/changepsw.do", "/welcome.do", "/logout.do"})
@MultipartConfig(fileSizeThreshold = 1024 * 1024 * 1, // 1MB
        maxFileSize = 1024 * 1024 * 1, // 1MB
        maxRequestSize = 1024 * 1024 * 1)   // 50MB
public class LoginController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class);

    protected void processGetRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String servletPath = request.getServletPath();
        HttpSession session = request.getSession();
        UserRightsManager userRightsManager = (UserRightsManager) session.getAttribute(AppConstant.USER_RIGHTS_MANAGER_BEAN);

        if (userRightsManager == null) {
            userRightsManager = new UserRightsManager();
            session.setAttribute(AppConstant.USER_RIGHTS_MANAGER_BEAN, userRightsManager);
        }
        try {
            switch (servletPath) {
                case "/login.do":
                    request.setAttribute(AppConstant.ERROR_MESSAGE, AppConstant.STRING_EMPTY);
                    requestDispatcher(request, response, "/WEB-INF/jsp/admin/common/login.jsp");
                    break;
                case "/sidemenu.do":
                    viewSideMenu(request, response, userRightsManager);
                    break;
                case "/submit.do":
                    menuSubmit(request, response, userRightsManager);
                    break;
                case "/changepsw.do":
                    requestDispatcher(request, response, "/WEB-INF/jsp/admin/user_password_reset/password_change.jsp");
                    break;
                case "/welcome.do":
                    requestDispatcher(request, response, "/WEB-INF/jsp/admin/common/welcome.jsp");
                    break;
                case "/logout.do":
                    session.invalidate();
                    requestDispatcher(request, response, "/WEB-INF/jsp/admin/common/login.jsp");
                    break;

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewSideMenu(HttpServletRequest request, HttpServletResponse response, UserRightsManager userRightsManager) {
        List<UserRights> userRightList;
        HttpSession session = request.getSession();
        UserDto user = getSessionUser(request);
        try {
            userRightList = userRightsManager.getUserRightsList(user.getUserCode(), user.getAccessUserType());
            session.setAttribute(AppConstant.G_MENU_ITEM, userRightsManager.getUserRightsMap());
            request.setAttribute(AppConstant.USER_RIGHT_LIST, userRightList);
            requestDispatcher(request, response, "/WEB-INF/jsp/admin/common/sideMenu.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void menuSubmit(HttpServletRequest request, HttpServletResponse response, UserRightsManager userRightsManager) {
        HttpSession session = request.getSession();
        UserDto user = getSessionUser(request);
        int menuId = 0;
        int itemId = 0;
        String key = "";
        try {
            menuId = Integer.parseInt(request.getParameter(AppConstant.P_MENU_ID) == null ? AppConstant.ZERO : request.getParameter(AppConstant.P_MENU_ID));
            itemId = Integer.parseInt(request.getParameter(AppConstant.P_ITEM_ID) == null ? AppConstant.ZERO : request.getParameter(AppConstant.P_ITEM_ID));
            key = menuId + AppConstant.STRING_EMPTY + itemId;
            if (!(menuId == 25 && itemId == 1) && !(menuId == 13 && itemId == 3)) {
                session.removeAttribute(AppConstant.SESSION_CLAIM_DTO);
            }


            session.removeAttribute(AppConstant.SESSION_FIELD);
            session.removeAttribute(AppConstant.SESSION_ASC_DESC_STATUS);
            session.removeAttribute(AppConstant.SESSION_IS_ASC);
            session.removeAttribute(AppConstant.SESSION_TYPE);

            session.removeAttribute(AppConstant.SESSION_CLICK_PAGE_NUMBER);
            session.removeAttribute(AppConstant.SESSION_SORT_FIELD);
            session.removeAttribute(AppConstant.RIGHT_I);
            session.removeAttribute(AppConstant.RIGHT_M);
            session.removeAttribute(AppConstant.RIGHT_D);
            session.removeAttribute(AppConstant.RIGHT_A1);
            session.removeAttribute(AppConstant.RIGHT_A2);
            session.removeAttribute(AppConstant.AUTHTYPE);
            session.removeAttribute(AppConstant.SELECT_MENU_NAME);
            session.removeAttribute(AppConstant.SELECT_SUB_MENU_NAME);

            session.removeAttribute(AppConstant.SEARCH_POLICY_NO);
            session.removeAttribute(AppConstant.SEARCH_CHASSIS_NO);
            session.removeAttribute(AppConstant.SEARCH_ENGINE_NO);
            session.removeAttribute(AppConstant.SEARCH_REF_NUMBER);
            session.removeAttribute(AppConstant.SEARCH_VEHICLE_NUMBER);
            session.removeAttribute(AppConstant.SEARCH_INSURED_NAME);
            session.removeAttribute(AppConstant.SEARCH_INSURED_NIC);
            session.removeAttribute(AppConstant.SEARCH_CLAIM_NUMBER);
            session.removeAttribute(AppConstant.SEARCH_FROM_DATE);
            session.removeAttribute(AppConstant.SEARCH_TO_DATE);
            session.removeAttribute(AppConstant.SEARCH_CLAIM_STATUS);
            session.removeAttribute(AppConstant.SEARCH_FOLLOWUP_CALL_DONE);
            session.removeAttribute(AppConstant.SEARCH_USER_NAME);
            session.removeAttribute(AppConstant.SEARCH_POLICY_STATUS);
            session.removeAttribute(AppConstant.SEARCH_ISF_CLAIM_NUMBER);
            session.removeAttribute(AppConstant.SEARCH_CLI_NUMBER);
            session.removeAttribute(AppConstant.SEARCH_POLICY_CHANNEL_TYPE);


            Map<String, UserRights> userRightMap = (Map<String, UserRights>) session.getAttribute(AppConstant.G_MENU_ITEM);
            if (userRightMap != null) {
                UserRights userRight = userRightMap.get(key);
                if (userRight != null) {
                    session.setAttribute(AppConstant.RIGHT_I, new String(userRight.getV_input()));
                    session.setAttribute(AppConstant.RIGHT_M, new String(userRight.getV_modify()));
                    session.setAttribute(AppConstant.RIGHT_D, new String(userRight.getV_delete()));
                    session.setAttribute(AppConstant.RIGHT_A1, new String(userRight.getV_auth1()));
                    session.setAttribute(AppConstant.RIGHT_A2, new String(userRight.getV_auth2()));
                    session.setAttribute(AppConstant.USER_RIGHT, userRight);
                    session.setAttribute(AppConstant.SELECT_MENU_NAME, userRight.getV_mnuname());
                    session.setAttribute(AppConstant.SELECT_SUB_MENU_NAME, userRight.getV_itmname());

                    this.unlockedIntimation(request, user);


                    String logString = "Access application -: " + userRight.getV_mnuname() + " -->" + userRight.getV_itmname() + " , User=" + user.getUserId();
                    AdminLog.getInstance().logRecord(user.getUserId(), user.getIpaddress(), logString);
                    requestDispatcher(request, response, "/WEB-INF/jsp/admin/common/menuSubmit.jsp");
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processGetRequest(request, response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

    }


    @Override
    public String getServletInfo() {
        return "MI Synergy pvt";
    }// </editor-fold>

}
