package com.misyn.mcms.claim.controller.callcenter;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.CallCenterService;
import com.misyn.mcms.claim.service.ClaimHandlerService;
import com.misyn.mcms.claim.service.RequestAriService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "RequestAriController", urlPatterns = "/RequestAriController/*")
public class RequestAriController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestAriController.class);
    private int draw = 1;
    private CallCenterService callCenterService = null;
    private RequestAriService requestAriService = null;
    private ClaimHandlerService claimHandlerService = null;


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        callCenterService = getCallCenterServiceBySession(request);
        requestAriService = getByRequestAri(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        ClaimUserTypeDto claimUserTypeDto;
        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/claimListView":
                    claimHandlerList(request, response);
                    break;
                case "/claimList":
                    claimList(request, response);
                    // requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/assessor/requestARI.jsp");
                    break;
                case "/viewEdit":
                    viewEdit(request, response);
                    break;
                case "/saveAri":
                    saveAri(request, response);
                    break;
                case "/ariListView":
                    int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    removeSessionType(request, response);
                    updateSessionType(request, response, type);
                    Integer claimNo = null == request.getParameter("P_N_CLIM_NO") || request.getParameter("P_N_CLIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    request.setAttribute(AppConstant.P_N_CLIM_NO, claimNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/requestAriList.jsp");
                    break;
                case "/ariList":
                    ariList(request, response);
                    break;
                case "/updateRevoke":
                    updateRequestedAriRemark(request, response);
                    break;
                case "/updateAri":
                    updateAri(request, response);
                    break;
                case "/remarkView":
                    int requestAriId = request.getParameter("N_ARI_ID") == null ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_ARI_ID"));
                    type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    request.setAttribute("N_ARI_ID", requestAriId);
                    request.setAttribute("remarkList", requestAriService.getRemarkListByRemarkId(requestAriId));
                    request.setAttribute(AppConstant.TYPE, type);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/requestARIRemark.jsp");
                    break;
                case "/saveRemark":
                    saveRemark(request, response);
                    break;

                case "/requestSpecialRemark":
                    requestAriId = request.getParameter("N_ARI_ID") == null ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_ARI_ID"));
                    request.setAttribute("remarkList", requestAriService.getRemarkListByRemarkId(requestAriId));
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/requestAriSpecialRemarks.jsp");
                    break;
                case "/sendAriPendingMail":
                    sendAriPendingMail(request, response);
                    break;
                case "/loadAriPage":
                    loadAriRequestPage(request, response);
                    break;
                case "/ariRequestUsers":
                    getUserListByAccessUserType(request, response, "27, 28");
                    break;
                case "/allAriRequestUsers":
                    int ariType = null == request.getParameter("TYPE") || request.getParameter("TYPE").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("TYPE"));
                    getUserListByAccessUserType(request, response, ariType == 4 ? "20,21,21,22,23,24,27,28" : "20,21,21,22,23,24,25,104");
                    break;
                case "/loadClaimStampsForAri":
                    loadClaimStampsForAri(request, response);
                    break;
                case "/revokeAriRequest":
                    revokeAriRequest(request, response);
                    break;
                case "/loadCustomerDetail":
                    loadCustomerDetails(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void loadCustomerDetails(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        try {
            RequestAriDto requestAriDto = requestAriService.searchByClaimNoPending(claimNo);
            request.setAttribute(AppConstant.REQUEST_ARI_DETAILS, requestAriDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/customerDetail.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void revokeAriRequest(HttpServletRequest request, HttpServletResponse response) {
        Integer requestAriId = null == request.getParameter("N_REQUEST_ARI_ID") || request.getParameter("N_REQUEST_ARI_ID").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_REQUEST_ARI_ID"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        Gson gson = new Gson();
        String json;
        UserDto user;
        try {
            user = getSessionUser(request);
            boolean isUpdated = requestAriService.revokeAriRequest(requestAriId, remark, user);
            json = gson.toJson(isUpdated ? "SUCCESS" : "FAIL");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void loadClaimStampsForAri(HttpServletRequest request, HttpServletResponse response) {
        boolean salvageArranged = false;
        boolean ariArranged = false;
        boolean ariRequested = false;
        boolean salvageRequested = false;
        Integer claimNoToSearch = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        try {
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNoToSearch);
            int ariRequestedID = requestAriService.isAriOrSalvageRequested(claimNoToSearch);
            int salvageORAriArranged = requestAriService.isSalvageORAriArranged(claimNoToSearch);
            BulkCloseDetailDto bulkCloseDetailDto = requestAriService.getBulkCloseDetail(claimNoToSearch);
            if (ariRequestedID == AppConstant.ARI_REQUEST || ariRequestedID == AppConstant.ARI || ariRequestedID == AppConstant.ARI_SALVAGE) {
                ariRequested = true;
            } else if (ariRequestedID == AppConstant.SALVAGE_REQUEST || ariRequestedID == AppConstant.COLLECT_SALVAGE) {
                salvageRequested = true;
            }

            if (salvageORAriArranged == AppConstant.ARI_INSPECTION) {
                ariArranged = true;
            } else if (salvageORAriArranged == AppConstant.SALVAGE_INSPECTION) {
                salvageArranged = true;
            }

            if (salvageArranged || ariArranged) {
                salvageRequested = false;
                ariRequested = false;
            }
            request.setAttribute(AppConstant.ARI_REQUESTED, ariRequested);
            request.setAttribute(AppConstant.ARI_ARRANGED, ariArranged);
            request.removeAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO);
            request.getSession().removeAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO);
            request.removeAttribute(AppConstant.CLAIM_HANDLER_DTO);
            request.getSession().removeAttribute(AppConstant.CLAIM_HANDLER_DTO);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);
            request.setAttribute(AppConstant.SALVAGE_ARRANGED, salvageArranged);
            request.setAttribute(AppConstant.SALVAGE_REQUESTED, salvageRequested);
            request.setAttribute(AppConstant.BULK_CLOSE_DETAIL_DTO, bulkCloseDetailDto);
            request.setAttribute(AppConstant.CLOSE_STATUS_CLOSE, AppConstant.CLOSE_STATUS_CLOSE.equals(claimHandlerDto.getCloseStatus()));
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimStampPage.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void loadAriRequestPage(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("P_N_CLIM_NO") || request.getParameter("P_N_CLIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        try {
            RequestAriDto ariDetails = requestAriService.getAriDetails(claimNo);
            request.setAttribute(AppConstant.REQUEST_ARI_DETAILS, ariDetails);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/inspectiondetails/motorengineer/ariRequestView.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void sendAriPendingMail(HttpServletRequest request, HttpServletResponse response) {
        Integer id = request.getParameter("N_REQ_REF_ID") == null || request.getParameter("N_REQ_REF_ID").isEmpty() ? 0 : Integer.parseInt(request.getParameter("N_REQ_REF_ID"));
        String reason = request.getParameter("rejReason") == null || request.getParameter("rejReason").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("rejReason");
        String remark = request.getParameter("ariRemark") == null || request.getParameter("ariRemark").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("ariRemark");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            if (id != 0 && !reason.isEmpty()) {
                requestAriService.sendAriPendingMail(id, reason, remark, user);
                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            } else {
                json = gson.toJson("FAIL");
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void claimList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String insuredName = request.getParameter(AppConstant.TXT_INSURED_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSURED_NAME);
        String engineNo = request.getParameter(AppConstant.TXT_ENGINE_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ENGINE_NO);
        String insuredNic = request.getParameter(AppConstant.TXT_INSURED_NIC) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSURED_NIC);
        String chassisNo = request.getParameter(AppConstant.TXT_CHASSIS_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CHASSIS_NO);
        String followupCallDone = request.getParameter(AppConstant.TXT_FOLLOWUP_CALL_DONE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FOLLOWUP_CALL_DONE);
        String callUserName = request.getParameter(AppConstant.TXT_CALL_USER_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CALL_USER_NAME);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_CUST_NAME", insuredName, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_CUST_NIC", insuredNic, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_ENGINE_NO", engineNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_CHASSIS_NO", chassisNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_CALL_USER", callUserName, FieldParameterDto.SearchType.Like, parameterList);
            if (!"0".equals(status)) {
                this.addFieldParameter("t1.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!"0".equals(followupCallDone)) {
                this.addFieldParameter("t1.V_IS_FOLLOWUP_CALL_DONE", followupCallDone, FieldParameterDto.SearchType.Equal, parameterList);
            }

            switch (orderColumnName) {
                case "refNo":
                    orderColumnName = "t1.N_REF_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumber":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "v_vehicle_number";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "accidDate":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "placeOfAccid":
                    orderColumnName = "t1.V_PLACE_OF_ACCID";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t2.v_status_desc";
                    break;
                case "chassisNo":
                    orderColumnName = "t3.V_CHASSIS_NO";
                    break;

            }
            DataGridDto data = callCenterService.getClaimDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void claimHandlerList(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,84)");

        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
        removeSessionType(request, response);
        updateSessionType(request, response, type);
        request.setAttribute("statusList", popupItemDtoList);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/ariClaimList.jsp");
    }

    private void saveAri(HttpServletRequest request, HttpServletResponse response) {
        RequestAriDto requestAriDto = new RequestAriDto();
        try {
            InspectionDetailsDto inspectionDetailsDto = getInspectionDetails(request);
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.populate(requestAriDto, request.getParameterMap());
            RequestAriDto insert = requestAriService.insert(requestAriDto, getSessionUser(request));
            if (null != insert) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Updated");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
        }

        requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/requestARI.jsp");
    }

    private void viewEdit(HttpServletRequest request, HttpServletResponse response) {

        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        request.setAttribute("claimNo", claimNo);
        try {

            RequestAriDto requestAri = requestAriService.searchByClaimNo(claimNo);
            if (null != requestAri) {
                request.setAttribute(AppConstant.ERROR_MESSAGE, "Record Already exists");
                request.setAttribute(AppConstant.IS_DISABLED, "Y");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/requestARI.jsp");

    }

    private void ariList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        HttpSession session = request.getSession();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String requestDate = null == request.getParameter(AppConstant.TXT_REQUEST_DATE_TIME) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REQUEST_DATE_TIME);
        String requestUser = null == request.getParameter(AppConstant.TXT_REQUEST_USER) ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REQUEST_USER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        boolean isAssessor = (boolean) session.getAttribute(AppConstant.IS_ASSESSOR);
        boolean isRte = (boolean) session.getAttribute(AppConstant.IS_RTE);
        boolean isSpcood = (boolean) session.getAttribute(AppConstant.IS_SPARE_COORDINATOR);
        boolean isScrutinizing = (boolean) session.getAttribute(AppConstant.IS_SCRUTINIZING_COORDINATOR);
        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);
            String userId = getSessionUser(request).getUserId();
            String empNo = getSessionUser(request).getEmployeeNumber();

            this.addFieldParameter("t2.vehicle_no", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);


            if (type == 4) {
                this.addFieldParameter("t1.claim_no", claimNumber, FieldParameterDto.SearchType.Like, parameterList);
                if (!"0".equals(status)) {
                    String statusVal = AppConstant.STRING_EMPTY;
                    statusVal = switch (status) {
                        case "1" -> "C";
                        case "2" -> "R";
                        case "3" -> "S";
                        case "4" -> "P";
                        case "5" -> "U";
                        case "6" -> "PR";
                        case "7" -> "FR";
                        default -> statusVal;
                    };

                    this.addFieldParameter("t2.status", statusVal, FieldParameterDto.SearchType.Equal, parameterList);
                } else {
                    this.addFieldParameter("t2.status", "'C','S','PR'", FieldParameterDto.SearchType.IN, parameterList);
                }

                if (isAssessor) {
                    this.addFieldParameter("t2.requested_assessor_code", empNo, FieldParameterDto.SearchType.Equal, parameterList);
                }

                if (isRte) {
                    this.addFieldParameter("t2.rte_code", userId, FieldParameterDto.SearchType.Equal, parameterList);
                }

                if (isSpcood || isScrutinizing) {
                    this.addFieldParameter("t3.V_IS_FILE_STORE", "'AR','N'", FieldParameterDto.SearchType.IN, parameterList);
                }

            } else {
                this.addFieldParameter("t3.n_accessusrtype", "'27','28'", type == 5 ? FieldParameterDto.SearchType.NOT_IN : FieldParameterDto.SearchType.IN, parameterList);
                String statusVal = AppConstant.STRING_EMPTY;
                if (!"0".equals(status)) {
                    switch (status) {
                        case "1":
                            statusVal = "C";
                            break;
                        case "2":
                            statusVal = "R";
                            break;
                        case "3":
                            statusVal = "S";
                            break;
                        case "4":
                            statusVal = "P";
                            break;
                        case "5":
                            statusVal = "U";
                            break;

                    }
                    this.addFieldParameter("t2.status", statusVal, FieldParameterDto.SearchType.Equal, parameterList);
                } else {
                    this.addFieldParameter("t2.status", "'P','U'", FieldParameterDto.SearchType.IN, parameterList);
                }

                this.addFieldParameter("t2.claim_no", claimNumber, FieldParameterDto.SearchType.Like, parameterList);
            }


            switch (orderColumnName) {
                case "id":
                    orderColumnName = "t2.request_ari_id";
                    break;
                case "claimNo":
                    orderColumnName = type == 4 ? "t1.claim_no" : "t2.claim_no";
                    break;
                case "vehicleNo":
                    orderColumnName = "t2.vehicle_no";
                    break;
                case "contactNo":
                    orderColumnName = "t2.contact_no";
                    break;
                case "accidentDate":
                    orderColumnName = "t2.accident_date";
                    break;
                case "requestedUser":
                    orderColumnName = "t2.requested_user";
                    break;
                case "requestedDate":
                    orderColumnName = "t2.requested_date";
                    break;
                case "assignedAssessorCode":
                    orderColumnName = "t2.assigned_assesr_code";
                    break;
                case "assignedAssessor":
                    orderColumnName = "t2.assigned_assessor_name";
                    break;
                case "customerName":
                    orderColumnName = "t2.customer_name";
                    break;
                case "status":
                    orderColumnName = "t2.status";
                    break;
                case "daysFromRequest":
                    orderColumnName = "days_from_request";
                    break;
                case "daysFromAssignment":
                    orderColumnName = "assigned_days";
                    break;
                case "assigningDateTime":
                    orderColumnName = "t2.assiging_datetime";
                    break;
                case "assessorSubmittedDate":
                    orderColumnName = "t2.assessor_submitted_datetime";
                    break;
            }

            if (!requestUser.isEmpty()) {
                this.addFieldParameter("t2.requested_user", requestUser, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!requestDate.isEmpty()) {
                addFieldParameter("t2.requested_date", requestDate, FieldParameterDto.SearchType.Like, parameterList);
            }
            DataGridDto data;
            if (type == 4) {
                if (isSpcood || isScrutinizing) {
                    data = requestAriService.getRequestDataGridDtoByUser(parameterList, draw++, start, length, columnOrder, orderColumnName, isSpcood, userId);
                } else {
                    data = requestAriService.getRequestDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName);
                }
            } else {
                data = requestAriService.getRequestDataGridDtoPending(parameterList, draw++, start, length, columnOrder, orderColumnName);
            }
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void updateRequestedAriRemark(HttpServletRequest request, HttpServletResponse response) {
        try {
            String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");
            Integer id = request.getParameter("N_REQ_REF_ID") == null ? 0 : Integer.parseInt(request.getParameter("N_REQ_REF_ID"));
            UserDto user = getSessionUser(request);
            boolean isUpdated = requestAriService.updateStatusAndRevokeByRef(remark, id, user);

            if (isUpdated) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Updated");
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Faild");
        } finally {
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/requestAriList.jsp");
        }
    }

    private void updateAri(HttpServletRequest request, HttpServletResponse response) {
        RequestAriDto requestAriDto = new RequestAriDto();
        String successMessage;
        String errorMessage;
        String json;
        Gson gson = new Gson();
        try {
            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.populate(requestAriDto, request.getParameterMap());
            RequestAriDto insert = requestAriService.updateAri(requestAriDto, getSessionUser(request));
            if (null != insert) {
                successMessage = "SUCCESS";
                json = gson.toJson(successMessage);
                printWriter(request, response, json);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            errorMessage = "ERROR";
            json = gson.toJson(errorMessage);
            printWriter(request, response, json);
        }

    }

    private void saveRemark(HttpServletRequest request, HttpServletResponse response) {
        Integer requestAri = request.getParameter("requestId") == null ? 0 : Integer.parseInt(request.getParameter("requestId"));
        String remark = request.getParameter("remark");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            RequestAriRemarkDto requestAriRemarkDto = new RequestAriRemarkDto();
            requestAriRemarkDto.setRemark(remark);
            requestAriRemarkDto.setRequestAriId(requestAri);
            requestAriService.saveRemark(requestAriRemarkDto, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }


}
