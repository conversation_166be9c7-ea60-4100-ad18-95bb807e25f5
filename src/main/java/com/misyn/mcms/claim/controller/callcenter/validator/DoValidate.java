package com.misyn.mcms.claim.controller.callcenter.validator;

import com.misyn.mcms.claim.dto.ErrorMessageDto;
import com.misyn.mcms.claim.dto.FormFieldDto;
import com.misyn.mcms.claim.service.FormFieldService;
import com.misyn.mcms.claim.service.impl.FormFieldServiceImpl;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

public class DoValidate<T> {
    private static final Logger LOGGER = LoggerFactory.getLogger(DoValidate.class);

    public ErrorMessageDto validate(T t, Integer formNameId) {

        FormFieldService formFieldService = new FormFieldServiceImpl();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();

        Set<ConstraintViolation<T>> cvs = validator.validate(t);

        if (!cvs.isEmpty()) {

            for (ConstraintViolation<T> cv : cvs) {
                try {
                    StringBuilder err = new StringBuilder();
                    FormFieldDto fieldDetails = formFieldService.getDtoFieldRelatedField(formNameId, cv.getPropertyPath().toString());
                    if (null != fieldDetails) {
                        errorMessageDto.setErrorCode(520);
                        err.append(fieldDetails.getFormFieldName().isEmpty() || null == fieldDetails.getFormFieldName() ?
                                fieldDetails.getDtoFieldName() : fieldDetails.getFormFieldName());
                        err.append(" ");
                        err.append(cv.getMessage());
                        errorMessageDto.setMessage(err.toString());
                        errorMessageDto.setDtoFieldName(fieldDetails.getDtoFieldName());
                        errorMessageDto.setFormFieldName(fieldDetails.getFormFieldName());
                        break;
                    }

                } catch (Exception e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }

        return errorMessageDto;
    }
}
