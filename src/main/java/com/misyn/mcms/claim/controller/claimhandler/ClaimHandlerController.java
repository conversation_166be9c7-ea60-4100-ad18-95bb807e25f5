package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.enums.DocumentStatusEnum;
import com.misyn.mcms.claim.enums.InvestigationStatusEnum;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "ClaimHandlerController", urlPatterns = "/ClaimHandlerController/*")
public class ClaimHandlerController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimHandlerController.class);
    private CallCenterService callCenterService;
    private ClaimHandlerService claimHandlerService;
    private ClaimWiseDocumentService claimWiseDocumentService;
    private ClaimDocumentService claimDocumentService;
    private ReminderPrintService reminderPrintService;
    private InspectionDetailsService inspectionDetailsService = null;
    private InvestigationDetailsService investigationDetailsService = null;
    private SupplyOrderService supplyOrderService = null;
    private RequestAriService requestAriService = null;
    private CalculationSheetService calculationSheetService = null;
    private MotorEngineerService motorEngineerService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        process(request, response);
    }

    private void setPopupList(HttpServletRequest request) {
        String strInsepctionTypes = DbRecordCommonFunction.getInstance()
                .getPopupList("claim_inspection_type ", "inspection_type_id", "inspection_type_desc", "inspection_type_id <> 0", "");

        String strInsepctionReasonTypes = DbRecordCommonFunction.getInstance()
                .getPopupList("claim_inspection_type_reason ", "N_ID", "V_REASON", "V_REC_STATUS='A' AND N_ID <>0 ", "");

        String strRejectedReason = DbRecordCommonFunction.getInstance()
                .getPopupList("claim_assessor_reject_reason ", "N_ID", "V_REASON", "V_REC_STATUS='A' AND N_ID <>0   ", "");

        String strReassigningReason = DbRecordCommonFunction.getInstance()
                .getPopupList("claim_assessor_reassign_reason ", "N_ID", "V_REASON", " V_REC_STATUS='A' AND N_ID <>0  ", "");
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        //    assessorService = getAssessorServiceBySession(request);
        //   cityService = getCityServiceBySession(request);
        //   assessorAllocationService = getAssessorAllocationServiceServiceBySession(request);
        //  districtService = getDistrictServiceBySession(request);
        callCenterService = getCallCenterServiceBySession(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        claimWiseDocumentService = getClaimWiseDocumentServiceBySession(request);
        reminderPrintService = getReminderPrintServiceBySession(request);
        inspectionDetailsService = getInspectionDetailsBySession(request);
        investigationDetailsService = getInvestigationDetailsBySession(request);
        supplyOrderService = getSupplyOrderServiceBySession(request);
        requestAriService = getByRequestAri(request);
        calculationSheetService = getCalculationSheetServiceBySession(request);
        claimDocumentService = getClaimDocumentServiceBySession(request);
        motorEngineerService = getMotorEngineerBySession(request);
        List<ClaimLogsDto> logList;
        List<SpecialRemarkDto> specialRemarkList;
        ClaimUserTypeDto claimUserTypeDto;
        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/claimHandlerList":
                    claimHandlerList(request, response);
                    break;
                case "/claimList":
                    claimList(request, response);
                    break;
                case "/viewEdit":
                    viewEdit(request, response);
                    break;
                case "/updateFinancialInfo":
                    updateFinancialInfo(request, response);
                    break;
                case "/updateLiabilityCheckList":
                    updateLiabilityInfo(request, response);
                    break;
                case "/saveRemark":
                    saveRemark(request, response);
                    break;
                case "/documentUpload":
                    viewDocumentUpload(request, response);
                    break;
                case "/defineDocument":
                    viewDefineDocument(request, response);
                    break;
                case "/updateDefineDocument":
                    updateDefineDocument(request, response);
                    break;
                case "/updateDefineDocumentOnOther":
                    updateDefineDocumentOnOther(request, response);
                    break;
                case "/viewSpecialRemark":
                    Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    specialRemarkList = claimHandlerService.searchAllRemarksByClaimNo(claimNo);
                    request.setAttribute(AppConstant.CLAIM_HANDLER_SPECIAL_REMARK, specialRemarkList);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/specialRemarks.jsp");
                    break;
                case "/viewBranchRemark":
                    claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    List<SpecialRemarkDto> branchRemarkList = claimHandlerService.searchRemarksByClaimNoAndDepartmentId(claimNo, AppConstant.BRANCH_DEPARTMENT_ID);
                    request.setAttribute(AppConstant.CLAIM_HANDLER_BRANCH_REMARK, branchRemarkList);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/branchRemarks.jsp");
                    break;
                case "/viewLogTrail":
                    claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    logList = claimHandlerService.getLogList(claimNo);
                    request.setAttribute(AppConstant.LOG_TARILS, logList);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/logDetails.jsp");
                    break;
                case "/viewGenerateReminder":
                    viewReminderPrint(request, response);
                    break;
                case "/generateReminder":
                    generateReminderPrint(request, response);
                    break;
                case "/viewGeneratedReminderLetter":
                    viewGeneratedReminderLetter(request, response);
                    break;
                case "/viewRepudiatedLetter":
                    viewRepudiatedLetter(request, response);
                    break;
                case "/documentViewer":
                    viewDocumentViewer(request, response);
                    break;
                case "/checkDocument":
                    checkDocument(request, response);
                    break;
                case "/holdDocument":
                    holdDocument(request, response);
                    break;
                case "/rejectDocument":
                    rejectDocument(request, response);
                    break;
                case "/approveInitLiability":
                    approveInitLiability(request, response);
                    break;
                case "/approveLiability":
                    approveLiability(request, response);
                    break;
                case "/pendingLiability":
                    pendingLiability(request, response);
                    break;
                case "/referClaimPanel":
                    referClaimPanel(request, response);
                    break;
                case "/storeFile":
                    storeFile(request, response);
                    break;
                case "/restoreFile":
                    restoreFile(request, response);
                    break;
                case "/viewDocumentStatusChange":
                    viewDocumentStatusChange(request, response);
                    break;
                case "/approveRejectionByPanel":
                    approveRejectionByPanel(request, response);
                    break;
                case "/rejectRejectionByPanel":
                    rejectRejectionByPanel(request, response);
                    break;
                case "/requestForInvestigationByPanel":
                    requestForInvestigationByPanel(request, response);
                    break;
                case "/generateRejectionLetter":
                    generateRejectionLetter(request, response);
                    break;
                case "/forwardToPanel":
                    forwardToPanel(request, response);
                    break;
                case "/getDecisionMakingUserList":
                    getDecisionMakingUserList(request, response);
                    break;
                case "/requestForInvestigationByDecisionMaker":
                    requestForInvestigationByDecisionMaker(request, response);
                    break;
                case "/requestForInvestigationByClaimHandler":
                    requestForInvestigationByClaimHandler(request, response);
                    break;
                case "/arrangeInvestigationByDecisionMaker":
                    arrangeInvestigationByDecisionMaker(request, response);
                    break;
                case "/viewInvestigation":
                    viewInvestigation(request, response);
                    break;
                case "/viewDriverDetails":
                    viewDriverDetails(request, response);
                    break;
                case "/updateArrangeInvestigation":
                    updateArrangeInvestigation(request, response, claimUserTypeDto);
                    break;
                case "/updateCompleteInvestigation":
                    updateCompleteInvestigation(request, response, claimUserTypeDto);
                    break;
                case "/updateCancelInvestigation":
                    updateCancelInvestigation(request, response, claimUserTypeDto);
                    break;
                case "/updateInvestigationPayment":
                    updateInvestigationPayment(request, response, claimUserTypeDto);
                    break;
                case "/claimHandlerPanelList":
                    claimHandlerPanelList(request, response);
                    break;
                case "/claimPanelList":
                    claimPanelList(request, response);
                    break;
                case "/calculateInvestigationPayment":
                    calculateInvestigationPayment(request, response);
                    break;
                case "/viewInvestigationIndividual":
                    viewInvestigationIndividual(request, response);
                    break;
                case "/viewInvestigationPayment":
                    viewInvestigationPayment(request, response);
                    break;
                case "/loadClaimStampPage":
                    loadClaimStampPage(request, response);
                    break;
                case "/viewSupplyOrder":
                    viewSupplyOrder(request, response);
                    break;
                case "/supplyOrderData":
                    supplyOrderData(request, response);
                    break;
                case "/saveSupplyOrder":
                    saveSupplyOrder(request, response);
                    break;
                case "/viewClaimHistory":
                    viewClaimHistory(request, response);
                    break;
                case "/getUserList":
                    getUserList(request, response);
                    break;
                case "/updateSpecialCommentUsers":
                    updateSpecialCommentUsers(request, response);
                    break;
                case "/requestForSupplierOrder":
                    requestForSupplierOrder(request, response);
                    break;
                case "/recallSupplyOrder":
                    recallSupplyOrder(request, response);
                    break;
                case "/forwardScrTeamSupplyOrder":
                    forwardToScrTeamSupplyOrder(request, response);
                    break;
                case "/returnSpCoordSupplyOrder":
                    returnToSpCoordSupplyOrder(request, response);
                    break;
                case "/returnSpCoordSupplyOrderByClaimHandler":
                    returnSpCoordSupplyOrderByClaimHandler(request, response);
                    break;
                case "/returnScrTeamSupplyOrderByClaimHandler":
                    returnScrTeamSupplyOrderByClaimHandler(request, response);
                    break;
                case "/approvedAndForwardClaimHandlerSupplyOrder":
                    approvedAndForwardToClaimHandlerSupplyOrder(request, response);
                    break;
                case "/forwardGenerateSupplyOrder":
                    forwardGenerateSupplyOrder(request, response);
                    break;
                case "/validateSupplyOrderCalculationSheetApproval":
                    isValidateSupplyOrderCalculationSheetApproved(request, response);
                    break;
                case "/checkedAllInitialLiabilityApproveDocument":
                    isCheckedAllInitialLiabilityApproveDocument(request, response);
                    break;
                case "/checkedAllLiabilityApproveDocument":
                    isCheckedAllLiabilityApproveDocument(request, response);
                    break;
                case "/scrutinizingUserList":
                    getUserListByAccessUserType(request, response, String.valueOf(AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM));
                    break;
                case "/approveLiabilityAndStoreFile":
                    approveLiabilityAndStoreFile(request, response);
                    break;
                case "/updateSupplyOrderGenerate":
                    updateSupplyOrderGenerate(request, response);
                    break;
                case "/claimSupplyuOrderList":
                    claimSupplyuOrderList(request, response);
                    break;
                case "/claimScrutinizingList":
                    claimScrutinizingList(request, response);
                    break;
                case "/loadClaimHandlerBtnPanel":
                    loadClaimHandlerBtnPanel(request, response);
                    break;
                case "/approveInitialLiabilityAndStoreFile":
                    approveInitialLiabilityAndStoreFile(request, response);
                    break;
                case "/isCheckedLiablity":
                    isCheckedLiablity(request, response);
                    break;
                case "/requestedAri":
                    requestedAri(request, response);
                    break;
                case "/closeClaim":
                    closeClaim(request, response);
                    break;
                case "/reOpenClaim":
                    reOpenClaim(request, response);
                    break;
                case "/forwardToClaimHandler":
                    forwardToClaimHandler(request, response);
                    break;
                case "/specialCommentApprovelList":
                    specialCommentApprovelList(request, response);
                    break;
                case "/policyExcessChange":
                    policyExcessChange(request, response);
                    break;
                case "/calVatAmount":
                    calculationVatAmount(request, response);
                    break;
                case "/claimBulkCloseClaimList":
                    claimBulkCloseClaimList(request, response);
                    break;
                case "/closeClaimList":
                    closeClaimList(request, response);
                    break;
                case "/claimCloseBulk":
                    claimCloseBulk(request, response);
                    break;
                case "/requestForRecallDO":
                    recallDO(request, response);
                    break;
                case "/returnToClaimHandler":
                    returnToClaimHandler(request, response);
                    break;
                case "/coverNoteApprove":
                    coverNoteApprove(request, response);
                    break;
                case "/investigationSelectedImages":
                    investigationSelectedImages(request, response);
                    break;
                case "/viewAllImages":
                    viewAllImages(request, response);
                    break;
                case "/viewInvestigationImageList":
                    viewInvestigationImageList(request, response);
                    break;
                case "/updateSupplyOrder":
                    updateSupplyOrder(request, response);
                    break;
                case "/viewClaimSummary":
                    viewClaimSummary(request, response);
                    break;
                case "/returnToDecisionMaker":
                    returnToDecisionMaker(request, response);
                    break;
                case "/viewPreviousClaims":
                    viewPreviousClaims(request, response);
                    break;
                case "/viewEngineeringDocumentStatusChange":
                    viewEngineeringDocumentStatusChange(request, response);
                    break;
                case "/forwardSPCSupplyOrder":
                    forwardToSparePartsCoordinatorForApproval(request, response);
                    break;
                case "/returnScrTeamSupplyOrderBySPC":
                    returnScrTeamSupplyOrderBySPC(request, response);
                    break;
                case "/approveAndForwardToScrutinizingTeam":
                    approveAndForwardToScrutinizingTeam(request, response);
                    break;
                case "/forwardToScrutinizingTeam":
                    forwardToScrutinizingTeam(request, response);
                    break;
                case "/recallFromSparePartsCoordinator":
                    recallFromSparePartsCoordinator(request, response);
                    break;
                case "/recallFromScrutinizingTeam":
                    recallFromScrutinizingTeam(request, response);
                    break;
                case "/returnToSparePartsCoordinatorAfterUpdate":
                    returnToSparePartsCoordinatorAfterUpdate(request, response);
                    break;
                case "/approvedAndForwardClaimHandlerSupplyOrderAfterUpdate":
                    approvedAndForwardClaimHandlerSupplyOrderAfterUpdate(request, response);
                    break;
                case "/loadAdvanceAmountPage":
                    loadAdvanceAmountPage(request, response);
                    break;
                case "/requestForAdvance":
                    requestForAdvance(request, response);
                    break;
                case "/forwardForAdvance":
                    forwardForAdvance(request, response);
                    break;
                case "/advanceReturnToClaimHandler":
                    returnToClaimHandlerAdvance(request, response);
                    break;
                case "/approveAdvance":
                    approveAdvance(request, response);
                    break;
                case "/recallAdvanceRequest":
                    recallAdvance(request, response);
                    break;
                case "/advanceForwardList":
                    forwardAdvanceList(request, response);
                    break;
                case "/advanceReturn":
                    advanceReturn(request, response);
                    break;
                case "/returnAdvanceByClaimHandler":
                    returnAdvanceByClaimHandler(request, response);
                    break;
                case "/claimFileForwardedDetails":
                    claimFileForwardedDetails(request, response);
                    break;
                case "/returnClaimByEngineer":
                    returnClaimByEngineer(request, response);
                    break;
                case "/claimFilesForEngineer":
                    claimFilesForEngineer(request, response);
                    break;
                case "/mainPanelList":
                    mainPanelList(request, response);
                    break;
                case "/viewPanelDecision":
                    viewPanelDecision(request, response);
                    break;
                case "/getPanelUsers":
                    getPanelUsersByClaimNo(request, response);
                    break;
                case "/updatePanelMembers":
                    updatePanelMembers(request, response);
                    break;
                case "/viewRejectionDocumentViewer":
                    viewRejectionDocumentViewer(request, response);
                    break;
                case "/letterPanelList":
                    viewLetterPanel(request, response);
                    break;
                case "/lPanel":
                    viewLetterPanelList(request, response);
                    break;
                case "/loadReasons":
                    loadReasons(request, response);
                    break;
                case "/attachRejectionFileOnOtherSelect":
                    attachRejectionFileOnOtherSelect(request, response);
                    break;
                case "/viewEngDocUpload":
                    viewEngDocUpload(request, response);
                    break;
                case "/isSpecailCommentApprovel":
                    specailCommentApprovelChecked(request, response);
                    break;
                case "/isAllMainPanelApproved":
                    isAllMainPanelApproved(request, response);
                    break;
                case "/getTrailerDetail":
                    getTrailerDetail(request, response);
                    break;
                case "/getTradePlateDetail":
                    getTradePlateDetail(request, response);
                    break;
                case "/getClaimStatus":
                    getClaimStatus(request, response);
                    break;
                case "/checkDeliverOrderDocumentStatus":
                    isPendingBillForDeliveryOrderApprovedDocument(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void getTradePlateDetail(HttpServletRequest request, HttpServletResponse response) {
        String policyNo = null == request.getParameter("PolicyNo") || request.getParameter("PolicyNo").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("PolicyNo").trim();
        String policyChannelType = null == request.getParameter("PolicyChannelType") || request.getParameter("PolicyChannelType").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("PolicyChannelType");
        Gson gson = new Gson();
        String json;
        try {
            List<TradePlateDetailDto> tradePlateDetail = callCenterService.getTradePlateDetail(policyNo, policyChannelType);
            if (null != tradePlateDetail) {
                json = gson.toJson(tradePlateDetail);
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void getTrailerDetail(HttpServletRequest request, HttpServletResponse response) {
        String policyNo = null == request.getParameter("PolicyNo") || request.getParameter("PolicyNo").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("PolicyNo").trim();
        String policyChannelType = null == request.getParameter("PolicyChannelType") || request.getParameter("PolicyChannelType").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("PolicyChannelType");
        Gson gson = new Gson();
        String json;
        try {
            TrailerDetailDto trailerDetail = callCenterService.getTrailerDetail(policyNo, policyChannelType);
            if (null != trailerDetail) {
                json = gson.toJson(trailerDetail);
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updateDefineDocumentOnOther(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        Gson gson = new Gson();
        UserDto user = getSessionUser(request);
        String json;
        try {
            claimWiseDocumentService.updateDefineDocumentOnOther(claimNo, 80, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void attachRejectionFileOnOtherSelect(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        Gson gson = new Gson();
        String json;
        try {
            claimHandlerService.attachRejectionFileOnOtherSelect(claimNo);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void loadReasons(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        try {
            List<ClaimRepudiatedLetterTypeDto> list = claimHandlerService.getActiveRejectionReasons(AppConstant.ACTIVE_STATUS);
            json = gson.toJson(list);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void viewLetterPanel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            request.setAttribute("dMakerList", claimHandlerService.getUSersByAccessUsrType(AppConstant.ACCESS_LEVEL_DECISION_MAKER));
            request.setAttribute("reasonList", claimHandlerService.getActiveRejectionReasons(AppConstant.ACTIVE_STATUS));
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/lPanel.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updatePanelMembers(HttpServletRequest request, HttpServletResponse response) {
        String userList = null == request.getParameter("userList") ? AppConstant.STRING_EMPTY : request.getParameter("userList");
        String removedUserList = null == request.getParameter("removedUserList") ? AppConstant.STRING_EMPTY : request.getParameter("removedUserList");
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {
            if (claimNo > 0) {
                claimHandlerService.updatePanelUsers(claimNo, userList, removedUserList, user);
                json = gson.toJson("SUCCESS");
            } else {
                json = gson.toJson("FAIL");
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void getPanelUsersByClaimNo(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        try {
            List<PanelMemberListDto> panelMembers = claimHandlerService.getPanelMembers(claimNo);
            json = gson.toJson(panelMembers);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        }
    }

    private void viewPanelDecision(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        try {
            ClaimPanelDto claimPanelDto = claimHandlerService.searchPanelDecision(claimNo);
            request.setAttribute(AppConstant.PANEL_DECISION, claimPanelDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/panelDecisionView.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void viewRejectionDocumentViewer(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter(AppConstant.CLAIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.CLAIM_NO));
        Integer documentTypeId = request.getParameter(AppConstant.DOCUMENT_TYPE_ID) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.DOCUMENT_TYPE_ID));
        try {
            boolean documentAvailable = claimDocumentService.isDocumentAvailable(claimNo);
            request.setAttribute("isDocAvailable", documentAvailable);
            request.setAttribute(AppConstant.CLAIM_NO, claimNo);
            request.setAttribute(AppConstant.DOCUMENT_TYPE_ID, documentTypeId);
            int rejectionRefNo = claimHandlerService.getRejectionRefNo(claimNo, AppConstant.REJECTION_LETTER_TYPE_NO);
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimHandlerService.getClaimDocumentDto(rejectionRefNo));
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/rejectionLetterViewer.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void mainPanelList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        String userID = request.getParameter(AppConstant.TXT_USER_ID) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_USER_ID);
        String forwardDate = request.getParameter(AppConstant.TXT_REQUEST_DATE_TIME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REQUEST_DATE_TIME);
        String claimNo = request.getParameter(AppConstant.TXT_CLAIM_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_NUMBER);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            switch (orderColumnName) {
                case "claimNo":
                    orderColumnName = "N_CLAIM_NO";
                    break;
                case "forwardedUser":
                    orderColumnName = "V_INPUT_USER";
                    break;
                case "forwardedDateTime":
                    orderColumnName = "MIN(D_INPUT_DATETIME)";
                    break;
                case "lastUpdatedDateTime":
                    orderColumnName = "MAX(D_UPDATED_DATE_TIME)";
                    break;
                case "assignedPanelMembers":
                    orderColumnName = "COUNT(N_CLAIM_NO)";
                    break;
                default:
                    orderColumnName = "N_ID";
                    break;
            }
            if (!userID.equals(AppConstant.STRING_EMPTY)) {
                this.addFieldParameter("V_INPUT_USER", userID, FieldParameterDto.SearchType.Like, parameterList);
            }

            if (!forwardDate.equals(AppConstant.STRING_EMPTY)) {
                this.addFieldParameter("MIN(D_INPUT_DATETIME)", forwardDate, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!claimNo.equals(AppConstant.STRING_EMPTY)) {
                this.addFieldParameter("N_CLAIM_NO", claimNo, FieldParameterDto.SearchType.Equal, parameterList);
            }
            DataGridDto data = claimHandlerService.getMainPanelUsersGrid(parameterList, draw++, start, length, columnOrder, orderColumnName);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void claimFilesForEngineer(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String policyChannelType = request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        HttpSession session = request.getSession();
        UserDto user = getSessionUser(request);
        String userId = user.getUserId();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_POLICY_CHANNEL_TYPE", policyChannelType, FieldParameterDto.SearchType.Equal, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }


            if (!finalizedStatus.isEmpty()) {
                this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }


            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "assignUser":
                    orderColumnName = "t2.V_ASSIGN_USER_ID";
                    break;
                case "assignDateTime":
                    orderColumnName = "t2.D_ASSIGN_DATE_TIME";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                case "accidentDate":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "policyChannelType":
                    orderColumnName = "t1.V_POLICY_CHANNEL_TYPE";
                    break;


            }

            if (status.equals(String.valueOf(ClaimStatus.TASK_FORWARDED_TO_ENGINEER.getClaimStatus()))) {
                switch (user.getAccessUserType()) {
                    case 22:
                    case 23:
                    case 24:
                        this.addFieldParameter("t2.V_FORWARDED_ENGINEER", userId, FieldParameterDto.SearchType.Equal, parameterList);
                }

                if (type == 55) {
                    this.addFieldParameter("t4.access_user_type", String.valueOf(AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR), FieldParameterDto.SearchType.Equal, parameterList);
                } else if (type == 65) {
                    this.addFieldParameter("t4.access_user_type", String.valueOf(AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM), FieldParameterDto.SearchType.Equal, parameterList);
                }
            }

            request.getSession().setAttribute(AppConstant.SEARCH_FROM_DATE, fromDate);
            request.getSession().setAttribute(AppConstant.SEARCH_TO_DATE, toDate);
            request.getSession().setAttribute(AppConstant.SEARCH_REF_NUMBER, coverNoteNo);
            request.getSession().setAttribute(AppConstant.SEARCH_VEHICLE_NUMBER, vehicleNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_REF_NUMBER, coverNoteNo);
            request.getSession().setAttribute(AppConstant.SEARCH_CLAIM_NUMBER, claimNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_FINALIZED_STATUS, finalizedStatus);
            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_NO, policyNo);
            request.getSession().setAttribute(AppConstant.SEARCH_LOCATION, location);
            request.getSession().setAttribute(AppConstant.SEARCH_CLAIM_STATUS, status);
            request.getSession().setAttribute(AppConstant.SEARCH_LIABILITY_STATUS, liabilityStatus);
            request.getSession().setAttribute(AppConstant.SEARCH_FILE_STATUS, fileStatus);
            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_CHANNEL_TYPE, policyChannelType);

            DataGridDto data = claimHandlerService.getClaimsForEngineerDataGrid(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, status.equals(String.valueOf(ClaimStatus.TASK_FORWARDED_TO_ENGINEER.getClaimStatus())));
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void returnClaimByEngineer(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        String assignUser = null == request.getParameter("V_USERID") ? AppConstant.STRING_EMPTY : request.getParameter("V_USERID");
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        UserDto user = getSessionUser(request);
        boolean isReturned = false;
        String json;
        Gson gson = new Gson();
        try {
            isReturned = claimHandlerService.returnClaimByEngineer(claimNo, assignUser, user, remark);
            if (isReturned) {
                json = gson.toJson("SUCCESS");
            } else {
                json = gson.toJson("FAIL");
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void claimFileForwardedDetails(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        String json;
        Gson gson = new Gson();
        try {
            RtePendingClaimsDto rtePendingClaimsDto = claimHandlerService.checkRtePendingDetails(claimNo);
            json = gson.toJson(rtePendingClaimsDto);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnAdvanceByClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        UserDto sessionUser = getSessionUser(request);
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            claimHandlerService.requestForAdvance(claimNo, remark, null, sessionUser);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void advanceReturn(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        UserDto sessionUser = getSessionUser(request);
        Gson gson = new Gson();
        String json;
        try {
            claimHandlerService.returnAdvance(claimNo, remark, sessionUser);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void forwardAdvanceList(HttpServletRequest request, HttpServletResponse response) {
        Integer TYPE = null == request.getParameter("TYPE") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("TYPE"));
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String policyChannelType = request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);

        String userId = getSessionUser(request).getUserId();
        int accessUserType = getSessionUser(request).getAccessUserType();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_POLICY_CHANNEL_TYPE", policyChannelType, FieldParameterDto.SearchType.Equal, parameterList);

            if (!"".equals(finalizedStatus)) {
                this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }

            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                case "supplierOrderAssignDateTime":
                    orderColumnName = "t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME";
                    break;
                case "policyChannelType":
                    orderColumnName = "t1.V_POLICY_CHANNEL_TYPE";
                    break;
            }

            DataGridDto data = claimHandlerService.getAdvanceForwardedList(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, userId);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void recallAdvance(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        try {
            claimHandlerService.recallAdvance(claimNo, remark, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void approveAdvance(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        BigDecimal advance = null == request.getParameter("advance") || request.getParameter("advance").isEmpty() ? BigDecimal.ZERO : new BigDecimal(request.getParameter("advance"));
        UserDto sessionUser = getSessionUser(request);
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            claimHandlerService.ApproveAdvance(claimNo, advance, remark, sessionUser);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToClaimHandlerAdvance(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        UserDto sessionUser = getSessionUser(request);
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            claimHandlerService.returnToClaimHandlerAdvance(claimNo, remark, sessionUser);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void forwardForAdvance(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer type = null == request.getParameter("TYPE") ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("TYPE"));
        String assignUsername = null == request.getParameter("assignUser") ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");
        Integer accessUserType = Integer.valueOf(null == request.getParameter("accessUserCode") ? AppConstant.STRING_EMPTY : request.getParameter("accessUserCode"));
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        BigDecimal advanceAmount = null == request.getParameter("AMOUNT") || request.getParameter("AMOUNT").isEmpty() ? BigDecimal.ZERO : new BigDecimal(request.getParameter("AMOUNT"));
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        try {
            UserDto assignUser = new UserDto();
            assignUser.setUserId(assignUsername);
            assignUser.setAccessUserType(accessUserType);
            claimHandlerService.forwardForAdvance(claimNo, type, remark, assignUser, user, advanceAmount);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void requestForAdvance(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String assignUser = null == request.getParameter("assignUser") ? AppConstant.STRING_EMPTY : request.getParameter("assignUser");
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        Integer accessUserType = null == request.getParameter("accessUserCode") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("accessUserCode"));
        UserDto sessionUser = getSessionUser(request);
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            UserDto user = new UserDto();
            user.setUserId(assignUser);
            user.setAccessUserType(accessUserType);
            claimHandlerService.requestForAdvance(claimNo, remark, user, sessionUser);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void loadAdvanceAmountPage(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        UserDto user = getSessionUser(request);
        try {
            MotorEngineerDetailsDto motorEngineerDetailsDto = new MotorEngineerDetailsDto();
            motorEngineerService.setAdvanceAmountDetails(motorEngineerDetailsDto, claimNo);
            List<SpecialRemarkDto> remarkList = claimHandlerService.searchAllRemarksByClaimNo(claimNo);
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            BigDecimal totalPaidAdvanceForClaim = calculationSheetService.getTotalPaidAdvanceForClaim(claimNo);

            request.setAttribute(AppConstant.MOTOR_ENGINEER_DETAILS, motorEngineerDetailsDto);

            PopupItemDto forwardAction = new PopupItemDto();

            if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR) {
                forwardAction.setLabel("Forward to Bill Checking User");
                forwardAction.setValue("2");
            } else if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM) {
                forwardAction.setLabel("Forward to Spare Parts Coordinator");
                forwardAction.setValue("1");
            }
            request.setAttribute(AppConstant.ACTION, forwardAction);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);
            request.setAttribute("advanceAmount", totalPaidAdvanceForClaim);

            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimAdvanceAmountView.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void specailCommentApprovelChecked(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        Gson gson = new Gson();
        String json;

        try {
            boolean isExist = claimHandlerService.checkClaimStatusByClaimNO(claimNo);
            if (isExist) {
                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            } else {
                json = gson.toJson("FAIL");
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }

    }

    private void viewEngDocUpload(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            String prevInspection = request.getParameter(AppConstant.PREVIOUS_INSPECTION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.PREVIOUS_INSPECTION);
            if (AppConstant.YES.equals(prevInspection)) {
                request.setAttribute(AppConstant.PREVIOUS_INSPECTION, prevInspection);
            }
            claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId, "'ENGINEERING_DOC'");
            request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/claimHandlerEngDocUploadView.jsp");
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewEngineeringDocumentStatusChange(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimHandlerService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/motorEngineerDocumentStatusChange.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void approvedAndForwardClaimHandlerSupplyOrderAfterUpdate(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        ClaimHandlerDto claimHandlerDto = (ClaimHandlerDto) request.getSession().getAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO);
        try {
            supplyOrderService.approvedAndForwardClaimHandlerSupplyOrderAfterUpdate(claimNo, refNo, user, claimHandlerDto.getAssignUserId());
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToSparePartsCoordinatorAfterUpdate(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.returnSpcoodSupplyOrderByScrutinizing(claimNo, refNo, user, false);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void recallFromScrutinizingTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.returnSpcoodSupplyOrderByScrutinizing(claimNo, refNo, user, true);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void recallFromSparePartsCoordinator(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.returnScrTeamSupplyOrderBySPC(claimNo, refNo, user, true);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void forwardToScrutinizingTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.forwardToScrutinizingTeam(claimNo, refNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void approveAndForwardToScrutinizingTeam(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.approveAndForwardToScrutinizingTeam(claimNo, refNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnScrTeamSupplyOrderBySPC(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.returnScrTeamSupplyOrderBySPC(claimNo, refNo, user, false);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void forwardToSparePartsCoordinatorForApproval(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Integer refNo = null == request.getParameter("supplyOrderRefNo") || request.getParameter("supplyOrderRefNo").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        try {
            supplyOrderService.forwardToSparePartsCoordinatorForApproval(claimNo, refNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToDecisionMaker(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String remark = null == request.getParameter("panleRemark") ? AppConstant.STRING_EMPTY : request.getParameter("panleRemark");
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            claimHandlerService.returnToDecisionMaker(claimNo, remark, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void viewPreviousClaims(HttpServletRequest request, HttpServletResponse response) {
        String vehicleNo = null == request.getParameter("VEHICLE_NO") ? AppConstant.STRING_EMPTY : request.getParameter("VEHICLE_NO");
        Integer claimNo = null == request.getParameter("P_N_CLIM_NO") || request.getParameter("P_N_CLIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        List<ClaimsDto> claimsDtos = null;
        try {
            if (!vehicleNo.isEmpty()) {
                claimsDtos = claimHandlerService.viewPreviousClaim(vehicleNo, claimNo);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.setAttribute(AppConstant.PREVIOUS_CLAIM_LIST, claimsDtos);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/previousClaimsView.jsp");
        }
    }

    private void viewClaimSummary(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            String polStatus = null == request.getParameter("POL_STATUS") ? AppConstant.STRING_EMPTY : request.getParameter("POL_STATUS");
            ClaimSummaryDto claimSummaryDto = motorEngineerService.getClaimSummaryDetails(claimNo);
            claimSummaryDto.setPolicyStatus(polStatus);
            request.setAttribute("claimSummaryDto", claimSummaryDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimSummary.jsp");

        }
    }

    private void updateSupplyOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        BigDecimal finalTotal = BigDecimal.ZERO;
        BigDecimal totalAmount;
        BigDecimal deductTotal;
        BigDecimal oaAmount;
        BigDecimal totalOaAmount = BigDecimal.ZERO;
        Boolean isPendingIndividualPrice;
        String message = AppConstant.STRING_EMPTY;
        String json;
        UserDto user = getSessionUser(request);
        String submitUser = user.getUserId();//user.getV_firstname().concat(" ").concat(user.getV_lastname());
        Gson gson = new Gson();
        try {
            Integer sparePartCount = Integer.parseInt(request.getParameter("sparePartCount") == null ? "0" : request.getParameter("sparePartCount"));

            SupplyOrderSummaryDto supplyOrderSummaryDto = new SupplyOrderSummaryDto();

            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.getConvertUtils().register(false, false, 0);
            beanUtilsBean.populate(supplyOrderSummaryDto, request.getParameterMap());
            supplyOrderSummaryDto.setInputUserId(submitUser);
            supplyOrderSummaryDto.setInputDateTime(Utility.sysDateTime());
            List<SupplyOrderDetailsDto> supplyOrderDetailsDtoList = new ArrayList<>();
            for (int i = 0; i <= sparePartCount; i++) {
                SupplyOrderDetailsDto supplyOrderDetailsDto = new SupplyOrderDetailsDto();
                if (request.getParameter(getParameterName(i, "sparePartRefNo")) != null) {
                    supplyOrderDetailsDto.setIndex(Integer.parseInt(request.getParameter(getParameterName(i, "indexValue"))));
                    supplyOrderDetailsDto.setSparePartRefNo(Integer.parseInt(request.getParameter(getParameterName(i, "sparePartRefNo"))));
                    supplyOrderDetailsDto.setQuantity(Integer.parseInt(request.getParameter(getParameterName(i, "quantity")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "quantity"))));

                    supplyOrderDetailsDto.setIndividualPrice(new BigDecimal(request.getParameter(getParameterName(i, "individualPrice")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "individualPrice"))));

                    String isPendingPrice = request.getParameter(getParameterName(i, "isPendingIndividualPrice"));
                    isPendingIndividualPrice = "true".equalsIgnoreCase(isPendingPrice) || "on".equalsIgnoreCase(isPendingPrice) || "1".equals(isPendingPrice);
                    supplyOrderDetailsDto.setIsPendingIndividualPrice(isPendingIndividualPrice);

                    supplyOrderDetailsDto.setOaRate(new BigDecimal(request.getParameter(getParameterName(i, "oaRate")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "oaRate"))));


                    supplyOrderDetailsDto.setTotalAmount(new BigDecimal(request.getParameter(getParameterName(i, "totalAmount")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "totalAmount"))));

                    totalAmount = supplyOrderDetailsDto.getIndividualPrice().multiply(new BigDecimal(supplyOrderDetailsDto.getQuantity())).setScale(2, RoundingMode.HALF_UP);
                    if (supplyOrderDetailsDto.getOaRate().doubleValue() > 0) {
                        oaAmount = (totalAmount.multiply(supplyOrderDetailsDto.getOaRate()).setScale(2, RoundingMode.HALF_UP)).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                        totalOaAmount = totalOaAmount.add(oaAmount).setScale(2, RoundingMode.HALF_UP);
                        totalAmount = totalAmount.subtract(oaAmount);
                    }

                    supplyOrderDetailsDto.setTotalAmount(totalAmount);

                    supplyOrderDetailsDtoList.add(supplyOrderDetailsDto);
                    finalTotal = finalTotal.add(totalAmount).setScale(2, RoundingMode.HALF_UP);
                }
            }
            supplyOrderSummaryDto.setTotalOwnersAccountAmount(totalOaAmount);
            supplyOrderSummaryDto.setTotalAmount(finalTotal);
            //  deductTotal = (finalTotal.multiply(supplyOrderSummaryDto.getOwnersAccountDeductionRate())).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);

            if (supplyOrderSummaryDto.getVatStatus().equalsIgnoreCase(AppConstant.ADD)) {
                finalTotal = finalTotal.add(finalTotal.multiply(supplyOrderSummaryDto.getVatAmount()).divide(new BigDecimal(100)));
            } else {
                finalTotal = finalTotal.subtract(finalTotal.multiply(supplyOrderSummaryDto.getVatAmount()).divide(new BigDecimal(100)));
            }

            if (AppConstant.YES.equals(supplyOrderSummaryDto.getIsExcessInclude())) {
                finalTotal = finalTotal.subtract(supplyOrderSummaryDto.getPolicyExcess()).subtract(supplyOrderSummaryDto.getOthertDeductionAmount());
            } else {
                finalTotal = finalTotal.subtract(supplyOrderSummaryDto.getOthertDeductionAmount());
            }

            supplyOrderSummaryDto.setFinalAmount(finalTotal.setScale(2, RoundingMode.HALF_UP));
            supplyOrderSummaryDto.setSupplyOrderDetailsDtoList(supplyOrderDetailsDtoList);

            supplyOrderService.updateSupplyOrder(supplyOrderSummaryDto, user);
            message = "SUCCESS";
            json = gson.toJson(message);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson(message);
            printWriter(request, response, json);
        }
    }

    private void viewInvestigationImageList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer claimNo = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            InvestigationDetailsFormDto investigationDetailsFormDto = investigationDetailsService.getSelectedImages(claimNo);
            request.setAttribute("investigationSelectedImagesDetail", investigationDetailsFormDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimInvestigationImagesView.jsp");
    }

    private void viewAllImages(HttpServletRequest request, HttpServletResponse response) {
        List<ClaimImageFormDto> claimImageFormList = new ArrayList<>();
        int selectedImagesCount = 0;
        try {
            Integer claimNo = Integer.valueOf(request.getParameter("CLAIM_NO"));
            claimImageFormList = inspectionDetailsService.getClaimImageFormDtoList(claimNo);
            selectedImagesCount = investigationDetailsService.getSelectedImagesCount(claimNo);
            removeSessionClaimDetails(request, response);
            request.setAttribute("claimImageFormList", claimImageFormList);
            request.setAttribute("CLAIM_NO", claimNo);
            request.setAttribute("selectedImagesCount", selectedImagesCount);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/viewAllImages.jsp");

    }

    private void investigationSelectedImages(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {
            String selectedeImagesRefNo = request.getParameter("selectedImages");
            Integer claimNo = Integer.valueOf(request.getParameter("claimNo"));
            claimHandlerService.saveInvestigationSelectedImages(selectedeImagesRefNo, claimNo, user);
            json = gson.toJson("SUCCESS");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
        }
        printWriter(request, response, json);
    }

    private void coverNoteApprove(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {
            Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
            claimHandlerService.coverNoteApprove(claimNo, user);
            json = gson.toJson("SUCCESS");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
        }
        printWriter(request, response, json);
    }

    private void claimCloseBulk(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        Integer noOfDays = null == request.getParameter("days") || request.getParameter("days").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("days"));
        try {
            String ids = request.getParameter("selectedIds");
            List<Integer> selectedList = claimHandlerService.getSelectedList(ids);

            for (Integer claimNo : selectedList) {
                claimHandlerService.updateClaimClose(claimNo, user);
                claimHandlerService.updateBulkClose(claimNo, noOfDays, user);
            }

            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void closeClaimList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String noOfDays = request.getParameter(AppConstant.NO_OF_DAYS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.NO_OF_DAYS);
        String calsheetStatus = request.getParameter(AppConstant.TXT_CALSHEET_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CALSHEET_STATUS);
        String lossType = request.getParameter(AppConstant.TXT_LOSS_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOSS_TYPE);
        HttpSession session = request.getSession();
        boolean liablityUser = (boolean) session.getAttribute(AppConstant.IS_INIT_LIABILITY_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER);
        boolean claimUser = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        boolean decisionMakerUser = (boolean) session.getAttribute(AppConstant.IS_DECISION_MAKER);
        String userId = getSessionUser(request).getUserId();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }


            if ("".equals(finalizedStatus)) {
                this.addFieldParameter("t2.V_CLOSE_STATUS", "'CLOSE','REJECT','SETTLE'", FieldParameterDto.SearchType.NOT_IN, parameterList);
            } else {
                this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            this.addFieldParameter("t2.N_CLAIM_STATUS", "41,44", FieldParameterDto.SearchType.NOT_IN, parameterList);
            // 10 View All Claims
            if (10 != type) {
                if (liablityUser) {
                    this.addFieldParameter("t2.V_INIT_LIABILITY_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                }

                if (2 == type && decisionMakerUser) {
                    this.addFieldParameter("t2.V_DECISION_MAKING_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                } else if (claimUser) {
                    this.addFieldParameter("t2.V_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                }
            }

            switch (lossType) {
                case "1":
                case "2":
                    this.addFieldParameter("t2.N_LOSS_TYPE", lossType, FieldParameterDto.SearchType.Equal, parameterList);
            }

            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                default:
                    orderColumnName = "t1.N_CLIM_NO";
                    break;


            }
            if (!noOfDays.equals(AppConstant.STRING_EMPTY)) {
                toDate = Utility.getBeforeNoOfDays(Integer.parseInt(noOfDays));
                fromDate = AppConstant.DEFAULT_DATE;
            }
            DataGridDto data = claimHandlerService.getClaimHandlerDataGridDtoByCalsheetStatus(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, calsheetStatus);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void claimBulkCloseClaimList(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(47,35,36,37,38,39,40" + ",41,42,43,44,45,46,47,48,49,17,50,52,53,54,55,56,57,68,5,84)");

        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
        removeSessionType(request, response);
        updateSessionType(request, response, type);
        removeSessionClaimDetails(request, response);
        request.setAttribute("statusList", popupItemDtoList);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimBulkCloseClaimList.jsp");

    }

    private void returnToClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        UserDto user = getSessionUser(request);
        ClaimHandlerDto claimHandlerDto = (ClaimHandlerDto) request.getSession().getAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO);
        Gson gson = new Gson();
        try {
            supplyOrderService.returnToClaimHandler(claimNo, supplyOrderRefNo, user, false);
            json = gson.toJson("SUCCESS");
            if (null != claimHandlerDto) claimHandlerDto.setSupplyOrderAssignUser(AppConstant.EMPTY_STRING);
            printWriter(request, response, json);
        } catch (ErrorMsgException error) {
            LOGGER.error(error.getErrorMessage());
            json = gson.toJson(error.getErrorMessage());
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void recallDO(HttpServletRequest request, HttpServletResponse response) {

        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.recallDO(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void calculationVatAmount(HttpServletRequest request, HttpServletResponse response) {
        BigDecimal finalAmount;
        try {
            String vatRadio = getCalculableAmount(request.getParameter("vatRadio"));
            BigDecimal vatAmount = new BigDecimal(getCalculableAmount(request.getParameter("vatAmount")));
            BigDecimal totalAmount = new BigDecimal(getCalculableAmount(request.getParameter("totalAmount")));
            BigDecimal deduction = new BigDecimal(getCalculableAmount(request.getParameter("deduction")));


            if (vatRadio.equalsIgnoreCase(AppConstant.ADD)) {
                finalAmount = totalAmount.add((totalAmount.multiply(vatAmount)).divide(new BigDecimal(100)));
//                finalAmount = new BigDecimal((new BigDecimal(totalAmount).add(new BigDecimal(new BigDecimal(totalAmount).multiply(new BigDecimal(vatAmount)).toString()).divide(new BigDecimal(100)))).toString());
                finalAmount = finalAmount.subtract(deduction);
            } else {
//                finalAmount = new BigDecimal((new BigDecimal(totalAmount).subtract(new BigDecimal(new BigDecimal(totalAmount).multiply(new BigDecimal(vatAmount)).toString()).divide(new BigDecimal(100)))).toString());
                finalAmount = totalAmount.subtract((totalAmount.multiply(vatAmount)).divide(new BigDecimal(100)));
                finalAmount = finalAmount.subtract(deduction);
            }
            BigDecimal scaled = finalAmount.setScale(2, RoundingMode.HALF_UP);
            response.getWriter().println(scaled);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    private String getCalculableAmount(String parameter) {
        if (null != parameter && !parameter.isEmpty()) {
            return parameter.replaceAll(",", "");
        } else {
            return "0";
        }
    }

    private void specialCommentApprovelList(HttpServletRequest request, HttpServletResponse response) {
        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(47,35,36,37,38,39,40" + ",41,42,43,44,45,46,47,48,49,17,50,52,53,54,55,56,57,68,5,84)");

        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
        removeSessionType(request, response);
        updateSessionType(request, response, type);
        removeSessionClaimDetails(request, response);
        request.setAttribute("statusList", popupItemDtoList);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/specialCommentApprovel.jsp");
    }

    private void forwardGenerateSupplyOrder(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            supplyOrderService.forwardToSparePartsCoordinatorForGeneratingLetter(claimNo, supplyOrderRefNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void approvedAndForwardToClaimHandlerSupplyOrder(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        boolean isUpdated = null != request.getParameter("IS_UPDATED") && !request.getParameter("IS_UPDATED").isEmpty() && Boolean.parseBoolean(request.getParameter("IS_UPDATED"));
        String json;
        UserDto user = getSessionUser(request);
        ClaimHandlerDto claimHandlerDto = (ClaimHandlerDto) request.getSession().getAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO);
        Gson gson = new Gson();
        try {
            supplyOrderService.approvedAndForwardToClaimHandler(claimNo, supplyOrderRefNo, user, claimHandlerDto.getAssignUserId(), isUpdated);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void returnToSpCoordSupplyOrder(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            supplyOrderService.returnToSparePartsCoordinator(claimNo, supplyOrderRefNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void returnSpCoordSupplyOrderByClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            supplyOrderService.returnToSparePartsCoordinatorByClaimHandler(claimNo, supplyOrderRefNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void returnScrTeamSupplyOrderByClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            supplyOrderService.returnToScrTeamSupplyOrderByClaimHandler(claimNo, supplyOrderRefNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void forwardToScrTeamSupplyOrder(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        String approveAssignUser = request.getParameter("scrutinizingUserId");
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            supplyOrderService.forwardToScrTeamSupplyOrder(claimNo, supplyOrderRefNo, approveAssignUser, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void recallSupplyOrder(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            supplyOrderService.recall(claimNo, supplyOrderRefNo, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void viewClaimHistory(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            ClaimsDto claimsDto = callCenterService.getViewAccidentClaimsDto(claimId);
            HttpSession session = request.getSession();
            request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
            request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_HISTORY);
            session.removeAttribute(AppConstant.CLAIM_DTO_HISTORY);
            session.setAttribute(AppConstant.CLAIM_DTO_HISTORY, claimsDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
    }

    private String getParameterName(Integer index, String fieldName) {
        StringBuilder sb = new StringBuilder();
        try {
            sb.append("supplyOrderSummaryDto").append(".").append("supplyOrderDetailsDtoList");
            sb.append("[").append(index).append("]").append(".").append(fieldName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return sb.toString();
    }

    private void viewEdit(HttpServletRequest request, HttpServletResponse response) {
        Integer tabIndex = 0;
        try {
            Integer type = null == request.getParameter("TYPE") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("TYPE"));
            setTypeForViewInvestigation(request);
            tabIndex = Integer.parseInt(request.getParameter(AppConstant.P_TAB_INDEX) == null ? AppConstant.ZERO : request.getParameter(AppConstant.P_TAB_INDEX));
            HttpSession session = request.getSession();
            Integer claimNoToSearch = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            Integer refNo = null == request.getParameter("N_REF_NO") || request.getParameter("N_REF_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_REF_NO"));
            boolean isPreviousClaim = null != request.getParameter("IS_PREVIOUS_CLAIM") && Boolean.parseBoolean(request.getParameter("IS_PREVIOUS_CLAIM"));
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNoToSearch);
            ClaimsDto claimsDto = claimHandlerDto.getClaimsDto();
            UserDto user = getSessionUser(request);
            if (null != claimsDto) {
                claimHandlerService.setUnderWritingDetails(claimsDto);
            }
            List<ReminderPrintSummaryDto> reminderPrintSummaryDtos = reminderPrintService.getReminderSummeryListByClaimNo(claimHandlerDto.getClaimNo());
            if (null != reminderPrintSummaryDtos && !reminderPrintSummaryDtos.isEmpty()) {
                request.setAttribute("IS_REMINDER_LETTER_PRINT", "Y");
            } else {
                request.setAttribute("IS_REMINDER_LETTER_PRINT", "N");
            }
            RequestAriDto requestAri = requestAriService.searchByClaimNo(claimNoToSearch);

            if (null != requestAri) {
                request.setAttribute(AppConstant.IS_REQUESTED, AppConstant.YES);
            } else {
                request.setAttribute(AppConstant.IS_REQUESTED, AppConstant.NO);
            }

            boolean isSupplyOrderPending = supplyOrderService.isPendingSupplyOrder(claimNoToSearch);
            boolean isForwardedToMainPanel = claimHandlerService.isForwardedToMainPanel(claimNoToSearch);
            boolean isTheft = claimsDto.getCauseOfLoss().equals(AppConstant.THEFT);

            List<LeasingCompanyDto> leasingCompanyDetails = claimHandlerService.getLeasingCompanyDetails();
            List<ClaimLogsDto> logList = claimHandlerService.getLogList(claimHandlerDto.getClaimNo());
            List<SpecialRemarkDto> specialRemarkList = claimHandlerService.searchAllRemarksByClaimNo(claimHandlerDto.getClaimNo());
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionList(claimHandlerDto.getClaimNo());
            request.setAttribute(AppConstant.SESSION_TYPE, type);
            request.setAttribute(AppConstant.LEASING_COMPANIES, leasingCompanyDetails);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.LOG_TARILS, logList);
            request.setAttribute(AppConstant.CLAIM_HANDLER_SPECIAL_REMARK, specialRemarkList);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);
            request.setAttribute(AppConstant.TAB_INDEX, tabIndex);
            request.setAttribute("DO_REF_NO", refNo);
            request.setAttribute(AppConstant.IS_PANEL_DECISION, isForwardedToMainPanel);
            request.setAttribute(AppConstant.IS_THEFT, isTheft);
            session.setAttribute(AppConstant.IS_PREVIOUS_CLAIM, isPreviousClaim);
            session.setAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO, claimHandlerDto);
            session.setAttribute(AppConstant.IS_PENDING_SUPPLY_ORDER, isSupplyOrderPending);
            session.setAttribute("CLAIM_HANDLER_MESSAGE_TYPE", AppConstant.EMPTY_STRING);
            session.setAttribute("G_USER", user);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimHandlerView.jsp");
    }

    private void setTypeForViewInvestigation(HttpServletRequest request) {
        //        Integer tabIndex = Integer.parseInt(request.getParameter(AppConstant.P_TAB_INDEX));
        boolean isHaveTableIndex = null != request.getParameter(AppConstant.P_TAB_INDEX);
        int type = null == request.getSession().getAttribute("TYPE") ? AppConstant.ZERO_INT : (int) request.getSession().getAttribute("TYPE");

        if (isHaveTableIndex && type == 10) {
            request.getSession().setAttribute("TYPE", 1);
        }
    }

    private void updateCancelInvestigation(HttpServletRequest request, HttpServletResponse response, ClaimUserTypeDto claimUserTypeDto) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String message = AppConstant.STRING_EMPTY;
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
            BeanUtils.populate(investigationDetailsDto, request.getParameterMap());
            investigationDetailsDto.setInvestArrangeUser(user.getUserId());
            investigationDetailsDto.setInvestArrangeDateTime(Utility.sysDateTime());
            investigationDetailsDto.setInvestigationStatus("CAN");
            investigationDetailsService.cancelInvestigationDetails(investigationDetailsDto, claimUserTypeDto, user);
            message = "Saved Successfully";
            json = gson.toJson(message);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(message);
            printWriter(request, response, json);
        }
    }

    private void viewSupplyOrder(HttpServletRequest request, HttpServletResponse response) {
        SupplyOrderSummaryDto supplyOrderSummaryDto = null;
        List<SupplyOrderSummaryDto> supplyOrderSummaryDtos = null;
        List<SupplyOrderSummaryDto> supplyOrderSummaryDtos1 = null;
        List<VatRateDto> vatRateDtos = null;
        Gson gson = new Gson();
        ClaimHandlerDto claimHandlerDto = null;
        boolean pendingInspection = false;
        boolean isDocUpload;
        boolean isVoucherGenerated = false;
        boolean isNotification;
        boolean isCreationRequest = false;
        Integer assignedDo = AppConstant.ZERO_INT;
        HttpSession session = request.getSession();
        Integer supplyOrderRefNo = AppConstant.ZERO_INT;
        Integer claimNo = request.getParameter("P_N_CLIM_NO") == null || request.getParameter("P_N_CLIM_NO").isEmpty() ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
        isDocUpload = null != request.getParameter("DOC_UPLOAD") && !request.getParameter("DOC_UPLOAD").isEmpty() && Boolean.parseBoolean(request.getParameter("DOC_UPLOAD"));
        isNotification = null != request.getParameter("NOTIFY") && !request.getParameter("NOTIFY").isEmpty() && Boolean.parseBoolean(request.getParameter("NOTIFY"));
        try {
            isCreationRequest = null != request.getParameter("N_REF_NO") && !request.getParameter("N_REF_NO").isEmpty() && request.getParameter("N_REF_NO").equalsIgnoreCase(AppConstant.ZERO);
            supplyOrderRefNo = null == request.getParameter("N_REF_NO") || request.getParameter("N_REF_NO").isEmpty() ? supplyOrderService.getMaxSupplyRefNo(claimNo) : Integer.parseInt(request.getParameter("N_REF_NO"));
            this.setSupplyOrderPopupListValues(request, claimNo);
            supplyOrderSummaryDto = supplyOrderService.getSupplyOrderSummaryDto(supplyOrderRefNo, claimNo);
            supplyOrderSummaryDtos = supplyOrderService.searchClaimSupplyOrderSummary(claimNo, supplyOrderRefNo);
            supplyOrderSummaryDtos1 = supplyOrderService.searchClaimSupplyOrderSummaryPending(claimNo, supplyOrderRefNo);
            claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            supplyOrderSummaryDto.setIsExcessInclude(claimHandlerDto.getIsExcessInclude());
            vatRateDtos = supplyOrderService.getVateRateList();
            pendingInspection = motorEngineerService.checkPendingInspection(claimNo);
            isVoucherGenerated = calculationSheetService.hasVoucherGeneratedCalSheetForDo(supplyOrderRefNo);
            Integer doAssigned = supplyOrderService.isDoAssigned(claimNo);
            assignedDo = null == doAssigned ? AppConstant.ZERO_INT : doAssigned;
            UserDto userDetailByUserId = motorEngineerService.getUserDetailByUserId(supplyOrderSummaryDto.getInputUserId());
            BigDecimal authLimit = BigDecimal.valueOf(userDetailByUserId.getPaymentAuthLimit());
            BigDecimal totalAmount = supplyOrderSummaryDto.getTotalAmount();
            if (authLimit.compareTo(totalAmount) < 0) {
                supplyOrderSummaryDto.setLimitExceeded(Boolean.TRUE);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute(AppConstant.PENDING_INSPECTION, pendingInspection);
            request.setAttribute(AppConstant.IS_HAVING_VOUCHER_GENERATED_CALSHEET, isVoucherGenerated);
            request.setAttribute(AppConstant.SUPPLY_ORDER_SUMMARY_DTO, supplyOrderSummaryDto);
            request.setAttribute(AppConstant.SUPPLY_SUMMARY_LIST, supplyOrderSummaryDtos);
            request.setAttribute(AppConstant.SUPPLY_ORDER_LIST, supplyOrderSummaryDtos1);
            request.setAttribute(AppConstant.VAT_RATE_LIST, vatRateDtos);
            request.setAttribute(AppConstant.DOC_UPLOAD, isDocUpload);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);
            request.setAttribute(AppConstant.MAX_DO, supplyOrderService.getMaxSupplyRefNo(claimNo));
            request.setAttribute(AppConstant.IS_NOTIFY, isNotification);
            request.setAttribute(AppConstant.IS_DO_CREATION_REQUEST, isCreationRequest);
            request.setAttribute(AppConstant.IS_DO_ASSIGNED, assignedDo);
            if (isNotification || (!isCreationRequest && supplyOrderRefNo.equals(AppConstant.ZERO_INT))) {
                session.setAttribute(AppConstant.DO_REF_NO, supplyOrderRefNo);
            }
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimSupplyOrderView.jsp");
        }
    }

    private void saveSupplyOrder(HttpServletRequest request, HttpServletResponse response) {

        BigDecimal finalTotal = BigDecimal.ZERO;
        BigDecimal totalAmount;
        BigDecimal deductTotal;
        BigDecimal oaAmount;
        BigDecimal totalOaAmount = BigDecimal.ZERO;
        String message = AppConstant.STRING_EMPTY;
        Boolean isPendingIndividualPrice;
        String json;
        UserDto user = getSessionUser(request);
        String submitUser = user.getUserId();//user.getV_firstname().concat(" ").concat(user.getV_lastname());
        Gson gson = new Gson();
        try {
            Integer sparePartCount = Integer.parseInt(request.getParameter("sparePartCount") == null ? "0" : request.getParameter("sparePartCount"));

            SupplyOrderSummaryDto supplyOrderSummaryDto = new SupplyOrderSummaryDto();

            BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                @Override
                public Object convert(String value, Class clazz) {
                    if (clazz.isEnum()) {
                        return Enum.valueOf(clazz, value);
                    } else {
                        return super.convert(value, clazz);
                    }
                }
            });
            beanUtilsBean.getConvertUtils().register(false, false, 0);
            beanUtilsBean.populate(supplyOrderSummaryDto, request.getParameterMap());
            supplyOrderSummaryDto.setInputUserId(submitUser);
            supplyOrderSummaryDto.setInputDateTime(Utility.sysDateTime());
            List<SupplyOrderDetailsDto> supplyOrderDetailsDtoList = new ArrayList<>();
            for (int i = 0; i <= sparePartCount; i++) {
                SupplyOrderDetailsDto supplyOrderDetailsDto = new SupplyOrderDetailsDto();
                if (request.getParameter(getParameterName(i, "sparePartRefNo")) != null) {
                    supplyOrderDetailsDto.setIndex(Integer.parseInt(request.getParameter(getParameterName(i, "indexValue"))));
                    supplyOrderDetailsDto.setSparePartRefNo(Integer.parseInt(request.getParameter(getParameterName(i, "sparePartRefNo"))));
                    supplyOrderDetailsDto.setQuantity(Integer.parseInt(request.getParameter(getParameterName(i, "quantity")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "quantity"))));

                    supplyOrderDetailsDto.setIndividualPrice(new BigDecimal(request.getParameter(getParameterName(i, "individualPrice")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "individualPrice"))));

                    String isPendingPrice = request.getParameter(getParameterName(i, "isPendingIndividualPrice"));
                    isPendingIndividualPrice = "true".equalsIgnoreCase(isPendingPrice) || "on".equalsIgnoreCase(isPendingPrice) || "1".equals(isPendingPrice);
                    supplyOrderDetailsDto.setIsPendingIndividualPrice(isPendingIndividualPrice);

                    supplyOrderDetailsDto.setOaRate(new BigDecimal(request.getParameter(getParameterName(i, "oaRate")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "oaRate"))));


                    supplyOrderDetailsDto.setTotalAmount(new BigDecimal(request.getParameter(getParameterName(i, "totalAmount")) == null ? AppConstant.ZERO : request.getParameter(getParameterName(i, "totalAmount"))));

                    totalAmount = supplyOrderDetailsDto.getIndividualPrice().multiply(new BigDecimal(supplyOrderDetailsDto.getQuantity())).setScale(2, RoundingMode.HALF_UP);
                    if (supplyOrderDetailsDto.getOaRate().doubleValue() > 0) {
                        oaAmount = (totalAmount.multiply(supplyOrderDetailsDto.getOaRate()).setScale(2, RoundingMode.HALF_UP)).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                        totalOaAmount = totalOaAmount.add(oaAmount).setScale(2, RoundingMode.HALF_UP);
                        totalAmount = totalAmount.subtract(oaAmount);
                    }

                    supplyOrderDetailsDto.setTotalAmount(totalAmount);

                    supplyOrderDetailsDtoList.add(supplyOrderDetailsDto);
                    finalTotal = finalTotal.add(totalAmount).setScale(2, RoundingMode.HALF_UP);
                }
            }
            supplyOrderSummaryDto.setTotalOwnersAccountAmount(totalOaAmount);
            supplyOrderSummaryDto.setTotalAmount(finalTotal);
            //  deductTotal = (finalTotal.multiply(supplyOrderSummaryDto.getOwnersAccountDeductionRate())).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);

            if (supplyOrderSummaryDto.getVatStatus().equalsIgnoreCase(AppConstant.ADD)) {
                finalTotal = finalTotal.add(finalTotal.multiply(supplyOrderSummaryDto.getVatAmount()).divide(new BigDecimal(100)));
            } else {
                finalTotal = finalTotal.subtract(finalTotal.multiply(supplyOrderSummaryDto.getVatAmount()).divide(new BigDecimal(100)));
            }

            if (AppConstant.YES.equals(supplyOrderSummaryDto.getIsExcessInclude())) {
                finalTotal = finalTotal.subtract(supplyOrderSummaryDto.getPolicyExcess()).subtract(supplyOrderSummaryDto.getOthertDeductionAmount());
            } else {
                finalTotal = finalTotal.subtract(supplyOrderSummaryDto.getOthertDeductionAmount());
            }

            supplyOrderSummaryDto.setFinalAmount(finalTotal.setScale(2, RoundingMode.HALF_UP));
            supplyOrderSummaryDto.setSupplyOrderDetailsDtoList(supplyOrderDetailsDtoList);

            supplyOrderService.saveSupplyOrder(supplyOrderSummaryDto, user);
            message = "Saved Successfully";
            json = gson.toJson(message);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson(message);
            printWriter(request, response, json);
        }
    }

    private void supplyOrderData(HttpServletRequest request, HttpServletResponse response) {

        SupplyOrderSummaryDto supplyOrderSummaryDto = null;
        Gson gson = new Gson();
        boolean isLoad = !request.getParameter("isLoad").equals(AppConstant.ZERO);
        BigDecimal finalTotal = BigDecimal.ZERO;
        BigDecimal deductTotal = BigDecimal.ZERO;
        boolean isUpdate = false;
        try {
            isUpdate = null != request.getParameter("IS_UPDATE") && !request.getParameter("IS_UPDATE").isEmpty() && Boolean.parseBoolean(request.getParameter("IS_UPDATE"));
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo").equalsIgnoreCase(AppConstant.STRING_EMPTY) ? "0" : request.getParameter("supplyOrderRefNo"));
            this.setSupplyOrderPopupListValues(request, claimNo);
            UserDto user = getSessionUser(request);

            if (isLoad) {
                supplyOrderSummaryDto = supplyOrderService.getSupplyOrderSummaryDto(supplyOrderRefNo, claimNo);
            } else {
                supplyOrderSummaryDto = new SupplyOrderSummaryDto();
                String quantity;
                String individualPrice;
                String oaRate;
                BigDecimal totalAmount;
                BigDecimal oaAmount;
                BigDecimal totalOaAmount = BigDecimal.ZERO;
                Boolean isPendingIndividualPrice = Boolean.FALSE;

                Integer sparePartCount = Integer.parseInt(request.getParameter("sparePartCount") == null ? "0" : request.getParameter("sparePartCount"));

                BeanUtilsBean beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
                    @Override
                    public Object convert(String value, Class clazz) {
                        if (clazz.isEnum()) {
                            return Enum.valueOf(clazz, value);
                        } else {
                            return super.convert(value, clazz);
                        }
                    }
                });
                beanUtilsBean.getConvertUtils().register(false, false, 0);
                beanUtilsBean.populate(supplyOrderSummaryDto, request.getParameterMap());
                List<SupplyOrderDetailsDto> supplyOrderDetailsDtoList = new ArrayList<>();
                for (int i = 0; i <= sparePartCount; i++) {
                    SupplyOrderDetailsDto supplyOrderDetailsDto = new SupplyOrderDetailsDto();
                    if (request.getParameter(getParameterName(i, "sparePartRefNo")) != null) {
                        supplyOrderDetailsDto.setIndex(Integer.parseInt(request.getParameter(getParameterName(i, "indexValue"))));
                        supplyOrderDetailsDto.setSparePartRefNo(Integer.parseInt(request.getParameter(getParameterName(i, "sparePartRefNo"))));

                        quantity = request.getParameter(getParameterName(i, "quantity"));
                        individualPrice = request.getParameter(getParameterName(i, "individualPrice"));
                        oaRate = request.getParameter(getParameterName(i, "oaRate"));
                        String isPendingPrice = request.getParameter(getParameterName(i, "isPendingIndividualPrice"));
                        isPendingIndividualPrice = "true".equalsIgnoreCase(isPendingPrice) || "on".equalsIgnoreCase(isPendingPrice) || "1".equals(isPendingPrice);

                        supplyOrderDetailsDto.setQuantity(Integer.parseInt(quantity.isEmpty() ? AppConstant.ZERO : quantity));
                        supplyOrderDetailsDto.setIndividualPrice(new BigDecimal(individualPrice.isEmpty() ? AppConstant.ZERO : individualPrice));
                        supplyOrderDetailsDto.setOaRate(new BigDecimal(oaRate.isEmpty() ? AppConstant.ZERO : oaRate));
                        supplyOrderDetailsDto.setIsPendingIndividualPrice(isPendingIndividualPrice);

                        totalAmount = supplyOrderDetailsDto.getIndividualPrice().multiply(new BigDecimal(supplyOrderDetailsDto.getQuantity())).setScale(2, RoundingMode.HALF_UP);


                        if (supplyOrderDetailsDto.getOaRate().doubleValue() > 0) {
                            oaAmount = (totalAmount.multiply(supplyOrderDetailsDto.getOaRate()).setScale(2, RoundingMode.HALF_UP)).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                            totalOaAmount = totalOaAmount.add(oaAmount).setScale(2, RoundingMode.HALF_UP);
                            totalAmount = totalAmount.subtract(oaAmount);
                        }
                        supplyOrderDetailsDto.setTotalAmount(totalAmount);

                        supplyOrderDetailsDtoList.add(supplyOrderDetailsDto);
                        finalTotal = finalTotal.add(totalAmount).setScale(2, RoundingMode.HALF_UP);
                    }
                }
                supplyOrderSummaryDto.setTotalOwnersAccountAmount(totalOaAmount);
                supplyOrderSummaryDto.setTotalAmount(finalTotal);
                if (AppConstant.YES.equals(supplyOrderSummaryDto.getIsExcessInclude())) {
                    finalTotal = finalTotal.subtract(supplyOrderSummaryDto.getPolicyExcess()).subtract(supplyOrderSummaryDto.getOthertDeductionAmount());
                } else {
                    finalTotal = finalTotal.subtract(supplyOrderSummaryDto.getOthertDeductionAmount());
                }
                supplyOrderSummaryDto.setFinalAmount(finalTotal.setScale(2, RoundingMode.HALF_UP));
                supplyOrderSummaryDto.setSupplyOrderDetailsDtoList(supplyOrderDetailsDtoList);
            }

            BigDecimal authLimit = BigDecimal.valueOf(user.getPaymentAuthLimit());
            BigDecimal totalAmount = supplyOrderSummaryDto.getTotalAmount();
            if (authLimit.compareTo(totalAmount) < 0) {
                supplyOrderSummaryDto.setLimitExceeded(Boolean.TRUE);
            }

           /* if (Double.parseDouble(String.valueOf(finalTotal)) > userPaymentAuthLimit){
                supplyOrderSummaryDto.setLimitExceeded(Boolean.TRUE);
            }*/

//            UserDto userDetailByUserId = motorEngineerService.getUserDetailByUserId(supplyOrderSummaryDto.getInputUserId());
//            BigDecimal authLimit = BigDecimal.valueOf(userDetailByUserId.getPaymentAuthLimit());
//            BigDecimal totalAmount = supplyOrderSummaryDto.getTotalAmount();
//            if(authLimit.compareTo(totalAmount) < 0){
//                supplyOrderSummaryDto.setLimitExceeded(Boolean.TRUE);
//            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            String json = gson.toJson(supplyOrderSummaryDto);
            response.setContentType("application/json");
            try {
                response.getWriter().print(json);
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        }
    }

    private void updateCompleteInvestigation(HttpServletRequest request, HttpServletResponse response, ClaimUserTypeDto claimUserTypeDto) {

        String message = AppConstant.STRING_EMPTY;
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
            BeanUtils.populate(investigationDetailsDto, request.getParameterMap());
            investigationDetailsDto.setInvestCompletedUser(user.getUserId());
            investigationDetailsDto.setInvestCompletedDateTime(Utility.sysDateTime());
            investigationDetailsDto.setPaymentStatus("P");
            investigationDetailsService.completedInvestigationDetails(investigationDetailsDto, claimUserTypeDto, user);
            message = "Saved Successfully";
            json = gson.toJson(message);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(message);
            printWriter(request, response, json);
        }
    }

    private void viewInvestigation(HttpServletRequest request, HttpServletResponse response) {
        InvestigationDetailsFormDto investigationDetailsFormDto = null;
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            Integer maxInvestigationTxnId = 0;
            investigationDetailsFormDto = investigationDetailsService.getInvestigationDetailsFormDto(claimNo, maxInvestigationTxnId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.setAttribute(AppConstant.INVESTIGATION_DETAILS_FORM_DTO, investigationDetailsFormDto);
            setInvestigationPopupListValues(request);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimInvestigationView.jsp");

        }
    }

    private void viewDriverDetails(HttpServletRequest request, HttpServletResponse response) {
        DriverDetailDto driverDetailDto = null;
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            driverDetailDto = callCenterService.getDriverDetails(claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.setAttribute(AppConstant.DRIVER_DETAIL_DTO, driverDetailDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/driverDetailView.jsp");
        }
    }

    private void viewInvestigationIndividual(HttpServletRequest request, HttpServletResponse response) {
        InvestigationDetailsFormDto investigationDetailsFormDto = null;
        try {
            Integer investigationTxnId = request.getParameter("P_INVEST_TXN_ID") == null ? 0 : Integer.parseInt(request.getParameter("P_INVEST_TXN_ID"));
            investigationDetailsFormDto = investigationDetailsService.getInvestigationDetailsFormDto(0, investigationTxnId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.setAttribute(AppConstant.INVESTIGATION_DETAILS_FORM_DTO1, investigationDetailsFormDto);
            setInvestigationPopupListValues(request);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimInvestigationViewIndividual.jsp");

        }
    }

    private void viewInvestigationPayment(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        InvestigationDetailsFormDto investigationDetailsFormDto = null;
        try {
            Integer investigationTxnId = request.getParameter("P_INVEST_TXN_ID") == null ? 0 : Integer.parseInt(request.getParameter("P_INVEST_TXN_ID"));
            investigationDetailsFormDto = investigationDetailsService.getInvestigationDetailsFormDto(0, investigationTxnId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(investigationDetailsFormDto);
            printWriter(request, response, json);
        }
    }

    private void updateInvestigationPayment(HttpServletRequest request, HttpServletResponse response, ClaimUserTypeDto claimUserTypeDto) {

        String message = AppConstant.STRING_EMPTY;
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
            BeanUtils.populate(investigationDetailsDto, request.getParameterMap());
            investigationDetailsDto.setInvestCompletedUser(user.getUserId());
            investigationDetailsDto.setInvestCompletedDateTime(Utility.sysDateTime());
            investigationDetailsDto.setPaymentStatus("P");
            investigationDetailsService.updateInvestigationPayment(investigationDetailsDto, claimUserTypeDto, user);
            message = "Saved Successfully";
            json = gson.toJson(message);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(message);
            printWriter(request, response, json);
        }
    }

    private void viewDocumentStatusChange(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimHandlerService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimDocumentStatusChange.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void loadClaimStampPage(HttpServletRequest request, HttpServletResponse response) {
        boolean ariArranged = false;
        boolean salvageArranged = false;
        boolean ariRequested = false;
        boolean salvageRequested = false;
        try {
            Integer claimNoToSearch = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNoToSearch);

            List<ReminderPrintSummaryDto> reminderPrintSummaryDtos = reminderPrintService.getReminderSummeryListByClaimNo(claimHandlerDto.getClaimNo());

            if (null != reminderPrintSummaryDtos && !reminderPrintSummaryDtos.isEmpty()) {
                request.setAttribute("IS_REMINDER_LETTER_PRINT", "Y");
            } else {
                request.setAttribute("IS_REMINDER_LETTER_PRINT", "N");
            }

            int ariRequestedID = requestAriService.isAriOrSalvageRequested(claimNoToSearch);
            int salvageORAriArranged = requestAriService.isSalvageORAriArranged(claimNoToSearch);

            if (ariRequestedID == AppConstant.ARI_REQUEST || ariRequestedID == AppConstant.ARI || ariRequestedID == AppConstant.ARI_SALVAGE) {
                ariRequested = true;
            } else if (ariRequestedID == AppConstant.SALVAGE_REQUEST || ariRequestedID == AppConstant.COLLECT_SALVAGE) {
                salvageRequested = true;
            }

            if (salvageORAriArranged == AppConstant.ARI_INSPECTION) {
                ariArranged = true;
            } else if (salvageORAriArranged == AppConstant.SALVAGE_INSPECTION) {
                salvageArranged = true;
            }

            if (salvageArranged || ariArranged) {
                salvageRequested = false;
                ariRequested = false;
            }

            BulkCloseDetailDto bulkCloseDetailDto = claimHandlerService.getBulkCloseDetails(claimNoToSearch);
            List<LeasingCompanyDto> leasingCompanyDetails = claimHandlerService.getLeasingCompanyDetails();
            List<ClaimLogsDto> logList = claimHandlerService.getLogList(claimHandlerDto.getClaimNo());
            List<SpecialRemarkDto> list = claimHandlerService.searchDepartmentRemarksByClaimNo(claimHandlerDto.getClaimNo());
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionList(claimHandlerDto.getClaimNo());
            request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
            request.setAttribute(AppConstant.LEASING_COMPANIES, leasingCompanyDetails);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.LOG_TARILS, logList);
            request.setAttribute(AppConstant.CLAIM_HANDLER_SPECIAL_REMARK, list);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);
            request.setAttribute(AppConstant.ARI_REQUESTED, ariRequested);
            request.setAttribute(AppConstant.ARI_ARRANGED, ariArranged);
            request.setAttribute(AppConstant.SALVAGE_ARRANGED, salvageArranged);
            request.setAttribute(AppConstant.SALVAGE_REQUESTED, salvageRequested);
            request.setAttribute(AppConstant.BULK_CLOSE_DETAIL_DTO, bulkCloseDetailDto);
            request.getSession().setAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO, claimHandlerDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimStampPage.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void checkDocument(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        String successMessage = AppConstant.STRING_EMPTY;
        try {
            UserDto user = getSessionUser(request);
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            Integer claimNo = Integer.parseInt(request.getParameter("claimNo") == null ? AppConstant.ZERO : request.getParameter("claimNo"));
            Integer documentTypeId = Integer.parseInt(request.getParameter("documentTypeId") == null ? AppConstant.ZERO : request.getParameter("documentTypeId"));
            ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
            claimDocumentDto.setRefNo(refNo);
            claimDocumentDto.setDocumentTypeId(documentTypeId);
            claimDocumentDto.setClaimNo(claimNo);
            claimDocumentDto.setDocumentStatus(AppConstant.DOCUMENT_CHECK_STATUS);
            claimDocumentDto.setIsCheck(AppConstant.YES);
            claimDocumentDto.setCheckUser(user.getUserId());
            claimDocumentDto.setCheckDateTime(Utility.sysDateTime());
            claimHandlerService.checkedDocument(claimDocumentDto, user);
            successMessage = "Saved Successfully";
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        }
    }

    private void holdDocument(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        String successMessage = AppConstant.STRING_EMPTY;
        try {
            UserDto user = getSessionUser(request);
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            Integer claimNo = Integer.parseInt(request.getParameter("claimNo") == null ? AppConstant.ZERO : request.getParameter("claimNo"));
            Integer documentTypeId = Integer.parseInt(request.getParameter("documentTypeId") == null ? AppConstant.ZERO : request.getParameter("documentTypeId"));
            ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
            claimDocumentDto.setRefNo(refNo);
            claimDocumentDto.setDocumentTypeId(documentTypeId);
            claimDocumentDto.setClaimNo(claimNo);
            claimDocumentDto.setDocumentStatus(AppConstant.DOCUMENT_HOLD_STATUS);
            claimDocumentDto.setHoldUser(user.getUserId());
            claimDocumentDto.setHoldDateTime(Utility.sysDateTime());
            claimHandlerService.holdDocument(claimDocumentDto, user);
            successMessage = "Saved Successfully";
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        }
    }

    private void rejectDocument(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        String successMessage = AppConstant.STRING_EMPTY;
        try {
            UserDto user = getSessionUser(request);
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            Integer claimNo = Integer.parseInt(request.getParameter("claimNo") == null ? AppConstant.ZERO : request.getParameter("claimNo"));
            String email = request.getParameter("email") == null ? AppConstant.STRING_EMPTY : request.getParameter("email");
            Integer documentTypeId = Integer.parseInt(request.getParameter("documentTypeId") == null ? AppConstant.ZERO : request.getParameter("documentTypeId"));
            String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");
            ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
            claimDocumentDto.setRefNo(refNo);
            claimDocumentDto.setDocumentTypeId(documentTypeId);
            claimDocumentDto.setClaimNo(claimNo);
            claimDocumentDto.setDocumentStatus(AppConstant.DOCUMENT_REJECT_STATUS);
            claimDocumentDto.setRejectUser(user.getUserId());
            claimDocumentDto.setRejectDateTime(Utility.sysDateTime());
            claimDocumentDto.setRemark(remark);
            claimHandlerService.rejectDocument(claimDocumentDto, user, email, claimNo);
            successMessage = "Saved Successfully";
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        }
    }

    private void viewDocumentViewer(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimHandlerService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/pdfViewerAll.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewGeneratedReminderLetter(HttpServletRequest request, HttpServletResponse response) {
        ReminderPrintSummaryDto reminderPrintSummaryDto = null;
        try {
            Integer reminderSummaryRefId = request.getParameter("reminderSummaryRefId") == null ? 0 : Integer.parseInt(request.getParameter("reminderSummaryRefId"));
            reminderPrintSummaryDto = reminderPrintService.getReminderPrintSummaryDto(reminderSummaryRefId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.setAttribute(AppConstant.REMINDER_PRINT_SUMMARY_DTO, reminderPrintSummaryDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimReminderLetterView.jsp");
        }
    }

    private void generateReminderPrint(HttpServletRequest request, HttpServletResponse response) {
        String successMessage = AppConstant.STRING_EMPTY;
        String json;
        Gson gson = new Gson();


        try {

            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            Integer count = request.getParameter("count") == null ? 0 : Integer.parseInt(request.getParameter("count"));
            String email = request.getParameter("email")==null? AppConstant.STRING_EMPTY:  request.getParameter("email");
            UserDto user = getSessionUser(request);
            ReminderPrintSummaryDto reminderPrintSummaryDto = new ReminderPrintSummaryDto();
            List<ReminderPrintDetailsDto> reminderPrintDetailsList = reminderPrintSummaryDto.getReminderPrintDetailsList();
            for (int i = 0; i < count; i++) {
                ReminderPrintDetailsDto reminderPrintDetailsDto = new ReminderPrintDetailsDto();
                reminderPrintDetailsDto.setDocTypeId(Integer.parseInt(request.getParameter("docTypeId" + i) == null ? AppConstant.ZERO : request.getParameter("docTypeId" + i)));
                reminderPrintDetailsDto.setCheckReminderPrint(request.getParameter("checkReminderPrint" + i) == null ? AppConstant.NO : AppConstant.YES);
                if (reminderPrintDetailsDto.getCheckReminderPrint().equalsIgnoreCase(AppConstant.YES)) {
                    reminderPrintDetailsList.add(reminderPrintDetailsDto);
                }
            }
            reminderPrintSummaryDto.setClaimNo(claimNo);
            reminderPrintSummaryDto.setGeneratedUserId(user.getUserId());
            reminderPrintSummaryDto.setGeneratedDateTime(Utility.sysDateTime());
            if (reminderPrintDetailsList.size() > 0) {
                reminderPrintService.generateReminderPrint(reminderPrintSummaryDto, user , email, claimNo);
                claimHandlerService.addNotificationToBranchUsers(claimNo, user);
                successMessage = "Saved Successfully";
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        }
    }

    private void viewReminderPrint(HttpServletRequest request, HttpServletResponse response) {
        ReminderLetterFormDto reminderLetterFormDto = null;
        Map<Integer, Integer> uploadedDocumentsType = null;

        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            //  Integer reminderSummaryRefId = request.getParameter("reminderSummaryRefId") == null ? 0 : Integer.parseInt(request.getParameter("reminderSummaryRefId"));
            reminderLetterFormDto = reminderPrintService.getReminderLetterFormDto(claimNo);
            uploadedDocumentsType = claimHandlerService.getUploadedDocumentsType(claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.setAttribute(AppConstant.REMINDER_LETTER_FORM_DTO, reminderLetterFormDto);
            request.setAttribute("uploadedDocumentsType", uploadedDocumentsType);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimGenerateReminder.jsp");
        }
    }

    private void updateDefineDocument(HttpServletRequest request, HttpServletResponse response) {
        String successMessage;
        String json;
        Gson gson = new Gson();
        try {
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            Integer defineDocumentCount = request.getParameter("defineDocumentCount") == null ? 0 : Integer.parseInt(request.getParameter("defineDocumentCount"));
            String remark = request.getParameter("defineDocRemark") == null ? AppConstant.STRING_EMPTY : request.getParameter("defineDocRemark");

            UserDto user = getSessionUser(request);
            List<ClaimWiseDocumentDto> claimWiseDocumentDtoList = new ArrayList<>();
            for (int i = 0; i < defineDocumentCount; i++) {
                ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
                claimWiseDocumentDto.setClaimNo(claimNo);
                claimWiseDocumentDto.setInpUserId(user.getUserId());
                claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
                claimWiseDocumentDto.setIsMandatory(request.getParameter("isMandatory" + i));
                claimWiseDocumentDto.setDocReqFrom(Integer.parseInt(request.getParameter("docReqFrom" + i) == null ? AppConstant.ZERO : request.getParameter("docReqFrom" + i)));
                claimWiseDocumentDto.setRefNo(Integer.parseInt(request.getParameter("refNo" + i) == null ? AppConstant.ZERO : request.getParameter("refNo" + i)));
                claimWiseDocumentDtoList.add(claimWiseDocumentDto);
            }
            claimWiseDocumentService.updateDefineDocument(claimWiseDocumentDtoList, user, claimNo, remark);
            successMessage = "SUCCESS";
            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        } catch (ErrorMsgException e) {
            LOGGER.error(e.getMessage());
            successMessage = e.getErrorMessage();
            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void claimList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String policyChannelType = request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String priority = request.getParameter(AppConstant.TXT_PRIORITY) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_PRIORITY);
        HttpSession session = request.getSession();
        boolean liablityUser = (boolean) session.getAttribute(AppConstant.IS_INIT_LIABILITY_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER);
        boolean claimUser = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        boolean decisionMakerUser = (boolean) session.getAttribute(AppConstant.IS_DECISION_MAKER);
        String userId = getSessionUser(request).getUserId();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_POLICY_CHANNEL_TYPE", policyChannelType, FieldParameterDto.SearchType.Equal, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.STRING_EMPTY.equals(priority)) {
                this.addFieldParameter("t1.V_PRIORITY", priority.equals("1") ? AppConstant.PRIORITY_HIGH : AppConstant.PRIORITY_NORMAL, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }


            if ("".equals(finalizedStatus)) {
                if (liablityUser && type.equals(1)) {
                    this.addFieldParameter("t2.V_CLOSE_STATUS", AppConstant.CLOSE_STATUS_CLOSE, FieldParameterDto.SearchType.NOT_Equal, parameterList);
                }
            } else {
                this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            // 10 View All Claims
            if (10 != type && 80 != type) {
                if (liablityUser) {
                    this.addFieldParameter("t2.V_INIT_LIABILITY_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                }

                if (2 == type && decisionMakerUser) {
                    this.addFieldParameter("t2.V_DECISION_MAKING_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                } else if (claimUser) {
                    this.addFieldParameter("t2.V_ASSIGN_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);
                }
            }


            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignUser":
                    orderColumnName = "t2.V_ASSIGN_USER_ID";
                    break;
                case "assignDateTime":
                    orderColumnName = "t2.D_ASSIGN_DATE_TIME";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                case "accidentDate":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "policyChannelType":
                    orderColumnName = "t1.V_POLICY_CHANNEL_TYPE";
                    break;


            }

            request.getSession().setAttribute(AppConstant.SEARCH_FROM_DATE, fromDate);
            request.getSession().setAttribute(AppConstant.SEARCH_TO_DATE, toDate);
            request.getSession().setAttribute(AppConstant.SEARCH_REF_NUMBER, coverNoteNo);
            request.getSession().setAttribute(AppConstant.SEARCH_VEHICLE_NUMBER, vehicleNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_REF_NUMBER, coverNoteNo);
            request.getSession().setAttribute(AppConstant.SEARCH_CLAIM_NUMBER, claimNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_FINALIZED_STATUS, finalizedStatus);
            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_NO, policyNo);
            request.getSession().setAttribute(AppConstant.SEARCH_LOCATION, location);
            request.getSession().setAttribute(AppConstant.SEARCH_CLAIM_STATUS, status);
            request.getSession().setAttribute(AppConstant.SEARCH_LIABILITY_STATUS, liabilityStatus);
            request.getSession().setAttribute(AppConstant.SEARCH_FILE_STATUS, fileStatus);
            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_CHANNEL_TYPE, policyChannelType);

            DataGridDto data = claimHandlerService.getClaimHandlerDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, AppConstant.STRING_EMPTY.equals(priority) ? null : Integer.valueOf(priority));
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void viewLetterPanelList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null || request.getParameter(AppConstant.TXT_FROM_DATE).equals(AppConstant.STRING_EMPTY) ? Utility.sysDate() + " 00:00" : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null || request.getParameter(AppConstant.TXT_FROM_DATE).equals(AppConstant.STRING_EMPTY) ? Utility.sysDate() + " 23:59" : request.getParameter(AppConstant.TXT_TO_DATE);
        String rejectionPanelType = request.getParameter(AppConstant.TXT_REJECTION_PANEL_TYPE) == null ? AppConstant.EMPTY_STRING : request.getParameter(AppConstant.TXT_REJECTION_PANEL_TYPE);
        String rejection = request.getParameter(AppConstant.TXT_REJECTION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REJECTION);
        String lPanelStatus = request.getParameter(AppConstant.TXT_L_PANEL_STATUS) == null ? AppConstant.EMPTY_STRING : request.getParameter(AppConstant.TXT_L_PANEL_STATUS);
        String dMaker = null == request.getParameter(AppConstant.TXT_D_MAKER) ? AppConstant.EMPTY_STRING : request.getParameter(AppConstant.TXT_D_MAKER);

        try {

            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));

            if (!AppConstant.EMPTY_STRING.equals(rejectionPanelType)) {
                this.addFieldParameter("t2.N_DECISION_APPRV_CLAIM_PANEL", rejectionPanelType, FieldParameterDto.SearchType.IN, parameterList);
            }

            if (!AppConstant.EMPTY_STRING.equals(lPanelStatus)) {
                this.addFieldParameter("t2.V_IS_REJECTION_ATTACHED", lPanelStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.EMPTY_STRING.equals(rejection)) {
                String selectedrejection = AppConstant.EMPTY_STRING;
                switch (rejection) {
                    case "1":
                        selectedrejection = "'1'";
                        break;
                    case "2":
                        selectedrejection = "'2'";
                        break;
                    case "3":
                        selectedrejection = "'3'";
                        break;
                    case "4":
                        selectedrejection = "'4'";
                        break;
                    case "5":
                        selectedrejection = "'5'";
                        break;
                }
                this.addFieldParameter("t2.N_REPUDIATED_LETTER_TYPE", selectedrejection, FieldParameterDto.SearchType.IN, parameterList);
            }

            if (!AppConstant.STRING_EMPTY.equals(dMaker)) {
                this.addFieldParameter("t2.V_DECISION_MAKING_ASSIGN_USER_ID", dMaker, FieldParameterDto.SearchType.Equal, parameterList);
            }

            switch (orderColumnName) {
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "isfClaimNo":
                    orderColumnName = "t1.V_ISF_CLAIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "policyChannelType":
                    orderColumnName = "t1.V_POLICY_CHANNEL_TYPE";
                    break;
                case "rejectedReason":
                    orderColumnName = "t3.V_REPUDIATE_LETTER_TYPE_DESC";
                    break;
                case "accidentDate":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "dMakingAssignUid":
                    orderColumnName = "t2.V_DECISION_MAKING_ASSIGN_USER_ID";
                    break;
                case "assignDateTime":
                    orderColumnName = "t2.D_ASSIGN_DATE_TIME";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "isRejectionAttached":
                    orderColumnName = "t2.V_IS_REJECTION_ATTACHED";
                    break;

            }

            request.getSession().setAttribute(AppConstant.SEARCH_FROM_DATE, fromDate);
            request.getSession().setAttribute(AppConstant.SEARCH_TO_DATE, toDate);
            request.getSession().setAttribute(AppConstant.SEARCH_REJECTION_PANEL_TYPE, rejectionPanelType);
            request.getSession().setAttribute(AppConstant.SEARCH_REJECTION, rejection);
            request.getSession().setAttribute(AppConstant.SEARCH_D_MAKER, dMaker);
            request.getSession().setAttribute(AppConstant.SEARCH_L_PANEL_STATUS, lPanelStatus);

            DataGridDto data = claimHandlerService.getLetterPanelList(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
        }

//        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/lPanel.jsp");
    }

    private void claimSupplyuOrderList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String supplierStatus = request.getParameter(AppConstant.TXT_SUPPLIER_STAUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_SUPPLIER_STAUS);
        String policyChannelType = request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        HttpSession session = request.getSession();
        boolean liablityUser = (boolean) session.getAttribute(AppConstant.IS_INIT_LIABILITY_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER);
        boolean claimUser = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        boolean decisionMakerUser = (boolean) session.getAttribute(AppConstant.IS_DECISION_MAKER);
        String userId = getSessionUser(request).getUserId();
        int accessUserType = getSessionUser(request).getAccessUserType();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_POLICY_CHANNEL_TYPE", policyChannelType, FieldParameterDto.SearchType.Equal, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }

            if (accessUserType != 1) {
                this.addFieldParameter("t2.V_SUPPLY_ORDER_ASSIGN_USER", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if ("".equals(finalizedStatus)) {
                //  this.addFieldParameter("t2.V_CLOSE_STATUS", "'REOPEN','CLOSE'", FieldParameterDto.SearchType.NOT_IN, parameterList);
            } else {
                this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"0".equals(supplierStatus)) {
                String staus = AppConstant.EMPTY_STRING;
                switch (supplierStatus) {
                    case "1":
                        staus = "'p'";
                        break;
                    case "2":
                        staus = "'R'";
                        break;
                    case "3":
                        staus = "'SCRUTINIZING-F', 'SPC-A', 'SCRUTINIZING-R', 'A'";
                        break;
                    case "4":
                        staus = "'SCRUTINIZING-A'";
                        break;
                    case "5":
                        staus = "'CH-F'";
                        break;
                    case "6":
                        staus = "'CH-A'";
                        break;
                    case "7":
                        staus = "'RTE-F'";
                        break;
                    case "8":
                        staus = "'RTE-A'";
                        break;
                    case "9":
                        staus = "'G'";
                        break;
                    case "10":
                        staus = "'U'";
                        break;
                    case "11":
                        staus = "'SPC-F', 'SPC-R'";
                        break;
                }
                this.addFieldParameter("t4.v_supply_order_status", staus, FieldParameterDto.SearchType.IN, parameterList);
            }


            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                case "supplierOrderAssignDateTime":
                    orderColumnName = "t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME";
                    break;
                case "policyChannelType":
                    orderColumnName = "t1.V_POLICY_CHANNEL_TYPE";
                    break;


            }
            DataGridDto data = claimHandlerService.getClaimHandlerSupplierDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, type);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void claimScrutinizingList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        String finalizedStatus = request.getParameter(AppConstant.TXT_FINALIZED_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FINALIZED_STATUS);
        String supplierStatus = request.getParameter(AppConstant.TXT_SUPPLIER_STAUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_SUPPLIER_STAUS);
        String policyChannelType = request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        HttpSession session = request.getSession();
        boolean liablityUser = (boolean) session.getAttribute(AppConstant.IS_INIT_LIABILITY_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_INIT_LIABILITY_USER);
        boolean claimUser = (boolean) session.getAttribute(AppConstant.IS_CLAIM_HANDLER_USER) || (boolean) session.getAttribute(AppConstant.IS_OFFER_TEAM_CLAIM_HANDLER_USER);
        boolean decisionMakerUser = (boolean) session.getAttribute(AppConstant.IS_DECISION_MAKER);
        String userId = getSessionUser(request).getUserId();
        int accessUserType = getSessionUser(request).getAccessUserType();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_POLICY_CHANNEL_TYPE", policyChannelType, FieldParameterDto.SearchType.Equal, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }

            if (accessUserType != 1) {
                this.addFieldParameter("t4.v_apprv_assign_scrutinizing_user_id", userId, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if ("".equals(finalizedStatus)) {
                //  this.addFieldParameter("t2.V_CLOSE_STATUS", "'REOPEN','CLOSE'", FieldParameterDto.SearchType.NOT_IN, parameterList);
            } else {
                this.addFieldParameter("t2.V_CLOSE_STATUS", finalizedStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;
                case "policyChannelType":
                    orderColumnName = "t1.V_POLICY_CHANNEL_TYPE";
                    break;

            }

            if (!"0".equals(supplierStatus)) {
                String staus = AppConstant.EMPTY_STRING;
                switch (supplierStatus) {
                    case "1":
                        staus = "'p'";
                        break;
                    case "2":
                        staus = "'R'";
                        break;
                    case "3":
                        staus = "'SCRUTINIZING-F', 'SPC-A', 'SCRUTINIZING-R', 'A'";
                        break;
                    case "4":
                        staus = "'SCRUTINIZING-A'";
                        break;
                    case "5":
                        staus = "'CH-F'";
                        break;
                    case "6":
                        staus = "'CH-A'";
                        break;
                    case "7":
                        staus = "'RTE-F'";
                        break;
                    case "8":
                        staus = "'RTE-A'";
                        break;
                    case "9":
                        staus = "'G'";
                        break;
                    case "10":
                        staus = "'U'";
                        break;
                    case "11":
                        staus = "'SPC-F', 'SPC-R'";
                        break;
                }
                this.addFieldParameter("t4.v_supply_order_status", staus, FieldParameterDto.SearchType.IN, parameterList);
            }
            DataGridDto data = claimHandlerService.getClaimHandlerScrutinizingDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, type);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void calculateInvestigationPayment(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
        try {
            BeanUtils.populate(investigationDetailsDto, request.getParameterMap());
            investigationDetailsDto.setTotalFee(investigationDetailsDto.getProfFee().add(investigationDetailsDto.getTravelFee()).add(investigationDetailsDto.getOtherFee()));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            json = gson.toJson(investigationDetailsDto);
            printWriter(request, response, json);
        }
    }

    private void updateFinancialInfo(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer leasingRefNo = request.getParameter("leasingRefNo") == null ? 0 : Integer.parseInt(request.getParameter("leasingRefNo"));
        String financialInterest = request.getParameter("financialInterest") == null ? AppConstant.NO : request.getParameter("financialInterest");
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        String json;
        Gson gson = new Gson();
        UserDto user = getSessionUser(request);
        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setFinancialInterest(financialInterest);
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setLeasingRefNo(leasingRefNo);
            boolean updated = claimHandlerService.updateFinancialInfo(claimHandlerDto, user);
            if (updated) {
                successMessage = "Saved Successfully";
            }

            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void updateLiabilityInfo(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String ck1 = request.getParameter("isLcChk1");
        String ck2 = request.getParameter("isLcChk2");
        String ck3 = request.getParameter("isLcChk3");
        String ck4 = request.getParameter("isLcChk4");
        String ck5 = request.getParameter("isLcChk5");
        String ck6 = request.getParameter("isLcChk6");
        String ck7 = request.getParameter("isLcChk7");
        String ck8 = request.getParameter("isLcChk8");
        // Integer leasingRefNo = request.getParameter("leasingRefNo") == null ? 0 : Integer.parseInt(request.getParameter("leasingRefNo"));
        // String financialInterest = request.getParameter("financialInterest") == null ? AppConstant.NO : request.getParameter("financialInterest");
        String successMessage = AppConstant.STRING_EMPTY;
        String errorMessage = AppConstant.STRING_EMPTY;
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setIsLcChk1(ck1);
            claimHandlerDto.setIsLcChk2(ck2);
            claimHandlerDto.setIsLcChk3(ck3);
            claimHandlerDto.setIsLcChk4(ck4);
            claimHandlerDto.setIsLcChk5(ck5);
            claimHandlerDto.setIsLcChk6(ck6);
            claimHandlerDto.setIsLcChk7(ck7);
            claimHandlerDto.setIsLcChk8(ck8);

            claimHandlerDto.setInpDateTime(Utility.sysDateTime());
            claimHandlerDto.setInpUserId(user.getUserId());
            claimHandlerDto.setClaimNo(claimNo);

            boolean updated = claimHandlerService.updateLiabilityCheck(claimHandlerDto, user);
            if (updated) {
                successMessage = "Saved Successfully";
            }

            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void saveRemark(HttpServletRequest request, HttpServletResponse response) {

        try {
            String successMessage = AppConstant.STRING_EMPTY;
            String json;
            Gson gson = new Gson();
            Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            String remarkType = request.getParameter("remarkType") == null ? AppConstant.IR : request.getParameter("remarkType");
            String remark = request.getParameter("remark") == null ? AppConstant.STRING_EMPTY : request.getParameter("remark");
            UserDto user = getSessionUser(request);
            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimNo);
            specialRemarkDto.setRemark(remark);
            boolean addRemark = claimHandlerService.saveRemark(specialRemarkDto, user, remarkType);

            if (addRemark) {
                successMessage = "Saved Successfully";
            }
            json = gson.toJson(successMessage);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void viewDocumentUpload(HttpServletRequest request, HttpServletResponse response) {
        try {
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            if (historyRecord.equals(AppConstant.YES)) {
                claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId);
                request.setAttribute(AppConstant.HISTORY_CLAIM_UPLOADVIEW_DTO_LIST, claimUploadViewDtoList);
            } else {
                claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId);
                request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            }
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/documentUpload.jsp");
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewDefineDocument(HttpServletRequest request, HttpServletResponse response) {
        List<ClaimWiseDocumentDto> claimWiseDocumentDtoList = null;
        String documentReqFromList = AppConstant.STRING_EMPTY;
        String docName = AppConstant.STRING_EMPTY;
        try {
            documentReqFromList = recordCommonFunction.getPopupList("claim_doc_req_from", "N_DOC_REQ_FROM", "V_REQ_FROM_DESC", AppConstant.STRING_EMPTY, AppConstant.STRING_EMPTY);
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            docName = request.getParameter(AppConstant.DOC_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.DOC_NAME);
            if (docName.isEmpty()) {
                claimWiseDocumentDtoList = claimWiseDocumentService.getClaimWiseDocumentDtoList(claimId);
            } else {
                claimWiseDocumentDtoList = claimWiseDocumentService.getClaimWiseDocumentDtoList(claimId, docName);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            request.setAttribute("searchValue", docName);
            request.setAttribute(AppConstant.CLAIM_WISE_DOCUMENT_DTO_LIST, claimWiseDocumentDtoList);
            request.setAttribute(AppConstant.DOCUMENT_REQ_FROM_LIST, documentReqFromList);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimDefineDocument.jsp");
        }
    }

    private void storeFile(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.storeFile(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void restoreFile(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.restoreFile(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void referClaimPanel(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            //String decisionMakingUserId = request.getParameter("decisionMakingUserId");
            String remark = request.getParameter("panleRemark");
            Integer PANEL_ID = Integer.parseInt(request.getParameter("PANEL_ID"));
            claimHandlerService.referClaimPanel(claimNo, user, remark, PANEL_ID);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void updateSpecialCommentUsers(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        String json = gson.toJson("");
        try {
            String remark = request.getParameter("panleRemark");
            String specialUserId = request.getParameter("userId");
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNo);
            if (claimHandlerDto.getClaimStatus() != 57) {
                claimHandlerService.updateSpecialComment(claimNo, specialUserId, user, remark);
                json = gson.toJson("SUCCESS");
            } else {
                json = gson.toJson("CANNOT");
            }

            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void requestForSupplierOrder(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.requestForSupplierOrder(claimNo, user, remark);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void approveInitLiability(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && request.getParameter("IS_CANCELLED_POLICY").equals(AppConstant.CANCELLED_POLICY);
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.approveInitialLiability(claimNo, user, remark, outStandingPremium, isCancelledPolicy);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void forwardToPanel(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer repudiatedReason = null == request.getParameter("repudiatedReason") || request.getParameter("repudiatedReason").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("repudiatedReason"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String panelId = request.getParameter("PANEL_ID");
            String remark = request.getParameter("panleRemark");
            claimHandlerService.forwardToPanel(claimNo, user, panelId, remark, repudiatedReason);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("NOTFOUND");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void approveRejectionByPanel(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer repudiatedReason = null == request.getParameter("repudiatedReason") || request.getParameter("repudiatedReason").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("repudiatedReason"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String panelId = request.getParameter("PANEL_ID");
            String remark = request.getParameter("panleRemark");
            claimHandlerService.approveByPanel(claimNo, user, panelId, remark, repudiatedReason);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void rejectRejectionByPanel(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String panelId = request.getParameter("PANEL_ID");
            String remark = request.getParameter("panleRemark");
            claimHandlerService.rejectByPanel(claimNo, user, panelId, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void requestForInvestigationByPanel(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String panelId = request.getParameter("PANEL_ID");
            String remark = request.getParameter("panleRemark");
            claimHandlerService.requestForInvestigationByPanel(claimNo, user, panelId, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void generateRejectionLetter(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String rejectReasonId = request.getParameter("rejectReason");
            String remark = request.getParameter("panleRemark");
            Integer rejectionLatterType = Integer.valueOf(request.getParameter("rejectionLatterType"));
            claimHandlerService.generateRejectionLetter(claimNo, user, rejectReasonId, remark, rejectionLatterType);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void updateArrangeInvestigation(HttpServletRequest request, HttpServletResponse response, ClaimUserTypeDto claimUserTypeDto) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String message = AppConstant.STRING_EMPTY;
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
            Integer count = Integer.parseInt(request.getParameter("investigationReasonCount") == null ? "0" : request.getParameter("investigationReasonCount"));
            BeanUtils.populate(investigationDetailsDto, request.getParameterMap());
            investigationDetailsDto.setInvestArrangeUser(user.getUserId());
            investigationDetailsDto.setInvestArrangeDateTime(Utility.sysDateTime());
            investigationDetailsDto.setTotalFee(investigationDetailsDto.getOtherFee().add(investigationDetailsDto.getProfFee()).add(investigationDetailsDto.getTravelFee()));

            for (int i = 0; i < count; i++) {
                InvestigationReasonDetailsDto investigationReasonDetailsDto = new InvestigationReasonDetailsDto();
                investigationReasonDetailsDto.setInvesReasonRefNo(Integer.parseInt(request.getParameter("investigationDetailsDto.investigationReasonDetailsDtoList[" + i + "].invesReasonRefNo")));
                investigationReasonDetailsDto.setIsCheck(request.getParameter("investigationDetailsDto.investigationReasonDetailsDtoList[" + i + "].isCheck") == null ? "N" : "Y");
                investigationDetailsDto.getInvestigationReasonDetailsDtoList().add(investigationReasonDetailsDto);
            }
            investigationDetailsDto.setInvestReqUser(user.getUserId());
            investigationDetailsDto.setInvestReqDateTime(Utility.sysDateTime());
            if (investigationDetailsDto.getInvestigationStatus().equals(InvestigationStatusEnum.INVESTIGATION_APPROVED.getInvestigationStatus())) {
                investigationDetailsDto.setInvestReqAprvdUser(user.getUserId());
                investigationDetailsDto.setInvestReqAprvdDateTime(Utility.sysDateTime());
            }
            investigationDetailsService.saveInvestigationDetails(investigationDetailsDto, claimUserTypeDto, user);
            message = "Saved Successfully";
            json = gson.toJson(message);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(message);
            printWriter(request, response, json);
        }
    }

    private void getDecisionMakingUserList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Gson gson = new Gson();
            List<String> list = claimHandlerService.getDecisionMakingUserIdList();
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
        }
    }

    private void getUserList(HttpServletRequest request, HttpServletResponse response) {
        try {
            Gson gson = new Gson();
            List<String> list = claimHandlerService.getUserListByAccessUserType();
            String json = gson.toJson(list);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
        }
    }

    private void approveLiability(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer liabilityApproveType = Integer.parseInt(request.getParameter("LB_APPROVE_TYPE"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && request.getParameter("IS_CANCELLED_POLICY").equals(AppConstant.CANCELLED_POLICY);
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.approveLiability(claimNo, user, remark, liabilityApproveType, outStandingPremium, isCancelledPolicy);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void pendingLiability(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.pendingLiability(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void requestForInvestigationByDecisionMaker(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.requestForInvestigationByDecisionMaker(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void requestForInvestigationByClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.requestForInvestigationByClaimHandler(claimNo, user, remark);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void arrangeInvestigationByDecisionMaker(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.arrangeInvestigationByDecisionMaker(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void viewRepudiatedLetter(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer claimNoToSearch = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNoToSearch);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);

            switch (claimHandlerDto.getRejectionLatterType()) {
                case 1:
//                    claimHandlerList(request, response);
                    break;
                case 2:
//                    claimHandlerList(request, response);
                    break;
                case 3:
//                    claimHandlerList(request, response);
                    break;
                case 4:
//                    claimHandlerList(request, response);
                    break;
                case 5:
//                    claimHandlerList(request, response);
                    break;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimRepudiatedLetterView.jsp");
        }
    }

    private void claimHandlerList(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(47,35,36,37,38,39,40" + ",41,42,43,44,45,46,47,48,49,17,50,52,53,54,55,56,57,68,5,81,82,83,84)");

        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
        removeSessionType(request, response);
        updateSessionType(request, response, type);
        removeSessionClaimDetails(request, response);
        request.setAttribute("statusList", popupItemDtoList);
        request.getSession().setAttribute(AppConstant.SEARCH_POLICY_STATUS, AppConstant.CLAIM_STATUS_INITIAL_LIABILITY_PENDING);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimHandlerList.jsp");
    }

    private void claimHandlerPanelList(HttpServletRequest request, HttpServletResponse response) {

        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", type == 2 ? "n_ref_id IN(40,45,44,84)" : "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,84)");

        removeSessionType(request, response);
        updateSessionType(request, response, type);
        removeSessionClaimDetails(request, response);
        request.setAttribute("statusList", popupItemDtoList);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimHandlerPanelList.jsp");
    }

    private void claimPanelList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        Integer type = null == request.getParameter(AppConstant.TYPE) || request.getParameter(AppConstant.TYPE).isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter(AppConstant.TYPE));
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);
        HttpSession session = request.getSession();
        String userId = getSessionUser(request).getUserId();

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t4.V_USER_ID", userId, FieldParameterDto.SearchType.Equal, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }

            if (!AppConstant.ZERO.equals(status)) {
                this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            } else {
                this.addFieldParameter("t2.N_CLAIM_STATUS", "40,45,44,84", FieldParameterDto.SearchType.IN, parameterList);
            }

            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }

            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;

            }

            if (type == 2) {
                this.addFieldParameter("t5.N_PANEL_ID", String.valueOf(type), FieldParameterDto.SearchType.Equal, parameterList);
            }
            DataGridDto data = claimHandlerService.getClaimHandlerPanelDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, type);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void approveLiabilityAndStoreFile(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && request.getParameter("IS_CANCELLED_POLICY").equals(AppConstant.CANCELLED_POLICY);
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.approveLiabilityAndStoreFile(claimNo, user, remark, outStandingPremium, isCancelledPolicy);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void updateSupplyOrderGenerate(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            ClaimHandlerDto claimHandlerDto = (ClaimHandlerDto) request.getSession().getAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO);
            supplyOrderService.updateSupplyOrderGenerate(claimNo, supplyOrderRefNo, user.getUserId(), claimHandlerDto.getAssignUserId(), claimHandlerDto, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void loadClaimHandlerBtnPanel(HttpServletRequest request, HttpServletResponse response) {
        try {
            UserDto sessionUser = getSessionUser(request);
            Integer claimNoToSearch = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
            ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimNoToSearch);

            List<ReminderPrintSummaryDto> reminderPrintSummaryDtos = reminderPrintService.getReminderSummeryListByClaimNo(claimHandlerDto.getClaimNo());

            if (null != reminderPrintSummaryDtos && !reminderPrintSummaryDtos.isEmpty()) {
                request.setAttribute("IS_REMINDER_LETTER_PRINT", "Y");
            } else {
                request.setAttribute("IS_REMINDER_LETTER_PRINT", "N");
            }

            List<LeasingCompanyDto> leasingCompanyDetails = claimHandlerService.getLeasingCompanyDetails();
            List<ClaimLogsDto> logList = claimHandlerService.getLogList(claimHandlerDto.getClaimNo());
            List<SpecialRemarkDto> list = claimHandlerService.searchDepartmentRemarksByClaimNo(claimHandlerDto.getClaimNo());
            boolean isPendingApproval = false;
            if (claimHandlerDto.getClaimStatus().equals(AppConstant.FORWARD_TO_4_MEMBER_STATUS)) {
                isPendingApproval = claimHandlerService.isApprovelPending(claimNoToSearch, sessionUser);
            }
            List<PreviousClaimsDto> previousInspectionList = inspectionDetailsService.getPreviousInspectionList(claimHandlerDto.getClaimNo());
            request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
            request.setAttribute(AppConstant.LEASING_COMPANIES, leasingCompanyDetails);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION_LIST, previousInspectionList);
            request.setAttribute(AppConstant.LOG_TARILS, logList);
            request.setAttribute(AppConstant.CLAIM_HANDLER_SPECIAL_REMARK, list);
            request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);
            request.setAttribute(AppConstant.IS_APPROVAL_PENDING, isPendingApproval);
            request.getSession().setAttribute(AppConstant.SESSION_CLAIM_HANDLER_DTO, claimHandlerDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/claimHandlerBtnPanel.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void approveInitialLiabilityAndStoreFile(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        BigDecimal outStandingPremium = request.getParameter("OUTSTANDING_PREMIUM") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("OUTSTANDING_PREMIUM"));
        boolean isCancelledPolicy = null != request.getParameter("IS_CANCELLED_POLICY") && request.getParameter("IS_CANCELLED_POLICY").equals(AppConstant.CANCELLED_POLICY);
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.approveInitialLiabilityAndStoreFile(claimNo, user, remark, outStandingPremium, isCancelledPolicy);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void isCheckedLiablity(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        String code = AppConstant.NO;
        ClaimDocumentStatusDto claimDocumentStatusDto;
        try {
            claimDocumentStatusDto = claimDocumentService.getCheckedInitialLiabilityDocumentStatus(claimNo);
            if (claimDocumentStatusDto.getDocumentStatusEnum() == DocumentStatusEnum.CHECKED || claimDocumentStatusDto.getDocumentStatusEnum() == DocumentStatusEnum.DEFAULT || claimDocumentStatusDto.getDocumentStatusEnum() == DocumentStatusEnum.NO_VALID_DOCUMENT) {
                boolean liablityChecked = claimHandlerService.isLiablityChecked(claimNo);
                if (liablityChecked) {
                    code = AppConstant.YES;
                }
            } else {
                code = claimDocumentStatusDto.getDocumentStatusEnum().getDocumentStatus();
            }
            json = gson.toJson(code);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void requestedAri(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        Integer reason = null == request.getParameter("V_REASON") || request.getParameter("V_REASON").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("V_REASON"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {
            if ((user.getAccessUserType() == AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE) || (user.getAccessUserType() == AppConstant.TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE)) {
                reason = AppConstant.ARI;
            }
            boolean isRequested = claimHandlerService.requestedAri(claimNo, reason, remark, user);
            if (isRequested) {
                json = gson.toJson("SUCCESS");
                printWriter(request, response, json);
            } else {
                json = gson.toJson("FAIL");
                printWriter(request, response, json);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void closeClaim(HttpServletRequest request, HttpServletResponse response) {
        ReminderLetterFormDto reminderLetterFormDto = null;
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            String remark = request.getParameter("panleRemark");
//            String remark = request.getParameter("panleRemark");
            claimHandlerService.updateClaimClose(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void reOpenClaim(HttpServletRequest request, HttpServletResponse response) {
        ReminderLetterFormDto reminderLetterFormDto = null;
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            String type = request.getParameter("reOpenType") == null ? AppConstant.STRING_EMPTY : request.getParameter("reOpenType");
            String remark = request.getParameter("panleRemark");
            claimHandlerService.updateClaimReopen(claimNo, type, user, remark);
            json = gson.toJson(AppConstant.SUCCESS);
            printWriter(request, response, json);

        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.USER_NOT_FOUND);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }

    private void forwardToClaimHandler(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        try {
            String remark = request.getParameter("panleRemark");
            claimHandlerService.forwardToClaimHandler(claimNo, user, remark);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void isValidateSupplyOrderCalculationSheetApproved(HttpServletRequest request, HttpServletResponse response) {
        Integer result;
        Integer supplyOrderRefNo = request.getParameter("supplyOrderRefNo") == null ? 0 : Integer.parseInt(request.getParameter("supplyOrderRefNo"));
        String json;
        Gson gson = new Gson();
        String responseMessage;
        try {
            result = calculationSheetService.isSupplyOrderCalculationSheetApproved(supplyOrderRefNo);
            if (result > AppConstant.ZERO_INT) {
                responseMessage = calculationSheetService.validateDO(result) ? "VALID" : "UPDATED";
            } else {
                responseMessage = "INVALID";
            }
            json = gson.toJson(responseMessage);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("INVALID");
            printWriter(request, response, json);
        }
    }

    private void isCheckedAllInitialLiabilityApproveDocument(HttpServletRequest request, HttpServletResponse response) {

        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        Gson gson = new Gson();
        ClaimDocumentStatusDto claimDocumentStatusDto = null;
        try {
            claimDocumentStatusDto = claimDocumentService.getCheckedInitialLiabilityDocumentStatus(claimNo);
            json = gson.toJson(claimDocumentStatusDto.getDocumentStatusEnum().getDocumentStatus());
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("P");
            printWriter(request, response, json);
        }
    }

    private void isCheckedAllLiabilityApproveDocument(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        String json;
        Gson gson = new Gson();
        ClaimDocumentStatusDto claimDocumentStatusDto;
        try {
            claimDocumentStatusDto = claimDocumentService.getCheckedLiabilityDocumentStatus(claimNo);
            json = gson.toJson(claimDocumentStatusDto.getDocumentStatusEnum().getDocumentStatus());
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("P");
            printWriter(request, response, json);
        }
    }

    private void policyExcessChange(HttpServletRequest request, HttpServletResponse response) {
        BigDecimal policyExcess = request.getParameter("policyExcess") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("policyExcess"));
        BigDecimal finalAmount = request.getParameter("finalAmount") == null ? BigDecimal.ZERO : new BigDecimal(request.getParameter("finalAmount"));
        String isExcessInclude = request.getParameter("isExcessInclude") == null ? AppConstant.STRING_EMPTY : request.getParameter("isExcessInclude");
        String json;
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        BigDecimal total = BigDecimal.ZERO;
        try {
            if (AppConstant.YES.equals(isExcessInclude)) {
                total = finalAmount.subtract(policyExcess);
            } else if (AppConstant.NO.equals(isExcessInclude)) {
                total = finalAmount.add(policyExcess);
            }
            json = gson.toJson(total);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(BigDecimal.ZERO);
            printWriter(request, response, json);
        }
    }

    private void isAllMainPanelApproved(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
        Gson gson = new Gson();
        String json;
        try {
            String isMainPanelRejected = claimHandlerService.isAllMainPanelApproved(claimNo);
            json = gson.toJson(isMainPanelRejected);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson(AppConstant.FAIL);
            printWriter(request, response, json);
        }
    }


    private void getClaimStatus(HttpServletRequest request, HttpServletResponse response) {
        String json = "";
        Gson gson = new Gson();
        try {
            Integer claimId = request.getParameter(AppConstant.CLAIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.CLAIM_NO));
            Integer claimStatus = motorEngineerService.getClaimStatus(claimId);
            json = gson.toJson(claimStatus);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }

    private void isPendingBillForDeliveryOrderApprovedDocument(HttpServletRequest request, HttpServletResponse response) {
        String json = "";
        Gson gson = new Gson();
        try {
            Integer claimNo = request.getParameter(AppConstant.CLAIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.CLAIM_NO));
            Integer documentTypeId = request.getParameter("documentTypeId") == null ? 0 : Integer.parseInt(request.getParameter("documentTypeId"));
            Boolean isPendingBillDocument = supplyOrderService.isPendingBillForDeliveryOrderApprovedDocument(claimNo, documentTypeId);
            json = gson.toJson(isPendingBillDocument);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }

    }


}
