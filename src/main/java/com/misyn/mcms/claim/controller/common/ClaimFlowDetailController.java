package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimProcessFlowDto;
import com.misyn.mcms.claim.dto.ClaimUserTypeDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.ClaimFlowDetailService;
import com.misyn.mcms.claim.service.impl.ClaimProcessFlowDetailServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

@WebServlet(name = "ClaimFlowDetailController", urlPatterns = "/ClaimFlowDetailController/*")
public class ClaimFlowDetailController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BankDetailsController.class);

    ClaimFlowDetailService claimFlowDetailService = new ClaimProcessFlowDetailServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        ClaimUserTypeDto claimUserTypeDto;

        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/viewClaimFlowDetail":
                    ViewClaimFlowDetails(request, response);
                    break;

                case "/save-update":
                    saveClaimFlowDetails(request, response);
                    break;

                case "/save-claimLogs":
                    saveClaimLogs(request, response);
                    break;


            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void ViewClaimFlowDetails(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            List<ClaimProcessFlowDto> allClaimFlowDetails = claimFlowDetailService.getAllClaimFlowDetails();
            request.setAttribute(AppConstant.CLAIM_FLOW_DETAIL_LIST, allClaimFlowDetails);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimConfig/taskFlowDetails.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveClaimLogs(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").trim().isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("claimNo"));
         UserDto user = getSessionUser(request);
        try {
            claimFlowDetailService.saveClaimLogs(claimNo, user);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

    }

    private void saveClaimFlowDetails(HttpServletRequest request, HttpServletResponse response) {
        String task = null == request.getParameter("taskName") || request.getParameter("taskName").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("cardId");
        UserDto user = getSessionUser(request);
        Gson gson = new Gson();
        String json;
        boolean value = false;
        try {
            if (!task.equals(AppConstant.EMPTY_STRING)) {
                json = gson.toJson(value);
            } else {
                json = gson.toJson(claimFlowDetailService.SaveClaimFlowTask(task, user));
            }
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            json = gson.toJson("Error occurred while saving Task details");
            printWriter(request, response, json);
        }
    }

}
