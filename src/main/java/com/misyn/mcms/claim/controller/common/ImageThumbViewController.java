/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.controller.common;


import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.claim.service.impl.StorageServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;


/**
 * <AUTHOR>
 */
@WebServlet(name = "ImageThumbViewController", urlPatterns = "/ImageThumbViewController")
public class ImageThumbViewController extends HttpServlet {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageThumbViewController.class);


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) {

        HttpSession session = request.getSession();
        StorageService storageService = (StorageService)
                session.getAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE);
        if (storageService == null) {
            storageService = new StorageServiceImpl();
            session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
        }
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            request.setAttribute("refNo", refNo);
            ServletOutputStream outStream = response.getOutputStream();
            try (InputStream inputStream = storageService.viewThumbUploadImage(refNo)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outStream.write(buffer, 0, bytesRead);
                }
                outStream.flush();
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    @Override
    public String getServletInfo() {
        return "ImageThumbViewController";
    }


}
