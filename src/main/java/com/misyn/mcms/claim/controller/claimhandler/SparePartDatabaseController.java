package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.SparePartDatabaseService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "SparePartDatabaseController", urlPatterns = "/SparePartDatabaseController/*")
public class SparePartDatabaseController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserController.class);
    private SparePartDatabaseService sparePartDatabaseService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        sparePartDatabaseService = getCsparePartDatabaseServiceBySession(request);
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/save":
                    saveSparePartDatabase(request, response, user);
                    break;
                case "/search":
                    searchSparePartDatabase(request, response);
                    break;
                case "/searchAllsparePart":
                    searchAllSpareParts(request, response);
                    break;
                case "/viewSparePartDatabase":
                    setSupplyOrderPopupListValues(request, AppConstant.ZERO_INT);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/sparePartDatabase.jsp");
                    break;
                case "/VehicleMake":
                    vehicleMake(request, response);
                    break;
                case "/VehicleModel":
                    vehicleModel(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void vehicleMake(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            setClaimPanelUserPopupListValues(request);
            List<PopupItemDto> popupItemDto = sparePartDatabaseService.getVehicleMake();
            request.setAttribute("sparePartDatabase", popupItemDto);
            json = gson.toJson(popupItemDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void vehicleModel(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            setClaimPanelUserPopupListValues(request);
            String vehicleMake = request.getParameter("vehiclemakes");
            List<PopupItemDto> popupItemDto = sparePartDatabaseService.getVehicleModel(vehicleMake);
            request.setAttribute("sparePartDatabase", popupItemDto);
            json = gson.toJson(popupItemDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveSparePartDatabase(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, UserDto user) {
        SparePartDatabaseDto sparePartDatabaseDto = new SparePartDatabaseDto();
        SparePartDatabaseDto sparePartDatabase;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(sparePartDatabaseDto, httpServletRequest.getParameterMap());
            sparePartDatabaseDto.setInputDateTime(Utility.sysDateTime());
            sparePartDatabaseDto.setInputUserId(user.getUserId());
            sparePartDatabase = sparePartDatabaseService.save(sparePartDatabaseDto);

            if (null != sparePartDatabase) {
                json = "Data Added Successfully";
            }
            json = gson.toJson(json);
            printWriter(httpServletRequest, httpServletResponse, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, httpServletResponse);
            LOGGER.error(e.getMessage());
        }
    }

    private void searchAllSpareParts(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String vehicleMake = request.getParameter(AppConstant.TXT_VEHICLE_MAKE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_MAKE);
        String vehicleModel = request.getParameter(AppConstant.TXT_VEHICLE_MODEL) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_MODEL);
        String manufactureYear = request.getParameter(AppConstant.TXT_MANUFACTURE_YEAR) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_MANUFACTURE_YEAR);
        String supplerName = request.getParameter(AppConstant.TXT_SUPPLIER_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_SUPPLIER_NAME);
        Integer sparePart = Integer.parseInt(request.getParameter(AppConstant.TXT_SPAREPART_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_SPAREPART_NAME));
        setClaimPanelUserPopupListValues(request);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.vehicle_make", vehicleMake, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.vehicle_model", vehicleModel, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.manufacture_year", manufactureYear, FieldParameterDto.SearchType.Like, parameterList);
            if (sparePart != 0) {
                this.addFieldParameter("t2.n_spare_part_ref_no", String.valueOf(sparePart), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t3.N_SUPPLIER_ID", supplerName, FieldParameterDto.SearchType.Equal, parameterList);


            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t1.txn_id";
                    break;
                case "vehicleMake":
                    orderColumnName = "t1.vehicle_make";
                    break;
                case "vehicleModel":
                    orderColumnName = "t1.vehicle_model";
                    break;
                case "manufactureYear":
                    orderColumnName = "t1.manufacture_year";
                    break;
                case "sparePartRefNo":
                    orderColumnName = "t1.spare_part_ref_no";
                    break;
                case "supplierId":
                    orderColumnName = "t1.supplier_id";
                    break;
                case "price":
                    orderColumnName = "t1.price";
                    break;
                case "proceedDate":
                    orderColumnName = "t1.proceed_date";
                    break;
                case "inputDateTime":
                    orderColumnName = "t1.input_date_time";
                    break;
                case "inputUserId":
                    orderColumnName = "t1.input_user_id";
                    break;
                case "supplierDetailsMasterDto.supplerName":
                    orderColumnName = "t3.V_SUPPLER_NAME";
                    break;

            }
            boolean isSearch = request.getParameter("isSearch") == null
                    ? false
                    : (request.getParameter("isSearch").equalsIgnoreCase("1") ? true : false);
            DataGridDto data = sparePartDatabaseService.getSparePartDatabaseDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, isSearch);

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void searchSparePartDatabase(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        Gson gson = new Gson();
        String json;
        try {
            setClaimPanelUserPopupListValues(httpServletRequest);
            int id = Integer.parseInt(httpServletRequest.getParameter("txnId"));
            SparePartDatabaseDto sparePartDatabaseDto = sparePartDatabaseService.searchSparePartDatabase(id);
            httpServletRequest.setAttribute("sparePartDatabase", sparePartDatabaseDto);
            json = gson.toJson(sparePartDatabaseDto);
            PrintWriter out = httpServletResponse.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }
}
