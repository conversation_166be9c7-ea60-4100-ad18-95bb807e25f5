package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.ClaimCalculationSheetPayeeServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@WebServlet(name = "DashboardController", urlPatterns = "/DashboardController/*")
public class DashboardController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DashboardController.class);
    private static final AtomicReference<Long> currentTime = new AtomicReference<>(System.currentTimeMillis());
    private final ClaimCalculationSheetPayeeService claimCalculationSheetPayeeService = new ClaimCalculationSheetPayeeServiceImpl();
    private AcknowledgementService acknowledgementService;
    private ClaimSuperDashboardService claimSuperDashboardService = null;
    private ClaimHandlerService claimHandlerService;
    private RequestAriService requestAriService;
    private ClaimDocumentService claimDocumentService;
    private int draw = 1;
    private ClaimWiseDocumentService claimWiseDocumentService;
    private ReminderPrintService reminderPrintService;
    private SupplyOrderService supplyOrderService = null;
    private InspectionDetailsService inspectionDetailsService = null;
    private CallCenterService callCenterService = null;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        int type = 0;
        UserDto user = getSessionUser(request);
        String pathInfo = request.getPathInfo();
        UserDto sessionUser = getSessionUser(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        claimWiseDocumentService = getClaimWiseDocumentServiceBySession(request);
        requestAriService = getByRequestAri(request);
        reminderPrintService = getReminderPrintServiceBySession(request);
        acknowledgementService = getAcknowledgementService(request);
        claimDocumentService = getClaimDocumentServiceBySession(request);
        callCenterService = getCallServiceBySession(request);
        List<SpecialRemarkDto> specialRemarkList;
        ClaimUserTypeDto claimUserTypeDto;

        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/viewNotification":
                    viewList(request, response, user);
                    break;
                case "/viewAllNotification":
                    viewSuperDashboard(request, response, user);
                    break;
                case "/claimViewList":
                    claimList(request, response);
                    break;
                case "/claimList":
                    viewList(request, response, user);
                    break;
                case "/viewSuperDashboard":
                    viewSuperDashboard(request, response, user);
                    break;
                case "/viewSpecialRemark":
                    Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    specialRemarkList = claimHandlerService.searchRemarksByClaimNoAndDepartmentId(claimNo, AppConstant.BRANCH_DEPARTMENT_ID);
                    request.setAttribute(AppConstant.CLAIM_HANDLER_SPECIAL_REMARK, specialRemarkList);
                    request.setAttribute(AppConstant.CLAIM_NO, claimNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/specialRemarksBranch.jsp");
                    break;
                case "/viewRequestedAri":
                    claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    specialRemarkList = claimHandlerService.searchRemarksByClaimNoAndDepartmentId(claimNo, AppConstant.BRANCH_DEPARTMENT_ID);
                    RequestAriDto requestAriDto = requestAriService.searchByClaimNoPending(claimNo);
                    boolean isUploaded = requestAriService.checkDocumentsForARI(claimNo);
                    if (null != requestAriDto && (requestAriDto.getStatus().equals(AppConstant.STRING_PENDING) || requestAriDto.getStatus().equals(AppConstant.STRING_UPLOAD))) {
                        request.setAttribute(AppConstant.PENDING_ARI, AppConstant.YES);
                    } else {
                        request.setAttribute(AppConstant.ARI_ARRANGED, AppConstant.YES);
                    }
                    String dateTime = claimDocumentService.lastDocUploadDateTime(claimNo);
                    request.setAttribute(AppConstant.DOC_UPLOAD_DATE_TIME, dateTime);
                    request.setAttribute(AppConstant.REQUEST_ARI_DETAILS, requestAriDto);
                    request.setAttribute(AppConstant.IS_UPLOADED, isUploaded);
                    request.setAttribute(AppConstant.CLAIM_HANDLER_SPECIAL_REMARK, specialRemarkList);
                    request.setAttribute(AppConstant.CLAIM_NO, claimNo);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/requestBranch.jsp");
                    break;
                case "/viewDocumentUpload":
                    viewDocumentUpload(request, response);
                    break;
                case "/viewDocumentViewer":
                    viewDocumentViewer(request, response);
                    break;
                case "/viewAllDocumentUpload":
                    viewAllDocumentUpload(request, response);
                    break;
                case "/viewReminderPrint":
                    viewReminderPrint(request, response);
                    break;
                case "/viewAcknowledgementLetterPrint":
                    viewAcknowledgementLetterPrint(request, response);
                case "/viewDocumentStatusChange":
                    viewDocumentStatusChange(request, response);
                    break;
                case "/changeCheckReceiveStatus":
                    changeCheckReceiveStatus(request, response);
                    break;
                case "/viewReportAccidentList":
                    type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    removeSessionType(request, response);
                    updateSessionType(request, response, type);
                    removeSessionClaimDetails(request, response);
                    this.unlockedIntimation(request, sessionUser);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/policyList.jsp");
                    break;
                case "/underWrittingDetails":
                    Integer policyRefNo = request.getParameter("P_POL_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_REF_NO"));
                    claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
                    ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDto(policyRefNo, claimNo);
                    callCenterService.setOtherDetailsList(claimsDto);
                    request.setAttribute("claimsDto", claimsDto);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policyUnderwrittingDetails.jsp");
                    break;
                case "/policyMap":
                    policyRefNo = request.getParameter("P_POL_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_REF_NO"));
                    PolicyDto policy = callCenterService.getPolicyDetails(policyRefNo);
                    claimsDto = new ClaimsDto();
                    if (null != policy.getVehicleNumber()) {
                        claimsDto.setClaimHistory(callCenterService.getClaimHistoryForPolicyRefNo(policy.getVehicleNumber()));
                    }
                    claimsDto.setPolicyDto(policy);
                    request.setAttribute(AppConstant.SESSION_CLAIM_DTO, claimsDto);
                    updateSessionPolNo(request, response, policyRefNo);
                    request.setAttribute(AppConstant.policyRefNo, policyRefNo);
                    request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/policyView.jsp");
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void changeCheckReceiveStatus(HttpServletRequest request, HttpServletResponse response) {
        String voucherNo = null == request.getParameter("voucherNo") ? AppConstant.STRING_EMPTY : request.getParameter("voucherNo");
        String chequeNo = null == request.getParameter("chequeNo") ? AppConstant.STRING_EMPTY : request.getParameter("chequeNo");
        String dispatchLocation = null == request.getParameter("dispatchLocation") ? AppConstant.STRING_EMPTY : request.getParameter("dispatchLocation");
        Integer status = null == request.getParameter("status") || request.getParameter("status").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("status"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        Integer payeeId = null == request.getParameter("payeeId") || request.getParameter("payeeId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("payeeId"));
        String remark = null == request.getParameter("remark") ? AppConstant.STRING_EMPTY : request.getParameter("remark");
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        UserDto user = getSessionUser(request);
        try {
            ClaimPaymentDispatchDto claimPaymentDispatchDto = new ClaimPaymentDispatchDto();
            claimPaymentDispatchDto.setPayeeId(payeeId);
            claimPaymentDispatchDto.setCalSheetId(AppConstant.ZERO_INT);
            claimPaymentDispatchDto.setClaimNo(claimNo);
            claimPaymentDispatchDto.setVoucherNo(voucherNo);
            claimPaymentDispatchDto.setChequeNo(chequeNo);
            claimPaymentDispatchDto.setDispatchLocation(dispatchLocation);
            BranchDetailDto dispatchedLocationDetails = new BranchDetailDto();
            dispatchedLocationDetails.setBranchCode(user.getBranchCode());
            claimPaymentDispatchDto.setDispatchedLocation(dispatchedLocationDetails);
            claimPaymentDispatchDto.setDispatchUser(user.getUserId());
            claimPaymentDispatchDto.setDispatchDateTime(Utility.sysDateTime());
            claimPaymentDispatchDto.setChequeDispatchStatus(status == AppConstant.ONE_INT ? AppConstant.YES : AppConstant.NO);

            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimNo);
            specialRemarkDto.setRemark(remark);
            specialRemarkDto.setSectionName("Cheque Handover");
            specialRemarkDto.setDepartmentId(AppConstant.BRANCH_DEPARTMENT_ID);
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            claimCalculationSheetPayeeService.changeCheckReceiveStatus(claimPaymentDispatchDto, specialRemarkDto, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void viewSuperDashboard(HttpServletRequest request, HttpServletResponse response, UserDto user) {

        try {

            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));

            claimSuperDashboardService = getClaimSuperDashboardServiceBySession(request);
            ClaimSuperDashboardDto claimSuperDashboardDto = claimSuperDashboardService.getClaimSuperDashboardDto(claimNo);
            RequestAriDto requestAriDto = requestAriService.searchByClaimNoPending(claimNo);
            if (null != requestAriDto && (requestAriDto.getStatus().equals(AppConstant.STRING_PENDING) || requestAriDto.getStatus().equals(AppConstant.STRING_UPLOAD))) {
                request.setAttribute(AppConstant.PENDING_ARI, AppConstant.YES);
            }
            boolean isLeaveUser = false;
            if (null != claimSuperDashboardDto.getClaimHandlerDto().getAssignUserId()) {
                isLeaveUser = claimSuperDashboardService.isLeaveAssignUser(claimSuperDashboardDto.getClaimHandlerDto().getAssignUserId());
            }
            BulkCloseDetailDto bulkCloseDetailDto = claimHandlerService.getBulkCloseDetails(claimNo);

            request.setAttribute(AppConstant.REQUEST_ARI_DETAILS, requestAriDto);
            request.setAttribute(AppConstant.REQUEST_FORM_ID, currentTime.accumulateAndGet(System.currentTimeMillis(),
                    (prev, next) -> next > prev ? next : prev + 1));
            request.setAttribute(AppConstant.CLAIM_SUPER_DASHBOARD_DTO, claimSuperDashboardDto);
            request.setAttribute("isLeaveUser", isLeaveUser);
            request.setAttribute(AppConstant.BULK_CLOSE_DETAIL_DTO, bulkCloseDetailDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/superDashboard.jsp");
    }

    private void viewList(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer type = (Integer) request.getSession().getAttribute("TYPE");

        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String fileStatus = request.getParameter(AppConstant.TXT_FILE_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FILE_STATUS);
        String liabilityStatus = request.getParameter(AppConstant.TXT_LIABILITY_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LIABILITY_STATUS);


        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t2.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);

            if (!"All".equals(liabilityStatus)) {
                this.addFieldParameter("t2.V_LIABILITY_APRV_STATUS", liabilityStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }


            if (!"All".equals(fileStatus)) {
                String selectFileStatus = AppConstant.EMPTY_STRING;
                switch (fileStatus) {
                    case "1":
                        selectFileStatus = "'AR','N'";
                        break;
                    case "2":
                        selectFileStatus = "'Y'";
                        break;
                    case "3":
                        selectFileStatus = "'AR'";
                        break;
                }
                this.addFieldParameter("t2.V_IS_FILE_STORE", selectFileStatus, FieldParameterDto.SearchType.IN, parameterList);
            }


            switch (orderColumnName) {
                case "txnId":
                    orderColumnName = "t2.N_TXN_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumberValue":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "t1.V_VEHICLE_NO";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "assignDateTime":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "acr":
                    orderColumnName = "t2.N_APRV_TOT_ACR_AMOUNT";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t3.v_status_desc";
                    break;
                case "presentReverseAmount":
                    orderColumnName = "t2.N_RESERVE_AMOUNT";
                    break;
                case "liabilityAssignUser":
                    orderColumnName = "t2.V_LIABILITY_APRV_ASSIGN_USER";
                    break;
                case "liabilityAssignDatetime":
                    orderColumnName = "t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME";
                    break;
                case "intLiabilityAssignUser":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_USER_ID";
                    break;
                case "intLiabilityAssignDatetime":
                    orderColumnName = "t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME";
                    break;

            }
            DataGridDto data = claimHandlerService.getClaimHandlerDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, null);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void claimList(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,84)");

        int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
        removeSessionType(request, response);
        updateSessionType(request, response, type);
        // removeSessionClaimDetails(request, response);
        request.setAttribute("statusList", popupItemDtoList);
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/superdashboard/claimList.jsp");
    }

    private void viewDocumentUpload(HttpServletRequest request, HttpServletResponse response) {
        try {
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            if (historyRecord.equals(AppConstant.YES)) {
                claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadBranchViewDtoList(claimId);
                request.setAttribute(AppConstant.HISTORY_CLAIM_UPLOADVIEW_DTO_LIST, claimUploadViewDtoList);
            } else {
                claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadBranchViewDtoList(claimId);
                request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            }
            RequestAriDto requestAriDto = requestAriService.searchByClaimNo(claimId);
            request.setAttribute(AppConstant.PENDING_ARI, null != requestAriDto ? true : false);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            request.setAttribute(AppConstant.REQUEST_FORM_ID, request.getParameter(AppConstant.REQUEST_FORM_ID));
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/documentUpload.jsp");
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void viewDocumentViewer(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            String isBankDetails = request.getParameter("isBankDetails") == null ? AppConstant.NO : request.getParameter("isBankDetails");
            ClaimDocumentDto claimDocumentDto = claimHandlerService.getClaimDocumentDto(refNo);
            claimDocumentDto.setIsBankDetails(isBankDetails);
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimDocumentDto);
            request.setAttribute("refNo", refNo);
            request.setAttribute("isBankDetails", isBankDetails);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/pdfViewerAll.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    private void viewAllDocumentUpload(HttpServletRequest request, HttpServletResponse response) {
        boolean salvageArranged = false;
        boolean ariArranged = false;
        boolean ariRequested = false;
        boolean salvageRequested = false;
        try {
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            String SEARCH_DOC = request.getParameter(AppConstant.SEARCH_DOC) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.SEARCH_DOC);
            if (historyRecord.equals(AppConstant.YES)) {
                claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadBranchViewDtoList(claimId, SEARCH_DOC);
                request.setAttribute(AppConstant.HISTORY_CLAIM_UPLOADVIEW_DTO_LIST, claimUploadViewDtoList);
            } else {
                claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadBranchViewDtoList(claimId, SEARCH_DOC);
                request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
            }

            int ariRequestedID = requestAriService.isAriOrSalvageRequested(claimId);
            int salvageORAriArranged = requestAriService.isSalvageORAriArranged(claimId);

            if (ariRequestedID == AppConstant.ARI_REQUEST || ariRequestedID == AppConstant.ARI || ariRequestedID == AppConstant.ARI_SALVAGE) {
                ariRequested = true;
            } else if (ariRequestedID == AppConstant.SALVAGE_REQUEST || ariRequestedID == AppConstant.COLLECT_SALVAGE) {
                salvageRequested = true;
            }

            if (salvageORAriArranged == AppConstant.ARI_INSPECTION) {
                ariArranged = true;
            } else if (salvageORAriArranged == AppConstant.SALVAGE_INSPECTION) {
                salvageArranged = true;
            }

            if (salvageArranged || ariArranged) {
                salvageRequested = false;
                ariRequested = false;
            }
            request.setAttribute(AppConstant.ARI_REQUESTED, ariRequested);
            request.setAttribute(AppConstant.ARI_ARRANGED, ariArranged);
            request.setAttribute(AppConstant.SALVAGE_ARRANGED, salvageArranged);
            request.setAttribute(AppConstant.SALVAGE_REQUESTED, salvageRequested);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            request.setAttribute(AppConstant.CLAIM_NO, claimId);
            request.setAttribute(AppConstant.SEARCH_DOC_NAME, SEARCH_DOC);
            request.setAttribute(AppConstant.REQUEST_FORM_ID, request.getParameter(AppConstant.REQUEST_FORM_ID));
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/documentAllUpload.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewReminderPrint(HttpServletRequest request, HttpServletResponse response) {
        ReminderLetterFormDto reminderLetterFormDto = null;
        try {
            Integer claimNo = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            //  Integer reminderSummaryRefId = request.getParameter("reminderSummaryRefId") == null ? 0 : Integer.parseInt(request.getParameter("reminderSummaryRefId"));
            reminderLetterFormDto = reminderPrintService.getReminderLetterFormDto(claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            request.setAttribute(AppConstant.REMINDER_LETTER_FORM_DTO, reminderLetterFormDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/claimGenerateReminder.jsp");
        }
    }

    private void viewAcknowledgementLetterPrint(HttpServletRequest request, HttpServletResponse response) {

        List<PopupItemDto> popupItemDtoList = getDbRecordCommonFunctionBySession(request).getPopupItemDtoList("claim_status_para ", "n_ref_id", "v_status_desc", "n_ref_id IN(9,5,16,20,21,35,36,37,38,39,30,17,50,84)");
        List<ClaimWiseDocumentDto> claimWiseDocumentDtos;
        try {

            int type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
            int claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
            removeSessionType(request, response);
            updateSessionType(request, response, type);
            removeSessionClaimDetails(request, response);
            request.setAttribute("statusList", popupItemDtoList);
            List<AcknowledgementSummaryDto> previousAcknowledgementList = acknowledgementService.getPreviousAcknowledgementList(claimId);
            request.setAttribute("previousList", previousAcknowledgementList);
            request.setAttribute("IS_PREVIOUS_SHOW", AppConstant.YES);
            request.setAttribute("P_N_CLIM_NO", claimId);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/acknowledgementLetter.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewDocumentStatusChange(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimHandlerService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/dashboard/claimDocumentStatusChange.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
