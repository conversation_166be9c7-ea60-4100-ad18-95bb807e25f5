package com.misyn.mcms.claim.controller.common;

import com.misyn.mcms.claim.dto.JWTClaimDto;
import com.misyn.mcms.claim.redis.RedisService;
import com.misyn.mcms.utility.JwtUtil;
import com.misyn.mcms.utility.Parameters;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

@WebServlet(name = "logoutController", value = "/logout")
public class LogoutController extends HttpServlet {

    private final String AUTH_LOGOUT_URL = Parameters.getAuthServiceLogoutUrl();

    @Override
    public void init() {

    }

    @Override
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String token = (String) request.getSession(true).getAttribute("token");
        JWTClaimDto jwtClaimDto = JwtUtil.decodeJwt(token);
        System.out.println("token: " + token);
        if (jwtClaimDto != null) {
            RedisService.deleteCacheData( jwtClaimDto.getUsername().toLowerCase()+"-permission");
        }

        request.getSession(true).invalidate();
        response.sendRedirect(AUTH_LOGOUT_URL);
    }

    public void destroy() {
    }
}
