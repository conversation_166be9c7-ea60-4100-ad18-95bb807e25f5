package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.misyn.mcms.admin.admin.dto.BankDetailsDto;
import com.misyn.mcms.admin.admin.dto.BankDetailsSearchResponseDto;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeNameDto;
import com.misyn.mcms.claim.dto.ClaimUserTypeDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.BankDetailsService;
import com.misyn.mcms.claim.service.impl.BankDetailsServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.ListBoxItem;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@WebServlet(name = "BankDetailsController", urlPatterns = "/BankDetailsController/*")
public class BankDetailsController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BankDetailsController.class);
    private BankDetailsService bankDetailsService = new BankDetailsServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        ClaimUserTypeDto claimUserTypeDto;

        try {
            claimUserTypeDto = claimUserTypeDto(request);
            request.setAttribute(AppConstant.SESSION_CLAIM_USER_TYPE_DTO, claimUserTypeDto);
            setInitValues(request);
            switch (pathInfo) {
                case "/save-update":
                    saveBankDetails(request, response);
                    break;
                case "/update-verification":
                    updateVerification(request, response);
                    break;
                case "/update-rejection":
                    updateRejection(request, response);
                    break;
                case "/delete":
                    deleteBankDetails(request, response);
                    break;
                case "/search":
                    searchBankDetails(request, response);
                    break;
                case "/instrument-types":
                    getInstrumentTypeDetails(request, response);
                    break;
                case "/payee-types":
                    getLoadPayeeTypeList(request, response);
                    break;
                case "/payee-names":
                    getLoadPayeeList(request, response);
                    break;
                case "/insured-bank-details":
                    loadInsuredBankDetails(request, response);
                    break;
                case "/saved-bank-details":
                    loadSavedBankDetails(request, response);
                    break;
                case "/uploaded-doc-ref":
                    getUploadedDocRef(request, response);
                    break;
                case "/is-bank-details-verified":
                    isBankDetailsVerified(request, response);
                    break;

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void updateVerification(HttpServletRequest request, HttpServletResponse response) {
        Integer instrumentType = null == request.getParameter("instrumentType") || request.getParameter("instrumentType").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("instrumentType"));
        String payeeName = null == request.getParameter("payeeName") || request.getParameter("payeeName").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeName");
        Integer payeeType = null == request.getParameter("payeeType") || request.getParameter("payeeType").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("payeeType"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String verify = null == request.getParameter("verify") || request.getParameter("verify").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("verify");
        Integer cardId = null == request.getParameter("cardId") || request.getParameter("cardId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("cardId"));
        String instrumentTypeDesc = null == request.getParameter("instrumentTypeDesc") || request.getParameter("instrumentTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("instrumentTypeDesc");
        String payeeTypeDesc = null == request.getParameter("payeeTypeDesc") || request.getParameter("payeeTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeTypeDesc");
        String payeeNameDesc = null == request.getParameter("payeeNameDesc") || request.getParameter("payeeNameDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeNameDesc");

        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        UserDto user = getSessionUser(request);
        bankDetailsDto.setId(cardId);
        bankDetailsDto.setInstrumentType(instrumentType);
        bankDetailsDto.setPayeeName(payeeName);
        bankDetailsDto.setPayeeType(payeeType);
        bankDetailsDto.setVerifyStatus(verify);
        bankDetailsDto.setVerifyUser(user.getUserId());
        bankDetailsDto.setLastUpdateUser(user.getUserId());
        bankDetailsDto.setClaimNo(claimNo);
        bankDetailsDto.setInstrumentTypeDesc(instrumentTypeDesc);
        bankDetailsDto.setPayeeTypeDesc(payeeTypeDesc);
        bankDetailsDto.setPayeeNameDesc(payeeNameDesc);
        Gson gson = new Gson();
        String json;
        try {
            bankDetailsService.updateVerification(bankDetailsDto, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while updating verification status");
            printWriter(request, response, json);
        }
    }

    private void updateRejection(HttpServletRequest request, HttpServletResponse response) {
        Integer cardId = null == request.getParameter("cardId") || request.getParameter("cardId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("cardId"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String verify = null == request.getParameter("verify") || request.getParameter("verify").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("verify");
        String instrumentTypeDesc = null == request.getParameter("instrumentTypeDesc") || request.getParameter("instrumentTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("instrumentTypeDesc");
        String payeeTypeDesc = null == request.getParameter("payeeTypeDesc") || request.getParameter("payeeTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeTypeDesc");
        String payeeNameDesc = null == request.getParameter("payeeNameDesc") || request.getParameter("payeeNameDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeNameDesc");
        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        UserDto user = getSessionUser(request);
        bankDetailsDto.setId(cardId);
        bankDetailsDto.setClaimNo(claimNo);
        bankDetailsDto.setVerifyStatus(verify);
        bankDetailsDto.setRejectUser(user.getUserId());
        bankDetailsDto.setLastUpdateUser(user.getUserId());
        bankDetailsDto.setInstrumentTypeDesc(instrumentTypeDesc);
        bankDetailsDto.setPayeeTypeDesc(payeeTypeDesc);
        bankDetailsDto.setPayeeNameDesc(payeeNameDesc);
        Gson gson = new Gson();
        String json;
        try {
            bankDetailsService.updateRejection(bankDetailsDto, user);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while updating rejection status");
            printWriter(request, response, json);
        }
    }

    private void saveBankDetails(HttpServletRequest request, HttpServletResponse response) {
        Integer cardId = null == request.getParameter("cardId") || request.getParameter("cardId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("cardId"));
        Integer refId = null == request.getParameter("refId") || request.getParameter("refId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("refId"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        Integer instrumentType = null == request.getParameter("instrumentType") || request.getParameter("instrumentType").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("instrumentType"));
        Integer payeeType = null == request.getParameter("payeeType") || request.getParameter("payeeType").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("payeeType"));
        String payeeName = null == request.getParameter("payeeName") || request.getParameter("payeeName").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeName");
        String isUpload = null == request.getParameter("isUpload") || request.getParameter("isUpload").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("isUpload");
        String verify = null == request.getParameter("verify") || request.getParameter("verify").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("verify");
        String instrumentTypeDesc = null == request.getParameter("instrumentTypeDesc") || request.getParameter("instrumentTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("instrumentTypeDesc");
        String payeeTypeDesc = null == request.getParameter("payeeTypeDesc") || request.getParameter("payeeTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeTypeDesc");
        String payeeNameDesc = null == request.getParameter("payeeNameDesc") || request.getParameter("payeeNameDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeNameDesc");
        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        bankDetailsDto.setId(cardId);
        bankDetailsDto.setClaimNo(claimNo);
        bankDetailsDto.setInstrumentType(instrumentType);
        bankDetailsDto.setPayeeType(payeeType);
        bankDetailsDto.setPayeeName(payeeName);
        bankDetailsDto.setInstrumentTypeDesc(instrumentTypeDesc);
        bankDetailsDto.setPayeeTypeDesc(payeeTypeDesc);
        bankDetailsDto.setPayeeNameDesc(payeeNameDesc);
        bankDetailsDto.setDocRefNo(refId);
        bankDetailsDto.setUploadStatus(isUpload);
        bankDetailsDto.setVerifyStatus(verify);
        UserDto user = getSessionUser(request);
        bankDetailsDto.setInputUser(user.getUserId());
        bankDetailsDto.setLastUpdateUser(user.getUserId());
        Gson gson = new Gson();
        String json;
        try {
            Integer generatedCardIndex = bankDetailsService.saveBankDetails(bankDetailsDto, user);
            json = gson.toJson(generatedCardIndex);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            json = gson.toJson("Error occurred while saving bank details");
            printWriter(request, response, json);
        }
    }

    private void getInstrumentTypeDetails(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            Map<Integer, String> typeDetails = bankDetailsService.getInstrumentTypeList();
            json = gson.toJson(typeDetails);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void getLoadPayeeTypeList(HttpServletRequest request, HttpServletResponse response) {
        String json;
        Gson gson = new Gson();
        List<ClaimCalculationSheetPayeeNameDto> list = new ArrayList<>();
        try {
            list = bankDetailsService.getPayeeTypeList();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            response.setContentType("application/json");
            json = gson.toJson(list);
            printWriter(request, response, json);
        }
    }

    private void getLoadPayeeList(HttpServletRequest request, HttpServletResponse response) {
        Integer payeeType = null == request.getParameter("payeeType") || request.getParameter("payeeType").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("payeeType"));
        String customerName = null == request.getParameter("customerName") || request.getParameter("customerName").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("customerName");
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String policyChannelType = null == request.getParameter("policyChannelType") || request.getParameter("policyChannelType").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("policyChannelType");
        Gson gson = new Gson();
        String json;
        try {
            BankDetailsDto bankDetailsDto = new BankDetailsDto();
            bankDetailsDto.setPayeeType(payeeType);
            bankDetailsDto.setClaimNo(claimNo);
            bankDetailsDto.setCustomerName(customerName);
            bankDetailsDto.setPolicyChannelType(policyChannelType);
            List<ListBoxItem> typeDetails = bankDetailsService.getPayeeNameListById(bankDetailsDto);
            json = gson.toJson(typeDetails);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void loadSavedBankDetails(HttpServletRequest request, HttpServletResponse response) {
        String customerName = null == request.getParameter("customerName") || request.getParameter("customerName").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("customerName");
        String policyChannelType = null == request.getParameter("policyChannelType") || request.getParameter("policyChannelType").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("policyChannelType");
        Integer endCount = null == request.getParameter("endCount") || request.getParameter("endCount").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("endCount"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String policyNumber = null == request.getParameter("policyNumber") || request.getParameter("policyNumber").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("policyNumber");
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        bankDetailsDto.setClaimNo(claimNo);
        bankDetailsDto.setEndCount(endCount);
        bankDetailsDto.setPolicyNumber(policyNumber);
        bankDetailsDto.setCustomerName(customerName);
        bankDetailsDto.setPolicyChannelType(policyChannelType);
        try {

            // check before state flag
            List<BankDetailsDto> prevBankDetails = bankDetailsService.getPrevBankDetails(bankDetailsDto, user);  // Use potentially wrapped request from this point?
            json = gson.toJson(prevBankDetails);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while retrieving saved bank details");
            printWriter(request, response, json);
        }
    }

    private void loadInsuredBankDetails(HttpServletRequest request, HttpServletResponse response) {
        Integer cardId = null == request.getParameter("cardId") || request.getParameter("cardId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("cardId"));
        String customerNIC = null == request.getParameter("customerNIC") || request.getParameter("customerNIC").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("customerNIC");
        Integer endCount = null == request.getParameter("endCount") || request.getParameter("endCount").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("endCount"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        String policyNumber = null == request.getParameter("policyNumber") || request.getParameter("policyNumber").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("policyNumber");
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        bankDetailsDto.setClaimNo(claimNo);
        bankDetailsDto.setEndCount(endCount);
        bankDetailsDto.setPolicyNumber(policyNumber);
        bankDetailsDto.setCustomerNIC(customerNIC);
        bankDetailsDto.setId(cardId);
        try {
            BankDetailsDto prevBankDetails = bankDetailsService.getInsuredBankDetails(bankDetailsDto, user);  // Use potentially wrapped request from this point?
            json = gson.toJson(prevBankDetails);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while retrieving insured bank details");
            printWriter(request, response, json);
        }
    }

    private void searchBankDetails(HttpServletRequest request, HttpServletResponse response) {
        String customerName = null == request.getParameter("customerName") || request.getParameter("customerName").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("customerName");
        String policyChannelType = null == request.getParameter("policyChannelType") || request.getParameter("policyChannelType").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("policyChannelType");
        Integer endCount = null == request.getParameter("endCount") || request.getParameter("endCount").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("endCount"));
        String policyNumber = null == request.getParameter("policyNumber") || request.getParameter("policyNumber").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("policyNumber");
        String payeeName = null == request.getParameter("payeeName") || request.getParameter("payeeName").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeName");
        Integer payeeType = null == request.getParameter("payeeType") || request.getParameter("payeeType").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("payeeType"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);
        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        bankDetailsDto.setClaimNo(claimNo);
        bankDetailsDto.setEndCount(endCount);
        bankDetailsDto.setPolicyNumber(policyNumber);
        bankDetailsDto.setCustomerName(customerName);
        bankDetailsDto.setPolicyChannelType(policyChannelType);
        bankDetailsDto.setPayeeName(payeeName);
        bankDetailsDto.setPayeeType(payeeType);
        try {

            // check before state flag
            BankDetailsSearchResponseDto bankDetailsSearchResponseDto = bankDetailsService.getBankDetails(bankDetailsDto, user, payeeType, payeeName);  // Use potentially wrapped request from this point?
            json = gson.toJson(bankDetailsSearchResponseDto);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while retrieving bank details");
            printWriter(request, response, json);
        }
    }

    private void isBankDetailsVerified(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        Integer calSheetId = request.getParameter("calSheetId") == null || request.getParameter("calSheetId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("calSheetId"));
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(request);

        try {

            // check before state flag
            boolean isVerified = bankDetailsService.isBankDetailsVerified(claimNo, calSheetId, user);
            json = gson.toJson(isVerified);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while retrieving bank details");
            printWriter(request, response, json);
        }
    }

    private void getUploadedDocRef(HttpServletRequest request, HttpServletResponse response) {
        Integer cardId = null == request.getParameter("cardId") || request.getParameter("cardId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("cardId"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));
        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        bankDetailsDto.setId(cardId);
        bankDetailsDto.setClaimNo(claimNo);
        UserDto user = getSessionUser(request);
        bankDetailsDto.setInputUser(user.getUserId());
        Gson gson = new Gson();
        String json;
        try {
            Integer docRef = bankDetailsService.getUploadedDocRef(bankDetailsDto, user);
            json = gson.toJson(docRef);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while retrieving uploaded document reference number");
            printWriter(request, response, json);
        }
    }

    private void deleteBankDetails(HttpServletRequest request, HttpServletResponse response) {
        Integer cardId = null == request.getParameter("cardId") || request.getParameter("cardId").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("cardId"));
        Integer claimNo = null == request.getParameter("claimNo") || request.getParameter("claimNo").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("claimNo"));

        String instrumentTypeDesc = null == request.getParameter("instrumentTypeDesc") || request.getParameter("instrumentTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("instrumentTypeDesc");
        String payeeTypeDesc = null == request.getParameter("payeeTypeDesc") || request.getParameter("payeeTypeDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeTypeDesc");
        String payeeNameDesc = null == request.getParameter("payeeNameDesc") || request.getParameter("payeeNameDesc").isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("payeeNameDesc");

        BankDetailsDto bankDetailsDto = new BankDetailsDto();
        bankDetailsDto.setId(cardId);
        bankDetailsDto.setClaimNo(claimNo);
        UserDto user = getSessionUser(request);
        bankDetailsDto.setInputUser(user.getUserId());
        bankDetailsDto.setLastUpdateUser(user.getUserId());
        bankDetailsDto.setInstrumentTypeDesc(instrumentTypeDesc);
        bankDetailsDto.setPayeeTypeDesc(payeeTypeDesc);
        bankDetailsDto.setPayeeNameDesc(payeeNameDesc);
        Gson gson = new Gson();
        String json;
        try {

            // do not delete, maintain state flag
            bankDetailsService.deleteBankDetails(bankDetailsDto, user);
            json = gson.toJson("");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // Set HTTP status code to 500
            json = gson.toJson("Error occurred while deleting bank details");
            printWriter(request, response, json);
        }
    }

}
