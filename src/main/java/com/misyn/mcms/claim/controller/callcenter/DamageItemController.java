package com.misyn.mcms.claim.controller.callcenter;

import com.misyn.mcms.claim.controller.BaseController;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

@WebServlet(name = "DamageItemController", urlPatterns = "/DamageItem/*")
public class DamageItemController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DamageItemController.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }


    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        try {
            if (pathInfo.equals("/viewThirdPartyDetails")) {
                requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/assessorAllocation.jsp");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
