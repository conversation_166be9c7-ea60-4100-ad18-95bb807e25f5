package com.misyn.mcms.claim.controller.callcenter;


import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.controller.callcenter.validator.AssessorAllocationValidator;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.CommonBasketDto;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.CommonBasketServiceImpl;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.AppUrl;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by akila on 4/3/18.
 */
@WebServlet(name = "AssessorAllocationController", urlPatterns = "/AssessorAllocationController/*")
public class AssessorAllocationController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorAllocationController.class);
    private AssessorService assessorService = null;
    private AssessorAllocationService assessorAllocationService = null;
    private CityService cityService = null;
    private DistrictService districtService = null;
    private AssessorAllocationValidator assessorAllocationValidator = new AssessorAllocationValidator();
    private CallCenterService callCenterService;
    private ClaimWiseDocumentService claimWiseDocumentService;
    private ClaimHandlerService claimHandlerService;
    private MotorEngineerService motorEngineerService = null;
    private int draw = 1;
    CommonBasketService commonBasketService = new CommonBasketServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void setPopupList(HttpServletRequest request) {
        String strInsepctionTypes = DbRecordCommonFunction.getInstance().
                getPopupList("claim_inspection_type ", "inspection_type_id", "inspection_type_desc", "inspection_type_id <> 0", "");

        String strInsepctionReasonTypes = DbRecordCommonFunction.getInstance().
                getPopupList("claim_inspection_type_reason ", "N_ID", "V_REASON", "V_REC_STATUS='A' AND N_ID <>0 ", "");

        String strRejectedReason = DbRecordCommonFunction.getInstance().
                getPopupList("claim_assessor_reject_reason ", "N_ID", "V_REASON", "V_REC_STATUS='A' AND N_ID <>0   ", "");

        String strReassigningReason = DbRecordCommonFunction.getInstance().
                getPopupList("claim_assessor_reassign_reason ", "N_ID", "V_REASON", " V_REC_STATUS='A' AND N_ID <>0  ", "");
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        assessorService = getAssessorServiceBySession(request);
        cityService = getCityServiceBySession(request);
        assessorAllocationService = getAssessorAllocationServiceServiceBySession(request);
        districtService = getDistrictServiceBySession(request);
        callCenterService = getCallCenterServiceBySession(request);
        claimWiseDocumentService = getClaimWiseDocumentServiceBySession(request);
        claimHandlerService = getCallHandlerServiceBySession(request);
        motorEngineerService = getMotorEngineerBySession(request);
        try {
            switch (pathInfo) {
                case "/save":
                    saveAssessorAllocation(request, response);
                    break;
                case "/claimlist":
                    break;
                case "/citylist":
                    getCityList(request, response);
                    break;
                case "/assessorlist":
                    getAssessorList(request, response);
                    break;
                case "/reassign":
                    reAssignJob(request, response);
                    break;
                case "/validate":
                    validateAllocation(request, response);
                    break;
                case "/onsite":
                    getOnsiteInspection(request, response);
                    break;
                case "/districtList":
                    getDistrictList(request, response);
                    break;
                case "/assessorConatct":
                    getAssessorMobileNo(request, response);
                    break;
                case "/addRemark":
                    getSpecialRemark(request, response);
                    break;
                case "/completeJob":
                    updateCompledJob(request, response);
                    break;
                case "/resendSms":
                    resendSms(request, response);
                    break;
                case "/documentUpload":
                    viewDocumentUpload(request, response);
                    break;
                case "/viewDocumentViewer":
                    viewDocumentViewer(request, response);
                    break;
                case "/rejectAssessorJob":
                    rejectAssessorJob(request, response);
                    break;
                case "/assessorListByRte":
                    assessorListByRte(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void assessorListByRte(HttpServletRequest request, HttpServletResponse response) {
        String rteCode = null == request.getParameter("rteCode") ? AppConstant.STRING_EMPTY : request.getParameter("rteCode");
        Gson gson = new Gson();
        String json;
        try {
            List<AssessorDto> assessorListByRteCode = assessorAllocationService.getAssessorListByRteCode(rteCode);
            json = gson.toJson(assessorListByRteCode);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void rejectAssessorJob(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = Integer.valueOf(request.getParameter("claimId") == null ? AppConstant.STRING_EMPTY : request.getParameter("claimId"));
        String assignedUser = null == request.getParameter("assignedUser") ? AppConstant.STRING_EMPTY : request.getParameter("assignedUser");
        int inspectionId = null == request.getParameter("inspectionId") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("inspectionId"));
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.getClaimsDto().setClaimNo(claimNo);
            AssessorAllocationDto assessor = new AssessorAllocationDto();
            assessor.setClaimsDto(getSessionClaimDetails(request, response));
            String[] sysTime = Utility.getSeparateCurruntSysTimeString12hours();
            assessor.setAccidentTimeFileds(sysTime);
            assessor.setAssignDatetime(Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            UserDto user = getSessionUser(request);
            BeanUtils.populate(assessorAllocationDto, request.getParameterMap());
            assessorAllocationDto.getInspectionDto().setInspectionId(inspectionId);
            assessorAllocationService.rejectAssesorJob(assessorAllocationDto, user, assignedUser);
            request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Rejected Successfully");
            request.setAttribute(AppConstant.IS_DISABLED, AppConstant.NO);
            request.setAttribute(AppConstant.CLAIM_ID, claimNo);
            request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());
            request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
            request.setAttribute("assessorList", assessorService.getAssessorListByDivisionCode(AppConstant.STRING_EMPTY));
            request.setAttribute(AppConstant.INSPECTION_LIST, assessorAllocationService.getInspectionList(claimNo));
            request.setAttribute("assessorAllocationList", assessorAllocationService.getAssessorListByClaimNo(claimNo));
            request.setAttribute("assessorAllocationDto", assessor);
            requestDispatcher(request, response, AppUrl.URL_ASSESSORALLOCATION);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void saveAssessorAllocation(HttpServletRequest request, HttpServletResponse response) {
        AssessorAllocationDto assessorAllocation = null;
        List<AssessorAllocationDto> assessorAllocationList = null;
        String onsiteReview = request.getParameter("onsiteReview"); // "true" if checked, null if unchecked
        boolean isOnsiteReview = Boolean.parseBoolean(onsiteReview); // true or false
        String requestedInspectionId = request.getParameter("requestedInspectionId");
        ClaimsDto claimsDto = getSessionClaimDetails(request, response);
        boolean isSaved = false;
        int successCode = 0;
        String userID=null;
        try {

            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            UserDto user = getSessionUser(request);
            userID=user.getUserId();
            BeanUtils.populate(assessorAllocationDto, request.getParameterMap());

            String isPartnerGarage = request.getParameter("isPartnerGarage");
            if (isPartnerGarage != null && "Y".equals(isPartnerGarage)) {
                assessorAllocationDto.setIsPartnerGarage("Y");
            } else {
                assessorAllocationDto.setIsPartnerGarage("N");
            }

            if (null != claimsDto && claimsDto.getClaimNo().intValue() > 0) {
                assessorAllocationDto.setClaimsDto(claimsDto);
                assessorAllocationDto.setIsOnsiteReview(isOnsiteReview?AppConstant.YES : AppConstant.NO);
                assessorAllocation = assessorAllocationService.insert(assessorAllocationDto, user);
            } else {
                throw new Exception("Claim can not be processed");
            }

            if (AppConstant.DESKTOP_INSPECTION== assessorAllocationDto.getInspectionDto().getInspectionId() || AppConstant.DESKTOP_INSPECTION_ONLINE == assessorAllocationDto.getInspectionDto().getInspectionId()) {
                MotorEngineerDetailsDto motorEngineerDetailsDto = new MotorEngineerDetailsDto();
                motorEngineerDetailsDto.setClaimNo(claimsDto.getClaimNo());
                motorEngineerDetailsDto.setRefNo(assessorAllocationDto.getRefNo());
                motorEngineerDetailsDto.setJobId(assessorAllocationDto.getJobId());
                motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
                motorEngineerDetailsDto.setAssessorFeeDetailId(AppConstant.ZERO_INT);
                motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().setInspectionId(AppConstant.DESKTOP_INSPECTION == assessorAllocationDto.getInspectionDto().getInspectionId() ? AppConstant.DESKTOP_INSPECTION : AppConstant.DESKTOP_INSPECTION_ONLINE);
                populateInitTyreConditionList(motorEngineerDetailsDto, request.getParameterMap());
                 motorEngineerService.insertDesktopInitialRecords(motorEngineerDetailsDto, user);
            }

            request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());

            if (null != assessorAllocation) {
                isSaved = true;
                successCode = 1;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            request.setAttribute(AppConstant.ERROR_MESSAGE, e.getMessage());
        } finally {
            try {
                if (isSaved) {
                    if(isOnsiteReview){
                        commonBasketService.updateStatus(CommonBasketDto.Status.COMPLETED.getCode(), userID,Integer.valueOf(requestedInspectionId),claimsDto.getClaimNo());
                    }
                    response.sendRedirect(request.getContextPath() + "/CallCenter/viewAssessorAllocation?successCode=" + successCode);
                } else {
                    assessorAllocationList = assessorAllocationService.getAssessorListByClaimNo(claimsDto.getClaimNo());
                    AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
                    String[] sysTime = Utility.getSeparateCurruntSysTimeString12hours();
                    assessorAllocationDto.setAccidentTimeFileds(sysTime);
                    assessorAllocationDto.setAssignDatetime(Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                    assessorAllocationDto.setClaimsDto(claimsDto);
                    request.setAttribute("assessorAllocationList", assessorAllocationList);
                    request.setAttribute(AppConstant.IS_DISABLED, AppConstant.NO);
                    request.setAttribute(AppConstant.CLAIM_ID, claimsDto.getClaimNo());
                    request.setAttribute("assessorAllocationDto", assessorAllocationDto);
                    request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());
                    request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
                    request.setAttribute("assessorList", assessorService.getAssessorListByDivisionCode(AppConstant.STRING_EMPTY));
                    request.setAttribute(AppConstant.INSPECTION_LIST, assessorAllocationService.getInspectionList(claimsDto.getClaimNo()));
                    requestDispatcher(request, response, AppUrl.URL_ASSESSORALLOCATION);
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }

    }

    private void populateInitTyreConditionList(MotorEngineerDetailsDto motorEngineerDetailsDto, Map<String, String[]> parameterMap) {
        List<TireCondtionDto> tireCondtionDtoList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            TireCondtionDto tireCondtionDto = new TireCondtionDto();
            tireCondtionDto.setRefNo(motorEngineerDetailsDto.getRefNo());
            tireCondtionDto.setClaimsDto(motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto());
            String rf = null;
            String lf = null;
            String rr = null;
            String rl = null;
            String rri = null;
            String lri = null;
            String other = null;
            if (0 == i) {
                rf = "N/A";
                lf = "N/A";
                rr = "N/A";
                rl = "N/A";
                rri = "N/A";
                lri = "N/A";
                other = "N/A";
            } else {
                rf = "";
                lf = "";
                rr = "";
                rl = "";
                rri = "";
                lri = "";
                other = "";
            }
            tireCondtionDto.setPosition(i);
            tireCondtionDto.setRf(rf);
            tireCondtionDto.setLf(lf);
            tireCondtionDto.setRr(rr);
            tireCondtionDto.setRl(rl);
            tireCondtionDto.setRri(rri);
            tireCondtionDto.setLri(lri);
            tireCondtionDto.setOther(other);
            tireCondtionDtoList.add(tireCondtionDto);
        }
        motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtoList);
    }

    private void reAssignJob(HttpServletRequest request, HttpServletResponse response) {
        List<AssessorAllocationDto> assessorAllocationList = null;
        int refId = Integer.parseInt(request.getParameter("oldJobId"));
        AssessorAllocationDto assessor = null;
        List<CityDto> cityList = null;
        List<AssessorDto> assessorDtoList = null;

        try {

            assessor = assessorAllocationService.search(refId);
            request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());

            if (null != assessor) {
                assessorAllocationList = assessorAllocationService.getAssessorListByClaimNo(assessor.getClaimsDto().getClaimNo());
                assessor.setIsReAssign(AppConstant.YES);
                assessor.setClaimsDto(getSessionClaimDetails(request, response));
            }
            if (null != assessor && null != assessor.getDistrictDto()) {
                cityList = cityService.getCityListByDistrictCode(assessor.getDistrictDto().getDistrictCode());
                assessorDtoList = assessorService.getAssessorListByDivisionCode(assessor.getDistrictDto().getDistrictCode());

            }
            String[] sysTime = Utility.getSeparateCurruntSysTimeString12hours();
            assessor.setAccidentTimeFileds(sysTime);
            assessor.setAssignDatetime(Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            assessor.setPreviousRefId(refId);
            request.setAttribute(AppConstant.INSPECTION_LIST, assessorAllocationService.getInspectionListForReassignScreen(refId));

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

        request.setAttribute("assessorAllocationList", assessorAllocationList);
        request.setAttribute("cityList", cityList);
        request.setAttribute("assessorList", assessorDtoList);
        request.setAttribute("assessorAllocationDto", assessor);
        request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());

        if (null != assessor.getInspectionDto() && assessor.getInspectionDto().getInspectionId() == AppConstant.CALL_ESTIMATE) {
            request.setAttribute(AppConstant.IS_DISABLED, AppConstant.YES);
        }

        requestDispatcher(request, response, AppUrl.URL_ASSESSORALLOCATION);

    }


    private void validateAllocation(HttpServletRequest request, HttpServletResponse response) {
        AssessorAllocationDto assessorAllocation = null;
        List<AssessorAllocationDto> assessorAllocationList = null;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();


        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();

            BeanUtils.populate(assessorAllocationDto, request.getParameterMap());
            ErrorMessageDto errorMessageDto = assessorAllocationValidator.validateAssessorAllocation(assessorAllocationDto);

            json = gson.toJson(errorMessageDto);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }


    }

    private void getOnsiteInspection(HttpServletRequest request, HttpServletResponse response) {
        AssessorAllocationDto assessorAllocation = null;
        List<AssessorAllocationDto> assessorAllocationList = null;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ClaimsDto claimsDto = getSessionClaimDetails(request, response);
        DistrictDto districtDto = null;
        OnSiteInspectionDto onSiteInspectionDto = new OnSiteInspectionDto();

        try {
            ClaimsDto search = callCenterService.search(claimsDto.getClaimNo());
            onSiteInspectionDto.setPlaceOfInspection(search.getPlaceOfAccid());
            if (null != search) {
                districtDto = districtService.searchMasterId(search.getDistrictCode());
                districtDto = districtService.search(districtDto.getDistrictCode());

                if (null != districtDto) {
                    onSiteInspectionDto.setDistrictCode(districtDto.getDistrictCode());
                    onSiteInspectionDto.setDistrictName(districtDto.getDistrictName());
                    CityDto city = cityService.search(search.getNearestCity());
                    if (null != city) {
                        onSiteInspectionDto.setCityCode(city.getGramaCode());
                        onSiteInspectionDto.setCityName(city.getGramaName());

                        onSiteInspectionDto.setAssessorDtos(assessorService.getAssessorListByDivisionCode(districtDto.getDistrictCode()));
                    }
                }
            }

            json = gson.toJson(onSiteInspectionDto);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void getDistrictList(HttpServletRequest request, HttpServletResponse response) {
        AssessorAllocationDto assessorAllocation = null;
        List<AssessorAllocationDto> assessorAllocationList = null;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        List<DistrictDto> list = new ArrayList<>();

        try {
            list = districtService.searchAll();

            json = gson.toJson(list);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

    }

    private void getAssessorMobileNo(HttpServletRequest request, HttpServletResponse response) {

        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        String mobileNo = AppConstant.STRING_EMPTY;
        String assessorCode = request.getParameter("assessorCode");

        try {
            mobileNo = assessorService.getAssessorMobileNo(assessorCode);

            json = gson.toJson(mobileNo);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

    }

    private void getSpecialRemark(HttpServletRequest request, HttpServletResponse response) {

        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        String mobileNo = AppConstant.STRING_EMPTY;
        String remark = request.getParameter("remark");
        ClaimsDto claimsDto = getSessionClaimDetails(request, response);
        try {
            if (null == claimsDto) {
                int claimId = null == request.getParameter("claimId") ? 0 : Integer.parseInt(request.getParameter("claimId"));
                claimsDto = callCenterService.search(claimId);

            }
            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimsDto.getClaimNo());
            specialRemarkDto.setRemark(remark);
            UserDto user = getSessionUser(request);


            ErrorMessageDto errorMessageDto = assessorAllocationService.saveRemark(specialRemarkDto, user);
            if (errorMessageDto != null) {
                json = "Successfully Add Special Remark";
                errorMessageDto.setMessage(json);
            }
            json = gson.toJson(errorMessageDto);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

    }

    private void updateCompledJob(HttpServletRequest request, HttpServletResponse response) {


        ClaimsDto claimsDto = getSessionClaimDetails(request, response);
        UserDto user = getSessionUser(request);
        String successMessage = AppConstant.STRING_EMPTY;
        List<AssessorAllocationDto> assessorAllocationList = null;
        int refId = request.getParameter("oldJobId") == null ? 0 : Integer.parseInt(request.getParameter("oldJobId"));
        AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();

        try {
            boolean isSuccess = assessorAllocationService.updateCompletedJob(refId, user);
            if (isSuccess) {
                successMessage = "Successfully Completed";
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            request.setAttribute(AppConstant.ERROR_MESSAGE, "Record can not be saved");
        } finally {
            try {
                String[] sysTime = Utility.getSeparateCurruntSysTimeString12hours();
                assessorAllocationDto.setAccidentTimeFileds(sysTime);
                assessorAllocationDto.setClaimsDto(getSessionClaimDetails(request, response));
                assessorAllocationList = assessorAllocationService.getAssessorListByClaimNo(claimsDto.getClaimNo());
                request.setAttribute("assessorAllocationList", assessorAllocationList);
                request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, successMessage);
                request.setAttribute(AppConstant.IS_DISABLED, AppConstant.NO);
                request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
                assessorAllocationDto.setAssignDatetime(Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                request.setAttribute("assessorAllocationDto", assessorAllocationDto);
                request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
                request.setAttribute("assessorList", assessorService.getAssessorListByDivisionCode(AppConstant.STRING_EMPTY));
                request.setAttribute(AppConstant.INSPECTION_LIST, assessorAllocationService.getInspectionList(claimsDto.getClaimNo()));
                requestDispatcher(request, response, AppUrl.URL_ASSESSORALLOCATION);
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }


    }

    private void resendSms(HttpServletRequest request, HttpServletResponse response) {
        ClaimsDto claimsDto = getSessionClaimDetails(request, response);

        int jobId = Integer.parseInt(request.getParameter("oldJobId"));
        AssessorAllocationDto assessorAllocation = null;
        List<AssessorAllocationDto> assessorAllocationList = null;
        try {
            assessorAllocation = assessorAllocationService.search(jobId);
            if (null != assessorAllocation) {
                assessorAllocation.setClaimsDto(claimsDto);
            }
            AssessorAllocationDto assessorAllocationDto = assessorAllocationService.reSendMessages(assessorAllocation);

            if (null != assessorAllocationDto) {
                request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully Resend");
            }
            assessorAllocationList = assessorAllocationService.getAssessorListByClaimNo(claimsDto.getClaimNo());
            AssessorAllocationDto assessor = new AssessorAllocationDto();
            assessor.setClaimsDto(getSessionClaimDetails(request, response));
            String[] sysTime = Utility.getSeparateCurruntSysTimeString12hours();
            assessor.setAccidentTimeFileds(sysTime);
            request.setAttribute("assessorAllocationList", assessorAllocationList);
            request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());
            request.setAttribute(AppConstant.IS_DISABLED, AppConstant.NO);
            request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
            request.setAttribute(AppConstant.INSPECTION_LIST, assessorAllocationService.getInspectionList(claimsDto.getClaimNo()));
            assessor.setAssignDatetime(Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            request.setAttribute("assessorAllocationDto", assessor);
            request.setAttribute("assessorList", assessorService.getAssessorListByDivisionCode(AppConstant.STRING_EMPTY));
            requestDispatcher(request, response, AppUrl.URL_ASSESSORALLOCATION);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }


    }


    private void uploadDocumets(HttpServletRequest request, HttpServletResponse response) {
        int claimId = null == request.getParameter("claimNo") ? 0 : Integer.parseInt(request.getParameter("claimNo"));
        try {
            requestDispatcher(request, response, AppUrl.URL_ASSESSORALLOCATION);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }


    }

    private void viewDocumentUpload(HttpServletRequest request, HttpServletResponse response) {
        try {
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            List<ClaimUploadViewDto> claimUploadViewDtoList;
            Integer claimId = request.getParameter(AppConstant.P_N_CLIM_NO) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.P_N_CLIM_NO));
            Integer inspectionType = request.getParameter("inspectionType") == null ? 0 : Integer.parseInt(request.getParameter("inspectionType"));
            if (AppConstant.DESKTOP_INSPECTION == inspectionType) {
                if (historyRecord.equals(AppConstant.YES)) {
                    claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewForDesktopAssesment(claimId);
                    request.setAttribute(AppConstant.HISTORY_CLAIM_UPLOADVIEW_DTO_LIST, claimUploadViewDtoList);
                } else {
                    claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewForDesktopAssesment(claimId);
                    request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
                }
            } else {
                if (historyRecord.equals(AppConstant.YES)) {
                    claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId, AppConstant.DOC_TYPE_ESTIMATE);
                    request.setAttribute(AppConstant.HISTORY_CLAIM_UPLOADVIEW_DTO_LIST, claimUploadViewDtoList);
                } else {
                    claimUploadViewDtoList = claimWiseDocumentService.getClaimUploadViewDtoList(claimId, AppConstant.DOC_TYPE_ESTIMATE);
                    request.setAttribute(AppConstant.SESSION_CLAIM_UPLOAD_VIEW_DTO_LIST, claimUploadViewDtoList);
                }
            }
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            request.setAttribute(AppConstant.INSPECTION_TYPE, inspectionType);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/documentUpload.jsp");
        } catch (NumberFormatException e) {
            LOGGER.error(e.getMessage());
        }
    }


    private void viewDocumentViewer(HttpServletRequest request, HttpServletResponse response) {
        try {
            Integer refNo = Integer.parseInt(request.getParameter("refNo") == null ? AppConstant.ZERO : request.getParameter("refNo"));
            String historyRecord = request.getParameter("PREVIOUS_INSPECTION") == null ? AppConstant.NO : request.getParameter("PREVIOUS_INSPECTION");
            request.setAttribute(AppConstant.CLAIM_DOCUMENT_DTO, claimHandlerService.getClaimDocumentDto(refNo));
            request.setAttribute("refNo", refNo);
            request.setAttribute(AppConstant.PREVIOUS_INSPECTION, historyRecord);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/photocomparison/pdfViewerAll.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


}
