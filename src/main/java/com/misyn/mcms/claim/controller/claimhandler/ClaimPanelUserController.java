package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.ClaimPanelUserService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "ClaimPanelUserController", urlPatterns = "/ClaimPanelUserController/*")
public class ClaimPanelUserController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserController.class);
    private ClaimPanelUserService claimPanelUserService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        claimPanelUserService = getClaimPanelUserServiceBySession(request);
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/save":
                    saveClaimUser(request, response, user);
                    break;
                case "/update":
                    updateClaimUser(request, response);
                    break;
                case "/search":
                    searchClaimUser(request, response);
                    break;
                case "/searchAllUsers":
                    searchAllClaimUser(request, response);
                    break;
                case "/viewUser":
                    setClaimPanelUserPopupListValues(request);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/systemParameter/claimPanelUserView.jsp");
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void saveClaimUser(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, UserDto user) {
        ClaimClaimPanelUserDto claimPanelUserDto = new ClaimClaimPanelUserDto();
        String json;
        Gson gson = new Gson();
        try {
            String userPanelIds = httpServletRequest.getParameter("userPanelIds") == null ? AppConstant.STRING_EMPTY : httpServletRequest.getParameter("userPanelIds");
            BeanUtils.populate(claimPanelUserDto, httpServletRequest.getParameterMap());
            claimPanelUserDto.setInputDateTime(Utility.sysDateTime());
            claimPanelUserDto.setInputUser(user.getUserId());
            boolean isSaved = claimPanelUserService.saveClaimPanelUser(claimPanelUserDto, userPanelIds);

            if (isSaved) {
                json = "SUCCESS";
            } else {
                json = "FAIL";
            }
            json = gson.toJson(json);
            printWriter(httpServletRequest, httpServletResponse, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = "ERROR";
            printWriter(httpServletRequest, httpServletResponse, json);
        }
    }

    private void updateClaimUser(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ClaimClaimPanelUserDto claimClaimPanelUserDto = new ClaimClaimPanelUserDto();

        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(claimClaimPanelUserDto, httpServletRequest.getParameterMap());
            claimPanelUserService.updateClaimPanelUser(claimClaimPanelUserDto);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, httpServletResponse);
            LOGGER.error(e.getMessage());
        }
    }

    private void searchClaimUser(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            setClaimPanelUserPopupListValues(httpServletRequest);
            int id = Integer.parseInt(httpServletRequest.getParameter("id"));
            ClaimClaimPanelUserDto claimPanelUserDto = claimPanelUserService.searchClaimPanelUser(id);
            httpServletRequest.setAttribute("claimUser", claimPanelUserDto);
            json = gson.toJson(claimPanelUserDto);
            PrintWriter out = httpServletResponse.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            //  requestDispatcher(httpServletRequest, httpServletResponse, "/WEB-INF/jsp/claim/systemParameter/claimPanelUserView.jsp");
        }
    }

    private void searchAllClaimUser(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        setClaimPanelUserPopupListValues(request);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            //  this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);


            switch (orderColumnName) {
                case "id":
                    orderColumnName = "t1.N_ID";
                    break;
                case "userId":
                    orderColumnName = "t1.V_USER_ID";
                    break;
                case "userStatus":
                    orderColumnName = "t1.V_USER_STATUS";
                    break;
                case "userPanelName":
                    orderColumnName = "t2.V_PANEL_NAME";
                    break;
                case "inputUser":
                    orderColumnName = "t1.V_INPUT_USER";
                    break;
                case "inputDateTime":
                    orderColumnName = "t1.D_INPUT_DATETIME";
                    break;

            }
            DataGridDto data = claimPanelUserService.getUserDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }
}
