package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.TATDetailDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.TATConfigService;
import com.misyn.mcms.claim.service.impl.TATConfigServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;


/**
 * <AUTHOR>
 */
@WebServlet(name = "TATConfigController", urlPatterns = "/TATConfigController/*")
public class TATConfigController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(TATConfigController.class);
    private final TATConfigService tatConfigService = new TATConfigServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));

        try {
            switch (pathInfo) {
                case "/saveTATDetails":
                case "/updateTATDetails":
                    saveTATDetails(request, response);
                    break;
                case "/getAllTATDetails":
                    getAllTATDetails(request, response);
                    break;
                case "/searchTATDetail":
                    searchTATDetail(request, response);
                    break;
                case "/filterTatDetails":
                    filterTATDetails(request, response);
                    break;
                case "/deleteTATDetail":
                    deleteTATDetail(request, response);
                    break;
                case "/viewTatDetail":
                    viewTat(request, response);
                    break;

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void saveTATDetails(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        Integer tatId = null == request.getParameter("tatId") || request.getParameter("tatId").trim().isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("tatId"));
        String taskName = null == request.getParameter("taskName") || request.getParameter("taskName").trim().isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("taskName");
        String minTime = null == request.getParameter("minTime") || request.getParameter("minTime").trim().isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("minTime");
        String maxTime = null == request.getParameter("maxTime") || request.getParameter("maxTime").trim().isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("maxTime");
        String visibility = null == request.getParameter("visibility") || request.getParameter("visibility").trim().isEmpty() ? AppConstant.EMPTY_STRING : request.getParameter("visibility");

        TATDetailDto tatDetailDto = new TATDetailDto();
        tatDetailDto.setTatId(tatId);
        tatDetailDto.setTaskName(taskName);
        tatDetailDto.setMaxTime(maxTime);
        tatDetailDto.setMinTime(minTime);
        tatDetailDto.setVisibility(visibility);
        UserDto user = getSessionUser(request);

        try {
            json = gson.toJson(tatConfigService.saveTATData(tatDetailDto, user));
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            json = gson.toJson("An Error occurred while saving TAT information");
            printWriter(request, response, json);
        }
    }

    private void viewTat(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        try {
            requestDispatcher(servletRequest, servletResponse, "/WEB-INF/jsp/claim/tatConfiguration/tatConfiguration.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            servletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private void searchTATDetail(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        Gson gson = new Gson();
        String json;
        int tatId = null == servletRequest.getParameter("tatId") || servletRequest.getParameter("tatId").trim().isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(servletRequest.getParameter("tatId"));
        UserDto user = getSessionUser(servletRequest);

        try {
            json = gson.toJson(tatConfigService.searchTATDataById(tatId, user));
            printWriter(servletRequest, servletResponse, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            servletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            json = gson.toJson("Error occurred while retrieving saved TAT details");
            printWriter(servletRequest, servletResponse, json);
        }
    }

    private void deleteTATDetail(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        Gson gson = new Gson();
        String json;
        int tatId = null == servletRequest.getParameter("tatId") || servletRequest.getParameter("tatId").trim().isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(servletRequest.getParameter("tatId"));
        String deleteReason = null == servletRequest.getParameter("deleteReason") || servletRequest.getParameter("deleteReason").trim().isEmpty() ? AppConstant.EMPTY_STRING : servletRequest.getParameter("deleteReason");
        UserDto user = getSessionUser(servletRequest);

        try {
            json = gson.toJson(tatConfigService.deleteTATData(tatId, deleteReason, user));
            printWriter(servletRequest, servletResponse, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            servletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            json = gson.toJson("Error occurred while deleting TAT details");
            printWriter(servletRequest, servletResponse, json);
        }
    }

    private void getAllTATDetails(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        Gson gson = new Gson();
        String json;
        UserDto user = getSessionUser(servletRequest);

        try {
            int start = Integer.parseInt(servletRequest.getParameter("start"));
            int length = Integer.parseInt(servletRequest.getParameter("length"));
            int draw = Integer.parseInt(servletRequest.getParameter("draw"));
            String sortColumn = servletRequest.getParameter("order[0][column]");
            String sortDirection = servletRequest.getParameter("order[0][dir]");
            String searchValue = servletRequest.getParameter("search[value]");

            DataGridDto dataGridDto = tatConfigService.getAllTATData(user, start, length, sortColumn, sortDirection, searchValue);
            dataGridDto.setDraw(draw);

            json = gson.toJson(dataGridDto);
            printWriter(servletRequest, servletResponse, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            servletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            json = gson.toJson("Error occurred while retrieving saved TAT details");
            printWriter(servletRequest, servletResponse, json);
        }
    }

    private void filterTATDetails(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        int tatId = null == request.getParameter("tatId") || request.getParameter("tatId").trim().isEmpty() ? 0 : Integer.parseInt(request.getParameter("tatId"));
        String taskName = null == request.getParameter("taskName") || request.getParameter("taskName").trim().isEmpty() ? "" : request.getParameter("taskName");
        UserDto user = getSessionUser(request);

        try {
            DataGridDto dataGridDto = tatConfigService.filterTATDetails(tatId, taskName, user);
            json = gson.toJson(dataGridDto);
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            json = gson.toJson("Error occurred while retrieving filtered TAT details");
            printWriter(request, response, json);
        }
    }



}
