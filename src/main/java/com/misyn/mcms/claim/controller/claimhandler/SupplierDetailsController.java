package com.misyn.mcms.claim.controller.claimhandler;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.SupplierDetailsService;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "SupplierDetailsController", urlPatterns = "/SupplierDetailsController/*")
public class SupplierDetailsController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierDetailsController.class);
    private SupplierDetailsService supplierDetailsService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        supplierDetailsService = getSupplierDetailsServiceBySession(request);
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/saveSupplierDetails":
                    saveSupplierDetails(request, response, user);
                    break;
                case "/searchSupplierDetails":
                    searchSupplierDetails(request, response);
                    break;
                case "/searchAllsparePart":
                    searchAllSupplierDetails(request, response);
                    break;
                case "/viewSupplierDetails":
                    setSupplyOrderPopupListValues(request, AppConstant.ZERO_INT);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/claimHandler/supplierDetailsDatabase.jsp");
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchAllSupplierDetails(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        String supplerName = request.getParameter(AppConstant.TXT_SUPPLIER_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_SUPPLIER_NAME);
        setClaimPanelUserPopupListValues(request);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("V_SUPPLER_NAME", supplerName, FieldParameterDto.SearchType.Like, parameterList);


            switch (orderColumnName) {
                case "supplierId":
                    orderColumnName = "N_SUPPLIER_ID";
                    break;
                case "supplerName":
                    orderColumnName = "V_SUPPLER_NAME";
                    break;
                case "supplierAddressLine1":
                    orderColumnName = "V_SUPPLIER_ADDRESS_LINE1";
                    break;
                case "supplierAddressLine2":
                    orderColumnName = "V_SUPPLIER_ADDRESS_LINE2";
                    break;
                case "supplierAddressLine3":
                    orderColumnName = "V_SUPPLIER_ADDRESS_LINE3";
                    break;
                case "contactNo":
                    orderColumnName = "V_CONTACT_NO";
                    break;
                case "contactPerson":
                    orderColumnName = "V_CONTACT_PERSON";
                    break;
                case "email":
                    orderColumnName = "V_EMAIL";
                    break;
                case "recordStatus":
                    orderColumnName = "V_RECORD_STATUS";
                    break;
                case "inputUserId":
                    orderColumnName = "V_INPUT_USER_ID";
                    break;

            }
            boolean isSearch = request.getParameter("isSearch") == null
                    ? false
                    : (request.getParameter("isSearch").equalsIgnoreCase("1") ? true : false);
            DataGridDto data = supplierDetailsService.getSupplierDetailsDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, isSearch);

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchSupplierDetails(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            setClaimPanelUserPopupListValues(request);
            int id = Integer.parseInt(request.getParameter("supplierId"));
            SupplierDetailsMasterDto supplierDetailsMasterDto = supplierDetailsService.searchSupplierDetail(id);
            request.setAttribute("supplierDetailsMaster", supplierDetailsMasterDto);
            json = gson.toJson(supplierDetailsMasterDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveSupplierDetails(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        SupplierDetailsMasterDto supplierDetailsMasterDto = new SupplierDetailsMasterDto();
        SupplierDetailsMasterDto supplierDetailsMasterDto1;
        SparePartDatabaseDto sparePartDatabase;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(supplierDetailsMasterDto, request.getParameterMap());
            supplierDetailsMasterDto.setInputUserId(user.getUserId());
            supplierDetailsMasterDto1 = supplierDetailsService.insertSupplierDetail(supplierDetailsMasterDto);

            if (null != supplierDetailsMasterDto1) {
                json = "Saved Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }
}
