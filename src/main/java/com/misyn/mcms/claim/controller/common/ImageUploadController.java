/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.controller.common;

import com.misyn.mcms.admin.fileupload.FileUploadListener;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.ClaimImageDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.enums.FileTypeEnum;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.claim.service.impl.StorageServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.fileupload2.core.DiskFileItem;
import org.apache.commons.fileupload2.core.DiskFileItemFactory;
import org.apache.commons.fileupload2.jakarta.JakartaServletDiskFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;


/**
 * <AUTHOR>
 */
@WebServlet(name = "ImageUploadController", urlPatterns = "/ImageUploadController")
public class ImageUploadController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageUploadController.class);
    private static AtomicLong INDEX = new AtomicLong();


    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        response.setContentType("application/json");
        try (PrintWriter out = response.getWriter()) {
            HttpSession session = request.getSession();
            doImageUpload(session, request, response);

        }
    }

    private void doImageUpload(HttpSession session, HttpServletRequest request, HttpServletResponse response) throws IOException {

        StorageService storageService = (StorageService)
                session.getAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE);
        if (storageService == null) {
            storageService = new StorageServiceImpl();
            session.setAttribute(AppConstant.SESSION_SFTP_DOCUMENT_SERVICE, storageService);
        }

        UserDto user = (UserDto) session.getAttribute(AppConstant.SESSION_USER);
        int documentTypeId = AppConstant.ZERO_INT;
        int claimNo = AppConstant.ZERO_INT;
        int jobRefNo = AppConstant.ZERO_INT;
        String inputUserId = user.getUserId();
        boolean hasUpload = false;
        try {
            FileUploadListener listener = new FileUploadListener(request.getContentLength());
            session.removeAttribute("FILE_UPLOAD_STATS");
            session.setAttribute("FILE_UPLOAD_STATS", listener.getFileUploadStats());
            if (listener.getFileUploadStats().getTotalSize() / (1024 * 1024) > 1000) {
                listener.getFileUploadStats().setCurrentStatus("File Size Exceeded");
                listener.getFileUploadStats().setUploadPresentage("");
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            DiskFileItemFactory factory = DiskFileItemFactory.builder().get();

            //  JakartaServletFileUpload upload = new JakartaServletFileUpload(factory);
            JakartaServletDiskFileUpload upload = new JakartaServletDiskFileUpload(factory);
            List<DiskFileItem> items = upload.parseRequest(request);

            for (DiskFileItem fileItem : items) {
                // FileItem fileItem = (FileItem) item;
                if (fileItem.isFormField()) {
                    switch (fileItem.getFieldName()) {
                        case AppConstant.DOCUMENT_TYPE_ID:
                            fileItem.getString();
                            documentTypeId = Integer.parseInt(fileItem.getString());
                            break;
                        case AppConstant.CLAIM_NO:
                            fileItem.getString();
                            claimNo = Integer.parseInt(fileItem.getString());
                            break;
                        case AppConstant.JOB_REF_NO:
                            fileItem.getString();
                            jobRefNo = Integer.parseInt(fileItem.getString());
                            break;
                    }

                }
            }

            for (DiskFileItem fileItem : items) {

                if (!fileItem.isFormField()) {

                    switch (fileItem.getContentType()) {
                        case "image/png":
                        case "image/jpeg":
                            break;
                        default:
                            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                            response.getWriter().write("{" +
                                    "\"errors\": [\n" +
                                    "    {\n" +
                                    "      \"status\": \"500\",\n" +
                                    "      \"detail\": \"Invalid image type\"\n" +
                                    "    }\n" +
                                    "  ]}");
                            return;

                    }
                    if (!fileItem.getName().trim().equalsIgnoreCase(AppConstant.STRING_EMPTY)) {
                        String fileExtension = fileItem.getName().substring(fileItem.getName().lastIndexOf(AppConstant.STRING_DOT) + 1).toLowerCase();
                        String fileName = Utility.sysDate(AppConstant.DATE_TIME_FORMAT_INT)
                                .concat(String.valueOf(INDEX.incrementAndGet())).concat(AppConstant.STRING_UNDERSCORE_SIGN)
                                .concat(inputUserId)
                                .concat(AppConstant.STRING_DOT)
                                .concat(fileExtension);
                        ClaimImageDto claimImageDto = new ClaimImageDto();
                        claimImageDto.setClaimNo(claimNo);
                        claimImageDto.setJobRefNo(jobRefNo);
                        claimImageDto.setDocumentName(fileName);
                        claimImageDto.setInputStream(fileItem.getInputStream());
                        claimImageDto.setThumbInputStream(fileItem.getInputStream());
                        claimImageDto.setFileExtension(fileExtension);
                        claimImageDto.setDocumentTypeId(documentTypeId);
                        claimImageDto.setInpUser(user.getUserId());
                        claimImageDto.setInpDateTime(Utility.sysDateTime());

                        switch (fileExtension) {
                            case AppConstant.PDF:
                                claimImageDto.setFileTypeEnum(FileTypeEnum.PDF);
                                break;
                            case AppConstant.PNG:
                            case AppConstant.JPEG:
                            case AppConstant.JPG:
                            case AppConstant.GIF:
                                claimImageDto.setFileTypeEnum(FileTypeEnum.IMAGE);
                                break;
                            default:
                                claimImageDto.setFileTypeEnum(FileTypeEnum.DEFAULT);
                        }
                        storageService.uploadImage(claimImageDto, user);
                        hasUpload = true;
                    }

                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (hasUpload) {
                response.setStatus(HttpServletResponse.SC_OK);
                response.getWriter().write("{" +
                        "\"errors\": [\n" +
                        "    {\n" +
                        "      \"status\": \"200\",\n" +
                        "      \"detail\": \"Success\"\n" +
                        "    }\n" +
                        "  ]}");
            } else {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{" +
                        "\"errors\": [\n" +
                        "    {\n" +
                        "      \"status\": \"500\",\n" +
                        "      \"detail\": \"Failed\"\n" +
                        "    }\n" +
                        "  ]}");
            }
        }

    }


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    @Override
    public String getServletInfo() {
        return "Document Upload";
    }


}
