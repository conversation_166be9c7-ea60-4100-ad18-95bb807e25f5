package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.ClaimReserveAdjustmentService;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "ClaimReserveAdjustmentController", urlPatterns = "/ClaimReserveAdjustmentController/*")
public class ClaimReserveAdjustmentController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimReserveAdjustmentController.class);
    private ClaimReserveAdjustmentService claimReserveAdjustmentService = null;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        claimReserveAdjustmentService = getClaimReserveAdjustmentServiceBySession(request);
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/viewReserveAdjustmentUI":
                    viewReserveAdjustmentUI(request, response);
                    break;
                case "/save":
                    saveReserveAdjustment(request, response, user);
                    break;
                case "/searchAll":
                    processReserveAdjustmentList(request, response);
                    break;
                case "/checkActiveAdjustment":
                    checkReserveAdjustmentExists(request, response);
                    break;
                case "/categoryType":
                    getReserveCategories(request, response);
                    break;
                case "/periodType":
                    getReservePeriods(request, response);
                    break;

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewReserveAdjustmentUI(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/reserveAdjustmentConfig/reserveAdjustmentConfig.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }
    private void checkReserveAdjustmentExists(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String periodIdStr = request.getParameter("periodId");
        String categoryIdStr = request.getParameter("categoryId");

        int periodId = Integer.parseInt(periodIdStr);
        int categoryId = Integer.parseInt(categoryIdStr);

        boolean exists = claimReserveAdjustmentService.isActiveRecordExists(periodId, categoryId);

        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        out.print("{\"exists\": " + exists + "}");
        out.flush();
    }

    private void getReserveCategories(HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<ReserveCategoryDto> categories = claimReserveAdjustmentService.getReserveCategories();
        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        out.print(new Gson().toJson(categories));
        out.flush();
    }

    private void getReservePeriods(HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<ReservePeriodDto> periods = claimReserveAdjustmentService.getReservePeriods();
        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        out.print(new Gson().toJson(periods));
        out.flush();
    }

    private void saveReserveAdjustment(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        String json;
        Gson gson = new Gson();
        boolean isSaved = true;
        try {
            Type type = new TypeToken<List<ClaimReserveAdjustmentTypeDto>>() {
            }.getType();
            List<ClaimReserveAdjustmentTypeDto> dtos = gson.fromJson(request.getReader(), type);

            for (ClaimReserveAdjustmentTypeDto dto : dtos) {
                dto.setInputUser(user.getUserId());
                dto.setLastModifiedUser(user.getUserId());
                boolean thisSaved = claimReserveAdjustmentService.saveReserveAdjustment(dto);
                if (!thisSaved) {
                    isSaved = false;
                }
            }

            if (isSaved) {
                json = "SUCCESS";
            } else {
                json = "FAIL";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = "ERROR";
            printWriter(request, response, json);
        }
    }

    private void processReserveAdjustmentList(HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();

        String periodId = request.getParameter("periodId");
        String categoryId = request.getParameter("categoryId");
        String closed = request.getParameter("closed");
        String fromDate = request.getParameter("fromDate") == null ? "" : request.getParameter("fromDate");
        String toDate = request.getParameter("toDate") == null ? "" : request.getParameter("toDate");
        String recordStatus = request.getParameter("recordStatus");
        String inputUser = request.getParameter("inputUser");

        int start = 0;
        int length = 10;
        int draw = 1;
        String startStr = request.getParameter("start");
        if (startStr != null && !startStr.isEmpty()) {
            start = Integer.parseInt(startStr);
        }
        String lengthStr = request.getParameter("length");
        if (lengthStr != null && !lengthStr.isEmpty()) {
            length = Integer.parseInt(lengthStr);
        }
        String drawStr = request.getParameter("draw");
        if (drawStr != null && !drawStr.isEmpty()) {
            draw = Integer.parseInt(drawStr);
        }
        String orderType = request.getParameter("orderType") == null ? "ASC" : request.getParameter("orderType");
        String orderField = request.getParameter("orderField") == null ? "period_id" : request.getParameter("orderField");

        if (periodId != null && !periodId.isEmpty()) {
            this.addFieldParameter("t1.period_id", periodId, FieldParameterDto.SearchType.Equal, parameterList);
        }
        if (categoryId != null && !categoryId.isEmpty()) {
            this.addFieldParameter("t1.category_id", categoryId, FieldParameterDto.SearchType.Equal, parameterList);
        }
        if (closed != null && !closed.isEmpty()) {
            this.addFieldParameter("t1.is_closed", closed, FieldParameterDto.SearchType.Equal, parameterList);
        }
        if (recordStatus != null && !recordStatus.isEmpty()) {
            this.addFieldParameter("t1.record_status", recordStatus, FieldParameterDto.SearchType.Equal, parameterList);
        }
        if (inputUser != null && !inputUser.isEmpty()) {
            this.addFieldParameter("t1.input_user", inputUser, FieldParameterDto.SearchType.Equal, parameterList);
        }

        this.addFieldParameter("t1.record_status", "A", FieldParameterDto.SearchType.Equal, parameterList);

        DataGridDto dataGrid = claimReserveAdjustmentService.getReserveAdjustmentDataGridDto(
                parameterList, draw, start, length, orderType, orderField, fromDate, toDate);

        // Write as JSON
        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        out.print(gson.toJson(dataGrid));
        out.flush();
    }

//    private void searchAllReserveAdjustments(HttpServletRequest request, HttpServletResponse response) {
//        Gson gson = new Gson();
//        String json = AppConstant.STRING_EMPTY;
//        try {
//            List<ReserveAdjustmentDto> adjustments = claimReserveAdjustmentService.searchAll();
//            json = gson.toJson(adjustments);
//            PrintWriter out = response.getWriter();
//            out.print(json);
//        } catch (Exception e) {
//            LOGGER.error(e.getMessage());
//        }
//    }
}
