package com.misyn.mcms.claim.controller.common;

import com.google.gson.Gson;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.HolidayTypeDetailService;
import com.misyn.mcms.claim.service.impl.HolidayTypeDetailServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "HolidayTypeDetailController", urlPatterns = "/HolidayTypeDetailController/*")
public class HolidayTypeDetailController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(HolidayTypeDetailController.class);
    private final HolidayTypeDetailService holidayTypeDetailService = new HolidayTypeDetailServiceImpl();
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/viewHolidayType":
                    viewHolidayType(request, response);
                    break;
                case "/saveHolidayTypeDetail":
                    saveHolidayTypeDetail(request, response, user);
                    break;
                case "/updateHolidayTypeDetail":
                    updateHolidayTypeDetail(request, response, user);
                    break;
                case "/viewHolidayTypeDetail":
                    viewHolidayTypeDetail(request, response);
                    break;
                case "/viewHolidayTypeDetailList":
                    viewHolidayTypeDetailList(request, response);
                    break;
                case "/deleteHolidayTypeDetail":
                    deleteHolidayTypeDetail(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void viewHolidayType(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/holidayTypeConfig/holidayTypeConfig.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void saveHolidayTypeDetail(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        HolidayTypeDto holidayTypeDto = new HolidayTypeDto();
        HolidayTypeDto HolidayTypeDto1;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(holidayTypeDto, request.getParameterMap());
            HolidayTypeDto1 = holidayTypeDetailService.saveHolidayTypeDetail(holidayTypeDto);

            if (null != HolidayTypeDto1) {
                json = "Saved Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void updateHolidayTypeDetail(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        HolidayTypeDto holidayTypeDto = new HolidayTypeDto();
        HolidayTypeDto HolidayTypeDto1;
        String json = AppConstant.STRING_EMPTY;
        Gson gson = new Gson();
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(holidayTypeDto, request.getParameterMap());
            HolidayTypeDto1 = holidayTypeDetailService.updateHolidayTypeDetail(holidayTypeDto);

            if (null != HolidayTypeDto1) {
                json = "Saved Successfully";
            }
            json = gson.toJson(json);
            printWriter(request, response, json);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void viewHolidayTypeDetail(HttpServletRequest request, HttpServletResponse response) {
        Integer holidayTypeId = request.getParameter("holidayTypeId") == null || request.getParameter("holidayTypeId").isEmpty()
                ? 0
                : Integer.valueOf(request.getParameter("holidayTypeId"));

        Gson gson = new Gson();
        try {
            HolidayTypeDto holidayTypeDto = holidayTypeDetailService.getHolidayTypeDetail(holidayTypeId);
            String json = gson.toJson(holidayTypeDto);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void viewHolidayTypeDetailList(HttpServletRequest request, HttpServletResponse response) {

        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;

        Integer holidayTypeId = request.getParameter("holidayTypeId") == null || request.getParameter("holidayTypeId").isEmpty() ? 0 : Integer.parseInt(request.getParameter("holidayTypeId"));
        String holidayTypeName = request.getParameter("holidayTypeName") == null ? AppConstant.STRING_EMPTY : request.getParameter("holidayTypeName");
        String holidayTypeDesc = request.getParameter("holidayTypeDesc") == null ? AppConstant.STRING_EMPTY : request.getParameter("holidayTypeDesc");

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("holiday_type_id", holidayTypeId.equals(0) ? "" : String.valueOf(holidayTypeId), FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("holiday_type_name", holidayTypeName, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("holiday_type_desc", holidayTypeDesc, FieldParameterDto.SearchType.Like, parameterList);


            switch (orderColumnName) {
                case "holidayTypeId":
                    orderColumnName = "holiday_type_id";
                    break;
                case "holidayTypeName":
                    orderColumnName = "holiday_type_name";
                    break;
                case "holidayTypeDesc":
                    orderColumnName = "holiday_type_desc";
                    break;

            }
            boolean isSearch = request.getParameter("isSearch") != null && (request.getParameter("isSearch").equalsIgnoreCase("1"));
            DataGridDto data = holidayTypeDetailService.getHolidayTypeDetailsDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, isSearch);

            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void deleteHolidayTypeDetail(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json;
        try {
            Integer holidayTypeId = request.getParameter("holidayTypeId") == null || request.getParameter("holidayTypeId").isEmpty() ? 0 : Integer.valueOf(request.getParameter("holidayTypeId"));
            String deleteReason = request.getParameter("deleteReason") == null || request.getParameter("deleteReason").isEmpty() ? "" : request.getParameter("deleteReason");
            holidayTypeDetailService.deleteHolidayTypeDetail(holidayTypeId, deleteReason);
            json = gson.toJson("SUCCESS");
            printWriter(request, response, json);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }
}
