package com.misyn.mcms.claim.controller.common;

import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.NotificationFormDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.NotificationService;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

@WebServlet(name = "NotificationController", urlPatterns = "/NotificationController/*")
public class NotificationController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationController.class);
    private NotificationService notificationService = null;


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        UserDto user = getSessionUser(request);
        String pathInfo = request.getPathInfo();

        try {
            switch (pathInfo) {
                case "/viewNotification":
                    viewNotification(request, response, user);
                    break;
                case "/viewAllNotification":
                    viewAllNotification(request, response, user);
                    break;
                case "/updateNotification":
                    updateNotification(request, response);
                    break;
                case "/updateNotificationchecked":
                    updateNotificationchecked(request, response);
                    break;
                case "/deleteCheckedNotification":
                    deleteCheckedNotification(request, response);
                    break;
                case "/closeNotification":
                    closeNotification(request, response);
                    break;
                case "/closeSidePanelNotification":
                    closeSidePanelNotification(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void closeSidePanelNotification(HttpServletRequest request, HttpServletResponse response) {
        Integer txtId = Integer.parseInt(request.getParameter("P_N_NOTIFICATION_REF_NO") == null ? AppConstant.ZERO : request.getParameter("P_N_NOTIFICATION_REF_NO"));
        String status = AppConstant.STRING_EMPTY;
        try {
            if (status == "") {
                status = "C";
            }
            notificationService = getNotificationServiceBySession(request);
            notificationService.deleteCheckedNotification(txtId, status);
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private void closeNotification(HttpServletRequest request, HttpServletResponse response) {
        Integer txtId = Integer.parseInt(request.getParameter("P_N_NOTIFICATION_REF_NO") == null ? AppConstant.ZERO : request.getParameter("P_N_NOTIFICATION_REF_NO"));
        String status = AppConstant.STRING_EMPTY;
        try {
            if (status == "") {
                status = "C";
            }
            notificationService = getNotificationServiceBySession(request);
            notificationService.updateNotificationChecked(txtId, status);
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }


    private void viewNotification(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        HttpSession session = request.getSession();
        try {
            notificationService = getNotificationServiceBySession(request);
            NotificationFormDto notificationFormDto = notificationService.getNotificationFormDto(30, user);
            request.setAttribute(AppConstant.NOTIFICATION_FORM_DTO, notificationFormDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/common/notificationViewer.jsp");
    }

    private void viewAllNotification(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        HttpSession session = request.getSession();

        try {
            String fromDate = request.getParameter("txtFromDate") == null ? AppConstant.STRING_EMPTY : request.getParameter("txtFromDate");
            String toDate = request.getParameter("txtToDate") == null ? AppConstant.STRING_EMPTY : request.getParameter("txtToDate");
            String vehicleNo = request.getParameter("vehicleNo") == null ? AppConstant.STRING_EMPTY : request.getParameter("vehicleNo");
            String claimNo = request.getParameter("claimNo") == null ? AppConstant.STRING_EMPTY : request.getParameter("claimNo");
            notificationService = getNotificationServiceBySession(request);
            NotificationFormDto notificationFormDto = notificationService.getNotificationFormDto(user, fromDate, toDate, vehicleNo, claimNo);

            request.setAttribute(AppConstant.NOTIFICATION_FORM_DTO, notificationFormDto);
            request.setAttribute("fromDate", fromDate);
            request.setAttribute("toDate", toDate);
            request.setAttribute("vehicleNo", vehicleNo);
            request.setAttribute("claimNo", claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        requestDispatcher(request, response, "/WEB-INF/jsp/claim/common/notificationAllViewer.jsp");
    }

    private void updateNotification(HttpServletRequest request, HttpServletResponse response) {
        Integer txtId = Integer.parseInt(request.getParameter("P_N_NOTIFICATION_REF_NO") == null ? AppConstant.ZERO : request.getParameter("P_N_NOTIFICATION_REF_NO"));
        String status = AppConstant.STRING_EMPTY;
        if (status == "") {
            status = "Y";
        }
        try {
            notificationService = getNotificationServiceBySession(request);
            notificationService.updateNotification(txtId, status);
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

    }

    private void updateNotificationchecked(HttpServletRequest request, HttpServletResponse response) {
        Integer txtId = Integer.parseInt(request.getParameter("P_N_NOTIFICATION_REF_NO") == null ? AppConstant.ZERO : request.getParameter("P_N_NOTIFICATION_REF_NO"));
        String status = AppConstant.STRING_EMPTY;
        try {
            if (status == "") {
                status = "Y";
            }
            notificationService.updateNotificationChecked(txtId, status);
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }


    private void deleteCheckedNotification(HttpServletRequest request, HttpServletResponse response) {
        Integer txtId = Integer.parseInt(request.getParameter("P_N_NOTIFICATION_REF_NO") == null ? AppConstant.ZERO : request.getParameter("P_N_NOTIFICATION_REF_NO"));
        String status = AppConstant.STRING_EMPTY;
        try {
            if (status == "") {
                status = "Y";
            }
            notificationService.deleteCheckedNotification(txtId, status);
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

}
