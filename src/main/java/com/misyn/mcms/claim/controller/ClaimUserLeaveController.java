package com.misyn.mcms.claim.controller;

import com.google.gson.Gson;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.ClaimUserLeaveService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name = "ClaimUserLeaveController", urlPatterns = "/ClaimUserLeaveController/*")
public class ClaimUserLeaveController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveController.class);
    private ClaimUserLeaveService claimUserLeaveService = null;
    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        claimUserLeaveService = getClaimUserLeaveServiceBySession(request);
        UserDto user = getSessionUser(request);
        try {
            switch (pathInfo) {
                case "/save":
                    saveClaimUserLeave(request, response, user);
                    break;
                case "/update":
                    updateClaimUserLeave(request, response);
                    break;
                case "/search":
                    searchClaimUserLeave(request, response);
                    break;
                case "/searchAllLeave":
                    searchAllClaimUserLeave(request, response);
                    break;
                case "/viewLeave":
//                    setClaimUserLeavePopupListValues(request);
                    request.setAttribute(AppConstant.TYPE, null == request.getParameter(AppConstant.TYPE) ? 3 : request.getParameter(AppConstant.TYPE));
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/systemParameter/claimUserLeaveView.jsp");
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchAllClaimUserLeave(HttpServletRequest request, HttpServletResponse response) {
        Integer type = null == request.getParameter("type") ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("type"));
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String userID = request.getParameter(AppConstant.TXT_USER_ID) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_USER_ID);
        String leaveType = request.getParameter(AppConstant.TXT_LEAVE_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LEAVE_TYPE);
        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("v_usrid", userID, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_LEAVE_TYPE", leaveType, FieldParameterDto.SearchType.Like, parameterList);


            switch (orderColumnName) {
                case "index":
                    orderColumnName = "";
                    break;
                case "userId":
                    orderColumnName = "V_USER_ID";
                    break;
                case "fromDateTime":
                    orderColumnName = "D_FROM_DATE_TIME";
                    break;
                case "toDateTime":
                    orderColumnName = "D_TO_DATE_TIME";
                    break;
                case "leaveType":
                    orderColumnName = "V_LEAVE_TYPE";
                    break;
                case "inputUser":
                    orderColumnName = "V_INPUT_USER";
                    break;
                case "inputDateTime":
                    orderColumnName = "D_INPUT_DATE_TIME";
                    break;
                case "firstName":
                    orderColumnName = "t1.v_firstname";
                    break;
                case "lastName":
                    orderColumnName = "t1.v_lastname";
                    break;
            }
            DataGridDto data = claimUserLeaveService.getUserDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate, type);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void searchClaimUserLeave(HttpServletRequest request, HttpServletResponse response) {
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            setClaimPanelUserPopupListValues(request);
            String id = request.getParameter("userId");
            ClaimUserLeaveDto claimUserLeaveDto = claimUserLeaveService.searchClaimUserLeave(id);
            request.setAttribute("claimUserLeave", claimUserLeaveDto);
            json = gson.toJson(claimUserLeaveDto);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            //  requestDispatcher(httpServletRequest, httpServletResponse, "/WEB-INF/jsp/claim/systemParameter/claimPanelUserView.jsp");
        }
    }

    private void updateClaimUserLeave(HttpServletRequest request, HttpServletResponse response) {
        ClaimUserLeaveDto claimUserLeaveDto = new ClaimUserLeaveDto();

        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        try {
            BeanUtils.populate(claimUserLeaveDto, request.getParameterMap());
            claimUserLeaveService.updateClaimUserLeave(claimUserLeaveDto);

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }

    private void saveClaimUserLeave(HttpServletRequest request, HttpServletResponse response, UserDto user) {
        ClaimUserLeaveDto claimUserLeaveDto = new ClaimUserLeaveDto();
        ClaimUserLeaveDto claimUserLeave;
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        try {
            String userPanelIds = request.getParameter("userId") == null ? AppConstant.STRING_EMPTY : request.getParameter("userId");
            claimUserLeaveDto = claimUserLeaveService.searchClaimUserLeave(userPanelIds);

            if (null != claimUserLeaveDto.getLeaveType() && !AppConstant.STRING_EMPTY.equals(claimUserLeaveDto.getLeaveType())) {
                BeanUtils.populate(claimUserLeaveDto, request.getParameterMap());
                claimUserLeaveDto.setInputDateTime(Utility.sysDateTime());
                claimUserLeaveDto.setInputUser(user.getUserId());
                claimUserLeave = claimUserLeaveService.updateClaimUserLeave(claimUserLeaveDto);
                if (null != claimUserLeave) {
                    json = ("Successfully Update");
                }
                json = gson.toJson(json);
                printWriter(request, response, json);
            } else {
                BeanUtils.populate(claimUserLeaveDto, request.getParameterMap());
                claimUserLeaveDto.setInputDateTime(Utility.sysDateTime());
                claimUserLeaveDto.setInputUser(user.getUserId());
                claimUserLeave = claimUserLeaveService.saveClaimUserLeave(claimUserLeaveDto);
                if (null != claimUserLeave) {
                    json = ("Successfully Saved");
                }
                json = gson.toJson(json);
                printWriter(request, response, json);
            }

        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }
}
