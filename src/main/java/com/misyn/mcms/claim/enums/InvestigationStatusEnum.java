package com.misyn.mcms.claim.enums;

/**
 * Created by a<PERSON><PERSON> on 5/15/18.
 */
public enum InvestigationStatusEnum {
    CLAIM_HANDLER_REQUEST_INVESTIGATION("CH_REQ_INVEST"),
    DECISION_MAKER_REQUEST_INVESTIGATION("DM_REQ_INVEST"),
    INVESTIGATION_APPROVED("INVEST_APPROVED"),
    ARRANGE("AR"),
    COMPLETED("C"),
    CANCEL("CAN"),
    PENDING("P"),
    DEFAULT("N");
    private final String investigationStatus;

    InvestigationStatusEnum(String investigationStatus) {
        this.investigationStatus = investigationStatus;
    }

    public String getInvestigationStatus() {
        return investigationStatus;
    }
}
