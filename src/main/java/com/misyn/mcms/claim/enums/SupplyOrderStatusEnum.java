package com.misyn.mcms.claim.enums;

public enum SupplyOrderStatusEnum {
    PENDING("P"),
    REJECT("R"),
    FORWARD_SCRUTINIZING_TEAM("SCRUTINIZING-F"),
    APPROVED_SCRUTINIZING_TEAM("SCRUTINIZING-A"),
    FORWARD_CLAIM_HANDLER("CH-F"),
    APPROVED_CLAIM_HANDLER("CH-A"),
    FORWARD_RTE("RTE-F"),
    APPROVED_RTE("RTE-A"),
    GENERATE("G"),
    UPDATE("U"),
    FORWARD_SPARE_PARTS_COORDINATOR("SPC-F"),
    APPROVED_SPARE_PARTS_COORDINATOR("SPC-A"),
    RECALLED_SPARE_PARTS_COORDINATOR("SPC-R"),
    RECALLED_SCRUTINIZING_TEAM("SCRUTINIZING-R"),
    APPROVED_UPDATE("A");

    private final String supplyOrderStatusEnum;


    SupplyOrderStatusEnum(String supplyOrderStatusEnum) {
        this.supplyOrderStatusEnum = supplyOrderStatusEnum;
    }

    public String getSupplyOrderStatusEnum() {
        return supplyOrderStatusEnum;
    }
}
