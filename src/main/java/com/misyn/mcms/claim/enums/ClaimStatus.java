/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.enums;

/**
 * <AUTHOR>
 */
public enum ClaimStatus {

    ASSIGNED(3),
    REASSIGNED(4),
    CANCEL(6),
    ATTENDED(7),
    SUBMITTED(8),
    APPROVED(9),
    CLAIM_CHANGE_REQUESTED(10),
    SETTLED(11),
    VIEWED(12),
    CLAIM_VIEWED(13),
    INSPECTION_CHANGE_REQUESTED(14),
    CALLCENTER_PENDING(17),
    REJECTED(23),
    ACCEPTED(24),
    NO_RESPONSE(25),
    DRAFT(26),
    PENDING(27),
    COMPLETED(28),
    JOB_ASSIGNED(29),
    CLAIM_HANDLER_SPECIAL_COMMENT(57),
    CALCULATION_VERIFY_PENDING(58),
    FORWARDED_TO_THE_PAYMENT_APPROVAL_SPECIAL_TEAM_FOR_PAYMENT_APPROVAL(63),
    PAYMENT_APPROVED(65),
    PAYMENT_REJECTED(66),
    FORWARDED_TO_THE_MOTOR_ENGINEERING_TEAM_FOR_RESERVE_AMOUNT_APPROVAL(71),
    SAVE_AS_DRAFT(72),
    PAYMENT_CANCELLED(73),
    INSPECTION_FORWARDED(80),
    TASK_FORWARDED_TO_ENGINEER(83);


    private final int claimStatus;

    private ClaimStatus(int authStatus) {
        this.claimStatus = authStatus;
    }

    public int getClaimStatus() {
        return claimStatus;
    }


}
