package com.misyn.mcms.claim.enums;

public enum PolicyStatusEnum {
    POLICY_LAPSED("LAP"),
    POLICY_SUSPENDED("SUS"),
    POLICY_EXPIRED("EXP"),
    POLICY_CANCELLED("CAN"),
    PROPOSAL("PRP"),
    INFORCE("INF"),
    REJECTED("REJ");


    private String policyStatus;

    PolicyStatusEnum(String policyStatus) {
        this.policyStatus = policyStatus;
    }

    public String getPolicyStatus() {
        return policyStatus;
    }
}
