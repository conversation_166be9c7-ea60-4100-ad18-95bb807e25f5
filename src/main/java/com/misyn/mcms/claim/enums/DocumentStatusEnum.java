/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.enums;

/**
 * <AUTHOR>
 */
public enum DocumentStatusEnum {
    DEFAULT(""),
    PENDING("P"),
    CHECKED("A"),
    HOLD("H"),
    REJECTED("R"),
    NO_VALID_DOCUMENT("N"),
    CANCELLED("C");

    private String documentStatus;

    private DocumentStatusEnum(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    public String getDocumentStatus() {
        return documentStatus;
    }

    public void setResponseStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }
}
