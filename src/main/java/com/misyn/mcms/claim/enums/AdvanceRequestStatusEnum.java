package com.misyn.mcms.claim.enums;

/**
 * <AUTHOR>
 */
public enum AdvanceRequestStatusEnum {
    PENDING("P"),
    FORWARD_TO_SPARE_PARTS_COORDINATOR("SPC-F"),
    SPARE_PARTS_COORDINATOR_APPROVED("SPC-A"),
    FORWARD_TO_SCRUTINIZING_TEAM("SCR-F"),
    SCRUTINIZING_TEAM_APPROVED("SCR-A"),
    REJECTED("R");

    private final String advanceRequestStatusEnum;

    AdvanceRequestStatusEnum(String advanceRequestStatusEnum) {
        this.advanceRequestStatusEnum = advanceRequestStatusEnum;
    }

    public String getAdvanceRequestStatusEnum() {
        return advanceRequestStatusEnum;
    }
}
