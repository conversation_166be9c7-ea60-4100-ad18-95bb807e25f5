package com.misyn.mcms.claim.filter;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.RandomStringUtils;

import java.io.IOException;
import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON>lum on 6/23/2017.
 */

//@Priority(5)
//@WebFilter(filterName = "LoadCsrfFilter", urlPatterns = {"/*"})

public class LoadCsrfFilter implements Filter {
    public void destroy() {
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        // Assume its HTTP
        HttpServletRequest httpReq = (HttpServletRequest) request;
        // Check the user session for the salt cache, if none is present we create one
        Cache<String, Boolean> csrfPreventionSaltCache = getCsrfPreventionSaltCache(httpReq);

        // Generate the salt and store it in the users cache
        String salt = RandomStringUtils.random(20, 0, 0, true, true, null, new SecureRandom());
        csrfPreventionSaltCache.put(salt, Boolean.TRUE);

        // Add the salt to the current request so it can be used
        // by the page rendered in this request
        httpReq.setAttribute(AppConstant.CSRF_TOKEN, salt);
        chain.doFilter(request, response);
    }

    private static Cache<String, Boolean> getCsrfPreventionSaltCache(HttpServletRequest httpReq) {
        Cache<String, Boolean> csrfPreventionSaltCache = (Cache<String, Boolean>)
                httpReq.getSession().getAttribute("csrfPreventionSaltCache");

        if (csrfPreventionSaltCache == null) {
            csrfPreventionSaltCache = CacheBuilder.newBuilder()
                    .maximumSize(10000)
                    .expireAfterWrite(120, TimeUnit.MINUTES)
                    .build();
            httpReq.getSession().setAttribute("csrfPreventionSaltCache", csrfPreventionSaltCache);
        }
        return csrfPreventionSaltCache;
    }

    public void init(FilterConfig config) throws ServletException {

    }

}
