package com.misyn.mcms.claim.filter;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.misyn.mcms.claim.dto.JWTClaimDto;
import com.misyn.mcms.claim.redis.RedisService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.JwtUtil;
import com.misyn.mcms.utility.Parameters;
import jakarta.annotation.Priority;
import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Priority(10)
@WebFilter(filterName = "LoginValidateFilter", urlPatterns = {"/*"})
public class LoginValidateFilter implements Filter {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginValidateFilter.class);
    private static final String AUTH_LOGOUT_URL = Parameters.getAuthServiceLogoutUrl();

    private static void logout(HttpServletRequest req, HttpServletResponse res) throws IOException {
        req.getSession(true).invalidate();
        res.sendRedirect(AUTH_LOGOUT_URL);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException {

        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;


        HttpSession session = req.getSession();
        String uri = req.getRequestURI();
        String token = (String) session.getAttribute("token");

        if (uri.endsWith("auth")) {
            if (token == null) {
                session.invalidate();
            }
        } else {
            if (token == null) {
                logout(req, res);
                return;
            } else {
                JWTClaimDto jwtClaimDto = JwtUtil.decodeJwt(token);
                if (jwtClaimDto == null) {
                    logout(req, res);
                    return;
                } else {
                    Map<String, String> cachedData = RedisService.getHCachedDataAsMap(jwtClaimDto.getUsername());
                    if (cachedData == null || !token.equals(cachedData.get("token"))) {
                        logout(req, res);
                        return;
                    }else {
                        Cache<String, Boolean> csrfPreventionSaltCache = getCsrfPreventionSaltCache(req);
                    }
                }
            }
        }
        try {
            chain.doFilter(request, response);
        } catch (Throwable t) {
            LOGGER.error(t.getMessage(), t);
        }
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }



    private static Cache<String, Boolean> getCsrfPreventionSaltCache(HttpServletRequest httpReq) {
        @SuppressWarnings("unchecked")
        Cache<String, Boolean> csrfPreventionSaltCache = (Cache<String, Boolean>)
                httpReq.getSession().getAttribute(AppConstant.CSRF_PREVENTION_SALT_CACHE);

        if (csrfPreventionSaltCache == null) {
            csrfPreventionSaltCache = CacheBuilder.newBuilder()
                    .maximumSize(10000)
                    .expireAfterWrite(120, TimeUnit.MINUTES)
                    .build();
            httpReq.getSession().setAttribute(AppConstant.CSRF_PREVENTION_SALT_CACHE, csrfPreventionSaltCache);
        }
        return csrfPreventionSaltCache;
    }


}
