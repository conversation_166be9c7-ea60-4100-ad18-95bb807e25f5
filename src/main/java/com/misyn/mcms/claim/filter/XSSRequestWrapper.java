/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.filter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import org.owasp.encoder.Encode;

import java.util.regex.Pattern;


public class XSSRequestWrapper extends HttpServletRequestWrapper {

    private static Pattern[] patterns = new Pattern[]{

            // Script fragments
            Pattern.compile("<script>(.*?)</script>", Pattern.CASE_INSENSITIVE),
            // src='...'
            Pattern.compile("src[\r\n]*=[\r\n]*\\\'(.*?)\\\'", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL),
            Pattern.compile("src[\r\n]*=[\r\n]*\\\"(.*?)\\\"", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL),
            // lonely script tags
            Pattern.compile("</script>", Pattern.CASE_INSENSITIVE),
            Pattern.compile("<script(.*?)>", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL),
            // eval(...)
            Pattern.compile("eval\\((.*?)\\)", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL),
            // expression(...)
            Pattern.compile("expression\\((.*?)\\)", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL),
            // javascript:...
            Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
            // svg:...
            Pattern.compile("<svg", Pattern.CASE_INSENSITIVE),
            Pattern.compile("alert", Pattern.CASE_INSENSITIVE),


            // vbscript:...
            Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE),
            // onload(...)=...
            Pattern.compile("onload(.*?)=", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL)
    };

    public XSSRequestWrapper(HttpServletRequest servletRequest) {
        super(servletRequest);
    }

    @Override
    public String[] getParameterValues(String parameter) {
        String[] values = super.getParameterValues(parameter);

        if (values == null) {
            return null;
        }

        int count = values.length;
        String[] encodedValues = new String[count];
        for (int i = 0; i < count; i++) {
            encodedValues[i] = stripXSS(values[i]);
        }

        return encodedValues;
    }

    @Override
    public String getParameter(String parameter) {
        String result = null;
        String value = super.getParameter(parameter);
        switch (parameter) {
            case "imoMnuIds":
                result = stripXSS(value);
                break;
            case "password":
            case "NewPassword":
            case "ReNewPassword":
            case "curruntPasswordField":
            case "passwordField":
            case "passwordFieldConfirm":
            case "txtPassword":
            case "txtConfirmPassword":
            case "remark":
            case "selectPayeeDesc":
                result = value;
                break;
            case "custName":
                result = value.replace("&amp;", "&");
                break;
            default:
                if (value != null) {
                    result = Encode.forHtmlAttribute(value);
                }
                // result = Encode.forHtmlAttribute(value); //stripXSS(value);
                // result =Encode.forJavaScriptAttribute(value);
                //result = stripXSS(value);
        }
        return result;
    }

    @Override
    public String getHeader(String name) {
        String value = super.getHeader(name);
        return stripXSS(value);
    }

    private String stripXSS(String value) {
        if (value != null) {
            // NOTE: It's highly recommended to use the ESAPI library and uncomment the following line to
            // avoid encoded attacks.
            // value = ESAPI.encoder().canonicalize(value);

            // Avoid null characters
            value = value.replaceAll("\0", "");

            // Remove all sections that match a pattern
            for (Pattern scriptPattern : patterns) {
                value = scriptPattern.matcher(value).replaceAll("");
            }
            //  Pattern scriptPattern = Pattern.compile("\"", Pattern.CASE_INSENSITIVE);
            //  value = scriptPattern.matcher(value).replaceAll("&quot;");
            //  scriptPattern = Pattern.compile("\'", Pattern.CASE_INSENSITIVE);
            //   value = scriptPattern.matcher(value).replaceAll("&apos;");
            //   scriptPattern = Pattern.compile("<", Pattern.CASE_INSENSITIVE);
            //    value = scriptPattern.matcher(value).replaceAll("&lt;");
            //    scriptPattern = Pattern.compile(">", Pattern.CASE_INSENSITIVE);
            //    value = scriptPattern.matcher(value).replaceAll("&gt;");
        }
        return value;
    }
}
