package com.misyn.mcms.claim.filter;


import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


//@WebFilter(filterName = "ClickjackingFilter", urlPatterns = {"/*"})
public class ClickjackingFilter implements Filter {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClickjackingFilter.class);

    private static final boolean debug = true;

    private FilterConfig filterConfig = null;

    public ClickjackingFilter() {
    }


    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;

        res.setHeader("X-XSS-Protection", "1; mode=block");
        res.setHeader("Strict-Transport-Security", "max-age=7776000; includeSubdomains");
        res.addHeader("X-Content-Type-Options", "nosniff");
        res.addHeader("X-FRAME-OPTIONS", "SAMEORIGIN");

        String sessionid = req.getSession().getId();
        //  res.setHeader("SET-COOKIE", "JSESSIONID=" + sessionid + "; secure");
        String secure = "";
        if (request.isSecure()) {
            secure = "; Secure";
        }
        res.setHeader("SET-COOKIE", "JSESSIONID=" + sessionid
                + "; Path=" + req.getContextPath() + "; HttpOnly" + secure);
        res.setHeader("Content-Security-Policy", " script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-eval' 'unsafe-inline'");
        // res.setHeader("Content-Security-Policy", "default-src 'self' 'unsafe-inline' 'unsafe-eval' data:;");


        try {
            chain.doFilter(request, response);
        } catch (Throwable t) {
            LOGGER.error(t.getMessage(), t);
        }

    }


    public FilterConfig getFilterConfig() {
        return (this.filterConfig);
    }


    public void setFilterConfig(FilterConfig filterConfig) {
        this.filterConfig = filterConfig;
    }


    public void destroy() {
    }


    public void init(FilterConfig filterConfig) {
        this.filterConfig = filterConfig;
        if (filterConfig != null) {

        }
    }

    /**
     * Return a String representation of this object.
     */
    @Override
    public String toString() {
        if (filterConfig == null) {
            return ("ClickjackingFilter()");
        }
        StringBuffer sb = new StringBuffer("ClickjackingFilter(");
        sb.append(filterConfig);
        sb.append(")");
        return (sb.toString());
    }




}
