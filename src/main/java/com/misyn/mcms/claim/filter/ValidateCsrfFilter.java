package com.misyn.mcms.claim.filter;

import com.google.common.cache.Cache;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Created by <PERSON><PERSON> on 6/23/2017.
 */

//@Priority(2)
//@WebFilter(filterName = "ValidateCsrfFilter", urlPatterns = {"/*"})

public class ValidateCsrfFilter implements Filter {
    private static final Logger LOGGER = LoggerFactory.getLogger(ValidateCsrfFilter.class);
    private static final String LOGIN_JSP_URL = "/Login.jsp";

    public void destroy() {
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {

        // Assume its HTTP
        HttpServletRequest httpReq = (HttpServletRequest) request;
        HttpServletResponse httpRes = (HttpServletResponse) response;
        String uri = httpReq.getRequestURI();
        String salt = (String) httpReq.getParameter(AppConstant.CSRF_TOKEN);
        //set csrfPreventionSalt value by session attribute
        //  LOGGER.info("URI : " + uri);

        @SuppressWarnings("unchecked")
        Cache<String, Boolean> csrfPreventionSaltCache = (Cache<String, Boolean>) httpReq.getSession().getAttribute(AppConstant.CSRF_PREVENTION_SALT_CACHE);

        // exclude URL

        if (uri.equalsIgnoreCase(httpReq.getContextPath().concat(AppConstant.STRING_BACKSLASH_SIGN))) {

        } else if (uri.equalsIgnoreCase(httpReq.getContextPath())) {

        } else {
            if (httpReq.getMethod().equalsIgnoreCase("POST")) {
                if (uri.endsWith(httpReq.getContextPath().concat(AppConstant.STRING_BACKSLASH_SIGN).concat(AppConstant.LOGIN_DO))) {
                    if (csrfPreventionSaltCache != null &&
                            salt != null &&
                            csrfPreventionSaltCache.getIfPresent(salt) == null) {
                        //  LOGGER.error("CRSF Invlaid URL :" + uri);
                        //    httpRes.sendError(HttpServletResponse.SC_FORBIDDEN);
                        httpRes.sendRedirect(httpReq.getContextPath() + LOGIN_JSP_URL);
                        return;
                    }
                }
            }
        }


        Throwable problem = null;
        try {
            chain.doFilter(request, response);
        } catch (Throwable t) {
            problem = t;
            t.printStackTrace();
        }


    }

    public void init(FilterConfig config) throws ServletException {

    }

}
