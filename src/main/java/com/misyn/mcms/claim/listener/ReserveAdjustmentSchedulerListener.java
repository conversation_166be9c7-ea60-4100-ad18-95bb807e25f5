package com.misyn.mcms.claim.listener;

import com.misyn.mcms.claim.scheduler.ReserveAdjustmentScheduler;
import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ReserveAdjustmentSchedulerListener implements ServletContextListener {

    private ScheduledExecutorService scheduler;

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        scheduler = Executors.newSingleThreadScheduledExecutor();

        long initialDelay = computeInitialDelay();

        scheduler.scheduleAtFixedRate(
                new ReserveAdjustmentScheduler(),
                initialDelay,
                TimeUnit.DAYS.toSeconds(1), // repeat every 24 hours
                TimeUnit.SECONDS
        );
    }

    private long computeInitialDelay() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextRun = now.with(LocalTime.of(0, 1)); // today at 12:01 AM

        if (now.isAfter(nextRun)) {
            nextRun = nextRun.plusDays(1); // schedule for tomorrow
        }

        Duration duration = Duration.between(now, nextRun);
        return duration.getSeconds();
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        if (scheduler != null) {
            scheduler.shutdownNow();
        }
    }
}
