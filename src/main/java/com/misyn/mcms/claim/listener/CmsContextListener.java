/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.listener;

import com.misyn.mcms.claim.job.JobParameter;
import com.misyn.mcms.claim.job.impl.CallCenterJobImpl;
import com.misyn.mcms.utility.Parameters;
import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
public class CmsContextListener implements ServletContextListener {

    private final ScheduledExecutorService EXECUTOR = Executors.newScheduledThreadPool(10);
    private final int CALL_JOB_INITIAL_DELAY = 5;
    private final int CALL_JOB_PERIOD = 60;

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        Parameters.getInstance();
        sce.getServletContext().setAttribute("CompanyTitle", Parameters.getCompanyTitle());
        JobParameter.setFollowUpJobStart(Boolean.TRUE);
        EXECUTOR.scheduleAtFixedRate(new CallCenterJobImpl(), CALL_JOB_INITIAL_DELAY, CALL_JOB_PERIOD, TimeUnit.SECONDS);
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        JobParameter.setFollowUpJobStart(Boolean.FALSE);
        EXECUTOR.shutdown();
    }
}
