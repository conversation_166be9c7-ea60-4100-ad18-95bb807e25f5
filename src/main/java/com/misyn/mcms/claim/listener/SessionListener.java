/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.listener;


import com.misyn.mcms.admin.LoginHashMap;
import com.misyn.mcms.claim.dto.ClaimLockDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.ClaimLockService;
import com.misyn.mcms.claim.service.impl.ClaimLockServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.http.HttpSessionEvent;
import jakarta.servlet.http.HttpSessionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
public class SessionListener implements HttpSessionListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(SessionListener.class);
    private ClaimLockService claimLockService = null;

    @Override
    public void sessionCreated(HttpSessionEvent se) {
        LOGGER.info(se.getSession().getId());
        ClaimLockService claimLockService = (ClaimLockService) se.getSession().getAttribute(AppConstant.CLAIM_LOCK_SERVICE);
        if (null == claimLockService) {
            claimLockService = new ClaimLockServiceImpl();
            se.getSession().setAttribute(AppConstant.CLAIM_LOCK_SERVICE, claimLockService);
        }
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent se) {
        String sessionId = se.getSession().getId();
        LoginHashMap.loginUserMap.remove(sessionId);

        UserDto user = (UserDto) se.getSession().getAttribute(AppConstant.SESSION_USER);
        ClaimLockDto claimLockDto = (ClaimLockDto) se.getSession().getAttribute(AppConstant.SESSION_CLAIM_LOCK_DTO);
        if (claimLockDto != null && user != null) {
            if (null == claimLockService) {
                claimLockService = new ClaimLockServiceImpl();
            }
            claimLockService.unLockIntimation(claimLockDto, user.getUserId());
        }
        LOGGER.info(se.getSession().getId());
        se.getSession().invalidate();
    }


}
