package com.misyn.mcms.claim.exception;

public class WrongValueException extends Exception {
    private final String field;
    private final String errorMessage;

    public WrongValueException() {
        super();
        this.field = "";
        this.errorMessage = "";
    }

    public WrongValueException(String field, String errorMessage) {
        super(errorMessage);
        this.field = field;
        this.errorMessage = errorMessage;
    }

    public String getField() {
        return field;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
