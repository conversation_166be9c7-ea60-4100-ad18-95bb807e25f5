/*
 * < �KURA, This application manages the daily activities of a Teacher and a Student of a School>
 *
 * Copyright (C) 2012 Virtusa Corporation.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

package com.misyn.mcms.claim.exception;

/**
 * This represents a customized exception class for application specific exceptions.
 *
 * <AUTHOR> Corporation
 */

public class MisynAppException extends MisynException {

    /**
     * Class serial id.
     */
    private static final long serialVersionUID = -1004771925657146922L;

    /**
     * Constructs a default SMSAppException object.
     */
    public MisynAppException() {

        super();
    }

    /**
     * Constructs a SMSAppException object with the given error code.
     *
     * @param strErrorCode - The error code.
     */
    public MisynAppException(String strErrorCode) {

        super(strErrorCode);
    }


    public MisynAppException(String strErrorMsg, Throwable cause) {

        super(strErrorMsg, cause);
    }

    /**
     * Constructs a SMSAppException object with the given error message, error code and detailed cause.
     *
     * @param strErrorMsg  - The error message.
     * @param strErrorCode - the error code.
     * @param cause        - the cause of the error.
     */
    public MisynAppException(String strErrorMsg, String strErrorCode, Throwable cause) {

        super(strErrorMsg, strErrorCode, cause);
    }

}
