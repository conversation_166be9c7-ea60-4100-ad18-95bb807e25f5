package com.misyn.mcms.claim.exception;


public class MisynJ<PERSON><PERSON><PERSON>x<PERSON> extends MisynException {

    private static final long serialVersionUID = -1004771925657146922L;

    public MisynJDBCException() {
        super();
    }


    public MisynJDBCException(String strErrorCode) {
        super(strErrorCode);
    }

    public MisynJDBCException(Throwable cause) {
        super(cause);
    }


    public MisynJDBCException(String strErrorMsg, Throwable cause) {
        super(strErrorMsg, cause);
    }


    public MisynJDBCException(String strErrorMsg, String strErrorCode, Throwable cause) {
        super(strErrorMsg, strErrorCode, cause);
    }

}
