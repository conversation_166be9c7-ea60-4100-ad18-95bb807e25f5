package com.misyn.mcms.claim.exception;


public abstract class MisynException extends RuntimeException {

    /**
     * Class serial id.
     */
    private static final long serialVersionUID = -230773364309409472L;


    private String errorCode;


    private ErrorMsgLoader errorMsgLoader = new ErrorMsgLoader();


    public MisynException() {
        super();
    }

    public MisynException(Throwable cause) {
        super(cause);
    }

    public MisynException(String errCode) {
        super(errCode);
        this.errorCode = errCode;
    }


    public MisynException(String strErrorMsg, String errCode, Throwable cause) {
        super(strErrorMsg, cause);
        this.errorCode = errCode;
    }


    public MisynException(String strErrorMsg, Throwable cause) {
        super(strErrorMsg, cause);
    }

    public String getErrorCode() {

        return errorCode;
    }

    public void setErrorCode(String errCode) {

        this.errorCode = errCode;
    }


    public String getLocalizedMessage() {

        if (this.errorCode == null || this.errorCode.isEmpty()) {
            return getMessage();
        }

        return errorMsgLoader.getErrorMessage(errorCode);

    }

}
