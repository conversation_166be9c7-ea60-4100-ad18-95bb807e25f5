package com.misyn.mcms.claim.exception;

public class ErrorMsgException extends Exception {
    private final String field;
    private final String errorMessage;


    public ErrorMsgException() {
        super();
        this.field = "";
        this.errorMessage = "";
    }

    public ErrorMsgException(String field, String errorMessage) {
        super(errorMessage);
        this.field = field;
        this.errorMessage = errorMessage;
    }

    public String getField() {
        return field;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
