package com.misyn.mcms.claim.exception;


public class MisynSftpFileException extends MisynException {

    private static final long serialVersionUID = -1004771925657146922L;

    public MisynSftpFileException() {
        super();
    }


    public MisynSftpFileException(String strErrorCode) {
        super(strErrorCode);
    }

    public MisynSftpFileException(Throwable cause) {
        super(cause);
    }


    public MisynSftpFileException(String strErrorMsg, Throwable cause) {
        super(strErrorMsg, cause);
    }


    public MisynSftpFileException(String strErrorMsg, String strErrorCode, Throwable cause) {
        super(strErrorMsg, strErrorCode, cause);
    }

}
