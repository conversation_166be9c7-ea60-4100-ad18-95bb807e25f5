package com.misyn.mcms.claim.exception;

public class MisynRecordNotFoundException extends MisynJDBCException {
    public MisynRecordNotFoundException() {
    }

    public MisynRecordNotFoundException(Throwable cause) {
        super(cause);
    }

    public MisynRecordNotFoundException(String errCode) {
        super(errCode);
    }

    public MisynRecordNotFoundException(String strErrorMsg, String errCode, Throwable cause) {
        super(strErrorMsg, errCode, cause);
    }

    public MisynRecordNotFoundException(String strErrorMsg, Throwable cause) {
        super(strErrorMsg, cause);
    }
}
