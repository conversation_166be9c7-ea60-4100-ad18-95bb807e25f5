package com.misyn.mcms.claim.exception;

public class UserNotFoundException extends Exception {
    private final String field;
    private final String errorMessage;

    public UserNotFoundException() {
        super();
        this.field = "";
        this.errorMessage = "";
    }

    public UserNotFoundException(String field, String errorMessage) {
        super(errorMessage);
        this.field = field;
        this.errorMessage = errorMessage;
    }

    public String getField() {
        return field;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
