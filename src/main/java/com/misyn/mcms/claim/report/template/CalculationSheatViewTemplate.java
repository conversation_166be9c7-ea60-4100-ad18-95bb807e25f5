package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetDetailDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
public class CalculationSheatViewTemplate extends AbstractPdfTemplate implements PdfTemplate<ClaimCalculationSheetMainDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CalculationSheatViewTemplate.class);

    public ByteArrayOutputStream getGenerateLetter(ClaimCalculationSheetMainDto claimCalculationSheetMainDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;

        try {
            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();
            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
            this.setHeaderAndFooter(document, table1, user);

            Font f = new Font();
            f.setColor(BaseColor.WHITE);
            PdfPCell cell = new PdfPCell(new Phrase(" ", f));
            PdfPCell cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            PdfPCell cellEmpty1 = new PdfPCell();
            cellEmpty1.setBorder(PdfPCell.NO_BORDER);
            PdfPTable table2 = new PdfPTable(new float[]{180, 350, 80});
            table2.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table3 = new PdfPTable(new float[]{30, 130, 90, 100, 100, 110});
            table3.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table6 = new PdfPTable(new float[]{30, 130, 90, 100, 100, 110});
            table6.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table4 = new PdfPTable(new float[]{700, 250, 50, 450, 250});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table5 = new PdfPTable(new float[]{100});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table7 = new PdfPTable(new float[]{300, 500});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table8 = new PdfPTable(new float[]{500});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table9 = new PdfPTable(new float[]{500});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);


            Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
            f1.setColor(BaseColor.BLACK);
            cell.setBorder(PdfPCell.NO_BORDER);
            cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cell);
            table1.addCell(cell);
//            table1.addCell(cell);
//            table1.addCell(cell);
            f1 = new Font(Font.FontFamily.TIMES_ROMAN, 11, Font.NORMAL);
            f1.setColor(BaseColor.BLACK);

            if (65 == claimCalculationSheetMainDto.getStatus()) {
                table2.addCell(createCell(Utility.getDateValue(Utility.getDate(claimCalculationSheetMainDto.getAprDateTime(), AppConstant.DATE_TIME_FORMAT)), Element.ALIGN_LEFT, 11, Font.NORMAL));
            } else if (67 == claimCalculationSheetMainDto.getStatus()) {
                table2.addCell(createCell(Utility.getDateValue(Utility.getDate(claimCalculationSheetMainDto.getVoucherGeneratedDateTime(), AppConstant.DATE_TIME_FORMAT)), Element.ALIGN_LEFT, 11, Font.NORMAL));
            } else {
                table2.addCell(createCell(Utility.getDateValue(Utility.getDate(claimCalculationSheetMainDto.getInputDatetime(), AppConstant.DATE_TIME_FORMAT)), Element.ALIGN_LEFT, 11, Font.NORMAL));
            }
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("VEHICLE NO : " + claimCalculationSheetMainDto.getClaimHandlerDto().getClaimsDto().getVehicleNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cell);
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(createCell("Claim NO : " + String.valueOf(claimCalculationSheetMainDto.getClaimNo()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cell);
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(createCell("ISF Claim NO : " + claimCalculationSheetMainDto.getClaimHandlerDto().getClaimsDto().getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cell);
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));


            cellEmpty = new PdfPCell(table2);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table7.addCell(cell);
            table7.addCell(createCell("CALCULATION SHEET", Element.ALIGN_LEFT, 14, Font.UNDERLINE));
            cellEmpty = new PdfPCell(table7);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table9.addCell(createCell("LABOUR", Element.ALIGN_LEFT, 11, Font.BOLD));

            cellEmpty = new PdfPCell(table9);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);


            table3.addCell(createCell("NO", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("APPROVED AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("O/A AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("NBT AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("VAT AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("TOTAL AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            int index = 0;
            for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto : claimCalculationSheetMainDto.getClaimCalculationSheetDetailLabourDtos()) {
                int no = ++index;
                table3.addCell(createCell(String.valueOf(no), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table3.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDto.getApprovedAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table3.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDto.getOaAmount()) + "  (" + claimCalculationSheetDetailDto.getOa() + "%)", Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                if ("IGNORE".equals(claimCalculationSheetDetailDto.getNbtType())) {
                    table3.addCell(createCell("0.00", Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                } else {
                    table3.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDto.getNbtAmount()) + ("  (" + claimCalculationSheetDetailDto.getNbtRate() + "%)"), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                }
                if ("IGNORE".equals(claimCalculationSheetDetailDto.getVatType())) {
                    table3.addCell(createCell("0.00", Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                } else {
                    table3.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDto.getVatAmount()) + ("  (" + claimCalculationSheetDetailDto.getVatRate() + "%)"), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                }
                table3.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDto.getTotalAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            }

            table1.addCell(cell);


            cellEmpty = new PdfPCell(table3);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);


            table8.addCell(createCell("REPLACEMENTS", Element.ALIGN_LEFT, 11, Font.BOLD));

            cellEmpty = new PdfPCell(table8);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);

            table6.addCell(createCell("NO", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table6.addCell(createCell("APPROVED AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table6.addCell(createCell("O/A AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table6.addCell(createCell("NBT AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table6.addCell(createCell("VAT AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table6.addCell(createCell("TOTAL AMOUNT (Rs)", Element.ALIGN_LEFT, 9, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            index = 0;
            for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailDtos : claimCalculationSheetMainDto.getClaimCalculationSheetDetailReplacementDtos()) {
                int no = ++index;
                table6.addCell(createCell(String.valueOf(no), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table6.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDtos.getApprovedAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table6.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDtos.getOaAmount()) + String.valueOf("  (" + claimCalculationSheetDetailDtos.getOa() + "%)"), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                if ("IGNORE".equals(claimCalculationSheetDetailDtos.getNbtType())) {
                    table6.addCell(createCell("0.00", Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                } else {
                    table6.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDtos.getNbtAmount()) + ("  (" + claimCalculationSheetDetailDtos.getNbtRate() + "%)"), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                }
                if ("IGNORE".equals(claimCalculationSheetDetailDtos.getVatType())) {
                    table6.addCell(createCell("0.00", Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                } else {
                    table6.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDtos.getVatAmount()) + ("  (" + claimCalculationSheetDetailDtos.getVatRate() + "%)"), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

                }
                table6.addCell(createCell(Utility.formatCurrency(claimCalculationSheetDetailDtos.getTotalAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            }

            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);
            table6.addCell(cell);

            cellEmpty = new PdfPCell(table6);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table4.addCell(createCell("Name", Element.ALIGN_LEFT, 11, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell("Amount (Rs.)", Element.ALIGN_LEFT, 11, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);

            for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos()) {
                table4.addCell(createCell(claimCalculationSheetPayeeDto.getPayeeDesc(), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetPayeeDto.getAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table4.addCell(cell);
                table4.addCell(cell);
                table4.addCell(cell);
            }

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell(" SUMMARY", Element.ALIGN_LEFT, 11, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(" Rs", Element.ALIGN_RIGHT, 11, Font.BOLD)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("LABOUR", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getLabour()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("REPLACEMENTS", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getParts()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("TOTAL", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getTotalLabourAndParts()), Element.ALIGN_RIGHT, 11, Font.UNDERLINE)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            if ("Y".equals(claimCalculationSheetMainDto.getIsExcessInclude())) {
                table4.addCell(cell);
                table4.addCell(cell);
                table4.addCell(cell);
                table4.addCell(createCell("EXCESS", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
                table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getPolicyExcess()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            }

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("UNDER INSURANCE " + claimCalculationSheetMainDto.getUnderInsuranceRate() + "%", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getUnderInsurance()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("BALD TYRE  " + claimCalculationSheetMainDto.getBaldTyreRate() + "%", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getBaldTyre()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("TOTAL", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getTotalAfterDeductions()), Element.ALIGN_RIGHT, 11, Font.UNDERLINE)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("FINAL ACR", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getClaimHandlerDto().getAprvTotAcrAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("PAID ADVANCE", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getPaidAdvanceAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(createCell("PAYABLE  AMOUNT   ", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell(Utility.formatCurrency(claimCalculationSheetMainDto.getPayableAmount()), Element.ALIGN_RIGHT, 11, Font.UNDERLINE)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);

            Date date = new Date();
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd 'at' hh:mm:ss ");
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);
            table4.addCell(cell);

            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table5.addCell(createCell("Print Date : " + ft.format(date), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table5.addCell(createCell("Prepared by ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table5.addCell(createCell("..................................", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table5);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);


            table1.addCell(cell);
            document.add(table1);
            //End Document

            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            this.setFooter(table, f1);
            table.addCell(cell);
            FooterTable event = new FooterTable(table);
            docWriter.setPageEvent(event);


        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error(e.getMessage());
        } finally {
            if (document != null) {
                document.close();
            }
            if (docWriter != null) {
                docWriter.close();
            }
        }
        return baosPdf;
    }


}
