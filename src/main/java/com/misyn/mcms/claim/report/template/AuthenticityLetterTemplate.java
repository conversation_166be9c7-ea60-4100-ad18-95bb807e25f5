package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.ClaimHandlerDto;
import com.misyn.mcms.claim.dto.UserDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
public class AuthenticityLetterTemplate extends AbstractPdfTemplate implements PdfTemplate<ClaimHandlerDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthenticityLetterTemplate.class);


    public ByteArrayOutputStream getGenerateLetter(ClaimHandlerDto claimHandlerDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;

        try {
            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();
            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
            this.setHeaderAndFooter(document, table1, user);

            Font f = new Font();
            f.setColor(BaseColor.WHITE);
            PdfPCell cell = new PdfPCell(new Phrase(" ", f));
            PdfPCell cellEmpty = new PdfPCell();
            PdfPCell cellLine = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            PdfPCell cellEmpty1 = new PdfPCell();
            cellEmpty1.setBorder(PdfPCell.NO_BORDER);
            PdfPTable table2 = new PdfPTable(new float[]{180, 350, 80});
            table2.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table3 = new PdfPTable(new float[]{200, 350, 80});
            table3.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table4 = new PdfPTable(new float[]{500});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table5 = new PdfPTable(new float[]{100});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);

            Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
            f1.setColor(BaseColor.BLACK);
            cell.setBorder(PdfPCell.NO_BORDER);
            cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);

            f1 = new Font(Font.FontFamily.TIMES_ROMAN, 11, Font.NORMAL);
            f1.setColor(BaseColor.BLACK);
            table2.addCell(createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustName(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustAddressLine1(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustAddressLine2(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getCustAddressLine3(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(cell);
            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Dear All,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(cell);
            table2.addCell(cell);
            table2.addCell(cell);

            cellEmpty = new PdfPCell(table2);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table3.addCell(createCell("VEHICLE NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimHandlerDto.getClaimsDto().getPolicyDto().getVehicleNumber(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("CLAIM NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(String.valueOf(claimHandlerDto.getClaimsDto().getClaimNo()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("ISF CLAIM NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimHandlerDto.getClaimsDto().getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("POLICY NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(String.valueOf(claimHandlerDto.getClaimsDto().getPolicyNumber()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("DATE OF LOSS", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimHandlerDto.getClaimsDto().getAccidDate(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table3);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);
            cellLine.setBorder(PdfPCell.BOX+Rectangle.BOTTOM);
            table4.addCell(cellLine);
            table4.addCell(cell);
            table4.addCell(createCell("This is with reference to the claim that was intimated under the captioned policy.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            table4.addCell(createCell("Your claim file has received our careful consideration & according to the facts given by you it has been revealed that the information submitted by you is untrue & misrepresented.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            table4.addCell(createCell("Therefore, due to this reason, we regret to inform you that we are not in a position to entertain the subject claim and the subject claim file will be closed accordingly.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            table4.addCell(createCell("Yours faithfully  ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(createCell("MI Synergy PVT LTD ", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);
            table1.addCell(cell);

//            table5.addCell(createCell("Authorized Officer - Motor Claims", Element.ALIGN_LEFT, 11, Font.NORMAL));
//            table5.addCell(createCell(claimHandlerDto.getRepudiatedLetterPrintUserName() + " - LOLC Insurance PLC.", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table5);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
//            table1.addCell(cell);
//            table1.addCell(cell);
//            table1.addCell(cell);
//            table1.addCell(cell);
//            table1.addCell(cell);


            table1.addCell(cell);
//            this.setFooter(table1, cell, f1);
            document.add(table1);
            //End Document

            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            this.setFooter(table, f1);
            table.addCell(cell);
            FooterTable event = new FooterTable(table);
            docWriter.setPageEvent(event);


        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error(e.getMessage());
        } finally {
            if (document != null) {
                document.close();
            }
            if (docWriter != null) {
                docWriter.close();
            }
        }
        return baosPdf;
    }


}
