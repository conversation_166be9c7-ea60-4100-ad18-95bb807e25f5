package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
public class ReleaseLatterTemplate extends AbstractPdfTemplate implements PdfTemplate<ClaimCalculationSheetMainDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReleaseLatterTemplate.class);


    public ByteArrayOutputStream getGenerateLetter(ClaimCalculationSheetMainDto claimCalculationSheetMainDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;

        try {
            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();
            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
            this.setHeaderAndFooter(document, table1, user);

            Font f = new Font();
            f.setColor(BaseColor.WHITE);
            PdfPCell cell = new PdfPCell(new Phrase(" ", f));
            PdfPCell cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            PdfPCell cellEmpty1 = new PdfPCell();
            cellEmpty1.setBorder(PdfPCell.NO_BORDER);
            PdfPTable table2 = new PdfPTable(new float[]{180, 350, 80});
            table2.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table3 = new PdfPTable(new float[]{130, 350, 80});
            table3.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table4 = new PdfPTable(new float[]{500});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table5 = new PdfPTable(new float[]{100});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);

            Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
            f1.setColor(BaseColor.BLACK);
            cell.setBorder(PdfPCell.NO_BORDER);
            cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            f1 = new Font(Font.FontFamily.TIMES_ROMAN, 11, Font.NORMAL);
            f1.setColor(BaseColor.BLACK);

            table2.addCell(createCell(Utility.sysDate(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(cell);
            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("The Manager,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("" + claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDto().getPayeeDesc(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell(claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDto().getBranch(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

//            table2.addCell(createCell("Pepiliyana", Element.ALIGN_LEFT, 11, Font.NORMAL));
//            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
//            table2.addCell(cellEmpty);

            table2.addCell(cell);
            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Dear Sir,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);


            cellEmpty = new PdfPCell(table2);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table3.addCell(createCell("VEHICLE NO :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimCalculationSheetMainDto.getClaimHandlerDto().getClaimsDto().getVehicleNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("CLAIM NO :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(String.valueOf(claimCalculationSheetMainDto.getClaimHandlerDto().getClaimsDto().getClaimNo()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("ISF CLAIM NO :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimCalculationSheetMainDto.getClaimHandlerDto().getClaimsDto().getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("POLICY NO :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimCalculationSheetMainDto.getClaimHandlerDto().getClaimsDto().getPolicyNumber(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("NAME OF INSURED :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimCalculationSheetMainDto.getClaimHandlerDto().getClaimsDto().getPolicyDto().getCustName(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table3);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table4.addCell(createCell("This has reference to the insurance claim of above vehicle.", Element.ALIGN_LEFT, 14, Font.UNDERLINE));
            table4.addCell(cell);
            table4.addCell(createCell("We have covered the above numbered vehicle under a comprehensive insurance policy and hence please be good enough to release the vehicle to the above named insured on completion of the repairs. In terms of the policy a sum of Rs. " + Utility.formatCurrency(claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDto().getAmount()) + "  will be paid being full and final settlement of the subject claim.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            table4.addCell(createCell("Accordingly we wish to confirm the payment of Rs. " + Utility.formatCurrency(claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDto().getAmount()) + "  within 07 working days from the receipt of your Original Tax invoice which should be addressed to MI Synergy PVT LTD.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            table4.addCell(createCell("Kindly note that any excess amount if any, should be borne by the insured.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            table4.addCell(createCell("Your kind co-operation will be highly appreciated.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            table4.addCell(createCell("Yours Faithfully,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(createCell("MI Synergy PVT LTD,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(cell);
            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);


            table5.addCell(createCell("...............................", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table5.addCell(createCell("Manager - Motor claims", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table5);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);


            table1.addCell(cell);
//            this.setFooter(table1, cell, f1);
            document.add(table1);
            //End Document

            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            this.setFooter(table, f1);
            table.addCell(cell);
            FooterTable event = new FooterTable(table);
            docWriter.setPageEvent(event);

        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error(e.getMessage());
        } finally {
            if (document != null) {
                document.close();
            }
            if (docWriter != null) {
                docWriter.close();
            }
        }
        return baosPdf;
    }


}
