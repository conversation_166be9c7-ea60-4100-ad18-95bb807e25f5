package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.ClaimImageDto;
import com.misyn.mcms.claim.dto.InvestigationDetailsFormDto;
import com.misyn.mcms.claim.dto.InvestigationSelectImageDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.claim.service.impl.StorageServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;

public class InvestigationTemplate extends AbstractPdfTemplate implements PdfTemplate<InvestigationDetailsFormDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvestigationTemplate.class);
    private final StorageService storageService = new StorageServiceImpl();

    private static void close(Closeable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        }
    }

    @Override
    public ByteArrayOutputStream getGenerateLetter(InvestigationDetailsFormDto investigationDetailsFormDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;

        try {
            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();
            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
            this.setHeaderAndFooter(document, table1, user);

            Font f = new Font();
            f.setColor(BaseColor.WHITE);
            PdfPCell cell = new PdfPCell(new Phrase(" ", f));
            cell.setFixedHeight(11f);
            PdfPCell cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            PdfPCell cellEmpty1 = new PdfPCell();
            cellEmpty1.setBorder(PdfPCell.NO_BORDER);
            PdfPTable table2 = new PdfPTable(new float[]{110, 350});
            table2.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table3 = new PdfPTable(new float[]{100, 150, 150});
            table3.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table4 = new PdfPTable(new float[]{110, 350});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table5 = new PdfPTable(new float[]{200, 200});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table6 = new PdfPTable(new float[]{200});
            table6.setHorizontalAlignment(Element.ALIGN_LEFT);

            Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
            f1.setColor(BaseColor.BLACK);
            cell.setBorder(PdfPCell.NO_BORDER);
            cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cell);
            table1.addCell(cell);

            f1 = new Font(Font.FontFamily.TIMES_ROMAN, 11, Font.NORMAL);
            f1.setColor(BaseColor.BLACK);

            table2.addCell(cell);
            table2.addCell(cell);
            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Vehicle No : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getVehicleNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Insured Name : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getCustName(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Insured Address : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getCustAddressLine1(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getCustAddressLine2(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getCustAddressLine3(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Insured Contact No : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getCustMobileNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Policy Period : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getInspecDate() + (investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getCoverNoteNo() != investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyNumber() ? " to " + investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getExpireDate() : AppConstant.STRING_EMPTY), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Claim No : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getClaimNo().toString(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("ISF Claim No : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Lease Company and Branch : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPolicyDto().getFinanceCompany(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Driver Name : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getDriverName(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Date/Time of Accident : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getAccidDate() + investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getAccidTime(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Place of Accident : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationClaimHandlerDto().getClaimsDto().getPlaceOfAccid(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Inspection assessors name and contact No : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            if (investigationDetailsFormDto.getAssessorDtoList().isEmpty()) {
                table2.addCell(cell);
            } else {
                for (int i = 0; i < investigationDetailsFormDto.getAssessorDtoList().size(); i++) {
                    if (i == 0) {
                        table2.addCell(createCell("\n" + investigationDetailsFormDto.getAssessorDtoList().get(i).getFirstName() + " " + investigationDetailsFormDto.getAssessorDtoList().get(i).getLastName() + " - " + investigationDetailsFormDto.getAssessorDtoList().get(i).getAssessorContactNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                    } else {
                        table2.addCell(cell);
                        table2.addCell(createCell(investigationDetailsFormDto.getAssessorDtoList().get(i).getFirstName() + " " + investigationDetailsFormDto.getAssessorDtoList().get(i).getLastName() + " - " + investigationDetailsFormDto.getAssessorDtoList().get(i).getAssessorContactNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                    }
                }
            }

            table2.addCell(cell);
            table2.addCell(cell);

            String typeOfLoss = AppConstant.STRING_EMPTY;
            if (investigationDetailsFormDto.getInvestigationDetailsDto().getIsAccident().equalsIgnoreCase(AppConstant.YES)) {
                typeOfLoss = "Accident";
            } else if (investigationDetailsFormDto.getInvestigationDetailsDto().getIsTheft().equalsIgnoreCase(AppConstant.YES)) {
                typeOfLoss = "Theft";
            } else if (investigationDetailsFormDto.getInvestigationDetailsDto().getIsFire().equalsIgnoreCase(AppConstant.YES)) {
                typeOfLoss = "Fire";
            }

            table2.addCell(createCell("Type of Loss : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(typeOfLoss, Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Reasons for Investigation : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            int investigationReasonCheckCount = 0;
            for (int i = 0; i < investigationDetailsFormDto.getInvestigationDetailsDto().getInvestigationReasonDetailsDtoList().size(); i++) {
                if (investigationDetailsFormDto.getInvestigationDetailsDto().getInvestigationReasonDetailsDtoList().get(i).getIsCheck().equalsIgnoreCase(AppConstant.YES)) {
                    if (investigationReasonCheckCount == 0) {
                        table2.addCell(createCell(investigationDetailsFormDto.getInvestigationDetailsDto().getInvestigationReasonDetailsDtoList().get(i).getInvesReason(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                    } else {
                        table2.addCell(cell);
                        table2.addCell(createCell(investigationDetailsFormDto.getInvestigationDetailsDto().getInvestigationReasonDetailsDtoList().get(i).getInvesReason(), Element.ALIGN_LEFT, 11, Font.NORMAL));
                    }
                    ++investigationReasonCheckCount;
                }

            }
            if (investigationReasonCheckCount == 0) {
                table2.addCell(cell);
            }

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Reason : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationDetailsDto().getReason(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Investigation Assigned to  : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationDetailsDto().getAssignInvestigatorName(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Assigned User & Date/Time : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(investigationDetailsFormDto.getInvestigationDetailsDto().getInvestArrangeUser() + " " + investigationDetailsFormDto.getInvestigationDetailsDto().getInvestArrangeDateTime(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table2.addCell(createCell("Completed User & Date/Time : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(null != investigationDetailsFormDto.getInvestigationDetailsDto().getInvestCompletedUser() ? investigationDetailsFormDto.getInvestigationDetailsDto().getInvestCompletedUser() : AppConstant.STRING_EMPTY + " " + null != investigationDetailsFormDto.getInvestigationDetailsDto().getInvestCompletedDateTime() ? investigationDetailsFormDto.getInvestigationDetailsDto().getInvestCompletedDateTime() : AppConstant.STRING_EMPTY, Element.ALIGN_LEFT, 11, Font.NORMAL));

            table2.addCell(cell);
            table2.addCell(cell);

            table3.addCell(createCell("Investigator Accepting the Assignment : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("................................................", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("................................................", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table4.addCell(cell);
            table4.addCell(cell);


            table4.addCell(createCell("Important : ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table4.addCell(createCell("If Audio / Video evidence is part of the investigation report, an affidavit confirming the authenticity of the recording has to be provided by the investigator along with the recording", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table4.addCell(cell);
            table4.addCell(cell);

            table5.addCell(createCell("Date/Time : " + investigationDetailsFormDto.getInvestigationDetailsDto().getInvestArrangeDateTime(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table5.addCell(createCell("Job No : " + investigationDetailsFormDto.getInvestigationDetailsDto().getInvestJobNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            for (InvestigationSelectImageDto investigationSelectImageDto : investigationDetailsFormDto.getInvestigationSelectImageDtoList()) {
                PdfPCell imageCell = new PdfPCell();
                imageCell.addElement(this.getImage(investigationSelectImageDto.getImageRefNo()));
//                imageCell.setBorder(PdfPCell.NO_BORDER);
                table6.addCell(imageCell);
                table6.addCell(cell);
            }

            cellEmpty = new PdfPCell(table2);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            cellEmpty = new PdfPCell(table3);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            cellEmpty = new PdfPCell(table5);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            cellEmpty = new PdfPCell(table6);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            document.add(table1);
            //End Document

            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            this.setFooter(table, f1);
            table.addCell(cell);
            FooterTable event = new FooterTable(table);
            docWriter.setPageEvent(event);


        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error(e.getMessage());
        } finally {
            document.close();
            if (docWriter != null) {
                docWriter.close();
            }
        }
        return baosPdf;
    }

    private Image getImage(Integer refNo) {

        InputStream is = null;
        ByteArrayOutputStream outputStream = null;
        Image image = null;
        try {
            ClaimImageDto claimImageDto = storageService.getClaimImageDto(refNo);
            if (claimImageDto != null) {
                is = storageService.viewUploadImage(refNo);
                byte[] buffer = new byte[1024];
                outputStream = new ByteArrayOutputStream();
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                image = Image.getInstance(outputStream.toByteArray());
                image.scaleAbsolute(530f, 300f);
//                image.setWidthPercentage(100f);
                image.setScaleToFitHeight(true);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            close(is);
            close(outputStream);

        }
        return image;
    }
}


