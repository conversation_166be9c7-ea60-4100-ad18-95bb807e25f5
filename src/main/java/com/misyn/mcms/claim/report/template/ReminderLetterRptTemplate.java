package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.ReminderPrintDetailsDto;
import com.misyn.mcms.claim.dto.ReminderPrintSummaryDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.RestPolicyDetailsService;
import com.misyn.mcms.claim.service.impl.RestPolicyDetailsServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
public class ReminderLetterRptTemplate extends AbstractPdfTemplate implements PdfTemplate<ReminderPrintSummaryDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReminderLetterRptTemplate.class);
    private RestPolicyDetailsService restPolicyDetailsService = new RestPolicyDetailsServiceImpl();


    public ByteArrayOutputStream getGenerateLetter(ReminderPrintSummaryDto reminderPrintSummaryDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;
        ClaimsDto claimsDto = reminderPrintSummaryDto.getClaimsDto();

        try {
            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();
            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
            this.setHeaderAndFooter(document, table1, user);

            Font f = new Font();
            f.setColor(BaseColor.WHITE);
            PdfPCell cell = new PdfPCell(new Phrase(" ", f));
            PdfPCell cellEmpty = new PdfPCell();
            PdfPCell cellLine = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            cell.setBorder(PdfPCell.NO_BORDER);
            Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
            f1.setColor(BaseColor.BLACK);

            PdfPTable table2 = new PdfPTable(new float[]{500});
            table2.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table3 = new PdfPTable(new float[]{100, 300});
            table3.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table4 = new PdfPTable(new float[]{700});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table5 = new PdfPTable(new float[]{50, 500});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table7 = new PdfPTable(new float[]{500});
            table7.setHorizontalAlignment(Element.ALIGN_LEFT);

            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);

            table2.addCell(createCell("Subject: Pending Documents - Vehicle No: ".concat(claimsDto.getPolicyDto().getVehicleNumber()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            cellEmpty = new PdfPCell(table2);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table3.addCell(createCell("Dear All,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(cell);
            table3.addCell(cell);
            table3.addCell(createCell("Vehicle No :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getVehicleNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("Date of Loss :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getAccidDate(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("Leasing Company :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(null == claimsDto.getPolicyDto().getFinanceCompany() || claimsDto.getPolicyDto().getFinanceCompany().isEmpty() ? "N/A" : claimsDto.getPolicyDto().getFinanceCompany(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("Branch :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(null == claimsDto.getPolicyDto().getFinCompanyBranch() || claimsDto.getPolicyDto().getFinCompanyBranch().isEmpty() ? "N/A" : claimsDto.getPolicyDto().getFinCompanyBranch(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("Policy Number :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(null == claimsDto.getPolicyDto().getPolicyNumber() || claimsDto.getPolicyDto().getPolicyNumber().isEmpty() ? "N/A" : claimsDto.getPolicyDto().getPolicyNumber(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("Policy Branch :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(null == claimsDto.getPolicyDto().getPolicyBranch() || claimsDto.getPolicyDto().getPolicyBranch().isEmpty() ? "N/A" : claimsDto.getPolicyDto().getPolicyBranch(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("Customer Name :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getPolicyDto().getCustName(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("ISF Claim No :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table3);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            cellLine.setBorder(PdfPCell.BOX+Rectangle.BOTTOM);
            table1.addCell(cell);
            table4.addCell(cellLine);
            table4.addCell(cell);
            table4.addCell(createCell("With reference to the above vehicle claim, please be kind enough to send the below mentioned documents/information in order to process the claim further.", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            for (ReminderPrintDetailsDto reminderPrintDetailsDto : reminderPrintSummaryDto.getReminderPrintDetailsList()) {
                table5.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
                table5.addCell(createCell(reminderPrintDetailsDto.getReminderDocDisplayName(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            }
            cellEmpty = new PdfPCell(table5);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table7.addCell(createCell("Regards,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table7.addCell(createCell("...............................", Element.ALIGN_LEFT, 11, Font.NORMAL));
            cellEmpty = new PdfPCell(table7);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);
            table1.addCell(cell);


//            this.setFooter(table1, cell, f1);
            document.add(table1);
            //End Document

            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            this.setFooter(table, f1);
            table.addCell(cell);
            FooterTable event = new FooterTable(table);
            docWriter.setPageEvent(event);


        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error(e.getMessage());
        } finally {
            if (document != null) {
                document.close();
            }
            if (docWriter != null) {
                docWriter.close();
            }
        }
        return baosPdf;
    }

}
