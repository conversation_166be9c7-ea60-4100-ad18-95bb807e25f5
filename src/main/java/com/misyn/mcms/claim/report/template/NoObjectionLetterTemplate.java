package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.UserDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
public class NoObjectionLetterTemplate extends AbstractPdfTemplate implements PdfTemplate<ClaimsDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(NoObjectionLetterTemplate.class);


    public ByteArrayOutputStream getGenerateLetter(ClaimsDto claimsDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;

        try {
            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();
            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
            this.setHeaderAndFooter(document, table1, user);

            Font f = new Font();
            f.setColor(BaseColor.WHITE);
            PdfPCell cell = new PdfPCell(new Phrase(" ", f));
            PdfPCell cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            PdfPCell cellEmpty1 = new PdfPCell();
            cellEmpty1.setBorder(PdfPCell.NO_BORDER);
            PdfPTable table2 = new PdfPTable(new float[]{180, 350, 80});
            table2.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table3 = new PdfPTable(new float[]{200, 350, 80});
            table3.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table4 = new PdfPTable(new float[]{500});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);

            PdfPTable table5 = new PdfPTable(new float[]{100});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);

            Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
            f1.setColor(BaseColor.BLACK);
            cell.setBorder(PdfPCell.NO_BORDER);
            cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            f1 = new Font(Font.FontFamily.TIMES_ROMAN, 11, Font.NORMAL);
            f1.setColor(BaseColor.BLACK);

            table2.addCell(createCell("Dear All,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(cell);
            table2.addCell(cell);
            table2.addCell(cell);

            cellEmpty = new PdfPCell(table2);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table4.addCell(createCell("Please be kind enough to send the “No Objection Letter” for below mentioned vehicle.", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);
            table1.addCell(cell);


            table3.addCell(createCell("VEHICLE NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getVehicleNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("DATE OF LOSS", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getPolicyDto().getLatestClmLossDate(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("CLAIM AMOUNT", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("22", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("GARAGE NAME", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getIntGaragName(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));

            table3.addCell(createCell("ISF CLAIM NO", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell(claimsDto.getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            cellEmpty = new PdfPCell(table3);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);

            table1.addCell(cell);
            table1.addCell(cell);

            table5.addCell(createCell("Regards,", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table5.addCell(createCell("..................................", Element.ALIGN_LEFT, 11, Font.NORMAL));

            cellEmpty = new PdfPCell(table5);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
//          table1.addCell(cell);

//          table1.addCell(cell);
//          this.setFooter(table1, cell, f1);
            document.add(table1);
            //End Document

            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            this.setFooter(table, f1);
            table.addCell(cell);
            FooterTable event = new FooterTable(table);
            docWriter.setPageEvent(event);

        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error(e.getMessage());
        } finally {
            if (document != null) {
                document.close();
            }
            if (docWriter != null) {
                docWriter.close();
            }
        }
        return baosPdf;
    }


}
