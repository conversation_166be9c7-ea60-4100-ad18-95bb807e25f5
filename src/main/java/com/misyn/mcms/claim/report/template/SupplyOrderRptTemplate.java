package com.misyn.mcms.claim.report.template;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.misyn.mcms.claim.dto.SupplyOrderDetailsDto;
import com.misyn.mcms.claim.dto.SupplyOrderSummaryDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;

public class SupplyOrderRptTemplate extends AbstractPdfTemplate implements PdfTemplate<SupplyOrderSummaryDto> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyOrderRptTemplate.class);


    public ByteArrayOutputStream getGenerateLetter(SupplyOrderSummaryDto supplyOrderSummaryDto, UserDto user) {
        ByteArrayOutputStream baosPdf = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter docWriter = null;

        try {
            docWriter = PdfWriter.getInstance(document, baosPdf);
            document.open();
            PdfPTable table1 = new PdfPTable(new float[]{100});
            table1.setWidthPercentage(100f);
            this.setHeaderAndFooter(document, table1, user);

            Font f = new Font();
            f.setColor(BaseColor.WHITE);
            PdfPCell cell = new PdfPCell(new Phrase(" ", f));
            PdfPCell cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            PdfPCell cellEmpty1 = new PdfPCell();
            cellEmpty1.setBorder(PdfPCell.NO_BORDER);
            PdfPTable table2 = new PdfPTable(new float[]{150, 350, 80});
            table2.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table3 = new PdfPTable(new float[]{50, 100, 50, 100, 100});
            table3.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table4 = new PdfPTable(new float[]{100, 100});
            table4.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table5 = new PdfPTable(new float[]{150, 150, 150});
            table5.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table6 = new PdfPTable(new float[]{500, 50, 500});
            table6.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table7 = new PdfPTable(new float[]{100, 300});
            table7.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table8 = new PdfPTable(new float[]{500});
            table8.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table9 = new PdfPTable(new float[]{10, 500});
            table9.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table10 = new PdfPTable(new float[]{150, 150, 150});
            table10.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table11 = new PdfPTable(new float[]{10, 500});
            table11.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table12 = new PdfPTable(new float[]{10, 500});
            table12.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table13 = new PdfPTable(new float[]{150, 150, 150});
            table13.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table14 = new PdfPTable(new float[]{10, 500});
            table14.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table15 = new PdfPTable(new float[]{150, 150, 150});
            table15.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table16 = new PdfPTable(new float[]{10, 500});
            table16.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table17 = new PdfPTable(new float[]{10, 500});
            table17.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table18 = new PdfPTable(new float[]{150, 150, 150});
            table18.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table19 = new PdfPTable(new float[]{10, 150, 500});
            table19.setHorizontalAlignment(Element.ALIGN_LEFT);
            PdfPTable table20 = new PdfPTable(new float[]{10, 500});
            table20.setHorizontalAlignment(Element.ALIGN_LEFT);
            Font f1 = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
            f1.setColor(BaseColor.BLACK);
            cell.setBorder(PdfPCell.NO_BORDER);
            cellEmpty = new PdfPCell();
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            f1 = new Font(Font.FontFamily.TIMES_ROMAN, 11, Font.NORMAL);
            f1.setColor(BaseColor.BLACK);

            table2.addCell(createCell("Spare Parts Supplier", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getSupplierName(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Email", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getSupplierEmail(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Contact No.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getSupplierContactNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Insured Name", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getCustName(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Make & Model", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell((null == supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getVehicleMake() ? AppConstant.STRING_EMPTY : supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getVehicleMake()) + " " + (null == supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getVehicleModel() ? AppConstant.STRING_EMPTY : supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getVehicleModel()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Vehicle No.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getVehicleNumber(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Year of Make", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(String.valueOf(supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getManufactYear()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Chassis No.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getChassisNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Claim No /Policy No.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell((null == supplyOrderSummaryDto.getClaimNo() ? AppConstant.STRING_EMPTY : supplyOrderSummaryDto.getClaimNo()) + " / " + (null == supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getPolicyNumber() ? AppConstant.STRING_EMPTY : supplyOrderSummaryDto.getClaimsDto().getPolicyDto().getPolicyNumber()), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("ISF Claim No.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getClaimsDto().getIsfClaimNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Date of Loss", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getClaimsDto().getAccidDate(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            table2.addCell(createCell("Serial No.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(createCell(supplyOrderSummaryDto.getSupplyOrderSerialNo(), Element.ALIGN_LEFT, 11, Font.NORMAL));
            table2.addCell(cellEmpty);

            cellEmpty = new PdfPCell(table2);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table1.addCell(cell);
            table1.addCell(cell);

            table3.addCell(createCell("NO", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("ITEM", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("QUANTITY", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("INDIVIDUAL PRICE (Rs.)", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell("TOTAL AMOUNT(Rs.)", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            for (SupplyOrderDetailsDto supplyOrderDetailsDto : supplyOrderSummaryDto.getSupplyOrderDetailsDtoList()) {
                table3.addCell(createCell(String.valueOf(supplyOrderDetailsDto.getIndex()), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table3.addCell(createCell(supplyOrderDetailsDto.getSparePartName(), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table3.addCell(createCell(String.valueOf(supplyOrderDetailsDto.getQuantity()), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table3.addCell(createCell(Boolean.FALSE.equals(supplyOrderDetailsDto.getIsPendingIndividualPrice()) ? Utility.formatCurrency(supplyOrderDetailsDto.getIndividualPrice()) : "Pending", Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
                table3.addCell(createCell(Utility.formatCurrency(supplyOrderDetailsDto.getTotalAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            }
            /*table4.addCell(createCell("O/A", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table4.addCell(createCell("0%", Element.ALIGN_LEFT, 11, Font.NORMAL));
            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);*/

            table3.addCell(createCell(" ", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_RIGHT, 11, Font.NORMAL));

            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("Total", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell(Utility.formatCurrency(supplyOrderSummaryDto.getTotalAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

          /*  table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(cellEmpty).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell(String.valueOf(supplyOrderSummaryDto.getOwnersAccountDeductionRate()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
*/
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("Total Deductions", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell(Utility.formatCurrency(supplyOrderSummaryDto.getPolicyExcess().add(supplyOrderSummaryDto.getOthertDeductionAmount()).add(supplyOrderSummaryDto.getTotalOwnersAccountAmount())), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

//            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
//            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
//            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
//            table3.addCell(createCell("Total Amount", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
//            table3.addCell(createCell(Utility.formatCurrency(supplyOrderSummaryDto.getFinalAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            BigDecimal finalAmount = getVatAmount(supplyOrderSummaryDto);

            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("VAT   " + getVatStatus(supplyOrderSummaryDto.getVatStatus()) + "   " + supplyOrderSummaryDto.getVatAmount() + "%", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell(Utility.formatCurrency(finalAmount), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table3.addCell(createCell("Final Amount", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table3.addCell(createCell(Utility.formatCurrency(supplyOrderSummaryDto.getFinalAmount()), Element.ALIGN_RIGHT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);


            table3.addCell(cell);
            table3.addCell(cell);
            table3.addCell(cell);
            table3.addCell(cell);
            table3.addCell(cell);

            cellEmpty = new PdfPCell(table3);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table1.addCell(cell);
            table1.addCell(cell);

            table7.addCell(createCell("Workshop", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table7.addCell(createCell(supplyOrderSummaryDto.getWorkShopName(), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table7.addCell(createCell("Address", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table7.addCell(createCell(supplyOrderSummaryDto.getWorkShopAddress1() + supplyOrderSummaryDto.getWorkShopAddress2() + supplyOrderSummaryDto.getWorkShopAddress3(), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            table7.addCell(createCell("Contact No.", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table7.addCell(createCell(supplyOrderSummaryDto.getWorkShopContactNo(), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);
            table7.addCell(cell);
            table7.addCell(cell);

            table8.addCell(createCell("Supplier Remarks :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table8.addCell(createCell(supplyOrderSummaryDto.getOtherRemark(), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);


            table8.addCell(createCell("Other Remarks :", Element.ALIGN_LEFT, 11, Font.NORMAL));
            table8.addCell(createCell(supplyOrderSummaryDto.getSupplyOrderRemark(), Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.BOTTOM | Rectangle.RIGHT | Rectangle.TOP | Rectangle.LEFT);

            cellEmpty = new PdfPCell(table7);
            cellEmpty1 = new PdfPCell(table8);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            cellEmpty1.setBorder(PdfPCell.NO_BORDER);
            table6.addCell(cellEmpty1);
            table6.addCell(cell);
            table6.addCell(cellEmpty);


            cellEmpty = new PdfPCell(table6);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            cellEmpty = new PdfPCell(table4);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table3.addCell(cellEmpty);

            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);

            table5.addCell(cell);
            table5.addCell(cell);
            table5.addCell(cell);
            table5.addCell(cell);
            table5.addCell(cell);
            table5.addCell(cell);
            table5.addCell(createCell(supplyOrderSummaryDto.getInputUserFullName(), Element.ALIGN_CENTER, 11, Font.NORMAL));
            table5.addCell(createCell(supplyOrderSummaryDto.getApprvScrutinizingUserFullName(), Element.ALIGN_CENTER, 11, Font.NORMAL));
            table5.addCell(createCell(supplyOrderSummaryDto.getApprvClaimHandlerUserFullName(), Element.ALIGN_CENTER, 11, Font.NORMAL));

            table5.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table5.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table5.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));

            table5.addCell(createCell("Spare Parts Coordinator", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table5.addCell(createCell("Authorized Officer Engineering Department", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table5.addCell(createCell("Authorized Officer Motor Claim Department", Element.ALIGN_CENTER, 11, Font.NORMAL));

            table5.addCell(cell);
            table5.addCell(cell);
            table5.addCell(cell);


            cellEmpty = new PdfPCell(table5);
            table1.addCell(cellEmpty);

            table1.addCell(cell);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);

            table9.addCell(cell).setBorder(Rectangle.TOP | Rectangle.LEFT);
            table9.addCell(createCell("I Accept these spare parts & confirm that they are in good condition", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT | Rectangle.TOP);

            table9.addCell(cell).setBorder(Rectangle.LEFT);
            table9.addCell(cell).setBorder(Rectangle.RIGHT);
            cellEmpty = new PdfPCell(table9);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table10.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table10.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table10.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table10.addCell(createCell("Date", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table10.addCell(createCell("NIC No", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table10.addCell(createCell("Signature of Insured", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table10.addCell(cell).setBorder(Rectangle.LEFT | Rectangle.BOTTOM);
            table10.addCell(cell).setBorder(Rectangle.BOTTOM);
            table10.addCell(cell).setBorder(Rectangle.RIGHT | Rectangle.BOTTOM);

            cellEmpty = new PdfPCell(table10);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            table1.addCell(cell);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);

            table11.addCell(cell).setBorder(Rectangle.TOP | Rectangle.LEFT);
            table11.addCell(createCell("If Insured nominates a person to collect the spare parts", Element.ALIGN_LEFT, 12, Font.UNDERLINE | Font.BOLD)).setBorder(Rectangle.RIGHT | Rectangle.TOP);
            cellEmpty = new PdfPCell(table11);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table12.addCell(cell).setBorder(Rectangle.LEFT);
            table12.addCell(cell).setBorder(Rectangle.RIGHT);

            table12.addCell(cell).setBorder(Rectangle.LEFT);
            table12.addCell(createCell("I here by Authorize Mr/Ms_______________________Bearing NIC No._____________________to collect the parts on my behalf", Element.ALIGN_LEFT, 10, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table12.addCell(cell).setBorder(Rectangle.LEFT);
            table12.addCell(cell).setBorder(Rectangle.RIGHT);

            cellEmpty = new PdfPCell(table12);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table13.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table13.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table13.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table13.addCell(createCell("Date", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table13.addCell(createCell("NIC No", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table13.addCell(createCell("Signature of Insured", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table13.addCell(cell).setBorder(Rectangle.LEFT);
            table13.addCell(cell);
            table13.addCell(cell).setBorder(Rectangle.RIGHT);


            cellEmpty = new PdfPCell(table13);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table14.addCell(cell).setBorder(Rectangle.LEFT);
            table14.addCell(createCell("I Accept these spare parts & confirm that they are in good condition", Element.ALIGN_LEFT, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table14.addCell(cell).setBorder(Rectangle.LEFT);
            table14.addCell(cell).setBorder(Rectangle.RIGHT);
            cellEmpty = new PdfPCell(table14);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table15.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table15.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table15.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table15.addCell(createCell("Date", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table15.addCell(createCell("NIC No", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table15.addCell(createCell("Signature of Nominee", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table15.addCell(cell).setBorder(Rectangle.LEFT | Rectangle.BOTTOM);
            table15.addCell(cell).setBorder(Rectangle.BOTTOM);
            table15.addCell(cell).setBorder(Rectangle.RIGHT | Rectangle.BOTTOM);


            cellEmpty = new PdfPCell(table15);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table1.addCell(cell);

            table16.addCell(cell).setBorder(Rectangle.LEFT | Rectangle.TOP);
            table16.addCell(createCell("To be Completed by Spare parts supplier", Element.ALIGN_LEFT, 12, Font.UNDERLINE | Font.BOLD)).setBorder(Rectangle.RIGHT | Rectangle.TOP);
            cellEmpty = new PdfPCell(table16);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table17.addCell(cell).setBorder(Rectangle.LEFT);
            table17.addCell(cell).setBorder(Rectangle.RIGHT);

            table17.addCell(cell).setBorder(Rectangle.LEFT);
            table17.addCell(createCell("The above mentioned spare parts were handed over to Mr/Ms:-", Element.ALIGN_LEFT, 10, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table17.addCell(cell).setBorder(Rectangle.LEFT);
            table17.addCell(cell).setBorder(Rectangle.RIGHT);
            cellEmpty = new PdfPCell(table17);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);


            table18.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table18.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table18.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table18.addCell(createCell("Date", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.LEFT);
            table18.addCell(createCell("NIC No", Element.ALIGN_CENTER, 11, Font.NORMAL));
            table18.addCell(createCell("Name,Signature & Rubber Stamp", Element.ALIGN_CENTER, 11, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table18.addCell(cell).setBorder(Rectangle.LEFT | Rectangle.BOTTOM);
            table18.addCell(cell).setBorder(Rectangle.BOTTOM);
            table18.addCell(cell).setBorder(Rectangle.RIGHT | Rectangle.BOTTOM);

            cellEmpty = new PdfPCell(table18);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table1.addCell(cell);

            table19.addCell(cell).setBorder(Rectangle.LEFT | Rectangle.TOP);
            table19.addCell(createCell(getHtmlPhrase("<span style=\"color: RED; font-size: 10px;font-weight: bold;margin-left: 5%;\"><u>Instruction to the Insured-</u>", f1), Element.ALIGN_LEFT, 10, Font.UNDERLINE | Font.BOLD)).setBorder(Rectangle.TOP);
            table19.addCell(createCell(getHtmlPhrase("<span style=\"color: RED; font-size: 10px;\">Accepted goods can be returned only within two days from the date of acceptance and is subject to the terms", f1), Element.ALIGN_LEFT, 10, Font.NORMAL)).setBorder(Rectangle.TOP | Rectangle.RIGHT);
            cellEmpty = new PdfPCell(table19);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

//            Font font = new Font();
            table20.addCell(cell).setBorder(Rectangle.LEFT);
            table20.addCell(cell).setBorder(Rectangle.RIGHT);

            table20.addCell(cell).setBorder(Rectangle.LEFT);
            table20.addCell(createCell(getHtmlPhrase("<span style=\"color: RED; font-size: 11px;font-weight: bold;\"><u>Instruction to the Spare Part Supplier</u>", f1), Element.ALIGN_LEFT, 11, Font.UNDERLINE | Font.BOLD)).setBorder(Rectangle.RIGHT);

            table20.addCell(cell).setBorder(Rectangle.LEFT);
            table20.addCell(createCell(getHtmlPhrase("<span style=\"color: RED; font-size: 10px;\">01.Please make sure this delivery order has been signed by us & the Insured before Suppling the spare parts.", f1), Element.ALIGN_LEFT, 10, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table20.addCell(cell).setBorder(Rectangle.LEFT);
            table20.addCell(createCell(getHtmlPhrase("<span style=\"color: RED; font-size: 10px;\">02.The original invoice/tax invoice should be sent along with the copy of the delivery order so that the payment can be processed.", f1), Element.ALIGN_LEFT, 10, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table20.addCell(cell).setBorder(Rectangle.LEFT);
            table20.addCell(createCell(getHtmlPhrase("<span style=\"color: RED; font-size: 10px;\">03.Please ensure the NIC no.of the person collecting the spare parts is as indicated above.", f1), Element.ALIGN_LEFT, 10, Font.NORMAL)).setBorder(Rectangle.RIGHT);

            table20.addCell(cell).setBorder(Rectangle.LEFT | Rectangle.BOTTOM);
            table20.addCell(cell).setBorder(Rectangle.RIGHT | Rectangle.BOTTOM);

            cellEmpty = new PdfPCell(table20);
//            cellEmpty.setBackgroundColor(BaseColor.RED);
            cellEmpty.setBorder(PdfPCell.NO_BORDER);
            table1.addCell(cellEmpty);

            table1.addCell(cell);
            table1.addCell(cell);


//            table5.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
//            table5.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
//            table5.addCell(createCell(".........................................", Element.ALIGN_CENTER, 11, Font.NORMAL));
//
//            table5.addCell(createCell("Spare Parts Coordinator", Element.ALIGN_CENTER, 11, Font.NORMAL));
//            table5.addCell(createCell("Authorized Officer Engineering Department", Element.ALIGN_CENTER, 11, Font.NORMAL));
//            table5.addCell(createCell("Authorized Officer Motor Claim Department", Element.ALIGN_CENTER, 11, Font.NORMAL));


            table1.addCell(cell);
//            this.setFooter(table1, cell, f1);
            document.add(table1);
            //End Document

            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
//            this.setFooter(table, f1);
            table.addCell(cell);
            FooterTable event = new FooterTable(table);
            docWriter.setPageEvent(event);


        } catch (Exception e) {
            baosPdf.reset();
            LOGGER.error(e.getMessage());
        } finally {
            if (document != null) {
                document.close();
            }
            if (docWriter != null) {
                docWriter.close();
            }
        }
        return baosPdf;
    }

//    private BigDecimal getFinalAmount(SupplyOrderSummaryDto supplyOrderSummaryDto, BigDecimal finalAmount) {
//
//        if (null != supplyOrderSummaryDto.getVatStatus() && supplyOrderSummaryDto.getVatStatus().equalsIgnoreCase(AppConstant.REMOVE)) {
//            return supplyOrderSummaryDto.getFinalAmount().subtract(finalAmount);
//        } else {
//            return supplyOrderSummaryDto.getFinalAmount().add(finalAmount);
//        }
//    }

    private String getVatStatus(String vatStatus) {

        if (null != vatStatus && vatStatus.equalsIgnoreCase(AppConstant.REMOVE)) {
            return "(Removed)";
        } else {
            return "(Added)";
        }
    }

    private BigDecimal getVatAmount(SupplyOrderSummaryDto supplyOrderSummaryDto) {
        if (null != supplyOrderSummaryDto.getVatStatus() && supplyOrderSummaryDto.getVatStatus().equalsIgnoreCase(AppConstant.REMOVE)) {
            return (supplyOrderSummaryDto.getTotalAmount().multiply(supplyOrderSummaryDto.getVatAmount())).divide(new BigDecimal(100));
        } else {
            return (supplyOrderSummaryDto.getTotalAmount().multiply(supplyOrderSummaryDto.getVatAmount())).divide(new BigDecimal(100));
        }
    }
}
