package com.misyn.mcms.claim.job.impl;

import com.misyn.mcms.claim.dto.ApplicationAlertDto;
import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.NotificationDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.job.CallCenterJob;
import com.misyn.mcms.claim.job.JobParameter;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.MessageConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
public class CallCenterJobImpl implements CallCenterJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(CallCenterJobImpl.class);
    private static final Object LOCK = new Object();
    private AssessorAllocationService assessorAllocationService = new AssessorAllocationServiceImpl();
    private ClaimCalculationSheetPayeeService claimCalculationSheetPayeeService = new ClaimCalculationSheetPayeeServiceImpl();
    private ClaimHandlerService claimHandlerService = new ClaimHandlerServiceImpl();
    private NotificationService notificationService = new NotificationServiceImpl();
    private InvestigationDetailsService investigationDetailsService = new InvestigationDetailsServiceImpl();

    @Override
    public void executeFollowUpJob() {

        try {
            if (JobParameter.isFollowUpJobStart()) {
                notification();
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void sendEmails() {
        try {
            claimCalculationSheetPayeeService.getSendPendingEmailList();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void executeTheftClaim() {
        try {
            theftClaims();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void sendAssessorEmails() {
        try {
            synchronized (LOCK) {
                claimCalculationSheetPayeeService.getSendAssessorPaymentEmail();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void sendDocumentNotifications() {
        try {
            notificationService.sendDocumentNotification();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void sendNotificationForPendingInvestigationAssignUser() {
        try {
            investigationDetailsService.sendNotificationForPendingInvestigationAssignUser();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void sendAriNotifications() {
        try {
            notificationService.sendAriNotification();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public void sendAssessorReminder() {
        try {
            notificationService.checkTimeDuration();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public void run() {
        this.executeFollowUpJob();
        this.sendEmails();
        this.executeTheftClaim();
        this.sendAssessorEmails();
        this.sendDocumentNotifications();
        this.sendNotificationForPendingInvestigationAssignUser();
        this.sendAriNotifications();
        this.sendAssessorReminder();
    }


    private void notification() {
        try {
            List<NotificationDto> notifiList = assessorAllocationService.getNotifiList();
            for (NotificationDto notificationDto : notifiList) {
                if (null == assessorAllocationService.searchAlert(notificationDto.getClaimNo(), 1)) {
                    notificationDto.setInpUserId("SYSTEM");
                    notificationDto.setMessage(MessageConstant.MESSAGE);
                    notificationDto.setReadDateTime(AppConstant.DEFAULT_DATE_TIME);
                    notificationDto.setUrl(AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(notificationDto.getClaimNo())).concat("&P_TAB_INDEX=3&TYPE=2"));

                    ApplicationAlertDto applicationAlertDto = new ApplicationAlertDto();
                    applicationAlertDto.setKeyValue(notificationDto.getClaimNo());
                    applicationAlertDto.setType(1);
                    applicationAlertDto.setProcessDateTime(Utility.sysDateTime());
                    applicationAlertDto.setInpUser("SYSTEM");
                    assessorAllocationService.insertAlert(applicationAlertDto);
                    assessorAllocationService.insertNotification(notificationDto);

                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void theftClaims() {
        try {
            List<ClaimsDto> theftClaimsList = claimHandlerService.getTheftClaimsList();
            for (ClaimsDto claimsDto : theftClaimsList) {
                if (null == assessorAllocationService.searchAlert(claimsDto.getClaimNo(), 2)) {
                    List<UserDto> totalUserList = claimHandlerService.getTotalUserList();
                    for (UserDto user : totalUserList) {
                        NotificationDto notificationDto = new NotificationDto();
                        notificationDto.setInpUserId("SYSTEM");
                        notificationDto.setMessage(MessageConstant.THEFT_CLAIM);
                        notificationDto.setReadDateTime(AppConstant.DEFAULT_DATE_TIME);
                        notificationDto.setUrl(AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimsDto.getClaimNo())).concat("&P_TAB_INDEX=0"));
                        notificationDto.setAccidentDate(claimsDto.getAccidDate());
                        notificationDto.setAssignUserId(user.getUserId());
                        notificationDto.setClaimNo(claimsDto.getClaimNo());
                        notificationDto.setNotifyDateTime(Utility.sysDateTime());
                        notificationDto.setVehicleNo(claimsDto.getVehicleNo());
                        notificationDto.setCoverNoteNo(claimsDto.getCoverNoteNo());
                        notificationDto.setProductName(claimsDto.getPolicyDto().getProduct());
                        notificationDto.setCategoryDesc(claimsDto.getPolicyDto().getCategoryDescription());
                        assessorAllocationService.insertNotification(notificationDto);
                    }
                    ApplicationAlertDto applicationAlertDto = new ApplicationAlertDto();
                    applicationAlertDto.setKeyValue(claimsDto.getClaimNo());
                    applicationAlertDto.setType(2);
                    applicationAlertDto.setProcessDateTime(Utility.sysDateTime());
                    applicationAlertDto.setInpUser("SYSTEM");
                    assessorAllocationService.insertAlert(applicationAlertDto);

                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
