package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimDocumentDto;
import com.misyn.mcms.claim.dto.ClaimDocumentStatusDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.sql.Connection;

public interface ClaimDocumentService {

    ClaimDocumentStatusDto getDocumentStatus(Connection connection, Integer claimNo, Integer documentTypeId);

    ClaimDocumentStatusDto getInvestigationReportDocumentStatus(Connection connection, Integer claimNo);

    ClaimDocumentDto getClaimDocumentDto(Integer refId);

    ClaimDocumentDto updateBillCheckDetails(ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynJDBCException;

    ClaimDocumentStatusDto getCheckedInitialLiabilityDocumentStatus(Integer claimNo);

    ClaimDocumentStatusDto getCheckedLiabilityDocumentStatus(Integer claimNo);

    void cancelDocument(ClaimDocumentDto claimDocumentDto, UserDto user) throws Exception;

    boolean isDocumentAvailable(int claimNo)throws Exception;

    String lastDocUploadDateTime(Integer claimNo) throws Exception;
}
