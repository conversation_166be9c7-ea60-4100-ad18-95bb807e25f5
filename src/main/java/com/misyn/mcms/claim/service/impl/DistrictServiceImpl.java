package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.DistrictDao;
import com.misyn.mcms.claim.dao.impl.DistrictDaoImpl;
import com.misyn.mcms.claim.dto.DistrictDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.DistrictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class DistrictServiceImpl extends AbstractBaseService<DistrictServiceImpl> implements DistrictService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DistrictServiceImpl.class);
    private DistrictDao districtDao = new DistrictDaoImpl();

    @Override
    public DistrictDto insert(DistrictDto districtDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public DistrictDto update(DistrictDto districtDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public DistrictDto delete(DistrictDto districtDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public DistrictDto updateAuthPending(DistrictDto districtDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public DistrictDto deleteAuthPending(DistrictDto districtDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public DistrictDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public DistrictDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public DistrictDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public DistrictDto search(Object id) throws Exception {
        DistrictDto districtDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            districtDto = districtDao.searchMaster(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return districtDto;
    }

    @Override
    public DistrictDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<DistrictDto> searchAll() throws Exception {
        List<DistrictDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = districtDao.searchAll(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }

        return list;
    }

    @Override
    public List<DistrictDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public DistrictDto searchMasterId(Object id) throws Exception {
        DistrictDto districtDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            districtDto = districtDao.searchMasterId(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return districtDto;
    }
}
