package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.SupplyOrderStatusEnum;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.CallCenterService;
import com.misyn.mcms.claim.service.SupplyOrderService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class SupplyOrderServiceImpl extends AbstractBaseService<SupplyOrderServiceImpl> implements SupplyOrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyOrderServiceImpl.class);
    private static final Integer ROW_COUNT = 4;
    private final Lock LOCK = new ReentrantLock();
    protected CallCenterService callCenterService = new CallCenterServiceImpl();
    private final SupplyOrderSummaryDao supplyOrderSummaryDao = new SupplyOrderSummaryDaoImpl();
    private final SupplyOrderDetailsDao supplyOrderDetailsDao = new SupplyOrderDetailsDaoIpml();
    private final ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private final SparePartDatabaseDao sparePartDatabaseDao = new SparePartDatabaseDaoImpl();
    private final SequenceKeyTableDao sequenceKeyTableDao = new SequenceKeyTableDaoImpl();
    private final MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private final RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private final ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private final SupplyOrderHistoryDao supplyOrderHistoryDao = new SupplyOrderHistoryDaoImpl();
    private final SupplyOrderDetailsHistoryDao supplyOrderDetailsHistoryDao = new SupplyOrderDetailsHistoryDaoImpl();
    private final SupplyOrderRequestDao supplyOrderRequestDao = new SupplyOrderRequestDaoImpl();

    @Override
    public SupplyOrderSummaryDto getSupplyOrderSummaryDto(Integer supplyOrderRefNo, Integer claimNo) {
        SupplyOrderSummaryDto supplyOrderSummaryDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            supplyOrderSummaryDto = getSupplyOrderSummaryDto(connection, supplyOrderRefNo, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return supplyOrderSummaryDto;
    }


    private SupplyOrderSummaryDto getSupplyOrderSummaryDto(Connection connection, Integer supplyOrderRefNo, Integer claimNo) {
        List<SupplyOrderDetailsDto> supplyOrderDetailsList = new ArrayList<>();
        SupplyOrderSummaryDto supplyOrderSummaryDto = null;

        try {
            if (supplyOrderRefNo == 0) {
                supplyOrderSummaryDto = new SupplyOrderSummaryDto();
                for (int i = 1; i <= ROW_COUNT; i++) {
                    SupplyOrderDetailsDto supplyOrderDetailsDto = new SupplyOrderDetailsDto();
                    supplyOrderDetailsDto.setIndex(i);
                    supplyOrderDetailsList.add(supplyOrderDetailsDto);
                }
            } else {
                supplyOrderSummaryDto = supplyOrderSummaryDao.searchMasterUpdate(connection, supplyOrderRefNo);
                supplyOrderDetailsList = supplyOrderDetailsDao.searchAllBySupplyOrderDetailsRefNo(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
            }
            if (null != supplyOrderSummaryDto) {
                supplyOrderSummaryDto.setSupplyOrderDetailsDtoList(supplyOrderDetailsList);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return supplyOrderSummaryDto;
    }

    private SupplyOrderSummaryDto getSupplyOrderSummaryDtoBySupplyOrderRefNo(Connection connection, Integer supplyOrderRefNo, Integer claimNo) {
        List<SupplyOrderDetailsDto> supplyOrderDetailsList = new ArrayList<>();
        SupplyOrderSummaryDto supplyOrderSummaryDto = null;

        try {
            if (supplyOrderRefNo == 0) {
                supplyOrderSummaryDto = new SupplyOrderSummaryDto();
                for (int i = 1; i <= ROW_COUNT; i++) {
                    SupplyOrderDetailsDto supplyOrderDetailsDto = new SupplyOrderDetailsDto();
                    supplyOrderDetailsDto.setIndex(i);
                    supplyOrderDetailsList.add(supplyOrderDetailsDto);
                }
            } else {
                supplyOrderSummaryDto = supplyOrderSummaryDao.searchMasterBySupplyOrderRefNo(connection, supplyOrderRefNo);
                supplyOrderDetailsList = supplyOrderDetailsDao.searchAllBySupplyOrderDetailsRefNo(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
            }
            if (null != supplyOrderSummaryDto) {
                supplyOrderSummaryDto.setSupplyOrderDetailsDtoList(supplyOrderDetailsList);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return supplyOrderSummaryDto;
    }


    @Override
    public SupplyOrderSummaryDto getSupplyOrderSummaryDto(Integer supplyOrderRefNo, boolean isMaster) {
        List<SupplyOrderDetailsDto> supplyOrderDetailsList = new ArrayList<>();
        SupplyOrderSummaryDto supplyOrderSummaryDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (isMaster) {
                supplyOrderSummaryDto = supplyOrderSummaryDao.searchBysupplyOrderRefNo(connection, supplyOrderRefNo);
                supplyOrderDetailsList = supplyOrderDetailsDao.searchAllBySupplyOrderDetailsRefNo(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
            } else {
                String policyChannelType = supplyOrderSummaryDao.getPolicyChannelType(connection, supplyOrderRefNo, AppConstant.ZERO_INT);
                supplyOrderSummaryDto = supplyOrderHistoryDao.searchByRefNo(connection, supplyOrderRefNo, policyChannelType);
                supplyOrderDetailsList = supplyOrderDetailsHistoryDao.searchAllByRefNo(connection, supplyOrderSummaryDto.getSupplyOrderDetailRefNo());
            }
            if (null != supplyOrderSummaryDto) {
                ClaimsDto viewAccidentClaimsDto = callCenterService.getViewAccidentClaimsDto(connection, supplyOrderSummaryDto.getClaimNo());
                supplyOrderSummaryDto.setClaimsDto(viewAccidentClaimsDto);
                supplyOrderSummaryDto.setSupplyOrderDetailsDtoList(supplyOrderDetailsList);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return supplyOrderSummaryDto;
    }

    @Override
    public List<SupplyOrderDetailsDto> getSupplyOrderDetailsDtoList(Integer supplyOrderRefNo) {
        return null;
    }

    @Override
    public void saveSupplyOrder(SupplyOrderSummaryDto supplyOrderSummaryDto, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        SupplyOrderSummaryDto supplyOrderSummaryDtoSaved;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            supplyOrderDetailsDao.deleteSupplyOrderDetailsRefNo(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
            supplyOrderSummaryDto.setApproveAssignSparePartCoordinator(supplyOrderSummaryDto.getInputUserId());
            supplyOrderSummaryDto.setApproveAssignSparePartCoordinatorDateTime(Utility.sysDateTime());
            supplyOrderSummaryDto.setGenerateUserId(AppConstant.STRING_EMPTY);
            supplyOrderSummaryDto.setGenerateDateTime(AppConstant.DEFAULT_DATE_TIME);
            supplyOrderSummaryDto.setIsGenerate(AppConstant.NO);
            if (supplyOrderSummaryDto.getSupplyOrderRefNo() == 0) {
                updateMaxJobId(connection, supplyOrderSummaryDto);
                supplyOrderSummaryDtoSaved = supplyOrderSummaryDao.insertMaster(connection, supplyOrderSummaryDto);
            } else {
                supplyOrderSummaryDtoSaved = supplyOrderSummaryDao.updateMaster(connection, supplyOrderSummaryDto);
            }
            if (supplyOrderSummaryDtoSaved != null) {
                List<SupplyOrderDetailsDto> supplyOrderDetailsDtoList = supplyOrderSummaryDto.getSupplyOrderDetailsDtoList();
                for (SupplyOrderDetailsDto supplyOrderDetailsDto : supplyOrderDetailsDtoList) {
                    supplyOrderDetailsDto.setSupplyOrderRefNo(supplyOrderSummaryDtoSaved.getSupplyOrderRefNo());
                    supplyOrderDetailsDao.insertMaster(connection, supplyOrderDetailsDto);
                }
            }
            if (AppConstant.YES.equals(supplyOrderSummaryDto.getIsExcessInclude())) {
                claimHandlerDao.updateExcessIncludeStatus(connection, AppConstant.YES, supplyOrderSummaryDto.getClaimNo());
            } else {
                claimHandlerDao.updateExcessIncludeStatus(connection, AppConstant.NO, supplyOrderSummaryDto.getClaimNo());
            }

            Integer pendingDO = supplyOrderRequestDao.getPendingDO(connection, supplyOrderSummaryDto.getClaimNo());
            if (null == pendingDO) {
                supplyOrderRequestDao.newDoRequest(connection, supplyOrderSummaryDto.getClaimNo(), supplyOrderSummaryDto.getSupplyOrderRefNo());
            } else {
                supplyOrderRequestDao.updateDoRequest(connection, supplyOrderSummaryDto.getClaimNo(), supplyOrderSummaryDto.getSupplyOrderRefNo());
            }

            saveClaimsLogs(connection, supplyOrderSummaryDto.getClaimNo(), user, "Save Supplier Order", "Save Supplier Order");

            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            rollbackTransaction(connection);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void updateMaxJobId(Connection connection, SupplyOrderSummaryDto supplyOrderSummaryDto) {
        try {
            LOCK.lock();
            SequenceTableDto selectedKeyValue = sequenceKeyTableDao.getSelectedKeyValue(connection, AppConstant.JOB_TABLE_ID_FOR_SUPPLY_ORDER);

            //System Date to Reset Sequence
            String sysDate = Utility.sysDate("yyyyMMdd");

            //New Values
            int newKeyValue;
            String newSysDate;
            String formatNewSysDate;
            String serialNumber;

            if (sysDate.equals(selectedKeyValue.getMaxSysDate())) {
                newKeyValue = selectedKeyValue.getMaxKeyValue();
                newSysDate = selectedKeyValue.getMaxSysDate();

            } else {
                newKeyValue = 1;
                newSysDate = sysDate;
                //jobNo = newSysDate + Utility.addZeroRJ(String.valueOf(newKeyValue), 4);
            }

            formatNewSysDate = Utility.getCustomDateFormat(newSysDate, AppConstant.DATE_FORMAT_INT, AppConstant.JOB_DATE_FORMAT);
            serialNumber = formatNewSysDate + Utility.addZeroRJ(String.valueOf(newKeyValue), 4);

            if (null != selectedKeyValue) {
                supplyOrderSummaryDto.setSupplyOrderSerialNo(serialNumber);
            }
            selectedKeyValue.setMaxKeyValue(newKeyValue + 1);
            selectedKeyValue.setMaxSysDate(newSysDate);
            selectedKeyValue.setTableId(AppConstant.JOB_TABLE_ID_FOR_SUPPLY_ORDER);
            sequenceKeyTableDao.updateMaster(connection, selectedKeyValue);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            LOCK.unlock();
        }
    }

    @Override
    public void forwardToScrTeamSupplyOrder(Integer claimNo, Integer supplyOrderRefNo, String apprvAssignScrutinizingUserId, UserDto user) throws MisynJDBCException {
        String submitUser = user.getUserId();// user.getV_firstname().concat(" ").concat(user.getV_lastname());
        Connection connection = null;
        String message = "You have received supply order from Spare Parts Coordinator ";
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            supplyOrderSummaryDao.updateSupplyOrderScrTeamDetails(connection, supplyOrderRefNo, SupplyOrderStatusEnum.FORWARD_SCRUTINIZING_TEAM, apprvAssignScrutinizingUserId, Utility.sysDateTime());

            saveNotification(connection, claimNo, user.getUserId(), apprvAssignScrutinizingUserId, message, URL);
            saveClaimsLogs(connection, claimNo, user, "Forward To Scrutinizing Team", "Forward To Scrutinizing Team - "
                    .concat("[").concat(apprvAssignScrutinizingUserId).concat("]"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToSparePartsCoordinator(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException {

        Connection connection = null;
        String message = "supply order has returned from Scrutinizing Team";
        // String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, supplyOrderRefNo);
            supplyOrderSummaryDao.updateSupplyOrderReturnToSupCoord(connection, supplyOrderRefNo);
            saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getInputUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, "Return to Spare Parts Coordinator", "Return to Spare Parts Coordinator [" + supplyOrderSummaryDto.getInputUserId() + "]");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToSparePartsCoordinatorByClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        String message = "supply order has returned from claim handler ";
        // String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, supplyOrderRefNo);
            supplyOrderSummaryDao.updateSupplyOrderReturnToSupCoord(connection, supplyOrderRefNo);

            if (motorEngineerDetailsDao.isInspectionPending(connection, claimNo, AppConstant.ZERO_INT)) {
                if (motorEngineerDetailsDao.isAriSalvagePending(connection, claimNo)) {
                    claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
                    saveClaimsLogs(connection, claimNo, user, "Auto Store File", "Claim File Auto Stored due to Pending ARI/ Salvage Inspection");
                    saveClaimProcessFlow(connection, claimNo, 0, "Auto Store File", supplyOrderSummaryDto.getInputUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                }
                UserDto user1 = new UserDto();
                user1.setUserId(supplyOrderSummaryDto.getInputUserId());
                sendNotificationForJobPendingRte(claimNo, user1, connection, true);
                rtePendingClaimDetailDao.savePendingJobs(connection, claimNo, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR, supplyOrderSummaryDto.getInputUserId());
                saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getInputUserId(), message, URL, "#FFFFBF");
            } else {
                saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getInputUserId(), message, URL);
            }

            saveClaimsLogs(connection, claimNo, user, "Return to Spare Parts Coordinator from Claim Handler", "Return to Spare Parts Coordinator [" + supplyOrderSummaryDto.getInputUserId() + "]");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnToScrTeamSupplyOrderByClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        String message = "supply order has returned from claim handler";
        // String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, supplyOrderRefNo);
            supplyOrderSummaryDao.updateSupplyOrderReturnToScrTeam(connection, SupplyOrderStatusEnum.FORWARD_SCRUTINIZING_TEAM, supplyOrderRefNo);

            if (motorEngineerDetailsDao.isInspectionPending(connection, claimNo, AppConstant.ZERO_INT)) {
                if (motorEngineerDetailsDao.isAriSalvagePending(connection, claimNo)) {
                    claimHandlerDao.updateStoreStatus(connection, claimNo, "Y");
                    saveClaimsLogs(connection, claimNo, user, "Auto Store File", "Claim File Auto Stored due to Pending ARI/ Salvage Inspection");
                    saveClaimProcessFlow(connection, claimNo, 0, "Auto Store File", supplyOrderSummaryDto.getApprvScrutinizingUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                }
                UserDto user1 = new UserDto();
                user1.setUserId(supplyOrderSummaryDto.getApprvScrutinizingUserId());
                sendNotificationForJobPendingRte(claimNo, user1, connection, true);
                rtePendingClaimDetailDao.savePendingJobs(connection, claimNo, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR, supplyOrderSummaryDto.getApprvScrutinizingUserId());
                saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getApprvScrutinizingUserId(), message, URL, "#FFFFBF");
            } else {
                saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getApprvScrutinizingUserId(), message, URL);
            }

            saveClaimsLogs(connection, claimNo, user, "Return to Scrutinizing Team from Claim Handler", "Return to Scrutinizing Team [" + supplyOrderSummaryDto.getInputUserId() + "]");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void recall(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        String message = "Your supply order has been recalled. ";
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, supplyOrderRefNo);
            supplyOrderSummaryDao.updateSupplyOrderReturnToSupCoord(connection, supplyOrderRefNo);

            saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getApprvAssignScrutinizingUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, "Recall Supply order", "Recall Supply order");

            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                sendNotificationForJobPendingRte(claimNo, user, connection, false);
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void approvedAndForwardToClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user, String assignClaimHandlerUserId, boolean isUpdated) throws MisynJDBCException {
        String submitUser = user.getUserId();// user.getV_firstname().concat(" ").concat(user.getV_lastname());
        Connection connection = null;
        String message;
        if (user.getAccessUserType() == 27) {
            message = isUpdated ? "DO amended. Please recall DO calculation immediately and proceed again " : "You have received approved supply order from Spare Parts Coordinator ";
        } else {
            message = isUpdated ? "DO amended. Please recall DO calculation immediately and proceed again " : "You have received approved supply order from Scrutinizing Team ";
        }
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String SPTEAMURL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        try {
            connection = getJDBCConnection();
            ClaimCalculationSheetMainDto availableCalSheet = supplyOrderSummaryDao.getAvailableCalSheet(connection, supplyOrderRefNo);
            if (null != availableCalSheet) {
                String messageSPTeam = "DO amended. Please return Calsheet no - [" + availableCalSheet.getCalSheetId() + "] to Claim Handler";
                if (availableCalSheet.getStatus().equals(AppConstant.CAL_SHEET_PAYMENT_APPROVED)) {
                    saveNotification(connection, claimNo, user.getUserId(), availableCalSheet.getSpecialTeamAssignUserId(), messageSPTeam, SPTEAMURL);
                } else {
                    if (availableCalSheet.getStatus().equals(AppConstant.CAL_SHEET_FORWARD_FOR_MOFA_APPROVAL)) {
                        saveNotification(connection, claimNo, user.getUserId(), availableCalSheet.getSpecialTeamMofaAssignUserId(), messageSPTeam, SPTEAMURL);
                    } else {
                        saveNotification(connection, claimNo, user.getUserId(), assignClaimHandlerUserId, message, URL);
                    }
                }
            } else {
                message = "You have received approved supply order from".concat(user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR ? "Spare Parts Coordinator" : "Scrutinizing Team");
                saveNotification(connection, claimNo, user.getUserId(), assignClaimHandlerUserId, message, URL);
            }
            supplyOrderSummaryDao.updateSupplyOrderScrTeamApprovedAndForwardClaimHandler(connection, supplyOrderRefNo, user.getUserId(), Utility.sysDateTime());
            saveClaimsLogs(connection, claimNo, user, isUpdated ? "Amended DO Approved and Forward to Claim Handler" : "Approved and Forward to Claim Handler", "Approved and Forward to Claim Handler - "
                    .concat("[").concat(assignClaimHandlerUserId).concat("]"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToSparePartsCoordinatorForGeneratingLetter(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException {
        String submitUser = user.getUserId();// user.getV_firstname().concat(" ").concat(user.getV_lastname());
        Connection connection = null;
        String message = "You have received  supply order to generating a report ";
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, supplyOrderRefNo);
            supplyOrderSummaryDao.updateSupplyOrderForwardAndGenerate(connection, supplyOrderRefNo, submitUser);

            claimHandlerDao.updateStoreStatus(connection, claimNo, AppConstant.NO);
            saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getInputUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, "Forward to spare parts coordinator", "Forward to spare parts coordinator - "
                    .concat("[").concat(supplyOrderSummaryDto.getInputUserId()).concat("]"));
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Integer getMaxSupplyRefNo(Integer claimNo) {
        Integer maxSupplyOrderRefNo = 0;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            maxSupplyOrderRefNo = supplyOrderSummaryDao.getMaxSupplyOrderRefNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return maxSupplyOrderRefNo;
    }

    @Override
    public List<SupplyOrderSummaryDto> searchClaimSupplyOrderSummary(Integer claimNo, Integer refNo) {
        Connection connection = null;
        List<SupplyOrderSummaryDto> list = null;
        try {
            connection = getJDBCConnection();
            list = supplyOrderSummaryDao.searchClaimSupplyOrderSummary(connection, claimNo, refNo);
            for (SupplyOrderSummaryDto supplyOrderSummaryDto : list) {
                if (claimCalculationSheetMainDao.isHavingVoucherGeneratedCalsheetForDo(connection, supplyOrderSummaryDto.getSupplyOrderRefNo())) {
                    supplyOrderSummaryDto.setVoucherGenerated(AppConstant.YES);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return list;
    }

    @Override
    public void updateSupplyOrderGenerate(Integer claimNo, Integer supplyOrderRefNo, String inputUser, String claimHandlerUser, ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        String submitUser = user.getUserId();// user.getV_firstname().concat(" ").concat(user.getV_lastname());
        Connection connection = null;
        String message = "Delivery Order Generated.";
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            SupplyOrderSummaryDto supplyOrderSummaryDto = new SupplyOrderSummaryDto();
            supplyOrderSummaryDto.setSupplyOrderRefNo(supplyOrderRefNo);
            supplyOrderSummaryDto.setGenerateUserId(submitUser);
            supplyOrderSummaryDto.setGenerateDateTime(Utility.sysDateTime());
            supplyOrderSummaryDto.setIsGenerate(AppConstant.YES);
            supplyOrderSummaryDao.updateSupplyOrderGenerate(connection, supplyOrderSummaryDto);
            supplyOrderRequestDao.updateDoRequest(connection, claimNo, AppConstant.ZERO_INT);
            claimHandlerDao.updateSupplierOrderAssignStatus(connection, claimNo, AppConstant.NO);
            saveSparePartDatabase(connection, supplyOrderRefNo, claimNo, claimHandlerDto, inputUser);
//            saveNotification(connection, claimNo, inputUser, claimHandlerUser, message, URL);
            saveClaimsLogs(connection, claimNo, user, "Generate supply order", "Generate supply order");
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<VatRateDto> getVateRateList() {
        Connection connection = null;
        List<VatRateDto> list = null;
        try {
            connection = getJDBCConnection();
            list = supplyOrderSummaryDao.getVateRateList(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return list;
    }

    @Override
    public void returnToClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user, boolean isAriRequestNReturn) throws Exception {
        String submitUser = user.getUserId();// user.getV_firstname().concat(" ").concat(user.getV_lastname());
        Connection connection = null;
        String message = "Supply order return by Spare Parts Coordinator ";
        //  String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(supplyOrderRefNo))
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimHandlerDao.updateSupplierOrderAssignDetails(connection, claimNo, null, AppConstant.NO, AppConstant.DEFAULT_DATE_TIME);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);

            if (AppConstant.FORWARD_TO_ENGINEER.equals(claimHandlerDto.getClaimStatus())) {
                throw new ErrorMsgException("Forward", "Return failed! Claim file is already forwarded to Engineer");
            }

            saveNotification(connection, claimNo, user.getUserId(), claimHandlerDto.getAssignUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, "Supply order return by Spare Parts Coordinator", "Supply order return by Spare Parts Coordinator - "
                    .concat("[").concat(claimHandlerDto.getAssignUserId()).concat("]"));
            saveClaimProcessFlow(connection, claimNo, 0, "Claim File has been return to[" + claimHandlerDto.getAssignUserId() + "]", user.getUserId(), Utility.sysDateTime(), claimHandlerDto.getAssignUserId(), AppConstant.NO);
            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo) && !isAriRequestNReturn) {
                sendNotificationForJobPendingRte(claimNo, user, connection, false);
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }
            commitTransaction(connection);
        } catch (ErrorMsgException e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new ErrorMsgException(e.getField(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isPendingSupplyOrder(Integer claimNoToSearch) throws Exception {
        boolean isPending = false;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            isPending = supplyOrderSummaryDao.isPendingSupplyOrder(connection, claimNoToSearch);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return isPending;
    }

    @Override
    public void updateSupplyOrder(SupplyOrderSummaryDto supplyOrderSummaryDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            if (supplyOrderSummaryDao.isGenerated(connection, supplyOrderSummaryDto.getSupplyOrderRefNo())) {
                supplyOrderHistoryDao.shiftDoToHistory(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
                supplyOrderHistoryDao.shiftDoDetailsToHistory(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
            }

            supplyOrderSummaryDto.setSupplyOrderStatus(SupplyOrderStatusEnum.UPDATE.getSupplyOrderStatusEnum());
            supplyOrderSummaryDto.setApprvScrutinizingUserId(AppConstant.STRING_EMPTY);
            supplyOrderSummaryDto.setApprvScrutinizingDateTime(AppConstant.DEFAULT_DATE_TIME);
            supplyOrderSummaryDto.setApprvClaimHandlerUserId(AppConstant.STRING_EMPTY);
            supplyOrderSummaryDto.setApprvClaimHandlerDateTime(AppConstant.DEFAULT_DATE_TIME);
            supplyOrderSummaryDto.setIsUpdated(AppConstant.YES);
            supplyOrderSummaryDto.setGenerateUserId(AppConstant.STRING_EMPTY);
            supplyOrderSummaryDto.setGenerateDateTime(AppConstant.DEFAULT_DATE_TIME);
            supplyOrderSummaryDto.setIsGenerate(AppConstant.NO);
            if (user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM) {
                supplyOrderSummaryDto.setInputUserId(AppConstant.STRING_EMPTY);
                supplyOrderSummaryDto.setInputDateTime(AppConstant.DEFAULT_DATE_TIME);
            }
            SupplyOrderSummaryDto supplyOrderSummaryDto1 = supplyOrderSummaryDao.updateMaster(connection, supplyOrderSummaryDto);
            if (supplyOrderSummaryDto1 != null) {
                List<SupplyOrderDetailsDto> supplyOrderDetailsDtoList = supplyOrderSummaryDto.getSupplyOrderDetailsDtoList();
                supplyOrderDetailsDao.deleteSupplyOrderDetailsRefNo(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
                for (SupplyOrderDetailsDto supplyOrderDetailsDto : supplyOrderDetailsDtoList) {
                    supplyOrderDetailsDto.setSupplyOrderRefNo(supplyOrderSummaryDto1.getSupplyOrderRefNo());
                    supplyOrderDetailsDao.insertMaster(connection, supplyOrderDetailsDto);
                }
                saveClaimsLogs(connection, supplyOrderSummaryDto.getClaimNo(), user, "Supply Order Updated", "Supply Order ref no " + supplyOrderSummaryDto.getSupplyOrderRefNo() + " has been Updated by " + user.getUserId());
            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnScrTeamSupplyOrderBySPC(Integer claimNo, Integer refNo, UserDto user, boolean isRecall) throws Exception {
        Connection connection = null;
        String message = isRecall ? "Amended Supply Order has Recalled by Scrutinizing User" : "Amended Supply Order has Returned from Spare Parts Coordinator";
        String type = isRecall ? "Amended Supply Order Recalled by Scrutinizing User from Spare Parts Coordinator" : "Amended Supply Order Returned to Scrutinizing Team from Spare Parts Coordinator";
        String logMessage = isRecall ? "Amended Supply Order Recalled By [" : "Amended Supply Order Returned to Scrutinizing Team [";
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(refNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, refNo);
            supplyOrderSummaryDao.updateSupplyOrderReturnToScrTeam(connection, isRecall ? SupplyOrderStatusEnum.RECALLED_SCRUTINIZING_TEAM : SupplyOrderStatusEnum.UPDATE, refNo);
            saveNotification(connection, claimNo, user.getUserId(), isRecall ? supplyOrderSummaryDto.getApproveAssignSparePartCoordinator() : supplyOrderSummaryDto.getApprvAssignScrutinizingUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, type, logMessage.concat(supplyOrderSummaryDto.getApprvAssignScrutinizingUserId() + "]"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToSparePartsCoordinatorForApproval(Integer claimNo, Integer refNo, UserDto user) throws Exception {
        Connection connection = null;
        String message = "You have received amended supply order from Scrutinizing Team ";
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(refNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, refNo);
            if (null != supplyOrderSummaryDto.getApproveAssignSparePartCoordinator() && !supplyOrderSummaryDto.getApproveAssignSparePartCoordinator().isEmpty()) {
                supplyOrderSummaryDao.updateSupplyOrderSpcoodDetails(connection, refNo, SupplyOrderStatusEnum.FORWARD_SPARE_PARTS_COORDINATOR, supplyOrderSummaryDto.getApproveAssignSparePartCoordinator());

                saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getApproveAssignSparePartCoordinator(), message, URL);
                saveClaimsLogs(connection, claimNo, user, "Forward Amended DO To Spare Parts Coordinator by Scrutinizing Team", "Forward To Spare Parts Coordinator - "
                        .concat("[").concat(supplyOrderSummaryDto.getApproveAssignSparePartCoordinator()).concat("]"));
            } else {
                throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User Not Found");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void approveAndForwardToScrutinizingTeam(Integer claimNo, Integer refNo, UserDto user) throws Exception {
        Connection connection = null;
        String message = "You have received amended supply order from Spare Parts Coordinator ";
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(refNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, refNo);
            supplyOrderSummaryDao.updateApproveAndForwardBySPC(connection, refNo, SupplyOrderStatusEnum.APPROVED_UPDATE.getSupplyOrderStatusEnum(), supplyOrderSummaryDto.getApprvAssignScrutinizingUserId(), supplyOrderSummaryDto.getApproveAssignSparePartCoordinator());
            saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getApprvAssignScrutinizingUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, "Forward Amended DO To Scrutinizing Team by Spare Parts Coordinator", "Forward To Scrutinizing Team User - "
                    .concat("[").concat(supplyOrderSummaryDto.getApprvAssignScrutinizingUserId()).concat("]"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void forwardToScrutinizingTeam(Integer claimNo, Integer refNo, UserDto user) throws Exception {
        Connection connection = null;
        String message = "You have received amended supply order from Spare Parts Coordinator";
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(refNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, refNo);
            supplyOrderSummaryDao.updateSupplyOrderScrTeamDetails(connection, refNo, SupplyOrderStatusEnum.APPROVED_SPARE_PARTS_COORDINATOR, supplyOrderSummaryDto.getApprvAssignScrutinizingUserId(), Utility.sysDateTime());
            saveNotification(connection, claimNo, user.getUserId(), supplyOrderSummaryDto.getApprvAssignScrutinizingUserId(), message, URL);
            saveClaimsLogs(connection, claimNo, user, "Forward Amended DO To Scrutinizing Team by Spare Parts Coordinator", "Forward To Scrutinizing Team User - "
                    .concat("[").concat(supplyOrderSummaryDto.getApprvAssignScrutinizingUserId()).concat("]"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnSpcoodSupplyOrderByScrutinizing(Integer claimNo, Integer refNo, UserDto user, boolean isRecall) throws Exception {
        Connection connection = null;
        String message = isRecall ? "Amended Supply Order has Recalled by Spare Parts Coordinator" : "Amended Supply Order has Returned from Scrutinizing User";
        String type = isRecall ? "Amended Supply Order Recalled by Spare Parts Coordinator from Scrutinizing User" : "Amended Supply Order Returned to Spare Parts Coordinator from Scrutinizing User";
        String logMessage = isRecall ? "Amended Supply Order Recalled By [" : "Amended Supply Order Returned to Spare Parts Coordinator [";
        String URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(refNo))
                .concat("&TYPE=4")
                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, refNo);
            supplyOrderSummaryDao.updateSupplyOrderReturnToScrTeam(connection, isRecall ? SupplyOrderStatusEnum.RECALLED_SPARE_PARTS_COORDINATOR : SupplyOrderStatusEnum.UPDATE, refNo);
            saveNotification(connection, claimNo, user.getUserId(), isRecall ? supplyOrderSummaryDto.getApprvAssignScrutinizingUserId() : supplyOrderSummaryDto.getApproveAssignSparePartCoordinator(), message, URL);
            saveClaimsLogs(connection, claimNo, user, type, logMessage.concat(supplyOrderSummaryDto.getInputUserId() + "]"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void approvedAndForwardClaimHandlerSupplyOrderAfterUpdate(Integer claimNo, Integer refNo, UserDto user, String assignUserId) throws Exception {
        Connection connection = null;
        String message = "You have received amended supply order from Scrutinizing Team ";
        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&N_REF_NO=").concat(Integer.toString(refNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_SUPPLY_ORDER));
        String SPTEAMURL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        try {
            connection = getJDBCConnection();
            SupplyOrderSummaryDto supplyOrderSummaryDto = supplyOrderSummaryDao.searchMaster(connection, refNo);
            supplyOrderSummaryDao.supplyOrderUpprovedAndForwardToClaimHandler(connection, refNo, supplyOrderSummaryDto.getApprvAssignScrutinizingUserId(), supplyOrderSummaryDto.getApproveAssignSparePartCoordinator());

            ClaimCalculationSheetMainDto availableCalSheet = supplyOrderSummaryDao.getAvailableCalSheet(connection, supplyOrderSummaryDto.getSupplyOrderRefNo());
            if (null != availableCalSheet) {
                String messageSPTeam = "DO amended. Please return Calsheet no - [" + availableCalSheet.getCalSheetId() + "] to Claim Handler";
                if (availableCalSheet.getStatus().equals(AppConstant.CAL_SHEET_PAYMENT_APPROVED)) {
                    saveNotification(connection, claimNo, user.getUserId(), availableCalSheet.getSpecialTeamAssignUserId(), messageSPTeam, SPTEAMURL);
                } else {
                    if (availableCalSheet.getStatus().equals(AppConstant.CAL_SHEET_FORWARD_FOR_MOFA_APPROVAL)) {
                        saveNotification(connection, claimNo, user.getUserId(), availableCalSheet.getSpecialTeamMofaAssignUserId(), messageSPTeam, SPTEAMURL);
                    } else {
                        saveNotification(connection, supplyOrderSummaryDto.getClaimNo(), user.getUserId(), availableCalSheet.getAssignUserId(), "DO amended. Please recall DO calculation immediately and proceed again. Calsheet No - [" + availableCalSheet.getCalSheetId() + "]", URL);
                    }
                }
            } else {
                saveNotification(connection, claimNo, user.getUserId(), assignUserId, message, URL);
                saveClaimsLogs(connection, claimNo, user, "Amended Supply Order Approved and Forward to Claim Handler", "Approved and Forward to Claim Handler - "
                        .concat("[").concat(assignUserId).concat("]"));
            }
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<SupplyOrderSummaryDto> searchClaimSupplyOrderSummaryPending(Integer claimNo, Integer refNo) {
        Connection connection = null;
        List<SupplyOrderSummaryDto> list = null;
        try {
            connection = getJDBCConnection();
            list = supplyOrderSummaryDao.searchClaimSupplyOrderSummaryPending(connection, claimNo, refNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public Integer isDoAssigned(Integer claimNo) throws Exception {
        Connection connection = null;
        Integer refNo;
        try {
            connection = getJDBCConnection();
            refNo = supplyOrderRequestDao.getPendingDO(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return refNo;
    }

    @Override
    public String getPolicyChannelType(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return supplyOrderSummaryDao.getPolicyChannelType(connection, AppConstant.ZERO_INT, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public SupplyOrderSummaryDto getActiveSupplyOrderDetails(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return supplyOrderSummaryDao.getOngoingSupplyOrder(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Boolean isPendingBillForDeliveryOrderApprovedDocument(Integer claimNo, Integer documentTypeId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return supplyOrderSummaryDao.isPendingBillForDeliveryOrderApprovedDocument(connection, claimNo, documentTypeId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void saveSparePartDatabase(Connection connection, Integer supplyOrderRefNo, Integer claimNo, ClaimHandlerDto claimHandlerDto, String userId) throws Exception {
        try {
            ClaimsDto claimsDto = claimHandlerDto.getClaimsDto();
            PolicyDto policyDto;
            if (claimsDto != null) {
                policyDto = claimsDto.getPolicyDto();
                if (policyDto != null) {
                    SupplyOrderSummaryDto supplyOrderSummaryDto = this.getSupplyOrderSummaryDtoBySupplyOrderRefNo(connection, supplyOrderRefNo, claimNo);
                    for (SupplyOrderDetailsDto supplyOrderDetailsDto : supplyOrderSummaryDto.getSupplyOrderDetailsDtoList()) {
                        SparePartDatabaseDto sparePartDatabaseDto = new SparePartDatabaseDto();
                        sparePartDatabaseDto.setVehicleMake(policyDto.getVehicleMake());
                        sparePartDatabaseDto.setVehicleModel(policyDto.getVehicleModel());
                        sparePartDatabaseDto.setManufactureYear(policyDto.getManufactYear());
                        sparePartDatabaseDto.setSparePartRefNo(supplyOrderDetailsDto.getSparePartRefNo());
                        sparePartDatabaseDto.setSupplierId(supplyOrderDetailsDto.getSupplyOrderRefNo());
                        sparePartDatabaseDto.setPrice(supplyOrderDetailsDto.getIndividualPrice());
                        sparePartDatabaseDto.setProceedDate(Utility.sysDate());
                        sparePartDatabaseDto.setInputDateTime(Utility.sysDateTime());
                        sparePartDatabaseDto.setInputUserId(userId);
                        sparePartDatabaseDao.insertMaster(connection, sparePartDatabaseDto);

                    }
                }

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }
}
