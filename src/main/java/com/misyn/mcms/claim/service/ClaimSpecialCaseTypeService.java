package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimSpecialCaseTypeDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface ClaimSpecialCaseTypeService {
    ClaimSpecialCaseTypeDto saveClaimSpecialCaseType(ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws MisynJDBCException;

    ClaimSpecialCaseTypeDto updateClaimSpecialCaseType(ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws MisynJDBCException;

    ClaimSpecialCaseTypeDto getClaimSpecialCaseType(Integer id) throws Exception;

    List<ClaimSpecialCaseTypeDto> getClaimSpecialCaseTypeList() throws Exception;

    void deleteClaimSpecialCaseType(Integer id, String userName) throws Exception;

    Boolean isExistClaimNo(String claimNo) throws Exception;

    DataGridDto getClaimSpecialCaseTypeSDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch);
}
