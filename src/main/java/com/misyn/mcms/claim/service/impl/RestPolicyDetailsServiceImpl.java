package com.misyn.mcms.claim.service.impl;

import com.google.gson.Gson;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.list.*;
import com.misyn.mcms.claim.service.RestPolicyDetailsService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Parameters;
import jakarta.ws.rs.client.ClientBuilder;
import jakarta.ws.rs.client.InvocationCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
public class RestPolicyDetailsServiceImpl implements RestPolicyDetailsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RestPolicyDetailsServiceImpl.class);
    private String REST_SERVICE_URL = Parameters.getSyncAppUrl().concat("policy/");
    private String REST_COVER_URL = Parameters.getSyncAppUrl().concat("cover/");

    @Override
    public List<CoverDto> getCoverDtoList(PolicyDto policyDto, String policyChannelType) {
        CoverDtoList coverDtoList = new CoverDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("cover/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            coverDtoList = gson.fromJson(entityFuture.get(), CoverDtoList.class);
            setCallAndGoCoverDetail(policyDto, coverDtoList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return coverDtoList.getResults();
    }


    private void setCallAndGoCoverDetail(PolicyDto policyDto, CoverDtoList coverDtoList) {
        for (CoverDto coverDto : coverDtoList.getResults()) {
            if (null != coverDto.getCoverDesc() && coverDto.getCoverDesc().startsWith(AppConstant.CALL_GO_COVER)) {
                policyDto.setCallAndGoDescription(true);
                break;
            }
        }

    }

    @Override
    public List<ExcessDto> getExcessDtoList(PolicyDto policyDto, String policyChannelType) {
        BigDecimal totalExcess = new BigDecimal("0.00");
        ExcessDtoList excessDtoList = new ExcessDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("excess/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            excessDtoList = gson.fromJson(entityFuture.get(), ExcessDtoList.class);
            if (excessDtoList != null) {
                for (ExcessDto excessDtos : excessDtoList.getResults()) {
                    totalExcess = totalExcess.add(excessDtos.getExcessAmount());
                }
                policyDto.setExcess(totalExcess);
            }

        } catch (Exception e) {
            policyDto.setExcess(totalExcess);
            LOGGER.error(e.getMessage(), e);
        }
        return excessDtoList.getResults();
    }

    @Override
    public List<PaidDetailsDto> getPaidDetailsDtoList(PolicyDto policyDto, String policyChannelType) {
        PaidDetailsDtoList paidDetailsDtoList = new PaidDetailsDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("paid-details/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            //  LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            paidDetailsDtoList = gson.fromJson(entityFuture.get(), PaidDetailsDtoList.class);
            BigDecimal tot = BigDecimal.ZERO;
            for (PaidDetailsDto paidDetailsDto :
                    paidDetailsDtoList.getResults()) {
                if (null != paidDetailsDto.getPaidAmount()) {
                    tot = tot.add(paidDetailsDto.getPaidAmount());
                }
            }
            policyDto.setPaidTotalAmount(tot);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return paidDetailsDtoList.getResults();
    }

    @Override
    public List<PolicyMemoDto> getPolicyMemoDtoList(PolicyDto policyDto, String policyChannelType) {
        PolicyMemoDtoList policyMemoDtoList = new PolicyMemoDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("policy-memo/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            //   LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            policyMemoDtoList = gson.fromJson(entityFuture.get(), PolicyMemoDtoList.class);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return policyMemoDtoList.getResults();
    }

    @Override
    public PremiumBreakupFormDto getPremiumBreakupFormDto(PolicyDto policyDto, String policyChannelType) {
        BigDecimal annualPremium = BigDecimal.ZERO.setScale(2, RoundingMode.CEILING);
        PremiumBreakupFormDto premiumBreakupFormDto = new PremiumBreakupFormDto();
        List<PolicyPremiumDto> policyPremiumRiotStrikeList = new ArrayList<>();
        List<PolicyPremiumDto> list = new ArrayList<>();

        PolicyPremiumDtoList policyPremiumDtoList = new PolicyPremiumDtoList();
        ChargesBreakupDtoList chargesBreakupDtoList = new ChargesBreakupDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture1 = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("premium-breakup/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            //  LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            policyPremiumDtoList = gson.fromJson(entityFuture1.get(), PolicyPremiumDtoList.class);
            for (PolicyPremiumDto policyPremiumDto : policyPremiumDtoList.getResults()) {
                switch (policyPremiumDto.getSecCode().trim()) {
                    case "TC":
                    case "SRCC":
                        policyPremiumRiotStrikeList.add(policyPremiumDto);
                        break;
                    default:
                        list.add(policyPremiumDto);
                }
                annualPremium = annualPremium.add(policyPremiumDto.getPremiumAmount() == null ? BigDecimal.ZERO : policyPremiumDto.getPremiumAmount());
            }


            final Future<String> entityFuture2 = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("charges-breakup/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            //LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            chargesBreakupDtoList = gson.fromJson(entityFuture2.get(), ChargesBreakupDtoList.class);
            for (ChargesBreakupDto chargesBreakupDto : chargesBreakupDtoList.getResults()) {
                premiumBreakupFormDto.setChargesBreakupDto(chargesBreakupDto);
                annualPremium = annualPremium
                        .add(chargesBreakupDto.getPof() == null ? BigDecimal.ZERO : chargesBreakupDto.getPof())
                        .add(chargesBreakupDto.getSd() == null ? BigDecimal.ZERO : chargesBreakupDto.getSd())
                        .add(chargesBreakupDto.getCess() == null ? BigDecimal.ZERO : chargesBreakupDto.getCess())
                        .add(chargesBreakupDto.getRt() == null ? BigDecimal.ZERO : chargesBreakupDto.getRt())
                        .add(chargesBreakupDto.getNbt() == null ? BigDecimal.ZERO : chargesBreakupDto.getNbt())
                        .add(chargesBreakupDto.getVat() == null ? BigDecimal.ZERO : chargesBreakupDto.getVat());
            }
            premiumBreakupFormDto.setPolicyPremiumDtoList(list);
            premiumBreakupFormDto.setPolicyPremiumRiotStrikeList(policyPremiumRiotStrikeList);
            policyDto.setAnnualPremium(annualPremium);


        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return premiumBreakupFormDto;
    }

    @Override
    public List<SellingAgentDetailsDto> getSellingAgentDetailsDtoList(PolicyDto policyDto, String policyChannelType) {
        SellingAgentDetailsDtoList sellingAgentDetailsDtoList = new SellingAgentDetailsDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("selling-agent/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            sellingAgentDetailsDtoList = gson.fromJson(entityFuture.get(), SellingAgentDetailsDtoList.class);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return sellingAgentDetailsDtoList.getResults();
    }

    @Override
    public List<EndorsementHistoryDto> getEndorsementHistoryDtoList(PolicyDto policyDto, String policyChannelType) {
        EndorsementHistoryDtoList endorsementHistoryDtoList = new EndorsementHistoryDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("endorsement-history/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            endorsementHistoryDtoList = gson.fromJson(entityFuture.get(), EndorsementHistoryDtoList.class);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return endorsementHistoryDtoList.getResults();
    }

    @Override
    public List<BillingInfoDto> getBillingInfoDtoList(PolicyDto policyDto, String policyChannelType) {
        BillingInfoDtoList billingInfoDtoList = new BillingInfoDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("billing-info/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            billingInfoDtoList = gson.fromJson(entityFuture.get(), BillingInfoDtoList.class);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return billingInfoDtoList.getResults();
    }

    @Override
    public List<LearnerDriverDetailsDto> getLearnerDriverDetailsDtoList(PolicyDto policyDto, String policyChannelType) {
        LearnerDriverDetailsDtoList learnerDriverDetailsDtoList = new LearnerDriverDetailsDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("learn-drive-detail/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            //  LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            learnerDriverDetailsDtoList = gson.fromJson(entityFuture.get(), LearnerDriverDetailsDtoList.class);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return learnerDriverDetailsDtoList.getResults();
    }

    @Override
    public PolicySellingAgentDetailsDto getPolicySellingAgentDetailsDto(PolicyDto policyDto, String policyChannelType) {
        PolicySellingAgentDetailsDto policySellingAgentDetailsDto = new PolicySellingAgentDetailsDto();
        Gson gson = new Gson();
        try {
            if (!policyDto.getAgentCode().isEmpty() && null != policyDto.getAgentCode()) {
                final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                        .path(policyChannelType + "/")
                        .path("policy-selling-agent-detail/")
                        //.path("LOLCNEG0001")
                        .path(policyDto.getAgentCode())
                        .request().async().get(new InvocationCallback<String>() {
                            @Override
                            public void completed(String response) {
                                //  LOGGER.info(response);
                            }

                            @Override
                            public void failed(Throwable throwable) {
                                LOGGER.error("Invocation failed.");
                            }
                        });

                policySellingAgentDetailsDto = gson.fromJson(entityFuture.get(), PolicySellingAgentDetailsDto.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return policySellingAgentDetailsDto;
    }

    @Override
    public IntroducerDto getIntroducerDetailsDto(PolicyDto policyDto, String policyChannelType) {
        IntroducerDto introducerDto = new IntroducerDto();
        Gson gson = new Gson();
        try {
            if (!policyDto.getIntroducer().isEmpty() && null != policyDto.getIntroducer()) {
                final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                        .path(policyChannelType + "/")
                        .path("introducer-detail/")
                        .path(policyDto.getIntroducer())
                        .request().async().get(new InvocationCallback<String>() {
                            @Override
                            public void completed(String s) {

                            }

                            @Override
                            public void failed(Throwable throwable) {
                                LOGGER.error("Invocation failed.");
                            }
                        });
                introducerDto = gson.fromJson(entityFuture.get(), IntroducerDto.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return introducerDto;
    }

    @Override
    public List<CweDetailDto> getCweDetail(PolicyDto policyDto, String policyChannelType) {
        CweDetailDtoList cweDetailDtoList = new CweDetailDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("cwe-detail/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {

                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                        }
                    });
            cweDetailDtoList = gson.fromJson(entityFuture.get(), CweDetailDtoList.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return cweDetailDtoList.getResults();
    }

    @Override
    public List<TradePlateDetailDto> getTradePlateDetail(String policyNumber, String policyChannelType) {
        TradePlateDetailDtoList tradePlateDetailList = new TradePlateDetailDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("tradePlate-detail/")
                    .path(policyNumber)
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {

                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                        }
                    });
            tradePlateDetailList = gson.fromJson(entityFuture.get(), TradePlateDetailDtoList.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return tradePlateDetailList.getResults();
    }

    @Override
    public TrailerDetailDto getTrailerDetail(String policyNumber, String policyChannelType) {
        TrailerDetailDto trailerDetailDto = new TrailerDetailDto();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("trailer-detail/")
                    .path(policyNumber)
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {

                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                        }
                    });
            trailerDetailDto = gson.fromJson(entityFuture.get(), TrailerDetailDto.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return trailerDetailDto;
    }

    @Override
    public IntroducerDto getIntroducerDetail(PolicyDto policyDto, String policyChannelType) {
        IntroducerDto introducerDto = new IntroducerDto();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("introduce-detail-list/")
                    .path(policyDto.getAgentCode())
                    .path("/" + policyDto.getPolicyNumber())
                    .path("/" + policyDto.getIntroducer())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {

                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });
            introducerDto = gson.fromJson(entityFuture.get(), IntroducerDto.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return introducerDto;
    }

    @Override
    public LargeClaimDto getLargeClaimDetailsDtoList(PolicyDto policyDto, String policyChannelType) {
        LargeClaimDto largeClaimDto = new LargeClaimDto();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("large-claim/")
                    .path(policyDto.getIsfClaimNo())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            //  LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            largeClaimDto = gson.fromJson(entityFuture.get(), LargeClaimDto.class);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return largeClaimDto;
    }

    @Override
    public CoInsOrFacDetailDto getCoInsOrFacDetail(PolicyDto policyDto, String policyChannelType) {
        CoInsOrFacDetailDto coInsOrFacDetailDto = new CoInsOrFacDetailDto();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("isCoInsOrFac/")
                    .path(policyDto.getIsfClaimNo())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            //  LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });
            coInsOrFacDetailDto = gson.fromJson(entityFuture.get(), CoInsOrFacDetailDto.class);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return coInsOrFacDetailDto;
    }

    @Override
    public List<PolicyBenefitDetailsDto> getBenefitCategoryDtoList(PolicyDto policyDto, String policyChannelType) {
        PolicyBenefitDetailsDtoList policyBenefitDetailsDtoList = new PolicyBenefitDetailsDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_COVER_URL)
                    .path(policyChannelType + "/")
                    .path("benefit-detail/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            policyBenefitDetailsDtoList = gson.fromJson(entityFuture.get(), PolicyBenefitDetailsDtoList.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return policyBenefitDetailsDtoList.getResults();
    }

    @Override
    public List<CweDetailDto> getCweCategoryDtoList(PolicyDto policyDto, String policyChannelType) {
        CweDetailDtoList cweDetailDtoList = new CweDetailDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_COVER_URL)
                    .path(policyChannelType + "/")
                    .path("cwe-detail/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            cweDetailDtoList = gson.fromJson(entityFuture.get(), CweDetailDtoList.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return cweDetailDtoList.getResults();
    }

    @Override
    public List<PolicyOthChargesDetailsDto> getOthChargesCategoryDtoList(PolicyDto policyDto, String policyChannelType) {
        PolicyOthChargesDetailsDtoList policyOthChargesDetailsDtoList = new PolicyOthChargesDetailsDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_COVER_URL)
                    .path(policyChannelType + "/")
                    .path("othcharges-detail/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            policyOthChargesDetailsDtoList = gson.fromJson(entityFuture.get(), PolicyOthChargesDetailsDtoList.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return policyOthChargesDetailsDtoList.getResults();
    }

    @Override
    public List<PolicySrcctcDetailsDto> getSrcctcCategoryDtoList(PolicyDto policyDto, String policyChannelType) {
        PolicySrcctcDetailsDtoList policySrcctcDetailsDtoList = new PolicySrcctcDetailsDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_COVER_URL)
                    .path(policyChannelType + "/")
                    .path("srcctc-detail/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            policySrcctcDetailsDtoList = gson.fromJson(entityFuture.get(), PolicySrcctcDetailsDtoList.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return policySrcctcDetailsDtoList.getResults();
    }

    @Override
    public List<PolicyMemoDto> getMemoCategoryDtoList(PolicyDto policyDto, String policyChannelType) {
        PolicyMemoDtoList policyMemoDtoList = new PolicyMemoDtoList();
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_COVER_URL)
                    .path(policyChannelType + "/")
                    .path("memo-detail/")
                    .path(policyDto.getPolicyNumber())
                    .path("/" + policyDto.getRenCount())
                    .path("/" + policyDto.getEndCount())
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            // LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");

                        }
                    });

            policyMemoDtoList = gson.fromJson(entityFuture.get(), PolicyMemoDtoList.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return policyMemoDtoList.getResults();
    }

}



/*
*
*
*
*   @Override
    public List<PolicyBaseCategoryDataDto> getPolicyServiceCategoryDataDtoList(PolicyDto policyDto, String policyChannelType) {
        List<PolicyBaseCategoryDataDto> serviceList = new ArrayList<>();

        serviceList.add(new PolicyServiceCategoryDataDto("1", "BEN1", "Service Description", "200", "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Adipisci amet debitis distinctio doloremque, ducimus eos minima molestias necessitatibus nesciunt numquam perferendis, quod quos recusandae rem repellat sequi similique soluta? Accusamus, animi blanditiis consequuntur deserunt dignissimos enim esse et exercitationem fugit id illum ipsa iusto molestiae nam nisi non odit, optio quam quibusdam quos recusandae reiciendis sapiente sequi ut velit? Autem dignissimos doloribus dolorum esse, est perspiciatis quisquam reprehenderit sapiente ut voluptate. Accusamus amet aperiam, autem corporis deserunt dicta, libero magni modi pariatur quaerat quas quos tempora. Ad dolorem eveniet impedit iure iusto mollitia nulla perferendis quam, reprehenderit saepe, sunt, unde.",false));
        serviceList.add(new PolicyServiceCategoryDataDto("2", "BEN2", "Service Description", "250", " Lorem ipsum dolor sit.",false));
        serviceList.add(new PolicyServiceCategoryDataDto("2", "BEN3", "Service Description", "400", " Lorem ipsum dolor sit amet, consectetur adipisicing elit. Molestiae, nemo.",true));
        serviceList.add(new PolicyServiceCategoryDataDto("1", "CWE1", "Service Description", "700", "Lorem ipsum dolor sit amet, consectetur adipisicing.",true));

        return serviceList;
    }

    @Override
    public List<PolicyBaseCategoryDataDto> getPolicyCoverCategoryDataDtoList(PolicyDto policyDto, String policyChannelType) {
        List<PolicyBaseCategoryDataDto> coverList = new ArrayList<>();
        coverList.add(new PolicyCoverCategoryDataDto("1", "CHR1", "Cover Description", "200", "Lorem ipsum dolor sit amet.",false));
        coverList.add(new PolicyCoverCategoryDataDto("2", "BEN4", "Cover Description", "250", "Lorem ipsum dolor sit.",false));
        coverList.add(new PolicyCoverCategoryDataDto("2", "CHR2", "Cover Description", "400", "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Molestiae, nemo.",true));
        coverList.add(new PolicyCoverCategoryDataDto("1", "CWE2", "Cover Description", "700", "Lorem ipsum dolor sit amet, consectetur adipisicing.",true));
        return coverList;
    }

    @Override
    public List<PolicyBaseCategoryDataDto> getPolicyConditionCategoryDataDtoList(PolicyDto policyDto, String policyChannelType) {
        List<PolicyBaseCategoryDataDto> conditionList = new ArrayList<>();
        conditionList.add(new PolicyConditionCategoryDataDto("1", "CWE3", "Condition Description", "400", "Lorem ipsum dolor sit amet.",false));
        conditionList.add(new PolicyConditionCategoryDataDto("2", "CWE4", "Condition Description", "450", "Lorem ipsum dolor sit.",false));
        conditionList.add(new PolicyConditionCategoryDataDto("2", "BEN5", "Condition Description", "600", "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Molestiae, nemo.",true));
        conditionList.add(new PolicyConditionCategoryDataDto("1", "CHR3", "Condition Description", "900", "Lorem ipsum dolor sit amet, consectetur adipisicing.",true));
        return conditionList;

    }

    @Override
    public List<PolicyBaseCategoryDataDto> getPolicyBenefitCategoryDataDtoList(PolicyDto policyDto, String policyChannelType) {
        List<PolicyBaseCategoryDataDto> benefitList = new ArrayList<>();
        benefitList.add(new PolicyBenefitCategoryDataDto("1", "CHR4", "Benefits Description", "300", "Lorem ipsum dolor sit amet.",false));
        benefitList.add(new PolicyBenefitCategoryDataDto("2", "BEN6", "Benefits Description", "350", "Lorem ipsum dolor sit.",false));
        benefitList.add(new PolicyBenefitCategoryDataDto("2", "BEN7", "Benefits Description", "500", "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Molestiae, nemo.",true));
        benefitList.add(new PolicyBenefitCategoryDataDto("1", "BEN8", "Benefits Description", "800", "Lorem ipsum dolor sit amet, consectetur adipisicing.",true));
        return benefitList;
    }

    @Override
    public List<PolicyBaseCategoryDataDto> getPolicySpecialCategoryDataDtoList(PolicyDto policyDto, String policyChannelType) {
        List<PolicyBaseCategoryDataDto> specialList = new ArrayList<>();
        specialList.add(new PolicySpecialCategoryDataDto("1", "CWE5", "Special Description", "500", "Lorem ipsum dolor sit amet.",false));
        specialList.add(new PolicySpecialCategoryDataDto("2", "CHR5", "Special Description", "550", "Lorem ipsum dolor sit.",false));
        specialList.add(new PolicySpecialCategoryDataDto("2", "CHR6", "Special Description", "700", "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Molestiae, nemo.",true));
        specialList.add(new PolicySpecialCategoryDataDto("1", "CWE6", "Special Description", "1000", "Lorem ipsum dolor sit amet, consectetur adipisicing.",true));
        return specialList;
    }

    @Override
    public List<PolicyMemoCategoryDataDto> getPolicyMemoCategoryDataDtoList(PolicyDto policyDto, String policyChannelType) {
        List<PolicyMemoCategoryDataDto> memoList = new ArrayList<>();
        memoList.add(new PolicyMemoCategoryDataDto("1", "MemoInfo1", "2023-01-01", "ExclusionInfo"," Lorem ipsum dolor sit amet, consectetur adipisicing elit. Omnis, voluptatem.444444444444"));
        memoList.add(new PolicyMemoCategoryDataDto("2", "MemoInfo2", "2023-01-01", "ExclusionInfo","  Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aperiam dicta error expedita."));
        memoList.add(new PolicyMemoCategoryDataDto("2", "MemoInfo2", "2023-01-01", "ExclusionInfo","  Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aperiam dicta error expedita.", true));
        memoList.add(new PolicyMemoCategoryDataDto("1", "MemoInfo3", "2023-01-01", "ExclusionInfo"," Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ab est natus nihil nisi.2222222222", true));
        return memoList;
    }*/
