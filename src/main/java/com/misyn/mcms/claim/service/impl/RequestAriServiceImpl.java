package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.EmailService;
import com.misyn.mcms.claim.service.RequestAriService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
public class RequestAriServiceImpl extends AbstractBaseService<LoggerTrailServiceImpl> implements RequestAriService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RequestAriServiceImpl.class);
    private RequestAriDao requestAriDao = new RequestAriDaoImpl();
    private CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private AssessorAllocationDao assessorAllocationDao = new AssessorAllocationDaoImpl();
    private MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private EmailDao emailDao = new EmailDaoImpl();
    private UserDao userDao = new UserDaoImpl();
    private EmailService emailService = new EmailServiceImpl();
    private AriNotificationDao ariNotificationDao = new AriNotificationDaoImpl();
    private BulkCloseDetailDao bulkCloseDetailDao = new BulkCloseDetailDaoImpl();

    @Override
    public RequestAriDto insert(RequestAriDto requestAriDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, requestAriDto.getClaimNo());
            connection = getJDBCConnection();
            requestAriDto.setInpDateTime(Utility.sysDateTime());
            requestAriDto.setInpUserId(user.getUserId());
            requestAriDto.setAccidentDate(claimsDto.getAccidDate().concat(" ").concat(claimsDto.getAccidTime()));
            requestAriDto.setRequestedDate(Utility.sysDateTime());
            requestAriDto.setStatus(AppConstant.STRING_PENDING);
            requestAriDto = requestAriDao.insertMaster(connection, requestAriDto);
            String message = "Ari Requested to ".concat(requestAriDto.getClaimNo().toString());
            saveNotification(connection, requestAriDto.getClaimNo(), user.getUserId(), claimsDto.getInpUser(), message, AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimsDto.getClaimNo())));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
        return requestAriDto;
    }

    @Override
    public RequestAriDto update(RequestAriDto requestAriDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            RequestAriDto requestAri = requestAriDao.searchByClaimNo(connection, requestAriDto.getClaimNo());
            if (null != requestAri) {
                requestAriDto.setId(requestAri.getId());
                requestAriDto.setStatus(AppConstant.STRING_UPLOAD);
            }
            requestAriDto = requestAriDao.updateMaster(connection, requestAriDto);
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, requestAriDto.getClaimNo());
            if (null != claimsDto) {
                saveNotification(connection, requestAriDto.getClaimNo(), user.getUserId(), claimsDto.getInpUser(), "Final bill received. Arrange ARI.", AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimsDto.getClaimNo())));
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
        return requestAriDto;
    }

    @Override
    public RequestAriDto delete(RequestAriDto requestAriDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public RequestAriDto updateAuthPending(RequestAriDto requestAriDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public RequestAriDto deleteAuthPending(RequestAriDto requestAriDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public RequestAriDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public RequestAriDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public RequestAriDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public RequestAriDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public RequestAriDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<RequestAriDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<RequestAriDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public List<RequestAriDto> searchAll(Integer claimNo) throws Exception {
        List<RequestAriDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = requestAriDao.searchAll(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public RequestAriDto searchByClaimNo(Integer claimNo) throws Exception {
        Connection connection = null;
        RequestAriDto requestAriDto = null;
        try {
            connection = getJDBCConnection();
            requestAriDto = requestAriDao.searchByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return requestAriDto;
    }

    @Override
    public RequestAriDto insert(RequestAriDto requestAriDto, UserDto user, Connection connection) throws Exception {

        try {
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, requestAriDto.getClaimNo());
            requestAriDto.setInpDateTime(Utility.sysDateTime());
            requestAriDto.setInpUserId(user.getUserId());
            requestAriDto.setAccidentDate(claimsDto.getAccidDate().concat(" ").concat(claimsDto.getAccidTime()));
            requestAriDto.setRequestedDate(Utility.sysDateTime());
            requestAriDto.setRequestedUser(user.getUserId());
            requestAriDto.setStatus(AppConstant.STRING_PENDING);
            requestAriDto.setVehicleNo(claimsDto.getVehicleNo());
            requestAriDto = requestAriDao.insertMaster(connection, requestAriDto);
            String message;
//            if (!requestAriDto.getRefNo().isEmpty()) {
//                message = "ARI Requested to  ".concat(requestAriDto.getRefNo());
//            } else {
//                message = "ARI Requested to  ".concat(requestAriDto.getClaimNo().toString());
//            }
            saveClaimsLogs(connection, requestAriDto.getClaimNo(), user, "ARI Request", "Requested ARI from " + user.getUserId());

            // saveNotfication(connection, requestAriDto.getClaimNo(), user.getUserId(), claimsDto.getInpUser(), message, AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimsDto.getClaimNo())));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {

        }
        return requestAriDto;
    }

    @Override
    public DataGridDto getRequestDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        Connection connection = null;
        DataGridDto dataGridDto = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = requestAriDao.getRequestDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public boolean updateStatusAndRevokeByRef(String remark, Integer refId, UserDto user) throws Exception {
        Connection connection = null;
        boolean isUpdated = false;
        try {
            connection = getJDBCConnection();
            RequestAriDto requestAriDto = new RequestAriDto();
            requestAriDto.setId(refId);
            requestAriDto.setRemark(remark);
            requestAriDto.setStatus("R");
            requestAriDto.setRequestedUser(AppConstant.STRING_EMPTY);
            requestAriDto.setRequestedDate(AppConstant.DEFAULT_DATE_TIME);
            requestAriDto.setRevokeUser(user.getUserId());
            requestAriDto.setRevokeDate(Utility.sysDateTime());
            isUpdated = requestAriDao.updateStatusAndRevokeByRef(connection, requestAriDto);
            if (isUpdated) {
                RequestAriDto requestAri = requestAriDao.searchRequestAriDto(connection, refId);
                RequestAriDto requestedPendingAri = requestAriDao.searchByClaimNo(connection, requestAri.getClaimNo());
                saveClaimsLogs(connection, requestAri.getClaimNo(), user, "ARI Revoke", "Revoked ARI from " + user.getUserId());
                saveSpecialRemark(connection, user, requestAri.getClaimNo(), "ARI Revoked", remark);

                if (!AppConstant.FULLY_REVIEW.equals(requestAri.getStatus()) &&
                        !AppConstant.PARTIALLY_REVIEW.equals(requestAri.getStatus()) && requestAri.getRefId() > 0) {
                    AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, requestAri.getRefId());
                    assessorAllocationDao.rejectJobStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), AppConstant.REVOKED_STATUS, requestAri.getRefId());
                    assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), requestAri.getRefId());
                    if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, requestAri.getClaimNo())) {
                        if (!motorEngineerDetailsDao.isAriSalvagePending(connection, requestAri.getClaimNo()) && null == requestedPendingAri) {
                            if (claimHandlerDao.isAutoStored(connection, requestAri.getClaimNo())) {
                                claimHandlerDao.updateStoreStatus(connection, requestAri.getClaimNo(), "N");
                                saveClaimsLogs(connection, requestAri.getClaimNo(), user, "Auto Re-Store File", "Claim File Auto Re-Stored due to Pending ARI/ Salvage Inspection Rejection");
                                saveClaimProcessFlow(connection, requestAri.getClaimNo(), 0, "Auto Re-Store File", "SYSTEM", Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                            }
                            sendNotificationForAssignUser(connection, requestAri.getClaimNo(), user, AppConstant.ZERO_INT);
                        }
                    }
                    if (null != assessorAllocationDto.getAssessorDto().getUserName() && !assessorAllocationDto.getAssessorDto().getUserName().isEmpty()) {
                        String URL = "/InspectionDetailsController/viewEdit?P_N_CLIM_NO=" + requestAri.getClaimNo() + "&P_N_REF_NO=" + requestAri.getRefId();
                        saveNotification(connection, requestAri.getClaimNo(), user.getUserId(), assessorAllocationDto.getAssessorDto().getUserName(), "ARI/ Salvage Inspection: " + assessorAllocationDto.getJobId() + " has been Revoked", URL);
                    }
                    saveClaimsLogs(connection, requestAri.getClaimNo(), user, "ARI/ Salvage Revoked", "Assessor Job " + assessorAllocationDto.getJobId() + " Revoked by " + user.getUserId());
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not be updated");
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    @Override
    public boolean updateStatusByRefId(Integer refNo) throws Exception {
        Connection connection = null;
        boolean isUpdated = false;
        try {
            connection = getJDBCConnection();
            RequestAriDto requestAriDto = new RequestAriDto();
            requestAriDto.setRefId(refNo);
            requestAriDto.setStatus("C");
            isUpdated = requestAriDao.updateStatusByRefId(connection, requestAriDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not be updated");
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    @Override
    public DataGridDto getRequestDataGridDtoPending(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        Connection connection = null;
        DataGridDto dataGridDto = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = requestAriDao.getRequestDataGridDtoPending(connection, parameterList, drawRandomId, start, length, orderType, orderField);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public RequestAriDto searchByClaimNoPending(Integer claimNo) throws Exception {
        Connection connection = null;
        RequestAriDto requestAriDto = null;
        try {
            connection = getJDBCConnection();
            requestAriDto = requestAriDao.searchByClaimNoPending(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return requestAriDto;
    }

    @Override
    public RequestAriDto updateAri(RequestAriDto requestAriDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            RequestAriDto requestAri = requestAriDao.searchByClaimNoPending(connection, requestAriDto.getClaimNo());
            if (null != requestAri) {
                requestAriDto.setId(requestAri.getId());
                if (requestAri.getStatus().equals(AppConstant.STRING_PENDING)) {
                    requestAriDto.setStatus(AppConstant.STRING_UPLOAD);
                } else {
                    requestAriDto.setStatus(requestAri.getStatus());
                }
                requestAriDto.setDocumentUploadUser(user.getUserId());
            }
            requestAriDto = requestAriDao.updateRequestAri(connection, requestAriDto);

            if (ariNotificationDao.isAvailableUnsentNotification(connection, requestAriDto.getClaimNo())) {
                ariNotificationDao.removeAvailableRecordsByClaimNo(connection, requestAriDto.getClaimNo());
            }

            List<UserDto> userList = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE);
            List<UserDto> userListAriOnly = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE);
            userList.addAll(userListAriOnly);
            for (UserDto userDto : userList) {
                ariNotificationDao.saveAriNotification(connection, requestAriDto.getClaimNo(), user.getUserId(), userDto.getUserId());
//                saveNotfication(connection, requestAriDto.getClaimNo(), user.getUserId(), userDto.getUserId(), "Final bill received. Arrange ARI.", AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(requestAriDto.getClaimNo())).concat("&type=").concat("2"));
            }
            saveClaimsLogs(connection, requestAriDto.getClaimNo(), user, "Ari Request", "Final bill received to branch");

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
        return requestAriDto;
    }

    @Override
    public RequestAriRemarkDto saveRemark(RequestAriRemarkDto requestAriRemarkDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            requestAriRemarkDto.setInputUser(user.getUserId());
            requestAriRemarkDto.setInputDatetime(Utility.sysDateTime());
            requestAriRemarkDto.setDepartmentId(AppConstant.ASSESSOR_ALLOCATION_DEPARTMENT);
            requestAriRemarkDto = requestAriDao.saveRequestRemark(connection, requestAriRemarkDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
        return requestAriRemarkDto;
    }

    @Override
    public List<RequestAriRemarkDto> getRemarkListByRemarkId(Integer remarkId) {
        Connection connection = null;
        List<RequestAriRemarkDto> list = null;
        try {
            connection = getJDBCConnection();
            list = requestAriDao.getRemarkListByRemarkId(connection, remarkId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public DataGridDto getRequestDataGridDtoByUser(List<FieldParameterDto> parameterList, int i, int start, int length, String columnOrder, String orderColumnName, boolean isSpCood, String userId) {
        Connection connection = null;
        DataGridDto dataGridDto = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = requestAriDao.getRequestDataGridDtoByUser(connection, parameterList, i, start, length, columnOrder, orderColumnName, isSpCood, userId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public void sendAriPendingMail(Integer id, String reason, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.ARI_PENDING_MAIL);
            email.setEmailMassege(messageContentDetails.getMessageBody());
            RequestAriDto requestAriDto = requestAriDao.searchRequestAriDto(connection, id);


            if (null != requestAriDto) {
                String inspectionTypeDesc = assessorAllocationDao.getInspectionTypeDesc(connection, id);
                UserDto userByUsrid = userDao.getUserByUsrid(connection, requestAriDto.getDocumentUploadUser());
                if (null == userByUsrid.getEmail() || userByUsrid.getEmail().isEmpty()) {
                    Exception exception = new Exception("Fail");
                    throw exception;
                }
                email.setToAddresses(userByUsrid.getEmail());
                ArrayList<String> list = new ArrayList<>();

                list.add(requestAriDto.getVehicleNo());
                list.add(requestAriDto.getClaimNo().toString());
                list.add(requestAriDto.getAccidentDate());
                list.add(inspectionTypeDesc);
                list.add(reason);

                email.setParameterEmail(list);
                String subject = "Vehicle No :-".concat(requestAriDto.getVehicleNo()).concat(",Claim No :-").
                        concat(requestAriDto.getClaimNo().toString()).concat(",Date Of Accident :-").concat(requestAriDto.getAccidentDate())
                        .concat("/").concat(messageContentDetails.getSubject());
                email.setSubject(subject);
                emailService.sendEmail(connection, email);
                requestAriDto.setStatus("P");
                requestAriDao.updateStatusByRefId(connection, requestAriDto);
                saveClaimsLogs(connection, requestAriDto.getClaimNo(), user, "Branch Upload Rejected", "Details Uploaded by " + requestAriDto.getDocumentUploadUser() + " has been rejected by " + user.getUserId() + " as " + reason);
                saveClaimProcessFlow(connection, requestAriDto.getClaimNo(), 0, "Branch Upload Rejected", user.getUserId(), Utility.sysDateTime(), requestAriDto.getDocumentUploadUser(), AppConstant.NO);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean checkDocumentsForARI(Integer claimNo) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<Integer> documentIdList = claimDocumentDao.getDocumentIdList(connection, claimNo);
            if (documentIdList.contains(AppConstant.DOCUMENT_TYPE_FINAL_BILL) || documentIdList.contains(AppConstant.DOCUMENT_TYPE_SPARE_PARTS_BILL) ||
                    documentIdList.contains(AppConstant.DOCUMENT_TYPE_lABOUR_BILL) || documentIdList.contains(AppConstant.DOCUMENT_TYPE_TAX_INVOICE)) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public RequestAriDto getAriDetails(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return requestAriDao.getLatestAriRequest(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }


    @Override
    public int isAriOrSalvageRequested(Integer claimNoToSearch) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return requestAriDao.searchByClaimNoisSalvageORARIRequestedBySpcOrScr(connection, claimNoToSearch);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean revokeAriRequest(Integer requestAriId, String remark, UserDto user) throws Exception {
        Connection connection = null;
        boolean isUpdated = false;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            RequestAriDto requestAriDto = new RequestAriDto();
            requestAriDto.setId(requestAriId);
            requestAriDto.setRemark(remark);
            requestAriDto.setStatus("R");
            requestAriDto.setRequestedUser(AppConstant.STRING_EMPTY);
            requestAriDto.setRequestedDate(AppConstant.DEFAULT_DATE_TIME);
            requestAriDto.setRevokeUser(user.getUserId());
            requestAriDto.setRevokeDate(Utility.sysDateTime());
            isUpdated = requestAriDao.updateStatusAndRevokeByRef(connection, requestAriDto);
            if (isUpdated) {
                RequestAriDto requestAri = requestAriDao.searchRequestAriDto(connection, requestAriId);
                saveClaimsLogs(connection, requestAri.getClaimNo(), user, "ARI Revoke", "Revoked ARI from " + user.getUserId());
                saveSpecialRemark(connection, user, requestAri.getClaimNo(), "ARI Revoked", remark);

                /*if (requestAri.getRefId() > 0) {
                    AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, requestAri.getRefId());
                    assessorAllocationDao.rejectJobStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), AppConstant.REVOKED_STATUS, requestAri.getRefId());
                    assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), requestAri.getRefId());
                    if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, requestAri.getClaimNo())) {
                        if (claimHandlerDao.isAutoStored(connection, requestAri.getClaimNo())) {
                            if (!motorEngineerDetailsDao.isAriSalvagePending(connection, requestAri.getClaimNo())) {
                                claimHandlerDao.updateStoreStatus(connection, requestAri.getClaimNo(), "N");
                                saveClaimsLogs(connection, requestAri.getClaimNo(), user, "Auto Re-Store File", "Claim File Auto Re-Stored due to Pending ARI/ Salvage Inspection Rejection");
                                saveClaimProcessFlow(connection, requestAri.getClaimNo(), 0, "Auto Re-Store File", "SYSTEM", Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                            }
                        }
                    }
                    if (null != assessorAllocationDto.getAssessorDto().getUserName() && !assessorAllocationDto.getAssessorDto().getUserName().isEmpty()) {
                        String URL = "/InspectionDetailsController/viewEdit?P_N_CLIM_NO=" + requestAri.getClaimNo() + "&P_N_REF_NO=" + requestAri.getRefId();
                        saveNotfication(connection, requestAri.getClaimNo(), user.getUserId(), assessorAllocationDto.getAssessorDto().getUserName(), "ARI/ Salvage Inspection: " + assessorAllocationDto.getJobId() + " has been Revoked", URL);
                    }
                    saveClaimsLogs(connection, requestAri.getClaimNo(), user, "ARI/ Salvage Revoked", "Assessor Job " + assessorAllocationDto.getJobId() + " Revoked by " + user.getUserId());
                }*/
                if (!motorEngineerDetailsDao.isAriSalvagePending(connection, requestAri.getClaimNo())) {
                    if (claimHandlerDao.isAutoStored(connection, requestAri.getClaimNo())) {
                        claimHandlerDao.updateStoreStatus(connection, requestAri.getClaimNo(), "N");
                        saveClaimsLogs(connection, requestAri.getClaimNo(), user, "Auto Re-Store File", "Claim File Auto Re-Stored due to Pending ARI/ Salvage Inspection Rejection");
                        saveClaimProcessFlow(connection, requestAri.getClaimNo(), 0, "Auto Re-Store File", "SYSTEM", Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
                    }
                    rtePendingClaimDetailDao.removePendingJobs(connection, requestAri.getClaimNo());
                }
                List<UserDto> userList = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE);
                List<UserDto> userListAriOnly = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE);
                userList.addAll(userListAriOnly);
                String assignedClaimHandler = claimHandlerDao.getAssignedClaimHandler(connection, requestAri.getClaimNo());
                saveNotification(connection, requestAri.getClaimNo(), user.getUserId(), assignedClaimHandler, "ARI Request Revoked", "#");
                for (UserDto userDto : userList) {
                    saveNotification(connection, requestAri.getClaimNo(), user.getUserId(), userDto.getUserId(), "ARI Request Revoked", "#");
                }
                saveClaimProcessFlow(connection, requestAri.getClaimNo(), 0, "ARI Revoked", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }


    @Override
    public int isSalvageORAriArranged(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return requestAriDao.searchByClaimNoisSalvageORARIArrangedBySpcOrScr(connection, claimNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public BulkCloseDetailDto getBulkCloseDetail(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return bulkCloseDetailDao.getBulkCloseDetails(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }
}
