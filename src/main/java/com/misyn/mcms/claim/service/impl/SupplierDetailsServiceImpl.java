package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.SupplierDetailsMasterDao;
import com.misyn.mcms.claim.dao.impl.SupplierDetailsMasterDaoImpl;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.SupplierDetailsMasterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.SupplierDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class SupplierDetailsServiceImpl extends AbstractBaseService<SupplierDetailsServiceImpl> implements SupplierDetailsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierDetailsServiceImpl.class);
    private SupplierDetailsMasterDao supplierDetailsMasterDao = new SupplierDetailsMasterDaoImpl();

    @Override
    public SupplierDetailsMasterDto insertSupplierDetail(SupplierDetailsMasterDto supplierDetailsMasterDto) throws MisynJDBCException {
        Connection connection = null;
        SupplierDetailsMasterDto supplierDetailsMasterDto1;
        try {
            connection = getJDBCConnection();
            if (supplierDetailsMasterDto.getSupplierId().equals(0)) {
                supplierDetailsMasterDto1 = supplierDetailsMasterDao.insertMaster(connection, supplierDetailsMasterDto);
            } else {
                supplierDetailsMasterDto1 = supplierDetailsMasterDao.updateMaster(connection, supplierDetailsMasterDto);
            }

            return supplierDetailsMasterDto1;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public SupplierDetailsMasterDto updateSupplierDetail(SupplierDetailsMasterDto supplierDetailsMasterDto) throws MisynJDBCException {
        Connection connection = null;
        SupplierDetailsMasterDto supplierDetailsMasterDto1;
        try {
            connection = getJDBCConnection();
            supplierDetailsMasterDto1 = supplierDetailsMasterDao.updateMaster(connection, supplierDetailsMasterDto);
            return supplierDetailsMasterDto1;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());

        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public SupplierDetailsMasterDto searchSupplierDetail(Object id) throws MisynJDBCException {
        SupplierDetailsMasterDto supplierDetailsMasterDto1;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            supplierDetailsMasterDto1 = supplierDetailsMasterDao.searchMaster(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return supplierDetailsMasterDto1;
    }

    @Override
    public List<SupplierDetailsMasterDto> SupplierDetailtolist() {
        Connection connection = null;
        List<SupplierDetailsMasterDto> supplierDetailsMasterDtoList = null;
        try {
            connection = getJDBCConnection();
            supplierDetailsMasterDtoList = supplierDetailsMasterDao.searchAll(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return supplierDetailsMasterDtoList;
    }

    @Override
    public DataGridDto getSupplierDetailsDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (isSearch) {
                dataGridDto = supplierDetailsMasterDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField);
            } else {
                dataGridDto = new DataGridDto();
                dataGridDto.setDraw(drawRandomId);
                dataGridDto.setRecordsTotal(0);
                dataGridDto.setRecordsFiltered(0);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }
}
