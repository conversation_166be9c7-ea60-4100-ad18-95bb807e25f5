package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.AssessorFeeDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.ListItemDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface AssessorFeeService {
    AssessorFeeDto saveAssessorFee(AssessorFeeDto assessorFeeDto) throws MisynJDBCException;

    AssessorFeeDto updateAssessorFee(AssessorFeeDto assessorFeeDto) throws MisynJDBCException;

    AssessorFeeDto getAssessorFee(Integer assessorFeeDetailId) throws Exception;

    List<ListItemDto> getInspectionTypeList() throws Exception;

    List<ListItemDto> getDayTypeList() throws Exception;

    List<ListItemDto> getTimeSlotsByInspectionTypeId(Integer inspectionTypeId, Integer jobType) throws Exception;

    void deleteAssessorFee(Integer assessorFeeDetailId, String userName) throws Exception;


    DataGridDto getAssessorFeeDetailDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch);
}
