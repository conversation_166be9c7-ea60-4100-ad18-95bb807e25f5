package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.PolicyDto;
import com.misyn.mcms.claim.dto.list.PolicyCategoryDataList;

import java.util.Map;

public interface PolicyDetailsService {


    PolicyCategoryDataList getPolicyCategoryDataList(PolicyDto policyDto, String policyChannelType);

    Map<String, String> getCoverAmountDataList(PolicyDto policyDto, String policyChannelType);

    Map<String, String> getServiceFactorCategoryList();


    Map<String, String> getCoverCategoryList();


    Map<String, String> getBenefitCategoryList();


    Map<String, String> getConditionCategoryList();


    Map<String, String> getSpecialCategoryList();

}
