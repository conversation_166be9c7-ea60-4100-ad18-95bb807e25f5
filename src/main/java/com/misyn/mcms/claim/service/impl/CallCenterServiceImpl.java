package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.admin.admin.service.impl.UserManagementServiceImpl;
import com.misyn.mcms.claim.controller.callcenter.validator.LoggerTrail;
import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.enums.NotificationPriority;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.Utility;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class CallCenterServiceImpl extends AbstractBaseService<CallCenterServiceImpl> implements CallCenterService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CallCenterServiceImpl.class);
    private final BigDecimal INITIAL_ACR = new BigDecimal("25000.00");
    private CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private PolicyDao policyDao = new PolicyDaoImpl();
    private DamageBodyPartDao damageBodyPartDao = new DamageBodyPartDaoImpl();
    private ThirdPartyDao thirdPartyDao = new ThirdPartyDaoImpl();
    private LoggerTrailDao loggerTrailDao = new LoggerTrailDaoImpl();
    private SpecialRemarkDao specialRemarkDao = new SpecialRemarkDaoImpl();
    private OfflineIntimationDao offlineIntimationDao = new OfflineIntimationDaoImpl();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private ClaimWiseDocumentService claimWiseDocumentService = new ClaimWiseDocumentServiceImpl();
    private ClaimUserAllocationService claimUserAllocationService = new ClaimUserAllocationServiceImpl();
    private ClaimLogsDao claimLogsDao = new ClaimLogsDaoImpl();
    private RestPolicyDetailsService restPolicyDetailsService = new RestPolicyDetailsServiceImpl();
    private McmsClaimOfflineReserveClaimDao offlineReserveClaimDao = new McmsClaimOfflineReserveClaimDaoImpl();
    private ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private McmsClaimOfflinePaymentDao offlinePaymentDao = new McmsClaimOfflinePaymentDaoImpl();
    private McmsClaimOfflineReserveAssessorDao offlineReserveAssessorDao = new McmsClaimOfflineReserveAssessorDaoImpl();
    private EmailService emailService = new EmailServiceImpl();
    private EmailDao emailDao = new EmailDaoImpl();
    private UserManagementService userManagementService = new UserManagementServiceImpl();

    @Override
    public ClaimsDto insert(ClaimsDto claimsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto update(ClaimsDto claimsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto delete(ClaimsDto claimsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto updateAuthPending(ClaimsDto claimsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto deleteAuthPending(ClaimsDto claimsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto search(Object id) throws Exception {
        ClaimsDto claimsDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimsDto = callCenterDao.searchMaster(connection, id);
            if (claimsDto != null) {
                claimsDto.setDrivenLicenseDocumentList(claimDocumentDao.
                        findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus
                                (connection, claimsDto.getClaimNo(), AppConstant.DRIVING_LICENCE_DOCUMENT_TYPE_ID));

                claimsDto.setEstimateDocumentList(claimDocumentDao.
                        findAllByClaimNoAndInDocumentTypeIdAndInDocumentStatus
                                (connection, claimsDto.getClaimNo(), AppConstant.ALL_CLAIM_FORM_DOCUMENT_TYPE_IDS));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimsDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<ClaimsDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }


    @Override
    public ClaimsDto saveClaim(ClaimsDto claimsDto, UserDto user, String type) throws Exception {
        Connection connection = null;
        ClaimsDto savedClaimDto;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            PolicyDto policyDto = claimsDto.getPolicyDto();

            ClaimsDto oldClaimDto = callCenterDao.searchMaster(connection, claimsDto.getClaimNo());
            /* Check if the claim number already exists.
             * if exists ? claim number > 0
             * else ? claim number = 0
             * */
            if (oldClaimDto.getClaimNo() > 0) {
                claimsDto.setPolicyNumber(policyDto.getPolicyNumber());
                claimsDto.setPolicyBranch(policyDto.getPolicyBranch());
                claimsDto.setPolicyType(policyDto.getProduct());
                claimsDto.setPolRefNo(policyDto.getPolicyRefNo());
                claimsDto.setVehicleNo(policyDto.getVehicleNumber());
                claimsDto.setCoverNoteNo(policyDto.getCoverNoteNo());
                claimsDto.setVehicleNoLastDigit(policyDto.getVehicleNoLastDigit());
                claimsDto.setPolicyNumberLastDigit(policyDto.getPolicyNumberLastDigit());

                savedClaimDto = callCenterDao.updateMaster(connection, claimsDto);
            } else {
                fillClaimDtoOtherFields(claimsDto, policyDto, user);
                savedClaimDto = callCenterDao.insertMaster(connection, claimsDto);
                saveClaimHandler(savedClaimDto, connection);
                saveDocuments(connection, savedClaimDto, user);
                saveClaimsLogs(connection, savedClaimDto, user);
                policyDao.updateLatestIntimationDate(connection, claimsDto.getPolicyDto());

                //send Intimation SMS
                this.sendIntimationSmsToCustomer(claimsDto, connection, policyDto, user);
                this.sendIntimationSmsToAgent(claimsDto, connection, policyDto, user);
                this.sendIntimationSmsToIntroducer(claimsDto, connection, policyDto, user);
                this.sendIntimationEmailtoCustomerAndIntroducer(claimsDto, connection, policyDto);

            }

            thirdPartyDao.removeClaimThirdParty(connection, savedClaimDto.getClaimNo());
            thirdPartyDao.insertThirdPartyList(connection, claimsDto.getThirdPartyDtoMap(), savedClaimDto.getClaimNo());

            damageBodyPartDao.deleteDamageBodyPartsList(connection, savedClaimDto.getClaimNo());
            damageBodyPartDao.insertDamageBodyPartsList(connection, claimsDto.getDamageBodyPartDtoList(), savedClaimDto);

            LoggerTrail<ClaimsDto> loggerTrail = new LoggerTrail<>();
            List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(savedClaimDto, oldClaimDto, 1);

            if (policyDto.getPolicyNumber().equalsIgnoreCase(policyDto.getCoverNoteNo())) {
                ClaimLogTrailDto logTrailDto = new ClaimLogTrailDto();
                logTrailDto.setClaimNo(savedClaimDto.getClaimNo());
                logTrailDto.setFormNameId(1);
                logTrailDto.setVersionId(1);
                logTrailDto.setFieldName("Cover Note");
                logTrailDto.setFieldValue("Cover Note Created");
                logTrailDto.setUserId(policyDto.getCreateUser());

                loggerTrailList.add(logTrailDto);
            }

            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, savedClaimDto.getClaimNo(), user.getUserId(), 1);
            BigDecimal sumInsured = claimsDto.getPolicyDto().getSumInsured();
            if ((savedClaimDto.getClaimStatus() == 2
                    || savedClaimDto.getClaimStatus() == 3
                    || savedClaimDto.getClaimStatus() == 30)
                    && savedClaimDto.getIsfsUpdateStatus().equalsIgnoreCase("N")) {
                this.setOfflineIntimationDetails(connection, savedClaimDto);

                BigDecimal initialAcr;
                BigDecimal acr = new BigDecimal("0.00");
                initialAcr = claimHandlerDao.getTotalAcrAmount(connection, savedClaimDto.getClaimNo());
                if (initialAcr.equals(acr)) {
                    savedClaimDto.setTotalAcr(claimsDto.getCauseOfLoss().equals(AppConstant.THEFT) ? sumInsured : INITIAL_ACR);
                    this.offlineReserveClaim(connection, savedClaimDto, savedClaimDto.getCauseOfLoss(), user, false);
                    claimHandlerDao.updateReserveAmountFromRTE(INITIAL_ACR,
                            INITIAL_ACR,
                            INITIAL_ACR,
                            AppConstant.STRING_EMPTY,
                            connection,
                            savedClaimDto.getClaimNo());
                }
            }

            if ((type.equals("PS")) || type.equals("FW")) {
                if (claimsDto.getCauseOfLoss().equals(AppConstant.THEFT)) {
                    claimHandlerDao.updateReserveAmountFromRTE(sumInsured, sumInsured, sumInsured, AppConstant.STRING_EMPTY, connection, savedClaimDto.getClaimNo());
                    String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimsDto.getClaimNo())).concat("&P_TAB_INDEX=0");
                    StringBuilder sbMessage = new StringBuilder();
                    String userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimsDto.getClaimNo());

                    ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
                    claimHandlerDto.setInitLiabilityAssignUserId(userId);
                    claimHandlerDto.setInitLiabilityAssignDateTime(Utility.sysDateTime());
                    claimHandlerDto.setInitLiabilityAprvStatus("P");
                    claimHandlerDto.setClaimNo(claimsDto.getClaimNo());
                    claimHandlerDao.updateInitialLiabilityUser(connection, claimHandlerDto);

                    claimHandlerDto.setClaimStatus(AppConstant.ACCESS_LEVEL_INITIAL_LIABILITY_PENDING);
                    claimHandlerDto.setIsFileStore("N");
                    claimHandlerDao.updateClaimStatusFileStatus(connection, claimHandlerDto);
                    saveClaimProcessFlow(connection, claimsDto.getClaimNo(), AppConstant.ACCESS_LEVEL_INITIAL_LIABILITY_PENDING, "Mark as Theft claim file", user.getUserId(), Utility.sysDateTime(), claimHandlerDto.getInitLiabilityAssignUserId());


                    claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
                    claimHandlerDto.setAssignStatus("P");
                    claimHandlerDto.setAssignUserId(userId);
                    claimHandlerDao.updateAssignUser(connection, claimHandlerDto);
                    sbMessage.append("You have allocated new Theft claim file");
                    saveNotification(connection,
                            claimHandlerDto.getClaimNo(),
                            user.getUserId(),
                            claimHandlerDto.getInitLiabilityAssignUserId(),
                            sbMessage.toString(),
                            URL);

                    claimHandlerDao.updateLossType(connection, claimsDto.getClaimNo(), AppConstant.TOTAL_LOSS_TYPE.toString());
                }
            }
            //TODO assign theft claim file to C/Handler

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return savedClaimDto;
    }

    private void sendIntimationEmailtoCustomerAndIntroducer(ClaimsDto claimsDto, Connection connection, PolicyDto policyDto) throws Exception {
        ArrayList<String> list = new ArrayList<>();
        ArrayList<String> emailList = new ArrayList<>();
        emailList.add(policyDto.getIntroducerDto().getEmail());
        emailList.add(policyDto.getPolicySellingAgentDetailsDto().getEmail());
        try {
            for (String destinationEmail : emailList) {
                if (null != destinationEmail) {
                    Email email = new Email();
                    email.setToAddresses(destinationEmail);
                    MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.INTIMATION_MAIL);
                    email.setEmailMassege(messageContentDetails.getMessageBody());

                    if (null != claimsDto) {

                        list.add(claimsDto.getVehicleNo().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getVehicleNo());
                        list.add(claimsDto.getPolicyDto().getCustName().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getPolicyDto().getCustName());
                        list.add(claimsDto.getInsurdMobNo().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getInsurdMobNo());
                        list.add(claimsDto.getAccidDate().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getAccidDate());
                        list.add(claimsDto.getClaimNo() == AppConstant.ZERO_INT ? AppConstant.NOT_AVAILABLE : String.valueOf(claimsDto.getClaimNo()));
                        list.add(claimsDto.getReporterName().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getReporterName());
                        list.add(claimsDto.getCliNo().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getCliNo());
                        list.add(claimsDto.getPolicyDto().getCustName().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getPolicyDto().getCustName());
                        list.add(claimsDto.getCliNo().isEmpty() ? AppConstant.NOT_AVAILABLE : claimsDto.getCliNo());
                        list.add(claimsDto.getPolicyDto().getPolicyNumber());

                        email.setParameterEmail(list);
                        String subject = "CLAIM INTIMATION NOTIFICATION CLAIM NO- [".concat(claimsDto.getClaimNo().toString())
                                .concat("] VEHICLE NO-[").concat(claimsDto.getVehicleNo()).concat("]");
                        email.setSubject(subject);
                        emailService.sendEmail(connection, email);
                    }
                }
            }
        } catch (Exception e) {
            throw new Exception();
        }
    }

    private void sendIntimationSmsToIntroducer(ClaimsDto claimsDto, Connection connection, PolicyDto policyDto, UserDto user) throws Exception {
        List<String> smsParameterList = new ArrayList<>();
        smsParameterList.add(policyDto.getCustName());
        smsParameterList.add(policyDto.getVehicleNumber());
        smsParameterList.add(getNearestCity(connection, claimsDto.getNearestCity()));
        smsParameterList.add(claimsDto.getCliNo());
        sendSmsMessage(connection, 23, smsParameterList, policyDto.getIntroducerDto().getContactNo(), policyDto.getPolicyChannelType(), user, claimsDto.getClaimNo(), AppConstant.SEND_TO_INTRODUCER);
    }

    private void sendIntimationSmsToAgent(ClaimsDto claimsDto, Connection connection, PolicyDto policyDto, UserDto user) throws Exception {
        List<String> smsParameterList = new ArrayList<>();
        smsParameterList.add(policyDto.getCustName());
        smsParameterList.add(policyDto.getVehicleNumber());
        smsParameterList.add(getNearestCity(connection, claimsDto.getNearestCity()));
        smsParameterList.add(claimsDto.getCliNo());
        sendSmsMessage(connection, 23, smsParameterList, policyDto.getPolicySellingAgentDetailsDto().getContactNo(), policyDto.getPolicyChannelType(), user, claimsDto.getClaimNo(), AppConstant.SEND_TO_AGENT);
    }

    private void sendIntimationSmsToCustomer(ClaimsDto claimsDto, Connection connection, PolicyDto policyDto, UserDto user) throws Exception {
        List<String> smsParameterList = new ArrayList<>();
        smsParameterList.add(policyDto.getVehicleNumber());
        smsParameterList.add(String.valueOf(claimsDto.getClaimNo()));
        sendSmsMessage(connection, 20, smsParameterList, policyDto.getCustMobileNo(), policyDto.getPolicyChannelType(), user, claimsDto.getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
    }

    private void fillClaimDtoOtherFields(ClaimsDto claimsDto, PolicyDto policyDto, UserDto user) throws Exception {
        claimsDto.setPolicyNumber(policyDto.getPolicyNumber());
        claimsDto.setPolicyBranch(policyDto.getPolicyBranch());
        claimsDto.setPolicyType(policyDto.getProduct());
        claimsDto.setPolRefNo(policyDto.getPolicyRefNo());
        claimsDto.setVehicleNo(policyDto.getVehicleNumber());
        claimsDto.setCoverNoteNo(policyDto.getCoverNoteNo());
        claimsDto.setInpUser(user.getUserId());
        claimsDto.setInpTime(Utility.sysDateTime());
        claimsDto.setInsurdMobNo(policyDto.getCustMobileNo());
        claimsDto.setDateOfReport(Utility.sysDate(AppConstant.DATE_FORMAT));
        claimsDto.setCallUser(user.getUserId());
        claimsDto.setAccessusrType(user.getAccessUserType());
        claimsDto.setVehicleNoLastDigit(policyDto.getVehicleNoLastDigit());
        claimsDto.setPolicyNumberLastDigit(policyDto.getPolicyNumberLastDigit());
    }

    @Override
    public DataGridDto getPolicyDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start,
                                            int length, String orderType, String orderField, String accidentDate, String vehicleNumber) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = policyDao.getPolicyDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, accidentDate, vehicleNumber);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = callCenterDao.getClaimDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    private void setOtherDetailsList(PolicyDto policyDto) {
        String policyChannelType = null != policyDto.getPolicyChannelType() && !policyDto.getPolicyChannelType().isEmpty()
                && policyDto.getPolicyChannelType().equals(PolicyChannelType.TAKAFUL.name())
                ? AppConstant.URL_TYPE_TAKAFUL : AppConstant.URL_TYPE_CONVENTIONAL;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            policyDto.setCoverDtoList(restPolicyDetailsService.getCoverDtoList(policyDto, policyChannelType));
            policyDto.setExcessDtoList(restPolicyDetailsService.getExcessDtoList(policyDto, policyChannelType));
            policyDto.setPaidDetailsDtoList(restPolicyDetailsService.getPaidDetailsDtoList(policyDto, policyChannelType));
            policyDto.setPolicyMemoDtoList(restPolicyDetailsService.getPolicyMemoDtoList(policyDto, policyChannelType));
            policyDto.setSellingAgentDetailsDtoList(restPolicyDetailsService.getSellingAgentDetailsDtoList(policyDto, policyChannelType));
            policyDto.setEndorsementHistoryDtoList(restPolicyDetailsService.getEndorsementHistoryDtoList(policyDto, policyChannelType));
            policyDto.setBillingInfoDtoList(restPolicyDetailsService.getBillingInfoDtoList(policyDto, policyChannelType));
            policyDto.setLearnerDriverDetailsDtoList(restPolicyDetailsService.getLearnerDriverDetailsDtoList(policyDto, policyChannelType));
            policyDto.setPremiumBreakupFormDto(restPolicyDetailsService.getPremiumBreakupFormDto(policyDto, policyChannelType));
            policyDto.setPolicySellingAgentDetailsDto(restPolicyDetailsService.getPolicySellingAgentDetailsDto(policyDto, policyChannelType));
            policyDto.setIntroducerDto(restPolicyDetailsService.getIntroducerDetailsDto(policyDto, policyChannelType));
            policyDto.setCweDetailDtoList(restPolicyDetailsService.getCweDetail(policyDto, policyChannelType));
            policyDto.setNcbHistoryDetailsSummary(policyDao.searchNcbHistoryDetails(connection, policyDto.getPolicyNumber()));
            policyDto.setProductDetailListDto(userManagementService.getProductDetailByName(policyDto.getProduct()));

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public TrailerDetailDto getTrailerDetail(String policyNumber, String policyChannelType) throws Exception {
        TrailerDetailDto trailerDetail = null;
        try {
            trailerDetail = restPolicyDetailsService.getTrailerDetail(policyNumber, policyChannelType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return trailerDetail;
    }

    @Override
    public List<TradePlateDetailDto> getTradePlateDetail(String policyNumber, String policyChannelType) throws Exception {
        List<TradePlateDetailDto> tradePlateDetail = new ArrayList<>();
        try {
            tradePlateDetail = restPolicyDetailsService.getTradePlateDetail(policyNumber, policyChannelType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return tradePlateDetail;
    }

    @Override
    public void setOtherDetailsList(ClaimsDto claimsDto) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            PolicyDto policyDto = claimsDto.getPolicyDto();
            this.setOtherDetailsList(policyDto);

            String dateOfReport = claimsDto.getDateOfReport();
            if (AppConstant.DEFAULT_DATE.equalsIgnoreCase(dateOfReport)) {
                dateOfReport = Utility.sysDate(AppConstant.DATE_FORMAT);
            }
            List<ClaimsDto> policyClaimList = null;

            if (!policyDto.getVehicleNumber().isEmpty()) {
                policyClaimList = callCenterDao.getPolicyClaimList(connection, policyDto.getVehicleNumber(), claimsDto.getClaimNo(), dateOfReport);
            }
            claimsDto.setClaimHistory(policyClaimList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean updateDriverDetails(ClaimsDto claimsDto, UserDto user) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            Boolean updateDriverDetails = callCenterDao.updateDriverDetails(connection, claimsDto);
            saveClaimsLogs(connection, claimsDto.getClaimNo(), user, "Driver Details Updated", "Driver Details and Accident : " + (AppConstant.YES.equals(claimsDto.getDriverDetailNotRelevant()) ? "Not Relevant" : "Relevant"));
            return updateDriverDetails;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public DriverDetailDto getDriverDetails(Integer claimNo) {
        Connection connection = null;
        DriverDetailDto driverDetailDto = null;
        try {
            connection = getJDBCConnection();
            driverDetailDto = callCenterDao.getDriverDetails(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return driverDetailDto;
    }

    @Override
    public void markPriority(Integer claimNo, Integer priority, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String priorityValue = priority.equals(1) ? AppConstant.PRIORITY_HIGH : AppConstant.PRIORITY_NORMAL;
            callCenterDao.markPriority(connection, claimNo, priorityValue);

            String URL = "/ClaimHandlerController/viewEdit?P_N_CLIM_NO=".concat(claimNo.toString());
            List<String> assignUserbyClaimNo = getAssignUserbyClaimNo(connection, claimNo);
            if (!assignUserbyClaimNo.isEmpty()) {
                if (priority.equals(1)) {
                    for (String assignUser : assignUserbyClaimNo) {
                        saveNotification(connection, claimNo, user.getUserId(), assignUser, "You have received a " + priorityValue + " prioritized claim. Please treat this as Urgent", URL, NotificationPriority.HIGH, "#FADADD");
                    }
                }
            }
            saveClaimProcessFlow(connection, claimNo, 0, "Claim Priority Changed as " + priorityValue, user.getUserId(), Utility.sysDateTime(), assignUserbyClaimNo.isEmpty() ? AppConstant.STRING_EMPTY : assignUserbyClaimNo.get(0));
            saveSpecialRemark(connection, user, claimNo, "Claim Priority Changed as " + priorityValue, remark);
            saveClaimsLogs(connection, claimNo, user, "Claim Priority Changed", "Claim Priority Changed to " + priorityValue);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public PolicyWarningMessageDto checkPolicyValidity(String policyNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return policyDao.checkPolicyValidity(connection, policyNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean markAsTheftAndFound(Integer claimNo, String remark, UserDto user) throws Exception {
        Connection connection = null;
        boolean isUpdate = false;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            String totalLossAssignUSer = claimHandlerDao.getAssignedClaimHandler(connection, claimNo);
            claimHandlerDao.updateReserveAmountFromRTE(INITIAL_ACR, INITIAL_ACR, INITIAL_ACR, AppConstant.STRING_EMPTY, connection, claimNo);
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, claimNo);
            claimsDto.getPolicyDto().setPolicyChannelType(claimsDto.getPolicyChannelType());
            claimsDto.setTotalAcr(INITIAL_ACR);
            this.offlineReserveClaim(connection, claimsDto, 1, user, true);

            String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
            String sbMessage;

            String userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);

            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setInitLiabilityAssignUserId(userId);
            claimHandlerDto.setInitLiabilityAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setInitLiabilityAprvStatus("P");
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDao.updateInitialLiabilityUser(connection, claimHandlerDto);

            claimHandlerDto.setClaimStatus(AppConstant.ACCESS_LEVEL_INITIAL_LIABILITY_PENDING);
            claimHandlerDto.setIsFileStore("N");
            claimHandlerDao.updateClaimStatusFileStatus(connection, claimHandlerDto);

            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignStatus("P");
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateAssignUser(connection, claimHandlerDto);

            claimHandlerDto.setLiabilityAprvDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setLiabilityAprvUser(AppConstant.STRING_EMPTY);
            claimHandlerDto.setLiabilityAprvStatus("P");
            claimHandlerDao.updateLiabilityAprvStatus(connection, claimHandlerDto);
            sbMessage = "Vehicle was found. Contact customer immediately & Do the needful";
            saveNotification(connection,
                    claimHandlerDto.getClaimNo(),
                    user.getUserId(),
                    userId,
                    sbMessage,
                    URL);

            sbMessage = "Theft claim convert to partial loss & assign to claim handler (" + userId + ")";
            saveNotification(connection,
                    claimHandlerDto.getClaimNo(),
                    user.getUserId(),
                    totalLossAssignUSer,
                    sbMessage,
                    URL);

            claimHandlerDao.updateLossType(connection, claimNo, AppConstant.PARTIAL_LOSS_TYPE.toString());
            claimCalculationSheetMainDao.updateCalsheetAssignUser(connection, userId, claimNo);
            isUpdate = callCenterDao.markTheftAndFound(connection, claimNo);
            saveClaimsLogs(connection, claimNo, user, "Claim Marked as Theft and Found", "Theft Claim " + claimNo + " marked as Vehicle Found");
            saveClaimsLogs(connection, claimNo, user, "Theft claim convert to partial loss", "Theft claim convert to partial loss & assign to claim handler " + userId);
            saveSpecialRemark(connection, user, claimNo, "Theft and Found", remark);
            saveClaimProcessFlow(connection, claimNo, AppConstant.ACCESS_LEVEL_INITIAL_LIABILITY_PENDING, "Theft claim convert to partial loss", user.getUserId(), Utility.sysDateTime(), userId);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdate;
    }

    @Override
    public boolean isTheftClaim(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return callCenterDao.isTheftClaim(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void setExcessDetails(PolicyDto policyDto) {
        policyDto.setExcessDtoList(restPolicyDetailsService.getExcessDtoList(policyDto, null != policyDto.getPolicyChannelType()
                && !policyDto.getPolicyChannelType().isEmpty()
                && policyDto.getPolicyChannelType().equals(PolicyChannelType.TAKAFUL.name())
                ? AppConstant.URL_TYPE_TAKAFUL : AppConstant.URL_TYPE_CONVENTIONAL));
    }

    @Override
    public ClaimsDto getReportAccidentClaimsDto(Integer policyRefNo, Integer claimNo) {
        Connection connection = null;
        ClaimsDto claimsDto = null;
        try {
            connection = getJDBCConnection();
            PolicyDto policyDto = policyDao.searchMaster(connection, policyRefNo);
            policyDao.setVehicleBodyType(connection, policyDto);
            setOtherDetailsList(policyDto);
            claimsDto = callCenterDao.searchMaster(connection, claimNo);
            List<ClaimsDto> policyClaimList = callCenterDao.getPolicyClaimList(connection, policyDto.getVehicleNumber());
            claimsDto.setClaimHistory(policyClaimList);
            claimsDto.setPolicyDto(policyDto);
            claimsDto.setVehClsId(policyDto.getVehicleClassId());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto getReportAccidentClaimsDtoByClaimNo(Integer claimId) {
        Connection connection = null;
        ClaimsDto claimsDto = null;
        try {
            connection = getJDBCConnection();
            claimsDto = getReportAccidentClaimsDtoByClaimNo(connection, claimId);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto getReportAccidentClaimsDtoByClaimNo(Connection connection, Integer claimId) {
        ClaimsDto claimsDto = null;
        Integer policyRefNo;
        try {
            claimsDto = callCenterDao.searchMaster(connection, claimId);
            if (claimsDto != null) {
                policyRefNo = claimsDto.getPolRefNo();
                PolicyDto policyDto = policyDao.searchMaster(connection, policyRefNo);
                policyDto.setExcessDtoList(restPolicyDetailsService.getExcessDtoList(policyDto, null != policyDto.getPolicyChannelType()
                        && !policyDto.getPolicyChannelType().isEmpty()
                        && policyDto.getPolicyChannelType().equals(PolicyChannelType.TAKAFUL.name())
                        ? AppConstant.URL_TYPE_TAKAFUL : AppConstant.URL_TYPE_CONVENTIONAL));
                Map<Integer, ThirdPartyDto> thirdPartyDtoMap = thirdPartyDao.searchAllClaimsThirdParty(connection, claimId);
                claimsDto.setThirdPartyDtoMap(thirdPartyDtoMap);
                List<ClaimsDto> policyClaimList = callCenterDao.getPolicyClaimList(connection, policyDto.getVehicleNumber());
                claimsDto.setClaimHistory(policyClaimList);
                claimsDto.setPolicyDto(policyDto);
                setOtherDetailsList(policyDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto getViewAccidentClaimsDto(Integer claimNo) {
        Connection connection = null;
        ClaimsDto claimsDto = null;
        try {
            connection = getJDBCConnection();
            claimsDto = callCenterDao.searchMaster(connection, claimNo);
            Map<Integer, ThirdPartyDto> thirdPartyDtoMap = thirdPartyDao.searchAllClaimsThirdParty(connection, claimNo);
            List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, 1, claimNo);
            List<SpecialRemarkDto> specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo);
            claimsDto.setRemarkList(specialRemarkList);
            claimsDto.setThirdPartyDtoMap(thirdPartyDtoMap);
            claimsDto.setLogList(loggerTrailList);
            if (claimsDto.getPolRefNo() != null) {
                PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
                setOtherDetailsList(policyDto);
                claimsDto.setPolicyDto(policyDto);
                List<ClaimsDto> policyClaimList = callCenterDao.getPolicyClaimList(connection, policyDto.getVehicleNumber(), claimNo, claimsDto.getDateOfReport());
                claimsDto.setClaimHistory(policyClaimList);

                claimsDto.setDrivenLicenseDocumentList(claimDocumentDao.
                        findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus
                                (connection, claimsDto.getClaimNo(), AppConstant.DRIVING_LICENCE_DOCUMENT_TYPE_ID));

                claimsDto.setEstimateDocumentList(claimDocumentDao.
                        findAllByClaimNoAndInDocumentTypeIdAndInDocumentStatus
                                (connection, claimsDto.getClaimNo(), AppConstant.ALL_CLAIM_FORM_DOCUMENT_TYPE_IDS));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);//
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto getViewAccidentClaimsForCallCenter(Integer claimNo) {
        Connection connection = null;
        ClaimsDto claimsDto = null;
        try {
            connection = getJDBCConnection();
            claimsDto = callCenterDao.searchMaster(connection, claimNo);
            Map<Integer, ThirdPartyDto> thirdPartyDtoMap = thirdPartyDao.searchAllClaimsThirdParty(connection, claimNo);
            List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, 1, claimNo);
            List<SpecialRemarkDto> specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo);
            claimsDto.setRemarkList(specialRemarkList);
            claimsDto.setThirdPartyDtoMap(thirdPartyDtoMap);
            claimsDto.setLogList(loggerTrailList);
            if (claimsDto.getPolRefNo() != null) {
                PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
                setOtherDetailsList(policyDto);
                claimsDto.setPolicyDto(policyDto);


                claimsDto.setDrivenLicenseDocumentList(claimDocumentDao.
                        findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus
                                (connection, claimsDto.getClaimNo(), AppConstant.DRIVING_LICENCE_DOCUMENT_TYPE_ID));

                claimsDto.setEstimateDocumentList(claimDocumentDao.
                        findAllByClaimNoAndInDocumentTypeIdAndInDocumentStatus
                                (connection, claimsDto.getClaimNo(), AppConstant.ALL_CLAIM_FORM_DOCUMENT_TYPE_IDS));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto getViewAccidentClaimsDto(Connection connection, Integer claimNo) {
        ClaimsDto claimsDto = null;
        try {
            claimsDto = callCenterDao.searchMaster(connection, claimNo);
            Map<Integer, ThirdPartyDto> thirdPartyDtoMap = thirdPartyDao.searchAllClaimsThirdParty(connection, claimNo);
            List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, 1, claimNo);
            List<SpecialRemarkDto> specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo);
            claimsDto.setRemarkList(specialRemarkList);
            claimsDto.setThirdPartyDtoMap(thirdPartyDtoMap);
            claimsDto.setLogList(loggerTrailList);
            if (claimsDto.getPolRefNo() != null) {
                PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
                setOtherDetailsList(policyDto);
                claimsDto.setPolicyDto(policyDto);
                List<ClaimsDto> policyClaimList = callCenterDao.getPolicyClaimList(connection, policyDto.getVehicleNumber(), claimNo, claimsDto.getDateOfReport());
                claimsDto.setClaimHistory(policyClaimList);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto getClaimsDtoByClaimNo(Connection connection, Integer claimNo) {
        ClaimsDto claimsDto = null;
        try {
            claimsDto = callCenterDao.searchMaster(connection, claimNo);
            Map<Integer, ThirdPartyDto> thirdPartyDtoMap = thirdPartyDao.searchAllClaimsThirdParty(connection, claimNo);
            List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, 1, claimNo);
            List<SpecialRemarkDto> specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo);
            claimsDto.setRemarkList(specialRemarkList);
            claimsDto.setThirdPartyDtoMap(thirdPartyDtoMap);
            //   claimsDto.setLogList(loggerTrailList);
            if (claimsDto.getPolRefNo() != null) {
                PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
                policyDto.setIsfClaimNo(claimsDto.getIsfClaimNo());
                setOtherDetailsList(policyDto);
                setExcessDetails(policyDto);
                claimsDto.setPolicyDto(policyDto);
                List<ClaimsDto> policyClaimList = callCenterDao.getPolicyClaimList(connection, claimsDto.getVehicleNo(), claimNo, claimsDto.getDateOfReport());
                claimsDto.setClaimHistory(policyClaimList);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimsDto;
    }

    @Override
    public List<DamageBodyPartDto> getDamageBodyPartDtoList(Integer claimNo, Integer vehClsId) {
        List<DamageBodyPartDto> damageBodyPartDtoList = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            damageBodyPartDtoList = damageBodyPartDao.getDamageBodyPartDtoList(connection, claimNo, vehClsId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return damageBodyPartDtoList;
    }

    @Override
    public Integer updateFollowUpCallInfo(ClaimsDto claimsDto, UserDto user) throws Exception {
        Connection connection = null;
        Integer savedResponse = 0;
        try {
            connection = getJDBCConnection();
            ClaimsDto oldClaimDto = callCenterDao.searchMaster(connection, claimsDto.getClaimNo());
            savedResponse = callCenterDao.updateMasterFollowUp(connection, claimsDto);

            LoggerTrail<ClaimsDto> loggerTrail = new LoggerTrail<>();
            List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(claimsDto, oldClaimDto, 1);
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, claimsDto.getClaimNo(), user.getUserId(), 1);
            List<SpecialRemarkDto> specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimsDto.getClaimNo(), 1);
            claimsDto.setRemarkList(specialRemarkList);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception("Can not Update");
        } finally {
            releaseJDBCConnection(connection);
        }
        return savedResponse;
    }

    @Override
    public ErrorMessageDto saveSpecialRemark(SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception {
        Connection connection = null;
        ErrorMessageDto errorMessageDto = null;
        try {
            connection = getJDBCConnection();
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto = specialRemarkDao.insertMaster(connection, specialRemarkDto);

            if (null != specialRemarkDto) {
                errorMessageDto = new ErrorMessageDto();
                errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                errorMessageDto.setMessage("Remark Added");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not add remarks");
        } finally {
            releaseJDBCConnection(connection);
        }
        return errorMessageDto;
    }

    @Override
    public PolicyDto getPolicyDetails(Integer policyRefNo) throws Exception {
        Connection connection = null;
        PolicyDto policyDto = null;

        try {
            connection = getJDBCConnection();
            policyDto = policyDao.searchMaster(connection, policyRefNo);
            setOtherDetailsList(policyDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return policyDto;
    }

    @Override
    public ClaimsDto updatePolicyDetails(ClaimsDto claimsDto, Integer mappedPolicyNo, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<ClaimLogTrailDto> loggerTrailList = new ArrayList<>();

            PolicyDto dto = policyDao.searchMaster(connection, mappedPolicyNo);
            boolean isCoverNote = false;
            if (dto.getPolicyNumber().equalsIgnoreCase(dto.getCoverNoteNo())) {
                isCoverNote = true;
                ClaimLogTrailDto logTrailDto = new ClaimLogTrailDto();
                logTrailDto.setClaimNo(claimsDto.getClaimNo());
                logTrailDto.setFormNameId(1);
                logTrailDto.setVersionId(1);
                logTrailDto.setFieldName("Cover Note");
                logTrailDto.setFieldValue("Policy Mapped");
                logTrailDto.setUserId(user.getUserId());

                loggerTrailList.add(logTrailDto);
            }
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, claimsDto.getClaimNo(), user.getUserId(), 1);

            if (null != claimsDto.getPolicyDto()) {
                PolicyDto policyDto = claimsDto.getPolicyDto();
                claimsDto.setPolicyNumber(policyDto.getPolicyNumber());
                claimsDto.setPolicyBranch(policyDto.getPolicyBranch());
                claimsDto.setPolicyType(policyDto.getProduct());
                claimsDto.setPolRefNo(policyDto.getPolicyRefNo());
                claimsDto.setVehicleNo(policyDto.getVehicleNumber());
                claimsDto.setInsurdMobNo(null == policyDto.getCustMobileNo() ? AppConstant.STRING_EMPTY : policyDto.getCustMobileNo());
                claimsDto.setPolicyChannelType(policyDto.getPolicyChannelType());
                claimsDto.setVehicleNoLastDigit(null == policyDto.getVehicleNumber() ? AppConstant.STRING_EMPTY : getLastDigit(policyDto.getVehicleNumber()));
                claimsDto.setPolicyNumberLastDigit(null == policyDto.getPolicyNumber() ? AppConstant.STRING_EMPTY : getLastDigit(policyDto.getPolicyNumber()));
                claimsDto = callCenterDao.updatePolicyDetails(connection, claimsDto);

                if (isCoverNote) {
                    callCenterDao.updatePolicyStatusByRefNo(connection, AppConstant.POL_DELETE, mappedPolicyNo);
                }

                String clmIntimateDate = dto.getLatestClmIntimDate();
                if (AppConstant.DEFAULT_DATE.equals(clmIntimateDate)) {
                    clmIntimateDate = claimsDto.getDateTimeOfReport();
                }
                callCenterDao.updateLastIntimateDate(connection, claimsDto.getPolicyDto().getPolicyRefNo(), clmIntimateDate);
                updateOfflineIntimation(connection, claimsDto);
                updateOtherOfflineData(connection, claimsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not update", e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimsDto;
    }

    private String getLastDigit(String value) {
        String _value = AppConstant.EMPTY_STRING;
        try {
            Pattern p = Pattern.compile("\\d+");
            Matcher m = p.matcher(value);
            while (m.find()) {
                _value = m.group();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return _value;
    }

    private void updateOtherOfflineData(Connection connection, ClaimsDto claimsDto) throws Exception {
        try {
            offlineReserveClaimDao.updateReserveClaimPolicyChannelType(connection, claimsDto.getClaimNo(), claimsDto.getPolicyChannelType());
            offlineReserveAssessorDao.updateReserveAssessorPolicyChannelType(connection, claimsDto.getClaimNo(), claimsDto.getPolicyChannelType());
            offlinePaymentDao.updatePaymentPolicyChannelType(connection, claimsDto.getClaimNo(), claimsDto.getPolicyChannelType());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ClaimsDto savePolicyCoverNote(PolicyDto policyDto, UserDto user) throws Exception {
        Connection connection = null;
        ClaimsDto claimDto = new ClaimsDto();
        try {
            connection = getJDBCConnection();
            policyDto.setCreateUser(user.getUserId());
            policyDto.setCreateDate(Utility.sysDate());
            policyDto.setCreateTime(Utility.sysTime());
            policyDto.setPolStatus("INF");
            policyDto.setInspecDate(Utility.sysDate());
            policyDao.insertMaster(connection, policyDto);
            claimDto.setPolicyDto(policyDto);
            return claimDto;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }

    }

    @Override
    public List<ClaimsDto> getClaimHistoryForPolicyRefNo(String vehicleNo) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();

            List<ClaimsDto> policyClaimList = callCenterDao.getPreviousClaims(connection, vehicleNo);

            return policyClaimList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return new ArrayList<>();

    }

    @Override
    public ClaimsDto getViewAccidentClaimsDto(Integer claimNo, Integer policyRefNo) {
        Connection connection = null;
        ClaimsDto claimsDto = null;
        try {
            connection = getJDBCConnection();
            claimsDto = callCenterDao.searchMaster(connection, claimNo);
            Map<Integer, ThirdPartyDto> thirdPartyDtoMap = thirdPartyDao.searchAllClaimsThirdParty(connection, claimNo);
            List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, 1, claimNo);
            List<SpecialRemarkDto> specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo);
            claimsDto.setRemarkList(specialRemarkList);
            claimsDto.setThirdPartyDtoMap(thirdPartyDtoMap);
            claimsDto.setLogList(loggerTrailList);
            if (policyRefNo != null) {
                PolicyDto policyDto = policyDao.searchMaster(connection, policyRefNo);
                setOtherDetailsList(policyDto);
                claimsDto.setPolicyDto(policyDto);
//                List<ClaimsDto> policyClaimList = callCenterDao.getPolicyClaimList(connection, policyDto.getPolicyRefNo());
//                claimsDto.setClaimHistory(policyClaimList);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimsDto;
    }

    @Override
    public List<SpecialRemarkDto> getSpecialRemarkDtoList(ClaimsDto claimsDto) {
        List<SpecialRemarkDto> specialRemarkList = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimsDto.getClaimNo());
            claimsDto.setRemarkList(specialRemarkList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return specialRemarkList;
    }

    @Override
    public void setSpecialRemarkDtoList(ClaimsDto claimsDto, Integer claimNo) {
        List<SpecialRemarkDto> specialRemarkList = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            specialRemarkList = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo);
            if (specialRemarkList != null) {
                claimsDto.setRemarkList(specialRemarkList);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public PolicyDto searchPolicyByVehicleNo(String vehicleNo) throws Exception {
        Connection connection = null;
        PolicyDto policyDto = new PolicyDto();
        try {
            connection = getJDBCConnection();
            policyDto = policyDao.searchPolicyByVehicleNo(connection, vehicleNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return policyDto;
    }

    @Override
    public PolicyDto searchPolicyByValidPolicyDate(Integer policeRefNo, String accidentDate) throws Exception {
        Connection connection = null;
        PolicyDto policyDto = new PolicyDto();
        try {
            connection = getJDBCConnection();
            policyDto = policyDao.searchPolicyByValidPolicyDate(connection, policeRefNo, accidentDate);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return policyDto;
    }

    private void setOfflineIntimationDetails(Connection connection, ClaimsDto claimsDto) throws Exception {
        PolicyDto policyDto = null;
        try {
            if (claimsDto != null && claimsDto.getPolicyDto() != null) {
                OfflineIntimationDto offlineIntimationDto = new OfflineIntimationDto();
                policyDto = claimsDto.getPolicyDto();
                offlineIntimationDto.setOvPolicyNo(policyDto.getPolicyNumber());
                offlineIntimationDto.setOnRenCount(policyDto.getRenCount());
                offlineIntimationDto.setOnEndCount(policyDto.getEndCount());
                offlineIntimationDto.setOvRiskNo(policyDto.getRisk());
                offlineIntimationDto.setOvIntNo(String.valueOf(claimsDto.getClaimNo()));
                offlineIntimationDto.setOdIntimation(claimsDto.getDateOfReport());
                offlineIntimationDto.setOvIntimationTime(claimsDto.getTimeOfReport());
                offlineIntimationDto.setOvIntimatedSource("I");
                offlineIntimationDto.setOvIntimatedMeans("Phone");
                offlineIntimationDto.setOvIntType("I");
                offlineIntimationDto.setOvIntimatorName(claimsDto.getReporterName());
                offlineIntimationDto.setOdLoss(claimsDto.getAccidDate());
                offlineIntimationDto.setOdLossTime(claimsDto.getAccidTime());
                offlineIntimationDto.setOdTravelStartDt(claimsDto.getAccidDate());
                offlineIntimationDto.setOnNoOfDays(1);
                offlineIntimationDto.setOvIdenNoDriver(claimsDto.getDriverNic());
                offlineIntimationDto.setOvIdenCodeDriver("CNIC");
                offlineIntimationDto.setOvCauseOfLoss(callCenterDao.findRecord(connection, "claim_cause_of_loss_type", "LOSS_CODE", "N_ID=" + claimsDto.getCauseOfLoss()));//
                offlineIntimationDto.setOvDescOfLoss(claimsDto.getAccidDesc());
                offlineIntimationDto.setOvLossCode("ODPL");
                offlineIntimationDto.setOvReportType("P");
                offlineIntimationDto.setOvPanelCategory("AS");
                offlineIntimationDto.setOvSurvIndComp("I");
                offlineIntimationDto.setOvSurvIdenCode("CNIC");
                offlineIntimationDto.setOdAppointment(AppConstant.DEFAULT_DATE);
                offlineIntimationDto.setOdAssessment(claimsDto.getDateOfReport());
                offlineIntimationDto.setOdSubmit(Utility.sysDateTime());
                offlineIntimationDto.setOvPoliceStation(callCenterDao.findRecord(connection, "claim_police_station", "V_POLICE_CODE", "N_REF_NO=" + claimsDto.getNearPoliceStation()));//
                offlineIntimationDto.setOdReportDate(claimsDto.getDateOfReport());
                offlineIntimationDto.setOdServiceRequestDate(claimsDto.getDateOfReport());
                offlineIntimationDto.setOvUserId(claimsDto.getCallUser());
                offlineIntimationDto.setOvDriverName(claimsDto.getDriverName());
                offlineIntimationDto.setPolicyChannelType(claimsDto.getPolicyDto().getPolicyChannelType());
                if (claimsDto.getPolicyDto().getPolicyNumber().startsWith(AppConstant.COVER_NOTE)) {
                    offlineIntimationDto.setIsfsUpdateStat("C");
                }
                offlineIntimationDao.saveOfflineIntimation(connection, offlineIntimationDto);
                offlineIntimationDao.updateIsfUpdateStatus(connection, "U", claimsDto.getClaimNo());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }


    private void offlineReserveClaim(Connection connection, ClaimsDto claimsDto, int lossType, UserDto user, boolean theftAndFound) throws Exception {
        try {
            McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto = new McmsClaimOfflineReserveClaimDto();
            String lossCode = commonUtilDao.findOne(connection, "claim_loss_type", "V_LOSS_CODE", "N_ID=" + lossType);
            if (null != claimsDto) {
                mcmsClaimOfflineReserveClaimDto.setOvClaimNo(claimsDto.getIsfClaimNo());
                mcmsClaimOfflineReserveClaimDto.setOnBillAmount(claimsDto.getTotalAcr());
                mcmsClaimOfflineReserveClaimDto.setOnAllowedAmount(claimsDto.getTotalAcr());
                mcmsClaimOfflineReserveClaimDto.setOnDepPer(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOnPaEstimateAmount(claimsDto.getTotalAcr());
                mcmsClaimOfflineReserveClaimDto.setOvReportType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationNo(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAppointment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvPanelCategory(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvPanelType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvLossType(null != lossCode ? lossCode : AppConstant.LOSS_TYPE);
                mcmsClaimOfflineReserveClaimDto.setCvRequired(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvRiskNo(Integer.toString(1));
                mcmsClaimOfflineReserveClaimDto.setDInsertDateTime(Utility.sysDateTime());
                mcmsClaimOfflineReserveClaimDto.setNRetryAttempt(AppConstant.ZERO_INT);
                mcmsClaimOfflineReserveClaimDto.setVIsfsUpdateStat(AppConstant.NO);
                mcmsClaimOfflineReserveClaimDto.setDIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssesSub(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setClaimNo(claimsDto.getClaimNo());
                mcmsClaimOfflineReserveClaimDto.setPolicyChannelType(claimsDto.getPolicyDto().getPolicyChannelType());
                offlineReserveClaimDao.insertMaster(connection, mcmsClaimOfflineReserveClaimDto);
                saveClaimsLogs(connection, claimsDto.getClaimNo(), user, theftAndFound ? "Claim Reserve Changed - Theft and Found" : "Initial Claim Reserve - Call Center Intimation", "Total ACR Amount : " + claimsDto.getTotalAcr().toEngineeringString());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }


    }

    @Override
    public List<ClaimThirdPartyDetailsGenericDto> getClaimThirdPartyDetailsGeneric(Integer claimNo) throws Exception {
        List<ClaimThirdPartyDetailsGenericDto> claimThirdPartyDetailsGenericDtos = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<ThirdPartyDto> thirdPartyDtos = thirdPartyDao.searchAll(connection, claimNo);
            for (ThirdPartyDto thirdPartyDto : thirdPartyDtos) {
                ClaimThirdPartyDetailsGenericDto claimThirdPartyDetailsGenericDto = new ClaimThirdPartyDetailsGenericDto();
                BeanUtils.copyProperties(claimThirdPartyDetailsGenericDto, thirdPartyDto);
                claimThirdPartyDetailsGenericDto.setType("CALL_CENTER");
                claimThirdPartyDetailsGenericDtos.add(claimThirdPartyDetailsGenericDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimThirdPartyDetailsGenericDtos;
    }

    @Override
    public boolean getClaimNoByVehicleNoAndAccidentDate(String vehicleNo, String accidentDate) {
        Connection connection = null;
        boolean isClaim = false;
        try {
            connection = getJDBCConnection();
            accidentDate = Utility.getDate(accidentDate, AppConstant.DATE_FORMAT);
            isClaim = callCenterDao.getClaimNoByVehicleNoAndAccidentDate(connection, vehicleNo, accidentDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return isClaim;
    }

    @Override
    public boolean updateDoubtClaim(String status, String remark, Integer claimNo, Integer jobRefNo, UserDto user, SpecialRemarkDto specialRemarkDto) throws Exception {
        Connection connection = null;
        boolean updated = false;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            claimHandlerDao.updateDoubtStatus(connection, claimNo, status);
            updated = callCenterDao.updateDoubtClaim(connection, status, remark, claimNo);
            specialRemarkDao.insertMaster(connection, specialRemarkDto);
            initialLog(connection, claimNo, user, "Doubt Claim", "Doubt Claim Status : ".concat(status), jobRefNo);
            saveClaimsLogs(connection, claimNo, user, "Doubt Claim", "Doubt Claim Status : ".concat(status));
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return updated;
    }

    @Override
    public void setUnderWritingDetails(ClaimsDto claimsDto) {
        try {
            setOtherDetailsList(claimsDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public void updateIsfClaim(ClaimsDto claimsDto) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            this.setOfflineIntimationDetails(connection, claimsDto);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    protected void initialLog(Connection connection, Integer claimNo, UserDto user, String fieldName, String fieldValue, Integer jobRefNo) throws Exception {
        try {
            List<ClaimLogTrailDto> loggerTrailList = new ArrayList<>();
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(fieldName);
            claimLogTrailDto.setFieldValue(fieldValue);
            claimLogTrailDto.setClaimNo(claimNo);
            claimLogTrailDto.setInputDateTime(Utility.sysDateTime());
            claimLogTrailDto.setUserId(user.getUserId());
            claimLogTrailDto.setFormNameId(AppConstant.CALL_CENTER_LOG);
            loggerTrailList.add(claimLogTrailDto);
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, claimNo, 0, user.getUserId(), 1);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void saveClaimHandler(ClaimsDto claimsDto, Connection connection) throws Exception {
        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimsDto.getClaimNo());
            claimHandlerDto.setClaimStatus(ClaimStatus.CALLCENTER_PENDING.getClaimStatus());
            claimHandlerDto.setTeamId(AppConstant.ZERO_INT);
            claimHandlerDto.setAccessUserType(AppConstant.ZERO_INT);
            claimHandlerDto.setLossType(AppConstant.PARTIAL_LOSS);
            claimHandlerDto.setLeasingRefNo(AppConstant.ZERO_INT);
            claimHandlerDto.setReopenNoOfTime(AppConstant.ZERO_INT);
            claimHandlerDto.setVersionNo(AppConstant.ZERO_INT);
            claimHandlerDto.setAssignStatus(AppConstant.NO);
            claimHandlerDto.setInitLiabilityAprvDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setLiabilityAprvAssignDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setLiabilityAprvDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setLiabilityAprvDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setPenaltyBaldTyreDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setPenaltyUnderInsurceDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setInvestigationArrangeDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setIntimationChkDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setClaimPanelAssignUserDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setClaimPanelDecisionDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setFinalizeDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setSupplyOrderAssignDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setSupplyOrderCreateDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setFileStoreDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setReopenAssignUserDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setReopenDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setGenFinalRemindLetterDateTime(AppConstant.DEFAULT_DATE_TIME);
            claimHandlerDto.setInpUserId(claimsDto.getInpUser());
            claimHandlerDto.setInpDateTime(claimsDto.getInpTime());
            claimHandlerDto.setRepudiatedType(AppConstant.ZERO_INT);
            claimHandlerDto.setAssignStatus("P");
            claimHandlerDto.setInvestigationStatus(AppConstant.NO);
            claimHandlerDto.setIsLcChk1(AppConstant.NO);
            claimHandlerDto.setIsLcChk2(AppConstant.NO);
            claimHandlerDto.setIsLcChk3(AppConstant.NO);
            claimHandlerDto.setIsLcChk4(AppConstant.YES);
            claimHandlerDto.setIsLcChk5(AppConstant.NO);
            claimHandlerDto.setIsLcChk6(AppConstant.NO);
            claimHandlerDto.setIsLcChk7(AppConstant.NO);
            claimHandlerDto.setCloseStatus(AppConstant.CLOSE_STATUS_PENDING);
            claimHandlerDto.setReOpenType(AppConstant.NO);
            claimHandlerDto.setIsFileStore(AppConstant.NO);
            claimHandlerDto.setIsExcessInclude(AppConstant.NO);
            if (null != claimsDto.getPolicyDto()) {
                PolicyDto policyDto = claimsDto.getPolicyDto();

                if (policyDto.getVehicleUsage().equals("Dual purpose hiring") || policyDto.getVehicleUsage().equals("Hiring")) {
                    claimHandlerDto.setIsLcChk8("H");
                } else if (policyDto.getVehicleUsage().equals("Rent Vehicle") || policyDto.getVehicleUsage().equals("Dual Purpose Rent")) {
                    claimHandlerDto.setIsLcChk8("R");
                } else if (policyDto.getVehicleUsage().equals("Private") || policyDto.getVehicleUsage().equals("Dual purpose private")) {
                    claimHandlerDto.setIsLcChk8("P");
                }


            }

            ClaimHandlerDto claimHandler = claimHandlerDao.insertMaster(connection, claimHandlerDto);
            saveClaimProcessFlow(connection, claimsDto.getClaimNo(), claimsDto.getClaimStatus(), "Create Claim File", claimsDto.getInpUser(), claimHandler.getInpDateTime(), AppConstant.STRING_EMPTY);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    private void saveDocuments(Connection connection, ClaimsDto claimsDto, UserDto user) throws Exception {
        try {
            claimWiseDocumentService.saveAll(connection, claimsDto.getClaimNo(), user);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e);
        }
    }

    private void saveClaimsLogs(Connection connection, ClaimsDto claimsDto, UserDto user) throws Exception {
        ClaimLogsDto claimLogsDto = new ClaimLogsDto();

        try {
            claimLogsDto.setDepartmentId(AppConstant.CLAIM_HANDLER_DEPARTMENT);
            claimLogsDto.setClaimNo(claimsDto.getClaimNo());
            claimLogsDto.setUserId(user.getUserId());
            claimLogsDto.setIpAddress(user.getIpaddress());
            claimLogsDto.setHeading("Insert Claim Handler");
            claimLogsDto.setDescription("Insert Claim Handler Record. -> OK");
            claimLogsDto.setTxndate(Utility.sysDate());
            claimLogsDto.setTxntime(Utility.sysTime());
            claimLogsDao.insertMaster(connection, claimLogsDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e);
        }

    }

    private void updateOfflineIntimation(Connection connection, ClaimsDto claimsDto) throws Exception {
        try {
            OfflineIntimationDto offlineIntimationDto = new OfflineIntimationDto();
            offlineIntimationDto.setOvIntNo(claimsDto.getClaimNo().toString());
            offlineIntimationDto.setOvPolicyNo(claimsDto.getPolicyNumber());
            offlineIntimationDto.setIsfsUpdateStat(AppConstant.NO);
            offlineIntimationDto.setOvRiskNo(claimsDto.getPolicyDto().getRisk());
            offlineIntimationDto.setPolicyChannelType(claimsDto.getPolicyChannelType());
            offlineIntimationDao.updateOfflineIntimation(connection, offlineIntimationDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e);
        }
    }
}
