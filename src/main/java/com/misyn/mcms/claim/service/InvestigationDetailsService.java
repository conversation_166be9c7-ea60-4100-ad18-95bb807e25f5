package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;

import java.util.List;

public interface InvestigationDetailsService {
    InvestigationDetailsDto saveInvestigationDetails(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception;

    InvestigationDetailsDto completedInvestigationDetails(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception;

    InvestigationDetailsDto updateInvestigationPayment(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception;


    InvestigationDetailsDto cancelInvestigationDetails(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception;

    InvestigationDetailsFormDto getInvestigationDetailsFormDto(Integer claimNo, Integer txnNo);

    List<InvestigationDetailsDto> getInvestigationDetailsDtoList(Integer claimNo);

    List<InvestigationReasonDetailsDto> getInvestigationReasonDetailsDtos();

    int getSelectedImagesCount(Integer claimNo);

    InvestigationDetailsFormDto getSelectedImages(Integer claimNo);

    void sendNotificationForPendingInvestigationAssignUser();
}
