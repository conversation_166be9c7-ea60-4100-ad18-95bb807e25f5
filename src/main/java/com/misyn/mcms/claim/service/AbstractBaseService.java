package com.misyn.mcms.claim.service;


import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.NotificationPriority;
import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.Parameters;
import com.misyn.mcms.utility.Utility;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by akila on 4/16/18.
 */
public abstract class AbstractBaseService<T> {
    protected static final String THUMB_DIRECTORY = "thumb";
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractBaseService.class);
    private final String CLAIM_IMAGE_FOLDER = "images";
    private final NotificationDao notificationDao = new NotificationDaoImpl();
    private final CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private final LoggerTrailDao loggerTrailDao = new LoggerTrailDaoImpl();
    private final ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private final PolicyDao policyDao = new PolicyDaoImpl();
    private final ClaimUserAllocationHistoryDao claimUserAllocationHistoryDao = new ClaimUserAllocationHistoryDaoImpl();
    private final SupplyOrderSummaryDao supplyOrderSummaryDao = new SupplyOrderSummaryDaoImpl();
    private final ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private final MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private final RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private final UserDao userDao = new UserDaoImpl();
    protected ClaimLogsDao claimLogsDao = new ClaimLogsDaoImpl();
    protected SpecialRemarkDao specialRemarkDao = new SpecialRemarkDaoImpl();
    protected ClaimDocumentDao claimDocumentDao = new ClaimDocumentDaoImpl();
    protected ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();
    protected CommonUtilDao commonUtilDao = new CommonUtilDaoImpl();
    protected ClaimProcessFlowDao claimProcessFlowDao = new ClaimProcessFlowDaoImpl();
    protected SmsDao smsDao = new SmsDaoImpl();

    private String activeMQborkerUrl;
    private String activeMQUser;
    private String activeMQPassword;
    private String mailSendUser;
    private String appUrl;
    private String internalAppUrl;
    private Integer theftClaimPeriod = 180;
    private ConnectionPool cp = null;


    public AbstractBaseService() {
        Parameters.getInstance();
        try {
            cp = ConnectionPool.getInstance();
            activeMQUser = Parameters.getActiveMQUser();
            activeMQPassword = Parameters.getActiveMQPassword();
            activeMQborkerUrl = Parameters.getActiveMQBrokerUrl();
            mailSendUser = Parameters.getMailSendUser();
            appUrl = Parameters.getAppUrl();
            internalAppUrl = Parameters.getInternalAppUrl();
            theftClaimPeriod = Parameters.getTheftClaimPeriod();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    protected synchronized void beginTransaction(Connection connection) {
        cp.beginTransaction(connection);
    }

    protected synchronized void commitTransaction(Connection connection) {
        cp.commitTransaction(connection);
    }

    protected synchronized void rollbackTransaction(Connection connection) {
        cp.rollbackTransaction(connection);
    }


    protected synchronized String getDocumentPath(int claimNo) {
        String CLAIM_DOCUMENT_FOLDER = "documents";
        return Parameters.getClaimDocumentDirectory()
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(String.valueOf(claimNo))
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(CLAIM_DOCUMENT_FOLDER)
                .concat(AppConstant.STRING_BACKSLASH_SIGN);
    }


    protected synchronized String getImagePath(int claimNo) {

        return Parameters.getClaimDocumentDirectory()
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(String.valueOf(claimNo))
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(CLAIM_IMAGE_FOLDER)
                .concat(AppConstant.STRING_BACKSLASH_SIGN);
    }

    protected synchronized String getImageThumbPath(int claimNo) {

        return Parameters.getClaimDocumentDirectory()
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(String.valueOf(claimNo))
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(CLAIM_IMAGE_FOLDER)
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(THUMB_DIRECTORY)
                .concat(AppConstant.STRING_BACKSLASH_SIGN);
    }


    protected void saveClaimSpecialRemark(Connection connection, UserDto user, Integer claimNo, String section, String remark) throws Exception {
        try {
            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimNo);
            specialRemarkDto.setRemark(remark);
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setDepartmentId(AppConstant.CLAIM_HANDLER_DEPARTMENT);

            specialRemarkDto.setSectionName(section);

            specialRemarkDto = specialRemarkDao.insertMaster(connection, specialRemarkDto);
            if (null != specialRemarkDto) {
                saveClaimsLogs(connection, specialRemarkDto.getClaimNo(), user, section, "Insert remark to ".concat(section));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }


    protected void saveSpecialRemark(Connection connection, UserDto user, Integer claimNo, String section, String remark) throws Exception {
        try {
            SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
            specialRemarkDto.setClaimNo(claimNo);
            specialRemarkDto.setRemark(remark);
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setDepartmentId(AppConstant.CLAIM_HANDLER_DEPARTMENT);
            specialRemarkDto.setSectionName(section);
            specialRemarkDao.insertMaster(connection, specialRemarkDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    protected void saveClaimsLogs(Connection connection, Integer claimNo, UserDto user, String type, String message) throws
            Exception {
        ClaimLogsDto claimLogsDto = new ClaimLogsDto();

        try {
            claimLogsDto.setDepartmentId(AppConstant.CLAIM_HANDLER_DEPARTMENT);
            claimLogsDto.setClaimNo(claimNo);
            claimLogsDto.setUserId(user.getUserId());
            claimLogsDto.setIpAddress(user.getIpaddress());
            claimLogsDto.setHeading(type);
            claimLogsDto.setDescription(message);
            claimLogsDto.setTxndate(Utility.sysDateTime());
            claimLogsDto.setTxntime(Utility.sysTime());
            claimLogsDao.insertMaster(connection, claimLogsDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e);
        }

    }

    protected void saveUserAllocationHistory(Connection connection, Integer claimNo, String assignUserId, String inputUser, String type, Integer accessUsrType) throws Exception {
        ClaimUserAllocationHistoryDto historyDto = new ClaimUserAllocationHistoryDto();
        try {
            historyDto.setClaimNo(claimNo);
            historyDto.setAssignUserId(assignUserId);
            historyDto.setAssignDate(Utility.sysDate());
            historyDto.setAssignTime(Utility.sysTime());
            historyDto.setAssignDateTime(Utility.sysDateTime());
            historyDto.setAccessUsrType(accessUsrType);
            historyDto.setType(type);
            historyDto.setInputUser(inputUser);
            claimUserAllocationHistoryDao.insert(connection, historyDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    protected void saveNotification(Connection connection, Integer claimNo, String inputUserId, String assignUserId, String message, String url) throws Exception {
        saveNotification(connection, claimNo, inputUserId, assignUserId, message, url, "#FFFFFF");
    }

    protected void saveNotification(Connection connection, Integer claimNo, String inputUserId, String assignUserId, String message, String url, String colorCode) throws Exception {
        saveCommonNotification(connection, claimNo, inputUserId, assignUserId, message, url, colorCode, AppConstant.ZERO_INT, NotificationPriority.LOW.getNotificationPriority());
    }

    protected void saveNotification(Connection connection, Integer claimNo, String inputUserId, String assignUserId, String message, String url, int refNo) throws Exception {
        saveCommonNotification(connection, claimNo, inputUserId, assignUserId, message, url, AppConstant.DEFAULT_NOTIFICATION_COLOR_CODE, refNo, NotificationPriority.LOW.getNotificationPriority());
    }

    protected void saveNotification(Connection connection, Integer claimNo, String inputUserId, String assignUserId, String message, String url, NotificationPriority notificationPriority, String colorCode) throws Exception {
        saveCommonNotification(connection, claimNo, inputUserId, assignUserId, message, url, colorCode, AppConstant.ZERO_INT, notificationPriority.getNotificationPriority());
    }

    protected void saveNotification(Connection connection, Integer claimNo, String inputUserId, String assignUserId, String message, String url, NotificationPriority notificationPriority) throws Exception {
        saveCommonNotification(connection, claimNo, inputUserId, assignUserId, message, url, AppConstant.DEFAULT_NOTIFICATION_COLOR_CODE, AppConstant.ZERO_INT, notificationPriority.getNotificationPriority());
    }


    protected void initialLog(Connection connection, Integer claimNo, UserDto user, String fieldName, String fieldValue, Integer jobRefNo) throws Exception {
        List<ClaimLogTrailDto> loggerTrailList = new ArrayList<>();
        ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
        claimLogTrailDto.setFieldName(fieldName);
        claimLogTrailDto.setFieldValue(fieldValue);
        claimLogTrailDto.setClaimNo(claimNo);
        claimLogTrailDto.setInputDateTime(Utility.sysDateTime());
        claimLogTrailDto.setUserId(user.getUserId());
        claimLogTrailDto.setFormNameId(AppConstant.MOTOR_ENGINEER_MODULE_LOG);
        loggerTrailList.add(claimLogTrailDto);
        loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, claimNo, jobRefNo, user.getUserId(), 1);

    }

    protected List<UserDto> getBranchUserList(Connection connection, Integer claimNo) {
        List<UserDto> list = new ArrayList<>();
        try {
            ClaimsDto claims = callCenterDao.searchMaster(connection, claimNo);
            if (null != claims) {
                PolicyDto policyDto = policyDao.searchMaster(connection, claims.getPolRefNo());
                if (null != policyDto) {
                    list = claimHandlerDao.getBranchUserList(connection, policyDto.getAgentBroker());
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    protected jakarta.jms.Connection getActiveMQConnection() {
        jakarta.jms.Connection connection = null;
        try {
            ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory(activeMQUser, activeMQPassword, activeMQborkerUrl);
            connection = connectionFactory.createConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return connection;
    }

    protected void closeActiveMQConnection(jakarta.jms.Connection connection) {
        try {
            connection.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    protected String getEmailBody(Email email) {
        int place = 1;
        String msg = email.getEmailMassege();
        for (String parameter : email.getParameterEmail()) {
            msg = msg.replace(AppConstant.QUESTIONS_SYMBOLE + place + AppConstant.PERCENTAGE_SYMBOLE, parameter);
            place++;
        }
        return msg;
    }

    protected String getMailSendUser() {
        return mailSendUser;
    }

    protected String getAppUrl() {
        return appUrl;
    }

    protected String getInternalAppUrl() {
        return internalAppUrl;
    }

    protected Integer getTheftClaimPeriod() {
        return theftClaimPeriod;
    }

    protected void saveClaimProcessFlow(Connection connection, Integer claimNo, Integer status, String task, String userId, String inpDatetime, String assignUserId) throws Exception {
        try {
            ClaimProcessFlowDto claimProcessFlowDto = new ClaimProcessFlowDto();
            claimProcessFlowDto.setClaimNo(claimNo);
            claimProcessFlowDto.setClaimStatus(status);
            claimProcessFlowDto.setTask(task);
            claimProcessFlowDto.setInpUserId(userId);
            claimProcessFlowDto.setAssignUserId(assignUserId);
            claimProcessFlowDto.setInpDatetime(inpDatetime);
            claimProcessFlowDao.insert(connection, claimProcessFlowDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    protected void saveClaimProcessFlow(Connection connection, Integer claimNo, Integer status, String task, String userId, String inpDatetime, String assignUserId, String isVisible) throws Exception {
        try {
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            ClaimProcessFlowDto claimProcessFlowDto = new ClaimProcessFlowDto();
            claimProcessFlowDto.setClaimNo(claimNo);
            claimProcessFlowDto.setClaimStatus(null != claimHandlerDto ? claimHandlerDto.getClaimStatus() : status);
            claimProcessFlowDto.setTask(task);
            claimProcessFlowDto.setInpUserId(userId);
            claimProcessFlowDto.setAssignUserId(assignUserId);
            claimProcessFlowDto.setInpDatetime(inpDatetime);
            claimProcessFlowDto.setIsVisible(isVisible);
            claimProcessFlowDao.insert(connection, claimProcessFlowDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    protected void sendSmsMessage(Connection connection, int templateId, List<String> parameterList, String mobile, String policyChannelType, UserDto user, Integer claimNo, String type) throws Exception {
        String message;
        int index = 0;
        String mobileNo = (mobile == null || mobile.isEmpty()) ? AppConstant.EMPTY_STRING : mobile;
        try {
            SmsParameterDto smsParameter = smsDao.getSmsParameter(connection, templateId);
            message = smsParameter.getSmsBody();
            for (String parameter : parameterList) {
                message = message.replace(AppConstant.QUESTIONS_SYMBOLE + (++index) + AppConstant.PERCENTAGE_SYMBOLE, parameter);
            }
            SmsDto smsDto = new SmsDto();
            smsDto.setDestination(mobileNo);
            smsDto.setMessage(message);
            smsDto.setMessagePriority(AppConstant.PIRORITY);
            smsDto.setMessageType(AppConstant.MESSAGE_TYPE);
            smsDto.setStatus(AppConstant.MESSAGE_STATUS);
            smsDto.setInsertDateTime(Utility.sysDateTime());
            smsDto.setSendDateTime(AppConstant.DEFAULT_DATE_TIME);
            smsDto.setSmsStatus(AppConstant.SEND_STATUS_NEW);
            smsDto.setPolicyChannelType(policyChannelType);
            smsDao.insertMaster(connection, smsDto);
            if (mobileNo.isEmpty()) {
                mobileNo = "DESTINATION MOBILE NUMBER NOT AVAILABLE";
            }
            saveClaimsLogs(connection, claimNo, user, type, message.concat(String.format(" -> Recipient : %s", mobileNo)));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System error");
        }
    }

    protected String getNearestCity(Connection connection, int cityCode) {
        String nearestCity = commonUtilDao.findOne(connection, "claim_gramas", "V_GRAMA_NAME", "N_GRAMA_CODE=" + cityCode);
        return nearestCity == null ? "N/A" : nearestCity;
    }

    protected void shiftNotificationOnAction(Connection connection, Integer claimNo, UserDto user) throws Exception {
        try {
            notificationDao.shiftNotificationOnAction(connection, claimNo, user);
            notificationDao.deleteShiftedNotification(connection, claimNo, user);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    protected List<String> getAssignUserbyClaimNo(Connection connection, Integer claimNo) throws Exception {
        try {
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            Integer status = claimHandlerDto.getClaimStatus();
            List<String> assignUserId = new ArrayList<>();
            switch (status) {
                case 35:
                case 37:
                    assignUserId.add(claimHandlerDto.getInitLiabilityAssignUserId());
                    break;
                case 36:
                case 49:
                case 51:
                    assignUserId.add(claimHandlerDto.getAssignUserId());
                    break;
                case 38:
                case 41:
                case 42:
                case 44:
                case 45:
                case 47:
                case 48:
                case 55:
                case 56:
                case 68:
                    assignUserId.add(claimHandlerDto.getDecisionMakingAssignUserId());
                    break;
                case 39:
                    List<UserDto> uSersByAccessUsrType = userDao.getUSersByAccessUsrType(connection, AppConstant.ACCESS_LEVEL_SUB_PANEL);
                    for (UserDto userDto : uSersByAccessUsrType) {
                        assignUserId.add(userDto.getUserId());
                    }
                    break;
                case 40:
                    List<UserDto> uSersByAccessUsrType1 = userDao.getUSersByAccessUsrType(connection, AppConstant.ACCESS_LEVEL_MAIN_PANEL);
                    for (UserDto userDto : uSersByAccessUsrType1) {
                        assignUserId.add(userDto.getUserId());
                    }
                    break;
                case 50:
                    ClaimCalculationSheetMainDto activeCalsheet = claimCalculationSheetMainDao.getLatestCalsheet(connection, claimNo);
                    if (null == activeCalsheet) {
                        if (null != claimHandlerDto.getSupplyOrderAssignStatus() && !claimHandlerDto.getSupplyOrderAssignStatus().isEmpty() && claimHandlerDto.getSupplyOrderAssignStatus().equals(AppConstant.YES)) {
                            SupplyOrderSummaryDto ongoingSupplyOrder = supplyOrderSummaryDao.getOngoingSupplyOrder(connection, claimNo);
                            if (null != ongoingSupplyOrder) {
                                switch (ongoingSupplyOrder.getSupplyOrderStatus()) {
                                    case "SCRUTINIZING-F":
                                        assignUserId.add(ongoingSupplyOrder.getApprvAssignScrutinizingUserId());
                                        break;
                                    case "SCRUTINIZING-A":
                                        assignUserId.add(ongoingSupplyOrder.getApprvScrutinizingUserId());
                                        break;
                                    default:
                                        assignUserId.add(ongoingSupplyOrder.getApproveAssignSparePartCoordinator());
                                        break;
                                }
                            } else {
                                assignUserId.add(claimHandlerDto.getSupplyOrderAssignUser());
                            }
                        } else {
                            assignUserId.add(claimHandlerDto.getAssignUserId());
                        }
                    } else {
                        assignUserId.add(getCalsheetAssignUser(activeCalsheet));
                    }
                    break;
                case 52:
                case 53:
                    assignUserId.add(claimHandlerDto.getInvestigationAssignUserId());
                    break;
                case 54:
                    assignUserId.add(claimHandlerDto.getInvestigationArrangeUserId());
                    break;
                case 57:
                    assignUserId.add(claimHandlerDto.getSpecialApprovalUserId());
                    break;
            }
            return assignUserId;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    protected String getCalsheetAssignUser(ClaimCalculationSheetMainDto calSheet) {
        Integer calSheetStatus = calSheet.getStatus();
        return switch (calSheetStatus) {
            case 58 -> calSheet.getAssignUserId();
            case 59 -> calSheet.getSparePartCordinatorAssignUserId();
            case 61 -> calSheet.getScrutinizeTeamAssignUserId();
            case 63 -> calSheet.getSpecialTeamAssignUserId();
            case 64 -> calSheet.getSpecialTeamMofaAssignUserId();
            case 65 -> calSheet.getAprUserId();
            case 66, 67, 70, 72, 73 -> calSheet.getAssignUserId();
            case 71 -> calSheet.getRteAssignUserId();
            default -> null;
        };
    }

    protected void sendNotificationForJobPendingRte(Integer claimNo, UserDto user, Connection connection, boolean state) throws Exception {
        try {
            List<String> rteList = motorEngineerDetailsDao.getPendingInspectionReportingRteList(connection, claimNo);

            String URL = "/MotorEngineerController/jobView?TYPE=30&claimNo=" + claimNo;
            String userId = user.getAccessUserType() == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR ? "Spare Parts Coordinator" : "Scrutinizing Team";
            String notificationMsg = state ? "You have received a claim file with pending inspections by " + userId : "Claim File (" + claimNo + ") with pending inspection(s) has been recalled by " + userId;
            if (null != rteList && !rteList.isEmpty()) {
                for (String rteUser : rteList) {
                    saveNotification(connection, claimNo, user.getUserId(), rteUser, notificationMsg, URL, "#FFFFBF");
                    if (state) {
                        saveClaimsLogs(connection, claimNo, user, "Pending Inspection Reminder for ".concat(rteUser), "File Pending in - "
                                .concat("[").concat(user.getUserId()).concat("]"));
                    }
                    saveClaimProcessFlow(connection, claimNo, 0, state ? "File Pending in [" + user.getUserId() + "] due to Unauthorized Inspections" : "File Pending in [" + user.getUserId() + "] has been recalled by Claim Handler", user.getUserId(), Utility.sysDateTime(), rteUser, AppConstant.NO);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    protected void sendNotificationOnAuthorizedInspections(Connection connection, Integer claimNo, UserDto user, boolean isAuth, Integer inspectionId) {
        try {
            if (!motorEngineerDetailsDao.isInspectionPending(connection, claimNo, inspectionId) && !claimHandlerDao.isClaimForwardedToEngineer(connection, claimNo, null)) {
                String uri;
                String uriCalsheet = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                        .concat("&P_CAL_SHEET_NO=");
                String uriNonCalsheet = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                        .concat("&TYPE=4")
                        .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
                String message;
                boolean isAriOrSalvage = inspectionId.equals(AppConstant.ARI_INSPECTION) || inspectionId.equals(AppConstant.SALVAGE_INSPECTION);
                RtePendingClaimsDto pendingJobs = rtePendingClaimDetailDao.checkRtePendingJobs(connection, claimNo);
                if (null != pendingJobs && pendingJobs.getAccessUserType().equals(AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR)) {
                    if (isAriOrSalvage) {
                        message = "ARI salvage inspection reviewed claim file assigned for Spare Parts Coordinator";
                    } else {
                        if (isAuth) message = "You have received a Claim File with authorized Inspections";
                        else message = "No more Pending Inspections Available for this Claim";
                    }
                    ClaimCalculationSheetMainDto calSheet = claimCalculationSheetMainDao.getSparePartsCoordFromClaimNo(connection, claimNo);
                    if (null != calSheet) {
                        uri = uriCalsheet
                                .concat(String.valueOf(calSheet.getCalSheetId()))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(9));
                        saveNotification(connection, claimNo, user.getUserId(), calSheet.getSparePartCordinatorAssignUserId(), message, uri, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, isAuth ? "Authorized Inspection Reminder for ".concat(calSheet.getSparePartCordinatorAssignUserId()) : "Pending Inspection Rejected", isAuth ? "File Pending in - "
                                .concat("[").concat(calSheet.getSparePartCordinatorAssignUserId()).concat("]").concat(" has been Authorized") : "Available Pending inspection has been Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, isAuth ? "File Pending in [" + pendingJobs.getUserId() + "] has been Authorized" : "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), calSheet.getSparePartCordinatorAssignUserId(), AppConstant.NO);
                    } else {
                        saveNotification(connection, claimNo, user.getUserId(), pendingJobs.getUserId(), message, uriNonCalsheet, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, isAuth ? "Authorized Inspection Reminder for ".concat(pendingJobs.getUserId()) : "Pending Inspection Rejected", isAuth ? "File Pending in - "
                                .concat("[").concat(pendingJobs.getUserId()).concat("]").concat(" has been Authorized") : "Available Pending inspection has been Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, isAuth ? "File Pending in [" + pendingJobs.getUserId() + "] has been Authorized" : "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), pendingJobs.getUserId(), AppConstant.NO);
                    }
                } else if (null != pendingJobs && pendingJobs.getAccessUserType().equals(AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM)) {
                    if (isAriOrSalvage) {
                        message = "ARI salvage inspection reviewed claim file assigned for Scrutinizing Team";
                    } else {
                        if (isAuth) message = "You have received a completed claim file from RTE for Scrutinizing";
                        else message = "No more Pending Inspections Available for this Claim";
                    }
                    ClaimCalculationSheetMainDto calSheet = claimCalculationSheetMainDao.getScrutinizingUserFromClaimNo(connection, claimNo);
                    if (null != calSheet) {
                        uri = uriCalsheet
                                .concat(String.valueOf(calSheet.getCalSheetId()))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(9));
                        saveNotification(connection, claimNo, user.getUserId(), calSheet.getScrutinizeTeamAssignUserId(), message, uri, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, isAuth ? "Authorized Inspection Reminder for ".concat(calSheet.getScrutinizeTeamAssignUserId()) : "Pending Inspection Rejected", isAuth ? "File Pending in - "
                                .concat("[").concat(calSheet.getScrutinizeTeamAssignUserId()).concat("]").concat(" has been Authorized") : "Pending Inspection Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, isAuth ? "File Pending in [" + calSheet.getScrutinizeTeamAssignUserId() + "] has been Authorized" : "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), calSheet.getScrutinizeTeamAssignUserId(), AppConstant.NO);
                    } else {
                        saveNotification(connection, claimNo, user.getUserId(), pendingJobs.getUserId(), message, uriNonCalsheet, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, isAuth ? "Authorized Inspection Reminder for ".concat(pendingJobs.getUserId()) : "Pending Inspection Rejected", isAuth ? "File Pending in - "
                                .concat("[").concat(pendingJobs.getUserId()).concat("]").concat(" has been Authorized") : "Available Pending inspection has been Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, isAuth ? "File Pending in [" + pendingJobs.getUserId() + "] has been Authorized" : "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), pendingJobs.getUserId(), AppConstant.NO);
                    }
                }
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    protected void sendNotificationForAssignUser(Connection connection, Integer claimNo, UserDto user, Integer inspectionId) {
        try {
            if (!motorEngineerDetailsDao.isInspectionPending(connection, claimNo, inspectionId) && !claimHandlerDao.isClaimForwardedToEngineer(connection, claimNo, null)) {
                String uri;
                String uriCalSheet = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                        .concat("&P_CAL_SHEET_NO=");
                String uriNonCalsheet = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                        .concat("&TYPE=4")
                        .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
                RtePendingClaimsDto pendingJobs = rtePendingClaimDetailDao.checkRtePendingJobs(connection, claimNo);
                if (null != pendingJobs && pendingJobs.getAccessUserType().equals(AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR)) {
                    ClaimCalculationSheetMainDto calSheet = claimCalculationSheetMainDao.getSparePartsCoordFromClaimNo(connection, claimNo);
                    if (null != calSheet) {
                        uri = uriCalSheet
                                .concat(String.valueOf(calSheet.getCalSheetId()))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(9));
                        saveNotification(connection, claimNo, user.getUserId(), calSheet.getSparePartCordinatorAssignUserId(), "No more Pending Inspections Available for this Claim", uri, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, "Pending Inspection Rejected", "Available Pending inspection has been Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), calSheet.getSparePartCordinatorAssignUserId(), AppConstant.NO);
                    } else {
                        saveNotification(connection, claimNo, user.getUserId(), pendingJobs.getUserId(), "No more Pending Inspections Available for this Claim", uriNonCalsheet, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, "Pending Inspection Rejected", "Available Pending inspection has been Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), pendingJobs.getUserId(), AppConstant.NO);
                    }
                } else if (null != pendingJobs && pendingJobs.getAccessUserType().equals(AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM)) {
                    ClaimCalculationSheetMainDto calSheet = claimCalculationSheetMainDao.getScrutinizingUserFromClaimNo(connection, claimNo);
                    if (null != calSheet) {
                        uri = uriCalSheet
                                .concat(String.valueOf(calSheet.getCalSheetId()))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(9));
                        saveNotification(connection, claimNo, user.getUserId(), calSheet.getScrutinizeTeamAssignUserId(), "No more Pending Inspections Available for this Claim", uri, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, "Pending Inspection Rejected", "Available Pending inspection has been Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), calSheet.getScrutinizeTeamAssignUserId(), AppConstant.NO);
                    } else {
                        saveNotification(connection, claimNo, user.getUserId(), pendingJobs.getUserId(), "No more Pending Inspections Available for this Claim", uriNonCalsheet, "#FFFFBF");
                        saveClaimsLogs(connection, claimNo, user, "Pending Inspection Rejected", "Available Pending inspection has been Rejected");
                        saveClaimProcessFlow(connection, claimNo, 0, "Pending Inspection Rejected", user.getUserId(), Utility.sysDateTime(), pendingJobs.getUserId(), AppConstant.NO);
                    }
                }
                rtePendingClaimDetailDao.removePendingJobs(connection, claimNo);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    protected String getPolicyChannelTypeByClaimNo(Connection connection, Integer claimNo) throws Exception {
        try {
            return callCenterDao.getPolicyChannelType(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    protected String getPolicyChannelTypeByVehicleNo(Connection connection, String vehicleNo) throws Exception {
        try {
            return callCenterDao.getPolicyChannelTypeByVehicleNo(connection, vehicleNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    protected String formatDate(String date) throws Exception {
        try {
            return date.replace("-", "/");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    protected List<UserDto> getUsersByAccessUserType(Connection connection, Integer accessUsrType) throws Exception {
        try {
            return userDao.getUSersByAccessUsrType(connection, accessUsrType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    protected void saveCommonNotification(Connection connection,
                                          Integer claimNo,
                                          String inputUserId,
                                          String assignUserId,
                                          String message,
                                          String url,
                                          String colorCode,
                                          int refNo,
                                          String notificationPriority) throws Exception {
        NotificationDto notificationDto = new NotificationDto();
        try {
            ClaimsDto claimsDto = callCenterDao.searchNotifation(connection, claimNo);
            if (null != claimsDto) {
                notificationDto.setVehicleNo(claimsDto.getPolicyDto().getVehicleNumber());
                notificationDto.setCoverNoteNo(claimsDto.getPolicyDto().getCoverNoteNo());
                notificationDto.setAccidentDate(claimsDto.getAccidDate());
                notificationDto.setPolicyChannelType(claimsDto.getPolicyDto().getPolicyChannelType());
                notificationDto.setCategoryDesc(claimsDto.getPolicyDto().getCategoryDescription());
                notificationDto.setProductName(claimsDto.getPolicyDto().getProduct());
            }
            notificationDto.setInpUserId(inputUserId);
            notificationDto.setMessage(message);
            notificationDto.setClaimNo(claimNo);
            notificationDto.setNotifyDateTime(Utility.sysDateTime());
            notificationDto.setReadDateTime(AppConstant.DEFAULT_DATE_TIME);
            notificationDto.setAssignUserId(assignUserId);
            notificationDto.setUrl(url);
            notificationDto.setColorCode(colorCode);
            notificationDto.setRefNo(refNo);
            notificationDto.setPriorityStatus(notificationPriority);
            notificationDao.insertMaster(connection, notificationDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    protected String replaceSpecialChar(String inputString) throws Exception {
        Map<Integer, Integer> strMap = new HashMap<>();
        Map<Integer, Integer> escChar = new HashMap<>();
        populateCharacterReplacementMap(strMap, escChar);
        return replaceCharactersWithSpace(inputString, strMap, escChar);
    }

    private void populateCharacterReplacementMap(Map<Integer, Integer> strMap, Map<Integer, Integer> escChar) throws Exception {

        strMap.put(128, 67);
        strMap.put(129, 36);
        strMap.put(130, 44);
        strMap.put(131, 70);
        strMap.put(132, 34);
        strMap.put(133, 46);
        strMap.put(134, 43);
        strMap.put(135, 43);
        strMap.put(136, 94);
        strMap.put(137, 37);
        strMap.put(138, 83);
        strMap.put(139, 60);
        strMap.put(140, 36);
        strMap.put(141, 36);
        strMap.put(142, 90);
        strMap.put(143, 36);
        strMap.put(144, 36);
        strMap.put(145, 39);
        strMap.put(146, 39);
        strMap.put(147, 34);
        strMap.put(148, 34);
        strMap.put(149, 1);
        strMap.put(150, 36);
        strMap.put(151, 36);
        strMap.put(152, 126);
        strMap.put(153, 36);
        strMap.put(154, 83);
        strMap.put(155, 41);
        strMap.put(156, 36);
        strMap.put(157, 36);
        strMap.put(158, 90);
        strMap.put(159, 89);
        strMap.put(160, 36);
        strMap.put(161, 36);
        strMap.put(162, 36);
        strMap.put(163, 36);
        strMap.put(164, 36);
        strMap.put(165, 36);
        strMap.put(166, 36);
        strMap.put(167, 36);
        strMap.put(168, 36);
        strMap.put(169, 36);
        strMap.put(170, 36);
        strMap.put(171, 36);
        strMap.put(172, 36);
        strMap.put(173, 36);
        strMap.put(174, 36);
        strMap.put(175, 36);
        strMap.put(176, 36);
        strMap.put(177, 36);
        strMap.put(178, 36);
        strMap.put(179, 36);
        strMap.put(180, 36);
        strMap.put(181, 36);
        strMap.put(182, 36);
        strMap.put(183, 36);
        strMap.put(184, 36);
        strMap.put(185, 36);
        strMap.put(186, 36);
        strMap.put(187, 36);
        strMap.put(188, 36);
        strMap.put(189, 36);
        strMap.put(190, 36);
        strMap.put(191, 36);
        strMap.put(192, 36);
        strMap.put(193, 36);
        strMap.put(194, 36);
        strMap.put(195, 36);
        strMap.put(196, 36);
        strMap.put(197, 36);
        strMap.put(198, 36);
        strMap.put(199, 36);
        strMap.put(200, 36);
        strMap.put(201, 36);
        strMap.put(202, 36);
        strMap.put(203, 36);
        strMap.put(204, 36);
        strMap.put(205, 36);
        strMap.put(206, 36);
        strMap.put(207, 36);
        strMap.put(208, 36);
        strMap.put(209, 36);
        strMap.put(210, 36);
        strMap.put(211, 36);
        strMap.put(212, 36);
        strMap.put(213, 36);
        strMap.put(214, 36);
        strMap.put(215, 36);
        strMap.put(216, 36);
        strMap.put(217, 36);
        strMap.put(218, 36);
        strMap.put(219, 36);
        strMap.put(220, 36);
        strMap.put(221, 36);
        strMap.put(222, 36);
        strMap.put(223, 36);
        strMap.put(224, 36);
        strMap.put(225, 36);
        strMap.put(226, 36);
        strMap.put(227, 36);
        strMap.put(228, 36);
        strMap.put(229, 36);
        strMap.put(230, 36);
        strMap.put(231, 36);
        strMap.put(232, 36);
        strMap.put(233, 36);
        strMap.put(234, 36);
        strMap.put(235, 36);
        strMap.put(236, 36);
        strMap.put(237, 36);
        strMap.put(238, 36);
        strMap.put(239, 36);
        strMap.put(240, 36);
        strMap.put(241, 36);
        strMap.put(242, 36);
        strMap.put(243, 36);
        strMap.put(244, 36);
        strMap.put(245, 36);
        strMap.put(246, 36);
        strMap.put(247, 36);
        strMap.put(248, 36);
        strMap.put(249, 36);
        strMap.put(250, 36);
        strMap.put(251, 36);
        strMap.put(252, 36);
        strMap.put(253, 36);
        strMap.put(254, 36);
        strMap.put(255, 36);
        strMap.put(256, 65);
        strMap.put(257, 65);
        strMap.put(258, 65);
        strMap.put(259, 65);
        strMap.put(260, 65);
        strMap.put(261, 65);
        strMap.put(262, 67);
        strMap.put(263, 67);
        strMap.put(264, 67);
        strMap.put(265, 67);
        strMap.put(266, 67);
        strMap.put(267, 67);
        strMap.put(268, 67);
        strMap.put(269, 67);
        strMap.put(270, 68);
        strMap.put(271, 68);
        strMap.put(272, 68);
        strMap.put(273, 68);
        strMap.put(274, 69);
        strMap.put(275, 69);
        strMap.put(276, 69);
        strMap.put(277, 69);
        strMap.put(278, 69);
        strMap.put(279, 69);
        strMap.put(280, 69);
        strMap.put(281, 69);
        strMap.put(282, 69);
        strMap.put(283, 69);
        strMap.put(284, 71);
        strMap.put(285, 71);
        strMap.put(286, 71);
        strMap.put(287, 71);
        strMap.put(288, 71);
        strMap.put(289, 71);
        strMap.put(290, 71);
        strMap.put(291, 71);
        strMap.put(292, 72);
        strMap.put(293, 72);
        strMap.put(294, 72);
        strMap.put(295, 72);
        strMap.put(296, 73);
        strMap.put(297, 73);
        strMap.put(298, 73);
        strMap.put(299, 73);
        strMap.put(300, 73);
        strMap.put(301, 73);
        strMap.put(302, 74);
        strMap.put(303, 74);
        strMap.put(304, 73);
        strMap.put(305, 73);
        strMap.put(306, 36);
        strMap.put(307, 36);
        strMap.put(308, 74);
        strMap.put(309, 74);
        strMap.put(310, 75);
        strMap.put(311, 75);
        strMap.put(312, 75);
        strMap.put(313, 76);
        strMap.put(314, 76);
        strMap.put(315, 76);
        strMap.put(316, 76);
        strMap.put(317, 76);
        strMap.put(318, 76);
        strMap.put(319, 76);
        strMap.put(320, 76);
        strMap.put(321, 76);
        strMap.put(322, 76);
        strMap.put(323, 78);
        strMap.put(324, 78);
        strMap.put(325, 78);
        strMap.put(326, 78);
        strMap.put(327, 78);
        strMap.put(328, 78);
        strMap.put(329, 78);
        strMap.put(330, 78);
        strMap.put(331, 78);
        strMap.put(332, 79);
        strMap.put(333, 79);
        strMap.put(334, 79);
        strMap.put(335, 79);
        strMap.put(336, 79);
        strMap.put(337, 79);
        strMap.put(338, 36);
        strMap.put(339, 36);
        strMap.put(340, 82);
        strMap.put(341, 82);
        strMap.put(342, 82);
        strMap.put(343, 82);
        strMap.put(344, 82);
        strMap.put(345, 82);
        strMap.put(346, 83);
        strMap.put(347, 83);
        strMap.put(348, 83);
        strMap.put(349, 83);
        strMap.put(350, 83);
        strMap.put(351, 83);
        strMap.put(352, 83);
        strMap.put(353, 83);
        strMap.put(354, 84);
        strMap.put(355, 84);
        strMap.put(356, 84);
        strMap.put(357, 84);
        strMap.put(358, 84);
        strMap.put(359, 84);
        strMap.put(360, 85);
        strMap.put(361, 85);
        strMap.put(362, 85);
        strMap.put(363, 85);
        strMap.put(364, 85);
        strMap.put(365, 85);
        strMap.put(366, 85);
        strMap.put(367, 85);
        strMap.put(368, 85);
        strMap.put(369, 85);
        strMap.put(370, 85);
        strMap.put(371, 85);
        strMap.put(372, 87);
        strMap.put(373, 87);
        strMap.put(374, 89);
        strMap.put(375, 89);
        strMap.put(376, 89);
        strMap.put(377, 90);
        strMap.put(378, 90);
        strMap.put(379, 90);
        strMap.put(380, 90);
        strMap.put(381, 90);
        strMap.put(382, 90);
        strMap.put(383, 76);
        strMap.put(384, 66);
        strMap.put(385, 66);
        strMap.put(386, 66);
        strMap.put(387, 66);
        strMap.put(388, 66);
        strMap.put(389, 66);
        strMap.put(390, 67);
        strMap.put(391, 67);
        strMap.put(392, 67);
        strMap.put(393, 68);
        strMap.put(394, 68);
        strMap.put(395, 68);
        strMap.put(396, 68);
        strMap.put(397, 68);
        strMap.put(398, 69);
        strMap.put(399, 69);
        strMap.put(400, 69);
        strMap.put(401, 70);
        strMap.put(402, 70);
        strMap.put(403, 71);
        strMap.put(404, 36);
        strMap.put(405, 36);
        strMap.put(406, 76);
        strMap.put(407, 73);
        strMap.put(408, 75);
        strMap.put(409, 75);
        strMap.put(410, 76);
        strMap.put(411, 36);
        strMap.put(412, 36);
        strMap.put(413, 78);
        strMap.put(414, 78);
        strMap.put(415, 79);
        strMap.put(416, 79);
        strMap.put(417, 79);
        strMap.put(418, 36);
        strMap.put(419, 36);
        strMap.put(420, 80);
        strMap.put(421, 80);
        strMap.put(422, 82);
        strMap.put(423, 83);
        strMap.put(424, 83);
        strMap.put(425, 36);
        strMap.put(426, 36);
        strMap.put(427, 84);
        strMap.put(428, 84);
        strMap.put(429, 84);
        strMap.put(430, 84);
        strMap.put(431, 85);
        strMap.put(432, 85);
        strMap.put(433, 85);
        strMap.put(434, 85);
        strMap.put(435, 89);
        strMap.put(436, 89);
        strMap.put(437, 90);
        strMap.put(438, 90);
        strMap.put(439, 36);
        strMap.put(440, 36);
        strMap.put(441, 36);
        strMap.put(442, 36);
        strMap.put(443, 36);
        strMap.put(444, 36);
        strMap.put(445, 36);
        strMap.put(446, 36);
        strMap.put(447, 36);
        strMap.put(448, 36);
        strMap.put(449, 36);
        strMap.put(450, 36);
        strMap.put(451, 36);
        strMap.put(452, 36);
        strMap.put(453, 36);
        strMap.put(454, 36);
        strMap.put(455, 36);
        strMap.put(456, 36);
        strMap.put(457, 36);
        strMap.put(458, 36);
        strMap.put(459, 36);
        strMap.put(460, 36);
        strMap.put(461, 65);
        strMap.put(462, 65);
        strMap.put(463, 73);
        strMap.put(464, 73);
        strMap.put(465, 79);
        strMap.put(466, 79);
        strMap.put(467, 85);
        strMap.put(468, 85);
        strMap.put(469, 85);
        strMap.put(470, 85);
        strMap.put(471, 85);
        strMap.put(472, 85);
        strMap.put(473, 85);
        strMap.put(474, 85);
        strMap.put(475, 85);
        strMap.put(476, 85);
        strMap.put(477, 69);
        strMap.put(478, 65);
        strMap.put(479, 65);
        strMap.put(480, 65);
        strMap.put(481, 65);
        strMap.put(482, 36);
        strMap.put(483, 36);
        strMap.put(484, 71);
        strMap.put(485, 71);
        strMap.put(486, 71);
        strMap.put(487, 71);
        strMap.put(488, 75);
        strMap.put(489, 75);
        strMap.put(490, 81);
        strMap.put(491, 81);
        strMap.put(492, 81);
        strMap.put(493, 81);
        strMap.put(494, 36);
        strMap.put(495, 36);
        strMap.put(496, 74);
        strMap.put(497, 36);
        strMap.put(498, 36);
        strMap.put(499, 36);
        strMap.put(500, 71);
        strMap.put(501, 71);
        strMap.put(502, 36);
        strMap.put(503, 80);
        strMap.put(504, 78);
        strMap.put(505, 78);
        strMap.put(506, 65);
        strMap.put(507, 65);
        strMap.put(508, 36);
        strMap.put(509, 36);
        strMap.put(510, 79);
        strMap.put(511, 79);
        strMap.put(512, 65);
        strMap.put(513, 65);
        strMap.put(514, 65);
        strMap.put(515, 65);
        strMap.put(516, 69);
        strMap.put(517, 69);
        strMap.put(518, 69);
        strMap.put(519, 69);
        strMap.put(520, 73);
        strMap.put(521, 73);
        strMap.put(522, 73);
        strMap.put(523, 73);
        strMap.put(524, 79);
        strMap.put(525, 79);
        strMap.put(526, 79);
        strMap.put(527, 79);
        strMap.put(528, 82);
        strMap.put(529, 82);
        strMap.put(530, 82);
        strMap.put(531, 82);
        strMap.put(532, 85);
        strMap.put(533, 85);
        strMap.put(534, 85);
        strMap.put(535, 85);
        strMap.put(536, 83);
        strMap.put(537, 83);
        strMap.put(538, 84);
        strMap.put(539, 84);
        strMap.put(540, 36);
        strMap.put(541, 36);
        strMap.put(542, 72);
        strMap.put(543, 72);
        strMap.put(546, 36);
        strMap.put(547, 36);
        strMap.put(548, 90);
        strMap.put(549, 90);
        strMap.put(550, 65);
        strMap.put(551, 65);
        strMap.put(552, 69);
        strMap.put(553, 69);
        strMap.put(554, 79);
        strMap.put(555, 79);
        strMap.put(556, 79);
        strMap.put(557, 79);
        strMap.put(558, 79);
        strMap.put(559, 79);
        strMap.put(560, 79);
        strMap.put(561, 79);
        strMap.put(562, 89);
        strMap.put(563, 89);
        strMap.put(592, 65);
        strMap.put(593, 65);
        strMap.put(594, 65);
        strMap.put(595, 66);
        strMap.put(596, 67);
        strMap.put(597, 67);
        strMap.put(598, 68);
        strMap.put(599, 68);
        strMap.put(600, 69);
        strMap.put(601, 69);
        strMap.put(602, 69);
        strMap.put(603, 69);
        strMap.put(604, 69);
        strMap.put(605, 69);
        strMap.put(606, 36);
        strMap.put(607, 70);
        strMap.put(608, 70);
        strMap.put(609, 71);
        strMap.put(610, 71);
        strMap.put(611, 89);
        strMap.put(612, 89);
        strMap.put(613, 89);
        strMap.put(614, 72);
        strMap.put(615, 72);
        strMap.put(616, 73);
        strMap.put(617, 76);
        strMap.put(618, 73);
        strMap.put(619, 73);
        strMap.put(620, 73);
        strMap.put(621, 76);
        strMap.put(622, 36);
        strMap.put(623, 87);
        strMap.put(624, 87);
        strMap.put(625, 77);
        strMap.put(626, 78);
        strMap.put(627, 78);
        strMap.put(628, 78);
        strMap.put(629, 79);
        strMap.put(630, 36);
        strMap.put(631, 36);
        strMap.put(632, 36);
        strMap.put(633, 76);
        strMap.put(634, 76);
        strMap.put(635, 36);
        strMap.put(636, 36);
        strMap.put(637, 36);
        strMap.put(638, 82);
        strMap.put(639, 36);
        strMap.put(640, 82);
        strMap.put(641, 36);
        strMap.put(642, 83);
        strMap.put(643, 36);
        strMap.put(644, 36);
        strMap.put(645, 36);
        strMap.put(646, 36);
        strMap.put(647, 36);
        strMap.put(648, 36);
        strMap.put(649, 85);
        strMap.put(650, 85);
        strMap.put(651, 85);
        strMap.put(652, 36);
        strMap.put(653, 36);
        strMap.put(654, 36);
        strMap.put(655, 89);
        strMap.put(656, 90);
        strMap.put(657, 90);
        strMap.put(658, 36);
        strMap.put(659, 36);
        strMap.put(660, 36);
        strMap.put(661, 36);
        strMap.put(662, 36);
        strMap.put(663, 67);
        strMap.put(664, 79);
        strMap.put(665, 36);
        strMap.put(666, 36);
        strMap.put(667, 71);
        strMap.put(668, 72);
        strMap.put(669, 36);
        strMap.put(670, 36);
        strMap.put(671, 76);
        strMap.put(672, 36);
        strMap.put(673, 36);
        strMap.put(674, 36);
        strMap.put(675, 36);
        strMap.put(676, 36);
        strMap.put(677, 36);
        strMap.put(678, 36);
        strMap.put(679, 36);
        strMap.put(680, 36);
        strMap.put(681, 36);
        strMap.put(682, 36);
        strMap.put(683, 36);
        strMap.put(684, 36);
        strMap.put(685, 36);
        strMap.put(688, 36);
        strMap.put(689, 72);
        strMap.put(690, 74);
        strMap.put(691, 82);
        strMap.put(692, 36);
        strMap.put(693, 36);
        strMap.put(694, 36);
        strMap.put(695, 87);
        strMap.put(696, 89);
        strMap.put(697, 39);
        strMap.put(698, 34);
        strMap.put(699, 39);
        strMap.put(700, 39);
        strMap.put(701, 39);
        strMap.put(702, 39);
        strMap.put(703, 39);
        strMap.put(704, 39);
        strMap.put(705, 39);
        strMap.put(706, 60);
        strMap.put(707, 62);
        strMap.put(708, 94);
        strMap.put(709, 36);
        strMap.put(710, 94);
        strMap.put(711, 36);
        strMap.put(712, 39);
        strMap.put(713, 36);
        strMap.put(714, 39);
        strMap.put(715, 39);
        strMap.put(716, 44);
        strMap.put(717, 95);
        strMap.put(718, 46);
        strMap.put(719, 44);
        strMap.put(720, 58);
        strMap.put(721, 46);
        strMap.put(722, 44);
        strMap.put(723, 44);
        strMap.put(724, 36);
        strMap.put(725, 36);
        strMap.put(726, 36);
        strMap.put(727, 36);
        strMap.put(728, 36);
        strMap.put(729, 36);
        strMap.put(730, 36);
        strMap.put(731, 36);
        strMap.put(732, 36);
        strMap.put(733, 36);
        strMap.put(734, 36);
        strMap.put(735, 36);
        strMap.put(736, 36);
        strMap.put(737, 36);
        strMap.put(738, 36);
        strMap.put(739, 36);
        strMap.put(740, 36);
        strMap.put(741, 36);
        strMap.put(742, 36);
        strMap.put(743, 36);
        strMap.put(744, 36);
        strMap.put(745, 36);
        strMap.put(746, 36);
        strMap.put(747, 36);
        strMap.put(748, 36);
        strMap.put(749, 36);
        strMap.put(750, 36);
        strMap.put(768, 36);
        strMap.put(769, 36);
        strMap.put(770, 36);
        strMap.put(771, 36);
        strMap.put(772, 36);
        strMap.put(773, 36);
        strMap.put(774, 36);
        strMap.put(775, 36);
        strMap.put(776, 36);
        strMap.put(777, 36);
        strMap.put(778, 36);
        strMap.put(779, 36);
        strMap.put(780, 36);
        strMap.put(781, 36);
        strMap.put(782, 36);
        strMap.put(783, 36);
        strMap.put(784, 36);
        strMap.put(785, 36);
        strMap.put(786, 36);
        strMap.put(787, 36);
        strMap.put(788, 36);
        strMap.put(789, 36);
        strMap.put(790, 36);
        strMap.put(791, 36);
        strMap.put(792, 36);
        strMap.put(793, 36);
        strMap.put(794, 36);
        strMap.put(795, 36);
        strMap.put(796, 36);
        strMap.put(797, 36);
        strMap.put(798, 36);
        strMap.put(799, 36);
        strMap.put(800, 36);
        strMap.put(801, 36);
        strMap.put(802, 36);
        strMap.put(803, 36);
        strMap.put(804, 36);
        strMap.put(805, 36);
        strMap.put(806, 36);
        strMap.put(807, 36);
        strMap.put(808, 36);
        strMap.put(809, 36);
        strMap.put(810, 36);
        strMap.put(811, 36);
        strMap.put(812, 36);
        strMap.put(813, 36);
        strMap.put(814, 36);
        strMap.put(815, 36);
        strMap.put(816, 36);
        strMap.put(817, 36);
        strMap.put(818, 36);
        strMap.put(819, 36);
        strMap.put(820, 36);
        strMap.put(821, 36);
        strMap.put(822, 36);
        strMap.put(823, 36);
        strMap.put(824, 36);
        strMap.put(825, 36);
        strMap.put(826, 36);
        strMap.put(827, 36);
        strMap.put(828, 36);
        strMap.put(829, 36);
        strMap.put(830, 36);
        strMap.put(831, 36);
        strMap.put(832, 36);
        strMap.put(833, 36);
        strMap.put(834, 36);
        strMap.put(835, 36);
        strMap.put(836, 36);
        strMap.put(837, 36);
        strMap.put(838, 36);
        strMap.put(839, 36);
        strMap.put(840, 36);
        strMap.put(841, 36);
        strMap.put(842, 36);
        strMap.put(843, 36);
        strMap.put(844, 36);
        strMap.put(845, 36);
        strMap.put(846, 36);
        strMap.put(864, 36);
        strMap.put(865, 36);
        strMap.put(866, 36);
        strMap.put(884, 39);
        strMap.put(885, 44);
        strMap.put(890, 46);
        strMap.put(894, 59);
        strMap.put(900, 39);
        strMap.put(901, 36);
        strMap.put(902, 65);
        strMap.put(903, 39);
        strMap.put(904, 69);
        strMap.put(905, 72);
        strMap.put(906, 73);
        strMap.put(908, 79);
        strMap.put(910, 89);
        strMap.put(911, 36);
        strMap.put(912, 36);
        strMap.put(913, 65);
        strMap.put(914, 66);
        strMap.put(915, 82);
        strMap.put(916, 36);
        strMap.put(917, 69);
        strMap.put(918, 90);
        strMap.put(919, 72);
        strMap.put(920, 79);
        strMap.put(921, 73);
        strMap.put(922, 75);
        strMap.put(923, 65);
        strMap.put(924, 77);
        strMap.put(925, 78);
        strMap.put(926, 36);
        strMap.put(927, 79);
        strMap.put(928, 36);
        strMap.put(929, 80);
        strMap.put(931, 36);
        strMap.put(932, 84);
        strMap.put(933, 89);
        strMap.put(934, 79);
        strMap.put(935, 88);
        strMap.put(936, 36);
        strMap.put(937, 36);
        strMap.put(938, 73);
        strMap.put(939, 89);
        strMap.put(940, 36);
        strMap.put(941, 36);
        strMap.put(942, 36);
        strMap.put(943, 36);
        strMap.put(944, 36);
        strMap.put(945, 36);
        strMap.put(946, 36);
        strMap.put(947, 36);
        strMap.put(948, 36);
        strMap.put(949, 36);
        strMap.put(950, 36);
        strMap.put(951, 36);
        strMap.put(952, 36);
        strMap.put(953, 36);
        strMap.put(954, 36);
        strMap.put(955, 36);
        strMap.put(956, 36);
        strMap.put(957, 36);
        strMap.put(958, 36);
        strMap.put(959, 36);
        strMap.put(960, 36);
        strMap.put(961, 36);
        strMap.put(962, 36);
        strMap.put(963, 36);
        strMap.put(964, 36);
        strMap.put(965, 36);
        strMap.put(966, 36);
        strMap.put(967, 36);
        strMap.put(968, 36);
        strMap.put(969, 36);
        strMap.put(970, 36);
        strMap.put(971, 36);
        strMap.put(972, 36);
        strMap.put(973, 36);
        strMap.put(974, 36);
        strMap.put(976, 36);
        strMap.put(977, 36);
        strMap.put(978, 36);
        strMap.put(979, 36);
        strMap.put(980, 36);
        strMap.put(981, 36);
        strMap.put(982, 36);
        strMap.put(983, 36);
        strMap.put(986, 36);
        strMap.put(987, 36);
        strMap.put(988, 36);
        strMap.put(989, 36);
        strMap.put(990, 36);
        strMap.put(991, 36);
        strMap.put(992, 36);
        strMap.put(993, 36);
        strMap.put(994, 36);
        strMap.put(995, 36);
        strMap.put(996, 36);
        strMap.put(997, 36);
        strMap.put(998, 36);
        strMap.put(999, 36);
        strMap.put(1000, 36);
        strMap.put(1001, 36);
        strMap.put(1002, 36);
        strMap.put(1003, 36);
        strMap.put(1004, 36);
        strMap.put(1005, 36);
        strMap.put(1006, 36);
        strMap.put(1007, 36);
        strMap.put(1008, 36);
        strMap.put(1009, 36);
        strMap.put(1010, 67);
        strMap.put(1011, 74);
        strMap.put(1024, 69);
        strMap.put(1025, 69);
        strMap.put(1026, 36);
        strMap.put(1027, 36);
        strMap.put(1028, 36);
        strMap.put(1029, 83);
        strMap.put(1030, 73);
        strMap.put(1031, 73);
        strMap.put(1032, 74);
        strMap.put(1033, 36);
        strMap.put(1034, 36);
        strMap.put(1035, 36);
        strMap.put(1036, 75);
        strMap.put(1037, 78);
        strMap.put(1038, 89);
        strMap.put(1039, 36);
        strMap.put(1040, 65);
        strMap.put(1041, 36);
        strMap.put(1042, 66);
        strMap.put(1043, 36);
        strMap.put(1044, 36);
        strMap.put(1045, 69);
        strMap.put(1046, 36);
        strMap.put(1047, 36);
        strMap.put(1048, 36);
        strMap.put(1049, 36);
        strMap.put(1050, 75);
        strMap.put(1051, 36);
        strMap.put(1052, 77);
        strMap.put(1053, 72);
        strMap.put(1054, 79);
        strMap.put(1055, 36);
        strMap.put(1056, 80);
        strMap.put(1057, 67);
        strMap.put(1058, 84);
        strMap.put(1059, 89);
        strMap.put(1060, 36);
        strMap.put(1061, 88);
        strMap.put(1062, 36);
        strMap.put(1063, 36);
        strMap.put(1064, 36);
        strMap.put(1065, 36);
        strMap.put(1066, 36);
        strMap.put(1067, 36);
        strMap.put(1068, 36);
        strMap.put(1069, 36);
        strMap.put(1070, 36);
        strMap.put(1071, 36);
        strMap.put(1072, 65);
        strMap.put(1073, 36);
        strMap.put(1074, 66);
        strMap.put(1075, 82);
        strMap.put(1076, 36);
        strMap.put(1077, 69);
        strMap.put(1078, 36);
        strMap.put(1079, 36);
        strMap.put(1080, 36);
        strMap.put(1081, 36);
        strMap.put(1082, 75);
        strMap.put(1083, 36);
        strMap.put(1084, 77);
        strMap.put(1085, 72);
        strMap.put(1086, 79);
        strMap.put(1087, 36);
        strMap.put(1088, 80);
        strMap.put(1089, 67);
        strMap.put(1090, 84);
        strMap.put(1091, 89);
        strMap.put(1092, 36);
        strMap.put(1093, 88);
        strMap.put(1094, 36);
        strMap.put(1095, 36);
        strMap.put(1096, 87);
        strMap.put(1097, 36);
        strMap.put(1098, 36);
        strMap.put(1099, 36);
        strMap.put(1100, 66);
        strMap.put(1101, 36);
        strMap.put(1102, 36);
        strMap.put(1103, 36);
        strMap.put(1104, 69);
        strMap.put(1105, 69);
        strMap.put(1106, 36);
        strMap.put(1107, 82);
        strMap.put(1108, 69);
        strMap.put(1109, 83);
        strMap.put(1110, 73);
        strMap.put(1111, 73);
        strMap.put(1112, 74);
        strMap.put(1113, 36);
        strMap.put(1114, 36);
        strMap.put(1115, 36);
        strMap.put(1116, 36);
        strMap.put(1117, 36);
        strMap.put(1118, 36);
        strMap.put(1119, 36);
        strMap.put(1120, 36);
        strMap.put(1121, 87);
        strMap.put(1122, 36);
        strMap.put(1123, 36);
        strMap.put(1124, 36);
        strMap.put(1125, 36);
        strMap.put(1126, 36);
        strMap.put(1127, 36);
        strMap.put(1128, 36);
        strMap.put(1129, 36);
        strMap.put(1130, 36);
        strMap.put(1131, 36);
        strMap.put(1132, 36);
        strMap.put(1133, 36);
        strMap.put(1134, 36);
        strMap.put(1135, 36);
        strMap.put(1136, 36);
        strMap.put(1137, 36);
        strMap.put(1138, 36);
        strMap.put(1139, 36);
        strMap.put(1140, 86);
        strMap.put(1141, 86);
        strMap.put(1142, 86);
        strMap.put(1143, 86);
        strMap.put(1144, 36);
        strMap.put(1145, 36);
        strMap.put(1146, 79);
        strMap.put(1147, 79);
        strMap.put(1148, 36);
        strMap.put(1149, 36);
        strMap.put(1150, 36);
        strMap.put(1151, 36);
        strMap.put(1152, 36);
        strMap.put(1153, 36);
        strMap.put(1154, 36);
        strMap.put(1155, 36);
        strMap.put(1156, 36);
        strMap.put(1157, 36);
        strMap.put(1158, 36);
        strMap.put(1160, 36);
        strMap.put(1161, 36);
        strMap.put(1164, 36);
        strMap.put(1165, 36);
        strMap.put(1166, 36);
        strMap.put(1167, 36);
        strMap.put(1168, 36);
        strMap.put(1169, 36);
        strMap.put(1170, 36);
        strMap.put(1171, 36);
        strMap.put(1172, 36);
        strMap.put(1173, 36);
        strMap.put(1174, 36);
        strMap.put(1175, 36);
        strMap.put(1176, 36);
        strMap.put(1177, 36);
        strMap.put(1178, 75);
        strMap.put(1179, 75);
        strMap.put(1180, 75);
        strMap.put(1181, 75);
        strMap.put(1182, 75);
        strMap.put(1183, 75);
        strMap.put(1184, 75);
        strMap.put(1185, 36);
        strMap.put(1186, 36);
        strMap.put(1187, 36);
        strMap.put(1188, 36);
        strMap.put(1189, 36);
        strMap.put(1190, 36);
        strMap.put(1191, 36);
        strMap.put(1192, 36);
        strMap.put(1193, 36);
        strMap.put(1194, 36);
        strMap.put(1195, 36);
        strMap.put(1196, 36);
        strMap.put(1197, 36);
        strMap.put(1198, 89);
        strMap.put(1199, 89);
        strMap.put(1200, 89);
        strMap.put(1201, 89);
        strMap.put(1202, 88);
        strMap.put(1203, 88);
        strMap.put(1204, 36);
        strMap.put(1205, 36);
        strMap.put(1206, 36);
        strMap.put(1207, 36);
        strMap.put(1208, 36);
        strMap.put(1209, 36);
        strMap.put(1210, 72);
        strMap.put(1211, 72);
        strMap.put(1212, 69);
        strMap.put(1213, 69);
        strMap.put(1214, 69);
        strMap.put(1215, 69);
        strMap.put(1216, 73);
        strMap.put(1217, 36);
        strMap.put(1218, 36);
        strMap.put(1219, 36);
        strMap.put(1220, 36);
        strMap.put(1223, 36);
        strMap.put(1224, 36);
        strMap.put(1227, 36);
        strMap.put(1228, 36);
        strMap.put(1232, 36);
        strMap.put(1233, 36);
        strMap.put(1234, 36);
        strMap.put(1235, 36);
        strMap.put(1236, 36);
        strMap.put(1237, 36);
        strMap.put(1238, 36);
        strMap.put(1239, 36);
        strMap.put(1240, 36);
        strMap.put(1241, 36);
        strMap.put(1242, 36);
        strMap.put(1243, 36);
        strMap.put(1244, 36);
        strMap.put(1245, 36);
        strMap.put(1246, 36);
        strMap.put(1247, 36);
        strMap.put(1248, 51);
        strMap.put(1249, 51);
        strMap.put(1250, 36);
        strMap.put(1251, 36);
        strMap.put(1252, 36);
        strMap.put(1253, 36);
        strMap.put(1254, 79);
        strMap.put(1255, 79);
        strMap.put(1256, 79);
        strMap.put(1257, 79);
        strMap.put(1258, 79);
        strMap.put(1259, 79);
        strMap.put(1260, 36);
        strMap.put(1261, 36);
        strMap.put(1262, 89);
        strMap.put(1263, 89);
        strMap.put(1264, 89);
        strMap.put(1265, 89);
        strMap.put(1266, 89);
        strMap.put(1267, 89);
        strMap.put(1268, 36);
        strMap.put(1269, 36);
        strMap.put(1272, 36);
        strMap.put(1273, 36);
        strMap.put(1329, 36);
        strMap.put(1330, 36);
        strMap.put(1331, 36);
        strMap.put(1332, 36);
        strMap.put(1333, 36);
        strMap.put(1334, 36);
        strMap.put(1335, 36);
        strMap.put(1336, 36);
        strMap.put(1337, 36);
        strMap.put(1338, 36);
        strMap.put(1339, 36);
        strMap.put(1340, 36);
        strMap.put(1341, 36);
        strMap.put(1342, 36);
        strMap.put(1343, 36);
        strMap.put(1344, 36);
        strMap.put(1345, 36);
        strMap.put(1346, 36);
        strMap.put(1347, 36);
        strMap.put(1348, 36);
        strMap.put(1349, 36);
        strMap.put(1350, 36);
        strMap.put(1351, 36);
        strMap.put(1352, 36);
        strMap.put(1353, 36);
        strMap.put(1354, 36);
        strMap.put(1355, 36);
        strMap.put(1356, 36);
        strMap.put(1357, 36);
        strMap.put(1358, 36);
        strMap.put(1359, 83);
        strMap.put(1360, 36);
        strMap.put(1361, 83);
        strMap.put(1362, 36);
        strMap.put(1363, 36);
        strMap.put(1364, 36);
        strMap.put(1365, 79);
        strMap.put(1366, 36);
        strMap.put(1369, 39);
        strMap.put(1370, 39);
        strMap.put(1371, 39);
        strMap.put(1372, 39);
        strMap.put(1373, 39);
        strMap.put(1374, 34);
        strMap.put(1375, 36);
        strMap.put(1377, 87);
        strMap.put(1378, 36);
        strMap.put(1379, 36);
        strMap.put(1380, 36);
        strMap.put(1381, 36);
        strMap.put(1382, 81);
        strMap.put(1383, 84);
        strMap.put(1384, 36);
        strMap.put(1385, 36);
        strMap.put(1386, 36);
        strMap.put(1387, 36);
        strMap.put(1388, 36);
        strMap.put(1389, 36);
        strMap.put(1390, 36);
        strMap.put(1391, 36);
        strMap.put(1392, 36);
        strMap.put(1393, 36);
        strMap.put(1394, 36);
        strMap.put(1395, 36);
        strMap.put(1396, 36);
        strMap.put(1397, 74);
        strMap.put(1398, 36);
        strMap.put(1399, 50);
        strMap.put(1400, 78);
        strMap.put(1401, 36);
        strMap.put(1402, 36);
        strMap.put(1403, 36);
        strMap.put(1404, 36);
        strMap.put(1405, 85);
        strMap.put(1406, 36);
        strMap.put(1407, 36);
        strMap.put(1408, 36);
        strMap.put(1409, 71);
        strMap.put(1410, 76);
        strMap.put(1411, 36);
        strMap.put(1412, 36);
        strMap.put(1413, 79);
        strMap.put(1414, 36);
        strMap.put(1415, 36);
        strMap.put(1417, 58);
        strMap.put(1418, 36);
        strMap.put(1425, 36);
        strMap.put(1426, 36);
        strMap.put(1427, 36);
        strMap.put(1428, 36);
        strMap.put(1429, 36);
        strMap.put(1430, 36);
        strMap.put(1431, 36);
        strMap.put(1432, 36);
        strMap.put(1433, 36);
        strMap.put(1434, 36);
        strMap.put(1435, 36);
        strMap.put(1436, 36);
        strMap.put(1437, 36);
        strMap.put(1438, 36);
        strMap.put(1439, 36);
        strMap.put(1440, 36);
        strMap.put(1441, 36);
        strMap.put(1443, 36);
        strMap.put(1444, 36);
        strMap.put(1445, 36);
        strMap.put(1446, 36);
        strMap.put(1447, 36);
        strMap.put(1448, 36);
        strMap.put(1449, 36);
        strMap.put(1450, 36);
        strMap.put(1451, 36);
        strMap.put(1452, 36);
        strMap.put(1453, 36);
        strMap.put(1454, 36);
        strMap.put(1455, 36);
        strMap.put(1456, 36);
        strMap.put(1457, 36);
        strMap.put(1458, 36);
        strMap.put(1459, 36);
        strMap.put(1460, 36);
        strMap.put(1461, 36);
        strMap.put(1462, 36);
        strMap.put(1463, 36);
        strMap.put(1464, 36);
        strMap.put(1465, 36);
        strMap.put(1467, 36);
        strMap.put(1468, 36);
        strMap.put(1469, 36);
        strMap.put(1470, 36);
        strMap.put(1471, 36);
        strMap.put(1472, 36);
        strMap.put(1473, 36);
        strMap.put(1474, 36);
        strMap.put(1475, 36);
        strMap.put(1476, 36);
        strMap.put(1488, 36);
        strMap.put(1489, 36);
        strMap.put(1490, 36);
        strMap.put(1491, 36);
        strMap.put(1492, 36);
        strMap.put(1493, 36);
        strMap.put(1494, 36);
        strMap.put(1495, 36);
        strMap.put(1496, 36);
        strMap.put(1497, 36);
        strMap.put(1498, 36);
        strMap.put(1499, 36);
        strMap.put(1500, 36);
        strMap.put(1501, 36);
        strMap.put(1502, 36);
        strMap.put(1503, 36);
        strMap.put(1504, 36);
        strMap.put(1505, 36);
        strMap.put(1506, 36);
        strMap.put(1507, 36);
        strMap.put(1508, 36);
        strMap.put(1509, 36);
        strMap.put(1510, 36);
        strMap.put(1511, 36);
        strMap.put(1512, 36);
        strMap.put(1513, 36);
        strMap.put(1514, 36);
        strMap.put(1520, 36);
        strMap.put(1521, 36);
        strMap.put(1522, 36);
        strMap.put(1523, 36);
        strMap.put(1524, 36);
        strMap.put(1548, 36);
        strMap.put(1563, 36);
        strMap.put(1567, 36);
        strMap.put(1569, 36);
        strMap.put(1570, 36);
        strMap.put(1571, 36);
        strMap.put(1572, 36);
        strMap.put(1573, 36);
        strMap.put(1574, 36);
        strMap.put(1575, 36);
        strMap.put(1576, 36);
        strMap.put(1577, 36);
        strMap.put(1578, 36);
        strMap.put(1579, 36);
        strMap.put(1580, 36);
        strMap.put(1581, 36);
        strMap.put(1582, 36);
        strMap.put(1583, 36);
        strMap.put(1584, 36);
        strMap.put(1585, 36);
        strMap.put(1586, 36);
        strMap.put(1587, 36);
        strMap.put(1588, 36);
        strMap.put(1589, 36);
        strMap.put(1590, 36);
        strMap.put(1591, 36);
        strMap.put(1592, 36);
        strMap.put(1593, 36);
        strMap.put(1594, 36);
        strMap.put(1600, 36);
        strMap.put(1601, 36);
        strMap.put(1602, 36);
        strMap.put(1603, 36);
        strMap.put(1604, 36);
        strMap.put(1605, 36);
        strMap.put(1606, 36);
        strMap.put(1607, 36);
        strMap.put(1608, 36);
        strMap.put(1609, 36);
        strMap.put(1610, 36);
        strMap.put(1611, 36);
        strMap.put(1612, 36);
        strMap.put(1613, 36);
        strMap.put(1614, 36);
        strMap.put(1615, 36);
        strMap.put(1616, 36);
        strMap.put(1617, 36);
        strMap.put(1618, 36);
        strMap.put(1619, 36);
        strMap.put(1620, 36);
        strMap.put(1621, 36);
        strMap.put(1632, 36);
        strMap.put(1633, 36);
        strMap.put(1634, 36);
        strMap.put(1635, 36);
        strMap.put(1636, 36);
        strMap.put(1637, 36);
        strMap.put(1638, 36);
        strMap.put(1639, 36);
        strMap.put(1640, 36);
        strMap.put(1641, 36);
        strMap.put(1642, 36);
        strMap.put(1643, 36);
        strMap.put(1644, 36);
        strMap.put(1645, 36);
        strMap.put(1648, 36);
        strMap.put(1649, 36);
        strMap.put(1650, 36);
        strMap.put(1651, 36);
        strMap.put(1652, 36);
        strMap.put(1653, 36);
        strMap.put(1654, 36);
        strMap.put(1655, 36);
        strMap.put(1656, 36);
        strMap.put(1657, 36);
        strMap.put(1658, 36);
        strMap.put(1659, 36);
        strMap.put(1660, 36);
        strMap.put(1661, 36);
        strMap.put(1662, 36);
        strMap.put(1663, 36);
        strMap.put(1664, 36);
        strMap.put(1665, 36);
        strMap.put(1666, 36);
        strMap.put(1667, 36);
        strMap.put(1668, 36);
        strMap.put(1669, 36);
        strMap.put(1670, 36);
        strMap.put(1671, 36);
        strMap.put(1672, 36);
        strMap.put(1673, 36);
        strMap.put(1674, 36);
        strMap.put(1675, 36);
        strMap.put(1676, 36);
        strMap.put(1677, 36);
        strMap.put(1678, 36);
        strMap.put(1679, 36);
        strMap.put(1680, 36);
        strMap.put(1681, 36);
        strMap.put(1682, 36);
        strMap.put(1683, 36);
        strMap.put(1684, 36);
        strMap.put(1685, 36);
        strMap.put(1686, 36);
        strMap.put(1687, 36);
        strMap.put(1688, 36);
        strMap.put(1689, 36);
        strMap.put(1690, 36);
        strMap.put(1691, 36);
        strMap.put(1692, 36);
        strMap.put(1693, 36);
        strMap.put(1694, 36);
        strMap.put(1695, 36);
        strMap.put(1696, 36);
        strMap.put(1697, 36);
        strMap.put(1698, 36);
        strMap.put(1699, 36);
        strMap.put(1700, 36);
        strMap.put(1701, 36);
        strMap.put(1702, 36);
        strMap.put(1703, 36);
        strMap.put(1704, 36);
        strMap.put(1705, 36);
        strMap.put(1706, 36);
        strMap.put(1707, 36);
        strMap.put(1708, 36);
        strMap.put(1709, 36);
        strMap.put(1710, 36);
        strMap.put(1711, 36);
        strMap.put(1712, 36);
        strMap.put(1713, 36);
        strMap.put(1714, 36);
        strMap.put(1715, 36);
        strMap.put(1716, 36);
        strMap.put(1717, 36);
        strMap.put(1718, 36);
        strMap.put(1719, 36);
        strMap.put(1720, 36);
        strMap.put(1721, 36);
        strMap.put(1722, 36);
        strMap.put(1723, 36);
        strMap.put(1724, 36);
        strMap.put(1725, 36);
        strMap.put(1726, 36);
        strMap.put(1727, 36);
        strMap.put(1728, 36);
        strMap.put(1729, 36);
        strMap.put(1730, 36);
        strMap.put(1731, 36);
        strMap.put(1732, 36);
        strMap.put(1733, 36);
        strMap.put(1734, 36);
        strMap.put(1735, 36);
        strMap.put(1736, 36);
        strMap.put(1737, 36);
        strMap.put(1738, 36);
        strMap.put(1739, 36);
        strMap.put(1740, 36);
        strMap.put(1741, 36);
        strMap.put(1742, 36);
        strMap.put(1743, 36);
        strMap.put(1744, 36);
        strMap.put(1745, 36);
        strMap.put(1746, 36);
        strMap.put(1747, 36);
        strMap.put(1748, 36);
        strMap.put(1749, 36);
        strMap.put(1750, 36);
        strMap.put(1751, 36);
        strMap.put(1752, 36);
        strMap.put(1753, 36);
        strMap.put(1754, 36);
        strMap.put(1755, 36);
        strMap.put(1756, 36);
        strMap.put(1757, 36);
        strMap.put(1758, 36);
        strMap.put(1759, 36);
        strMap.put(1760, 36);
        strMap.put(1761, 36);
        strMap.put(1762, 36);
        strMap.put(1763, 36);
        strMap.put(1764, 36);
        strMap.put(1765, 36);
        strMap.put(1766, 36);
        strMap.put(1767, 36);
        strMap.put(1768, 36);
        strMap.put(1769, 36);
        strMap.put(1770, 36);
        strMap.put(1771, 36);
        strMap.put(1772, 36);
        strMap.put(1773, 36);
        strMap.put(1776, 36);
        strMap.put(1777, 36);
        strMap.put(1778, 36);
        strMap.put(1779, 36);
        strMap.put(1780, 36);
        strMap.put(1781, 36);
        strMap.put(1782, 36);
        strMap.put(1783, 36);
        strMap.put(1784, 36);
        strMap.put(1785, 36);
        strMap.put(1786, 36);
        strMap.put(1787, 36);
        strMap.put(1788, 36);
        strMap.put(1789, 36);
        strMap.put(1790, 36);
        strMap.put(1792, 36);
        strMap.put(1793, 36);
        strMap.put(1794, 36);
        strMap.put(1795, 36);
        strMap.put(1796, 36);
        strMap.put(1797, 36);
        strMap.put(1798, 36);
        strMap.put(1799, 36);
        strMap.put(1800, 36);
        strMap.put(1801, 36);
        strMap.put(1802, 36);
        strMap.put(1803, 36);
        strMap.put(1804, 36);
        strMap.put(1805, 36);
        strMap.put(1807, 36);
        strMap.put(1808, 36);
        strMap.put(1809, 36);
        strMap.put(1810, 36);
        strMap.put(1811, 36);
        strMap.put(1812, 36);
        strMap.put(1813, 36);
        strMap.put(1814, 36);
        strMap.put(1815, 36);
        strMap.put(1816, 36);
        strMap.put(1817, 36);
        strMap.put(1818, 36);
        strMap.put(1819, 36);
        strMap.put(1820, 36);
        strMap.put(1821, 36);
        strMap.put(1822, 36);
        strMap.put(1823, 36);
        strMap.put(1824, 36);
        strMap.put(1825, 36);
        strMap.put(1826, 36);
        strMap.put(1827, 36);
        strMap.put(1828, 36);
        strMap.put(1829, 36);
        strMap.put(1830, 36);
        strMap.put(1831, 36);
        strMap.put(1832, 36);
        strMap.put(1833, 36);
        strMap.put(1834, 36);
        strMap.put(1835, 36);
        strMap.put(1836, 36);
        strMap.put(1840, 36);
        strMap.put(1841, 36);
        strMap.put(1842, 36);
        strMap.put(1843, 36);
        strMap.put(1844, 36);
        strMap.put(1845, 36);
        strMap.put(1846, 36);
        strMap.put(1847, 36);
        strMap.put(1848, 36);
        strMap.put(1849, 36);
        strMap.put(1850, 36);
        strMap.put(1851, 36);
        strMap.put(1852, 36);
        strMap.put(1853, 36);
        strMap.put(1854, 36);
        strMap.put(1855, 36);
        strMap.put(1856, 36);
        strMap.put(1857, 36);
        strMap.put(1858, 36);
        strMap.put(1859, 36);
        strMap.put(1860, 36);
        strMap.put(1861, 36);
        strMap.put(1862, 36);
        strMap.put(1863, 36);
        strMap.put(1864, 36);
        strMap.put(1865, 36);
        strMap.put(1866, 36);
        strMap.put(1920, 36);
        strMap.put(1921, 36);
        strMap.put(1922, 36);
        strMap.put(1923, 36);
        strMap.put(1924, 36);
        strMap.put(1925, 36);
        strMap.put(1926, 36);
        strMap.put(1927, 36);
        strMap.put(1928, 36);
        strMap.put(1929, 36);
        strMap.put(1930, 36);
        strMap.put(1931, 36);
        strMap.put(1932, 36);
        strMap.put(1933, 36);
        strMap.put(1934, 36);
        strMap.put(1935, 36);
        strMap.put(1936, 36);
        strMap.put(1937, 36);
        strMap.put(1938, 36);
        strMap.put(1939, 36);
        strMap.put(1940, 36);
        strMap.put(1941, 36);
        strMap.put(1942, 36);
        strMap.put(1943, 36);
        strMap.put(1944, 36);
        strMap.put(1945, 36);
        strMap.put(1946, 36);
        strMap.put(1947, 36);
        strMap.put(1948, 36);
        strMap.put(1949, 36);
        strMap.put(1950, 36);
        strMap.put(1951, 36);
        strMap.put(1952, 36);
        strMap.put(1953, 36);
        strMap.put(1954, 36);
        strMap.put(1955, 36);
        strMap.put(1956, 36);
        strMap.put(1957, 36);
        strMap.put(1958, 36);
        strMap.put(1959, 36);
        strMap.put(1960, 36);
        strMap.put(1961, 36);
        strMap.put(1962, 36);
        strMap.put(1963, 36);
        strMap.put(1964, 36);
        strMap.put(1965, 36);
        strMap.put(1966, 36);
        strMap.put(1967, 36);
        strMap.put(1968, 36);
        strMap.put(8208, 45);
        strMap.put(8209, 45);
        strMap.put(8210, 45);
        strMap.put(8211, 45);
        strMap.put(8212, 45);
        strMap.put(8213, 45);
        strMap.put(8214, 124);
        strMap.put(8215, 61);
        strMap.put(8216, 39);
        strMap.put(8217, 39);
        strMap.put(8218, 44);
        strMap.put(8219, 39);
        strMap.put(8220, 34);
        strMap.put(8221, 34);
        strMap.put(8222, 34);
        strMap.put(8223, 34);
        strMap.put(8226, 42);
        strMap.put(8228, 46);
        strMap.put(8229, 46);
        strMap.put(8230, 46);
        strMap.put(8231, 46);
        strMap.put(8242, 39);
        strMap.put(8243, 34);
        strMap.put(8244, 34);
        strMap.put(8245, 39);
        strMap.put(8246, 34);
        strMap.put(8247, 34);
        strMap.put(8297, 32);

    }

    public String replaceCharactersWithSpace(String inputText, Map<Integer, Integer> strMap, Map<Integer, Integer> escChar) {
        int i = 0;
        int charCode;
        int charCodeNew;
        StringBuilder outText = new StringBuilder();
        try {
            inputText = inputText.replace("\t", "");
            inputText = inputText.replace("\n", "");
            for (; i < inputText.length(); i++) {
                charCode = inputText.codePointAt(i);
                if (strMap.containsKey(charCode)) {
                    charCodeNew = strMap.get(charCode);
                    if (escChar.containsKey(charCodeNew)) {
                        outText.appendCodePoint(escChar.get(charCodeNew));
                    }
                    outText.appendCodePoint(charCodeNew);
                } else {
                    outText.appendCodePoint(charCode);
                }
            }
            return outText.toString();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }
}
