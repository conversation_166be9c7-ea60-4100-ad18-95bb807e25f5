package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimPaymentDispatchDto;
import com.misyn.mcms.claim.dto.SpecialRemarkDto;
import com.misyn.mcms.claim.dto.UserDto;

/**
 * Created by akila on 9/4/18.
 */
public interface ClaimCalculationSheetPayeeService {

    public void getSendPendingEmailList() throws Exception;

    public void getSendAssessorPaymentEmail() throws Exception;

    void changeCheckReceiveStatus(ClaimPaymentDispatchDto claimPaymentDispatchDto, SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception;
}
