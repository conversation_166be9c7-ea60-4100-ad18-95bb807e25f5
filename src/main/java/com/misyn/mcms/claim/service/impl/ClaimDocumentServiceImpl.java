package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimCalculationSheetMainDao;
import com.misyn.mcms.claim.dao.ClaimDocumentTypeDao;
import com.misyn.mcms.claim.dao.ClaimWiseDocumentDao;
import com.misyn.mcms.claim.dao.impl.ClaimCalculationSheetMainDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimDocumentTypeDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimWiseDocumentDaoImpl;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.DocumentStatusEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimDocumentService;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
public class ClaimDocumentServiceImpl extends AbstractBaseService<ClaimDocumentDto> implements ClaimDocumentService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimDocumentServiceImpl.class);
    private StorageService storageService = new StorageServiceImpl();
    private ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private MotorEngineerDetailsDao motorEngineerDetailsDao;
    private ClaimDocumentTypeDao claimDocumentTypeDao;
    private ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();

    public ClaimDocumentServiceImpl() {
        motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
        claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    }

    @Override
    public ClaimDocumentStatusDto getDocumentStatus(Connection connection, Integer claimNo, Integer documentTypeId) {
        DocumentStatusEnum documentStatusEnum = DocumentStatusEnum.NO_VALID_DOCUMENT;
        ClaimDocumentStatusDto claimDocumentStatusDto = new ClaimDocumentStatusDto();
        List<ClaimDocumentDto> investigationReportList = new ArrayList<>();
        try {
            investigationReportList = claimDocumentDao.findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus(connection, claimNo, documentTypeId);
            claimDocumentStatusDto.setClaimDocumentDtoList(investigationReportList);
            if (investigationReportList.isEmpty()) {
                documentStatusEnum = DocumentStatusEnum.NO_VALID_DOCUMENT;
            } else {
                for (ClaimDocumentDto claimDocumentDto : investigationReportList) {
                    if (claimDocumentDto.getDocumentStatus().equals(DocumentStatusEnum.PENDING.getDocumentStatus())) {
                        documentStatusEnum = DocumentStatusEnum.PENDING;
                        break;
                    } else {
                        documentStatusEnum = DocumentStatusEnum.CHECKED;
                    }
                }
            }
            claimDocumentStatusDto.setDocumentStatusEnum(documentStatusEnum);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentStatusDto;
    }

    @Override
    public ClaimDocumentStatusDto getInvestigationReportDocumentStatus(Connection connection, Integer claimNo) {
        ClaimDocumentStatusDto claimDocumentStatusDto = new ClaimDocumentStatusDto();
        try {
            claimDocumentStatusDto = getDocumentStatus(connection, claimNo, AppConstant.INVESTIGATION_REPORT_DOCUMENT_TYPE_ID);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentStatusDto;
    }

    @Override
    public ClaimDocumentDto getClaimDocumentDto(Integer refId) {
        ClaimDocumentDto claimDocumentDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimDocumentDto = claimDocumentDao.searchMaster(connection, refId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDocumentDto;
    }

    @Override
    public ClaimDocumentDto updateBillCheckDetails(ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynJDBCException {
        Connection connection = null;
        ClaimDocumentDto searchClaimDocument = null;
        ClaimDocumentDto searchApprovedBill = null;
        ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
        Integer billApprovedDocTypeId = AppConstant.ZERO_INT;

        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            if (claimDocumentDao.updateDocumentBillCheckDetails(connection, claimDocumentDto) != null) {
                storageService.updatedDocumentCheckAndMandatoryDocumentStatus(connection, claimDocumentDto.getClaimNo());
                searchClaimDocument = claimDocumentDao.searchMaster(connection, claimDocumentDto.getRefNo());
                searchApprovedBill = claimDocumentDao.searchByBillCheckRefNo(connection, claimDocumentDto.getRefNo());

                BillCheckDto billCheckDto = new BillCheckDto();
                billCheckDto.setCalculationSheetNo(claimDocumentDto.getCalculationSheetNo());
                billCheckDto.setClaimNo(claimDocumentDto.getClaimNo());
                billCheckDto.setDocumentUploadRefNo(claimDocumentDto.getRefNo());
                billCheckDto.setBillCheckUserId(user.getUserId());
                billCheckDto.setBillCheckDateTime(Utility.sysDateTime());

                searchClaimDocument.setBillCheckRefNo(searchClaimDocument.getRefNo());
                if (searchApprovedBill == null) {
                    billApprovedDocTypeId = claimDocumentDao.getBillApprovedDocTypeId(connection, searchClaimDocument.getDocumentTypeId());

                    claimWiseDocumentDto.setClaimNo(searchClaimDocument.getClaimNo());
                    claimWiseDocumentDto.setIsMandatory(AppConstant.YES);
                    claimWiseDocumentDto.setInpUserId(user.getUserId());
                    claimWiseDocumentDto.setInpDateTime(searchClaimDocument.getInpDateTime());
                    claimWiseDocumentDto.setDocumentTypeId(billApprovedDocTypeId);

                    searchClaimDocument.setDocumentTypeId(billApprovedDocTypeId);
                    searchClaimDocument.setDocumentStatus(AppConstant.DOCUMENT_CHECK_STATUS);
                    searchClaimDocument.setIsCheck(AppConstant.YES);
                    searchClaimDocument.setCheckUser(user.getUserId());
                    searchClaimDocument.setCheckDateTime(Utility.sysDateTime());
                    if (searchClaimDocument.getDocumentTypeId() > 0) {
                        claimDocumentDao.insertMaster(connection, searchClaimDocument);
                        claimWiseDocumentDao.updateMaster(connection, claimWiseDocumentDto);
                    }

                }
                // billCheckDao.insert(connection, billCheckDto);
                if (searchClaimDocument != null) {
                    saveClaimsLogs(connection, claimDocumentDto.getClaimNo(), user, "Bill Check", searchClaimDocument.getClaimDocumentTypeDto().getDocumentTypeName().concat(" is Checked"));
                    if (searchClaimDocument.getClaimDocumentTypeDto().getDocumentTypeId() == AppConstant.DO_BILL_DOCUMENT_TYPE_ID) {
                        StringBuilder sbMessage = new StringBuilder();
                        sbMessage.append("Please Generate the voucher for delivery order.");
                        String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimDocumentDto.getClaimNo())).concat("&P_TAB_INDEX=15");
                        String userId = claimCalculationSheetMainDao.getSpecialTeamAssignUserId(connection, claimDocumentDto.getClaimNo(), 65, 3);
                        if (null != userId && !userId.isEmpty()) {
                            saveNotification(connection, claimDocumentDto.getClaimNo(), user.getUserId(), userId, sbMessage.toString(), URL);
                        }
                    }
                }
                commitTransaction(connection);
                return claimDocumentDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new MisynJDBCException(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public ClaimDocumentStatusDto getCheckedInitialLiabilityDocumentStatus(Integer claimNo) {
        DocumentStatusEnum documentStatusEnum = DocumentStatusEnum.NO_VALID_DOCUMENT;
        ClaimDocumentStatusDto claimDocumentStatusDto = new ClaimDocumentStatusDto();
        claimDocumentStatusDto.setDocumentStatusEnum(DocumentStatusEnum.DEFAULT);
        boolean isOnsite;
        List<ClaimDocumentDto> checkedDocumentList;
        List<ClaimDocumentTypeDto> claimDocumentTypeDtoList;
        boolean isBreak = false;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            isOnsite = motorEngineerDetailsDao.isInspectionExist(connection, claimNo, 1);
            //If onSite Inspection
            if (isOnsite) {
                claimDocumentTypeDtoList = claimDocumentTypeDao.getClaimDocumentTypeDtoList(connection, AppConstant.ASSESSOR_DEPARTMENT_ID, 1);
            } else {
                //If Garage Inspection
                claimDocumentTypeDtoList = claimDocumentTypeDao.getClaimDocumentTypeDtoList(connection, AppConstant.ASSESSOR_DEPARTMENT_ID, 4);
            }
            for (ClaimDocumentTypeDto claimDocumentTypeDto : claimDocumentTypeDtoList) {
                checkedDocumentList = claimDocumentDao.findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus(connection, claimNo, claimDocumentTypeDto.getDocumentTypeId());
                claimDocumentStatusDto.setClaimDocumentDtoList(checkedDocumentList);
                for (ClaimDocumentDto claimDocumentDto : checkedDocumentList) {
                    if (claimDocumentDto.getDocumentStatus().equals(DocumentStatusEnum.PENDING.getDocumentStatus())) {
                        documentStatusEnum = DocumentStatusEnum.PENDING;
                        isBreak = true;
                        break;
                    } else {
                        documentStatusEnum = DocumentStatusEnum.CHECKED;
                    }
                }
                claimDocumentStatusDto.setDocumentStatusEnum(documentStatusEnum);
                if (isBreak) {
                    break;
                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDocumentStatusDto;
    }

    @Override
    public ClaimDocumentStatusDto getCheckedLiabilityDocumentStatus(Integer claimNo) {
        DocumentStatusEnum documentStatusEnum = DocumentStatusEnum.NO_VALID_DOCUMENT;
        ClaimDocumentStatusDto claimDocumentStatusDto = new ClaimDocumentStatusDto();
        claimDocumentStatusDto.setDocumentStatusEnum(documentStatusEnum);
        List<ClaimDocumentDto> checkedDocumentList;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            checkedDocumentList = claimDocumentDao.findAllMandatoryDocumentByClaimNo(connection, claimNo);
            claimDocumentStatusDto.setClaimDocumentDtoList(checkedDocumentList);
            for (ClaimDocumentDto claimDocumentDto : checkedDocumentList) {
                if (claimDocumentDto.getDocumentStatus().equals(DocumentStatusEnum.PENDING.getDocumentStatus())) {
                    documentStatusEnum = DocumentStatusEnum.PENDING;
                    break;
                } else {
                    documentStatusEnum = DocumentStatusEnum.CHECKED;
                }
            }
            claimDocumentStatusDto.setDocumentStatusEnum(documentStatusEnum);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDocumentStatusDto;
    }

    @Override
    public void cancelDocument(ClaimDocumentDto claimDocumentDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            ClaimDocumentDto searchedClaimDocumentDto = claimDocumentDao.searchMaster(connection, claimDocumentDto.getRefNo());
            claimDocumentDao.updateDocumentBillCancelDetails(connection, claimDocumentDto);
            saveClaimsLogs(connection, claimDocumentDto.getClaimNo(), user, "Bill Cancel", searchedClaimDocumentDto.getClaimDocumentTypeDto().getDocumentTypeName().concat(" is Cancelled"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean isDocumentAvailable(int claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimWiseDocumentDao.isUploadDocumentByClaimNoAndDocTypeId(connection, claimNo, AppConstant.REJECTION_LETTER_TYPE_NO);
        } catch (Exception e) {

            LOGGER.error(e.getMessage(), e);
            throw e;

        } finally {
            releaseJDBCConnection(connection);
        }
    }

    public String lastDocUploadDateTime(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimDocumentDao.getDocumentUploadTime(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }
}
