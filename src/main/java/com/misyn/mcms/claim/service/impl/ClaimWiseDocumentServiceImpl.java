package com.misyn.mcms.claim.service.impl;


import com.misyn.mcms.claim.dao.ClaimDocumentTypeDao;
import com.misyn.mcms.claim.dao.ClaimWiseDocumentDao;
import com.misyn.mcms.claim.dao.impl.ClaimDocumentTypeDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimWiseDocumentDaoImpl;
import com.misyn.mcms.claim.dto.ClaimDocumentTypeDto;
import com.misyn.mcms.claim.dto.ClaimUploadViewDto;
import com.misyn.mcms.claim.dto.ClaimWiseDocumentDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.enums.ClaimLossTypeEnum;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimWiseDocumentService;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
public class ClaimWiseDocumentServiceImpl extends AbstractBaseService<ClaimWiseDocumentServiceImpl> implements ClaimWiseDocumentService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimWiseDocumentServiceImpl.class);
    protected DbRecordCommonFunction recordCommonFunction = DbRecordCommonFunction.getInstance();
    private ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();
    private ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private StorageService storageService = new StorageServiceImpl();

    @Override
    public void saveAll(Connection connection, Integer claimNo, UserDto user) throws MisynJDBCException {
        try {
            claimWiseDocumentDao.saveAll(connection, claimNo, user.getUserId(), Utility.sysDateTime());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        }
    }

    @Override
    public ClaimWiseDocumentDto updateClaimWiseDocument(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        return null;
    }

    @Override
    public void updateDocumentInspectionWise(Connection connection, Integer claimNo, Integer inspectionTypeId, UserDto user) throws MisynJDBCException {
        List<ClaimDocumentTypeDto> claimDocumentTypeDtoList;
        try {
            claimDocumentTypeDtoList = claimDocumentTypeDao.getClaimDocumentTypeDtoList(connection, AppConstant.ASSESSOR_DEPARTMENT_ID, inspectionTypeId);
            for (ClaimDocumentTypeDto claimDocumentTypeDto : claimDocumentTypeDtoList) {
                ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
                claimWiseDocumentDto.setClaimNo(claimNo);
                claimWiseDocumentDto.setDocumentTypeId(claimDocumentTypeDto.getDocumentTypeId());
                claimWiseDocumentDto.setIsMandatory(AppConstant.YES);
                claimWiseDocumentDto.setInpUserId(user.getUserId());
                claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
                claimWiseDocumentDao.updateByClaimNoAndDocumentTypeId(connection, claimWiseDocumentDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        }

    }

    @Override
    public void updateDocumentLostTypeWise(Connection connection, Integer claimNo, ClaimLossTypeEnum claimLossTypeEnum) {

    }

    @Override
    public List<ClaimWiseDocumentDto> getClaimWiseDocumentDtoList(Integer claimNo) {
        Connection connection = null;
        List<ClaimWiseDocumentDto> claimUploadViewDtoList = null;
        try {
            connection = getJDBCConnection();
            claimUploadViewDtoList = claimWiseDocumentDao.searchAllByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo) {
        Connection connection = null;
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            claimUploadViewDtoList = claimWiseDocumentDao.getClaimUploadViewDtoList(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo, String documentType) {
        Connection connection = null;
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            claimUploadViewDtoList = claimWiseDocumentDao.getClaimUploadViewDtoList(connection, claimNo, documentType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewForDesktopAssesment(Integer claimNo) {
        Connection connection = null;
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            claimUploadViewDtoList = claimWiseDocumentDao.getClaimUploadViewForDesktopAssesment(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUploadViewDtoList;
    }

    @Override
    public void updateDefineDocument(List<ClaimWiseDocumentDto> claimWiseDocumentDtoList, UserDto user, Integer claimNo, String remark) throws Exception {
        Connection connection = null;
        StringBuilder sb = new StringBuilder();
        boolean isUpdated = false;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            Map<Integer, ClaimWiseDocumentDto> map = claimWiseDocumentDao.searchAllByClaimNo(connection, claimNo).stream()
                    .collect(Collectors.toMap(ClaimWiseDocumentDto::getRefNo, claimWiseDocumentDto -> claimWiseDocumentDto));

            sb.append("[");
            for (ClaimWiseDocumentDto claimWiseDocumentDto : claimWiseDocumentDtoList) {
                ClaimWiseDocumentDto oldClaimWiseDocumentDto = map.get(claimWiseDocumentDto.getRefNo());
                claimWiseDocumentDao.updateDefineDocumentByRefId(connection, claimWiseDocumentDto);

                if (oldClaimWiseDocumentDto != null) {
                    if (AppConstant.YES.equalsIgnoreCase(oldClaimWiseDocumentDto.getIsMandatory()) && AppConstant.NO.equalsIgnoreCase(claimWiseDocumentDto.getIsMandatory())) {
                        if (claimWiseDocumentDao.isUploadDocumentByClaimNoAndDocTypeId(connection, claimNo, oldClaimWiseDocumentDto.getDocumentTypeId())) {
                            throw new ErrorMsgException(AppConstant.ERROR_MESSAGE, "You cannot change '" + oldClaimWiseDocumentDto.getReminderDocDisplayName() + "' document type as optional since there is an uploaded document !");
                        }
                    }

                    if ((!oldClaimWiseDocumentDto.getIsMandatory().equals(claimWiseDocumentDto.getIsMandatory()))
                            || (!oldClaimWiseDocumentDto.getDocReqFrom().equals(claimWiseDocumentDto.getDocReqFrom()))) {
                        String name;
                        String reqFrmName = recordCommonFunction.findRecord("claim_doc_req_from", "V_REQ_FROM_DESC", "N_DOC_REQ_FROM =" + String.valueOf(claimWiseDocumentDto.getDocReqFrom()));
                        if (oldClaimWiseDocumentDto.getIsMandatory().equals("Y")) {
                            name = "Optional";
                        } else {

                            name = "Mandatory";
                        }
                        sb.append(oldClaimWiseDocumentDto.getDocumentTypeName() + "=" + name + "," + reqFrmName + " /").append(" ");
                        isUpdated = true;
                    }
                }
            }
            sb.append("]");

            if (null != claimNo) {
                storageService.updatedDocumentCheckAndMandatoryDocumentStatus(connection, claimNo);
                if (isUpdated) {
                    saveClaimsLogs(connection, claimNo, user, "Define Document", "Update Define Document : ".concat(sb.toString()));
                }

                if (null != remark && !remark.isEmpty()) {
                    saveClaimSpecialRemark(connection, user, claimNo, "Update Define Document", remark);
                }
            }
            commitTransaction(connection);
        } catch (ErrorMsgException e) {
            LOGGER.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new MisynJDBCException("Can not be Saved");
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimWiseDocumentDto> getClaimWiseDocumentDtoList(Integer claimNo, String docName) {
        Connection connection = null;
        List<ClaimWiseDocumentDto> claimUploadViewDtoList = null;
        try {
            connection = getJDBCConnection();
            claimUploadViewDtoList = claimWiseDocumentDao.searchAllByClaimNoAndName(connection, claimNo, docName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadBranchViewDtoList(Integer claimNo, String searchDoc) {
        Connection connection = null;
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            claimUploadViewDtoList = claimWiseDocumentDao.getClaimUploadBranchViewDtoList(connection, claimNo, searchDoc);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUploadViewDtoList;
    }

    @Override
    public void updateIsMandatory(String userId, String date, Integer claimNo, Integer documentTypeId) throws Exception {
        Connection connection = null;
        ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
        try {
            connection = getJDBCConnection();
            claimWiseDocumentDto.setIsMandatory(AppConstant.YES);
            claimWiseDocumentDto.setClaimNo(claimNo);
            claimWiseDocumentDto.setInpDateTime(date);
            claimWiseDocumentDto.setDocumentTypeId(documentTypeId);
            claimWiseDocumentDto.setInpUserId(userId);
            claimWiseDocumentDao.updateMaster(connection ,claimWiseDocumentDto);
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
            throw e;
        }finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateDefineDocumentOnOther(Integer claimNo, Integer documentTypeId, UserDto user) throws Exception {
        Connection connection = null;
        ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
        try {
            connection = getJDBCConnection();
            claimWiseDocumentDto.setIsMandatory(AppConstant.YES);
            claimWiseDocumentDto.setClaimNo(claimNo);
            claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
            claimWiseDocumentDto.setDocumentTypeId(documentTypeId);
            claimWiseDocumentDto.setInpUserId(user.getUserId());
            claimWiseDocumentDto.setDocReqFrom(2);
            claimWiseDocumentDao.updateDefineDocumentByDocumentTypeId(connection, claimWiseDocumentDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadBranchViewDtoList(Integer claimNo) {
        Connection connection = null;
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            claimUploadViewDtoList = claimWiseDocumentDao.getClaimUploadBranchViewDtoList(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUploadViewDtoList;
    }

}
