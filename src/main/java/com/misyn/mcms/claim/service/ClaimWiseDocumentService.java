package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimUploadViewDto;
import com.misyn.mcms.claim.dto.ClaimWiseDocumentDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.enums.ClaimLossTypeEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.sql.Connection;
import java.util.List;

public interface ClaimWiseDocumentService {

    void saveAll(Connection connection, Integer claimNo, UserDto user) throws MisynJDBCException;

    ClaimWiseDocumentDto updateClaimWiseDocument(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException;

    void updateDocumentInspectionWise(Connection connection, Integer claimNo, Integer inspectionTypeId, UserDto user) throws MisynJDBCException;

    void updateDocumentLostTypeWise(Connection connection, Integer claimNo, ClaimLossTypeEnum claimLossTypeEnum) throws MisynJDBCException;

    List<ClaimWiseDocumentDto> getClaimWiseDocumentDtoList(Integer claimNo);

    List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo);

    List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo, String documentType);

    List<ClaimUploadViewDto> getClaimUploadViewForDesktopAssesment(Integer claimNo);

    void updateDefineDocument(List<ClaimWiseDocumentDto> claimWiseDocumentDtoList, UserDto user, Integer claimNo, String remark) throws Exception;

    List<ClaimWiseDocumentDto> getClaimWiseDocumentDtoList(Integer claimNo, String docName);

    List<ClaimUploadViewDto> getClaimUploadBranchViewDtoList(Integer claimNo);

    List<ClaimUploadViewDto> getClaimUploadBranchViewDtoList(Integer claimNo, String searchDoc);

    void updateIsMandatory(String userId, String date, Integer claimNo, Integer documentTypeId) throws Exception;

    void updateDefineDocumentOnOther(Integer claimNo, Integer documentTypeId, UserDto user) throws Exception;
}
