package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.HolidayTypeDetailDao;
import com.misyn.mcms.claim.dao.impl.HolidayTypeDetailDaoImpl;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.HolidayTypeDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.HolidayTypeDetailService;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class HolidayTypeDetailServiceImpl extends AbstractBaseService<HolidayTypeDetailServiceImpl> implements HolidayTypeDetailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(HolidayTypeDetailServiceImpl.class);
    //    private SupplierDetailsMasterDao supplierDetailsMasterDao = new SupplierDetailsMasterDaoImpl();
    private final HolidayTypeDetailDao holidayTypeDetailDao = new HolidayTypeDetailDaoImpl();

    @Override
    public HolidayTypeDto saveHolidayTypeDetail(HolidayTypeDto holidayTypeDto) throws MisynJDBCException {
        Connection connection = null;
        HolidayTypeDto holidayTypeDto1 = null;
        try {
            connection = getJDBCConnection();
            if (null == holidayTypeDto.getHolidayTypeId() || holidayTypeDto.getHolidayTypeId().equals(0)) {
                holidayTypeDto.setRecordStatus(AppConstant.ACTIVE_STATUS);
                holidayTypeDto1 = holidayTypeDetailDao.insertHolidayTypeDetail(connection, holidayTypeDto);
            }
            return holidayTypeDto1;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public HolidayTypeDto updateHolidayTypeDetail(HolidayTypeDto holidayTypeDto) throws MisynJDBCException {
        Connection connection = null;
        HolidayTypeDto holidayTypeDto1 = null;
        try {
            connection = getJDBCConnection();
            if (null != holidayTypeDto.getHolidayTypeId() || !holidayTypeDto.getHolidayTypeId().equals(0)) {
                holidayTypeDto.setRecordStatus(AppConstant.ACTIVE_STATUS);
                holidayTypeDto1 = holidayTypeDetailDao.updateHolidayTypeDetail(connection, holidayTypeDto);
            }
            return holidayTypeDto1;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public HolidayTypeDto getHolidayTypeDetail(Integer holidayTypeId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return holidayTypeDetailDao.getHolidayTypeDetail(connection, holidayTypeId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<HolidayTypeDto> getHolidayTypeDetailList() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return holidayTypeDetailDao.getHolidayTypeDetailList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void deleteHolidayTypeDetail(Integer holidayTypeId, String deleteReason) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            holidayTypeDetailDao.deleteHolidayTypeDetail(connection, holidayTypeId, deleteReason);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getHolidayTypeDetailsDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = holidayTypeDetailDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

}
