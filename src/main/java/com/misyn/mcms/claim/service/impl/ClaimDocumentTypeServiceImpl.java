package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimDocumentTypeDao;
import com.misyn.mcms.claim.dao.impl.ClaimDocumentTypeDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimDocumentTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class ClaimDocumentTypeServiceImpl extends AbstractBaseService<ClaimDocumentTypeServiceImpl> implements ClaimDocumentTypeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserServiceImpl.class);
    private ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();

    @Override
    public ClaimDocumentTypeDto save(ClaimDocumentTypeDto claimDocumentTypeDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            if (claimDocumentTypeDto.getDocumentTypeId() == 0) {
                claimDocumentTypeDao.insert(connection, claimDocumentTypeDto);
            } else {
                claimDocumentTypeDao.update(connection, claimDocumentTypeDto);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDocumentTypeDto;
    }

    @Override
    public DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimDocumentTypeDao.getDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public ClaimDocumentTypeDto searchDocTypeId(Integer id) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        ClaimDocumentTypeDto claimDocumentTypeDto;
        try {
            beginTransaction(connection);
            claimDocumentTypeDto = claimDocumentTypeDao.searchByDocumentTypeId(connection, id);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDocumentTypeDto;
    }

    @Override
    public List<ClaimDepartmentDto> documentlist() throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        List<ClaimDepartmentDto> claimDepartmentDtoList = null;
        try {
            beginTransaction(connection);
            claimDepartmentDtoList = claimDocumentTypeDao.searchClaimDocument(connection);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDepartmentDtoList;
    }

    @Override
    public List<DocReqFormDto> documentReqFormlist() throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        List<DocReqFormDto> docReqFormDtosList = null;
        try {
            beginTransaction(connection);
            docReqFormDtosList = claimDocumentTypeDao.claimDocReqForm(connection);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return docReqFormDtosList;
    }

    @Override
    public String validateDoctypeName(String DoctypeName) throws MisynJDBCException {
        Connection connection = null;
        String docTypeName = "";
        try {
            connection = getJDBCConnection();
            docTypeName = claimDocumentTypeDao.validateDocTypeNmae(connection, DoctypeName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return docTypeName;
    }


    @Override
    public ClaimDocumentTypeDto insert(ClaimDocumentTypeDto claimDocumentTypeDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto update(ClaimDocumentTypeDto claimDocumentTypeDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto delete(ClaimDocumentTypeDto claimDocumentTypeDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto updateAuthPending(ClaimDocumentTypeDto claimDocumentTypeDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto deleteAuthPending(ClaimDocumentTypeDto claimDocumentTypeDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentTypeDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimDocumentTypeDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<ClaimDocumentTypeDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }
}
