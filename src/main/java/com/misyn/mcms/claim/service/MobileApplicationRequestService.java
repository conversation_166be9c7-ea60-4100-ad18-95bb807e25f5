package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.util.List;

public interface MobileApplicationRequestService {

    DataGridDto getMobileRequestDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String filterdBy);

    DataGridDto getMobileInspectionDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    List<UserDto> getAllAssessor(Integer accessUsrType) throws Exception;

    DataGridDto searchNotification(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate,String notificationStatus) throws Exception;
}
