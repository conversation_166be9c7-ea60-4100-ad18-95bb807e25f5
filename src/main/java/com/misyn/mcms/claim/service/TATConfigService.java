package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.TATDetailDto;
import com.misyn.mcms.claim.dto.UserDto;


/**
 * <AUTHOR>
 */
public interface TATConfigService {
    String saveTATData(TATDetailDto tatDetailDto, UserDto user) throws Exception;

    DataGridDto getAllTATData(UserDto user, int start, int length, String sortColumn, String sortDirection, String searchValue) throws Exception;

    TATDetailDto searchTATDataById(int tatId, UserDto user) throws Exception;

    String deleteTATData(int tatId, String deleteReason, UserDto user) throws Exception;

    DataGridDto filterTATDetails(int tatId, String taskName, UserDto user) throws Exception;
}
