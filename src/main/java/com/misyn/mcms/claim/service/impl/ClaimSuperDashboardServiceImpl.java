package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
public class ClaimSuperDashboardServiceImpl extends AbstractBaseService<ClaimSuperDashboardDto> implements ClaimSuperDashboardService {


    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimSuperDashboardServiceImpl.class);
    private final InspectionDetailsService inspectionDetailsService = new InspectionDetailsServiceImpl();
    private final MotorEngineerService motorEngineerService = new MotorEngineerServiceImpl();
    private final ClaimHandlerService claimHandlerService = new ClaimHandlerServiceImpl();
    private final CalculationSheetService calculationSheetService = new CalculationSheetServiceImpl();
    private final InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private final AssessorAllocationDao assessorAllocationDao = new AssessorAllocationDaoImpl();
    private final MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private final CalculationProcessFlowDao calculationProcessFlowDao = new CalculationProcessFlowDaoImpl();
    private final AssessorDao assessorDao = new AssessorDaoImpl();
    private final ClaimUserLeaveDao claimUserLeaveDao = new ClaimUserLeaveDaoImpl();

    private ClaimCalculationSheetPayeeDao claimCalculationSheetPayeeDao = new ClaimCalculationSheetPayeeDaoImpl();

    @Override
    public ClaimSuperDashboardDto getClaimSuperDashboardDto(Integer claimNo) {
        ClaimSuperDashboardDto claimSuperDashboardDto = new ClaimSuperDashboardDto();
        List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList = new ArrayList<>();
        List<PreviousClaimsDto> previousInspectionList = new ArrayList<>();

        Connection connection = null;
        String recordStatus;
        String causeOfLoss;
        String lossType;
        String claimStatusPara;
        String claimStatuss;
        try {
            connection = getJDBCConnection();
            previousInspectionList = motorEngineerService.getInspectionList(connection, claimNo);
            claimSuperDashboardDto.setClaimHandlerDto(claimHandlerService.searchClaimByClaimNo(connection, claimNo));

            List<AssessorAllocationDto> assessorListByClaimNo = assessorAllocationDao.getAssessorListSuperByClaimNo(connection, claimNo);
            List<AssessorAllocationDto> assessorList = new ArrayList<>();
            for (AssessorAllocationDto assessorAllocationDto : assessorListByClaimNo) {
                if (null != assessorAllocationDto.getInspectionDto() && assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    if (null != assessorAllocationDto.getAssessorDto()) {
                        UserDto user = assessorDao.searchUserByUserId(connection, assessorAllocationDto.getRteCode());
                        if (null != user) {
                            assessorAllocationDto.getAssessorDto().setName(user.getFirstName().concat(" ").concat(user.getLastName()));
                            assessorAllocationDto.getAssessorDto().setReportingToName(user.getReportingToName().concat(" [").concat(user.getReportingTo()).concat("]"));
                            assessorAllocationDto.getAssessorDto().setReportingCode(user.getReportingTo());
                            assessorAllocationDto.getAssessorDto().setRteMobileNo(user.getMobile());
                        }

                    }
                } else if (assessorAllocationDto.getInspectionDto().getInspectionId() != AppConstant.CALL_ESTIMATE) {
                    UserDto user = assessorDao.searchUserByEmployeeNo(connection, assessorAllocationDto.getAssessorDto().getCode());
                    if (Objects.nonNull(user)) {
                        assessorAllocationDto.getAssessorDto().setName(user.getFirstName().concat(" ").concat(user.getLastName()));
                        assessorAllocationDto.getAssessorDto().setReportingToName(user.getReportingToName().concat(" [").concat(user.getReportingTo()).concat("]"));
                        assessorAllocationDto.getAssessorDto().setReportingCode(user.getReportingTo());
                        assessorAllocationDto.getAssessorDto().setRteMobileNo(user.getMobile());
                    }
                }
                String assessorAssignRte = motorEngineerDetailsDao.getAssignRte(connection, assessorAllocationDto.getJobId());

                if (null != assessorAssignRte && !assessorAssignRte.isEmpty()) {
                    assessorAllocationDto.getAssessorDto().setReportingCode(assessorAssignRte);
                }

                if (null != assessorAllocationDto.getAssessorDto()) {
                    String assessorMobileNo = assessorDao.getAssessorMobileNo(connection, assessorAllocationDto.getAssessorDto().getCode());
                    assessorAllocationDto.getAssessorDto().setAssessorContactNo(assessorMobileNo);
                }
                assessorList.add(assessorAllocationDto);
            }
            if (!assessorList.isEmpty()) {
                claimSuperDashboardDto.setAssessorAllocationDtoList(assessorList);
            }

            for (PreviousClaimsDto previousInspection : previousInspectionList) {
                MotorEngineerDetailsDto motorEngineerDetailsDto = motorEngineerService.search(connection, previousInspection.getJobRefNo());
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsService.search(connection, previousInspection.getJobRefNo()));
                motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDao.searchMaster(connection, previousInspection.getJobRefNo()));
                motorEngineerDetailsDto.getInspectionDto().setInspectionValue(previousInspection.getInspectionType());

                String assessorAssignRte = motorEngineerDetailsDao.getAssignRte(connection, motorEngineerDetailsDto.getJobId());

                if (null != assessorAssignRte && !assessorAssignRte.isEmpty()) {
                    motorEngineerDetailsDto.setInputUserId(assessorAssignRte);
                }

                if (null != motorEngineerDetailsDto.getInspectionDetailsDto().getApproveAssignRteUser() && !AppConstant.STRING_EMPTY.equals(motorEngineerDetailsDto.getInspectionDetailsDto().getApproveAssignRteUser()) && !motorEngineerDetailsDto.getInspectionDetailsDto().getApproveAssignRteUser().equals(motorEngineerDetailsDto.getInspectionDetailsDto().getAssignRteUser())) {
                    motorEngineerDetailsDto.setIsForwarded(AppConstant.YES);
                }

                claimSuperDashboardDto.getMotorEngineerDetailsDtoList().add(motorEngineerDetailsDto);

                recordStatus = commonUtilDao.findOne(connection, "claim_status_para", "v_status_desc", "n_ref_id=" + motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus());
                motorEngineerDetailsDto.setRecordStatusDesc(recordStatus);

                String engineerTat = calEngineerTat(motorEngineerDetailsDto);
                motorEngineerDetailsDto.setEngineerTat(engineerTat);
            }
            claimCalculationSheetMainDtoList = calculationSheetService.getCalculationSheetList(claimNo);
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto1 : claimCalculationSheetMainDtoList) {
                claimStatuss = commonUtilDao.findOne(connection, "claim_status_para", "v_status_desc", "n_ref_id=" + claimCalculationSheetMainDto1.getStatus());
                claimCalculationSheetMainDto1.setCalculationProcessFlowDtos(calculationProcessFlowDao.searchAllByCalSheetId(connection, claimCalculationSheetMainDto1.getCalSheetId()));
                claimCalculationSheetMainDto1.setClaimStatus(claimStatuss);
            }


            if (claimSuperDashboardDto.getClaimHandlerDto().getClaimsDto().getCauseOfLoss() > 0) {
                causeOfLoss = commonUtilDao.findOne(connection, "claim_cause_of_loss_type", "V_CAUSE_OF_LOSS", "N_ID=" + claimSuperDashboardDto.getClaimHandlerDto().getClaimsDto().getCauseOfLoss());
                claimSuperDashboardDto.getClaimHandlerDto().setCauseOfLoss(causeOfLoss);
            }

            if (claimSuperDashboardDto.getClaimHandlerDto().getLossType() > 0) {
                lossType = commonUtilDao.findOne(connection, "claim_loss_type", "V_CAUSE_OF_LOSS", "N_ID=" + claimSuperDashboardDto.getClaimHandlerDto().getLossType());
                claimSuperDashboardDto.getClaimHandlerDto().setLossTypeDecs(lossType);
            }

            claimStatusPara = commonUtilDao.findOne(connection, "claim_status_para", "v_status_desc", "n_ref_id=" + claimSuperDashboardDto.getClaimHandlerDto().getClaimStatus());
            claimSuperDashboardDto.getClaimHandlerDto().setClaimStatusPara(claimStatusPara);
            claimSuperDashboardDto.setClaimCalculationSheetMainDtoList(claimCalculationSheetMainDtoList);
            claimSuperDashboardDto.getClaimHandlerDto().setProcessingStage(claimStatus(connection, claimSuperDashboardDto));

            claimSuperDashboardDto.setClaimProcessFlowDtos(claimProcessFlowDao.searchAllByClaimNo(connection, claimNo));
            List<VoucherDetailsDto> voucherDetailsDtoList = new ArrayList<>();
            for (ClaimCalculationSheetMainDto dto : claimCalculationSheetMainDtoList) {
                ClaimCalculationSheetPayeeDto payeeDetailsByVoucherNo = claimCalculationSheetPayeeDao.findPayeeDetailsByVoucherNo(connection, dto.getVoucherNo());
                VoucherDetailsDto voucherDetailsDto = new VoucherDetailsDto();
                voucherDetailsDto.setVoucherNo(null == dto.getVoucherNo() ? AppConstant.CLOSE_STATUS_PENDING : dto.getVoucherNo());
                voucherDetailsDto.setChequeNumber(payeeDetailsByVoucherNo.getChequeNo());
                voucherDetailsDto.setChequeDate(payeeDetailsByVoucherNo.getResponseDateTime());
                voucherDetailsDto.setStatusDesc("Printed");
                voucherDetailsDto.getClaimPaymentDispatchDto().getBranchDetailDto().setBranchName(payeeDetailsByVoucherNo.getBranchDetailDto().getBranchName());
                voucherDetailsDto.getClaimPaymentDispatchDto().setDispatchDateTime("N/A");
                voucherDetailsDto.getClaimPaymentDispatchDto().getDispatchedLocation().setBranchName("N/A");
                voucherDetailsDto.getClaimPaymentDispatchDto().setChequeDispatchStatus("N");
                voucherDetailsDtoList.add(voucherDetailsDto);
            }
            claimSuperDashboardDto.setVoucherDetailsDtos(voucherDetailsDtoList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimSuperDashboardDto;
    }

    @Override
    public boolean isLeaveAssignUser(String assignUserId) {

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimUserLeaveDao.isLeaveUser(connection, assignUserId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return false;
    }

    private String calEngineerTat(MotorEngineerDetailsDto motorEngineerDetailsDto) {
        String tat = AppConstant.EMPTY_STRING;
        try {
            if (!AppConstant.DEFAULT_DATE_TIME.equals(motorEngineerDetailsDto.getInputDatetime())) {

                long diff = Utility.getNoMiniutsTimeDiff(motorEngineerDetailsDto.getInspectionDetailsDto().getAssignRteDatetime(), motorEngineerDetailsDto.getInputDatetime(), AppConstant.DATE_TIME_FORMAT);
                long[] dateTime = Utility.getDayHoursMinSecondDifferenceForWorkingHours(diff);
                tat = String.valueOf(dateTime[0]).concat(" d ").concat(String.valueOf(dateTime[1])).concat(" h ").concat(String.valueOf(dateTime[2])).concat(" m ");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return tat;
    }

    private Integer claimStatus(Connection connection, ClaimSuperDashboardDto superDashboardDto) {
        Integer staus = AppConstant.ZERO_INT;

        try {
            List<PreviousClaimsDto> previousClaimList = inspectionDetailsDao.getPreviousClaimList(superDashboardDto.getClaimHandlerDto().getClaimNo(), connection);
            List<AssessorAllocationDto> assessorListByClaimNo = assessorAllocationDao.getAssessorListByClaimNo(connection, superDashboardDto.getClaimHandlerDto().getClaimNo());
            List<MotorEngineerDetailsDto> motorEngList = motorEngineerDetailsDao.searchByClaimNo(connection, superDashboardDto.getClaimHandlerDto().getClaimNo());
            if (null != superDashboardDto.getClaimHandlerDto().getAssignUserId()) {
                staus = 5;
            } else if (motorEngList.size() > 0) {
                staus = 4;
            } else if (previousClaimList.size() > 0) {
                staus = 3;
            } else if (assessorListByClaimNo.size() > 0) {
                staus = 2;
            } else {
                staus = 1;
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return staus;
    }


}
