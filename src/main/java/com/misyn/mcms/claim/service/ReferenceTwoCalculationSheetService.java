package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.utility.ListBoxItem;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 9/12/23.
 */
public interface ReferenceTwoCalculationSheetService {
    void saveCalculationSheet(String action, UserDto user, ClaimCalculationSheetMainDto claimCalculationSheetMainDto, BigDecimal outStandingPremium, boolean isReserveAmountExceed, boolean isSAveAsDraft, boolean isCancelledPolicy) throws Exception;

    List<ClaimCalculationSheetMainDto> getCalculationSheetList(Integer claimNo) throws Exception;

    List<ClaimCalculationSheetMainDto> getCalculationSheetListByCalSheetTypeAndClaimNo(Integer claimNo, int calType) throws Exception;

    List<ClaimCalculationSheetMainDto> getPaymentOptionCalculationSheetList(Integer claimNo) throws Exception;

    ClaimCalculationSheetMainDto getCalculationSheet(Integer claimNo, Integer calSheetNo, PolicyDto policyDto) throws Exception;

    ClaimCalculationSheetMainDto getClaimCalculationSheetMainDto(Integer claimNo, Integer calSheetNo, UserDto user) throws Exception;

    BigDecimal getTotalPaidAdvanceForClaim(Integer claimNo) throws Exception;

    BigDecimal getTotalPaidForClaim(Integer claimNo, Integer calType) throws Exception;

    void forwardToSparePartsCoordinator(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void forwardToScrutinizingTeam(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void forwardToScrutinizingTeamByClaimHandler(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void forwardToSpecialTeam(Integer calSheetId, Integer claimNo, UserDto user, BigDecimal outStandingPremium, boolean isCancelledPolicy) throws Exception;

    void forwardToMofa(Integer calSheetId, Integer claimNo, String forwardingUserId, UserDto user) throws Exception;

    void returnToClaimHandlerBySpc(Integer calSheetId, Integer claimNo, UserDto user, boolean isAriRequestNReturn) throws Exception;

    void returnToClaimHandlerBySpecialTeam(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void returnToClaimHandlerBySpecialTeamAfterApproved(Integer calSheetId, Integer claimNo, String specialRemark, UserDto user) throws Exception;

    void returnToClaimHandlerByMofa(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void returnToClaimHandlerByStm(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void returnToSparePartCoordinatorByStm(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void approvePayment(Integer calSheetId, Integer claimNo, UserDto user, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception;

    void setStoredYes(Integer claimNo) throws Exception;

    void updateReleaseOrderDetails(int claimNo, int calSheetId, String isReleaseOrderGenerate, String releaseOrderGenerateUserId) throws Exception;

    void callNoObjection(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception;

    void revokeNoObjection(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception;

    void callPremiumOutstanding(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception;

    void revokePremiumOutstanding(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception;

    void rejectPayment(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void generateVoucher(Integer calSheetId, Integer claimNo, BigDecimal outstandingPremium, boolean isCancelledPolicy, UserDto user) throws Exception;

    void returnToClaimHandlerByScrutinizingTeam(Integer calSheetId, Integer claimNo, UserDto user, boolean isAriRequestNReturn) throws Exception;

    void recallByClaimHandler(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    void returnToSparePartCoordinatorByScrutinizingTeam(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    DataGridDto getCalculationSheetDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, int type);

    List<UserDto> getMofaUserList(String amount, Integer claimNo) throws Exception;

    List<ClaimCalculationSheetMainDto> getAdvanceListForClaim(Integer claimNo) throws Exception;

    ClaimDocumentStatusDto getClaimDocumentStatus(Integer claimNo, int type) throws Exception;

    Integer isSupplyOrderCalculationSheetApproved(Integer supplyOrderId);

    BigDecimal getTotalPaidForClaim(Connection connection, Integer claimNo) throws Exception;

    void saveSpecialRemark(Integer calsheetId, String remark, UserDto user) throws Exception;

    void saveSpecialRemark(Connection connection, Integer calsheetId, String remark, UserDto user) throws Exception;

    List<CalSheetSpecialRemarkDto> calSheetRemarkList(Integer calSheetId) throws Exception;

    boolean isPendingAdvancedPayment(List<ClaimCalculationSheetMainDto> list) throws Exception;

    boolean isPendingPayment(List<ClaimCalculationSheetMainDto> list) throws Exception;

    SupplyOrderSummaryDto getSupplierDetails(Integer supplierId) throws Exception;

    void updateClaSheetPrintStatus(Integer calSheetId, String user) throws Exception;

    List<ListBoxItem> getPayeeSelectItem(int payeeType, Integer supplyOrderRefNo, String customerName, boolean isReadonly, String selectPayeeDes, Integer claimNoc);

    List<ListBoxItem> getPayeeTypeSelectItem(int calculationType, boolean isReadonly, String selectPayeeType);

    boolean saveClaimSpecialRemarkByCalsheetId(Integer calsheetId, String remark, UserDto user) throws Exception;

    String isExcessAlreadyApply(Integer claimNo, Integer calSheetId) throws Exception;

    void recallCalsheetforwardedtoSpTeamByClaimHandler(Integer calSheetId, Integer claimNo, UserDto user) throws Exception;

    ClaimCalculationSheetMainTempDto getCalsheetTempDetails(Integer calSheetId) throws Exception;

    void callNoClaimBonus(Integer calSheetId, Integer claimNo, String email, UserDto user) throws Exception;

    void recommendAndForwardToNextLevel(Integer calSheetId, Integer claimNo, UserDto user, BigDecimal outStandingPremium, boolean isCancelledPolicy) throws Exception;

    void approveIncreaseReserveByRte(Integer claimNo, Integer calSheetId, String remark, UserDto user) throws Exception;

    void returnToClaimHandlerByRte(Integer claimNo, Integer calSheetId, String remark, UserDto user) throws Exception;

    List<CalculationSheetPayeeHistoryDto> getPayeeHistoryDetails(Integer calSheetId, Integer claimNo);

    List<CalculationSheetMofaLevelHistoryDto> getMofaHistoryDetails(Integer calSheetId);

    void forwardToRte(Integer calSheetId, Integer claimNo, BigDecimal payableAmount, UserDto user, BigDecimal outStandingPremium) throws Exception;

    BigDecimal getPaidTotalAdvanceAmount(Integer claimNo) throws Exception;

    BigDecimal getTotalPaidAmount(Integer claimNo, Integer calSheetId) throws Exception;

    List<String> getAssignUserListByLevel(Integer claimNo, Integer level);

    List<String> getUserListByLevel(Integer assignUserType, Integer level);

    List<String> getNextAssignUsersByCurrentUserName(String assignUser, Integer claimNo, Boolean levelReassign, Integer Status) throws Exception;

    void saveCalculationSheetAssignMofaLevels(Connection connection, Integer txnId, Integer claimNo, String user, String assignUser, Integer level) throws Exception;

    boolean hasVoucherGeneratedCalSheetForDo(Integer supplyOrderRefNo);

    void updateBillCheck(Integer id, String isChecked, UserDto user) throws Exception;

    Boolean validateDO(Integer calsheetId) throws Exception;

    String getPolicyChannelType(Integer claimNo) throws Exception;

    Integer getCalsheetForBillChecking(Integer claimNo) throws Exception;

    boolean checkPendingPayments(Integer claimNo) throws Exception;

    void setOtherDetailsList(ClaimsDto claimsDto);

    CalSheetTypeDto checkReserveAmountWithCalSheetType(Integer claimNo, Integer calSheetType);
}
