package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimClaimPanelDao;
import com.misyn.mcms.claim.dao.impl.ClaimClaimPanelDaoImpl;
import com.misyn.mcms.claim.dto.ClaimClaimPanelDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimPanelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class ClaimPanelServiceImpl extends AbstractBaseService<ClaimPanelServiceImpl> implements ClaimPanelService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelServiceImpl.class);
    private ClaimClaimPanelDao claimClaimPanelDao = new ClaimClaimPanelDaoImpl();

    @Override
    public List<ClaimClaimPanelDto> claimPanelDtolist() {
        Connection connection = null;
        List<ClaimClaimPanelDto> ClaimPanelList = null;
        try {
            connection = getJDBCConnection();
            ClaimPanelList = claimClaimPanelDao.searchAll(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return ClaimPanelList;
    }
}
