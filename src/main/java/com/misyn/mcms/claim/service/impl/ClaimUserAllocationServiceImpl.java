/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimUserAllocationDao;
import com.misyn.mcms.claim.dao.ClaimUserAllocationHistoryDao;
import com.misyn.mcms.claim.dao.impl.ClaimUserAllocationDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimUserAllocationHistoryDaoImpl;
import com.misyn.mcms.claim.dto.ClaimUserAllocationDto;
import com.misyn.mcms.claim.dto.UserAuthorityLimitDto;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimUserAllocationService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
public class ClaimUserAllocationServiceImpl extends AbstractBaseService<ClaimUserAllocationDto> implements ClaimUserAllocationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserAllocationServiceImpl.class);

    private ClaimUserAllocationDao claimUserAllocationDao = new ClaimUserAllocationDaoImpl();
    private ClaimUserAllocationHistoryDao claimUserAllocationHistoryDao = new ClaimUserAllocationHistoryDaoImpl();


    @Override
    public String getNextAssignUser(Connection connection, Integer accessLevel, Integer allocationFunctionType, String type, String inputUser, Integer claimNo) throws Exception {
        return getNextAuthLimitAssignUser(connection, accessLevel, allocationFunctionType, type, inputUser, claimNo, false, BigDecimal.ZERO, BigDecimal.ZERO, 0, false);
    }

    @Override
    public List<String> getNextAuthLimitAssignUserList(Connection connection, Integer accessLevel, Integer allocationFunctionType, UserAuthorityLimitDto userAuthorityLimitDto, boolean isMandatory) {
        String sysDatetime = Utility.sysDateTime();
        String status = "A";
        List<String> userList = new ArrayList<>();
        try {
            List<ClaimUserAllocationDto> mofaListNotInLeave = claimUserAllocationDao.getMofaListNotInLeave(connection, accessLevel, allocationFunctionType, sysDatetime, status, userAuthorityLimitDto.getFromLimit().add(BigDecimal.ONE), userAuthorityLimitDto.getToLimit(), isMandatory);
            if (null != mofaListNotInLeave && !mofaListNotInLeave.isEmpty()) {
                for (ClaimUserAllocationDto claimUserAllocationDto : mofaListNotInLeave) {
                    userList.add(claimUserAllocationDto.getAssignUserId());
                }
            }
            return userList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public String getNextAuthLimitAssignUser(Connection connection, Integer accessLevel, Integer allocationFunctionType, String type, String inputUser, Integer claimNo, boolean isAuthLimitForward, BigDecimal amount, BigDecimal limit, int limitLevel, boolean isMandatory) throws Exception {

        try {
            String sysDate = Utility.sysDate();
            String sysDatetime = Utility.sysDateTime();
            String status = "A";
            List<ClaimUserAllocationDto> claimUserAllocationDtos;
            if (isAuthLimitForward) {
                if (AppConstant.ACCESS_LEVEL_RTE.equals(accessLevel)) {
                    claimUserAllocationDtos = claimUserAllocationDao.getRteListNotInLeave(connection, allocationFunctionType, sysDatetime, status, limitLevel);
                } else {
                    claimUserAllocationDtos = claimUserAllocationDao.getMofaListNotInLeave(connection, accessLevel, allocationFunctionType, sysDatetime, status, amount, limit, isMandatory);
                }
            } else if (isClaimHandlerDepartment(accessLevel)) {
                claimUserAllocationDtos = claimUserAllocationDao.getListNotInLeaveClaimHandlerDepartment(connection, accessLevel, allocationFunctionType, sysDatetime, status, claimNo);
            } else {
                claimUserAllocationDtos = claimUserAllocationDao.getListNotInLeave(connection, accessLevel, allocationFunctionType, sysDatetime, status);
            }

            List<ClaimUserAllocationDto> todayLeaveUsers = null;
            if (isClaimHandlerDepartment(accessLevel)) {
                todayLeaveUsers = claimUserAllocationDao.getListInLeaveClaimHandlerDepartment(connection, accessLevel, allocationFunctionType, sysDatetime, status, claimNo);
            } else {
                todayLeaveUsers = claimUserAllocationDao.getListInLeave(connection, accessLevel, allocationFunctionType, sysDatetime, status);
            }

            ClaimUserAllocationDto previousClaimUserAllocationDto = null;
            ClaimUserAllocationDto claimUserAllocationDtoToAssign = null;

            for (ClaimUserAllocationDto claimUserAllocationDto : claimUserAllocationDtos) {
                if (sysDate.equals(claimUserAllocationDto.getLastAssignDate())) {
                    if (null == claimUserAllocationDtoToAssign) {
                        claimUserAllocationDtoToAssign = claimUserAllocationDto;
                    } else {
                        if (claimUserAllocationDtoToAssign.getTotAssignReportToday() > claimUserAllocationDto.getTotAssignReportToday()) {
                            claimUserAllocationDtoToAssign = claimUserAllocationDto;
                        }
                    }
                } else {
                    if (null == previousClaimUserAllocationDto) {
                        previousClaimUserAllocationDto = claimUserAllocationDto;
                    } else {
                        if (previousClaimUserAllocationDto.getAssignIndex() > claimUserAllocationDto.getAssignIndex()) {
                            previousClaimUserAllocationDto = claimUserAllocationDto;
                        }
                    }
                }
            }

            if (null != previousClaimUserAllocationDto) {
                int assignReportForToday = claimUserAllocationDao.checkAssignReportForToday(connection, accessLevel);
                Integer assignIndex = previousClaimUserAllocationDto.getAssignIndex() > previousClaimUserAllocationDto.getTotAssignReportToday() ?
                        previousClaimUserAllocationDto.getAssignIndex() : previousClaimUserAllocationDto.getTotAssignReportToday();
                previousClaimUserAllocationDto.setLastAssignDate(sysDate);
                previousClaimUserAllocationDto.setTotAssignReport(
                        previousClaimUserAllocationDto.getTotAssignReport()
                                + previousClaimUserAllocationDto.getTotAssignReportToday());
                previousClaimUserAllocationDto.setTotAssignReportToday(Math.max(assignReportForToday, 1));
                previousClaimUserAllocationDto.setAssignIndex(previousClaimUserAllocationDto.getAssignIndex() + 1);
                claimUserAllocationDao.updateMaster(connection, previousClaimUserAllocationDto);
                updateLeaveUsersAssignReport(connection, todayLeaveUsers, Math.max(assignReportForToday, 1), assignIndex);
                saveUserAllocationHistory(connection, claimNo, previousClaimUserAllocationDto.getAssignUserId(), inputUser, type, previousClaimUserAllocationDto.getAccessUsrType());
                return previousClaimUserAllocationDto.getAssignUserId();
            } else if (null != claimUserAllocationDtoToAssign) {
                Integer assignIndex = claimUserAllocationDtoToAssign.getAssignIndex() > claimUserAllocationDtoToAssign.getTotAssignReportToday() ?
                        claimUserAllocationDtoToAssign.getAssignIndex() : claimUserAllocationDtoToAssign.getTotAssignReportToday();
                claimUserAllocationDtoToAssign.setLastAssignDate(sysDate);
                int totAssignReportToday = claimUserAllocationDtoToAssign.getTotAssignReportToday();
                claimUserAllocationDtoToAssign.setTotAssignReportToday(totAssignReportToday + 1);
                claimUserAllocationDtoToAssign.setAssignIndex(claimUserAllocationDtoToAssign.getAssignIndex() + 1);
                claimUserAllocationDao.updateMaster(connection, claimUserAllocationDtoToAssign);
                updateLeaveUsersAssignReport(connection, todayLeaveUsers, totAssignReportToday, assignIndex);
                saveUserAllocationHistory(connection, claimNo, claimUserAllocationDtoToAssign.getAssignUserId(), inputUser, type, claimUserAllocationDtoToAssign.getAccessUsrType());
                return claimUserAllocationDtoToAssign.getAssignUserId();
            } else {
                throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User not found to assign claim");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private boolean isClaimHandlerDepartment(Integer accessLevel) {
        return AppConstant.ACCESS_LEVEL_CLAIM_HANDLER.equals(accessLevel);
    }

    private void updateLeaveUsersAssignReport(Connection connection, List<ClaimUserAllocationDto> todayLeaveUsers, Integer assignReport, Integer assignIndex) throws Exception {
        for (ClaimUserAllocationDto dto : todayLeaveUsers) {
            try {
                claimUserAllocationDao.updateLeaveUserAssignReport(connection, assignReport, assignIndex, dto.getAssignUserId());
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
                throw e;
            }
        }
    }

}
