package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ReserveAdjustmentDao;
import com.misyn.mcms.claim.dao.impl.ReserveAdjustmentDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimReserveAdjustmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.List;

public class ClaimReserveAdjustmentServiceImpl extends AbstractBaseService<ClaimReserveAdjustmentServiceImpl> implements ClaimReserveAdjustmentService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimReserveAdjustmentServiceImpl.class);
    private final ReserveAdjustmentDao reserveAdjustmentDao = new ReserveAdjustmentDaoImpl();

    @Override
    public boolean saveReserveAdjustment(ClaimReserveAdjustmentTypeDto dto) throws Exception {
        Connection conn = getJDBCConnection();
        boolean isSaved = false;
        try {
            beginTransaction(conn);

            boolean exists = false;

            if (dto.getClaimReserveAdjustmentId() != null && dto.getClaimReserveAdjustmentId() > 0) {
                exists = reserveAdjustmentDao.isAlreadySaved(conn, dto.getClaimReserveAdjustmentId());
            }

            if (!exists) {
                dto.setInputDateTime(String.valueOf(LocalDateTime.now()));
                dto.setLastModifiedDateTime(String.valueOf(LocalDateTime.now()));
                reserveAdjustmentDao.insert(conn, dto);
            } else {
                dto.setLastModifiedDateTime(String.valueOf(LocalDateTime.now()));
                reserveAdjustmentDao.update(conn, dto);
            }

            commitTransaction(conn);
            isSaved = true;
        } catch (Exception e) {
            rollbackTransaction(conn);
            LOGGER.error("Error saving reserve adjustment", e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(conn);
        }
        return isSaved;
    }

    @Override
    public DataGridDto getReserveAdjustmentDataGridDto(
            List<FieldParameterDto> parameterList,
            int drawRandomId,
            int start,
            int length,
            String orderType,
            String orderField,
            String fromDate,
            String toDate) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = reserveAdjustmentDao.getReserveAdjustmentDataGridDto(
                    connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public boolean isActiveRecordExists(int periodId, int categoryId) {
        Connection conn = getJDBCConnection();
        return reserveAdjustmentDao.isActiveRecordExists(conn, periodId, categoryId);
    }

    @Override
    public List<ReserveCategoryDto> getReserveCategories() {
        Connection conn = getJDBCConnection();
        return reserveAdjustmentDao.getReserveCategories(conn);
    }

    @Override
    public List<ReservePeriodDto> getReservePeriods() {
        Connection conn = getJDBCConnection();
        return reserveAdjustmentDao.getReservePeriods(conn);
    }

}
