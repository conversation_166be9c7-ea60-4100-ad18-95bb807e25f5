package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimProcessFlowDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimFlowDetailService {

    boolean SaveClaimFlowTask(String task, UserDto user) throws Exception;

    List<ClaimProcessFlowDto> getAllClaimFlowDetails() throws  Exception;

    void saveClaimLogs(Integer claimNo, UserDto user) throws Exception;

}
