package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 5/21/18.
 */
public interface RequestAriService extends BaseService<RequestAriDto> {

    public List<RequestAriDto> searchAll(Integer claimNo) throws Exception;

    public RequestAriDto searchByClaimNo(Integer claimNo) throws Exception;

    public RequestAriDto insert(RequestAriDto requestAriDto, UserDto user, Connection connection) throws Exception;

    public DataGridDto getRequestDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    public boolean updateStatusAndRevokeByRef(String remark, Integer refId, UserDto user) throws Exception;

    public boolean updateStatusByRefId(Integer refNo) throws Exception;

    public DataGridDto getRequestDataGridDtoPending(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    public RequestAriDto searchByClaimNoPending(Integer claimNo) throws Exception;

    public RequestAriDto updateAri(RequestAriDto requestAriDto, UserDto user) throws Exception;

    public RequestAriRemarkDto saveRemark(RequestAriRemarkDto requestAriRemarkDto, UserDto user) throws Exception;

    public List<RequestAriRemarkDto> getRemarkListByRemarkId(Integer remarkId);


    DataGridDto getRequestDataGridDtoByUser(List<FieldParameterDto> parameterList, int i, int start, int length, String columnOrder, String orderColumnName, boolean isSpCood, String userId);

    void sendAriPendingMail(Integer id, String reason, String remark, UserDto user) throws Exception;

    boolean checkDocumentsForARI(Integer claimNo);

    RequestAriDto getAriDetails(Integer claimNo) throws Exception;

    int isAriOrSalvageRequested(Integer claimNoToSearch) throws Exception;

    boolean revokeAriRequest(Integer requestAriId, String remark, UserDto user) throws Exception;

    int isSalvageORAriArranged(Integer claimNo)throws Exception;

    BulkCloseDetailDto getBulkCloseDetail(Integer claimNo)throws Exception;

}
