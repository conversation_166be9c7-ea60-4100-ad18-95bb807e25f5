package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.SalutaitionDao;
import com.misyn.mcms.claim.dao.impl.SalutationDaoIMPL;
import com.misyn.mcms.claim.dto.SalutationDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.SalutationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class SalutationServiceImpl extends AbstractBaseService<SalutationServiceImpl> implements SalutationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CityServiceImpl.class);
    private SalutaitionDao salutaitionDao = new SalutationDaoIMPL();

    @Override
    public SalutationDto insert(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto update(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto delete(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto updateAuthPending(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto deleteAuthPending(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public SalutationDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public SalutationDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<SalutationDto> searchAll() throws Exception {
        List<SalutationDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = salutaitionDao.searchAll(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<SalutationDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }
}
