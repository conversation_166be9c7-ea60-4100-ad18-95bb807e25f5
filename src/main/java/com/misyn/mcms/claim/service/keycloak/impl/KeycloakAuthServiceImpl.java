package com.misyn.mcms.claim.service.keycloak.impl;

import com.misyn.mcms.claim.dao.UserDao;
import com.misyn.mcms.claim.dao.impl.UserDaoImpl;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.keycloak.KeycloakAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
public class KeycloakAuthServiceImpl extends AbstractBaseService<KeycloakAuthServiceImpl> implements KeycloakAuthService {
    private static final Logger LOGGER = LoggerFactory.getLogger(KeycloakAuthServiceImpl.class);
    private final UserDao userDao = new UserDaoImpl();

    @Override
    public UserDto userLoginValidate(String username) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userDao.userLoginValidate(connection, username);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public UserDto getUserDto(String username) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userDao.getUser(connection, username);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }
}
