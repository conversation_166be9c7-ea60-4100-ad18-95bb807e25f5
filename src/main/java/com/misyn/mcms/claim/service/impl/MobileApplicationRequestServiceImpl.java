package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.MobileApplicationRequestDao;
import com.misyn.mcms.claim.dao.UserDao;
import com.misyn.mcms.claim.dao.impl.MobileApplicationRequestDaoImpl;
import com.misyn.mcms.claim.dao.impl.UserDaoImpl;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.MobileApplicationRequestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class MobileApplicationRequestServiceImpl extends AbstractBaseService<MobileApplicationRequestServiceImpl> implements MobileApplicationRequestService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MobileApplicationRequestServiceImpl.class);
    private MobileApplicationRequestDao mobileApplicationRequestDao = new MobileApplicationRequestDaoImpl();
    private UserDao userDao = new UserDaoImpl();

    @Override
    public DataGridDto getMobileRequestDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String filteredBy) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = mobileApplicationRequestDao.getDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField, fromDate, toDate, filteredBy);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getMobileInspectionDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = mobileApplicationRequestDao.getInspectionDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<UserDto> getAllAssessor(Integer accessUsrType) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return getUsersByAccessUserType(connection, accessUsrType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto searchNotification(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String notificationStatus) throws Exception {
        DataGridDto dataGridDto = new DataGridDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = mobileApplicationRequestDao.searchNotification(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, notificationStatus);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }


}
