package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimClaimPanelUserDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface ClaimPanelUserService {
    boolean saveClaimPanelUser(ClaimClaimPanelUserDto claimClaimPanelUserDto, String userPanelIds) throws Exception;

    void updateClaimPanelUser(ClaimClaimPanelUserDto claimClaimPanelUserDto) throws MisynJDBCException;

    ClaimClaimPanelUserDto searchClaimPanelUser(Object id) throws MisynJDBCException;

    List<ClaimClaimPanelUserDto> ClaimPanelUserDtolist() throws MisynJDBCException;

    public DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);


}
