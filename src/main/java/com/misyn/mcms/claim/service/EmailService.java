package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.MessageContentDetails;
import com.misyn.mcms.utility.Email;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON>la on 8/26/18.
 */
public interface EmailService {

    public void sendEmail(Email email) throws Exception;

    public void sendEmail(Connection connection, Email email) throws Exception;

    public MessageContentDetails searchMessageContentDetail(Connection connection, Integer id) throws Exception;

    public List<String> getFinanceDepartmentMailList(Connection connection);


}
