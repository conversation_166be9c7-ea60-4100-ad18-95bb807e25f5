package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimHandlerDashboardDao;
import com.misyn.mcms.claim.dao.impl.ClaimHandlerDashboardDaoImpl;
import com.misyn.mcms.claim.dto.ClaimDashboardDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimHandlerDashboardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
public class ClaimHandlerDashboardServiceImpl extends AbstractBaseService<ClaimHandlerDashboardServiceImpl> implements ClaimHandlerDashboardService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserServiceImpl.class);
    private ClaimHandlerDashboardDao claimHandlerDashboardDao = new ClaimHandlerDashboardDaoImpl();

    @Override
    public ClaimDashboardDto getCountFromClaimStatusDetail(String fromDate, String toDate) throws Exception {
        ClaimDashboardDto claimDashboardDto = new ClaimDashboardDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimDashboardDto.setClaimHandlerStatusDetailList(claimHandlerDashboardDao.getClaimHandlerStatusDetail(connection, fromDate, toDate));
            claimDashboardDto.setCalsheetStatusDetailList(claimHandlerDashboardDao.getCalsheetStatusDetail(connection, fromDate, toDate));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDashboardDto;
    }
}
