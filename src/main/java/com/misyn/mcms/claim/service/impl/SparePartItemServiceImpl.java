package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.SparePartItemDao;
import com.misyn.mcms.claim.dao.impl.SparePartItemDaoImpl;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.SparePartItemDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.SparePartItemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class SparePartItemServiceImpl extends AbstractBaseService<SparePartItemServiceImpl> implements SparePartItemService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserServiceImpl.class);
    private SparePartItemDao sparePartItemDao = new SparePartItemDaoImpl();

    @Override
    public SparePartItemDto insert(SparePartItemDto sparePartItemDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            if (sparePartItemDto.getSparePartRefNo() == 0) {
                sparePartItemDao.insertMaster(connection, sparePartItemDto);
            } else {
                sparePartItemDao.updateMaster(connection, sparePartItemDto);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return sparePartItemDto;
    }

    @Override
    public SparePartItemDto search(Integer id) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        SparePartItemDto sparePartItemDto;
        try {
            beginTransaction(connection);
            sparePartItemDto = sparePartItemDao.searchMaster(connection, id);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return sparePartItemDto;
    }

    @Override
    public DataGridDto getSparePartItemDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = sparePartItemDao.getDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public String validateparePartName(String spareParteName) throws MisynJDBCException {
        Connection connection = null;
        String spareParteNames = "";
        try {
            connection = getJDBCConnection();
            spareParteNames = sparePartItemDao.validateSparePartNmae(connection, spareParteName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return spareParteNames;
    }
}
