package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.HolidayTypeDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface HolidayTypeDetailService {
    HolidayTypeDto saveHolidayTypeDetail(HolidayTypeDto holidayTypeDto) throws MisynJDBCException;

    HolidayTypeDto updateHolidayTypeDetail(HolidayTypeDto holidayTypeDto) throws MisynJDBCException;

    HolidayTypeDto getHolidayTypeDetail(Integer holidayTypeId) throws Exception;

    List<HolidayTypeDto> getHolidayTypeDetailList() throws Exception;

    void deleteHolidayTypeDetail(Integer holidayTypeId, String deleteReason) throws Exception;

    DataGridDto getHolidayTypeDetailsDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch);
}
