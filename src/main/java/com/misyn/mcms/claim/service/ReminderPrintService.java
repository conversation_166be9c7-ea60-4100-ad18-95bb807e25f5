package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ReminderLetterFormDto;
import com.misyn.mcms.claim.dto.ReminderPrintSummaryDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface ReminderPrintService {
    ReminderPrintSummaryDto getReminderPrintSummaryDto(Integer reminderSummaryRefId);

    ReminderLetterFormDto getReminderLetterFormDto(Integer claimNo);

    void generateReminderPrint(ReminderPrintSummaryDto reminderPrintSummaryDto, UserDto user ,String email,Integer ClaimNo) throws MisynJDBCException;

    List<ReminderPrintSummaryDto> getReminderSummeryListByClaimNo(Integer claimNo);
}
