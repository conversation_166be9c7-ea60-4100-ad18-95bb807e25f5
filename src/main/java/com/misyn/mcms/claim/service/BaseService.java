package com.misyn.mcms.claim.service;



import com.misyn.mcms.claim.dto.UserDto;

import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 4/16/18.
 */
public interface BaseService<T> {

    public T insert(T t, UserDto user) throws Exception;

    public T update(T t, UserDto user) throws Exception;

    public T delete(T t, UserDto user) throws Exception;

    public T updateAuthPending(T t, UserDto user) throws Exception;

    public T deleteAuthPending(T t, UserDto user) throws Exception;

    public T auth(Object id, UserDto user) throws Exception;

    public T reject(Object id, UserDto user) throws Exception;

    public T reject(Object id, UserDto user, String rejectMessage) throws Exception;

    public T search(Object id) throws Exception;

    public T searchAuthPending(Object id) throws Exception;

    public List<T> searchAll() throws Exception;

    public List<T> searchAllAuthPending() throws Exception;

    public String getMessage(int messageId);
}
