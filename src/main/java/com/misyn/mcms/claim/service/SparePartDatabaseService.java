package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.PopupItemDto;
import com.misyn.mcms.claim.dto.SparePartDatabaseDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface SparePartDatabaseService extends BaseService<SparePartDatabaseDto> {
    SparePartDatabaseDto save(SparePartDatabaseDto sparePartDatabaseDto) throws MisynJDBCException;

    SparePartDatabaseDto searchSparePartDatabase(Integer txnId) throws MisynJDBCException;

    DataGridDto getSparePartDatabaseDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, boolean isSearch);

    List<PopupItemDto> getVehicleMake() throws MisynJDBCException;

    List<PopupItemDto> getVehicleModel(String vehicleMake) throws MisynJDBCException;
}
