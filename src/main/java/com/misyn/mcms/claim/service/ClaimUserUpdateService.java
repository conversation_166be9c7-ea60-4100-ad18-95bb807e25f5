package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.HashMap;
import java.util.List;

public interface ClaimUserUpdateService extends BaseService<ClaimHandlerDto> {
    ClaimHandlerDto searchUsingTxnId(Integer txnId) throws MisynJDBCException;

    boolean updateClaimnUser(Integer txnId, String liabilityAssignUser, String assignUser, String user, Integer claimNo, String assignUserType, String alreadyAssignUser, Integer level, Integer status) throws MisynJDBCException;

    DataGridDto getClaimHandlerDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserType, String assignUserName, boolean isSearch, boolean isRejectedClaim, String calsheetStatus, String assingUserLevel, UserDto sessionUser);

    List<String> getIdsToArray(String selectedIds);

    List<Integer> getSelectedList(String array);

    HashMap<String, String> setClaimNoandTxnIdToMap(String txnIds) throws Exception;

    HashMap<String, String> setClaimNoandAlreadyAssignUserMap(String claimNoAndTxnIds) throws Exception;

    DataGridDto getMofaDataGridDto(List<FieldParameterDto> parameterList, int i, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, String assignUserType, String assignUserName, boolean isSearch, String calsheetStatus, String status,String assignUserLevel);

    List<PopupItemDto> getMofaLevels();
}
