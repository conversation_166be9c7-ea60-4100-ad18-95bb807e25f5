package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.NotificationDto;
import com.misyn.mcms.claim.dto.NotificationFormDto;
import com.misyn.mcms.claim.dto.UserDto;

public interface NotificationService {

    NotificationFormDto getNotificationFormDto(int limit, UserDto user);

    NotificationFormDto getNotificationFormDto(UserDto user, String fromDate, String toDate, String vehicleNo, String claimNo);

    NotificationDto addNotificationDto(NotificationDto notificationDto) throws Exception;

    void updateNotification(Integer txnId, String status) throws Exception;

    int notificationCount(UserDto user);

    void updateNotificationChecked(Integer txtId, String status);

    void deleteCheckedNotification(Integer txtId, String status);

    void sendDocumentNotification();

    void sendAriNotification();

    void checkTimeDuration();
}
