package com.misyn.mcms.claim.service;


import com.misyn.mcms.claim.dto.ClaimDocumentDto;
import com.misyn.mcms.claim.dto.ClaimImageDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.exception.MisynSftpFileException;
import com.misyn.mcms.claim.exception.UserNotFoundException;

import java.io.InputStream;
import java.sql.Connection;

public interface StorageService {

    void uploadImage(ClaimImageDto claimImageDto, UserDto user) throws MisynSftpFileException;

    ClaimDocumentDto uploadDocument(Long requestFormId, ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynSftpFileException, UserNotFoundException;

    InputStream viewUploadDocument(Integer documentRefNo) throws MisynSftpFileException;

    InputStream viewUploadAudio(Integer documentRefNo) throws MisynSftpFileException;

    InputStream viewUploadImage(Integer imageRefNo) throws MisynSftpFileException;

    InputStream viewThumbUploadImage(Integer imageRefNo) throws MisynSftpFileException;

    ClaimImageDto getClaimImageDto(Integer imageRefNo);


    boolean deleteImages(String images, Integer claimNo, UserDto user, Integer jobRefNo) throws Exception;

    boolean deleteDocuments(String docs, Integer documentTypeId, Integer claimNo, Integer jobRefNo, UserDto user) throws Exception;

    void updatedDocumentCheckAndMandatoryDocumentStatus(Integer claimNo) throws MisynJDBCException;

    void updatedDocumentCheckAndMandatoryDocumentStatus(Connection connection, Integer claimNo) throws MisynJDBCException;


}
