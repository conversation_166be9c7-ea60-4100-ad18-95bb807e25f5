package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.AcknowledgementSummaryDao;
import com.misyn.mcms.claim.dao.CallCenterDao;
import com.misyn.mcms.claim.dao.ClaimDocumentTypeDao;
import com.misyn.mcms.claim.dao.ClaimWiseDocumentDao;
import com.misyn.mcms.claim.dao.impl.AcknowledgementSummaryDaoImpl;
import com.misyn.mcms.claim.dao.impl.CallCenterDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimDocumentTypeDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimWiseDocumentDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.AcknowledgementService;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
public class AcknowledgementServiceImpl extends AbstractBaseService<AcknowledgementServiceImpl> implements AcknowledgementService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AcknowledgementServiceImpl.class);
    private AcknowledgementSummaryDao acknowledgementSummaryDao = new AcknowledgementSummaryDaoImpl();
    private ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();
    private ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private CallCenterDao callCenterDao = new CallCenterDaoImpl();

    @Override
    public AcknowledgementSummaryDto insert(AcknowledgementSummaryDto acknowledgementSummaryDto, String documentsIds, UserDto user) throws Exception {
        Connection connection = null;
        try {

            List<Integer> selectedList = getSelectedList(documentsIds);
            List<ClaimDocumentTypeDto> claimDocumentTypeDtoList = new ArrayList<>();
            connection = getJDBCConnection();
            beginTransaction(connection);
            acknowledgementSummaryDto.setPrintUser(user.getUserId());
            acknowledgementSummaryDto.setPrintDatetime(Utility.sysDateTime());
            acknowledgementSummaryDto.setInputUser(user.getUserId());
            acknowledgementSummaryDto.setInputDatetime(Utility.sysDateTime());
            acknowledgementSummaryDto.setRecordStatus("P");
            acknowledgementSummaryDto = acknowledgementSummaryDao.insertMaster(connection, acknowledgementSummaryDto);

            for (Integer id : selectedList) {

                AcknowledgementDetailsDto acknowledgementDetailsDto = new AcknowledgementDetailsDto();
                acknowledgementDetailsDto.setDocumentId(id);
                acknowledgementDetailsDto.setAcknowledgementId(acknowledgementSummaryDto.getAcknowledgementId());
                ClaimDocumentTypeDto claimDocumentTypeDto = claimDocumentTypeDao.searchByDocumentTypeId(connection, id);
                claimDocumentTypeDtoList.add(claimDocumentTypeDto);
                if (null != claimDocumentTypeDto) {
                    acknowledgementSummaryDto.getList().add(claimDocumentTypeDto);
                }
                acknowledgementSummaryDao.insertDetailsMaster(connection, acknowledgementDetailsDto);

            }

            saveClaimsLogs(connection, acknowledgementSummaryDto.getClaimNo(), user, "Acknowledgement", "Generate a acknowledgement letter");
            commitTransaction(connection);

        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return acknowledgementSummaryDto;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = acknowledgementSummaryDao.getClaimHandlerDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<ClaimWiseDocumentDto> searchDocumentsByClaimNo(Integer claimNo) throws Exception {
        List<ClaimWiseDocumentDto> claimWiseDocumentDtos = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimWiseDocumentDtos = claimWiseDocumentDao.searchAllByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimWiseDocumentDtos;
    }

    @Override
    public AcknowledgementSummaryDto getAcknowledgementSummarybyId(Integer acknowledgementId) throws Exception {
        AcknowledgementSummaryDto acknowledgementSummaryDto = new AcknowledgementSummaryDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            acknowledgementSummaryDto = acknowledgementSummaryDao.getAcknowledgementSummeryList(connection, acknowledgementId);
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, acknowledgementSummaryDto.getClaimNo());
            acknowledgementSummaryDto.setClaimsDto(claimsDto);
            for (ClaimDocumentTypeDto claimDocumentTypeDto : acknowledgementSummaryDto.getList()) {
                ClaimDocumentTypeDto documentTypeDto;
                documentTypeDto = claimDocumentTypeDao.searchByDocumentTypeId(connection, claimDocumentTypeDto.getDocumentTypeId());
                claimDocumentTypeDto.setDocumentTypeName(documentTypeDto.getDocumentTypeName());
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return acknowledgementSummaryDto;
    }

    @Override
    public List<AcknowledgementSummaryDto> getPreviousAcknowledgementList(Integer claimNo) {
        List<AcknowledgementSummaryDto> list = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = acknowledgementSummaryDao.getAcknowledgementSummeryListByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    private List<Integer> getSelectedList(String array) {
        List<Integer> list = null;
        if (!array.isEmpty()) {
            list = Arrays.stream(array.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }
}
