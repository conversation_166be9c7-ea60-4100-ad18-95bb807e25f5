package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.AssessorFeeDao;
import com.misyn.mcms.claim.dao.impl.AssessorFeeDaoImpl;
import com.misyn.mcms.claim.dto.AssessorFeeDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.ListItemDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.AssessorFeeService;
import com.misyn.mcms.utility.AppConstant;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.List;

public class AssessorFeeServiceImpl extends AbstractBaseService<AssessorFeeServiceImpl> implements AssessorFeeService {
    private static final Logger LOGGER = Logger.getLogger(AssessorFeeServiceImpl.class);
    private final AssessorFeeDao assessorFeeDao = new AssessorFeeDaoImpl();


    private void validateFields(AssessorFeeDto assessorFeeDto) throws Exception {
       /* if (StringUtils.isNotBlank(assessorFeeDto.getRemark()) && assessorFeeDto.getRemark().length() > 255) {
            throw new MisynJDBCException("Remark exceeds the maximum number of Characters");
        }
        if (StringUtils.isNotBlank(assessorFeeDto.getClaimType()) && assessorFeeDto.getClaimType().length() > 255) {
            throw new MisynJDBCException("Claim Type exceeds the maximum number of Characters");
        }*/
        assessorFeeDto.setRecordStatus(AppConstant.ACTIVE_STATUS);
    }

    private void existTimeSlot(Connection connection, AssessorFeeDto assessorFeeDto) throws Exception {
        Boolean existTimeSlot = assessorFeeDao.isExistTimeSlot(connection, assessorFeeDto);
        if (Boolean.TRUE.equals(existTimeSlot)) {
            throw new MisynJDBCException("Time slot already exists or overlaps for the selected inspection type. Please choose a different time range.");
        }
    }

    @Override
    public AssessorFeeDto saveAssessorFee(AssessorFeeDto assessorFeeDto) throws MisynJDBCException {
        Connection connection = null;
        AssessorFeeDto savedDto = null;
        try {
            connection = getJDBCConnection();
            if (null == assessorFeeDto.getAssessorFeeDetailId() || assessorFeeDto.getAssessorFeeDetailId().equals(0)) {
                validateFields(assessorFeeDto);
                existTimeSlot(connection, assessorFeeDto);
                assessorFeeDto.setInputDateTime(LocalDateTime.now());
                assessorFeeDto.setLastModifiedDateTime(LocalDateTime.now());
                savedDto = assessorFeeDao.insertAssessorFee(connection, assessorFeeDto);
            }
            return savedDto;
        } catch (Exception e) {
            LOGGER.error(e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public AssessorFeeDto updateAssessorFee(AssessorFeeDto assessorFeeDto) throws MisynJDBCException {
        Connection connection = null;
        AssessorFeeDto savedDto = null;
        try {
            connection = getJDBCConnection();
            if (null != assessorFeeDto.getAssessorFeeDetailId() || !assessorFeeDto.getAssessorFeeDetailId().equals(0)) {
                validateFields(assessorFeeDto);
                existTimeSlot(connection, assessorFeeDto);
                assessorFeeDto.setLastModifiedDateTime(LocalDateTime.now());
                savedDto = assessorFeeDao.updateAssessorFee(connection, assessorFeeDto);
            }
            return savedDto;
        } catch (Exception e) {
            LOGGER.error(e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public AssessorFeeDto getAssessorFee(Integer assessorFeeDetailId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return assessorFeeDao.getAssessorFee(connection, assessorFeeDetailId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ListItemDto> getInspectionTypeList() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return assessorFeeDao.getInspectionTypeList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ListItemDto> getDayTypeList() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return assessorFeeDao.getDayTypeList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ListItemDto> getTimeSlotsByInspectionTypeId(Integer inspectionTypeId, Integer jobType) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return assessorFeeDao.getTimeSlotsByInspectionTypeId(connection, inspectionTypeId, jobType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void deleteAssessorFee(Integer assessorFeeDetailId, String userName) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            assessorFeeDao.deleteAssessorFee(connection, assessorFeeDetailId, userName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }


    @Override
    public DataGridDto getAssessorFeeDetailDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = assessorFeeDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField);

        } catch (Exception e) {
            LOGGER.error(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

}
