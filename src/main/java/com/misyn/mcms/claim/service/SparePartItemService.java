package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.SparePartItemDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface SparePartItemService {

    SparePartItemDto insert(SparePartItemDto sparePartItemDto) throws MisynJDBCException;

    SparePartItemDto search(Integer id) throws MisynJDBCException;

    DataGridDto getSparePartItemDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    String validateparePartName(String spareParteName) throws MisynJDBCException;
}
