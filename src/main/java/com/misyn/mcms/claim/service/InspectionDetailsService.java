package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.utility.ListBoxItem;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON>la on 5/16/18.
 */
public interface InspectionDetailsService extends BaseService<InspectionDetailsDto> {

    DataGridDto getClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNo(Integer claimNo, Integer departmentId) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNoMultipleDepartmentId(Integer claimNo, String departmentId) throws Exception;

    ClaimInspectionTypeDto getInspectionTypeDto(String id) throws Exception;

    List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Integer departmentId, Integer inspectionTypeId) throws Exception;

    List<ClaimDocumentDto> getClaimDocumentDtoList(Integer jobRefNo, Integer departmentId) throws Exception;

    List<ClaimThirdPartyDetailsGenericDto> getClaimThirdPartyDetailsGeneric(Integer claimNo) throws Exception;

    List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo, Integer jobRefNo, Integer departmentId, Integer inspectionTypeId);

    List<PreviousClaimsDto> getPreviousClaimList(String vehicleNo, Integer refNo) throws Exception;

    List<PreviousClaimsDto> getPreviousInspectionClaimList(Integer claimNo, Integer jobNo) throws Exception;

    List<PreviousClaimsDto> getPreviousInspectionList(Integer claimNo) throws Exception;

    List<ClaimImageDto> getClaimImageDtoList(Integer claimNo, Integer jobRefNo);

    List<ClaimImageFormDto> getClaimImageFormDtoList(Integer claimNo);

    DataGridDto getSubmittedInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int i, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, UserDto user, String assignUser, String status) throws Exception;

    DataGridDto getFwdDesktopInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int i, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, UserDto user, int recordStatus) throws Exception;

    List<ClaimImageDto> findAllClaimImageDtoByClaimNo(Integer claimNo);

    InspectionDetailsDto setAcrValue(InspectionDetailsDto inspectionDetailsDto, AssessorAllocationDto assessorAllocationDto);

    InspectionDetailsDto setAcrValue(InspectionDetailsDto inspectionDetails, BigDecimal acr, AssessorAllocationDto assessorAllocationDto);

    InspectionDetailsDto setPenaltyValue(InspectionDetailsDto inspectionDetailsDto, AssessorAllocationDto assessorAllocationDto);

    InspectionDetailsDto setPenaltyValue(InspectionDetailsDto inspectionDetails, BigDecimal bladTyre, BigDecimal baldPercentage, BigDecimal underInsure, BigDecimal underPercentage, AssessorAllocationDto assessorAllocationDto);

    InspectionDetailsDto search(Connection connection, Object id) throws Exception;

    InspectionDetailsDto searchByMe(Connection connection, Object id) throws Exception;

    ErrorMessageDto saveRemark(SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception;

    String getAssessorTypeByRefNo(String refNo) throws Exception;

    Integer getInspectionType(Integer refNo, Integer claimNo) throws Exception;

    Integer getRefNoForOnOrOffSite(Integer claimNo) throws Exception;

    public DataGridDto getSubmittedInspectionOfferDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, String isRteOrAssessorDetails, String type, String inspectionId) throws Exception;

    boolean isRteApproved(Integer refNo) throws Exception;

    boolean isAssessorApproved(Integer refNo) throws Exception;

    List<AssessorDto> getAllAssignAssessorsByClaim(Integer claimNo);

    BigDecimal getMileageFee(String assessorType);

    BigDecimal getAssessorFee(Integer assessorFeeDetailId, String assessorType);

    DataGridDto getSparePartsCoordInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, Integer Status) throws Exception;

    DataGridDto getScrutinizingInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, Integer status) throws Exception;

    DataGridDto getInspectionGridDtoForwardToRte(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType) throws Exception;

    List<ListBoxItem> getDesktopReassignReasons() throws Exception;

    List<ListBoxItem> getReassignReasons() throws Exception;

    DataGridDto getAssessorPendingClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate);
}
