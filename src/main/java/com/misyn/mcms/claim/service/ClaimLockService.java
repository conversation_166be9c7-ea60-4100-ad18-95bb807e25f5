package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimLockDto;

public interface ClaimLockService {

    boolean isLockIntimation(ClaimLockDto claimLockDto);

    void lockIntimation(ClaimLockDto claimLockDto);

    void unLockIntimation(ClaimLockDto claimLockDto, String userId);

    void unLockIntimation(Integer key, String userId);

    ClaimLockDto getClaimLockDto(Integer key);
}
