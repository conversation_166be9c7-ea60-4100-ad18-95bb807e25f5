package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.AssessorDao;
import com.misyn.mcms.claim.dao.impl.AssessorDaoImpl;
import com.misyn.mcms.claim.dto.AssessorDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.AssessorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class AssessorServiceImpl extends AbstractBaseService<AssessorServiceImpl> implements AssessorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorServiceImpl.class);
    private AssessorDao assessorDao = new AssessorDaoImpl();

    @Override
    public AssessorDto insert(AssessorDto assessorDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorDto update(AssessorDto assessorDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorDto delete(AssessorDto assessorDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorDto updateAuthPending(AssessorDto assessorDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorDto deleteAuthPending(AssessorDto assessorDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public AssessorDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public AssessorDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public AssessorDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<AssessorDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<AssessorDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public List<AssessorDto> getAssessorListByDivisionCode(String divisionCode) throws Exception {
        List<AssessorDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = assessorDao.getAssessorListByDivisionCode(connection, divisionCode);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public String getAssessorMobileNo(String assessorCode) throws Exception {
        String mobileNo = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            mobileNo = assessorDao.getAssessorMobileNo(connection, assessorCode);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return mobileNo;
    }
}
