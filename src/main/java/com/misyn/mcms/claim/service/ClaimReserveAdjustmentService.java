package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;

import java.util.List;

public interface ClaimReserveAdjustmentService {
    boolean saveReserveAdjustment(ClaimReserveAdjustmentTypeDto dto) throws Exception;

    DataGridDto getReserveAdjustmentDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    boolean isActiveRecordExists(int periodId, int categoryId);

    List<ReserveCategoryDto> getReserveCategories();

    List<ReservePeriodDto> getReservePeriods();

}
