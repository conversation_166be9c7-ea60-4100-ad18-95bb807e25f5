package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.TATConfigDao;
import com.misyn.mcms.claim.dao.impl.TATConfigDaoImpl;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.TATDetailDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.TATConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
public class TATConfigServiceImpl extends AbstractBaseService<TATConfigServiceImpl> implements TATConfigService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TATConfigServiceImpl.class);
    private final TATConfigDao tatConfigDao = new TATConfigDaoImpl();

    @Override
    public String saveTATData(TATDetailDto tatDetailDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();

            if (tatConfigDao.isAvailable(connection, tatDetailDto)) {
                tatConfigDao.updateByID(connection, tatDetailDto);
//                throw new Exception("TAT ID Already Exists.");
            } else {
                tatConfigDao.saveByID(connection, tatDetailDto);
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while saving TAT details.", e);
            throw new Exception("Failed to save TAT details. Please try again !", e);
        } finally {
            releaseJDBCConnection(connection);
        }

        return "Record submission successful.";
    }

    @Override
    public TATDetailDto searchTATDataById(int tatId, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return tatConfigDao.searchTATData(tatId, connection);

        } catch (Exception e) {
            LOGGER.error("Error occurred while saving TAT details.", e);
            throw new Exception("Failed to save TAT details. Please try again !", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public String deleteTATData(int tatId, String deleteReason, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            tatConfigDao.deleteTATData(tatId, deleteReason, connection);
            return "Delete Successful";

        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting TAT details.", e);
            throw new Exception("Failed to delete TAT details. Please try again !", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getAllTATData(UserDto user, int start, int length, String sortColumn, String sortDirection, String searchValue) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return tatConfigDao.findAllTATData(connection, start, length, sortColumn, sortDirection, searchValue);
        } catch (Exception e) {
            LOGGER.error("Error occurred while retrieving TAT details.", e);
            throw new Exception("Failed to retrieve TAT details. Please try again !", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    public DataGridDto filterTATDetails(int tatId, String taskName, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<TATDetailDto> tatDetailsList = tatConfigDao.filterTATDetails(tatId, taskName, connection);
            DataGridDto dataGridDto = new DataGridDto();
            dataGridDto.setDraw(1); // This should be taken from the request if available
            dataGridDto.setRecordsTotal(tatDetailsList.size());
            dataGridDto.setRecordsFiltered(tatDetailsList.size());
            dataGridDto.setData(new ArrayList<>(tatDetailsList));
            return dataGridDto;
        } catch (Exception e) {
            LOGGER.error("Error occurred while filtering TAT details.", e);
            throw new Exception("Failed to filter TAT details. Please try again!", e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }


}
