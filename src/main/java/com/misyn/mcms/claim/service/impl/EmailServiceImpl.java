package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.EmailDao;
import com.misyn.mcms.claim.dao.impl.EmailDaoImpl;
import com.misyn.mcms.claim.dto.MessageContentDetails;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.EmailService;
import com.misyn.mcms.utility.ActiveMQEmail;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class EmailServiceImpl extends AbstractBaseService<EmailServiceImpl> implements EmailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorAllocationServiceImpl.class);
    private ActiveMQEmail activeMQEmail = new ActiveMQEmail();
    private EmailDao emailDao = new EmailDaoImpl();

    @Override
    public void sendEmail(Email email) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            email.setEmailMassege(getEmailBody(email));
            email.setJmsSentTime(Utility.sysDateTime());
            email.setMailSentTime(Utility.sysDateTime());
            if (null != email.getFileList() && !email.getFileList().isEmpty()) {
                email.setJmsAttachment(email.getFileList().toString().replaceAll("\\[", "").replaceAll("]", "").replaceAll(",", "|"));
            }
            email.setStatus(AppConstant.CONST_STATUS_JMS_PENDING);
            emailDao.insertEmail(connection, email);
            createBytesMap(email);
            activeMQEmail.sendFromEmail(email);
            email.setStatus(AppConstant.CONST_STATUS_JMS_SUCCESS);
            emailDao.updateEmail(connection, email);
            commitTransaction(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void sendEmail(Connection connection, Email email) throws Exception {

        try {
            email.setEmailMassege(getEmailBody(email));
            email.setJmsSentTime(Utility.sysDateTime());
            email.setMailSentTime(Utility.sysDateTime());
            if (null != email.getFileList() && !email.getFileList().isEmpty()) {
                email.setJmsAttachment(email.getFileList().toString().replaceAll("\\[", "").replaceAll("]", "").replaceAll(",", "|"));
            }
            email.setStatus(AppConstant.CONST_STATUS_JMS_PENDING);
            emailDao.insertEmail(connection, email);
            createBytesMap(email);
            activeMQEmail.sendFromEmail(email);
            email.setStatus(AppConstant.CONST_STATUS_JMS_SUCCESS);
            emailDao.updateEmail(connection, email);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            //todo
//            throw new Exception(e.getMessage());
        }
    }

    @Override
    public MessageContentDetails searchMessageContentDetail(Connection connection, Integer id) throws Exception {
        MessageContentDetails messageContentDetails = null;
        try {
            messageContentDetails = emailDao.searchMessageContentDetail(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return messageContentDetails;
    }

    @Override
    public List<String> getFinanceDepartmentMailList(Connection connection) {
        List<String> list = new ArrayList<>();
        try {
            list = emailDao.getFinanceDepartmentMailList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    public static void main(String[] args) {
        Email email = new Email();
        email.setToAddresses("<EMAIL>");
        email.setStatus("jp");

        email.setSubject("TEXT");
        email.setEmailMassege("1223fr34");
        // File file = new File("/home/<USER>/Pictures/EMAILTEST.TXT");
        File file1 = new File("/home/<USER>/Pictures/Book1.xlsx");
        List<File> fileList = new ArrayList<>();
        //   fileList.add(file);
        fileList.add(file1);
        email.setFileList(fileList);

        try {
            new EmailServiceImpl().sendEmail(email);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Email createBytesMap(Email email) {
        List<String> attachementFileNameList = new ArrayList<>();
        Map<String, byte[]> map = new HashMap();
        try {
            if (null != email.getJmsAttachment() && !email.getJmsAttachment().isEmpty()) {
                String[] filePathList = email.getJmsAttachment().split("\\|");
                for (String filePath : filePathList) {
                    File file = new File(filePath);
                    attachementFileNameList.add(file.getName());
                    byte[] bytesArray = new byte[(int) file.length()];
                    try (FileInputStream fis = new FileInputStream(file)) {
                        fis.read(bytesArray);
                    }
                    map.put(file.getName(), bytesArray);
                }
            }
            email.setAttachmentNameList(attachementFileNameList);
            email.setAttachmentMap(map);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return email;
    }
}
