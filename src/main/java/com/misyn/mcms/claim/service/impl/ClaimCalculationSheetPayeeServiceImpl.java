package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.admin.admin.dao.BranchMstDao;
import com.misyn.mcms.admin.admin.dao.impl.BranchMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.CallCenterService;
import com.misyn.mcms.claim.service.ClaimCalculationSheetPayeeService;
import com.misyn.mcms.claim.service.EmailService;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ClaimCalculationSheetPayeeServiceImpl extends AbstractBaseService<ClaimCalculationSheetPayeeServiceImpl> implements ClaimCalculationSheetPayeeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimCalculationSheetPayeeServiceImpl.class);
    private static final Object LOCK = new Object();
    DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private ClaimCalculationSheetPayeeDao claimCalculationSheetPayeeDao = new ClaimCalculationSheetPayeeDaoImpl();
    private ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private EmailService emailService = new EmailServiceImpl();
    private CallCenterService callCenterService = new CallCenterServiceImpl();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private AssessorPaymentDetailsDao assessorPaymentDetailsDao = new AssessorPaymentDetailsDaoImpl();
    private ClaimPaymentDispatchDao claimPaymentDispatchDao = new ClaimPaymentDispatchDaoImpl();
    private BranchMstDao branchMstDao = new BranchMstDaoImpl();
    private UserDao userDao = new UserDaoImpl();

    @Override
    public void getSendPendingEmailList() throws Exception {
        Connection connection = null;
        List<ClaimCalculationSheetPayeeDto> list = null;
        try {
            connection = getJDBCConnection();
            MessageContentDetails messageContentDetails = emailService.searchMessageContentDetail(connection, AppConstant.VOUCER_GENERATE_EMAIL);
            List<Integer> voucherGeneratedList = claimCalculationSheetPayeeDao.getVoucherGeneratedList(connection);


            StringBuilder sb;
            for (Integer calSheetId : voucherGeneratedList) {
                Email email = new Email();
                ClaimsDto claimsDto = new ClaimsDto();
                ArrayList<String> paramList = new ArrayList<>();


                List<ClaimCalculationSheetPayeeDto> pendingEmailList = claimCalculationSheetPayeeDao.getPendingEmailList(connection, calSheetId, true);
                List<ClaimCalculationSheetPayeeDto> payeeList = claimCalculationSheetPayeeDao.searchByCalSheetId(connection, calSheetId);
                if (null != pendingEmailList && null != payeeList && pendingEmailList.size() > 0 && payeeList.size() > 0 && payeeList.size() == pendingEmailList.size()) {

                    ClaimCalculationSheetMainDto claimCalculationSheetMainDto = claimCalculationSheetMainDao.searchMaster(connection, calSheetId);
                    claimsDto = getClaimsDto(connection, claimsDto, paramList, claimCalculationSheetMainDto);

                    sb = new StringBuilder();
                    for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : pendingEmailList) {
                        sb = sb.append(" <tr style=\"border: 1px solid black;\">");
                        String name;
                        name = claimCalculationSheetPayeeDto.getPayeeDesc();
                        switch (claimCalculationSheetPayeeDto.getPayeeId()) {
                            case 1:
                                //TODO  20191104
                                //     this.sendVoucherGenerateSmsToCustomer(connection, claimsDto, claimCalculationSheetPayeeDto);
                                //     this.sendVoucherGenerateSmsToAgent(connection, claimsDto, claimCalculationSheetPayeeDto);
                                break;
                            case 2:
                            case 3:
                            case 9:
                            case 10:
                                String policyChannelType = claimCalculationSheetMainDao.getPolicyChannelType(connection, claimCalculationSheetMainDto.getClaimNo());
                                PopupItemDto popupItemDto = dbRecordCommonFunction.getPopupItemDto(connection, policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? "takaful_claim_company_details_main" : "claim_company_details_main", "V_COMPANY_CODE", "V_COMPANY_NAME", "N_REF_NO=" + claimCalculationSheetPayeeDto.getPayeeDesc());
                                name = popupItemDto.getLabel();
                                break;
                        }
                        sb = sb.append(" <td style=\"border: 1px solid black;\">").append(name).append("</td>");
                        sb = sb.append(" <td style=\"border: 1px solid black;\" align=\"right\"> ").append(claimCalculationSheetPayeeDto.getAmount()).append("</td>");
                        sb = sb.append(" <td style=\"border: 1px solid black;\">").append(claimCalculationSheetPayeeDto.getVoucherNo()).append("</td></tr>");
                        claimCalculationSheetPayeeDao.updateEmailStatus(connection, claimCalculationSheetPayeeDto.getCalSheetPayeeId());
                    }

                    paramList.add(sb.toString());
                    paramList.add(claimCalculationSheetMainDto.getInputUser());
                    paramList.add(null == claimCalculationSheetMainDto.getAprUserId() ? AppConstant.STRING_EMPTY : claimCalculationSheetMainDto.getAprUserId());
                    paramList.add(getAppUrl().concat("?url=ClaimHandlerController/viewEdit").concat("?P_N_CLIM_NO=").concat(claimCalculationSheetMainDto.getClaimNo().toString()));
                    paramList.add(getInternalAppUrl().concat("?url=ClaimHandlerController/viewEdit").concat("?P_N_CLIM_NO=").concat(claimCalculationSheetMainDto.getClaimNo().toString()));


                    List<String> financeDepartmentMailList = emailService.getFinanceDepartmentMailList(connection);

                    if (null != claimsDto) {
                        String subject = claimsDto.getPolicyDto().getPolicyChannelType().concat(",Vehicle No :-").concat(claimsDto.getVehicleNo()).concat(",Claim No :-").
                                concat(claimCalculationSheetMainDto.getClaimNo().toString()).concat(",Date Of Accident :-").concat(claimsDto.getAccidDate())
                                .concat("/").concat(messageContentDetails.getSubject());
                        email.setSubject(subject);
                    }

                    email.setEmailMassege(messageContentDetails.getMessageBody());
                    email.setParameterEmail(paramList);

                    for (String emailAddress : financeDepartmentMailList) {
                        email.setToAddresses(emailAddress);
                        emailService.sendEmail(connection, email);
                    }


                }


            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }

    }

    private void sendVoucherGenerateSmsToAgent(Connection connection, ClaimsDto claimsDto, ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto, UserDto user) {
        try {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(Utility.formatCurrency(claimCalculationSheetPayeeDto.getAmount()));
            smsParameterList.add(claimsDto.getVehicleNo());
            smsParameterList.add(claimsDto.getPolicyDto().getCustName());
            sendSmsMessage(connection, 41, smsParameterList, claimsDto.getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), claimsDto.getPolicyDto().getPolicyChannelType(), user, claimsDto.getClaimNo(), AppConstant.SEND_TO_AGENT);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void sendVoucherGenerateSmsToCustomer(Connection connection, ClaimsDto claimsDto, ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto, UserDto user) {
        try {
            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(Utility.formatCurrency(claimCalculationSheetPayeeDto.getAmount()));
            smsParameterList.add(claimsDto.getVehicleNo());
            sendSmsMessage(connection, 40, smsParameterList, claimsDto.getPolicyDto().getCustMobileNo(), claimsDto.getPolicyDto().getPolicyChannelType(), user, claimsDto.getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private ClaimsDto getClaimsDto(Connection connection, ClaimsDto claimsDto, ArrayList<String> paramList, ClaimCalculationSheetMainDto claimCalculationSheetMainDto) {
        if (null != claimCalculationSheetMainDto) {
            claimsDto = callCenterService.getViewAccidentClaimsDto(connection, claimCalculationSheetMainDto.getClaimNo());
            if (null != claimsDto) {
                paramList.add(claimsDto.getVehicleNo());
                paramList.add(claimsDto.getAccidDate());
                paramList.add("External Link - ".concat(claimCalculationSheetMainDto.getClaimNo().toString().concat("  (").concat(commonUtilDao.findOne(connection, "claim_calculation_sheet_type", "V_CAL_SHEET_TYPE_DESC", "N_CAL_SHEET_TYPE_ID=" + claimCalculationSheetMainDto.getCalSheetType())).concat(")")));
                paramList.add("Internal Link - ".concat(claimCalculationSheetMainDto.getClaimNo().toString().concat("  (").concat(commonUtilDao.findOne(connection, "claim_calculation_sheet_type", "V_CAL_SHEET_TYPE_DESC", "N_CAL_SHEET_TYPE_ID=" + claimCalculationSheetMainDto.getCalSheetType())).concat(")")));
                paramList.add(claimsDto.getIsfClaimNo());
                paramList.add(claimsDto.getPolicyDto().getPolicyNumber());
                paramList.add(claimCalculationSheetMainDto.getPayableAmount().toString());
                // ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimCalculationSheetMainDto.getClaimNo());


            }

        }
        return claimsDto;
    }

    @Override
    public void getSendAssessorPaymentEmail() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<AssessorPaymentDetailsDto> assessorPaymentDetailsList = assessorPaymentDetailsDao.getPendingEmailAndSuccessVoucherGeneration(connection);

            for (AssessorPaymentDetailsDto assessorPaymentDetailsDto : assessorPaymentDetailsList) {
                synchronized (LOCK) {
                    sendMail(connection, assessorPaymentDetailsDto);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void changeCheckReceiveStatus(ClaimPaymentDispatchDto claimPaymentDispatchDto, SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            ClaimPaymentDispatchDto voucherDetails = claimPaymentDispatchDao.getVoucherDetails(connection, claimPaymentDispatchDto.getPayeeId());
            if (null == voucherDetails) {
                claimPaymentDispatchDao.savePaymentDispatch(connection, claimPaymentDispatchDto);
            } else {
                claimPaymentDispatchDao.updatePaymentDispatch(connection, claimPaymentDispatchDto);
            }
            String branchCode = claimPaymentDispatchDto.getDispatchLocation();
            if (!branchCode.isEmpty() && !branchCode.equals(claimPaymentDispatchDto.getDispatchedLocation().getBranchCode())) {
                BranchDetailDto branchDetailByBranchCode = branchMstDao.getBranchDetailByBranchCode(connection, branchCode);
                if (null != branchDetailByBranchCode) {
                    List<UserDto> branchUsersByBranchCode = userDao.findBranchUsersByBranchCode(connection, branchCode);
                    String URL = AppConstant.SUPER_DASHBOARD_VIEW.concat("?P_N_CLIM_NO=").concat(String.valueOf(claimPaymentDispatchDto.getClaimNo()));
                    BranchDetailDto branchDetailDto = branchMstDao.getBranchDetailByBranchCode(connection, claimPaymentDispatchDto.getDispatchedLocation().getBranchCode());
                    for (UserDto branchUser : branchUsersByBranchCode) {
                        saveNotification(connection, claimPaymentDispatchDto.getClaimNo(), user.getUserId(), branchUser.getUserId(), "Cheque Dispatch Location changed to ".concat(branchDetailDto.getBranchName()).concat(" (").concat(user.getUserId()).concat("). Please Do the Needful"), URL);
                    }
                }
            }
            saveClaimsLogs(connection, claimPaymentDispatchDto.getClaimNo(), user, "Cheque Receive Status Changed", "Cheque no [" + claimPaymentDispatchDto.getChequeNo() + "] has been marked as [".concat(claimPaymentDispatchDto.getChequeDispatchStatus() == AppConstant.YES ? "Received]" : "Not Received]").concat(" by " + user.getUserId()));
            specialRemarkDao.insertMaster(connection, specialRemarkDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void sendMail(Connection connection, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception {
        try {
            List<String> financeDepartmentMailList = emailService.getFinanceDepartmentMailList(connection);
            MessageContentDetails messageContentDetails = emailService.searchMessageContentDetail(connection, AppConstant.PAYMENT_APPROVE_EMAIL);
            Email email = new Email();
            ArrayList<String> paramList = new ArrayList<>();
            Integer countForAssessorPaymentListByGenerateOccurrency = assessorPaymentDetailsDao.getCountForAssessorPaymentListByGenerateOccurrency(connection, assessorPaymentDetailsDto.getGenerateOccurrency());
            Integer countForAssessorPaymentListByVoucherNo = assessorPaymentDetailsDao.getCountForAssessorPaymentListByVoucherNo(connection, assessorPaymentDetailsDto.getGenerateOccurrency());

            if (countForAssessorPaymentListByGenerateOccurrency.equals(countForAssessorPaymentListByVoucherNo)) {
                StringBuilder sb = new StringBuilder();

                List<AssessorPaymentDetailForEmailDto> voucherGeneratedList = assessorPaymentDetailsDao.getPendingEmailList(connection, assessorPaymentDetailsDto.getGenerateOccurrency());
                if (voucherGeneratedList.isEmpty()) {
                    LOGGER.info("No data found for generateOccurrency : {}", assessorPaymentDetailsDto.getGenerateOccurrency());
                    return;
                }
                AssessorPaymentDetailForEmailDto assessorPaymentDetailForEmailDto = voucherGeneratedList.getFirst();
                if (Objects.isNull(assessorPaymentDetailForEmailDto)) {
                    LOGGER.info("No data found for generateOccurrency : {}", assessorPaymentDetailsDto.getGenerateOccurrency());
                    return;
                }

                BigDecimal totalCostOfCall = new BigDecimal(0);
                BigDecimal totalBeforeScheduleAmount = new BigDecimal(0);
                BigDecimal totalScheduleAmount = new BigDecimal(0);
                BigDecimal totalOtherCharges = new BigDecimal(0);
                BigDecimal totalDeductionFee = new BigDecimal(0);
                BigDecimal totalTravelFee = new BigDecimal(0);
                BigDecimal totalVoucherAmount = new BigDecimal(0);
                String name = assessorPaymentDetailForEmailDto.getName();
                String fromDate = assessorPaymentDetailForEmailDto.getFromDate();
                String toDate = assessorPaymentDetailForEmailDto.getToDate();
                String regionalEng = assessorPaymentDetailForEmailDto.getInputUser();
                String apprvUser = assessorPaymentDetailForEmailDto.getApproveUser();
                Integer index = 1;
                paramList.add(" Assessor Name : " + name);
                paramList.add("Date : " + fromDate + " To " + toDate);
                paramList.add("Regional Engineer : " + regionalEng);
                paramList.add("Special Team Approved User : " + apprvUser);


                for (AssessorPaymentDetailForEmailDto dto : voucherGeneratedList) {
                    sb = sb.append(" <tr style=\"border: 1px solid black;\">");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(index).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(dto.getClaimNo()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(dto.getRegistrationNo()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(dto.getJobDesc());
                    if (1 == dto.getJobType()) {
                        sb.append(" (DAY)").append("</td>");
                    } else {
                        sb.append(" (NIGHT)").append("</td>");
                    }
                    String dateMonth = Utility.getDate(dto.getVoucherDate(), AppConstant.DATE_FORMAT);
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(dto.getVoucherNo()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(dateMonth).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(dto.getBeforeDeductionScheduleAmount()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(dto.getDeductionFee()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(dto.getScheduleAmount()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(dto.getCostOfCall()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(dto.getTravelFee()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(dto.getOtherCharges()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(dto.getVoucherAmount()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(dto.getPolicyChannelType()).append("</td>");
                    sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"center\">").append(dto.getIsfClaimNo()).append("</td></tr>");


                    totalBeforeScheduleAmount = totalBeforeScheduleAmount.add(dto.getBeforeDeductionScheduleAmount());
                    totalDeductionFee = totalDeductionFee.add(dto.getDeductionFee());
                    totalScheduleAmount = totalScheduleAmount.add(dto.getScheduleAmount());
                    totalCostOfCall = totalCostOfCall.add(dto.getCostOfCall());
                    totalTravelFee = totalTravelFee.add(dto.getTravelFee());
                    totalOtherCharges = totalOtherCharges.add(dto.getOtherCharges());
                    totalVoucherAmount = totalVoucherAmount.add(dto.getVoucherAmount());
                    ++index;
                }
                sb = sb.append(" <tr style=\"border: 1px solid black;\">");
                sb = sb.append(" <td style=\"border: 1px solid black;\">").append(AppConstant.STRING_EMPTY).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\">").append(AppConstant.STRING_EMPTY).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\">").append(AppConstant.STRING_EMPTY).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\">").append(AppConstant.STRING_EMPTY).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\">").append(AppConstant.STRING_EMPTY).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\">").append("Total").append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(totalBeforeScheduleAmount).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(totalDeductionFee).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(totalScheduleAmount).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(totalCostOfCall).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(totalTravelFee).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(totalOtherCharges).append("</td>");
                sb = sb.append(" <td style=\"border: 1px solid black;\"align=\"right\">").append(totalVoucherAmount).append("</td></tr>");

                paramList.add(sb.toString());
                email.setEmailMassege(messageContentDetails.getMessageBody());
                email.setParameterEmail(paramList);
                String subject = "Assessor payment ".concat(" - " + name);
                for (String emailAddress : financeDepartmentMailList) {
                    email.setToAddresses(emailAddress);
                    email.setSubject(subject);
                    emailService.sendEmail(connection, email);
                }
                boolean isUpdated = assessorPaymentDetailsDao.updateEmailStatus(connection, assessorPaymentDetailsDto.getGenerateOccurrency());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
