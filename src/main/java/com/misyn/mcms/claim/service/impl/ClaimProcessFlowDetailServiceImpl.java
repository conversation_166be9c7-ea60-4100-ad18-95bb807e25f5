package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimProcessFlowDetailDao;
import com.misyn.mcms.claim.dao.impl.BankDetailsDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimProcessFlowDetailDaoImpl;
import com.misyn.mcms.claim.dto.ClaimProcessFlowDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimFlowDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;

public class ClaimProcessFlowDetailServiceImpl extends AbstractBaseService<ClaimProcessFlowDetailServiceImpl> implements ClaimFlowDetailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BankDetailsDaoImpl.class);
    private final ClaimProcessFlowDetailDao claimProcessFlowDetailDao = new ClaimProcessFlowDetailDaoImpl();

    @Override
    public boolean SaveClaimFlowTask(String task, UserDto user) throws Exception {

        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            return claimProcessFlowDetailDao.save(connection, task, user);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            commitTransaction(connection);
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimProcessFlowDto> getAllClaimFlowDetails() throws Exception {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            return claimProcessFlowDetailDao.getAll(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            commitTransaction(connection);
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void saveClaimLogs(Integer claimNo, UserDto user) throws Exception {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            saveClaimsLogs(connection, claimNo, user, "Investigation Opened !", "User opened the investigation section.");

        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception(e.getMessage());
        } finally {
            commitTransaction(connection);
            releaseJDBCConnection(connection);
        }
    }
}
