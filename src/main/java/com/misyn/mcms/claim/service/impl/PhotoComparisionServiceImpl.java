package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.CallCenterDao;
import com.misyn.mcms.claim.dao.ClaimDocumentTypeDao;
import com.misyn.mcms.claim.dao.ClaimImageDao;
import com.misyn.mcms.claim.dao.InspectionDetailsDao;
import com.misyn.mcms.claim.dao.impl.CallCenterDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimDocumentTypeDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimImageDaoImpl;
import com.misyn.mcms.claim.dao.impl.InspectionDetailsDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.PhotoComparisionService;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class PhotoComparisionServiceImpl extends AbstractBaseService<PhotoComparisionServiceImpl> implements PhotoComparisionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PhotoComparisionServiceImpl.class);

    private final InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private final ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private final CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private final ClaimImageDao claimImageDao = new ClaimImageDaoImpl();

    @Override
    public PhotoComparisionDto getPhotoComparisionDto(Integer claimNo, String policyNumber, String vehicleNumber, String comparisionType, String inspectionJobNo, Integer documentTypeId) {
        PhotoComparisionDto photoComparisionDto = new PhotoComparisionDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<PopupItemDto> claimNumberList;

            if (AppConstant.NOT_AVAILABLE.equalsIgnoreCase(vehicleNumber) || AppConstant.EMPTY_STRING.equalsIgnoreCase(vehicleNumber)){
                claimNumberList = callCenterDao.getClaimNumberList(connection, policyNumber, AppConstant.EMPTY_STRING);
            } else {
                claimNumberList = callCenterDao.getClaimNumberList(connection, policyNumber, vehicleNumber);
            }

            List<ClaimInspectionTypeDto> claimInspectionTypeList = inspectionDetailsDao.getClaimInspectionTypeDtoList(connection, claimNo);
            List<ClaimDocumentTypeDto> claimDocumentTypeDtoList = claimDocumentTypeDao.searchExcludeDocumentType(connection, AppConstant.CLAIM_IMAGE_DOCUMENT_TYPE_ID);
            List<ClaimDocumentDto> claimDocumentDtoList;
            List<ClaimImageDto> claimImageDtoList;

            if (!inspectionJobNo.equalsIgnoreCase(AppConstant.ZERO)) {
                claimDocumentDtoList = claimDocumentDao.findAllByClaimNoAndInspectionJobNoAndDocumentTypeId(connection, claimNo, inspectionJobNo, documentTypeId);
            } else {
                claimDocumentDtoList = claimDocumentDao.findAllByClaimNoAndDocumentTypeId(connection, claimNo, documentTypeId);
            }

            if (AppConstant.PHOTO.equalsIgnoreCase(comparisionType)) {
                if (!inspectionJobNo.equalsIgnoreCase(AppConstant.ZERO)) {
                    claimImageDtoList = claimImageDao.findAllClaimImageDtoByClaimNoAndInspectionType(connection, claimNo, inspectionJobNo);
                } else {
                    claimImageDtoList = claimImageDao.findAllClaimImageDtoByClaimNo(connection, claimNo);
                }
                claimDocumentTypeDtoList.clear();
                claimDocumentTypeDtoList.add(claimDocumentTypeDao.searchByDocumentTypeId(connection, AppConstant.CLAIM_IMAGE_DOCUMENT_TYPE_ID));
                photoComparisionDto.setClaimImageDtoList(claimImageDtoList);
            }

            photoComparisionDto.setClaimList(claimNumberList);
            photoComparisionDto.setClaimDocumentTypeList(claimDocumentTypeDtoList);
            photoComparisionDto.setClaimInspectionTypeList(claimInspectionTypeList);
            photoComparisionDto.setClaimDocumentDtoList(claimDocumentDtoList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return photoComparisionDto;
    }


}
