package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.LoggerTrailDao;
import com.misyn.mcms.claim.dao.impl.LoggerTrailDaoImpl;
import com.misyn.mcms.claim.dto.ClaimLogTrailDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.LoggerTrailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class LoggerTrailServiceImpl extends AbstractBaseService<LoggerTrailServiceImpl> implements LoggerTrailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoggerTrailServiceImpl.class);

    private LoggerTrailDao loggerTrailDao = new LoggerTrailDaoImpl();

    @Override
    public List<ClaimLogTrailDto> getLoggerTrailForForm(Integer formId, Integer claimNo) throws Exception {
        List<ClaimLogTrailDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = loggerTrailDao.getLoggerTrailForForm(connection, formId, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }
}
