package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.FormFieldDao;
import com.misyn.mcms.claim.dao.impl.FormFieldDaoImpl;
import com.misyn.mcms.claim.dto.FormFieldDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.FormFieldService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class FormFieldServiceImpl extends AbstractBaseService<FormFieldServiceImpl> implements FormFieldService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FormFieldServiceImpl.class);

    FormFieldDao formFieldDao = new FormFieldDaoImpl();

    @Override
    public FormFieldDto insert(FormFieldDto formFieldDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto update(FormFieldDto formFieldDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto delete(FormFieldDto formFieldDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto updateAuthPending(FormFieldDto formFieldDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto deleteAuthPending(FormFieldDto formFieldDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<FormFieldDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<FormFieldDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public List<FormFieldDto> getFormRelatedFields(Integer formId) throws Exception {
        List<FormFieldDto> list = null;
        Connection connection = null;
        try {
            FormFieldDto formFieldDto = new FormFieldDto();
            formFieldDto.setFormNameId(formId);
            formFieldDto.setFieldRequired("Y");
            connection = getJDBCConnection();
            list = formFieldDao.getFormRelatedFields(connection, formFieldDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public FormFieldDto getDtoFieldRelatedField(Integer formId, String dtoFieldName) throws Exception {
        FormFieldDto dto = null;
        Connection connection = null;
        try {
            FormFieldDto formFieldDto = new FormFieldDto();
            formFieldDto.setFormNameId(formId);
            formFieldDto.setDtoFieldName(dtoFieldName);
            formFieldDto.setFieldRequired("Y");
            connection = getJDBCConnection();
            dto = formFieldDao.getDtoFieldRelatedField(connection, formFieldDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dto;
    }
}
