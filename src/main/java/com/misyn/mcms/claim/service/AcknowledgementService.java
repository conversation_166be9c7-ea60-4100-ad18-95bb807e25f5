package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;

import java.util.List;

public interface AcknowledgementService {
    AcknowledgementSummaryDto insert(AcknowledgementSummaryDto acknowledgementSummaryDto, String documentsIds, UserDto user) throws Exception;

    DataGridDto getClaimHandlerDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    List<ClaimWiseDocumentDto> searchDocumentsByClaimNo(Integer claimNo) throws Exception;

    AcknowledgementSummaryDto getAcknowledgementSummarybyId(Integer acknowledgementId) throws Exception;

    List<AcknowledgementSummaryDto> getPreviousAcknowledgementList(Integer claimNo);
}
