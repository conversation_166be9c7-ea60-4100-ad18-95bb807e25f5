package com.misyn.mcms.claim.service.impl;

import com.google.gson.Gson;
import com.misyn.mcms.claim.dao.ClaimCalculationSheetPayeeDao;
import com.misyn.mcms.claim.dao.ClaimPaymentDispatchDao;
import com.misyn.mcms.claim.dao.FinanceReasonUpdateDao;
import com.misyn.mcms.claim.dao.impl.ClaimCalculationSheetPayeeDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimPaymentDispatchDaoImpl;
import com.misyn.mcms.claim.dao.impl.FinanceReasonUpdateDaoImpl;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeDto;
import com.misyn.mcms.claim.dto.ClaimPaymentDispatchDto;
import com.misyn.mcms.claim.dto.FinanceReasonUpdateDetailsDto;
import com.misyn.mcms.claim.dto.VoucherDetailsDto;
import com.misyn.mcms.claim.dto.list.VoucherDetailsDtoList;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.FinanceVoucherDetailsService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.ListBoxItem;
import com.misyn.mcms.utility.Parameters;
import jakarta.ws.rs.client.ClientBuilder;
import jakarta.ws.rs.client.InvocationCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
public class FinanceVoucherDetailsServiceImpl extends AbstractBaseService<FinanceVoucherDetailsService> implements FinanceVoucherDetailsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FinanceVoucherDetailsServiceImpl.class);
    private String REST_SERVICE_URL = Parameters.getSyncAppUrl().concat("policy/");
    private FinanceReasonUpdateDao financeReasonUpdateDao = new FinanceReasonUpdateDaoImpl();
    private ClaimPaymentDispatchDao claimPaymentDispatchDao = new ClaimPaymentDispatchDaoImpl();
    private ClaimCalculationSheetPayeeDao claimCalculationSheetPayeeDao = new ClaimCalculationSheetPayeeDaoImpl();

    @Override
    public List<VoucherDetailsDto> getVoucherDetailsDtoList(String claimNo, String policyChannelType) {
        List<VoucherDetailsDto> resultList = new ArrayList<>();
        VoucherDetailsDtoList voucherDetailsDtoList = new VoucherDetailsDtoList();
        Gson gson = new Gson();
        Connection connection = null;
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("voucher-detail/")
                    .path(claimNo)
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                            LOGGER.info(response);
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                            throwable.printStackTrace();
                        }
                    });

            connection = getJDBCConnection();
            voucherDetailsDtoList = gson.fromJson(entityFuture.get(), VoucherDetailsDtoList.class);
            resultList = voucherDetailsDtoList.getResults();
            for (VoucherDetailsDto voucherDetailsDto : resultList) {
                ClaimPaymentDispatchDto voucherDetails = claimPaymentDispatchDao.getVoucherDetails(connection, voucherDetailsDto.getVoucherNo());
                if (voucherDetails != null) {
                    voucherDetailsDto.setClaimPaymentDispatchDto(voucherDetails);
                } else {
                    ClaimCalculationSheetPayeeDto payeeDetailsByVoucherNo = claimCalculationSheetPayeeDao.findPayeeDetailsByVoucherNo(connection, voucherDetailsDto.getVoucherNo());
                    voucherDetailsDto.getClaimPaymentDispatchDto().setChequeDispatchStatus(AppConstant.NO);
                    voucherDetailsDto.getClaimPaymentDispatchDto().setChequeNo(AppConstant.STRING_EMPTY);
                    voucherDetailsDto.getClaimPaymentDispatchDto().setClaimNo(Integer.valueOf(claimNo));
                    voucherDetailsDto.setVoucherNo(payeeDetailsByVoucherNo.getVoucherNo());
                    voucherDetailsDto.setPayeeName(payeeDetailsByVoucherNo.getPayeeDesc());
                    voucherDetailsDto.setBankCode(payeeDetailsByVoucherNo.getBankCode());
                    voucherDetailsDto.setBranchCode(payeeDetailsByVoucherNo.getBranch());
                    voucherDetailsDto.getClaimPaymentDispatchDto().setBranchDetailDto(payeeDetailsByVoucherNo.getBranchDetailDto());
                    voucherDetailsDto.getClaimPaymentDispatchDto().setPayeeId(payeeDetailsByVoucherNo.getCalSheetPayeeId());
                }
            }
            setFinanceReasonType(resultList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return resultList;
    }

    @Override
    public List<VoucherDetailsDto> getVoucherDetailsDtoListByClaimNo(String claimNo, String policyChannelType) {
        List<VoucherDetailsDto> resultList = new ArrayList<>();
        VoucherDetailsDtoList voucherDetailsDtoList;
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("voucher-detail/claim-no/")
                    .path(claimNo)
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                            throwable.printStackTrace();
                        }
                    });

            voucherDetailsDtoList = gson.fromJson(entityFuture.get(), VoucherDetailsDtoList.class);
            resultList = voucherDetailsDtoList.getResults();
            setFinanceReasonType(resultList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return resultList;
    }

    @Override
    public List<VoucherDetailsDto> getVoucherDetailsDtoListByVehicleNo(String vehicleNo, String policyChannelType) {
        List<VoucherDetailsDto> resultList = new ArrayList<>();
        VoucherDetailsDtoList voucherDetailsDtoList;
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("voucher-detail/vehicle-no/")
                    .path(vehicleNo)
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                            throwable.printStackTrace();
                        }
                    });

            voucherDetailsDtoList = gson.fromJson(entityFuture.get(), VoucherDetailsDtoList.class);
            resultList = voucherDetailsDtoList.getResults();
            setFinanceReasonType(resultList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return resultList;
    }

    @Override
    public List<VoucherDetailsDto> getVoucherDetailsDtoListByVoucherNo(String voucherNo, String policyChannelType) {
        List<VoucherDetailsDto> resultList = new ArrayList<>();
        VoucherDetailsDtoList voucherDetailsDtoList;
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("voucher-detail/voucher-no/")
                    .path(voucherNo)
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                            throwable.printStackTrace();
                        }
                    });

            voucherDetailsDtoList = gson.fromJson(entityFuture.get(), VoucherDetailsDtoList.class);
            resultList = voucherDetailsDtoList.getResults();
            setFinanceReasonType(resultList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return resultList;
    }

    @Override
    public List<VoucherDetailsDto> getVoucherDetailsDtoListByFromDateAndToDate(String fromDate, String toDate, String policyChannelType) {
        List<VoucherDetailsDto> resultList = new ArrayList<>();
        VoucherDetailsDtoList voucherDetailsDtoList;
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("voucher-detail/")
                    .path(fromDate.concat("/").concat(toDate))
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                            throwable.printStackTrace();
                        }
                    });

            voucherDetailsDtoList = gson.fromJson(entityFuture.get(), VoucherDetailsDtoList.class);
            resultList = voucherDetailsDtoList.getResults();
            setFinanceReasonType(resultList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return resultList;
    }

    @Override
    public List<VoucherDetailsDto> getVoucherDetailsDtoListByFromDateAndToDateAndStatus(String fromDate, String toDate, String status, String policyChannelType) {
        List<VoucherDetailsDto> resultList = new ArrayList<>();
        VoucherDetailsDtoList voucherDetailsDtoList;
        Gson gson = new Gson();
        try {
            final Future<String> entityFuture = ClientBuilder.newClient().target(REST_SERVICE_URL)
                    .path(policyChannelType + "/")
                    .path("voucher-detail/")
                    .path(fromDate.concat("/").concat(toDate).concat("/").concat(status))
                    .request().async().get(new InvocationCallback<String>() {
                        @Override
                        public void completed(String response) {
                        }

                        @Override
                        public void failed(Throwable throwable) {
                            LOGGER.error("Invocation failed.");
                            throwable.printStackTrace();
                        }
                    });

            voucherDetailsDtoList = gson.fromJson(entityFuture.get(), VoucherDetailsDtoList.class);
            resultList = voucherDetailsDtoList.getResults();
            setFinanceReasonType(resultList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return resultList;
    }

    @Override
    public List<ListBoxItem> getFinanceReasonList() {
        Connection connection = null;
        List<ListBoxItem> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        String strSQL;
        try {
            connection = getJDBCConnection();
            strSQL = "SELECT * FROM finance_reason_type ORDER BY finance_reason_type";
            ps = connection.prepareStatement(strSQL);
            rs = ps.executeQuery();
            while (rs.next()) {
                list.add(new ListBoxItem(rs.getString("finance_reason_type_id"), rs.getString("finance_reason_type")));
            }
            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public int saveFinanceReason(FinanceReasonUpdateDetailsDto financeReasonUpdateDetailsDto) throws Exception {
        Connection connection = null;
        int result;
        try {
            connection = getJDBCConnection();
            FinanceReasonUpdateDetailsDto financeReasonUpdateDetailsDto1 = financeReasonUpdateDao
                    .getFinanceReasonUpdateDetailsDto(connection, financeReasonUpdateDetailsDto.getVoucherNo());
            if (null == financeReasonUpdateDetailsDto1) {
                result = financeReasonUpdateDao.insert(connection, financeReasonUpdateDetailsDto);
            } else {
                result = financeReasonUpdateDao.update(connection, financeReasonUpdateDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return result;
    }

    @Override
    public String getPolicyChannelType(Integer claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return getPolicyChannelTypeByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public String getPolicyChannelTypeByVehicleNo(String vehicleNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return getPolicyChannelTypeByVehicleNo(connection, vehicleNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void setFinanceReasonType(List<VoucherDetailsDto> list) {
        Connection connection = null;
        FinanceReasonUpdateDetailsDto financeReasonUpdateDetailsDto;
        try {
            connection = getJDBCConnection();
            for (VoucherDetailsDto voucherDetailsDto :
                    list) {
                financeReasonUpdateDetailsDto =
                        financeReasonUpdateDao.getFinanceReasonUpdateDetailsDto(connection, voucherDetailsDto.getVoucherNo());
                if (null != financeReasonUpdateDetailsDto) {
                    voucherDetailsDto.setFinanceReasonTypeId(financeReasonUpdateDetailsDto.getReasonId());
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }

    }
}
