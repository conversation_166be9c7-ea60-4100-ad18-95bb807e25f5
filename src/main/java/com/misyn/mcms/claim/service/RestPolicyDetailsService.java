package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;

import java.util.List;

public interface RestPolicyDetailsService {
    List<CoverDto> getCoverDtoList(PolicyDto policyDto, String policyChannelType);

    List<ExcessDto> getExcessDtoList(PolicyDto policyDto, String policyChannelType);

    List<PaidDetailsDto> getPaidDetailsDtoList(PolicyDto policyDto, String policyChannelType);

    List<PolicyMemoDto> getPolicyMemoDtoList(PolicyDto policyDto, String policyChannelType);

    PremiumBreakupFormDto getPremiumBreakupFormDto(PolicyDto policyDto, String policyChannelType);

    List<SellingAgentDetailsDto> getSellingAgentDetailsDtoList(PolicyDto policyDto, String policyChannelType);

    List<EndorsementHistoryDto> getEndorsementHistoryDtoList(PolicyDto policyDto, String policyChannelType);

    List<BillingInfoDto> getBillingInfoDtoList(PolicyDto policyDto, String policyChannelType);

    List<LearnerDriverDetailsDto> getLearnerDriverDetailsDtoList(PolicyDto policyDto, String policyChannelType);

    PolicySellingAgentDetailsDto getPolicySellingAgentDetailsDto(PolicyDto policyDto, String policyChannelType);

    IntroducerDto getIntroducerDetailsDto(PolicyDto policyDto, String policyChannelType);

    List<CweDetailDto> getCweDetail(PolicyDto policyDto, String policyChannelType);

    List<TradePlateDetailDto> getTradePlateDetail(String policyNumber, String policyChannelType);

    TrailerDetailDto getTrailerDetail(String policyNumber, String policyChannelType);

    IntroducerDto getIntroducerDetail(PolicyDto policyDto, String policyChannelType);

    LargeClaimDto getLargeClaimDetailsDtoList(PolicyDto policyDto, String policyChannelType);

    CoInsOrFacDetailDto getCoInsOrFacDetail(PolicyDto policyDto, String policyChannelType);

    List<PolicyBenefitDetailsDto> getBenefitCategoryDtoList(PolicyDto policyDto, String policyChannelType);

    List<CweDetailDto> getCweCategoryDtoList(PolicyDto policyDto, String policyChannelType);

    List<PolicyOthChargesDetailsDto> getOthChargesCategoryDtoList(PolicyDto policyDto, String policyChannelType);

    List<PolicySrcctcDetailsDto> getSrcctcCategoryDtoList(PolicyDto policyDto, String policyChannelType);


    List<PolicyMemoDto> getMemoCategoryDtoList(PolicyDto policyDto, String policyChannelType);
}
