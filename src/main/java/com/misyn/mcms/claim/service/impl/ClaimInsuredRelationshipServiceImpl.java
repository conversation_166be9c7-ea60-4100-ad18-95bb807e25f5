package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dto.SalutationDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.ClaimInsuredRelationshipService;

import java.util.List;
public class ClaimInsuredRelationshipServiceImpl implements ClaimInsuredRelationshipService {
    @Override
    public SalutationDto insert(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto update(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto delete(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto updateAuthPending(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto deleteAuthPending(SalutationDto salutationDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SalutationDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public SalutationDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public SalutationDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<SalutationDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<SalutationDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }
}
