package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.enums.NotificationPriority;
import com.misyn.mcms.claim.enums.PaymentStatus;
import com.misyn.mcms.claim.exception.ErrorMsgException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.AssessorPaymentDetailsService;
import com.misyn.mcms.claim.service.InspectionDetailsService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
public class AssessorPaymentDetailsServiceImpl extends AbstractBaseService<AssessorPaymentDetailsServiceImpl> implements AssessorPaymentDetailsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorPaymentDetailsServiceImpl.class);
    private AssessorPaymentDetailsDao assessorPaymentDetailsDao = new AssessorPaymentDetailsDaoImpl();
    private InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private NotificationDao notificationDao = new NotificationDaoImpl();
    private AssessorAllocationDao assessorAllocationDao = new AssessorAllocationDaoImpl();
    private InvestigationDetailsDao investigationDetailsDao = new InvestigationDetailsDaoImpl();
    private InspectionDao inspectionDao = new InspectionDaoImpl();
    private CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private McmsClaimOfflinePaymentDao claimOfflinePaymentDao = new McmsClaimOfflinePaymentDaoImpl();
    private LoggerTrailDao loggerTrailDao = new LoggerTrailDaoImpl();
    private SequenceKeyTableDao sequenceKeyTableDao = new SequenceKeyTableDaoImpl();
    private InspectionDetailsService inspectionDetailsService = new InspectionDetailsServiceImpl();
    private AssessorDao assessorDao = new AssessorDaoImpl();
    private McmsClaimOfflineReserveAssessorDao offlineReserveAssessorDao = new McmsClaimOfflineReserveAssessorDaoImpl();
    private AssessorPaymentDeductionDetailDao assessorPaymentDeductionDetailDao = new AssessorPaymentDeductionDetailDaoImpl();
    private ApproveAssessorPaymentClaimWiseDao approveAssessorPaymentClaimWiseDao = new ApproveAssessorPaymentClaimWiseDaoImpl();
    private PolicyDao policyDao = new PolicyDaoImpl();

    @Override
    public List<AssessorPaymentDetailsDto> searchAll(String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, boolean isGridSearch, String claimType, String policyChannelType) {
        Connection connection = null;
        List<AssessorPaymentDetailsDto> list = null;
        // List<AssessorPaymentDetailsDto> dtoList=null;
        try {
            connection = getJDBCConnection();
            list = assessorPaymentDetailsDao.searchAll(connection, paymentStaus, status, name, fromDate, toDate, vehicleNumber, claimNumber, jobNumber, inspectionType, rteCode, isGridSearch, claimType, policyChannelType);
            for (AssessorPaymentDetailsDto assessorPaymentDetailsDto : list) {
                if ("A".equals(assessorPaymentDetailsDto.getType())) {
                    InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, assessorPaymentDetailsDto.getKeyId());
                    InspectionDto inspectionDto = inspectionDao.searchMaster(connection, inspectionDetailsDto.getInspectionDto().getInspectionId());
                    if (null != inspectionDto) {
                        assessorPaymentDetailsDto.setInspectionType(inspectionDto.getInspectionValue());
                    }
                }

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return list;
    }

    @Override
    public DataGridDto getPaymentDataGridDto(Map<String, Object> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        Connection connection = null;
        DataGridDto dataGridDto = new DataGridDto();
        try {
            connection = getJDBCConnection();
            dataGridDto = assessorPaymentDetailsDao.getAssessorPaymentDetailsDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
            for (Object dataItem : (List<?>) dataGridDto.getData()) {
                if (dataItem instanceof AssessorPaymentDetailsGridDto) {
                    AssessorPaymentDetailsGridDto assessorPaymentDetailsGridDto = (AssessorPaymentDetailsGridDto) dataItem;
                    if ("A".equals(assessorPaymentDetailsGridDto.getType())) {
                        InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, assessorPaymentDetailsGridDto.getRefNumber());
                        InspectionDto inspectionDto = inspectionDao.searchMaster(connection, inspectionDetailsDto.getInspectionDto().getInspectionId());
                        if (null != inspectionDto) {
                            assessorPaymentDetailsGridDto.setInspectionType(null == inspectionDto.getInspectionValue() ? AppConstant.EMPTY_STRING : inspectionDto.getInspectionValue());
                            assessorPaymentDetailsGridDto.setDateOfInspection(null == inspectionDetailsDto.getInspectDateTime() ? AppConstant.EMPTY_STRING : inspectionDetailsDto.getInspectDateTime());
                        }
                    }
                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return dataGridDto;
    }

    @Override
    public boolean updateStausByIdAndStatus(String ids, UserDto user, String from_date, String to_date) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<Integer> selectedList = getSelectedList(ids);
            int index = 0;
            Integer maxValue = getMaxKeyValue();
            for (Integer id : selectedList) {
                AssessorPaymentDetailsDto assessorPaymentDetailsDto = assessorPaymentDetailsDao.searchMaster(connection, id);
                if (PaymentStatus.Pending.name().equalsIgnoreCase(assessorPaymentDetailsDto.getPaymentStatus().name())) {

                    if (assessorPaymentDetailsDto.getTotalFee().signum() != 0) {
                        assessorPaymentDetailsDao.updateStausByIdAndStatus(connection, id, "A", user.getUserId(), Utility.sysDateTime(), maxValue, from_date, to_date);
                        if (null != assessorPaymentDetailsDto && "I".equalsIgnoreCase(assessorPaymentDetailsDto.getType())) {
                            InvestigationDetailsDto investigationDetailsDto = investigationDetailsDao.searchMaster(connection, assessorPaymentDetailsDto.getKeyId());
                            if (null != investigationDetailsDto) {
                                investigationDetailsDto.setInvestTxnNo(assessorPaymentDetailsDto.getKeyId());
                                investigationDetailsDto.setPaymentStatus("A");
                                investigationDetailsDao.updateInvestigationPaymentStatus(connection, investigationDetailsDto);
                                saveClaimsLogs(connection, investigationDetailsDto.getClaimNo(), user, "Investigation Payment Voucher Generated", "Investigation Payment Voucher Generated - "
                                        .concat(investigationDetailsDto.getInvestJobNo()).concat(" ,Total Amount :").concat(investigationDetailsDto.getTotalFee().toString()));
                            }

                        } else if (null != assessorPaymentDetailsDto && AppConstant.ASSESSOR_PAYMENT.equalsIgnoreCase(assessorPaymentDetailsDto.getType())) {
                            saveClaimsLogs(connection, assessorPaymentDetailsDto.getClaimNo(), user, "Assessor Payment Approved", "Assessor Payment Approved ");
                        }
                        ClaimsDto claimsDto = callCenterDao.searchMaster(connection, assessorPaymentDetailsDto.getClaimNo());
                        offlineReserveAssessorDao.updateReserveAssessorOvClaimNo(connection, assessorPaymentDetailsDto.getClaimNo(), claimsDto.getIsfClaimNo());
                        offlineReserveAssessorDao.updateReserveAssessorIsfNotUpdateList(connection, assessorPaymentDetailsDto.getClaimNo());
                        saveClaimOfflinePayment(connection, id, claimsDto);
                    }
                    index++;
                }
            }

            if (selectedList.size() == index) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    private Integer getMaxKeyValue() {
        Integer maxValue = 0;
        Connection connection = null;
        SequenceTableDto sequenceTableDto = new SequenceTableDto();

        try {
            connection = getJDBCConnection();
            sequenceTableDto = sequenceKeyTableDao.getSelectedKeyValue(connection, AppConstant.ASSESSOR_PAYMENT_APPROVE_MST_SEQUENCE_TABLE_ID);
            sequenceKeyTableDao.updateMaxKeyByTableId(connection, AppConstant.ASSESSOR_PAYMENT_APPROVE_MST_SEQUENCE_TABLE_ID);
            maxValue = sequenceTableDto.getMaxKeyValue();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return maxValue;
    }

    @Override
    public void updateRejectedStausByIdAndStatus(String ids, String remark, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            Integer id = Integer.parseInt(ids);
            Integer claimStatus = ClaimStatus.SUBMITTED.getClaimStatus();
            assessorPaymentDetailsDao.updateStausByIdAndStatus(connection, id, "R", remark, user.getUserId(), Utility.sysDateTime());

            AssessorPaymentDetailsDto assessorPaymentDetailsDto = assessorPaymentDetailsDao.searchMaster(connection, id);
            if (null != assessorPaymentDetailsDto && AppConstant.ASSESSOR_PAYMENT.equals(assessorPaymentDetailsDto.getType())) {
                InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, assessorPaymentDetailsDto.getKeyId());
                if (!AppConstant.STRING_PENDING.equalsIgnoreCase(inspectionDetailsDto.getAssessorFeeAuthStatus())) {
                    if (inspectionDetailsDto.getInspectionDetailsAuthStatus().equals(AppConstant.APPROVE)) {
                        claimStatus = ClaimStatus.APPROVED.getClaimStatus();
                    }
                    inspectionDetailsDao.updateAssessorFeeAuthByRefNo(connection, "P", inspectionDetailsDto.getAssessorFeeAuthUserId(), inspectionDetailsDto.getAssessorFeeAuthDatetime(), assessorPaymentDetailsDto.getKeyId());
                    inspectionDetailsDao.updateRecordStatusByRefNo(connection, claimStatus, assessorPaymentDetailsDto.getKeyId());

                    inspectionDetailsDao.updateAssessorFeeAuthByRefNoMe(connection, "P", inspectionDetailsDto.getAssessorFeeAuthUserId(), inspectionDetailsDto.getAssessorFeeAuthDatetime(), assessorPaymentDetailsDto.getKeyId());
                    inspectionDetailsDao.updateRecordStatusByRefNoMe(connection, claimStatus, assessorPaymentDetailsDto.getKeyId());

                    assessorAllocationDao.updateRecordStatusByJobId(connection, claimStatus, assessorPaymentDetailsDto.getKeyId());
                    saveNotificationAssessorPayment(user, inspectionDetailsDto, connection);
                    saveClaimsLogs(connection, inspectionDetailsDto.getClaimNo(), user, "Assessor Payment Rejected", "Assessor Payment Rejected");
                    saveSpecialRemark(connection, user, inspectionDetailsDto.getClaimNo(), "Assessor Payment Rejected", remark);
                    String assessorCode = assessorAllocationDao.getAssessorCodeByRefNo(connection, assessorPaymentDetailsDto.getKeyId());
                    updateSubtractOfflineFee(connection, inspectionDetailsDto.getClaimNo(), assessorPaymentDetailsDto.getKeyId(), assessorCode, AppConstant.PAYMENT_REJECT);
                } else {
                    throw new ErrorMsgException("Assessor payment reject error", "this payment has been already rejected");
                }
            } else {
                updateRejectedStausByIdAndStatusInvestigation(ids, remark, user, assessorPaymentDetailsDto, connection);
            }
            commitTransaction(connection);
        } catch (ErrorMsgException e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new ErrorMsgException(e.getField(), e.getErrorMessage());
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        } finally {
            releaseJDBCConnection(connection);

        }

    }


    private void updateRejectedStausByIdAndStatusInvestigation(String ids, String remark, UserDto user, AssessorPaymentDetailsDto assessorPaymentDetailsDto, Connection connection) throws Exception {
        try {
            InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
            if (null != assessorPaymentDetailsDto) {
                investigationDetailsDto.setInvestTxnNo(assessorPaymentDetailsDto.getKeyId());
                investigationDetailsDto.setPaymentStatus("CAN");
                investigationDetailsDao.updateInvestigationPaymentStatus(connection, investigationDetailsDto);
                investigationDetailsDto = investigationDetailsDao.searchMaster(connection, assessorPaymentDetailsDto.getKeyId());
                saveNotificationInvestigation(user, investigationDetailsDto, connection);
                saveClaimsLogs(connection, investigationDetailsDto.getClaimNo(), user, "Investigation Payment Rejected", "Investigation Payment Rejected");
                saveSpecialRemark(connection, user, investigationDetailsDto.getClaimNo(), "Investigation Payment Rejected", remark);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    @Override
    public BigDecimal getTotalAmount(String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, String claimType, String policyChannelType) {
        Connection connection = null;
        BigDecimal total = BigDecimal.ZERO;
        try {
            connection = getJDBCConnection();
            total = assessorPaymentDetailsDao.getTotalAmount(connection, paymentStaus, status, name, fromDate, toDate, vehicleNumber, claimNumber, jobNumber, inspectionType, rteCode, claimType, policyChannelType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return null == total ? BigDecimal.ZERO : total;
    }

    @Override
    public String getPaymentStatus(Integer keyId) {
        Connection connection = null;
        String paymentStatus = null;
        try {
            connection = getJDBCConnection();
            paymentStatus = assessorPaymentDetailsDao.getPaymentStatus(connection, keyId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return paymentStatus;
    }

    @Override
    public void updateHoldPayment(Integer keyId, UserDto user, Integer claimNo, String assessorCode) throws Exception {
        Connection connection = null;
        ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
        try {
            connection = getJDBCConnection();
            InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchByRefNo(connection, keyId);

            assessorPaymentDetailsDao.updateHoldPayment(connection, keyId);
            inspectionDetailsDao.updateAssFeeAprStatus(connection, keyId);
            inspectionDetailsDao.updateAssFeeAprStatusMe(connection, keyId);
            claimLogTrailDto.setClaimNo(claimNo);
            claimLogTrailDto.setJobRefNo(keyId);
            claimLogTrailDto.setFormNameId(5);
            claimLogTrailDto.setFieldName("Payment Hold");
            claimLogTrailDto.setFieldValue("Assessor Payment Hold");
            claimLogTrailDto.setVersionId(1);
            claimLogTrailDto.setUserId(user.getUserId());
            loggerTrailDao.insert(connection, claimLogTrailDto);

            this.updateSubtractOfflineFee(connection, claimNo, keyId, assessorCode, AppConstant.PAYMENT_HOLD);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    public void updateSubtractOfflineFee(Connection connection, Integer claimNo, Integer keyId, String assessorCode, String action) throws Exception {
        try {
            BigDecimal totalOtherAmount = BigDecimal.ZERO;
            BigDecimal totalScheduleAmount = BigDecimal.ZERO;
            McmsClaimOfflineReserveAssessorDto offlineReserveAssessorDto = new McmsClaimOfflineReserveAssessorDto();
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, claimNo);

            ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto = approveAssessorPaymentClaimWiseDao.getDetailByAssessorCodeAndClaimNo(connection, claimNo, assessorCode);


            AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto = assessorPaymentDeductionDetailDao.searchMaster(connection, keyId);

            if (null != assessorPaymentDeductionDetailDto && null != approveAssessorPaymentClaimWiseDto) {
                totalOtherAmount = approveAssessorPaymentClaimWiseDto.getTotalOtherAmount().subtract(assessorPaymentDeductionDetailDto.getOtherAmount());
                totalScheduleAmount = approveAssessorPaymentClaimWiseDto.getTotalScheduleAmount().subtract(assessorPaymentDeductionDetailDto.getScheduleAmount());
            }

            if (null != claimsDto) {
                offlineReserveAssessorDto.setOvClaimNo(claimsDto.getIsfClaimNo());
                offlineReserveAssessorDto.setOvIdentificationNo(assessorCode);
                offlineReserveAssessorDto.setOnOtherAmt(totalOtherAmount);
                offlineReserveAssessorDto.setOnScheduleAmt(totalScheduleAmount);
                offlineReserveAssessorDto.setOvPanelCategory(AppConstant.PANEL_CATEGORY);
                offlineReserveAssessorDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                offlineReserveAssessorDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
                offlineReserveAssessorDto.setOvIdentificationCode(AppConstant.IDENTIFICATION_CODE);
                offlineReserveAssessorDto.setDInsertDateTime(Utility.sysDateTime());
                offlineReserveAssessorDto.setNRetryAttempt(AppConstant.ZERO_INT);
                offlineReserveAssessorDto.setDIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                offlineReserveAssessorDto.setVIsfsUpdateStat(AppConstant.NO);
                offlineReserveAssessorDto.setClaimNo(claimNo);
                offlineReserveAssessorDto.setPolicyChannelType(claimsDto.getPolicyChannelType());
                offlineReserveAssessorDao.insertMaster(connection, offlineReserveAssessorDto);

                if (null != approveAssessorPaymentClaimWiseDto) {
                    approveAssessorPaymentClaimWiseDto.setTotalOtherAmount(totalOtherAmount);
                    approveAssessorPaymentClaimWiseDto.setTotalScheduleAmount(totalScheduleAmount);
                    approveAssessorPaymentClaimWiseDao.updateByclaimNoAndAssessorCode(connection, approveAssessorPaymentClaimWiseDto);

                    approveAssessorPaymentClaimWiseDto.setRefNo(keyId);
                    approveAssessorPaymentClaimWiseDto.setAction(action);
                    approveAssessorPaymentClaimWiseDto.setInputDatetime(Utility.sysDateTime());
                    approveAssessorPaymentClaimWiseDao.insertHistory(connection, approveAssessorPaymentClaimWiseDto);
                }


            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }

    }


    @Override
    public List<UserDto> getAssessorListByReportingRte(String rteName) {
        Connection connection = null;
        List<UserDto> list = null;
        // List<AssessorPaymentDetailsDto> dtoList=null;
        try {
            connection = getJDBCConnection();
            list = assessorPaymentDetailsDao.getAssessorListByReportingRte(connection, rteName);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return list;
    }


    private List<Integer> getSelectedList(String array) {
        List<Integer> list = null;
        if (!array.isEmpty()) {
            list = Arrays.stream(array.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }

    private void saveNotificationAssessorPayment(UserDto user, InspectionDetailsDto inspectionDetailsDto, Connection connection) throws Exception {
        String message = "Assessor payment rejected  ".concat(inspectionDetailsDto.getAssessorAllocationDto().getJobId());
        String url = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(inspectionDetailsDto.getRefNo()));
        saveCommonNotification(connection, inspectionDetailsDto.getClaimNo(), user.getUserId(), inspectionDetailsDto.getAssessorFeeAuthUserId(), message, url, AppConstant.DEFAULT_NOTIFICATION_COLOR_CODE, inspectionDetailsDto.getRefNo(), NotificationPriority.LOW.getNotificationPriority());
    }

    private void saveNotificationInvestigation(UserDto user, InvestigationDetailsDto investigationDetailsDto, Connection connection) throws Exception {
        String message = "Investigation payment rejected  ".concat(Integer.toString(investigationDetailsDto.getClaimNo()));
        String url = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(investigationDetailsDto.getClaimNo()));
        saveCommonNotification(connection, investigationDetailsDto.getClaimNo(), user.getUserId(), investigationDetailsDto.getInvestCompletedUser(), message, url, AppConstant.DEFAULT_NOTIFICATION_COLOR_CODE, AppConstant.ZERO_INT, NotificationPriority.LOW.getNotificationPriority());
    }

    private void saveClaimOfflinePayment(Connection connection, Integer id, ClaimsDto claimsDto) throws Exception {
        McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto = new McmsClaimOfflinePaymentDto();
        try {
            AssessorPaymentDetailsDto assessorPaymentDetailsDto = assessorPaymentDetailsDao.searchMaster(connection, id);
            if (null != assessorPaymentDetailsDto && assessorPaymentDetailsDto.getType().equals(AppConstant.ASSESSOR_PAYMENT)) {

                AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, assessorPaymentDetailsDto.getKeyId());
//                PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());

                if (null != assessorAllocationDto && null != claimsDto) {

                    mcmsClaimOfflinePaymentDto.setReferenceId(id);
                    mcmsClaimOfflinePaymentDto.setOvPayeeType(AppConstant.PAYEE_TYPE);
                    mcmsClaimOfflinePaymentDto.setOvIdentificationNo(assessorAllocationDto.getAssessorDto().getCode());
                    mcmsClaimOfflinePaymentDto.setOvClaimNo(claimsDto.getIsfClaimNo());
                    mcmsClaimOfflinePaymentDto.setOnPaidAmount(assessorPaymentDetailsDto.getTotalFee());
                    mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                    mcmsClaimOfflinePaymentDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
                    mcmsClaimOfflinePaymentDto.setOnTotalPayable(assessorPaymentDetailsDto.getTotalFee());
                    mcmsClaimOfflinePaymentDto.setOvIdentificationCode(AppConstant.IDENTIFICATION_CODE);
                    mcmsClaimOfflinePaymentDto.setOvVoucherFlag(AppConstant.VOUCHER_FLAG_ASSESSOR);
                    mcmsClaimOfflinePaymentDto.setNRetryAttempt(AppConstant.ZERO_INT);
                    mcmsClaimOfflinePaymentDto.setdIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                    mcmsClaimOfflinePaymentDto.setVIsfsUpdateStat(AppConstant.NO);
                    mcmsClaimOfflinePaymentDto.setdInsertDateTime(Utility.sysDateTime());
                    mcmsClaimOfflinePaymentDto.setClaimNo(claimsDto.getClaimNo());
                    mcmsClaimOfflinePaymentDto.setPolicyChannelType(claimsDto.getPolicyChannelType());
                    claimOfflinePaymentDao.insertMaster(connection, mcmsClaimOfflinePaymentDto);
                }

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }


}
