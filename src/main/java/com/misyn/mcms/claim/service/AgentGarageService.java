package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.AgentGarageDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.util.List;

public interface AgentGarageService {
    AgentGarageDto insert(AgentGarageDto agentGarageDto) throws Exception;

    AgentGarageDto search(Integer id) throws Exception;

    DataGridDto getgarageDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    String validateGarageName(String spareParteName) throws Exception;
}
