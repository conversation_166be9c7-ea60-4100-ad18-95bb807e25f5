package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.CallCenterService;
import com.misyn.mcms.claim.service.EmailService;
import com.misyn.mcms.claim.service.ReminderPrintService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
public class ReminderPrintServiceImpl extends AbstractBaseService<ReminderPrintServiceImpl> implements ReminderPrintService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PhotoComparisionServiceImpl.class);
    protected CallCenterService callCenterService = new CallCenterServiceImpl();
    private ReminderPrintSummaryDao reminderPrintSummaryDao = new ReminderPrintSummaryDaoImpl();
    private ReminderPrintDetailsDao reminderPrintDetailsDao = new ReminderPrintDetailsDaoImpl();
    private ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();
    private final EmailService emailService = new EmailServiceImpl();

    private final ClaimCalculationSheetMainDao claimCalculationSheetMainDto = new ClaimCalculationSheetMainDaoImpl();

    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private EmailDao emailDao = new EmailDaoImpl();

    @Override
    public ReminderPrintSummaryDto getReminderPrintSummaryDto(Integer reminderSummaryRefId) {
        Connection connection = null;
        ReminderPrintSummaryDto reminderPrintSummaryDto = null;
        try {
            connection = getJDBCConnection();
            reminderPrintSummaryDto = reminderPrintSummaryDao.getReminderPrintSummaryDto(connection, reminderSummaryRefId);
            if (reminderPrintSummaryDto == null) {
                reminderPrintSummaryDto = new ReminderPrintSummaryDto();
            }
            Integer claimNo = reminderPrintSummaryDto.getClaimNo();
            List<ReminderPrintDetailsDto> detailsDtoList = reminderPrintDetailsDao.getReminderPrintDetailsList(connection, reminderSummaryRefId);
            if (null != detailsDtoList) {
                reminderPrintSummaryDto.setReminderPrintDetailsList(detailsDtoList);
            }

            ClaimsDto claimsDto = callCenterService.getViewAccidentClaimsDto(connection, claimNo);
            reminderPrintSummaryDto.setClaimsDto(claimsDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return reminderPrintSummaryDto;
    }

    @Override
    public List<ReminderPrintSummaryDto> getReminderSummeryListByClaimNo(Integer claimNo) {
        Connection connection = null;
        List<ReminderPrintSummaryDto> reminderPrintSummaryDto = null;
        try {
            connection = getJDBCConnection();
            reminderPrintSummaryDto = reminderPrintSummaryDao.searchAllByClaimNo(connection, claimNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return reminderPrintSummaryDto;
    }

    @Override
    public ReminderLetterFormDto getReminderLetterFormDto(Integer claimNo) {
        ReminderLetterFormDto reminderLetterFormDto = new ReminderLetterFormDto();
        Integer reminderSummaryRefId;
        Connection connection = null;
        ReminderPrintSummaryDto reminderPrintSummaryDto = null;
        try {
            connection = getJDBCConnection();
            reminderSummaryRefId = reminderPrintSummaryDao.getMaxReminderSummaryRefIdByClaimNo(connection, claimNo);
            List<ClaimWiseDocumentDto> claimWiseDocumentDtoList = claimWiseDocumentDao.searchAllByClaimNoAndIsMandatory(connection, claimNo, AppConstant.YES);
            reminderPrintSummaryDto = reminderPrintSummaryDao.getReminderPrintSummaryDto(connection, reminderSummaryRefId);
            if (reminderPrintSummaryDto == null) {
                reminderPrintSummaryDto = new ReminderPrintSummaryDto();
            }
            for (ClaimWiseDocumentDto claimWiseDocumentDto : claimWiseDocumentDtoList) {
                ReminderPrintDetailsDto reminderPrintDetailsDto = reminderPrintDetailsDao.searchByRemindSummaryIdAndDocumentTypeId(connection, reminderSummaryRefId, claimWiseDocumentDto.getDocumentTypeId());
                if (reminderPrintDetailsDto == null) {
                    reminderPrintDetailsDto = new ReminderPrintDetailsDto();
                    reminderPrintDetailsDto.setReminderSummaryRefId(reminderPrintSummaryDto.getReminderSummaryRefId());
                    reminderPrintDetailsDto.setReminderDocDisplayName(claimWiseDocumentDto.getReminderDocDisplayName());
                    reminderPrintDetailsDto.setDocTypeId(claimWiseDocumentDto.getDocumentTypeId());
                    if (reminderSummaryRefId > 0) {
                        reminderPrintDetailsDto.setCheckReminderPrint(AppConstant.NO);
                    } else {
                        reminderPrintDetailsDto.setCheckReminderPrint(AppConstant.YES);
                    }

                }
                reminderPrintSummaryDto.getReminderPrintDetailsList().add(reminderPrintDetailsDto);
            }
            List<ReminderPrintSummaryDto> reminderPrintSummaryDtoList = reminderPrintSummaryDao.searchAllByClaimNo(connection, claimNo);
            reminderLetterFormDto.setReminderPrintSummaryDto(reminderPrintSummaryDto);
            reminderLetterFormDto.setReminderPrintSummaryList(reminderPrintSummaryDtoList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return reminderLetterFormDto;
    }

    @Override
    public void generateReminderPrint(ReminderPrintSummaryDto reminderPrintSummaryDto, UserDto user, String emailVal, Integer claimNo) throws MisynJDBCException {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            if (reminderPrintSummaryDao.insertMaster(connection, reminderPrintSummaryDto) != null) {
                List<ReminderPrintDetailsDto> list = reminderPrintSummaryDto.getReminderPrintDetailsList();
                for (ReminderPrintDetailsDto reminderPrintDetailsDto : list) {
                    reminderPrintDetailsDto.setReminderSummaryRefId(reminderPrintSummaryDto.getReminderSummaryRefId());
                    reminderPrintDetailsDao.insertMaster(connection, reminderPrintDetailsDto);
                }


                // Send reminder SMS to customer
                sendReminderSmsToCustomer(reminderPrintSummaryDto, connection, user);

                ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, reminderPrintSummaryDto.getClaimNo());
                if (claimHandlerDto == null && claimHandlerDto.getClaimNo() != null) {
                    Email email = new Email();
                    MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.CLAIM_PENDING_DOCUMENTS_EMAIL);
                    email.setEmailMassege(messageContentDetails.getMessageBody());
                    ClaimsDto claimsDto = claimHandlerDto.getClaimsDto();
                    PolicyDto policyDto = claimsDto.getPolicyDto();
                    email.setToAddresses(emailVal);

                    ArrayList<String> emailParams = new ArrayList<>();

                    emailParams.add(getTotalPaidForClaim(connection,claimNo).toString());
                    emailParams.add(claimsDto.getClaimNo().toString()); // ?1%
                    emailParams.add(claimsDto.getVehicleNo()); // ?2%
                    emailParams.add(policyDto.getPolicyNumber()); // ?3%
                    emailParams.add(String.valueOf(claimsDto.getClaimReserve())); // ?4%
                    emailParams.add(claimsDto.getAccidDate()); // ?5%
                    emailParams.add(policyDto.getPolicyBranch() != null ? policyDto.getPolicyBranch() : "N/A"); // ?6%
                    emailParams.add(policyDto.getFinanceCompany() != null ? policyDto.getFinanceCompany() : "N/A"); // ?7%
                    emailParams.add(policyDto.getFinCompanyBranch() != null ? policyDto.getFinCompanyBranch() : "N/A"); // ?8%
                    emailParams.add(policyDto.getCustName()); // ?9%
                    emailParams.add(policyDto.getCustNic()); // ?10%
                    emailParams.add(claimsDto.getIsfClaimNo()); // ?11%
                    emailParams.add(policyDto.getChannel()); // ?12%
                    emailParams.add(reminderPrintSummaryDto.getGeneratedDateTime()); // ?13%

                    List<ReminderPrintDetailsDto> reminderList = reminderPrintSummaryDto.getReminderPrintDetailsList();
                    for (int i = 0; i < 6; i++) {
                        if (reminderList != null && i < reminderList.size()) {
                            ReminderPrintDetailsDto detail = reminderList.get(i);
                            String reminderDate = detail.getReminderGeneratedDate() != null ? detail.getReminderGeneratedDate() : "N/A";
                            emailParams.add(reminderDate);
                        } else {
                            emailParams.add("N/A");
                        }
                    }

                    emailParams.add(getMailSendUser());

                    email.setParameterEmail(emailParams);
                    email.setSubject(messageContentDetails.getSubject());

                    // Log the email to the email_log table
                  emailService.sendEmail(connection,email);

                    Integer Id = reminderPrintSummaryDao.getMaxReminderIdByClaimNo(connection, reminderPrintSummaryDto.getClaimNo());
                    Integer nId = Id + 1;
                    saveClaimsLogs(connection, reminderPrintSummaryDto.getClaimNo(), user, "Generate Reminder", "Update Generate Reminder ".concat(String.valueOf(nId)));
                    saveClaimProcessFlow(connection, reminderPrintSummaryDto.getClaimNo(), 0, "Update Generate Reminder", user.getUserId(), Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.NO);

                }
            } else {
                throw new MisynJDBCException("Can not be saved ");

            }
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new MisynJDBCException("Can not be saved ");
        } finally {
            releaseJDBCConnection(connection);
        }
    }


    public BigDecimal getTotalPaidForClaim(Connection connection, Integer claimNo) throws Exception {
        try {
            List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtos = claimCalculationSheetMainDto.searchByClaimNo(connection, claimNo);
            BigDecimal total = BigDecimal.ZERO;
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : claimCalculationSheetMainDtos) {
                if (67 == claimCalculationSheetMainDto.getStatus() || 65 == claimCalculationSheetMainDto.getStatus() || 70 == claimCalculationSheetMainDto.getStatus()) {
                    total = total.add(claimCalculationSheetMainDto.getPayableAmount());
                }
            }
            return total;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }
// Add new sms Part
    private void sendReminderSmsToCustomer(ReminderPrintSummaryDto reminderPrintSummaryDto, Connection connection, UserDto user) throws Exception {
        ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, reminderPrintSummaryDto.getClaimNo());
        if (claimHandlerDto != null && claimHandlerDto.getClaimsDto() != null) {
            ClaimsDto claimsDto = claimHandlerDto.getClaimsDto();
            PolicyDto policyDto = claimsDto.getPolicyDto();

            List<String> smsParameterList = new ArrayList<>();
            smsParameterList.add(claimsDto.getVehicleNo()); // ?1% - Vehicle No
            smsParameterList.add(policyDto.getCustName()); // ?2% - Insured Name
            smsParameterList.add(claimsDto.getAccidDate()); // ?3% - Date of Accident
            smsParameterList.add(getTotalPaidForClaim(connection, reminderPrintSummaryDto.getClaimNo()).toString()); // ?4% - Offer Amount

            String customerMobile = policyDto.getCustMobileNo();
            String policyChannelType = policyDto.getPolicyChannelType();

            if (customerMobile != null && !customerMobile.isEmpty()) {
                sendSmsMessage(connection, 53, smsParameterList, customerMobile, policyChannelType, user,
                        reminderPrintSummaryDto.getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
            }
        }
    }

//

}

