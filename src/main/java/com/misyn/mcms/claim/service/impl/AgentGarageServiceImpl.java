package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.AgentGarageDao;
import com.misyn.mcms.claim.dao.impl.AgentGarageDaoImpl;
import com.misyn.mcms.claim.dto.AgentGarageDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.AgentGarageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class AgentGarageServiceImpl extends AbstractBaseService<AgentGarageServiceImpl> implements AgentGarageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserServiceImpl.class);
    private AgentGarageDao agentGarageDao = new AgentGarageDaoImpl();

    @Override
    public AgentGarageDto insert(AgentGarageDto agentGarageDto) throws Exception {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            if (agentGarageDto.getId() == 0) {
                agentGarageDao.addnewGarage(connection, agentGarageDto);
            } else {
                agentGarageDao.updateGarage(connection, agentGarageDto);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return agentGarageDto;
    }

    @Override
    public AgentGarageDto search(Integer id) throws Exception {
        Connection connection = getJDBCConnection();
        AgentGarageDto agentGarageDto;
        try {
            beginTransaction(connection);
            agentGarageDto = agentGarageDao.searchGarage(connection, id);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return agentGarageDto;
    }

    @Override
    public DataGridDto getgarageDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = agentGarageDao.getDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public String validateGarageName(String spareParteName) throws Exception {
        Connection connection = null;
        String garageNames = "";
        try {
            connection = getJDBCConnection();
            garageNames = agentGarageDao.validategarageNmae(connection, spareParteName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return garageNames;
    }

}
