package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.ClaimUserLeaveDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface ClaimUserLeaveService {

    ClaimUserLeaveDto saveClaimUserLeave(ClaimUserLeaveDto claimUserLeaveDto) throws MisynJDBCException;

    ClaimUserLeaveDto updateClaimUserLeave(ClaimUserLeaveDto claimUserLeaveDto) throws MisynJDBCException;

    ClaimUserLeaveDto searchClaimUserLeave(Object id) throws MisynJDBCException;

    DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type);
}
