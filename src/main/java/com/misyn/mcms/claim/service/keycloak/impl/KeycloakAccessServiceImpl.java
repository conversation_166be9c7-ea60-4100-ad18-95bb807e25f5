package com.misyn.mcms.claim.service.keycloak.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.misyn.mcms.claim.dto.keycloak.*;
import com.misyn.mcms.claim.service.KeycloakAccessService;
import com.misyn.mcms.claim.service.keycloak.KeycloakApiClient;
import com.misyn.mcms.utility.Parameters;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class KeycloakAccessServiceImpl implements KeycloakAccessService {


    private static final String REALM = Parameters.getKeycloakRealm();


   /* public static void main(String[] args) {
        KeycloakAccessServiceImpl keycloakAccessService = new KeycloakAccessServiceImpl();
        MenuFormDto list = keycloakAccessService.getMenuFormDto("c2a650bb-3ad8-4062-a9a8-8474f27ebe9f", "list");
        // Close the client
    }*/


    public List<String> getUserRoleList(String userId) {
        List<String> userRoleList = new ArrayList<>();
        String bearerToken = KeycloakApiClient.getBearerToken();

        List<KeyCloakGroupDto> userGroups = KeycloakApiClient.sendGetRequest(
                "/admin/realms/" + REALM + "/users/" + userId + "/groups",
                bearerToken,
                new TypeReference<>() {
                }
        );
        if (userGroups != null) {
            for (KeyCloakGroupDto group : userGroups) {
                KeyCloakGroupDto keyCloakGroupDto = KeycloakApiClient.sendGetRequest(
                        "/admin/realms/" + REALM + "/groups/" + group.getId(),
                        bearerToken,
                        new TypeReference<KeyCloakGroupDto>() {
                        }
                );
                if (Objects.nonNull(keyCloakGroupDto) && !keyCloakGroupDto.getRealmRoles().isEmpty()) {
                    userRoleList.addAll(keyCloakGroupDto.getRealmRoles());
                }
            }
        } else {
            System.out.println("Failed to fetch user groups.");
        }
        return userRoleList;
    }

    @Override
    public Map<String, String> getUserRoleMap(String userId) {
        Map<String, String> userRoleMap = new HashMap<>();
        String bearerToken = KeycloakApiClient.getBearerToken();

        List<KeyCloakGroupDto> userGroups = KeycloakApiClient.sendGetRequest(
                "/admin/realms/" + REALM + "/users/" + userId + "/groups",
                bearerToken,
                new TypeReference<>() {
                }
        );
        if (userGroups != null) {
            for (KeyCloakGroupDto group : userGroups) {
                KeyCloakGroupDto keyCloakGroupDto = KeycloakApiClient.sendGetRequest(
                        "/admin/realms/" + REALM + "/groups/" + group.getId(),
                        bearerToken,
                        new TypeReference<KeyCloakGroupDto>() {
                        }
                );
                if (Objects.nonNull(keyCloakGroupDto) && !keyCloakGroupDto.getRealmRoles().isEmpty()) {
                    keyCloakGroupDto.getRealmRoles().forEach(role -> {
                        userRoleMap.put(role, role);
                    });
                }
            }
        } else {
            System.out.println("Failed to fetch user groups.");
        }
        return userRoleMap;
    }


    public MenuFormDto getMenuFormDto(String userId, String roleStartsWith) {
        MenuFormDto menuFormDto = new MenuFormDto();
        String bearerToken = KeycloakApiClient.getBearerToken();

        List<KeycloakMenuRoleDto> keycloakMenuRoleList = KeycloakApiClient.sendGetRequest(
                "/admin/realms/" + REALM + "/roles",
                bearerToken,
                new TypeReference<>() {
                }
        );

        assert keycloakMenuRoleList != null;
        Map<String, KeycloakMenuRoleDto> allRoleMap = keycloakMenuRoleList.stream()
                .filter(keycloakMenuRoleDto -> keycloakMenuRoleDto.getName().contains(roleStartsWith))
                .collect(Collectors.toMap(KeycloakMenuRoleDto::getName, role -> role));


        Map<String, MenuItemDto> menuItemDtoMap = new ConcurrentHashMap<>();

        this.setKeycloakRoles(bearerToken, userId, allRoleMap, menuItemDtoMap);
        this.setMenuItems(menuFormDto, menuItemDtoMap);


        return menuFormDto;
    }


    private void setKeycloakRoles(
            String bearerToken,
            String userId,
            Map<String, KeycloakMenuRoleDto> allRoleMap,
            Map<String, MenuItemDto> menuItemDtoMap) {

        getUserRoleList(userId).stream()
                .map(allRoleMap::get)
                .filter(Objects::nonNull)
                .map(role -> fetchRoleFromKeycloak(role, bearerToken))
                .filter(Objects::nonNull)
                .filter(role -> hasValidAttributes(role.getAttributes()))
                .forEach(role -> processRole(role, menuItemDtoMap));
    }

    private KeycloakMenuRoleDto fetchRoleFromKeycloak(KeycloakMenuRoleDto role, String bearerToken) {
        return KeycloakApiClient.sendGetRequest(
                "/admin/realms/" + REALM + "/roles-by-id/" + role.getId(),
                bearerToken,
                new TypeReference<>() {
                }
        );
    }

    private boolean hasValidAttributes(AttributesDto attributes) {
        return attributes != null && !attributes.getParentMenuTitle().isEmpty();
    }

    private void processRole(KeycloakMenuRoleDto role, Map<String, MenuItemDto> menuItemDtoMap) {
        AttributesDto attributes = role.getAttributes();
        String parentMenuTitle = attributes.getParentMenuTitle().getFirst();
        int childMenuOrderIndex = parseInteger(attributes.getChildMenuOrderIndex().getFirst(), 0);
        int parentMenuOrderIndex = parseInteger(attributes.getParentMenuOrderIndex().getFirst(), 0);

        String menuIcon = getOrDefault(attributes.getMenuIcon(), "TeamOutlined");
        String parentMenuIcon = getOrDefault(attributes.getParentMenuIcon(), "AppstoreOutlined");
        String appUrl = getOrDefault(attributes.getAppUrl(), "#");

        MenuItemDto parentMenuItem = menuItemDtoMap.computeIfAbsent(parentMenuTitle, key -> {
            MenuItemDto newItem = new MenuItemDto();
            newItem.setId(role.getId());
            newItem.setMenuName(parentMenuTitle);
            newItem.setMenuIcon(menuIcon);
            newItem.setParentMenuIcon(parentMenuIcon);
            newItem.setSubMenuItems(new ArrayList<>());
            newItem.setOrderIndex(parentMenuOrderIndex);
            return newItem;
        });

        MenuItemDto subMenuItem = new MenuItemDto();
        subMenuItem.setId(role.getId());
        subMenuItem.setMenuName(role.getDescription());
        subMenuItem.setOrderIndex(childMenuOrderIndex);
        subMenuItem.setMenuIcon(menuIcon);
        subMenuItem.setAppUrl(appUrl);

        parentMenuItem.getSubMenuItems().add(subMenuItem);
    }

    private int parseInteger(String value, int defaultValue) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private String getOrDefault(List<String> list, String defaultValue) {
        return (list == null || list.isEmpty()) ? defaultValue : list.get(0);
    }


    private void setMenuItems(MenuFormDto menuFormDto, Map<String, MenuItemDto> menuItemDtoMap) {
        List<MenuItemDto> parentMenuList = new ArrayList<>();
        for (Map.Entry<String, MenuItemDto> entry : menuItemDtoMap.entrySet()) {
            MenuItemDto menuItemDto = entry.getValue();
            Collections.sort(menuItemDto.getSubMenuItems());
            parentMenuList.add(menuItemDto);
        }
        Collections.sort(parentMenuList);
        menuFormDto.setMenuItemDtoList(parentMenuList);
    }

}
