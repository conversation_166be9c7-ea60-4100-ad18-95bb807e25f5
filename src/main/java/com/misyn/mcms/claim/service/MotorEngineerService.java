package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.exception.ErrorMsgException;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.HashMap;
import java.util.List;

/**
 * Created by a<PERSON>la on 5/16/18.
 */
public interface MotorEngineerService extends BaseService<MotorEngineerDetailsDto> {

    DataGridDto getClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNo(Integer claimNo, Integer departmentId) throws Exception;

    ClaimInspectionTypeDto getInspectionTypeDto(String id) throws Exception;

    List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Integer departmentId, Integer inspectionTypeId) throws Exception;

    List<ClaimDocumentDto> getClaimDocumentDtoList(Integer jobRefNo, Integer departmentId) throws Exception;

    List<ClaimThirdPartyDetailsGenericDto> getClaimThirdPartyDetailsGeneric(Integer claimNo) throws Exception;

    List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo, Integer jobRefNo, Integer departmentId, Integer inspectionTypeId);

    List<PreviousClaimsDto> getPreviousClaimList(Integer polNo, Integer jobNo) throws Exception;

    List<PreviousClaimsDto> getPreviousInspectionClaimList(Integer claimNo, Integer jobNo) throws Exception;

    List<ClaimImageDto> getClaimImageDtoList(Integer claimNo, Integer jobRefNo);

    void forwardToInformDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, String forwardUserName, UserDto sessionUser) throws Exception;

    void informDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto sessionUser) throws Exception;

    void recallDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto sessionUser) throws Exception;

    void returnDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto sessionUser) throws Exception;

    void insertDesktopInitialRecords(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception;

    boolean updateToChangeRequest(Integer refNo, String remark, UserDto user, boolean isRteChange) throws Exception;

    boolean updateToChangeRequest(Integer refNo, String remark, UserDto user, boolean isRteChange, String approveAssignRteUser, Integer claimNo) throws Exception;

    boolean addRteRemarks(int refNo, String rteRemarks) throws Exception;

    boolean addSpecialRemark(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception;

    List<PreviousClaimsDto> getInspectionList(Connection connection, Integer claimNo);

    MotorEngineerDetailsDto search(Connection connection, Object id);

    List<MotorEngineerDetailsDto> getMotorEngineerDetailsDtoList(Integer claimNo);

    MotorEngineerDetailsDto getLatestUpdateOnSite(MotorEngineerDetailsDto motorEngineerDetailsDto);

    boolean updateAssignUser(Integer refNo, String assignUser, UserDto user, Integer ClaimNo) throws Exception;

    String updateSelfAssignRTEUser(Integer refNo, String assignUser, UserDto user, Integer ClaimNo) throws Exception;

    String updateSelfAssignCallCenterUser(String assignUser, UserDto user, Integer claimNo, Integer requestedInspectionId) throws Exception;


    boolean isOnSiteOrOffSIteInspectionApproved(Integer claimNo) throws Exception;

    ContactDetailDto getContactDetailForRte(String assignRteUser) throws Exception;

    boolean isAcrCanBeApprove(Integer inspectionId, BigDecimal totalAcr, BigDecimal sumInsured, BigDecimal pav, BigDecimal totalApprovedAcr, Integer claimNo) throws ErrorMsgException;

    BigDecimal getTotalAprvAcrAmount(Integer refNo);

    MotorEngineerDetailsDto setAdvanceAmountDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Integer claimNo);

    List<String> getIdsToArray(String selectClaimNoAndAssignUsers);

    HashMap<String, String> setClaimNoandJobNoToMap(String selectIds);

    boolean isOnsiteOrOffSitePending(Integer claimNo);

//    void isValidAuthorize(MotorEngineerDetailsDto motorEngineerDetailsDto, boolean isForward) throws Exception;

    void isValidAuthorize(MotorEngineerDetailsDto motorEngineerDetailsDto, boolean isForward, String emailVal) throws Exception;

    MotorEngineerDetailsDto getLatestUpdateOnsiteInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto);

    boolean checkPendingInspection(Integer claimNo);

    boolean isForwardInspection(BigDecimal acr, Integer refNo, Integer inspectionId, UserDto user);

    MotorEngineerDetailsDto insert(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, BigDecimal premium, boolean isCancelled) throws Exception;

    MotorEngineerDetailsDto update(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, BigDecimal premium, boolean isCancelled) throws Exception;

    boolean returnByApproveAssignRte(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception;

    ClaimSummaryDto getClaimSummaryDetails(Integer claimNo);

    List<ClaimLogTrailDto> getLogDetails(Integer claimNo, Integer jobRefNo);

    List<String[]> getRteListWithAuthLevel() throws Exception;

    List<PopupItemDto> getRteListForReassign(String userName) throws Exception;

    List<PopupItemDto> getReportingRteListForReassign() throws Exception;

    Integer getClaimStatus(Integer claimNo) throws Exception;

    UserDto getUserDetailByUserId(String userId) throws Exception;

    String callOnMiSiteOnlineAssessment(OnMiSiteReqDto dto,UserDto user) throws Exception;
}
