package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dto.ClaimLockDto;
import com.misyn.mcms.claim.service.ClaimLockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
public class ClaimLockServiceImpl implements ClaimLockService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimLockServiceImpl.class);
    private final static ConcurrentMap<Integer, ClaimLockDto> INTIMATION_LOCK_MAP = new ConcurrentHashMap();

    @Override
    public boolean isLockIntimation(ClaimLockDto claimLockDto) {
        boolean isLock = false;
        try {
            if (INTIMATION_LOCK_MAP.containsKey(claimLockDto.getKey())) {
                isLock = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return isLock;
    }

    @Override
    public void lockIntimation(ClaimLockDto claimLockDto) {
        try {
            if (!INTIMATION_LOCK_MAP.containsKey(claimLockDto.getKey())) {
                INTIMATION_LOCK_MAP.put(claimLockDto.getKey(), claimLockDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void unLockIntimation(ClaimLockDto claimLockDto, String userId) {
        try {
            if (INTIMATION_LOCK_MAP.containsKey(claimLockDto.getKey()) && userId.equalsIgnoreCase(claimLockDto.getInvokeUserId())) {
                INTIMATION_LOCK_MAP.remove(claimLockDto.getKey());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public void unLockIntimation(Integer key, String userId) {
        try {
            if (INTIMATION_LOCK_MAP.containsKey(key) && userId.equalsIgnoreCase(userId)) {
                INTIMATION_LOCK_MAP.remove(key);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public ClaimLockDto getClaimLockDto(Integer key) {
        return INTIMATION_LOCK_MAP.get(key);
    }
}
