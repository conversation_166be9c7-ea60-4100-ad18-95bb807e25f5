package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.InvestigationStatusEnum;
import com.misyn.mcms.claim.enums.PaymentStatus;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimDocumentService;
import com.misyn.mcms.claim.service.ClaimUserAllocationService;
import com.misyn.mcms.claim.service.InvestigationDetailsService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
public class InvestigationDetailsServiceImpl extends AbstractBaseService<InvestigationDetailsServiceImpl> implements InvestigationDetailsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(InvestigationDetailsServiceImpl.class);
    private InvestigationDetailsDao investigationDetailsDao = new InvestigationDetailsDaoImpl();
    private InvestigationReasonDetailsDao investigationReasonDetailsDao = new InvestigationReasonDetailsDaoImpl();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private ClaimDocumentService claimDocumentService = new ClaimDocumentServiceImpl();
    private ClaimPanelAssignUserDao claimPanelAssignUserDao = new ClaimPanelAssignUserDaoImpl();
    private ClaimUserAllocationService claimUserAllocationService = new ClaimUserAllocationServiceImpl();
    private AssessorPaymentDetailsDao assessorPaymentDetailsDao = new AssessorPaymentDetailsDaoImpl();
    private AssessorDao assessorDao = new AssessorDaoImpl();
    private InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private ClaimInvestigationSelectImageDao claimInvestigationSelectImageDao = new ClaimInvestigationSelectImageDaoImpl();
    private PendingInvestigationDetailsDao pendingInvestigationDetailsDao = new PendingInvestigationDetailsDaoImpl();
    private MainPanelAssignHstDao mainPanelAssignHstDao = new MainPanelAssignHstDaoImpl();

    @Override
    public InvestigationDetailsDto saveInvestigationDetails(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception {
        InvestigationDetailsDto saveInvestigationDetailsDto;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            InvestigationDetailsDto investigationDetailsDto1 = investigationDetailsDao.searchMaster(connection, investigationDetailsDto.getInvestTxnNo());

            if (null == investigationDetailsDto1) {
                saveInvestigationDetailsDto = investigationDetailsDao.insertMaster(connection, investigationDetailsDto);
            } else {
                saveInvestigationDetailsDto = investigationDetailsDao.updateMaster(connection, investigationDetailsDto);
            }

            if (saveInvestigationDetailsDto != null) {
                investigationReasonDetailsDao.deleteInvestigetionByTxnNo(connection, saveInvestigationDetailsDto.getInvestTxnNo());
                for (InvestigationReasonDetailsDto investigationReasonDetailsDto :
                        investigationDetailsDto.getInvestigationReasonDetailsDtoList()) {
                    investigationReasonDetailsDto.setInvesTxnNo(saveInvestigationDetailsDto.getInvestTxnNo());
                    investigationReasonDetailsDao.insertMaster(connection, investigationReasonDetailsDto);
                }
            }
            int claimNo = investigationDetailsDto.getClaimNo();
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);

            /*if (claimUserTypeDto.isDecisionMaker()) {
                claimHandlerDto.setClaimStatus(53);
            } else if (claimUserTypeDto.isTwoPanelUser()) {
                claimHandlerDto.setClaimStatus(48);
            }else{
                claimHandlerDto.setClaimStatus(53);
            }*/


            ///
//'AR','CAN','C','P','INVEST_APPROVED','DM_REQ_INVEST','CH_REQ_INVEST','N'
            String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_INVESTIGATION));
            StringBuilder sbMessage = new StringBuilder();
            String assignUserId = "";//TODO
            String remarkSection = "";
            String remark = "";
            String logType = "";
            switch (investigationDetailsDto.getInvestigationStatus()) {
                case "CH_REQ_INVEST":
                    //Assign user id should be DMaker
                    assignUserId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_DECISION_MAKER, AppConstant.CLAIM_HANDLER_INVESTIGATION_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                    remark = investigationDetailsDto.getReason();
                    remarkSection = "Investigation Request By Claim Handler - Assign user [" + assignUserId + "]";
                    logType = "Investigation Request";
                    sbMessage.append("You have received an Investigation Request");

                    claimHandlerDto.setInvestigationAssignUserId(assignUserId);
                    claimHandlerDto.setClaimNo(claimNo);
                    claimHandlerDto.setInvestigationAssignDateTime(Utility.sysDateTime());
                    claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.CLAIM_HANDLER_REQUEST_INVESTIGATION.getInvestigationStatus());
                    claimHandlerDto.setDecisionMakingAssignUserId(assignUserId);
                    claimHandlerDto.setDecisionMakingAssignDateTime(Utility.sysDateTime());
                    claimHandlerDao.updateInvestigationAssignUserAndDecisionMakeUser(connection, claimHandlerDto);
                    //56	CHRI	CLAIM HANDLER REQUEST INVESTIGATION	4
                    claimHandlerDao.updateClaimStatus(connection, claimNo, 56);
                    break;
                case "DM_REQ_INVEST":
                    remark = investigationDetailsDto.getReason();
                    remarkSection = "Investigation Request By Decision Maker";
                    logType = "Investigation Request";
                    sbMessage.append("You have received file to make an investigation decision");

                    List<String> userIdList = claimHandlerDao.getPanelUserIdList(connection, 1);
                    ClaimPanelAssignUserDto claimPanelDetails = claimPanelAssignUserDao.searchByClaimNo(connection, claimNo);
                    if (null != claimPanelDetails && claimPanelDetails.getPanelId().equals(AppConstant.MAIN_PANEL)) {
                        mainPanelAssignHstDao.shiftFromPanel(connection, claimNo);
                    }
                    claimPanelAssignUserDao.deleteByClaimNo(connection, claimNo);

                    List<ClaimPanelAssignUserDto> claimPanelAssignUserDtoList = new ArrayList<>();
                    for (String userId : userIdList) {
                        ClaimPanelAssignUserDto claimPanelAssignUserDto = new ClaimPanelAssignUserDto();
                        claimPanelAssignUserDto.setClaimNo(claimNo);
                        claimPanelAssignUserDto.setInputDateTime(Utility.sysDateTime());
                        claimPanelAssignUserDto.setInputUser(user.getUserId());
                        claimPanelAssignUserDto.setUserId(userId);
                        claimPanelAssignUserDtoList.add(claimPanelAssignUserDto);
                    }
                    for (ClaimPanelAssignUserDto claimPanelAssignUserDto : claimPanelAssignUserDtoList) {
                        claimPanelAssignUserDao.insertMaster(connection, claimPanelAssignUserDto);
                        ClaimHandlerDto calClaimHandlerDto = new ClaimHandlerDto();
                        calClaimHandlerDto.setAssignUserId(claimPanelAssignUserDto.getUserId());
                        calClaimHandlerDto.setClaimNo(claimPanelAssignUserDto.getClaimNo());
                        saveNotification(connection, claimNo, user.getUserId(), claimPanelAssignUserDto.getUserId(), sbMessage.toString(), URL);
                    }

                    investigationDetailsDao.updateInvestigationAssignUser(connection, investigationDetailsDto);

                    claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.DECISION_MAKER_REQUEST_INVESTIGATION.getInvestigationStatus());
                    //claimHandlerDao.updateInvestigationStatus(connection, claimHandlerDto);
                    claimHandlerDto.setInvestigationAssignUserId(user.getUserId());
                    claimHandlerDto.setClaimNo(claimNo);
                    claimHandlerDto.setInvestigationAssignDateTime(Utility.sysDateTime());

                    claimHandlerDto.setDecisionMakingAssignUserId(assignUserId);
                    claimHandlerDto.setDecisionMakingAssignDateTime(Utility.sysDateTime());

                    claimHandlerDao.updateInvestigationAssignUser(connection, claimHandlerDto);


                    claimHandlerDao.updateClaimStatus(connection, claimNo, 52);
                    //saveClaimSpecialRemark(connection, user, claimNo, "Investigation Request By Decision Maker", remark);
                    saveClaimsLogs(connection, claimNo, user, "Investigation Request", "Investigation Request By Decision Maker");
                    shiftNotificationOnAction(connection, claimNo, user);
                    break;
                case "INVEST_APPROVED":
                    assignUserId = investigationDetailsDto1.getInvestReqUser();
                    remark = investigationDetailsDto.getReason();
                    remarkSection = "Investigation request approved By Sub panel - Assign User [" + assignUserId + "]";
                    logType = "Investigation Request Approval";
                    sbMessage.append("You have received approved Investigation Request");
                    claimHandlerDto.setInvestigationAssignUserId(assignUserId);
                    claimHandlerDto.setClaimNo(claimNo);
                    claimHandlerDto.setInvestigationAssignDateTime(Utility.sysDateTime());
                    claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.INVESTIGATION_APPROVED.getInvestigationStatus());
                    investigationDetailsDao.updateInvestigationRequestApprove(connection, investigationDetailsDto);
                    //68	CHRI	CLAIM HANDLER REQUEST APPROVED	4
                    claimHandlerDao.updateClaimStatus(connection, claimNo, 68);
                    break;
                case "AR":
                    //53	DECISION MAKER INVESTIGATION ARRANGED
                    remark = investigationDetailsDto.getReason();
                    claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
                    claimHandlerDto.setInvestigationArrangeDateTime(Utility.sysDateTime());
                    claimHandlerDto.setInvestigationArrangeUserId(claimUserTypeDto.getUser().getUserId());
                    claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.ARRANGE.getInvestigationStatus());
                    assignUserId = claimHandlerDto.getAssignUserId();
                    investigationDetailsDao.updateInvestigationStatus(connection, investigationDetailsDto);
                    //48	2 MEMBER PANEL INVESTIGATION ARRANGED
                    claimHandlerDao.updateInvestigationArrangedUser(connection, claimHandlerDto);
                    claimHandlerDao.updateClaimStatus(connection, claimNo, 53);
                    break;
            }
            if (null != assignUserId && !assignUserId.isEmpty()) {
                saveNotification(connection, claimNo, user.getUserId(), assignUserId, sbMessage.toString(), URL);
            }

            //TODO - NOTIFICATION TO --> decisionMakingUserId | MSG - Investigation Request By Claim Handler
            saveSpecialRemark(connection, user, claimNo, remarkSection, remark);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, logType, remarkSection);
            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public InvestigationDetailsDto completedInvestigationDetails(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception {
        InvestigationDetailsDto saveInvestigationDetailsDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            if (null != investigationDetailsDto) {
                saveInvestigationDetailsDto = investigationDetailsDao.updateInvestigationCompleteDetails(connection, investigationDetailsDto);
                AssessorPaymentDetailsDto searchAssessorPaymentDetailsDto = assessorPaymentDetailsDao.searchByKeyAndType(connection, investigationDetailsDto.getInvestTxnNo(), AppConstant.INVESTIGATION_PAYMENT);
                AssessorPaymentDetailsDto assessorPaymentDetailsDto = this.getAssessorPaymentDetails(connection, investigationDetailsDto, user);
                if (null == searchAssessorPaymentDetailsDto) {
                    assessorPaymentDetailsDao.insertMaster(connection, assessorPaymentDetailsDto);
                } else {
                    assessorPaymentDetailsDao.updateStausByIdAndStatus(connection, searchAssessorPaymentDetailsDto.getTxnId(), assessorPaymentDetailsDto);
                }
            }

            int claimNo = investigationDetailsDto.getClaimNo();
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInvestigationArrangeDateTime(Utility.sysDateTime());
            claimHandlerDto.setInvestigationArrangeUserId(claimUserTypeDto.getUser().getUserId());
            claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.COMPLETED.getInvestigationStatus());
            claimHandlerDto.setClaimStatus(54);
            claimHandlerDao.updateInvestigationCompletedUser(connection, claimHandlerDto);
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Complete Investigation", "Complete Investigation - "
                    .concat(" Professional Fee : ")
                    .concat(investigationDetailsDto.getProfFee().toString())
                    .concat(" ,Traveling Fee  : ")
                    .concat(investigationDetailsDto.getTravelFee().toString())
                    .concat(" ,Other Fee  : ")
                    .concat(investigationDetailsDto.getOtherFee().toString())
                    .concat(" ,Total Fee  : ")
                    .concat(investigationDetailsDto.getTotalFee().toString())
            );

            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return saveInvestigationDetailsDto;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationPayment(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception {
        InvestigationDetailsDto saveInvestigationDetailsDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            if (null != investigationDetailsDto) {
                saveInvestigationDetailsDto = investigationDetailsDao.updateInvestigationPaymentDetails(connection, investigationDetailsDto);
                AssessorPaymentDetailsDto searchAssessorPaymentDetailsDto = assessorPaymentDetailsDao.searchByKeyAndType(connection, investigationDetailsDto.getInvestTxnNo(), AppConstant.INVESTIGATION_PAYMENT);
                AssessorPaymentDetailsDto assessorPaymentDetailsDto = this.getAssessorPaymentDetails(connection, investigationDetailsDto, user);
                if (null == searchAssessorPaymentDetailsDto) {
                    assessorPaymentDetailsDao.insertMaster(connection, assessorPaymentDetailsDto);
                } else {
                    assessorPaymentDetailsDao.updateStausByIdAndStatus(connection, searchAssessorPaymentDetailsDto.getTxnId(), assessorPaymentDetailsDto);
                }
            }

            Integer claimNo = investigationDetailsDto.getClaimNo();
            saveClaimsLogs(connection, claimNo, user, "Update Investigation Payment", "Update Investigation Payment - "
                    .concat(" Professional Fee : ")
                    .concat(investigationDetailsDto.getProfFee().toString())
                    .concat(" ,Traveling Fee  : ")
                    .concat(investigationDetailsDto.getTravelFee().toString())
                    .concat(" ,Other Fee  : ")
                    .concat(investigationDetailsDto.getOtherFee().toString())
                    .concat(" ,Total Fee  : ")
                    .concat(investigationDetailsDto.getTotalFee().toString())
            );

            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return saveInvestigationDetailsDto;

    }

    @Override
    public InvestigationDetailsDto cancelInvestigationDetails(InvestigationDetailsDto investigationDetailsDto, ClaimUserTypeDto claimUserTypeDto, UserDto user) throws Exception {
        InvestigationDetailsDto saveInvestigationDetailsDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            if (null != investigationDetailsDto) {
                saveInvestigationDetailsDto = investigationDetailsDao.updateInvestigationCancelDetails(connection, investigationDetailsDto);
            }
            InvestigationDetailsDto investigationDetailsDto1 = investigationDetailsDao.searchMaster(connection, investigationDetailsDto.getInvestTxnNo());

            int claimNo = investigationDetailsDto.getClaimNo();
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInvestigationArrangeDateTime(Utility.sysDateTime());
            claimHandlerDto.setInvestigationArrangeUserId(claimUserTypeDto.getUser().getUserId());
            claimHandlerDto.setInvestigationStatus(InvestigationStatusEnum.CANCEL.getInvestigationStatus());
            claimHandlerDto.setClaimStatus(55);
            claimHandlerDao.updateInvestigationCompletedUser(connection, claimHandlerDto);

            String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(AppConstant.TAB_INDEX_INVESTIGATION));
            saveNotification(connection, claimNo, user.getUserId(), investigationDetailsDto1.getInvestReqUser(), "You have received cancelled Investigation Request", URL);
            saveSpecialRemark(connection, user, claimNo, "Investigation Cancelled", investigationDetailsDto.getReason());
            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Cancel Investigation", "Cancel Investigation");

            commitTransaction(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return saveInvestigationDetailsDto;
    }

    @Override
    public InvestigationDetailsFormDto getInvestigationDetailsFormDto(Integer claimNo, Integer txnNo) {
        Connection connection = null;
        InvestigationDetailsFormDto investigationDetailsFormDto = new InvestigationDetailsFormDto();
        InvestigationDetailsDto investigationDetailsDto = null;
        try {
            connection = getJDBCConnection();
            if (txnNo == 0) {
                txnNo = investigationDetailsDao.getMaxInvestigationTxnIdByClaimNo(connection, claimNo);
            }
            investigationDetailsDto = investigationDetailsDao.searchMaster(connection, txnNo);
            if (investigationDetailsDto == null) {
                investigationDetailsDto = new InvestigationDetailsDto();
                investigationDetailsDto.setInvestArrangeDateTime(AppConstant.STRING_EMPTY);
                investigationDetailsDto.setInvestCompletedDateTime(AppConstant.STRING_EMPTY);
            }
            investigationDetailsDto.setInvestigationReasonDetailsDtoList(investigationReasonDetailsDao.searchAllByInvestigationTxnNo(connection, txnNo));
            List<InvestigationDetailsDto> investigationDetailsDtoList = investigationDetailsDao.searchAll(connection, claimNo);
            investigationDetailsFormDto.setInvestigationDetailsDto(investigationDetailsDto);
            investigationDetailsFormDto.setInvestigationDetailsDtoList(investigationDetailsDtoList);
            investigationDetailsFormDto.setInvestigationReportStatus(claimDocumentService.getInvestigationReportDocumentStatus(connection, claimNo));
            investigationDetailsFormDto.setAssessorDtoList(inspectionDetailsDao.getAssignAssessorsByClaim(connection, claimNo));
            investigationDetailsFormDto.setDrivenLicenseDocumentList(claimDocumentDao.
                    findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus
                            (connection, claimNo, AppConstant.DRIVING_LICENCE_DOCUMENT_TYPE_ID));
            investigationDetailsFormDto.setEstimateDocumentList(claimDocumentDao.
                    findAllByClaimNoAndInDocumentTypeIdAndInDocumentStatus
                            (connection, claimNo, AppConstant.ALL_CLAIM_FORM_DOCUMENT_TYPE_IDS));
            investigationDetailsFormDto.setPoliceReportList(claimDocumentDao.
                    findAllByClaimNoAndInDocumentTypeIdAndInDocumentStatus
                            (connection, claimNo, AppConstant.ALL_POLICE_REPORT_DOCUMENT_TYPE_IDS));

            investigationDetailsFormDto.setInvestigationSelectImageDtoList(claimInvestigationSelectImageDao.getSelectImages(connection, claimNo));

            investigationDetailsFormDto.setSelectedInvestigationImages(claimInvestigationSelectImageDao.isSeletedInvestigationImages(connection, claimNo));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return investigationDetailsFormDto;
    }

    @Override
    public List<InvestigationDetailsDto> getInvestigationDetailsDtoList(Integer claimNo) {
        List<InvestigationDetailsDto> investigationDetailsDtoList = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            investigationDetailsDtoList = investigationDetailsDao.searchAll(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return investigationDetailsDtoList;
    }

    @Override
    public List<InvestigationReasonDetailsDto> getInvestigationReasonDetailsDtos() {
        List<InvestigationReasonDetailsDto> list = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = investigationReasonDetailsDao.searchAllByInvestigationTxnNo(connection, 0);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;

    }

    @Override
    public int getSelectedImagesCount(Integer claimNo) {
        Connection connection = null;
        int selectedImagesCount = 0;
        try {
            connection = getJDBCConnection();
            selectedImagesCount = claimInvestigationSelectImageDao.getSelectedImagesCount(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return selectedImagesCount;
    }

    @Override
    public InvestigationDetailsFormDto getSelectedImages(Integer claimNo) {
        Connection connection = null;
        InvestigationDetailsFormDto investigationDetailsFormDto = new InvestigationDetailsFormDto();
        try {
            connection = getJDBCConnection();
            investigationDetailsFormDto.setInvestigationSelectImageDtoList(claimInvestigationSelectImageDao.getSelectImages(connection, claimNo));

            investigationDetailsFormDto.setSelectedInvestigationImages(claimInvestigationSelectImageDao.isSeletedInvestigationImages(connection, claimNo));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return investigationDetailsFormDto;
    }

    @Override
    public void sendNotificationForPendingInvestigationAssignUser() {
        Connection connection = null;
        String URL = AppConstant.STRING_EMPTY;
        String notificationMsg;
        try {
            connection = getJDBCConnection();
            List<InvestigationDetailsDto> investigationDetailsDtoList = investigationDetailsDao.getInvestigationListStillPendingAfterFiveDays(connection, Utility.getBeforeNoOfDays(10));
            if (null != investigationDetailsDtoList && !investigationDetailsDtoList.isEmpty()) {
                for (InvestigationDetailsDto investigationDetailsDto : investigationDetailsDtoList) {
                    if (!pendingInvestigationDetailsDao.isNotificationSend(connection, investigationDetailsDto.getClaimNo())) {

                        PendingInvestigationDetailsDto pendingInvestigationDetailsDto = new PendingInvestigationDetailsDto();
                        pendingInvestigationDetailsDto.setClaimNo(investigationDetailsDto.getClaimNo());
                        pendingInvestigationDetailsDto.setInvestigationJobNo(investigationDetailsDto.getInvestJobNo());
                        pendingInvestigationDetailsDto.setNotificationSendDate(Utility.sysDate());

                        pendingInvestigationDetailsDao.insertMaster(connection, pendingInvestigationDetailsDto);

                        notificationMsg = "INVESTIGATION PERIOD HAS BEEN EXPIRED";
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(investigationDetailsDto.getClaimNo())).concat("&P_TAB_INDEX=5");
                        saveNotification(connection, investigationDetailsDto.getClaimNo(), "SYSTEM", investigationDetailsDto.getInvestArrangeUser(), notificationMsg, URL);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private AssessorPaymentDetailsDto getAssessorPaymentDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto, UserDto user) throws Exception {
        AssessorPaymentDetailsDto assessorPaymentDetailsDto = new AssessorPaymentDetailsDto();
        try {

            assessorPaymentDetailsDto.setClaimNo(investigationDetailsDto.getClaimNo());
            assessorPaymentDetailsDto.setKeyId(investigationDetailsDto.getInvestTxnNo());
            assessorPaymentDetailsDto.setName(investigationDetailsDto.getAssignInvestigatorName());
            assessorPaymentDetailsDto.setType(AppConstant.INVESTIGATION_PAYMENT);
            assessorPaymentDetailsDto.setMilleage(0);
            assessorPaymentDetailsDto.setOtherFee(investigationDetailsDto.getOtherFee());
            assessorPaymentDetailsDto.setDeductionFee(BigDecimal.ZERO);
            assessorPaymentDetailsDto.setTotalFee(investigationDetailsDto.getTotalFee());
            assessorPaymentDetailsDto.setTravelFee(investigationDetailsDto.getTravelFee());
            assessorPaymentDetailsDto.setProfessionalFee(investigationDetailsDto.getProfFee());
            assessorPaymentDetailsDto.setPaymentStatus(PaymentStatus.Pending);
            assessorPaymentDetailsDto.setRecordStatus(1);
            assessorPaymentDetailsDto.setInpUserId(user.getUserId());
            assessorPaymentDetailsDto.setInpDate(Utility.sysDateTime());
            AssessorDto assessorDto = assessorDao.searchMaster(connection, investigationDetailsDto.getAssignInvestigatorUserRefId());
            if (null != assessorDto) {
                assessorPaymentDetailsDto.setName(assessorDto.getUserName());
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
        return assessorPaymentDetailsDto;
    }
}
