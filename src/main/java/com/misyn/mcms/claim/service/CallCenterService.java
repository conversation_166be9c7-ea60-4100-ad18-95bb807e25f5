package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.util.List;

public interface CallCenterService extends BaseService<ClaimsDto> {

    ClaimsDto saveClaim(ClaimsDto claimsDto, UserDto user, String type) throws Exception;

    DataGridDto getPolicyDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String accidentDate, String vehicleNumber);

    DataGridDto getClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    ClaimsDto getReportAccidentClaimsDto(Integer policyRefNo, Integer claimNo);

    //ClaimsDto getReportAccidentClaimsDto(Integer policyRefNo, Integer claimId);
    ClaimsDto getReportAccidentClaimsDtoByClaimNo(Integer claimId);

    ClaimsDto getReportAccidentClaimsDtoByClaimNo(Connection connection, Integer claimId);

    ClaimsDto getViewAccidentClaimsDto(Integer claimNo);

    ClaimsDto getViewAccidentClaimsForCallCenter(Integer claimNo);

    ClaimsDto getViewAccidentClaimsDto(Connection connection, Integer claimNo);

    ClaimsDto getClaimsDtoByClaimNo(Connection connection, Integer claimNo);

    List<DamageBodyPartDto> getDamageBodyPartDtoList(Integer claimNo, Integer vehClsId);

    Integer updateFollowUpCallInfo(ClaimsDto claimsDto, UserDto user) throws Exception;

    ErrorMessageDto saveSpecialRemark(SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception;

    PolicyDto getPolicyDetails(Integer policyRefNo) throws Exception;

    public ClaimsDto updatePolicyDetails(ClaimsDto claimsDto, Integer mappedPolicyNo, UserDto user) throws Exception;

    ClaimsDto savePolicyCoverNote(PolicyDto policyDto, UserDto user) throws Exception;

    List<ClaimsDto> getClaimHistoryForPolicyRefNo(String vehicleNo);

    ClaimsDto getViewAccidentClaimsDto(Integer claimNo, Integer policyRefNo);

    List<SpecialRemarkDto> getSpecialRemarkDtoList(ClaimsDto claimsDto);

    void setSpecialRemarkDtoList(ClaimsDto claimsDto, Integer claimNo);

    PolicyDto searchPolicyByVehicleNo(String vehicleNo) throws Exception;

    PolicyDto searchPolicyByValidPolicyDate(Integer policeRefNo, String accidentDate) throws Exception;

    List<ClaimThirdPartyDetailsGenericDto> getClaimThirdPartyDetailsGeneric(Integer claimNo) throws Exception;

    boolean getClaimNoByVehicleNoAndAccidentDate(String vehicleNo, String accidentDate);

    boolean updateDoubtClaim(String status, String remark, Integer claimNo, Integer jobRefNo, UserDto user, SpecialRemarkDto specialRemarkDto) throws Exception;

    void setUnderWritingDetails(ClaimsDto claimsDto);

    void updateIsfClaim(ClaimsDto claimsDto) throws Exception;

    void setOtherDetailsList(ClaimsDto claimsDto);

    boolean updateDriverDetails(ClaimsDto claimsDto, UserDto user);

    DriverDetailDto getDriverDetails(Integer claimNo);

    void markPriority(Integer claimNo, Integer priority, String remark, UserDto user) throws Exception;

    PolicyWarningMessageDto checkPolicyValidity(String policyNo) throws Exception;

    boolean markAsTheftAndFound(Integer claimNo, String remark, UserDto user) throws Exception;

    boolean isTheftClaim(Integer claimNo) throws Exception;

    TrailerDetailDto getTrailerDetail(String policyNumber, String policyChannelType) throws Exception;

    List<TradePlateDetailDto> getTradePlateDetail(String policyNumber, String policyChannelType) throws Exception;

}
