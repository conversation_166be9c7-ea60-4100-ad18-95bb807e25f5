package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimUserLeaveDao;
import com.misyn.mcms.claim.dao.ClaimUserLeaveHistoryDao;
import com.misyn.mcms.claim.dao.impl.ClaimUserLeaveDaoImpl;
import com.misyn.mcms.claim.dao.impl.ClaimUserLeaveHistoryDaoImpl;
import com.misyn.mcms.claim.dto.ClaimUserLeaveDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimUserLeaveService;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class ClaimUserLeaveServiceImpl extends AbstractBaseService<ClaimUserLeaveServiceImpl> implements ClaimUserLeaveService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveServiceImpl.class);
    private ClaimUserLeaveDao claimUserLeaveDao = new ClaimUserLeaveDaoImpl();
    private ClaimUserLeaveHistoryDao claimUserLeaveHistoryDao = new ClaimUserLeaveHistoryDaoImpl();

    @Override
    public ClaimUserLeaveDto saveClaimUserLeave(ClaimUserLeaveDto claimUserLeaveDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);

            claimUserLeaveDao.insertMaster(connection, claimUserLeaveDto);
            claimUserLeaveHistoryDao.insertMaster(connection, claimUserLeaveDto);

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUserLeaveDto;
    }

    @Override
    public ClaimUserLeaveDto updateClaimUserLeave(ClaimUserLeaveDto claimUserLeaveDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            if ("CANCEL".equals(claimUserLeaveDto.getLeaveType())) {
                claimUserLeaveDto.setFromDateTime(AppConstant.DEFAULT_DATE_TIME);
                claimUserLeaveDto.setToDateTime(AppConstant.DEFAULT_DATE_TIME);
            }
            claimUserLeaveDao.updateMaster(connection, claimUserLeaveDto);
            claimUserLeaveHistoryDao.insertMaster(connection, claimUserLeaveDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUserLeaveDto;
    }

    @Override
    public ClaimUserLeaveDto searchClaimUserLeave(Object id) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        ClaimUserLeaveDto claimUserLeaveDto;
        try {
            beginTransaction(connection);
            claimUserLeaveDto = claimUserLeaveDao.searchMaster(connection, id);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimUserLeaveDto;
    }


    @Override
    public DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimUserLeaveDao.getDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField, fromDate, toDate, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }
}
