package com.misyn.mcms.claim.service.impl;


import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.FileTypeEnum;
import com.misyn.mcms.claim.enums.NotificationPriority;
import com.misyn.mcms.claim.enums.YesNoWantDecideEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.exception.MisynSftpFileException;
import com.misyn.mcms.claim.exception.UserNotFoundException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimUserAllocationService;
import com.misyn.mcms.claim.service.StorageService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.DocumentApiClientUtil;
import com.misyn.mcms.utility.Parameters;
import com.misyn.mcms.utility.Utility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.imageio.stream.MemoryCacheImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.util.List;
import java.util.Objects;

public class StorageServiceImpl extends AbstractBaseService<StorageServiceImpl> implements StorageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StorageServiceImpl.class);
    private static final String tempFileDirectory = Parameters.getTempFileDirectory();
    private static final double IMAGE_RESIZE_PERCENT = Parameters.getImageResizePercent();
    private static final float IMAGE_COMPRESSION_QUALITY_FACTOR = Parameters.getImageCompressionQualityFactor();
    private static final String DOCUMENT_SERVICE_API_URL = Parameters.getDocumentServiceApiUrl();
    private final ClaimDocumentDao claimDocumentDao = new ClaimDocumentDaoImpl();
    private final ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private final ClaimImageDao claimImageDao = new ClaimImageDaoImpl();
    private final ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();
    private final ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private final ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private final UserDao userDao = new UserDaoImpl();
    private final DocumentHistoryDao documentHistoryDao = new DocumentHistoryDaoImpl();
    private final NotificationDao notificationDao = new NotificationDaoImpl();
    private final ClaimUserAllocationDao claimUserAllocationDao = new ClaimUserAllocationDaoImpl();
    private final ClaimUserAllocationService claimUserAllocationService = new ClaimUserAllocationServiceImpl();
    private final ClaimUserUpdateDao claimUserUpdateDao = new ClaimUserUpdateDaoImpl();
    private final InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private final BillCheckDao billCheckDao = new BillCheckDaoImpl();
    private final AriNotificationDao ariNotificationDao = new AriNotificationDaoImpl();
    private final RequestAriDao requestAriDao = new RequestAriDaoImpl();

    public StorageServiceImpl() {

    }

    private byte[] toByteArrayAutoClosable(BufferedImage image, String type) throws IOException {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ImageIO.write(image, type, out);
            return out.toByteArray();
        }
    }

    private byte[] getCompressedImageByte(InputStream inputStream, String fileExtension) throws IOException {
        byte[] compressedImageBytes;

        RenderedImage image = ImageIO.read(inputStream);
        ImageWriter jpegWriter = ImageIO.getImageWritersByFormatName(fileExtension).next();
        ImageWriteParam jpegWriteParam = jpegWriter.getDefaultWriteParam();
        jpegWriteParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        jpegWriteParam.setCompressionQuality(StorageServiceImpl.IMAGE_COMPRESSION_QUALITY_FACTOR);
        //     jpegWriteParam.setTiling(w,h,0,0);

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream(); ImageOutputStream output = new MemoryCacheImageOutputStream(baos)) {
            jpegWriter.setOutput(output);
            IIOImage outputImage = new IIOImage(image, null, null);
            jpegWriter.write(null, outputImage, jpegWriteParam);
            compressedImageBytes = baos.toByteArray();
        }
        jpegWriter.dispose();
        return compressedImageBytes;

    }

    private void uploadFile(InputStream inputStream, ClaimDocumentDto claimDocumentDto) throws MisynSftpFileException {

        String documentPath = getDocumentPath(claimDocumentDto.getClaimNo())
                .concat(claimDocumentDto.getDocumentName());
        claimDocumentDto.setDocumentPath(documentPath);
        try {
            DocumentApiClientUtil.uploadFile(inputStream.readAllBytes(), claimDocumentDto.getDocumentPath());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynSftpFileException("Can not be Upload");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }
    }

    @Override
    public void uploadImage(ClaimImageDto claimImageDto, UserDto user) throws MisynSftpFileException {
        Connection connection = null;
        InputStream inputStream = claimImageDto.getInputStream();

        String documentPath = getImagePath(claimImageDto.getClaimNo())
                .concat(claimImageDto.getDocumentName());
        claimImageDto.setDocumentPath(documentPath);
        claimImageDto.setDocumentTypeId(AppConstant.CLAIM_IMAGE_DOCUMENT_TYPE_ID);

        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            DocumentApiClientUtil.uploadFile(inputStream.readAllBytes(), claimImageDto.getDocumentPath());

            claimImageDao.insertMaster(connection, claimImageDto);
            this.initialLog(connection, claimImageDto.getClaimNo(), user, "imageUpload", "Image Upload", claimImageDto.getJobRefNo());
            uploadThumbnailImage(claimImageDto);
            commitTransaction(connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new MisynSftpFileException("Can not be Upload");
        } finally {
            releaseJDBCConnection(connection);
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }
    }

    private void changeAssignUser(Connection connection, Integer claimNo, String userId) {
        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateClaimAssignUser(connection, claimHandlerDto);
            claimCalculationSheetMainDao.updateCalsheetAssignUser(connection, userId, claimHandlerDto.getClaimNo());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public ClaimDocumentDto uploadDocument(Long requestFormId, ClaimDocumentDto claimDocumentDto, UserDto user) throws MisynSftpFileException, UserNotFoundException {
        InputStream inputStream = claimDocumentDto.getInputStream();
        Connection connection = null;
        String URL;
        ClaimDocumentDto claimDocumentDto1;
        String newAssignUser;
        try {
            if (claimDocumentDto.getFileTypeEnum() == FileTypeEnum.PDF) {
                this.uploadFile(inputStream, claimDocumentDto);
            } else if (claimDocumentDto.getFileTypeEnum() == FileTypeEnum.IMAGE) {
                imageToPdf(claimDocumentDto);
            } else if (claimDocumentDto.getFileTypeEnum() == FileTypeEnum.AUDIO) {
                this.uploadFile(inputStream, claimDocumentDto);
            }
            connection = getJDBCConnection();
            if (claimDocumentDto.getIsBankDetails().equals(AppConstant.YES)) {
                claimDocumentDto1 = claimDocumentDao.insertBankDetails(connection, claimDocumentDto);
                return claimDocumentDto1;
            }
            beginTransaction(connection);
            //    searchClaimDocumentDto = claimDocumentDao.searchMaster(connection, claimDocumentDto.getRefNo());
            claimDocumentDto1 = claimDocumentDao.insertMaster(connection, claimDocumentDto);
            this.updatedDocumentCheckAndMandatoryDocumentStatus(connection, claimDocumentDto.getClaimNo());
            //TODO  call notification and claim log
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimDocumentDto.getClaimNo());
            if (claimHandlerDto.getAssignUserId() != null && !claimHandlerDto.getAssignUserId().isEmpty() && user.getAccessUserType() == 100) {
                claimHandlerDao.updateStoreStatus(connection, claimDocumentDto.getClaimNo(), AppConstant.AUTO_RESTORE);
            }
            newAssignUser = claimHandlerDto.getAssignUserId();

            ClaimDocumentTypeDto claimDocumentTypeDto = claimDocumentTypeDao.searchByDocumentTypeId(connection, claimDocumentDto.getDocumentTypeId());
            URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimHandlerDto.getClaimNo())).concat("&P_TAB_INDEX=1");

            switch (user.getAccessUserType()) {
                case 27:
                case 28:
                    break;
                case 100:
                    boolean isProvideOffer = claimHandlerDao.isProvideOffer(connection, claimDocumentDto.getClaimNo());
                    StringBuilder sbMessage = new StringBuilder();
                    switch (claimHandlerDto.getClaimStatus()) {
                        case 38:
                        case 41:
                        case 42:
                        case 44:
                        case 45:
                        case 47:
                        case 48:
                        case 52:
                        case 53:
                        case 54:
                        case 55:
                        case 56:
                        case 68:
                            newAssignUser = claimHandlerDto.getDecisionMakingAssignUserId();
                            String decisionMaker = claimHandlerDto.getDecisionMakingAssignUserId();
                            if (claimUserAllocationDao.checkIfLeave(connection, AppConstant.ACCESS_LEVEL_DECISION_MAKER, AppConstant.CLAIM_HANDLER_INVESTIGATION_FUNCTION, Utility.sysDateTime(), decisionMaker)) {
                                String nextDecisionMaker = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_DECISION_MAKER, AppConstant.CLAIM_HANDLER_INVESTIGATION_FUNCTION, AppConstant.ASSIGN, decisionMaker, claimHandlerDto.getClaimNo());
                                if (null != nextDecisionMaker && !nextDecisionMaker.isEmpty()) {
                                    newAssignUser = nextDecisionMaker;
                                    claimUserUpdateDao.updateDecisionMakerByTxnId(connection, claimHandlerDto.getClaimNo(), nextDecisionMaker);
                                    URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimHandlerDto.getClaimNo())).concat("&P_TAB_INDEX=0");
                                    sbMessage.append("You have Assigned New Claim File");
                                    saveNotification(connection, claimHandlerDto.getClaimNo(), user.getUserId(), nextDecisionMaker, sbMessage.toString(), URL, NotificationPriority.LOW);
                                    saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Branch User Document Uploaded", "File Reassigned from " + decisionMaker + " to " + nextDecisionMaker);
                                    saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Documents Uploaded and Decision Maker Re-assigned from " + decisionMaker + " to " + nextDecisionMaker, user.getUserId(), Utility.sysDateTime(), nextDecisionMaker, AppConstant.YES);
                                }
                            }
                            break;
                        case 63:
                            List<ClaimCalculationSheetMainDto> list = claimCalculationSheetMainDao.searchByClaimNoAndNotInVoucherGeneratedOrPending(connection, claimHandlerDto.getClaimNo());
                            if (null != list && !list.isEmpty()) {
                                for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : list) {
                                    String spteam = claimCalculationSheetMainDto.getSpecialTeamAssignUserId();
                                    newAssignUser = spteam;
                                    String newSpecialUser = AppConstant.STRING_EMPTY;
                                    if (isProvideOffer) {
                                        if (claimUserAllocationDao.checkIfLeave(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), spteam)) {
                                            newSpecialUser = assignNextUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM, spteam, claimHandlerDto.getClaimNo());
                                        }
                                    } else {
                                        if (claimUserAllocationDao.checkIfLeave(connection, AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), spteam)) {
                                            newSpecialUser = assignNextUser(connection, AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, spteam, claimHandlerDto.getClaimNo());
                                        }
                                    }
                                    if (null != newSpecialUser && !newSpecialUser.isEmpty()) {
                                        newAssignUser = newSpecialUser;
                                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimHandlerDto.getClaimNo())).concat("&P_TAB_INDEX=0");
                                        sbMessage.append("You have Assigned New Claim File");
                                        saveNotification(connection, claimHandlerDto.getClaimNo(), user.getUserId(), newAssignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                                        saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Branch User Document Uploaded", "Cal Sheet Reassigned from " + spteam + " to " + newAssignUser);
                                        saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Documents Uploaded and Special Team User Reassigned from " + spteam + " to " + newAssignUser, user.getUserId(), Utility.sysDateTime(), newAssignUser, AppConstant.YES);
                                        claimUserUpdateDao.updateSpecialTeamByTxnId(connection, claimCalculationSheetMainDto.getCalSheetId(), newAssignUser);
                                    }
                                }
                            }
                            break;
                        default:
                            if (AppConstant.REJECTION_LETTER_TYPE != claimDocumentDto.getDocumentTypeId() && AppConstant.ESTIMATE_DOCUMENT_TYPE_ID != claimDocumentDto.getDocumentTypeId()) {
                                if (claimHandlerDto.getAssignUserId() != null && !claimHandlerDto.getAssignUserId().isEmpty()) {
                                    if (claimHandlerDto.getLossType() == 2) {
                                        String totalLoss = claimHandlerDto.getAssignUserId();
                                        if (claimUserAllocationDao.checkIfLeave(connection, AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), totalLoss)) {
                                            String nextTotalLoss = assignNextUser(connection, AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER, totalLoss, claimHandlerDto.getClaimNo());
                                            if (null != nextTotalLoss && !nextTotalLoss.isEmpty()) {
                                                newAssignUser = nextTotalLoss;
                                                claimUserUpdateDao.updateTotalLossTeamByTxnId(connection, claimHandlerDto.getClaimNo(), nextTotalLoss);
                                                URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimHandlerDto.getClaimNo())).concat("&P_TAB_INDEX=0");
                                                sbMessage.append("You have Assigned New Total Loss Claim File");
                                                saveNotification(connection, claimHandlerDto.getClaimNo(), user.getUserId(), nextTotalLoss, sbMessage.toString(), URL, NotificationPriority.LOW);
                                                saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Branch User Document Uploaded", "File Reassigned from " + totalLoss + " to " + nextTotalLoss);
                                                saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Documents Uploaded and Total Loss Claim Handler Re-assigned from " + totalLoss + " to " + nextTotalLoss, user.getUserId(), Utility.sysDateTime(), nextTotalLoss, AppConstant.YES);

                                                boolean ifHaveCalSheet = claimUserUpdateDao.selectCalsheetAssignUser(connection, claimHandlerDto.getClaimNo());
                                                if (ifHaveCalSheet) {
                                                    claimUserUpdateDao.updateCalsheetAssignUser(connection, nextTotalLoss, claimHandlerDto.getClaimNo());
                                                }
                                            }
                                        }
                                    } else {
                                        String claimHandler = claimHandlerDto.getAssignUserId();
                                        String newClaimHandler = AppConstant.STRING_EMPTY;
                                        if (isProvideOffer) {
                                            if (claimUserAllocationDao.checkIfLeave(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), claimHandler)) {
                                                newClaimHandler = assignNextUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, claimHandler, claimHandlerDto.getClaimNo());
                                            }
                                        } else {
                                            if (claimUserAllocationDao.checkIfLeave(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), claimHandler)) {
                                                newClaimHandler = assignNextUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, claimHandler, claimHandlerDto.getClaimNo());
                                            }
                                        }
                                        if (null != newClaimHandler && !newClaimHandler.isEmpty()) {
                                            newAssignUser = newClaimHandler;
                                            changeAssignUser(connection, claimHandlerDto.getClaimNo(), newAssignUser);
                                            URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimHandlerDto.getClaimNo())).concat("&P_TAB_INDEX=0");
                                            sbMessage.append("You have Assigned New Claim File");
                                            saveNotification(connection, claimHandlerDto.getClaimNo(), user.getUserId(), newAssignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                                            saveClaimsLogs(connection, claimHandlerDto.getClaimNo(), user, "Branch User Document Uploaded", "File Reassigned from " + claimHandler + " to " + newAssignUser);
                                            saveClaimProcessFlow(connection, claimHandlerDto.getClaimNo(), 0, "Document Uploaded Claim handler on leave & file reassigned from " + claimHandler + " to " + newAssignUser, user.getUserId(), Utility.sysDateTime(), newAssignUser, AppConstant.YES);
                                        }
                                    }
                                }
                            }
                            break;
                    }
                default:
                    if (claimDocumentTypeDto.getDocumentTypeId() == AppConstant.DO_BILL_DOCUMENT_TYPE_ID) {
                        DocumentNotificationDto documentNotificationDto = setNotificationDTO(requestFormId, claimDocumentDto.getClaimNo(), claimDocumentTypeDto.getDocumentTypeId(), user.getUserId(), claimHandlerDto.getSupplyOrderAssignUser());
                        notificationDao.savePendingNotification(connection, documentNotificationDto);
                    } else if (claimDocumentTypeDto.getDocumentTypeId() == AppConstant.GARAGE_BILL_DOCUMENT_TYPE_ID) {
                        String userId = claimCalculationSheetMainDao.getSpecialTeamAssignUserId(connection, claimDocumentDto.getClaimNo(), 65, 5);
                        DocumentNotificationDto documentNotificationDto = setNotificationDTO(requestFormId, claimDocumentDto.getClaimNo(), claimDocumentTypeDto.getDocumentTypeId(), user.getUserId(), userId);
                        notificationDao.savePendingNotification(connection, documentNotificationDto);
                    } else if (claimDocumentTypeDto.getDocumentTypeId() == AppConstant.NO_OBJECTION_LETTER_DOCUMENT_TYPE_ID) {
                        this.updateDefineDocument(connection, requestFormId, user, claimDocumentDto, claimDocumentTypeDto, claimHandlerDto, newAssignUser, URL);
                        boolean isFoundPendingDocUploadNoObjection = claimCalculationSheetMainDao.isFoundPendingDocUploadNoObjection(connection, claimDocumentDto.getClaimNo());
                        if (isFoundPendingDocUploadNoObjection) {
                            claimCalculationSheetMainDao.updateNoObjectionDocRefNo(connection, claimDocumentDto.getClaimNo(), claimDocumentDto.getRefNo());
                        }
                    } else if (claimDocumentTypeDto.getDocumentTypeId() == AppConstant.PREMIUM_OUTSTANDING_CONFIRMATION_DOCUMENT_TYPE_ID) {
                        this.updateDefineDocument(connection, requestFormId, user, claimDocumentDto, claimDocumentTypeDto, claimHandlerDto, newAssignUser, URL);
                        boolean isFoundPendingDocUploadPremiumOutstanding = claimCalculationSheetMainDao.isFoundPendingDocUploadPremiumOutstanding(connection, claimDocumentDto.getClaimNo());
                        if (isFoundPendingDocUploadPremiumOutstanding) {
                            claimCalculationSheetMainDao.updatePremiumOutstandingDocRefNo(connection, claimDocumentDto.getClaimNo(), claimDocumentDto.getRefNo());
                        }
                    } else {

                        this.updateDefineDocument(connection, requestFormId, user, claimDocumentDto, claimDocumentTypeDto, claimHandlerDto, newAssignUser, URL);
                    }

            }
            this.saveClaimsLogs(connection, claimDocumentDto.getClaimNo(), user, "Document Upload", claimDocumentTypeDto.getDocumentTypeName().concat(" has been uploaded."));
            this.initialLog(connection, claimDocumentDto.getClaimNo(), user, "Document Upload", claimDocumentTypeDto.getDocumentTypeName().concat(" has been uploaded."), claimDocumentDto.getJobRefNo());

            commitTransaction(connection);
        } catch (UserNotFoundException e) {
            LOGGER.error(e.getMessage(), e);
            rollbackTransaction(connection);
            throw e;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            rollbackTransaction(connection);
            throw new MisynSftpFileException("Can not be Upload");
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimDocumentDto1;
    }

    private DocumentNotificationDto setNotificationDTO(Long formID, int claimNo, int docType, String inpUsr, String assignUser) {
        DocumentNotificationDto documentNotificationDto = new DocumentNotificationDto();
        documentNotificationDto.setFormRelId(formID);
        documentNotificationDto.setClaimNo(claimNo);
        documentNotificationDto.setDocType(docType);
        documentNotificationDto.setUpdateDate(Utility.sysDateTime());
        documentNotificationDto.setInputUser(inpUsr);
        documentNotificationDto.setAssignUser(assignUser);
        documentNotificationDto.setStatus(AppConstant.NO);
        return documentNotificationDto;
    }

    private String assignNextUser(Connection connection, Integer accessLevel, String userId, Integer claimNo) {
        try {
            return claimUserAllocationService.getNextAssignUser(connection, accessLevel, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, userId, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private void updateDefineDocument(Connection connection, long formId, UserDto user, ClaimDocumentDto claimDocumentDto, ClaimDocumentTypeDto claimDocumentTypeDto, ClaimHandlerDto claimHandlerDto, String newAssignUser, String url) throws Exception {

        StringBuilder sbMessage = new StringBuilder();

        try {
            switch (user.getAccessUserType()) {
                case 20:
                case 22:
                case 27:
                    break;
                case 100://Only branch user's upload
                    ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
                    claimWiseDocumentDto.setClaimNo(claimDocumentDto.getClaimNo());
                    claimWiseDocumentDto.setInpUserId(user.getUserId());
                    claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
                    claimWiseDocumentDto.setDocumentTypeId(claimDocumentDto.getDocumentTypeId());
                    claimWiseDocumentDto.setIsMandatory(AppConstant.YES);
                    claimWiseDocumentDto.setDocReqFrom(5);
                    claimWiseDocumentDao.updateDefineDocumentByDocumentTypeId(connection, claimWiseDocumentDto);

                    List<Integer> engineeringDocuments = billCheckDao.getEngineeringDocuments(connection);
                    if (!engineeringDocuments.contains(claimDocumentDto.getDocumentTypeId())) {
                        sbMessage.append("You have received new document (").append(claimDocumentTypeDto.getDocumentTypeName()).append(")");
                        if (AppConstant.ESTIMATE_DOCUMENT_TYPE_ID == claimDocumentTypeDto.getDocumentTypeId()) {
                            List<UserDto> tecnicalCordinatorList;
                            List<UserDto> tecnicalCordinatorNonAriList;
                            tecnicalCordinatorList = userDao.getUSersByAccessUsrType(connection, AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE);
                            tecnicalCordinatorNonAriList = userDao.getUSersByAccessUsrType(connection, AppConstant.TECHNICAL_CORDINATOR_NON_ARI_ACCESSUSRTYPE);
                            tecnicalCordinatorList.addAll(tecnicalCordinatorNonAriList);

                            for (UserDto userDto : tecnicalCordinatorList) {
                                DocumentNotificationDto documentNotificationDto = setNotificationDTO(formId, claimDocumentDto.getClaimNo(), claimDocumentDto.getDocumentTypeId(), user.getUserId(), userDto.getUserId());
                                notificationDao.savePendingNotification(connection, documentNotificationDto);
                            }
                        } else if (AppConstant.DOCUMENT_TYPE_FINAL_BILL.equals(claimDocumentDto.getDocumentTypeId()) || AppConstant.DOCUMENT_TYPE_SPARE_PARTS_BILL.equals(claimDocumentDto.getDocumentTypeId()) ||
                                AppConstant.DOCUMENT_TYPE_lABOUR_BILL.equals(claimDocumentDto.getDocumentTypeId()) || AppConstant.DOCUMENT_TYPE_TAX_INVOICE.equals(claimDocumentDto.getDocumentTypeId())) {
                            if (ariNotificationDao.isAvailableUnsentNotification(connection, claimDocumentDto.getClaimNo())) {
                                ariNotificationDao.removeAvailableRecordsByClaimNo(connection, claimDocumentDto.getClaimNo());
                            }
                            switch (requestAriDao.searchByClaimNoisSalvageORARIRequestedBySpcOrScr(connection, claimDocumentDto.getClaimNo())) {
                                case AppConstant.ARI_REQUEST:
                                case AppConstant.ARI:
                                case AppConstant.SALVAGE_REQUEST:
                                case AppConstant.ARI_SALVAGE:
                                case AppConstant.COLLECT_SALVAGE:
                                    List<UserDto> userList = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE);
                                    List<UserDto> userListAriOnly = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE);
                                    userList.addAll(userListAriOnly);
                                    for (UserDto userDto : userList) {
                                        ariNotificationDao.saveAriNotification(connection, claimDocumentDto.getClaimNo(), user.getUserId(), userDto.getUserId());
                                    }
                                    break;
                            }
                            DocumentNotificationDto documentNotificationDto = setNotificationDTO(formId, claimDocumentDto.getClaimNo(), claimDocumentDto.getDocumentTypeId(), user.getUserId(), newAssignUser);
                            notificationDao.savePendingNotification(connection, documentNotificationDto);

                            saveClaimsLogs(connection, claimDocumentDto.getClaimNo(), user, "Ari Request", "Final bill received to branch");
                        } else if (!user.getUserId().equalsIgnoreCase(claimHandlerDto.getAssignUserId())) {
                            if (null == newAssignUser || newAssignUser.isEmpty()) {
                                if (null != claimHandlerDto.getDecisionMakingAssignUserId()
                                        && !claimHandlerDto.getDecisionMakingAssignUserId().isEmpty()) {
                                    newAssignUser = claimHandlerDto.getDecisionMakingAssignUserId();
                                } else {
                                    newAssignUser = claimHandlerDto.getInitLiabilityAssignUserId();
                                }
                                if (null == newAssignUser || newAssignUser.isEmpty()) {
                                    List<String> assignRteUsersForClaim = inspectionDetailsDao.getAssignRteUsersForClaim(connection, claimHandlerDto.getClaimNo());
                                    for (String newUser : assignRteUsersForClaim) {
                                        DocumentNotificationDto documentNotificationDto = setNotificationDTO(formId, claimDocumentDto.getClaimNo(), claimDocumentDto.getDocumentTypeId(), user.getUserId(), newUser);
                                        notificationDao.savePendingNotification(connection, documentNotificationDto);
                                    }
                                } else {
                                    DocumentNotificationDto documentNotificationDto = setNotificationDTO(formId, claimDocumentDto.getClaimNo(), claimDocumentDto.getDocumentTypeId(), user.getUserId(), newAssignUser);
                                    notificationDao.savePendingNotification(connection, documentNotificationDto);
                                }
                            } else {
                                DocumentNotificationDto documentNotificationDto = setNotificationDTO(formId, claimDocumentDto.getClaimNo(), claimDocumentDto.getDocumentTypeId(), user.getUserId(), newAssignUser);
                                notificationDao.savePendingNotification(connection, documentNotificationDto);
                            }
                        }
                        break;
                    }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public InputStream viewUploadDocument(Integer documentRefNo) throws MisynSftpFileException {
        Connection connection = null;
        InputStream inputStream = null;
        try {
            connection = getJDBCConnection();
            ClaimDocumentDto claimDocumentDto = claimDocumentDao.searchMaster(connection, documentRefNo);
            if (claimDocumentDto != null) {
                inputStream = DocumentApiClientUtil.streamContent(DOCUMENT_SERVICE_API_URL.concat("/v1/file/pdf?objectName=").concat(claimDocumentDto.getDocumentPath()));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynSftpFileException("Can not be view");
        } finally {
            releaseJDBCConnection(connection);
        }
        return inputStream;
    }


    @Override
    public InputStream viewUploadImage(Integer imageRefNo) throws MisynSftpFileException {
        Connection connection = null;
        InputStream inputStream = null;
        try {
            connection = getJDBCConnection();
            ClaimImageDto claimImageDto = claimImageDao.searchMaster(connection, imageRefNo);
            if (claimImageDto != null) {
                inputStream = DocumentApiClientUtil.streamContent(DOCUMENT_SERVICE_API_URL.concat("/v1/file/image?objectName=").concat(claimImageDto.getDocumentPath()));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynSftpFileException("Can not be view");
        } finally {
            releaseJDBCConnection(connection);
        }
        return inputStream;
    }

    @Override
    public synchronized InputStream viewThumbUploadImage(Integer imageRefNo) throws MisynSftpFileException {
        Connection connection = null;
        InputStream inputStream = null;
        try {
            connection = getJDBCConnection();
            ClaimImageDto claimImageDto = claimImageDao.searchMaster(connection, imageRefNo);
            if (claimImageDto != null) {
                String documentPath = getImageThumbPath(claimImageDto.getClaimNo())
                        .concat(claimImageDto.getDocumentName());
                inputStream = DocumentApiClientUtil.streamContent(DOCUMENT_SERVICE_API_URL.concat("/v1/file/image?objectName=").concat(documentPath));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynSftpFileException("Can not be view");
        } finally {
            releaseJDBCConnection(connection);
        }
        return inputStream;
    }

    @Override
    public ClaimImageDto getClaimImageDto(Integer imageRefNo) {
        Connection connection = null;
        ClaimImageDto claimImageDto = null;
        try {
            connection = getJDBCConnection();
            claimImageDto = claimImageDao.searchMaster(connection, imageRefNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());

        } finally {
            releaseJDBCConnection(connection);
        }
        return claimImageDto;
    }

    @Override
    public boolean deleteImages(String images, Integer claimNo, UserDto user, Integer jobRefNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String[] imagesArray = images.split(",");

            int index = 0;
            for (String image : imagesArray) {
                if (AppConstant.STRING_EMPTY.equalsIgnoreCase(image)) {
                    throw new Exception("Can not be deleted");
                }
                ClaimImageDto claimImageByRefNo = claimImageDao.getClaimImageByRefNo(connection, Integer.parseInt(image));
                if (Objects.nonNull(claimImageByRefNo)) {
                    DocumentApiClientUtil.deleteFile(claimImageByRefNo.getDocumentPath());
                    claimImageDao.deleteMaster(connection, image);
                    this.initialLog(connection, claimNo, user, "Image Delete", "Delete Image Ref Id : ".concat(image), jobRefNo);
                }
                index++;
            }
            if (index == imagesArray.length) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;

    }

    @Override
    public boolean deleteDocuments(String docs, Integer documentTypeId, Integer claimNo, Integer jobRefNo, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String[] docsArray = docs.split(",");
            int index = 0;
            for (String doc : docsArray) {
                if (AppConstant.STRING_EMPTY.equalsIgnoreCase(doc)) {
                    throw new Exception("Can not be deleted");
                }
                ClaimDocumentDto searchClaimDocumentDto = claimDocumentDao.searchMaster(connection, Integer.parseInt(doc));
                if (searchClaimDocumentDto != null) {
                    DocumentApiClientUtil.deleteFile(searchClaimDocumentDto.getDocumentPath());
                    claimDocumentDao.deleteMaster(connection, Integer.parseInt(doc), documentTypeId);
                    documentHistoryDao.deleteHistory(connection, searchClaimDocumentDto.getRefNo());

                    this.initialLog(connection, claimNo, user, "Document Delete", "Delete Document Type :"
                            .concat(searchClaimDocumentDto.getClaimDocumentTypeDto().getDocumentTypeName()
                                    .concat(" , Document File Name : ")
                                    .concat(searchClaimDocumentDto.getDocumentName())), jobRefNo);
                }
                index++;
            }

            if (index == docsArray.length) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public void updatedDocumentCheckAndMandatoryDocumentStatus(Integer claimNo) throws MisynJDBCException {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            this.updatedDocumentCheckAndMandatoryDocumentStatus(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("Can not be saved");
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updatedDocumentCheckAndMandatoryDocumentStatus(Connection connection, Integer claimNo) throws MisynJDBCException {
        YesNoWantDecideEnum upload = YesNoWantDecideEnum.NO;
        YesNoWantDecideEnum check = YesNoWantDecideEnum.NO;
        try {
            List<ClaimWiseDocumentDto> claimWiseDocumentDtoList = claimWiseDocumentDao.searchAllByClaimNoAndIsMandatory(connection, claimNo, AppConstant.YES);
            for (ClaimWiseDocumentDto claimWiseDocumentDto : claimWiseDocumentDtoList) {
                Integer documentTypeId = claimWiseDocumentDto.getDocumentTypeId();
                boolean isUpload = claimDocumentDao.isUploadAllMandatoryDocument(connection, claimNo, documentTypeId);
                //   Boolean isChecked = claimDocumentDao.isCheckedAllMandatoryDocument(connection, claimNo, documentTypeId);
                if (isUpload) {
                    upload = YesNoWantDecideEnum.YES;
                } else {
                    upload = YesNoWantDecideEnum.NO;
                    break;
                }
            }
            claimHandlerDao.updateUploadAllDocumentStatus(connection, claimNo, upload);

            for (ClaimWiseDocumentDto claimWiseDocumentDto : claimWiseDocumentDtoList) {
                Integer documentTypeId = claimWiseDocumentDto.getDocumentTypeId();
                boolean isChecked = claimDocumentDao.isCheckedAllMandatoryDocument(connection, claimNo, documentTypeId);
                if (isChecked) {
                    check = YesNoWantDecideEnum.YES;
                } else {
                    check = YesNoWantDecideEnum.NO;
                    break;
                }
            }
            claimHandlerDao.updateCheckedMandatoryDocumentStatus(connection, claimNo, check);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("Can not be saved");
        }
    }

    @Override
    public InputStream viewUploadAudio(Integer refNo) throws MisynSftpFileException {
        Connection connection = null;
        InputStream inputStream = null;
        try {
            connection = getJDBCConnection();
            ClaimDocumentDto claimDocumentDto = claimDocumentDao.searchMaster(connection, refNo);
            if (claimDocumentDto != null) {
                inputStream = DocumentApiClientUtil.streamContent(DOCUMENT_SERVICE_API_URL.concat("/v1/file/audio?objectName=").concat(claimDocumentDto.getDocumentPath()));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynSftpFileException("Can not be view");
        } finally {
            releaseJDBCConnection(connection);
        }
        return inputStream;
    }
    private void imageToPdf(ClaimDocumentDto claimDocumentDto) throws Exception {
        PDDocument document = new PDDocument();
        InputStream in = getResizeImageInputStream(claimDocumentDto.getInputStream(), claimDocumentDto.getFileExtension());
        BufferedImage bimg;
        ByteArrayOutputStream baos = null;
        float x = 0;
        float y = 0;
        String documentNameWithoutExtension = claimDocumentDto.getDocumentName()
                .substring(0, claimDocumentDto.getDocumentName().lastIndexOf(AppConstant.STRING_DOT));
        String tempPdfFile = tempFileDirectory
                .concat(documentNameWithoutExtension)
                .concat(AppConstant.STRING_DOT)
                .concat(AppConstant.PDF);
        String formatName = AppConstant.JPG;
        try {
            if (in != null) {
                formatName = switch (claimDocumentDto.getFileExtension()) {
                    case AppConstant.PNG -> AppConstant.PNG;
                    case AppConstant.JPEG -> AppConstant.JPEG;
                    case AppConstant.JPG -> AppConstant.JPG;
                    default -> formatName;
                };

                bimg = ImageIO.read(in);
                float width = bimg.getWidth();
                float height = bimg.getHeight();
                PDPage page = new PDPage(new PDRectangle(width, height));
                document.addPage(page);
                baos = new ByteArrayOutputStream();
                ImageIO.write(bimg, formatName, baos);
                baos.flush();
                byte[] imageInByte = baos.toByteArray();
                PDImageXObject pdImageXObject = PDImageXObject.createFromByteArray(document, imageInByte, tempPdfFile);
                PDPageContentStream contentStream = new PDPageContentStream(document, page);
                contentStream.drawImage(pdImageXObject, x, y);
                contentStream.close();
                in.close();
                document.save(tempPdfFile);
                document.close();

                InputStream inputPdf = new FileInputStream(tempPdfFile);
                claimDocumentDto.setDocumentName(documentNameWithoutExtension.concat(AppConstant.STRING_DOT).concat(AppConstant.PDF));
                this.uploadFile(inputPdf, claimDocumentDto);
                inputPdf.close();
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not be upload", e);
        } finally {
            if (in != null) {
                in.close();
            }
            if (baos != null) {
                baos.close();
            }
            document.close();
            tempFileDelete(tempPdfFile);
        }
    }

    private void tempFileDelete(String tempPdfFilePath) {
        try {
            Path filePath = Paths.get(tempPdfFilePath);
            Files.delete(filePath);
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void uploadThumbnailImage(ClaimImageDto claimImageDto) throws MisynSftpFileException {
        try (InputStream inputStream = claimImageDto.getThumbInputStream()) {
            String documentPath = getImageThumbPath(claimImageDto.getClaimNo())
                    .concat(claimImageDto.getDocumentName());
            InputStream imageThumbInputStream = getImageThumbInputStream(inputStream, claimImageDto.getFileExtension());
            DocumentApiClientUtil.uploadFile(imageThumbInputStream.readAllBytes(), documentPath);
            imageThumbInputStream.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynSftpFileException("Can not be Upload");
        }
    }

    private InputStream getImageThumbInputStream(InputStream inputStream, String fileExtension) {
        InputStream isResize;
        InputStream isResizeAndCompress = null;
        try {
            BufferedImage originalImage = ImageIO.read(inputStream);
            int type = originalImage.getType() == 0 ? BufferedImage.TYPE_INT_ARGB : originalImage.getType();
            BufferedImage thumb = new BufferedImage(100, 100, type);

            Graphics2D g2d = (Graphics2D) thumb.getGraphics();
            g2d.drawImage(originalImage, 0, 0, thumb.getWidth() - 1, thumb.getHeight() - 1, 0, 0,
                    originalImage.getWidth() - 1, originalImage.getHeight() - 1, null);
            g2d.dispose();
            isResize = new ByteArrayInputStream(toByteArrayAutoClosable(thumb, fileExtension));
            isResizeAndCompress = new ByteArrayInputStream(getCompressedImageByte(isResize, fileExtension));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return isResizeAndCompress;
    }

    private InputStream getResizeImageInputStream(InputStream inputStream, String fileExtension) {
        InputStream isResize;
        InputStream isResizeAndCompress = null;
        try {
            BufferedImage originalImage = ImageIO.read(inputStream);
            int type = originalImage.getType() == 0 ? BufferedImage.TYPE_INT_ARGB : originalImage.getType();
            int scaledWidth = (int) (originalImage.getWidth() * StorageServiceImpl.IMAGE_RESIZE_PERCENT);
            int scaledHeight = (int) (originalImage.getHeight() * StorageServiceImpl.IMAGE_RESIZE_PERCENT);
            BufferedImage resizeImage = new BufferedImage(scaledWidth, scaledHeight, type);

            Graphics2D g2d = (Graphics2D) resizeImage.getGraphics();
            g2d.drawImage(originalImage, 0, 0, resizeImage.getWidth() - 1, resizeImage.getHeight() - 1, 0, 0,
                    originalImage.getWidth() - 1, originalImage.getHeight() - 1, null);
            g2d.dispose();

            g2d.setComposite(AlphaComposite.Src);

            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                    RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                    RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                    RenderingHints.VALUE_ANTIALIAS_ON);

            isResize = new ByteArrayInputStream(toByteArrayAutoClosable(resizeImage, fileExtension));
            isResizeAndCompress = new ByteArrayInputStream(getCompressedImageByte(isResize, fileExtension));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return isResizeAndCompress;
    }


}
