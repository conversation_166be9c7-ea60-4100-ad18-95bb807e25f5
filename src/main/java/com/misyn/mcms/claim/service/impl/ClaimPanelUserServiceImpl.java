package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimClaimPanelUserDao;
import com.misyn.mcms.claim.dao.impl.ClaimClaimPanelUserDaoImpl;
import com.misyn.mcms.claim.dto.ClaimClaimPanelUserDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimPanelUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class ClaimPanelUserServiceImpl extends AbstractBaseService<ClaimPanelUserServiceImpl> implements ClaimPanelUserService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserServiceImpl.class);
    private ClaimClaimPanelUserDao claimClaimPanelUserDao = new ClaimClaimPanelUserDaoImpl();

    @Override
    public boolean saveClaimPanelUser(ClaimClaimPanelUserDto claimClaimPanelUserDto, String userPanelIds) throws Exception {
        Connection connection = getJDBCConnection();
        boolean isSaved = false;
        try {
            beginTransaction(connection);

            if (claimClaimPanelUserDto.getId() == 0) {
                for (String i : userPanelIds.split(",")) {
                    boolean alreadySavedInPanel = claimClaimPanelUserDao.isAlreadySavedInPanel(connection, Integer.parseInt(i), claimClaimPanelUserDto.getUserId());
                    if (!alreadySavedInPanel) {
                        claimClaimPanelUserDto.setUserPanelId(Integer.parseInt(i));
                        claimClaimPanelUserDao.insertMaster(connection, claimClaimPanelUserDto);
                        isSaved = true;
                    }
                }

            } else {
                claimClaimPanelUserDao.updateMaster(connection, claimClaimPanelUserDto);
                isSaved = true;
            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return isSaved;
    }

    @Override
    public void updateClaimPanelUser(ClaimClaimPanelUserDto claimClaimPanelUserDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            claimClaimPanelUserDao.updateMaster(connection, claimClaimPanelUserDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ClaimClaimPanelUserDto searchClaimPanelUser(Object id) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        ClaimClaimPanelUserDto claimClaimPanelUserDto1;
        try {
            beginTransaction(connection);
            claimClaimPanelUserDto1 = claimClaimPanelUserDao.searchMaster(connection, id);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimClaimPanelUserDto1;
    }

    @Override
    public List<ClaimClaimPanelUserDto> ClaimPanelUserDtolist() {
        return null;
    }

    @Override
    public DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimClaimPanelUserDao.getDataGridDto(connection, parameterList, drawRandomId, start, length,
                    orderType, orderField);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

}
