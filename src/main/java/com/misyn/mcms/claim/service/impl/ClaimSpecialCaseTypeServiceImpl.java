package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.ClaimSpecialCaseTypeDao;
import com.misyn.mcms.claim.dao.impl.ClaimSpecialCaseTypeDaoImpl;
import com.misyn.mcms.claim.dto.ClaimSpecialCaseTypeDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.ClaimSpecialCaseTypeService;
import com.misyn.mcms.utility.AppConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.List;

public class ClaimSpecialCaseTypeServiceImpl extends AbstractBaseService<ClaimSpecialCaseTypeServiceImpl> implements ClaimSpecialCaseTypeService {
    private static final Logger LOGGER = Logger.getLogger(ClaimSpecialCaseTypeServiceImpl.class);
    private final ClaimSpecialCaseTypeDao claimSpecialCaseTypeDao = new ClaimSpecialCaseTypeDaoImpl();


    private void validateFields(ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws Exception {
        if (StringUtils.isNotBlank(claimSpecialCaseTypeDto.getRemark()) && claimSpecialCaseTypeDto.getRemark().length() > 255) {
            throw new MisynJDBCException("Remark exceeds the maximum number of Characters");
        }
        if (StringUtils.isNotBlank(claimSpecialCaseTypeDto.getClaimType()) && claimSpecialCaseTypeDto.getClaimType().length() > 255) {
            throw new MisynJDBCException("Claim Type exceeds the maximum number of Characters");
        }
        claimSpecialCaseTypeDto.setRecordStatus(AppConstant.ACTIVE_STATUS);
    }

    private void isExistClaimNo(Connection connection, String claimNo) throws Exception {
        Boolean existClaimNo = claimSpecialCaseTypeDao.isExistClaimNo(connection, claimNo);
        if (Boolean.FALSE.equals(existClaimNo)) {
            throw new MisynJDBCException("Please enter a valid Claim No.");
        }
    }

    @Override
    public ClaimSpecialCaseTypeDto saveClaimSpecialCaseType(ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws MisynJDBCException {
        Connection connection = null;
        ClaimSpecialCaseTypeDto savedDto = null;
        try {
            connection = getJDBCConnection();
            if (null == claimSpecialCaseTypeDto.getId() || claimSpecialCaseTypeDto.getId().equals(0)) {
                validateFields(claimSpecialCaseTypeDto);
                isExistClaimNo(connection, claimSpecialCaseTypeDto.getClaimNo());
                claimSpecialCaseTypeDto.setInputDateTime(LocalDateTime.now());
                claimSpecialCaseTypeDto.setLastModifiedDateTime(LocalDateTime.now());
                savedDto = claimSpecialCaseTypeDao.insertClaimSpecialCaseType(connection, claimSpecialCaseTypeDto);
            }
            return savedDto;
        } catch (Exception e) {
            LOGGER.error(e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ClaimSpecialCaseTypeDto updateClaimSpecialCaseType(ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws MisynJDBCException {
        Connection connection = null;
        ClaimSpecialCaseTypeDto savedDto = null;
        try {
            connection = getJDBCConnection();
            if (null != claimSpecialCaseTypeDto.getId() || !claimSpecialCaseTypeDto.getId().equals(0)) {
                validateFields(claimSpecialCaseTypeDto);
                isExistClaimNo(connection, claimSpecialCaseTypeDto.getClaimNo());
                claimSpecialCaseTypeDto.setLastModifiedDateTime(LocalDateTime.now());
                savedDto = claimSpecialCaseTypeDao.updateClaimSpecialCaseType(connection, claimSpecialCaseTypeDto);
            }
            return savedDto;
        } catch (Exception e) {
            LOGGER.error(e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ClaimSpecialCaseTypeDto getClaimSpecialCaseType(Integer id) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimSpecialCaseTypeDao.getClaimSpecialCaseType(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ClaimSpecialCaseTypeDto> getClaimSpecialCaseTypeList() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimSpecialCaseTypeDao.getClaimSpecialCaseTypeList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void deleteClaimSpecialCaseType(Integer id, String userName) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            claimSpecialCaseTypeDao.deleteClaimSpecialCaseType(connection, id, userName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public Boolean isExistClaimNo(String claimNo) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimSpecialCaseTypeDao.isExistClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getClaimSpecialCaseTypeSDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimSpecialCaseTypeDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField);

        } catch (Exception e) {
            LOGGER.error(e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

}
