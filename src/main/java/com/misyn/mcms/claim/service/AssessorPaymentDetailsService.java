package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.AssessorPaymentDetailsDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by a<PERSON><PERSON> on 7/24/18.
 */
public interface AssessorPaymentDetailsService {

    List<AssessorPaymentDetailsDto> searchAll(String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, boolean isGridSearch, String claimType, String policyChannelType);

    DataGridDto getPaymentDataGridDto(Map<String, Object> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    boolean updateStausByIdAndStatus(String ids, UserDto user, String from_date, String to_date) throws Exception;

    void updateRejectedStausByIdAndStatus(String ids, String remark, UserDto user) throws Exception;

    BigDecimal getTotalAmount(String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, String claimType, String policyChannelType);

    String getPaymentStatus(Integer keyId);

    void updateHoldPayment(Integer keyId, UserDto user, Integer claimNo, String assessorCode) throws Exception;

    List<UserDto> getAssessorListByReportingRte(String rteName);
}
