package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.CityDao;
import com.misyn.mcms.claim.dao.impl.CityDaoImpl;
import com.misyn.mcms.claim.dto.CityDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.CityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class CityServiceImpl extends AbstractBaseService<CityServiceImpl> implements CityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CityServiceImpl.class);
    private CityDao cityDao = new CityDaoImpl();

    @Override
    public CityDto insert(CityDto cityDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public CityDto update(CityDto cityDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public CityDto delete(CityDto cityDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public CityDto updateAuthPending(CityDto cityDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public CityDto deleteAuthPending(CityDto cityDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public CityDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public CityDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public CityDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public CityDto search(Object id) throws Exception {
        CityDto cityDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            cityDto = cityDao.searchMaster(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return cityDto;
    }

    @Override
    public CityDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<CityDto> searchAll() throws Exception {
        List<CityDto> list = null;
        Connection connection = getJDBCConnection();
        try {
            list = cityDao.searchAll(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<CityDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public List<CityDto> getCityListByDistrictCode(String divisionCode) throws Exception {
        List<CityDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = cityDao.getCityListByDistrictCode(connection, divisionCode);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }
}
