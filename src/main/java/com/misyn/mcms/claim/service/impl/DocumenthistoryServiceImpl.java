package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.DocumentHistoryDao;
import com.misyn.mcms.claim.dao.impl.DocumentHistoryDaoImpl;
import com.misyn.mcms.claim.dto.DocumentHistoryDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.DocumenthistoryService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
public class DocumenthistoryServiceImpl extends AbstractBaseService<DistrictServiceImpl> implements DocumenthistoryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DistrictServiceImpl.class);

    private DocumentHistoryDao documentHistoryDao = new DocumentHistoryDaoImpl();

    @Override
    public boolean isDocumentDuplicated(DocumentHistoryDto documentHistoryDto) throws Exception {

        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String currentDatetime = Utility.sysDate(AppConstant.DATE_TIME_FORMAT);
            String fiveMinBeforedatetime = Utility.addMinute(currentDatetime, -5);
            return documentHistoryDao.isDuplicateDocument(connection, documentHistoryDto, fiveMinBeforedatetime, currentDatetime);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public boolean insert(DocumentHistoryDto documentHistoryDto) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return documentHistoryDao.insertMaster(connection, documentHistoryDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }
}
