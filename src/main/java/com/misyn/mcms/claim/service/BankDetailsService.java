package com.misyn.mcms.claim.service;

import com.misyn.mcms.admin.admin.dto.BankDetailsDto;
import com.misyn.mcms.admin.admin.dto.BankDetailsSearchResponseDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeNameDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.ListBoxItem;

import java.util.List;
import java.util.Map;

/**
 * Created by a<PERSON><PERSON> on 6/20/18.
 */
public interface BankDetailsService {
    Map<Integer,String> getInstrumentTypeList() throws Exception;
    List<ClaimCalculationSheetPayeeNameDto> getPayeeTypeList() throws Exception;
    List<ListBoxItem> getPayeeNameListById(BankDetailsDto bankDetailsDto) throws Exception;
    Integer saveBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    void deleteBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    Integer updateBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    void updateVerification(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    void updateRejection(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    void updateDocRefNo(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    Integer getUploadedDocRef(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    List<BankDetailsDto> getPrevBankDetails (BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    BankDetailsDto getInsuredBankDetails (BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    BankDetailsSearchResponseDto getBankDetails (BankDetailsDto bankDetailsDto, UserDto user, Integer payeeType, String payeeName) throws Exception;
    boolean isAlreadyExistBankDetails(BankDetailsDto bankDetailsDto, UserDto user) throws Exception;
    boolean isBankDetailsVerified(Integer claimNo, Integer calSheetId, UserDto user) throws Exception;

}
