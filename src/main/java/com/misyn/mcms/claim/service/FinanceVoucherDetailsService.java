package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.FinanceReasonUpdateDetailsDto;
import com.misyn.mcms.claim.dto.VoucherDetailsDto;
import com.misyn.mcms.utility.ListBoxItem;

import java.util.List;

public interface FinanceVoucherDetailsService {

    List<VoucherDetailsDto> getVoucherDetailsDtoList(String claimNo, String policyChannelType);

    List<VoucherDetailsDto> getVoucherDetailsDtoListByClaimNo(String claimNo, String policyChannelType);

    List<VoucherDetailsDto> getVoucherDetailsDtoListByVehicleNo(String vehicleNo, String policyChannelType);

    List<VoucherDetailsDto> getVoucherDetailsDtoListByVoucherNo(String voucherNo, String policyChannelType);

    List<VoucherDetailsDto> getVoucherDetailsDtoListByFromDateAndToDate(String fromDate, String toDate, String policyChannelType);

    List<VoucherDetailsDto> getVoucherDetailsDtoListByFromDateAndToDateAndStatus(String fromDate, String toDate, String status, String policyChannelType);

    List<ListBoxItem> getFinanceReasonList();

    int saveFinanceReason(FinanceReasonUpdateDetailsDto financeReasonUpdateDetailsDto) throws Exception;

    String getPolicyChannelType(Integer claimNo) throws Exception;

    String getPolicyChannelTypeByVehicleNo(String vehicleNo) throws Exception;
}
