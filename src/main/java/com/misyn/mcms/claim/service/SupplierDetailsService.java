package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.SupplierDetailsMasterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface SupplierDetailsService {
    SupplierDetailsMasterDto insertSupplierDetail(SupplierDetailsMasterDto supplierDetailsMasterDto) throws MisynJDBCException;

    SupplierDetailsMasterDto updateSupplierDetail(SupplierDetailsMasterDto supplierDetailsMasterDto) throws MisynJDBCException;

    SupplierDetailsMasterDto searchSupplierDetail(Object id) throws MisynJDBCException;

    List<SupplierDetailsMasterDto> SupplierDetailtolist() throws MisynJDBCException;

    DataGridDto getSupplierDetailsDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, boolean isSearch);
}
