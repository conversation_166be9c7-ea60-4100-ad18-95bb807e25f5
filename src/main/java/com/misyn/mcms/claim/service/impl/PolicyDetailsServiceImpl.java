package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.PolicyCategoryDao;
import com.misyn.mcms.claim.dao.impl.PolicyCategoryDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.list.PolicyCategoryDataList;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.PolicyDetailsService;
import com.misyn.mcms.claim.service.RestPolicyDetailsService;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
public class PolicyDetailsServiceImpl extends AbstractBaseService<PolicyDetailsServiceImpl> implements PolicyDetailsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PolicyDetailsServiceImpl.class);
    private final RestPolicyDetailsService restPolicyDetailsService = new RestPolicyDetailsServiceImpl();
    private final PolicyCategoryDao policyCategoryDao = new PolicyCategoryDaoImpl();


    List<PolicyBaseCategoryDataDto> setCweCategory(List<CweDetailDto> dtos) {
        return dtos.stream()
                .filter(Objects::nonNull)
                .map(dto -> new PolicyBaseCategoryDataDto(
                                "CWE",
                                Optional.ofNullable(dto.getCweCode()).orElse(AppConstant.STRING_EMPTY),
                                Optional.ofNullable(dto.getHeader()).orElse(AppConstant.STRING_EMPTY),
                                AppConstant.STRING_EMPTY,
                                Optional.ofNullable(dto.getText()).orElse(AppConstant.STRING_EMPTY),
                                Optional.ofNullable(dto.getIsDeleted()).map("Y"::equals).orElse(false)
                        )
                )
                .collect(Collectors.toList());
    }

    List<PolicyBaseCategoryDataDto> setBenefitCategory(List<PolicyBenefitDetailsDto> dtos) {
        return dtos.stream()
                .filter(Objects::nonNull)
                .map(dto -> new PolicyBaseCategoryDataDto(
                                "BEN",
                                Optional.ofNullable(dto.getCode()).orElse(AppConstant.STRING_EMPTY),
                                Optional.ofNullable(dto.getDesc()).orElse(AppConstant.STRING_EMPTY),
                                Optional.ofNullable(dto.getAmount()).map(String::valueOf).orElse(AppConstant.STRING_EMPTY),
                                AppConstant.STRING_EMPTY,
                                false   // not provided delete flag ?
                        )
                )
                .collect(Collectors.toList());
    }

    List<PolicyBaseCategoryDataDto> setSrcctcCategory(List<PolicySrcctcDetailsDto> dtos) {
        return dtos.stream()
                .filter(Objects::nonNull)
                .map(dto -> new PolicyBaseCategoryDataDto(
                                "SRCC",
                                Optional.ofNullable(dto.getAddSec()).orElse(AppConstant.STRING_EMPTY),
                                Optional.ofNullable(dto.getDesc()).orElse(AppConstant.STRING_EMPTY),
                                AppConstant.STRING_EMPTY,
                                AppConstant.STRING_EMPTY,
                                Optional.ofNullable(dto.getDltRecFlag()).map("Y"::equals).orElse(false)
                        )
                )
                .collect(Collectors.toList());
    }

    List<PolicyBaseCategoryDataDto> setOthChargeCategory(List<PolicyOthChargesDetailsDto> dtos) {
        return dtos.stream()
                .filter(Objects::nonNull)
                .map(dto -> new PolicyBaseCategoryDataDto(
                                "CHR",
                                Optional.ofNullable(dto.getCode()).orElse(AppConstant.EMPTY_STRING),
                                Optional.ofNullable(dto.getDesc()).orElse(AppConstant.EMPTY_STRING),
                                Optional.ofNullable(dto.getChgAmount()).map(String::valueOf).orElse(AppConstant.STRING_EMPTY),
                                AppConstant.STRING_EMPTY,
                               false // not requested delete flag
                        )
                )
                .collect(Collectors.toList());
    }

    List<PolicyMemoCategoryDataDto> setMemoCategory(List<PolicyMemoDto> dtos) {
        return dtos.stream()
                .filter(Objects::nonNull)
                .map(dto -> new PolicyMemoCategoryDataDto(
                                "MEMO",
                                Optional.ofNullable(dto.getMemo())
                                        .map(memo -> memo.substring(0, Math.min(20, memo.length())))
                                        .orElse("defaultMemo"),
                                Optional.ofNullable(dto.getMemoDate()).orElse(AppConstant.EMPTY_STRING),
                                Optional.ofNullable(dto.getExclusion()).orElse(AppConstant.EMPTY_STRING),
                                Optional.ofNullable(dto.getMemo()).orElse(AppConstant.EMPTY_STRING),
                                Optional.ofNullable(dto.getDelete()).map("Y"::equals).orElse(false)
                        )
                )
                .collect(Collectors.toList());
    }


    List<PolicyExcessCategoryDataDto> setExcessCategory(List<PolicyBenefitDetailsDto> dtos) {
        return dtos.stream()
                .filter(Objects::nonNull)
                .map(dto -> new PolicyExcessCategoryDataDto(
                        Optional.ofNullable(dto.getCode()).orElse(AppConstant.EMPTY_STRING),
                        Optional.ofNullable(dto.getDesc()).orElse(AppConstant.EMPTY_STRING),
                        Optional.ofNullable(dto.getExcessAmt()).orElse(BigDecimal.ZERO)))
                .collect(Collectors.toList());
    }



    @Override
    public PolicyCategoryDataList getPolicyCategoryDataList(PolicyDto policyDto, String policyChannelType) {
        PolicyCategoryDataList policyCategoryDataList = new PolicyCategoryDataList();
        Connection connection = null;

        try {
            connection = getJDBCConnection();
            List<CweDetailDto> cweCategoryDtoList = restPolicyDetailsService.getCweCategoryDtoList(policyDto, policyChannelType);
            List<PolicySrcctcDetailsDto> srcctcCategoryDtoList = restPolicyDetailsService.getSrcctcCategoryDtoList(policyDto, policyChannelType);
            List<PolicyMemoDto> memoCategoryDtoList = restPolicyDetailsService.getMemoCategoryDtoList(policyDto, policyChannelType);
            List<PolicyBenefitDetailsDto> benefitCategoryDtoList = restPolicyDetailsService.getBenefitCategoryDtoList(policyDto, policyChannelType);
            List<PolicyOthChargesDetailsDto> othChargesCategoryDtoList = restPolicyDetailsService.getOthChargesCategoryDtoList(policyDto, policyChannelType);

            List<PolicyBaseCategoryDataDto> cwe = setCweCategory(cweCategoryDtoList == null ? new ArrayList<>() : cweCategoryDtoList);
            List<PolicyBaseCategoryDataDto> benefit = setBenefitCategory(benefitCategoryDtoList == null ? new ArrayList<>() : benefitCategoryDtoList);
            List<PolicyBaseCategoryDataDto> other = setOthChargeCategory(othChargesCategoryDtoList == null ? new ArrayList<>() : othChargesCategoryDtoList);
            List<PolicyBaseCategoryDataDto> srcctc = setSrcctcCategory(srcctcCategoryDtoList == null ? new ArrayList<>() : srcctcCategoryDtoList);
            List<PolicyMemoCategoryDataDto> memo = setMemoCategory(memoCategoryDtoList == null ? new ArrayList<>() : memoCategoryDtoList);
            List<PolicyExcessCategoryDataDto> excess = setExcessCategory(benefitCategoryDtoList == null ? new ArrayList<>() : benefitCategoryDtoList);


            Stream<PolicyBaseCategoryDataDto> allItemsStream = Stream.of(cwe.stream(), benefit.stream(), other.stream(), srcctc.stream()

            ).flatMap(i -> i);

            Map<String, String> serviceFactorCategoryList = getServiceFactorCategoryList();
            Map<String, String> coverCategoryList = getCoverCategoryList();
            Map<String, String> benefitCategoryList = getBenefitCategoryList();
            Map<String, String> conditionCategoryList = getConditionCategoryList();
            Map<String, String> specialCategoryList = getSpecialCategoryList();

            Map<String, List<PolicyBaseCategoryDataDto>> categorizedData = allItemsStream.collect(Collectors.groupingBy(item -> {
                String itemCode = item.getCode();
                if (serviceFactorCategoryList.containsKey(itemCode)) {
                    return "PolicyServiceCategory";
                } else if (coverCategoryList.containsKey(itemCode)) {
                    return "PolicyCoverCategory";
                } else if (benefitCategoryList.containsKey(itemCode)) {
                    return "PolicyBenefitCategory";
                } else if (conditionCategoryList.containsKey(itemCode)) {
                    return "PolicyConditionCategory";
                } else if (specialCategoryList.containsKey(itemCode)) {
                    return "PolicySpecialCategory";
                } else {
                    return "PolicyOtherCategory";
                }
            }, Collectors.toList()));


            List<PolicyBaseCategoryDataDto> policyServiceCategory = categorizedData.getOrDefault("PolicyServiceCategory", Collections.emptyList()).stream().peek(dto -> dto.setName(serviceFactorCategoryList.get(dto.getCode()))).collect(Collectors.toList());

            List<PolicyBaseCategoryDataDto> reorderedPolicyServiceCategory = policyServiceCategory.stream().sorted(Comparator.comparing(dto -> new ArrayList<>(serviceFactorCategoryList.keySet()).indexOf(dto.getCode()))).collect(Collectors.toList());

            policyCategoryDataList.setPolicyServiceDataList(reorderedPolicyServiceCategory);


            policyCategoryDataList.setPolicyCoverDataList(categorizedData.getOrDefault("PolicyCoverCategory", Collections.emptyList()));
            policyCategoryDataList.setPolicyBenefitDataList(categorizedData.getOrDefault("PolicyBenefitCategory", Collections.emptyList()));
            policyCategoryDataList.setPolicyConditionDataList(categorizedData.getOrDefault("PolicyConditionCategory", Collections.emptyList()));
            policyCategoryDataList.setPolicySpecialDataList(categorizedData.getOrDefault("PolicySpecialCategory", Collections.emptyList()));
            policyCategoryDataList.setPolicyOtherDataList(categorizedData.getOrDefault("PolicyOtherCategory", Collections.emptyList()));

            policyCategoryDataList.setPolicyMemoDataList(memo);

            policyCategoryDataList.setPolicyExcessDataList(excess);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return policyCategoryDataList;
    }

    @Override
    public Map<String, String> getCoverAmountDataList(PolicyDto policyDto, String policyChannelType) {
        try (Connection connection = getJDBCConnection()) {
            List<PolicyBaseCategoryDataDto> cwe = setCweCategory(
                    Optional.ofNullable(restPolicyDetailsService.getCweCategoryDtoList(policyDto, policyChannelType)).orElse(Collections.emptyList())
            );
            List<PolicyBaseCategoryDataDto> benefit = setBenefitCategory(
                    Optional.ofNullable(restPolicyDetailsService.getBenefitCategoryDtoList(policyDto, policyChannelType)).orElse(Collections.emptyList())
            );
            List<PolicyBaseCategoryDataDto> other = setOthChargeCategory(
                    Optional.ofNullable(restPolicyDetailsService.getOthChargesCategoryDtoList(policyDto, policyChannelType)).orElse(Collections.emptyList())
            );
            List<PolicyBaseCategoryDataDto> srcctc = setSrcctcCategory(
                    Optional.ofNullable(restPolicyDetailsService.getSrcctcCategoryDtoList(policyDto, policyChannelType)).orElse(Collections.emptyList())
            );

            return Stream.of(cwe, benefit, other, srcctc)
                    .flatMap(List::stream)
                    .filter(dto -> Objects.nonNull(dto.getCode()))
                    .collect(Collectors.toMap(PolicyBaseCategoryDataDto::getCode, PolicyBaseCategoryDataDto::getAmount));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return Collections.emptyMap();
    }


    @Override
    public Map<String, String> getServiceFactorCategoryList() {
        Map<String, String> map = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            map = policyCategoryDao.getServiceFactorCategoryDtoList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return map;
    }

    @Override
    public Map<String, String> getCoverCategoryList() {
        Map<String, String> map = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            map = policyCategoryDao.getCoverCategoryDtoList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return map;
    }

    @Override
    public Map<String, String> getBenefitCategoryList() {
        Map<String, String> map = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            map = policyCategoryDao.getBenefitCategoryDtoList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return map;
    }

    @Override
    public Map<String, String> getConditionCategoryList() {
        Map<String, String> map = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            map = policyCategoryDao.getConditionCategoryDtoList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return map;
    }

    @Override
    public Map<String, String> getSpecialCategoryList() {
        Map<String, String> map = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            map = policyCategoryDao.getSpecialCategoryDtoList(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return map;
    }


}
