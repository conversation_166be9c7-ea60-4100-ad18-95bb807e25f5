package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface ClaimDocumentTypeService extends BaseService<ClaimDocumentTypeDto> {
    ClaimDocumentTypeDto save(ClaimDocumentTypeDto claimDocumentTypeDto) throws MisynJDBCException;

    DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    ClaimDocumentTypeDto searchDocTypeId(Integer id) throws MisynJDBCException;

    List<ClaimDepartmentDto> documentlist() throws MisynJDBCException;

    List<DocReqFormDto> documentReqFormlist() throws MisynJDBCException;

    String validateDoctypeName(String DoctypeName) throws MisynJDBCException;

}
