package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.NotificationPriority;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.CalculationSheetService;
import com.misyn.mcms.claim.service.ClaimUserAllocationService;
import com.misyn.mcms.claim.service.ClaimUserUpdateService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
public class ClaimUserUpdateServiceImpl extends AbstractBaseService<ClaimUserUpdateServiceImpl> implements ClaimUserUpdateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimWiseDocumentServiceImpl.class);
    private ClaimUserUpdateDao claimUserUpdateDao = new ClaimUserUpdateDaoImpl();
    private ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private ChangeRequestDetailDao changeRequestDetailDao = new ChangeRequestDetailsDaoImpl();
    private UserAuthorityLimitDao userAuthorityLimitDao = new UserAuthorityLimitDaoImpl();
    private ClaimUserAllocationService claimUserAllocationService = new ClaimUserAllocationServiceImpl();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private CalculationSheetService calculationSheetService = new CalculationSheetServiceImpl();
    private RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private UserDao userDao = new UserDaoImpl();

    @Override
    public ClaimHandlerDto searchUsingTxnId(Integer txnId) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        ClaimHandlerDto claimHandlerDto;
        try {
            beginTransaction(connection);
            claimHandlerDto = claimUserUpdateDao.searchClaimUser(connection, txnId);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimHandlerDto;
    }

    @Override
    public boolean updateClaimnUser(Integer txnId, String liabilityAssignUser, String assignUser, String user, Integer claimNo, String assignUserType, String alreadyAssignUser, Integer level, Integer status) throws MisynJDBCException {
        Connection connection = null;
        StringBuilder sbMessage = new StringBuilder();
        boolean success = false;
        Boolean ifHaveCalSheet;
        String URL;
        String accessUserType;
        UserDto user1 = new UserDto();
        user1.setUserId(user);
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            accessUserType = String.valueOf(userDao.getAccessUsertype(connection, assignUser));
            if (level != AppConstant.ZERO_INT && !assignUserType.equals(AppConstant.ZERO) || !AppConstant.EMPTY_STRING.equals(assignUserType)) {
                if (String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM).equals(accessUserType)) {
                    assignUserType = "12";
                } else if (String.valueOf(AppConstant.ACCESS_LEVEL_MOFA_TEAM).equals(accessUserType)) {
                    assignUserType = "6";
                } else if (String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM).equals(accessUserType)) {
                    assignUserType = "11";
                } else if (String.valueOf(AppConstant.ACCESS_LEVEL_SPECIAL_TEAM).equals(accessUserType)) {
                    assignUserType = "5";
                }
            }


            if (status.equals(AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT)) {
                success = claimUserUpdateDao.updateSpecialApprovalUser(connection, claimNo, assignUser, user);
                URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                sbMessage.append("You have received re-assign special comment or Approval Request");
                saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                saveClaimsLogs(connection, claimNo, user1, "Special Comment Approval User Reassign", "Special Comment Approval User Reassigned to " + assignUser);
                saveClaimProcessFlow(connection, claimNo, 0, "Special Comment Approval User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
            } else {
                switch (assignUserType) {
                    case "1":
                    case "9":
                        success = claimUserUpdateDao.updateClaimUHandlerByTxnId(connection, claimNo, assignUser);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                            sbMessage.append("You have re-assign New Claim File");
                            saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                            saveClaimsLogs(connection, claimNo, user1, "1".equalsIgnoreCase(assignUserType) ? "Claim Handler Reassign" : "Offer Claim Handler Reassign", "1".equalsIgnoreCase(assignUserType) ? String.format("Claim Handler Reassigned to %s", assignUser) : String.format("Offer Claim Handler Reassigned to %s", assignUser));
                            saveClaimProcessFlow(connection, claimNo, 0, "1".equalsIgnoreCase(assignUserType) ? String.format("Claim Handler Re-assigned to %s", assignUser) : String.format("Offer Claim Handler Re-assigned to %s", assignUser), user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                        ifHaveCalSheet = claimUserUpdateDao.selectCalsheetAssignUser(connection, claimNo);
                        if (ifHaveCalSheet) {
                            claimUserUpdateDao.updateCalsheetAssignUser(connection, assignUser, claimNo);
                        }
                        if (assignUserType.equals("1")) {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER);
                        } else {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER);
                        }
                        break;
                    case "8":
                        success = claimUserUpdateDao.updateTotalLossTeamByTxnId(connection, claimNo, assignUser);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                        sbMessage.append("You have re-assign New Total Loss Claim File");
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.HIGH);
                        saveClaimsLogs(connection, claimNo, user1, "Total Loss Claim Handler Reassign", "Total Loss Claim Handler Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Total Loss Claim Handler Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                        ifHaveCalSheet = claimUserUpdateDao.selectCalsheetAssignUser(connection, claimNo);
                        if (ifHaveCalSheet) {
                            claimUserUpdateDao.updateCalsheetAssignUser(connection, assignUser, claimNo);
                        }
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER);
                        break;
                    case "2":
                        success = claimUserUpdateDao.updateDecisionMakerByTxnId(connection, claimNo, assignUser);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                        sbMessage.append("You have re-assign New Claim File");
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Decision Maker Reassign", "Decision Maker Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Decision Maker Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                       // ifHaveCalSheet = claimUserUpdateDao.selectCalsheetAssignUser(connection, claimNo);
//                        if (ifHaveCalSheet) {
//                            //  claimUserUpdateDao.updateCalsheetAssignUser(connection, assignUser, claimNo);
//                        }
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL_DECISION_MAKER);
                        break;
                    case "3":
                        success = claimUserUpdateDao.updateTwoMemberByTxnId(connection, txnId, assignUser);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                        sbMessage.append("You have re-assign New Claim File");
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Sub Panel User Reassign", "Sub Panel User Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Sub Panel User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
//                        ifHaveCalSheet = claimUserUpdateDao.selectCalsheetAssignUser(connection, claimNo);
//                        if (ifHaveCalSheet) {
//                            //   claimUserUpdateDao.updateCalsheetAssignUser(connection, assignUser, claimNo);
//                        }
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 47);
                        break;
                    case "4":
                        success = claimUserUpdateDao.updateFourMemberByTxnId(connection, txnId, assignUser);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                        sbMessage.append("You have re-assign New Claim File");
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Main Panel User Reassign", "Main Panel User Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Main Panel User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
//                        ifHaveCalSheet = claimUserUpdateDao.selectCalsheetAssignUser(connection, claimNo);
//                        if (ifHaveCalSheet) {
//                            // claimUserUpdateDao.updateCalsheetAssignUser(connection, assignUser, claimNo);
//                        }
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 48);
                        break;
                    case "5":
                    case "11":
                        success = claimUserUpdateDao.updateSpecialTeamByTxnId(connection, txnId, assignUser);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                        sbMessage.append("You have re-assign New Claim File");
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Special Team User Reassign", "Special Team User Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Special Team User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                       // ifHaveCalSheet = claimUserUpdateDao.selectCalsheetAssignUser(connection, claimNo);
//                        if (ifHaveCalSheet) {
//                            //   claimUserUpdateDao.updateCalsheetAssignUser(connection, assignUser, claimNo);
//                        }
                        if (assignUserType.equals("5")) {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 43);
                        } else {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 63);
                        }
                        break;
                    case "6":
                    case "12":
                        success = claimUserUpdateDao.updateMofaByTxnId(connection, txnId, assignUser);
//                        claimCalculationSheetMainDao.updateClaimNumberByTxnId(connection,claimNo,AppConstant.CAL_SHEET_FORWARD_FOR_MOFA_APPROVAL);
                        calculationSheetService.saveCalculationSheetAssignMofaLevels(connection, txnId, claimNo, user, assignUser, level);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                        sbMessage.append(level != AppConstant.ZERO_INT ? "You Have re-assign new Calculation Sheet" : "You have re-assign New Claim File");
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Mofa User Reassign", "Mofa User Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Mofa User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);

                        if (assignUserType.equals("6")) {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 42);
                        } else {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 62);
                        }
                        break;
                    case "7":
                    case "10":
                        success = claimUserUpdateDao.updateInitClaimHandlerByTxnId(connection, claimNo, assignUser);
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=0");
                        sbMessage.append("You have re-assign New Claim File");
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Initial Liability User Reassign", "Initial Liability User Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Initial Liability User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                        if (assignUserType.equals("7")) {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 40);
                        } else {
                            saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, 60);
                        }
                        break;
                    case "13":
                        success = claimUserUpdateDao.updateSupplyOrderByClaimNo(connection, claimNo, assignUser);
                        if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                            rtePendingClaimDetailDao.updateAssignUser(connection, claimNo, assignUser, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR);
                        }
                        URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                                .concat("&TYPE=4")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
                        sbMessage.append("You have re-assign New Claim File - Re assigned By " + alreadyAssignUser);
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "SpareParts Coordinator/ Supply Order Assign User Reassign", "SpareParts Coordinator/ Supply Order Assign User Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "SpareParts Coordinator/ Supply Order Assign User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR);
                        break;
                    case "14":
                        success = claimUserUpdateDao.updateSparePartsCoordinatorByTxnId(connection, txnId, assignUser);
                        if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                            rtePendingClaimDetailDao.updateAssignUser(connection, claimNo, assignUser, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR);
                        }
                        URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                                .concat("&P_CAL_SHEET_NO=")
                                .concat(String.valueOf(txnId))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(9));
                        sbMessage.append("You have re-assign New Claim File - Re assigned By " + alreadyAssignUser);
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "SpareParts Coordinator Assign User Reassign", "SpareParts Coordinator Assign User Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "SpareParts Coordinator Assign User Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR);
                        break;
                    case "15":
                        success = claimUserUpdateDao.updateSupplyOrderScurtinizingTeamByTxnId(connection, txnId, assignUser);
                        if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                            rtePendingClaimDetailDao.updateAssignUser(connection, claimNo, assignUser, AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM);
                        }
                        URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                                .concat("&TYPE=4")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
                        sbMessage.append("You have re-assign New Claim File - Re assigned By " + alreadyAssignUser);
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Bill Checking Team Reassign", "Bill Checking Team Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Bill Checking Team Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM);
                        break;
                    case "16":
                        success = claimUserUpdateDao.updateScurtinizingTeamByTxnId(connection, txnId, assignUser);
                        if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo)) {
                            rtePendingClaimDetailDao.updateAssignUser(connection, claimNo, assignUser, AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM);
                        }
                        URL = AppConstant.BILL_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo))
                                .concat("&P_CAL_SHEET_NO=")
                                .concat(String.valueOf(txnId))
                                .concat("&TYPE=7")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(9));
                        sbMessage.append("You have re-assign New Claim File - Re assigned By " + alreadyAssignUser);
                        saveNotification(connection, claimNo, user, assignUser, sbMessage.toString(), URL, NotificationPriority.LOW);
                        saveClaimsLogs(connection, claimNo, user1, "Bill Checking Team Reassign", "Bill Checking Team Reassigned to " + assignUser);
                        saveClaimProcessFlow(connection, claimNo, 0, "Bill Checking Team Re-assigned to " + assignUser + "", user, Utility.sysDateTime(), assignUser, AppConstant.NO);
                        saveUserAllocationHistory(connection, claimNo, assignUser, user, AppConstant.REASSIGN, AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM);
                        break;
                }
                updateChangeRequestDetails(connection, claimNo, assignUser, alreadyAssignUser);
            }


            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        if (success) {
            return true;
        }
        return false;
    }

    private void updateChangeRequestDetails(Connection connection, Integer claimNo, String assignUser, String alreadyAssignUser) throws Exception {
        try {
            List<ChangeRequestDetailDto> list = changeRequestDetailDao.getAllrequestByclaimNoAndRequestUser(connection, claimNo, alreadyAssignUser);

            if (!list.isEmpty()) {
                for (ChangeRequestDetailDto dto : list) {
                    dto.setRequestUser(assignUser);
                    dto.setRequestDatetime(Utility.sysDateTime());
                    changeRequestDetailDao.update(connection, dto);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserType, String assignUserName, boolean isSearch, boolean isRejectedClaim, String calsheetStatus, String assingUserLevel,UserDto sessionUser) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (isSearch) {
                switch (assignUserType) {
                    case "1":
                    case "9":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoClaimHandler(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus, assignUserType, sessionUser);
                        break;
                    case "8":
                        dataGridDto = claimUserUpdateDao.getTotalLossDataGridDtoClaimHandler(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus);
                        break;
                    case "7":
                    case "10":
                        dataGridDto = claimUserUpdateDao.getInitialClaimHandlerDataGridDtoClaimHandler(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus, assignUserType);
                        break;
                    case "2":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoDecisionMaker(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, isRejectedClaim, calsheetStatus);
                        break;
                    case "3":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoTwoMember(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus);
                        break;
                    case "4":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoFourMember(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus);
                        break;
                    case "5":
                    case "11":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoSpecialTeam(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus, assignUserType);
                        break;
                    case "6":
                    case "12":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoMofa(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus, assignUserType, AppConstant.STRING_EMPTY,assingUserLevel);
                        break;
                    case "13":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoSparePartsCoordinatorSupplyOrder(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus, assignUserType);
                        break;
                    case "14":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoSparePartsCoordinatorCheckCalsheet(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus, assignUserType);
                        break;
                    case "15":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoBillCheckingTeamSupplyOrder(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, assignUserType);
                        break;
                    case "16":
                        dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoBillCheckingTeamCheckCalsheet(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, assignUserName, calsheetStatus, assignUserType);
                        break;
                    default:
                        dataGridDto = new DataGridDto();
                        dataGridDto.setDraw(drawRandomId);
                        dataGridDto.setRecordsTotal(0);
                        dataGridDto.setRecordsFiltered(0);
                        break;
                }

                List<UpdateClaimFileDto> dtos = (ArrayList) dataGridDto.getData();
                List<UpdateClaimFileDto> list = new ArrayList<>();
                int index = start;
                for (UpdateClaimFileDto gridDto : dtos) {
                    String status = this.getCalsheetStatus(gridDto.getClaimNo());
                    if (AppConstant.CALSHEET_STATUS_ADVANCE_PAID.equalsIgnoreCase(calsheetStatus)) {
                        if (status.equalsIgnoreCase(AppConstant.ADVANCE_PAID)) {
                            gridDto.setCalSheetStatus(status);
                            gridDto.setIndex(++index);
                            list.add(gridDto);
                        }
                    } else if (AppConstant.CALSHEET_STATUS_REJECTED.equalsIgnoreCase(calsheetStatus)) {
                        if (status.equalsIgnoreCase(AppConstant.CALSHEET_STATUS_REJECTED)) {
                            gridDto.setCalSheetStatus(status);
                            gridDto.setIndex(++index);
                            list.add(gridDto);
                        }
                    } else {
                        gridDto.setCalSheetStatus(status);
                        gridDto.setIndex(++index);
                        list.add(gridDto);
                    }
                }

                List<Object> newLimitList = new ArrayList<>();
                for (int i = start; i < (length + start); i++) {
                    if (i < list.size() + start && i < length + start) {
                        newLimitList.add(list.get(i));
                    }
                }
                dataGridDto.setData(newLimitList);
                dataGridDto.setRecordsTotal(dataGridDto.getRecordsTotal());
                dataGridDto.setRecordsFiltered(dataGridDto.getRecordsTotal());
                dataGridDto.setDraw(drawRandomId);

            } else {
                dataGridDto = new DataGridDto();
                dataGridDto.setDraw(drawRandomId);
                dataGridDto.setRecordsTotal(0);
                dataGridDto.setRecordsFiltered(0);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    public String getCalsheetStatus(Integer claimNo) throws Exception {
        Connection connection = null;
        String status = AppConstant.STRING_EMPTY;
        List<ClaimCalculationSheetMainDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            list = claimCalculationSheetMainDao.searchByClaimNoOrderByDesc(connection, claimNo);

            for (ClaimCalculationSheetMainDto dto : list) {
                if (AppConstant.CAL_SHEET_TYPE_ADVANCED.equals(dto.getCalSheetType())) {
                    return AppConstant.ADVANCE_PAID;
                } else if (AppConstant.CAL_SHEET_PAYMENT_REJECTED.equals(dto.getStatus())) {
                    return AppConstant.CALSHEET_STATUS_REJECTED;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return status;
    }

    @Override
    public List<String> getIdsToArray(String selectedIds) {

        return getSelectedList("CRB", selectedIds);
    }

    @Override
    public List<Integer> getSelectedList(String array) {
        List<Integer> list = null;
        if (!array.isEmpty()) {
            array = array.replaceAll("CCB", "");
            list = Arrays.stream(array.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }

    @Override
    public HashMap<String, String> setClaimNoandTxnIdToMap(String txnIds) throws Exception {
        List<String> claimNoAndTxnIdStr = getSelectedList("CRB", txnIds);
        HashMap<String, String> map = new HashMap<>();

        if (null != claimNoAndTxnIdStr) {
            for (String valueStr : claimNoAndTxnIdStr) {
                String key = before(valueStr, "-").trim();
                String value = after(valueStr, "-").trim();
                map.put(key, value);
            }
        }
        return map;
    }

    @Override
    public HashMap<String, String> setClaimNoandAlreadyAssignUserMap(String claimNoAndTxnIds) throws Exception {
        List<String> claimNoAndTxnIdStr = getSelectedList("", claimNoAndTxnIds);
        HashMap<String, String> map = new HashMap<>();

        if (null != claimNoAndTxnIdStr) {
            for (String valueStr : claimNoAndTxnIdStr) {
                String key = before(valueStr, "-").trim();
                String value = after(valueStr, "-").trim();
                map.put(key, value);
            }
        }
        return map;
    }

    @Override
    public DataGridDto getMofaDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, String assignUserType, String assignUserName, boolean isSearch, String calsheetStatus, String claimStatus,String assignUserLevel) {
        DataGridDto dataGridDto = new DataGridDto();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimUserUpdateDao.getClaimHandlerDataGridDtoMofa(connection, parameterList, drawRandomId, start, length, columnOrder, orderColumnName, fromDate, toDate, assignUserName, calsheetStatus, assignUserType, claimStatus,assignUserLevel);
            List<UpdateClaimFileDto> dtos = (ArrayList) dataGridDto.getData();
            List<UpdateClaimFileDto> list = new ArrayList<>();
            int index = start;
            for (UpdateClaimFileDto gridDto : dtos) {
                String status = this.getCalsheetStatus(gridDto.getClaimNo());
                if (AppConstant.CALSHEET_STATUS_ADVANCE_PAID.equalsIgnoreCase(calsheetStatus)){
                    if (status.equalsIgnoreCase(AppConstant.ADVANCE_PAID)) {
                        gridDto.setCalSheetStatus(status);
                        gridDto.setIndex(++index);
                        list.add(gridDto);
                    }
                } else if (AppConstant.CALSHEET_STATUS_REJECTED.equalsIgnoreCase(calsheetStatus)) {
                    if (status.equalsIgnoreCase(AppConstant.CALSHEET_STATUS_REJECTED)) {
                        gridDto.setCalSheetStatus(status);
                        gridDto.setIndex(++index);
                        list.add(gridDto);
                    }
                } else {
                    gridDto.setCalSheetStatus(status);
                    gridDto.setIndex(++index);
                    list.add(gridDto);
                }
            }

            List<Object> newLimitList = new ArrayList<>();
            for (int i = start; i < (length + start); i++) {
                if (i < list.size() + start && i < length + start) {
                    newLimitList.add(list.get(i));
                }
            }
            dataGridDto.setData(newLimitList);
            dataGridDto.setRecordsTotal(dataGridDto.getRecordsTotal());
            dataGridDto.setRecordsFiltered(dataGridDto.getRecordsTotal());
            dataGridDto.setDraw(drawRandomId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<PopupItemDto> getMofaLevels() {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userAuthorityLimitDao.getAllMofaLevels(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return null;
    }

    public String before(String value, String a) {
        // Return substring containing all characters before a string.
        int posA = value.indexOf(a);
        if (posA == -1) {
            return "";
        }
        return value.substring(0, posA);
    }

    public String after(String value, String a) {
        // Returns a substring containing all characters after a string.
        int posA = value.lastIndexOf(a);
        if (posA == -1) {
            return "";
        }
        int adjustedPosA = posA + a.length();
        if (adjustedPosA >= value.length()) {
            return "";
        }
        return value.substring(adjustedPosA);
    }

    public List<String> getSelectedList(String regex, String array) {
        List<String> list = null;
        if (!array.isEmpty()) {
            array = array.replaceAll(regex, "");
            list = Arrays.stream(array.split(",")).map(String::toString).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }

    @Override
    public ClaimHandlerDto insert(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto update(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto delete(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto updateAuthPending(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto deleteAuthPending(ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimHandlerDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<ClaimHandlerDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }
}
