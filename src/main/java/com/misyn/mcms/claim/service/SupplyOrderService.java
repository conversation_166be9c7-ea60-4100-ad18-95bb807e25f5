package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.util.List;

public interface SupplyOrderService {

    SupplyOrderSummaryDto getSupplyOrderSummaryDto(Integer supplyOrderRefNo, Integer claimNo);

    SupplyOrderSummaryDto getSupplyOrderSummaryDto(Integer supplyOrderRefNo, boolean isMaster);

    List<SupplyOrderDetailsDto> getSupplyOrderDetailsDtoList(Integer supplyOrderRefNo);

    void saveSupplyOrder(SupplyOrderSummaryDto supplyOrderSummaryDto, UserDto user) throws MisynJDBCException;

    void forwardToScrTeamSupplyOrder(Integer claimNo, Integer supplyOrderRefNo,
                                     String apprvAssignScrutinizingUserId, UserDto user) throws MisynJDBCException;

    void returnToSparePartsCoordinator(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException;

    void returnToSparePartsCoordinatorByClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException;

    void returnToScrTeamSupplyOrderByClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException;

    void recall(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException;

    void approvedAndForwardToClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user, String assignClaimHandlerUserId, boolean isUpdated) throws MisynJDBCException;

    void forwardToSparePartsCoordinatorForGeneratingLetter(Integer claimNo, Integer supplyOrderRefNo, UserDto user) throws MisynJDBCException;

    Integer getMaxSupplyRefNo(Integer claimNo);

    List<SupplyOrderSummaryDto> searchClaimSupplyOrderSummary(Integer claimNo, Integer refNo);

    void updateSupplyOrderGenerate(Integer claimNo, Integer supplyOrderRefNo, String inputUser, String claimHandlerUser, ClaimHandlerDto claimHandlerDto, UserDto user) throws Exception;

    List<VatRateDto> getVateRateList();

    void returnToClaimHandler(Integer claimNo, Integer supplyOrderRefNo, UserDto user, boolean isAriRequestNReturn) throws Exception;

    boolean isPendingSupplyOrder(Integer claimNoToSearch) throws Exception;

    void updateSupplyOrder(SupplyOrderSummaryDto supplyOrderSummaryDto, UserDto user) throws Exception;

    void returnScrTeamSupplyOrderBySPC(Integer claimNo, Integer refNo, UserDto user, boolean isRecall) throws Exception;

    void forwardToSparePartsCoordinatorForApproval(Integer claimNo, Integer refNo, UserDto user) throws Exception;

    void approveAndForwardToScrutinizingTeam(Integer claimNo, Integer refNo, UserDto user) throws Exception;

    void forwardToScrutinizingTeam(Integer claimNo, Integer refNo, UserDto user) throws Exception;

    void returnSpcoodSupplyOrderByScrutinizing(Integer claimNo, Integer refNo, UserDto user, boolean isRecall) throws Exception;

    void approvedAndForwardClaimHandlerSupplyOrderAfterUpdate(Integer claimNo, Integer refNo, UserDto user, String assignUserId) throws Exception;

    List<SupplyOrderSummaryDto> searchClaimSupplyOrderSummaryPending(Integer claimNo, Integer refNo);

    Integer isDoAssigned(Integer claimNo) throws Exception;

    String getPolicyChannelType(Integer claimNo) throws Exception;

    SupplyOrderSummaryDto getActiveSupplyOrderDetails(Integer claimNo) throws Exception;

    Boolean isPendingBillForDeliveryOrderApprovedDocument(Integer claimNo, Integer documentTypeId) throws Exception;
}
