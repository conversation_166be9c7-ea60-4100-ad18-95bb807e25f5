/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.UserAuthorityLimitDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ClaimUserAllocationService {

    String getNextAuthLimitAssignUser(Connection connection, Integer accessLevel, Integer allocationFunctionType, String type, String inputUser, Integer claimNo, boolean isAuthLimitForward, BigDecimal amount, BigDecimal limit, int limitLevel, boolean isMandatory) throws Exception;

    String getNextAssignUser(Connection connection, Integer accessLevel, Integer allocationFunctionType, String type, String inputUser, Integer claimNo) throws Exception;

    List<String> getNextAuthLimitAssignUserList(Connection connection, Integer accessLevel, Integer allocationFunctionType, UserAuthorityLimitDto userAuthorityLimitDto, boolean isMandatory);
}
