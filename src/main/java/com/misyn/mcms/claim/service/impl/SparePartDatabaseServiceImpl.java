package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.SparePartDatabaseDao;
import com.misyn.mcms.claim.dao.impl.SparePartDatabaseDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.SparePartDatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;
public class SparePartDatabaseServiceImpl extends AbstractBaseService<SparePartDatabaseServiceImpl> implements SparePartDatabaseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelUserServiceImpl.class);
    private SparePartDatabaseDao sparePartDatabaseDao = new SparePartDatabaseDaoImpl();

    @Override
    public SparePartDatabaseDto save(SparePartDatabaseDto sparePartDatabaseDto) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            sparePartDatabaseDao.insertMaster(connection, sparePartDatabaseDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return sparePartDatabaseDto;
    }

    @Override
    public SparePartDatabaseDto searchSparePartDatabase(Integer txnId) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        SparePartDatabaseDto sparePartDatabaseDto;
        try {
            beginTransaction(connection);
            sparePartDatabaseDto = sparePartDatabaseDao.searchMaster(connection, txnId);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return sparePartDatabaseDto;
    }

    @Override
    public DataGridDto getSparePartDatabaseDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, boolean isSearch) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            if (isSearch) {
                dataGridDto = sparePartDatabaseDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
            } else {
                dataGridDto = new DataGridDto();
                dataGridDto.setDraw(drawRandomId);
                dataGridDto.setRecordsTotal(0);
                dataGridDto.setRecordsFiltered(0);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<PopupItemDto> getVehicleMake() throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        List<PopupItemDto> popupItemDto;
        try {
            beginTransaction(connection);
            popupItemDto = sparePartDatabaseDao.getVehickeMake(connection);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return popupItemDto;
    }

    @Override
    public List<PopupItemDto> getVehicleModel(String vehicleMake) throws MisynJDBCException {
        Connection connection = getJDBCConnection();
        List<PopupItemDto> popupItemDto;
        try {
            beginTransaction(connection);
            popupItemDto = sparePartDatabaseDao.getVehickeModel(connection, vehicleMake);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return popupItemDto;
    }

    @Override
    public SparePartDatabaseDto insert(SparePartDatabaseDto sparePartDatabaseDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto update(SparePartDatabaseDto sparePartDatabaseDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto delete(SparePartDatabaseDto sparePartDatabaseDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto updateAuthPending(SparePartDatabaseDto sparePartDatabaseDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto deleteAuthPending(SparePartDatabaseDto sparePartDatabaseDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto search(Object id) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<SparePartDatabaseDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<SparePartDatabaseDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }
}
