package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.NotificationPriority;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.NotificationService;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Parameters;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NotificationServiceImpl extends AbstractBaseService<NotificationServiceImpl> implements NotificationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationServiceImpl.class);
    private static final Integer NOTIFICATION_TIMEOUT = Parameters.getDocumentNotificationTimeout();
    private static final Integer ARI_NOTIFICATION_TIMEOUT = Parameters.getAriNotificationTimeout();
    private NotificationDao notificationDao = new NotificationDaoImpl();
    private InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private UserDao userDao = new UserDaoImpl();
    private AriNotificationDao ariNotificationDao = new AriNotificationDaoImpl();
    private AssessorTimeExceedNotificationDao assessorTimeExceedNotificationDao = new AssessorTimeExceedNotificationDaoImpl();
    private AssessorDao assessorDao = new AssessorDaoImpl();

    @Override
    public NotificationFormDto getNotificationFormDto(int limit, UserDto user) {
        NotificationFormDto notificationFormDto = new NotificationFormDto();
        int count;
        int unreadCount = 0;
        List<NotificationDto> list;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = notificationDao.searchAllByAssignUserIdWithLimit(connection, user.getUserId(), limit);
            count = notificationDao.countByAssignUserId(connection, user.getUserId());
            unreadCount = notificationDao.countByAssignUserIdAndReadStatus(connection, user.getUserId(), AppConstant.NO);
            notificationFormDto.setNotificationDtoList(list);
            notificationFormDto.setNotificationCount(count);
            notificationFormDto.setUnreadNotificationCount(unreadCount);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return notificationFormDto;
    }

    @Override
    public NotificationFormDto getNotificationFormDto(UserDto user, String fromDate, String toDate, String vehicleNo, String claimNo) {
        NotificationFormDto notificationFormDto = new NotificationFormDto();
        List<NotificationDto> list = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            for (NotificationDto notificationDto : notificationDao.searchMasterAllByAssignUserId(connection, user.getUserId(), fromDate, toDate, vehicleNo, claimNo)) {
                list.add(notificationDto);
            }
            for (NotificationDto notificationDto : notificationDao.searchHistoryAllByAssignUserId(connection, user.getUserId(), fromDate, toDate, vehicleNo, claimNo)) {
                list.add(notificationDto);
            }

            notificationFormDto.setNotificationDtoList(list);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return notificationFormDto;
    }

    @Override
    public NotificationDto addNotificationDto(NotificationDto notificationDto) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
//            if (null != notificationDao.insertMaster(connection, notificationDto)) {
//                return notificationDto;
//            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("Can not be saved");
        } finally {
            this.releaseJDBCConnection(connection);
        }
        return null;
    }

    @Override
    public void updateNotification(Integer txnId, String status) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            NotificationDto notificationDto = new NotificationDto();
            notificationDto.setTxnId(txnId);
            notificationDto.setReadDateTime(Utility.sysDateTime());
            notificationDto.setReadStatus(status);
            notificationDao.updateMaster(connection, notificationDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

    }

    @Override
    public int notificationCount(UserDto user) {
        Connection connection = null;
        int count = 0;
        try {
            connection = getJDBCConnection();
            count = notificationDao.countByAssignUserId(connection, user.getUserId());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return count;
    }

    @Override
    public void updateNotificationChecked(Integer txtId, String status) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            notificationDao.updateNotificationChecked(connection, txtId, status);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void deleteCheckedNotification(Integer txtId, String status) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            notificationDao.insertMasterToHistory(connection, txtId, status);
            notificationDao.deleteNotification(connection, txtId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void sendDocumentNotification() {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<DocumentNotificationDto> pendingNotification = notificationDao.getPendingNotification(connection);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(AppConstant.DATE_TIME_FORMAT);
            if (null != pendingNotification && !pendingNotification.isEmpty()) {
                for (DocumentNotificationDto documentNotificationDto : pendingNotification) {
                    String message = "You Have Received ".concat(documentNotificationDto.getDocName());
                    long diff = simpleDateFormat.parse(Utility.sysDateTime()).getTime() - simpleDateFormat.parse(documentNotificationDto.getUpdateDate()).getTime();
                    long minuteDifference = diff / (60 * 1000);
                    String URL;
                    String colorCode;
                    int accessUserTypeByUserId = userDao.getAccessUserTypeByUserId(connection, documentNotificationDto.getAssignUser());
                    if (documentNotificationDto.getDocType() == AppConstant.DO_BILL_DOCUMENT_TYPE_ID) {
                        URL = AppConstant.SUPPLY_ORDER_CHECK_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(documentNotificationDto.getClaimNo()))
                                .concat("&TYPE=4")
                                .concat("&P_TAB_INDEX=").concat(String.valueOf(6));
                    } else if (documentNotificationDto.getDocType() == AppConstant.GARAGE_BILL_DOCUMENT_TYPE_ID) {
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(documentNotificationDto.getClaimNo())).concat("&P_TAB_INDEX=1");
                    } else if (documentNotificationDto.getDocType() == AppConstant.ESTIMATE_DOCUMENT_TYPE_ID) {
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(documentNotificationDto.getClaimNo())).concat("&P_TAB_INDEX=1");
                    } else {
                        URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(documentNotificationDto.getClaimNo())).concat("&P_TAB_INDEX=1");
                        if (21 == accessUserTypeByUserId || 22 == accessUserTypeByUserId || 23 == accessUserTypeByUserId || 24 == accessUserTypeByUserId) {
                            Integer latestInspectionRefNo = inspectionDetailsDao.getLatestInspectionRefNo(connection, documentNotificationDto.getClaimNo());
                            if (null != latestInspectionRefNo) {
                                URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(latestInspectionRefNo));
                                message = "Documents uploaded by the Branch User.Please review/process immediately";
                            }
                        }
                    }

                    if (minuteDifference > NOTIFICATION_TIMEOUT) {
                        colorCode = AppConstant.ACCESS_LEVEL_CLAIM_HANDLER == accessUserTypeByUserId ||
                                AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER == accessUserTypeByUserId ||
                                AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER == accessUserTypeByUserId ? "#FFFFBF" : "#FADADD";
                        if (null != documentNotificationDto.getAssignUser() && !documentNotificationDto.getAssignUser().isEmpty()) {
                            this.saveNotification(connection, documentNotificationDto.getClaimNo(), documentNotificationDto.getInputUser(), documentNotificationDto.getAssignUser(), message, URL, colorCode);
                            markSentNotifications(connection, documentNotificationDto.getFormRelId(), documentNotificationDto.getAssignUser());
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void sendAriNotification() {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<AriNotificationDto> ariNotificationDtos = ariNotificationDao.getPendingNotifications(connection);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(AppConstant.DATE_TIME_FORMAT);
            for (AriNotificationDto ariNotificationDto : ariNotificationDtos) {
                String URL = AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(ariNotificationDto.getClaimNo())).concat("&type=").concat("2");
                long diff = simpleDateFormat.parse(Utility.sysDateTime()).getTime() - simpleDateFormat.parse(ariNotificationDto.getInputDate()).getTime();
                long minuteDifference = diff / (60 * 1000);

                if (minuteDifference > ARI_NOTIFICATION_TIMEOUT) {
                    String message = "Final bill received. Arrange ARI.";
                    if (null != ariNotificationDto.getAssignUser() && !ariNotificationDto.getAssignUser().isEmpty()) {
                        this.saveNotification(connection, ariNotificationDto.getClaimNo(), ariNotificationDto.getInputUser(), ariNotificationDto.getAssignUser(), message, URL);
                        markSentNotifications(connection, ariNotificationDto.getClaimNo());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void markSentNotifications(Connection connection, Integer claimNo) {
        try {
            ariNotificationDao.removeAvailableRecordsByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void markSentNotifications(Connection connection, Long formId, String assignUser) {
        try {
            notificationDao.markSentNotifications(connection, formId, assignUser);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private NotificationDto setMessageDto(DocumentNotificationDto documentNotificationDto, String url) {
        NotificationDto notificationDto = new NotificationDto();
        String message = "You have received ";
        notificationDto.setClaimNo(documentNotificationDto.getClaimNo());
        notificationDto.setInpUserId(documentNotificationDto.getInputUser());
        notificationDto.setAssignUserId(documentNotificationDto.getAssignUser());
        notificationDto.setMessage(message.concat(documentNotificationDto.getDocName() + ","));
        notificationDto.setUrl(url);
        return notificationDto;
    }

    @Override
    public void checkTimeDuration() {
        Connection connection = null;
        List<AssessorTimeExceedNotificationDto> dtosList = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        String message = "Test Message";
        String url = "test URL";

        try {
            connection = getJDBCConnection();
            dtosList = assessorTimeExceedNotificationDao.fetchAll(connection);

            for (AssessorTimeExceedNotificationDto dto : dtosList) {
                String assessorName = assessorDao.getAssessorName(connection, dto.getInspectionAssessor());
                Date currentTimestamp = new Date();
                Date assignedDateTime = dateFormat.parse(dto.getAssignedDateTime());
                long expirationTimeMillis = assignedDateTime.getTime() + (dto.getDuration());
                Date expirationDateTime = new Date(expirationTimeMillis);

                if (currentTimestamp.after(expirationDateTime)) {
                    saveNotification(connection, dto.getClaimNo(), dto.getCallCenterUser(), assessorName, message, url, NotificationPriority.LOW, dto.getColourCode());
                    assessorTimeExceedNotificationDao.updateStatus(connection, dto.getId());
                }
            }

        } catch (ParseException e) {
            LOGGER.error("Error parsing assignedDateTime: " + e.getMessage());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
    }
}
