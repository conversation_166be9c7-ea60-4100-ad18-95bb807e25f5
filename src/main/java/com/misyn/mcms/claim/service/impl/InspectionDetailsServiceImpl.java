package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.controller.callcenter.validator.AssessorLoggerTrail;
import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.*;
import com.misyn.mcms.claim.dao.motorengineer.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.ListBoxItem;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
public class InspectionDetailsServiceImpl extends AbstractBaseService<InspectionDetailsServiceImpl> implements InspectionDetailsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InspectionDetailsServiceImpl.class);
    private final Lock LOCK = new ReentrantLock();
    ClaimWiseDocumentService claimWiseDocumentService = new ClaimWiseDocumentServiceImpl();
    private InspectionDao inspectionDao = new InspectionDaoImpl();
    private InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private AssessorAllocationDao assessorAllocationDao = new AssessorAllocationDaoImpl();
    private TireCondtionDao tireCondtionDao = new TireCondtionDaoImpl();
    private SpecialRemarkDao specialRemarkDao = new SpecialRemarkDaoImpl();
    private DesktopInspectionDetailsDao desktopInspectionDetailsDao = new DesktopInspectionDetailsDaoImpl();
    private DesktopInspectionDetailsMeDao desktopInspectionDetailsMeDao = new DesktopInspectionDetailsMeDaoImpl();
    private GarageInspectionDetailsDao garageInspectionDetailsDao = new GarageInspectionDetailsDaoImpl();
    private DrSupplementaryInspectionDetailsDao drSupplementaryInspectionDetailsDao = new DrSupplementaryInspectionDetailsDaoImpl();
    private DrSupplementaryInspectionDetailsMeDao drSupplementaryInspectionDetailsMeDao = new DrSupplementaryInspectionDetailsMeDaoImpl();
    private OnSiteInspectionDetailsDao onSiteInspectionDetailsDao = new OnSiteInspectionDetailsDaoImpl();
    private OnSiteInspectionDetailsMeDao onSiteInspectionDetailsMeDao = new OnSiteInspectionDetailsMeDaoImpl();
    private ARIInspectionDetailsDao aRIInspectionDetailsDao = new ARIInspectionDetailsDaoImpl();
    private ARIInspectionDetailsMeDao ariInspectionDetailsMeDao = new ARIInspectionDetailsMeDaoImpl();
    private ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private ClaimDocumentDao claimDocumentDao = new ClaimDocumentDaoImpl();
    private ThirdPartyAssessorDao thirdPartyAssessorDao = new ThirdPartyAssessorDaoImpl();
    private LoggerTrailDao loggerTrailDao = new LoggerTrailDaoImpl();
    private ClaimImageDao claimImageDao = new ClaimImageDaoImpl();
    private RequestAriDao requestAriDao = new RequestAriDaoImpl();
    private RequestAriService requestAriService = new RequestAriServiceImpl();
    private CallCenterService callCenterService = new CallCenterServiceImpl();
    private AssessorAllocationService assessorAllocationService = new AssessorAllocationServiceImpl();
    private MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private GarageInspectionDetailsMeDao garageInspectionDetailsMeDao = new GarageInspectionDetailsMeDaoImpl();
    private AssessorMileageFeeDao assessorMileageFeeDao = new AssessorMileageFeeDaoImpl();
    private AssessorFeeDao assessorFeeDao = new AssessorFeeDaoImpl();
    private RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private SequenceKeyTableDao sequenceKeyTableDao = new SequenceKeyTableDaoImpl();
    private CallCenterDao callCenterDao = new CallCenterDaoImpl();

    @Override
    public InspectionDetailsDto insert(InspectionDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            //Set Audit Details
            inspectionDetailsDto.setInputDatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            inspectionDetailsDto.setInputUserId(user.getUserId());

            //Motor Engineer Assign User Set - V_REPORT_TO
            inspectionDetailsDto.setAssignRteUser("N/A");// NB
            inspectionDetailsDto.setAssignRteDatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            //Set Nested Objects Values To Most Outer Object
            inspectionDetailsDto.setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
            inspectionDetailsDto.setJobId(inspectionDetailsDto.getAssessorAllocationDto().getJobId());
            inspectionDetailsDto.setClaimNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            inspectionDetailsDto.getInspectionDto().setInspectionId(inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());
            // inspectionDetailsDto.setInspectionId(inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());

            switch (inspectionDetailsDto.getActionType()) {
                case "DRAFT":
                    //Set and Updated Assessor Inspection Detail Record Status as 7 --> ATTENDED
                    inspectionDetailsDto.setRecordStatus(7);

                    //Set and Updated Assessor allocation Record Status as 7 --> ATTENDED
                    assessorAllocationDao.updateRecordStatusByRefNo(connection, ClaimStatus.ATTENDED.getClaimStatus(), inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
                    break;
                case "SUBMIT":
                    //Set and Updated Assessor Inspection Detail Record Status as 8 --> SUBMITTED
                    inspectionDetailsDto.setRecordStatus(8);

                    //Set and Updated Assessor allocation Record Status as 8 --> SUBMITTED
                    assessorAllocationDao.updateRecordStatusByRefNo(connection, ClaimStatus.SUBMITTED.getClaimStatus(), inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

                    claimWiseDocumentService.updateDocumentInspectionWise(connection, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getInspectionDto().getInspectionId(), user);
                    sendCallEstimateAssignedSmsToCustomer(inspectionDetailsDto, connection, user);
                    sendCallEstimateAssignedSmsToAgent(inspectionDetailsDto, connection, user);
                    break;
            }

            //update Online inspection status
//            assessorAllocationDao.updateOnlineInspectionStatus(connection, inspectionDetailsDto.getTypeOnlineInspection(), inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

            //Save INspection Details
            saveInspectionDetails(inspectionDetailsDto, connection);
            initialLog(inspectionDetailsDto, connection);//Include saveAssessorProfessionalFee

            if (inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 5
                    && inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 6
                    && inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 7
                    && inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId() != 9) {
                saveTyreCondition(inspectionDetailsDto.getTireCondtionDtoList(), connection);
            }

            //save special remarks seperately on insert and update record
            //  saveSpecialRemark(inspectionDetailsDto, user, connection);

            //save third Party Assesssor Dtos
            saveThirdPartyAssessorDetails(inspectionDetailsDto, user, connection);

            //Save Assessor Estimate For Repair as selected by Inspection Type
            boolean isAri = false;
            switch (inspectionDetailsDto.getInspectionDto().getInspectionId()) {
                case 4: //Garage Inspection
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
//                    if (AppConstant.YES.equals(inspectionDetailsDto.getGarageInspectionDetailsDto().getAriAndSalvage().getCondtionType())) {
//                        isAri = true;
//                    }
                    garageInspectionDetailsDao.insertMaster(connection, inspectionDetailsDto.getGarageInspectionDetailsDto());
                    break;
                case 5: //DR Insepection
                case 6: //Supplimantary Inspection
                    inspectionDetailsDto.getDrSuppInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
//                    if (AppConstant.YES.equals(inspectionDetailsDto.getDrSuppInspectionDetailsDto().getAriAndSalvage().getCondtionType())) {
//                        isAri = true;
//                    }
                    drSupplementaryInspectionDetailsDao.insertMaster(connection, inspectionDetailsDto.getDrSuppInspectionDetailsDto());
                    break;
                case 7: //After Repair inspection
                case 9: //Salvage Inspection
                    inspectionDetailsDto.getAriInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
                    aRIInspectionDetailsDao.insertMaster(connection, inspectionDetailsDto.getAriInspectionDetailsDto());
                    break;
                case 8: //Desktop Assesment
                    inspectionDetailsDto.getDesktopInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
                    desktopInspectionDetailsDao.insertMaster(connection, inspectionDetailsDto.getDesktopInspectionDetailsDto());
//                    if (AppConstant.YES.equals(inspectionDetailsDto.getDesktopInspectionDetailsDto().getAriSalvage().getCondtionType())) {
//                        isAri = true;
//                    }

                    break;
                default://On site Inspection

//                    if ("SUBMIT".equals(inspectionDetailsDto.getActionType())
//                            && AppConstant.YES.equals(inspectionDetailsDto.getOnSiteInspectionDetailsDto().getRequestAri().getCondtionType())) {
//                        isAri = true;
//                    }

                    //change onsite offer type
                    saveOnsiteOffer(connection, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType());
                    if (AppConstant.PROVIDE_OFFER == inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
                        inspectionDetailsDto.getOnSiteInspectionDetailsDto().setProvideOffer(ConditionType.Yes);
                    } else {
                        inspectionDetailsDto.getOnSiteInspectionDetailsDto().setProvideOffer(ConditionType.No);
                    }
                    inspectionDetailsDto.getOnSiteInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
                    onSiteInspectionDetailsDao.insertMaster(connection, inspectionDetailsDto.getOnSiteInspectionDetailsDto());
                    break;
            }

            RequestAriDto requestAri = requestAriDao.searchByClaimNo(connection, inspectionDetailsDto.getClaimNo());

//            if (isAri) {
//                RequestAriDto requestAriDto = new RequestAriDto();
//                if (null == requestAri) {
//                    requestAriDto.setClaimNo(inspectionDetailsDto.getClaimNo());
//                    ClaimsDto polClaim = callCenterService.getReportAccidentClaimsDtoByClaimNo(inspectionDetailsDto.getClaimNo());
//                    requestAriDto.setAssigningAssessorCode(user.getV_emp_no());
//                    requestAriDto.setRteCode(user.getReportingTo());
//                    requestAriDto.setCustomerName(polClaim.getPolicyDto().getCustName());
//                    requestAriDto.setAddress1(polClaim.getPolicyDto().getCustAddressLine1());
//                    requestAriDto.setAddress2(polClaim.getPolicyDto().getCustAddressLine2());
//                    requestAriDto.setAddress3(polClaim.getPolicyDto().getCustAddressLine3());
//                    requestAriDto.setContactNo(polClaim.getPolicyDto().getCustMobileNo());
//                    requestAriDto.setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getJobId());
//                    requestAriService.insert(requestAriDto, user, connection);
//                }
//
//            }

            requestAri = requestAriDao.searchByCompletedClaimNo(connection, inspectionDetailsDto.getClaimNo());
            if (null != requestAri && (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION || inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION)) {
                RequestAriDto requestAriDto = new RequestAriDto();
                requestAriDto.setStatus("S"); //S=submited
                requestAriDto.setRefId(inspectionDetailsDto.getRefNo());
                requestAriDto.setAssessorSubmittedDate(Utility.sysDateTime());
                requestAriDao.updateStatusAndDateByRefId(connection, requestAriDto);
            }


            AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, inspectionDetailsDto.getRefNo());
            if (null != assessorAllocationDto) {
                if (assessorAllocationDto.getJobStatusId() != ClaimStatus.COMPLETED.getClaimStatus()) {
                    assessorAllocationService.updateCompletedJob(inspectionDetailsDto.getRefNo(), user, connection);
                }
            }
            String url = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(inspectionDetailsDto.getRefNo()));



            commitTransaction(connection);
        } catch (Exception ex) {
            rollbackTransaction(connection);
            LOGGER.error(ex.getMessage());;
            throw ex;
        } finally {
            releaseJDBCConnection(connection);
        }
        return inspectionDetailsDto;
    }

    private void sendCallEstimateAssignedSmsToAgent(InspectionDetailsDto inspectionDetailsDto, Connection connection, UserDto user) throws Exception {
        if (inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType() == 3) {
            List<String> smsParameterList = new ArrayList<>();
            sendSmsMessage(connection, 45, smsParameterList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicyChannelType(), user, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), AppConstant.SEND_TO_AGENT);
        }
    }

    private void sendCallEstimateAssignedSmsToCustomer(InspectionDetailsDto inspectionDetailsDto, Connection connection, UserDto user) throws Exception {
        if (inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType() == 3) {
            List<String> smsParameterList = new ArrayList<>();
            sendSmsMessage(connection, 44, smsParameterList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustMobileNo(), inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicyChannelType(), user, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
        }
    }

    private void saveOnsiteOffer(Connection connection, int claimNo, int offerType) throws Exception {
        try {
            claimHandlerDao.updateOnsiteOffer(connection, claimNo, offerType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public InspectionDetailsDto update(InspectionDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            //Set Audit Details
            inspectionDetailsDto.setInputDatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            inspectionDetailsDto.setInputUserId(user.getUserId());

            //TODO
            //Motor Engineer Assign User Set - V_REPORT_TO
            inspectionDetailsDto.setAssignRteUser(user.getReportingTo());
            inspectionDetailsDto.setAssignRteDatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            inspectionDetailsDto.setApproveAssignRteUser(user.getReportingTo());
            inspectionDetailsDto.setApproverAssignRteDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            //Set Nested Objects Values To Most Outer Object
            inspectionDetailsDto.setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
            inspectionDetailsDto.setJobId(inspectionDetailsDto.getAssessorAllocationDto().getJobId());
            inspectionDetailsDto.setClaimNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            inspectionDetailsDto.getInspectionDto().setInspectionId(inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());

            switch (inspectionDetailsDto.getActionType()) {
                case "DRAFT":
                    //Set and Updated Assessor Inspection Detail Record Status as 7 --> ATTENDED
                    inspectionDetailsDto.setRecordStatus(7);

                    //Set and Updated Assessor allocation Record Status as 7 --> ATTENDED
                    assessorAllocationDao.updateRecordStatusByRefNo(connection, ClaimStatus.ATTENDED.getClaimStatus(), inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
                    break;
                case "SUBMIT":
                    //Set and Updated Assessor Inspection Detail Record Status as 8 --> SUBMITTED
                    inspectionDetailsDto.setRecordStatus(8);

                    //Set and Updated Assessor allocation Record Status as 8 --> SUBMITTED
                    assessorAllocationDao.updateRecordStatusByRefNo(connection, ClaimStatus.SUBMITTED.getClaimStatus(), inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

                    claimWiseDocumentService.updateDocumentInspectionWise(connection, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getInspectionDto().getInspectionId(), user);
                    break;
            }

            //update Online inspection status
//            assessorAllocationDao.updateOnlineInspectionStatus(connection, inspectionDetailsDto.getTypeOnlineInspection(), inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

            //Save INspection Details
            updateInspectionDetails(inspectionDetailsDto, connection); //Include saveAssessorProfessionalFee
            updateTyreCondition(inspectionDetailsDto.getTireCondtionDtoList(), connection, inspectionDetailsDto.getInputUserId(), inspectionDetailsDto);

            //save third Party Assesssor Dtos
            saveThirdPartyAssessorDetails(inspectionDetailsDto, user, connection);

            //save special remarks seperately on insert and update record
            //    saveSpecialRemark(inspectionDetailsDto, user, connection);
//            LoggerTrail<ClaimsDto> loggerTrail = new LoggerTrail<>();
//
//            List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(savedClaimDto, oldClaimDto, 1);
//            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, savedClaimDto.getClaimNo(), user.getV_usrid(), 1);

            //Save Assessor Estimate For Repair as selected by Inspection Type
            switch (inspectionDetailsDto.getInspectionDto().getInspectionId()) {
                case 4: //Garage Inspection
                    inspectionDetailsDto.getGarageInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

                    AssessorLoggerTrail<GarageInspectionDetailsDto> loggerGarageTrail = new AssessorLoggerTrail<>();
                    GarageInspectionDetailsDto oldGarage = garageInspectionDetailsDao.searchMaster(connection, inspectionDetailsDto.getRefNo());
                    List<ClaimLogTrailDto> loggerGarageTrailList = loggerGarageTrail.getLoggerTrailDetailsList(inspectionDetailsDto.getGarageInspectionDetailsDto(), oldGarage, AppConstant.ASSESSOR_MODULE_LOG);
                    loggerTrailDao.insertLoggerTrailList(connection, loggerGarageTrailList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);

                    garageInspectionDetailsDao.updateMaster(connection, inspectionDetailsDto.getGarageInspectionDetailsDto());
                    break;
                case 5: //DR Insepection
                case 6: //Supplimantary Inspection
                    inspectionDetailsDto.getDrSuppInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

                    AssessorLoggerTrail<DrSupplementaryInspectionDetailsDto> loggerDrTrail = new AssessorLoggerTrail<>();
                    DrSupplementaryInspectionDetailsDto oldDr = drSupplementaryInspectionDetailsDao.searchMaster(connection, inspectionDetailsDto.getRefNo());
                    List<ClaimLogTrailDto> loggerDrList = loggerDrTrail.getLoggerTrailDetailsList(inspectionDetailsDto.getDrSuppInspectionDetailsDto(), oldDr, AppConstant.ASSESSOR_MODULE_LOG);
                    loggerTrailDao.insertLoggerTrailList(connection, loggerDrList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);

                    drSupplementaryInspectionDetailsDao.updateMaster(connection, inspectionDetailsDto.getDrSuppInspectionDetailsDto());
                    break;
                case 7: //After Repair inspection
                case 9: //Salvage Inspection
                    inspectionDetailsDto.getAriInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());
                    AssessorLoggerTrail<ARIInspectionDetailsDto> loggerAriTrail = new AssessorLoggerTrail<>();
                    ARIInspectionDetailsDto oldAri = aRIInspectionDetailsDao.searchMaster(connection, inspectionDetailsDto.getRefNo());
                    List<ClaimLogTrailDto> loggerArList = loggerAriTrail.getLoggerTrailDetailsList(inspectionDetailsDto.getAriInspectionDetailsDto(), oldAri, AppConstant.ASSESSOR_MODULE_LOG);
                    loggerTrailDao.insertLoggerTrailList(connection, loggerArList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);

                    aRIInspectionDetailsDao.updateMaster(connection, inspectionDetailsDto.getAriInspectionDetailsDto());
                    break;
                case 8: //Desktop Assesment
                    inspectionDetailsDto.getDesktopInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

                    //update log
                    AssessorLoggerTrail<DesktopInspectionDetailsDto> loggerTrail = new AssessorLoggerTrail<>();
                    DesktopInspectionDetailsDto oldInspection1 = desktopInspectionDetailsDao.searchMaster(connection, inspectionDetailsDto.getRefNo());
                    List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(inspectionDetailsDto.getDesktopInspectionDetailsDto(), oldInspection1, AppConstant.ASSESSOR_MODULE_LOG);
                    loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);

                    desktopInspectionDetailsDao.updateMaster(connection, inspectionDetailsDto.getDesktopInspectionDetailsDto());
                    break;
                default://On site Inspection

//                    if ("SUBMIT".equals(inspectionDetailsDto.getActionType())
//                            && ConditionType.Yes == inspectionDetailsDto.getOnSiteInspectionDetailsDto().getRequestAri()) {
//                        saveAri(inspectionDetailsDto, user, connection);
//                    }

                    inspectionDetailsDto.getOnSiteInspectionDetailsDto().setRefNo(inspectionDetailsDto.getAssessorAllocationDto().getRefNo());

                    if (AppConstant.PROVIDE_OFFER == inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
                        inspectionDetailsDto.getOnSiteInspectionDetailsDto().setProvideOffer(ConditionType.Yes);
                    } else {
                        inspectionDetailsDto.getOnSiteInspectionDetailsDto().setProvideOffer(ConditionType.No);
                    }

                    AssessorLoggerTrail<OnSiteInspectionDetailsDto> loggerOnSiteTrail = new AssessorLoggerTrail<>();
                    OnSiteInspectionDetailsDto oldOnSite = onSiteInspectionDetailsDao.searchMaster(connection, inspectionDetailsDto.getRefNo());
                    List<ClaimLogTrailDto> loggerOnList = loggerOnSiteTrail.getLoggerTrailDetailsList(inspectionDetailsDto.getOnSiteInspectionDetailsDto(), oldOnSite, AppConstant.ASSESSOR_MODULE_LOG);
                    loggerTrailDao.insertLoggerTrailList(connection, loggerOnList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);

                    onSiteInspectionDetailsDao.updateMaster(connection, inspectionDetailsDto.getOnSiteInspectionDetailsDto());
                    saveOnsiteOffer(connection, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getOnSiteInspectionDetailsDto().getOfferType());
                    break;
            }
            commitTransaction(connection);
        } catch (Exception ex) {
            rollbackTransaction(connection);
            LOGGER.error(ex.getMessage());;
            throw ex;
        } finally {
            releaseJDBCConnection(connection);
        }
        return inspectionDetailsDto;
    }

    @Override
    public InspectionDetailsDto delete(InspectionDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto updateAuthPending(InspectionDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto deleteAuthPending(InspectionDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public InspectionDetailsDto search(Object id) throws Exception {
        Connection connection = null;
        InspectionDetailsDto inspectionDetailsDto = null;
        try {
            connection = getJDBCConnection();
            inspectionDetailsDto = this.search(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return inspectionDetailsDto;
    }

    @Override
    public InspectionDetailsDto search(Connection connection, Object id) throws Exception {

        InspectionDetailsDto inspectionDetailsDto = null;
        try {
            AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, id);
            inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, id);
            if (null == inspectionDetailsDto) {
                return null;
            }
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            List<TireCondtionDto> tireCondtionDtos = tireCondtionDao.searchByClaimNoAndRefNo(connection, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo());
            inspectionDetailsDto.setTireCondtionDtoList(tireCondtionDtos);
            if (null != inspectionDetailsDto.getInspectionDto()) {
                if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
                    GarageInspectionDetailsDto garageInspectionDetailsDto = garageInspectionDetailsDao.searchMaster(connection, id);
                    if (null != garageInspectionDetailsDto) {
                        inspectionDetailsDto.setGarageInspectionDetailsDto(garageInspectionDetailsDto);
                    }
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
                    DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsDao.searchMaster(connection, id);
                    if (null != drSupplementaryInspectionDetailsDto) {
                        inspectionDetailsDto.setDrSuppInspectionDetailsDto(drSupplementaryInspectionDetailsDto);
                    }
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION
                        || inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
                    ARIInspectionDetailsDto ariInspectionDetailsDto = aRIInspectionDetailsDao.searchMaster(connection, id);
                    if (null != ariInspectionDetailsDto) {
                        inspectionDetailsDto.setAriInspectionDetailsDto(ariInspectionDetailsDto);
                    }
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    DesktopInspectionDetailsDto desktopInspectionDetailsDto = desktopInspectionDetailsDao.searchMaster(connection, id);
                    if (null != desktopInspectionDetailsDto) {
                        inspectionDetailsDto.setDesktopInspectionDetailsDto(desktopInspectionDetailsDto);
                    }
                } else {
                    OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = onSiteInspectionDetailsDao.searchMaster(connection, id);
                    if (null != onSiteInspectionDetailsDto) {
                        inspectionDetailsDto.setOnSiteInspectionDetailsDto(onSiteInspectionDetailsDto);
                    }
                }
                if (null != assessorAllocationDto) {
                    List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, AppConstant.ASSESSOR_MODULE_LOG, assessorAllocationDto.getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo());
                    inspectionDetailsDto.setLogList(loggerTrailList);
                }

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return inspectionDetailsDto;
    }

    @Override
    public InspectionDetailsDto searchByMe(Connection connection, Object id) throws Exception {
        InspectionDetailsDto inspectionDetailsDto = null;
        try {
            AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, id);
            inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, id);
            if (null == inspectionDetailsDto) {
                return null;
            }
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            List<TireCondtionDto> tireCondtionDtos = tireCondtionDao.searchByClaimNoAndRefNo(connection, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo());
            inspectionDetailsDto.setTireCondtionDtoList(tireCondtionDtos);
            if (null != inspectionDetailsDto.getInspectionDto()) {
                if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
                    GarageInspectionDetailsDto garageInspectionDetailsDto = garageInspectionDetailsMeDao.searchMaster(connection, id);
                    if (null != garageInspectionDetailsDto) {
                        inspectionDetailsDto.setGarageInspectionDetailsDto(garageInspectionDetailsDto);
                    }
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
                    DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsMeDao.searchMaster(connection, id);
                    if (null != drSupplementaryInspectionDetailsDto) {
                        inspectionDetailsDto.setDrSuppInspectionDetailsDto(drSupplementaryInspectionDetailsDto);
                    }
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION
                        || inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
                    ARIInspectionDetailsDto ariInspectionDetailsDto = ariInspectionDetailsMeDao.searchMaster(connection, id);
                    if (null != ariInspectionDetailsDto) {
                        inspectionDetailsDto.setAriInspectionDetailsDto(ariInspectionDetailsDto);
                    }
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    DesktopInspectionDetailsDto desktopInspectionDetailsDto = desktopInspectionDetailsMeDao.searchMaster(connection, id);
                    if (null != desktopInspectionDetailsDto) {
                        inspectionDetailsDto.setDesktopInspectionDetailsDto(desktopInspectionDetailsDto);
                    }
                } else {
                    OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = onSiteInspectionDetailsMeDao.searchMaster(connection, id);
                    if (null != onSiteInspectionDetailsDto) {
                        inspectionDetailsDto.setOnSiteInspectionDetailsDto(onSiteInspectionDetailsDto);
                    }
                }
                if (null != assessorAllocationDto) {
                    List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, AppConstant.ASSESSOR_MODULE_LOG, assessorAllocationDto.getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo());
                    inspectionDetailsDto.setLogList(loggerTrailList);
                }

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return inspectionDetailsDto;
    }

    @Override
    public ErrorMessageDto saveRemark(SpecialRemarkDto specialRemarkDto, UserDto user) throws Exception {
        Connection connection = null;
        ErrorMessageDto errorMessageDto = null;
        try {
            connection = getJDBCConnection();
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setDepartmentId(AppConstant.ASSESSOR_DEPARTMENT_ID);
//            specialRemarkDto.setSectionName(AppConstant.ASSESSOR_SECTION);

            if (null != specialRemarkDto.getClaimNo() || specialRemarkDto.getClaimNo() != 0) {
                specialRemarkDto = specialRemarkDao.insertMaster(connection, specialRemarkDto);
            }

            if (null != specialRemarkDto) {
                errorMessageDto = new ErrorMessageDto();
                errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                errorMessageDto.setMessage("Remark Added");
            } else {
                errorMessageDto = new ErrorMessageDto();
                errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                errorMessageDto.setMessage("Remark Added fail");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return errorMessageDto;
    }

    @Override
    public String getAssessorTypeByRefNo(String refNo) throws Exception {
        Connection connection = null;
        String assessorType = null;
        try {
            connection = getJDBCConnection();
            assessorType = assessorAllocationDao.getAssessorTypeByRefNo(connection, refNo);
            if (null == assessorType || AppConstant.STRING_EMPTY.equalsIgnoreCase(assessorType)) {
                assessorType = inspectionDao.getAssessorTypeByRefNo(connection, refNo);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return assessorType;

    }

    @Override
    public Integer getInspectionType(Integer refNo, Integer claimNo) throws Exception {
        Connection connection = null;
        Integer inspectionType = null;
        try {
            connection = getJDBCConnection();
            inspectionType = inspectionDao.getInspectionType(connection, refNo, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return inspectionType;
    }

    @Override
    public Integer getRefNoForOnOrOffSite(Integer claimNo) throws Exception {
        Connection connection = null;
        Integer prevRefNo = null;
        try {
            connection = getJDBCConnection();
            prevRefNo = inspectionDao.getRefNoForOnOrOffSite(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return prevRefNo;
    }

    @Override
    public DataGridDto getSubmittedInspectionOfferDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, String isRteOrAssessorDetails, String type, String inspectionId) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = inspectionDetailsDao.getSubmittedInspectionDetailsOfferGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, user, offerType, isRteOrAssessorDetails, type, inspectionId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public boolean isRteApproved(Integer refNo) throws Exception {
        Connection connection = null;
        boolean isApproved = false;
        try {
            connection = getJDBCConnection();
            isApproved = inspectionDetailsDao.isRteApproved(connection, refNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return isApproved;
    }

    @Override
    public boolean isAssessorApproved(Integer refNo) throws Exception {
        Connection connection = null;
        boolean isApproved = false;
        try {
            connection = getJDBCConnection();
            isApproved = inspectionDetailsDao.isAssessorApproved(connection, refNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return isApproved;
    }

    @Override
    public List<AssessorDto> getAllAssignAssessorsByClaim(Integer claimNo) {
        Connection connection = null;
        List<AssessorDto> assessorDtoList = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            assessorDtoList = inspectionDetailsDao.getAssignAssessorsByClaim(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return assessorDtoList;
    }

    @Override
    public BigDecimal getMileageFee(String assessorType) {
        Connection connection = null;
        BigDecimal mileageFee = BigDecimal.ZERO;
        try {
            connection = getJDBCConnection();
            mileageFee = assessorMileageFeeDao.getMileageFeeByAssessorType(connection, assessorType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return mileageFee;
    }

    @Override
    public BigDecimal getAssessorFee(Integer assessorFeeDetailId, String assessorType) {
        Connection connection = null;
        BigDecimal mileageFee = BigDecimal.ZERO;
        try {
            connection = getJDBCConnection();
            mileageFee = assessorFeeDao.getMileageFeeByAssessorType(connection, assessorFeeDetailId, assessorType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return mileageFee;
    }

    @Override
    public DataGridDto getSparePartsCoordInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, Integer status) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<RtePendingClaimsDto> allPendingClaims = rtePendingClaimDetailDao.getAllPendingClaimsFromSparePartsCoord(connection);
            dataGridDto = inspectionDetailsDao.getInspectionDetailsOfferGridDtoByUser(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, user, offerType, allPendingClaims, status);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getScrutinizingInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, Integer status) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            List<RtePendingClaimsDto> allPendingClaims = rtePendingClaimDetailDao.getAllPendingClaimsFromScrutinizingTeam(connection);
            dataGridDto = inspectionDetailsDao.getInspectionDetailsOfferGridDtoByUser(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, user, offerType, allPendingClaims, status);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getInspectionGridDtoForwardToRte(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = inspectionDetailsDao.getSubmittedInspectionDetailsForwardedGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, user, offerType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public InspectionDetailsDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<InspectionDetailsDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<InspectionDetailsDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public DataGridDto getClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = inspectionDetailsDao.getJobDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getSubmittedInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, String assignUser, String status) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = inspectionDetailsDao.getSubmittedInspectionDetailsGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, user, assignUser, status);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public DataGridDto getFwdDesktopInspectionDetailsGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, int recordStatus) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;

        try {
            connection = getJDBCConnection();
            dataGridDto = inspectionDetailsDao.getFwdDesktopInspectionDetailsGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate, user, recordStatus);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<ClaimImageDto> findAllClaimImageDtoByClaimNo(Integer claimNo) {
        List<ClaimImageDto> list = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimImageDao.findAllClaimImageDtoByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public InspectionDetailsDto setAcrValue(InspectionDetailsDto inspectionDetails, AssessorAllocationDto assessorAllocationDto) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchAndOrderRefNo(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), assessorAllocationDto.getRefNo());

            BigDecimal acr = BigDecimal.ZERO;

            if (null != inspectionDetailsDto) {
                Integer id = inspectionDetailsDto.getRefNo();

                if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
                    GarageInspectionDetailsDto garageInspectionDetailsDto = garageInspectionDetailsDao.searchMaster(connection, id);
                    acr = null != garageInspectionDetailsDto ? null == garageInspectionDetailsDto.getAcr() ?
                            BigDecimal.ZERO : garageInspectionDetailsDto.getAcr() : BigDecimal.ZERO;

                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
                    DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsDao.searchMaster(connection, id);
                    acr = null != drSupplementaryInspectionDetailsDto ? BigDecimal.ZERO : drSupplementaryInspectionDetailsDto.getAcr();
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION
                        || inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
                    ARIInspectionDetailsDto ariInspectionDetailsDto = aRIInspectionDetailsDao.searchMaster(connection, id);

                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    DesktopInspectionDetailsDto desktopInspectionDetailsDto = desktopInspectionDetailsDao.searchMaster(connection, id);
                    acr = null != desktopInspectionDetailsDto ? desktopInspectionDetailsDto.getAcr() : BigDecimal.ZERO;
                } else {
                    OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = onSiteInspectionDetailsDao.searchMaster(connection, id);
                    acr = null != onSiteInspectionDetailsDto ? onSiteInspectionDetailsDto.getAcr() : BigDecimal.ZERO;
                }

                //Add to main object
                setAcrValue(inspectionDetails, acr, assessorAllocationDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            LOGGER.debug(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return inspectionDetails;
    }

    @Override
    public List<SpecialRemarkDto> searchRemarksByClaimNo(Integer claimNo, Integer departmentId) throws Exception {
        Connection connection = null;
        List<SpecialRemarkDto> list = null;
        try {
            connection = getJDBCConnection();
            list = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo, departmentId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<SpecialRemarkDto> searchRemarksByClaimNoMultipleDepartmentId(Integer claimNo, String departmentId) throws Exception {
        Connection connection = null;
        List<SpecialRemarkDto> list = null;
        try {
            connection = getJDBCConnection();
            list = specialRemarkDao.searchRemarksByClaimNoMultipleDepartmentId(connection, claimNo, departmentId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    private InspectionDetailsDto saveInspectionDetails(InspectionDetailsDto inspectionDetailsDto, Connection connection) throws Exception {
        return inspectionDetailsDao.insertMaster(connection, inspectionDetailsDto);
    }

    private List<TireCondtionDto> saveTyreCondition(List<TireCondtionDto> tireCondtionDtoList, Connection connection) throws Exception {
        for (TireCondtionDto tireCondtionDto : tireCondtionDtoList) {
            tireCondtionDao.insertMaster(connection, tireCondtionDto);
        }
        return tireCondtionDtoList;
    }

    private InspectionDetailsDto updateInspectionDetails(InspectionDetailsDto inspectionDetailsDto, Connection connection) throws Exception {
        AssessorLoggerTrail<InspectionDetailsDto> loggerTrail = new AssessorLoggerTrail<>();
        InspectionDetailsDto oldInspection = inspectionDetailsDao.searchMaster(connection, inspectionDetailsDto.getRefNo());
        List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(inspectionDetailsDto, oldInspection, 4);
        loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);
        return inspectionDetailsDao.updateMaster(connection, inspectionDetailsDto);
    }

    private void initialLog(InspectionDetailsDto inspectionDetailsDto, Connection connection) throws Exception {
        List<ClaimLogTrailDto> loggerTrailList = new ArrayList<>();
        ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
        claimLogTrailDto.setFieldName("Submission");
        claimLogTrailDto.setFieldValue("Inspection Detail Submission");
        claimLogTrailDto.setClaimNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
        claimLogTrailDto.setInputDateTime(Utility.sysDateTime());
        claimLogTrailDto.setUserId(inspectionDetailsDto.getInputUserId());
        claimLogTrailDto.setFormNameId(AppConstant.ASSESSOR_MODULE_LOG);
        loggerTrailList.add(claimLogTrailDto);
        loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);

    }

    private List<TireCondtionDto> updateTyreCondition(List<TireCondtionDto> tireCondtionDtoList, Connection connection, String userId, InspectionDetailsDto inspectionDetailsDto) throws Exception {
        int index = 0;
        List<ClaimLogTrailDto> list = new ArrayList<>();
        int claimNo = AppConstant.ZERO_INT;
        for (TireCondtionDto tireCondtionDto : tireCondtionDtoList) {
            TireCondtionDto oldTireCondtionDto = tireCondtionDao.searchTireCondtionByClaimNoAndPosition(connection, tireCondtionDto);
            claimNo = tireCondtionDto.getClaimsDto().getClaimNo();
            List<ClaimLogTrailDto> claimLogTrailDtos = saveTireCondtionLog(tireCondtionDto, oldTireCondtionDto, index, claimNo, userId);
            if (null != claimLogTrailDtos && claimLogTrailDtos.size() > 0) {
                loggerTrailDao.insertLoggerTrailList(connection, claimLogTrailDtos, claimNo, inspectionDetailsDto.getRefNo(), userId, 1);
            }

            index++;
            tireCondtionDao.updateMaster(connection, tireCondtionDto);
        }

        return tireCondtionDtoList;
    }

    private void saveSpecialRemark(InspectionDetailsDto inspectionDetailsDto, UserDto user, Connection connection) {
        SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
        try {
            if (null != inspectionDetailsDto.getAssessorAllocationDto() && null != inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto()) {
                if (!inspectionDetailsDto.getInspectionSpecialRemark().isEmpty()) {
                    specialRemarkDto.setInputDateTime(Utility.sysDateTime());
                    specialRemarkDto.setInputUserId(user.getUserId());
                    specialRemarkDto.setDepartmentId(AppConstant.ASSESSOR_MODULE);
                    specialRemarkDto.setSectionName(AppConstant.ASSESSOR_MODULE_SECTION);
                    specialRemarkDto.setClaimNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
                    specialRemarkDto.setRemark(inspectionDetailsDto.getInspectionSpecialRemark());
                    specialRemarkDao.insertMaster(connection, specialRemarkDto);
                    specialRemarkDto.setRemark(AppConstant.STRING_EMPTY);
                }
                if (!inspectionDetailsDto.getAssessorSpecialRemark().isEmpty()) {
                    specialRemarkDto.setInputDateTime(Utility.sysDateTime());
                    specialRemarkDto.setInputUserId(user.getUserId());
                    specialRemarkDto.setDepartmentId(AppConstant.ASSESSOR_MODULE);
                    specialRemarkDto.setSectionName(AppConstant.ASSESSOR_MODULE_SECTION);
                    specialRemarkDto.setClaimNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
                    specialRemarkDto.setRemark(inspectionDetailsDto.getAssessorSpecialRemark());
                    specialRemarkDao.insertMaster(connection, specialRemarkDto);
                    specialRemarkDto.setRemark(AppConstant.STRING_EMPTY);
                }

                int inspectionId = inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId();

                if (inspectionId == AppConstant.GARAGE_INSPECTION) {
                    if (null != inspectionDetailsDto.getGarageInspectionDetailsDto()
                            && !inspectionDetailsDto.getGarageInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(inspectionDetailsDto.getGarageInspectionDetailsDto().getSpecialRemark());
                    }
                } else if (inspectionId == AppConstant.DESKTOP_INSPECTION) {
                    if (null != inspectionDetailsDto.getDesktopInspectionDetailsDto()
                            && !inspectionDetailsDto.getDesktopInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(inspectionDetailsDto.getDesktopInspectionDetailsDto().getSpecialRemark());
                    }
                } else if (inspectionId == AppConstant.ARI_INSPECTION || inspectionId == AppConstant.SALVAGE_INSPECTION) {
                    if (null != inspectionDetailsDto.getAriInspectionDetailsDto()
                            && !inspectionDetailsDto.getAriInspectionDetailsDto().getAssessorSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(inspectionDetailsDto.getAriInspectionDetailsDto().getAssessorSpecialRemark());
                    }
                } else {
                    if (null != inspectionDetailsDto.getOnSiteInspectionDetailsDto()
                            && !inspectionDetailsDto.getOnSiteInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(inspectionDetailsDto.getOnSiteInspectionDetailsDto().getSpecialRemark());
                    }
                }
                if (null != specialRemarkDto.getRemark() && !specialRemarkDto.getRemark().isEmpty()) {
                    specialRemarkDao.insertMaster(connection, specialRemarkDto);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    @Override
    public ClaimInspectionTypeDto getInspectionTypeDto(String id) throws Exception {
        Connection connection = null;
        ClaimInspectionTypeDto claimInspectionTypeDto = null;
        try {
            connection = getJDBCConnection();
            claimInspectionTypeDto = inspectionDao.getInspectionTypeDto(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimInspectionTypeDto;
    }

    @Override
    public List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Integer departmentId, Integer inspectionTypeId) {
        List<ClaimDocumentTypeDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimDocumentTypeDao.getClaimDocumentTypeDtoList(connection, departmentId, inspectionTypeId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<ClaimDocumentDto> getClaimDocumentDtoList(Integer jobRefNo, Integer departmentId) {
        List<ClaimDocumentDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimDocumentDao.getClaimDocumentDtoList(connection, jobRefNo, departmentId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<ClaimThirdPartyDetailsGenericDto> getClaimThirdPartyDetailsGeneric(Integer claimNo) throws Exception {
        Connection connection = null;
        List<ClaimThirdPartyDetailsGenericDto> thirdPartyDtos = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            thirdPartyDtos = thirdPartyAssessorDao.searchAll(connection, claimNo);
            for (ClaimThirdPartyDetailsGenericDto thirdPartyDto : thirdPartyDtos) {
                thirdPartyDto.setType("ASSESSOR");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return thirdPartyDtos;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo, Integer jobRefNo, Integer departmentId, Integer inspectionTypeId) {
        Connection connection = null;
        List<ClaimUploadViewDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            list = claimDocumentDao.getClaimUploadViewDtoList(connection, claimNo, jobRefNo, departmentId, inspectionTypeId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> getPreviousClaimList(String vehicleNo, Integer claim) throws Exception {
        Connection connection = null;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<Integer> claimList = inspectionDetailsDao.getClaimListByPolNo(vehicleNo, connection);
            for (Integer claimNo : claimList) {
                List<PreviousClaimsDto> previousClaimList = inspectionDetailsDao.getPreviousClaimList(claimNo, connection);
                if (!claim.equals(claimNo)) {
                    if (previousClaimList.size() > 0) {
                        PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                        previousClaimsDto.setClaimNo(claimNo);
                        previousClaimsDto.setList(previousClaimList);
                        list.add(previousClaimsDto);
                    } else {
                        PreviousClaimsDto previousClaimsDto1 = callCenterDao.getPreviousClaimDetails(connection, claimNo);
                        previousClaimList.add(previousClaimsDto1);
                        PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                        previousClaimsDto.setClaimNo(claimNo);
                        previousClaimsDto.setList(previousClaimList);
                        list.add(previousClaimsDto);
                    }
                }

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;

    }

    @Override
    public List<PreviousClaimsDto> getPreviousInspectionClaimList(Integer claimNo, Integer jobNo) throws Exception {
        Connection connection = null;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();

            List<PreviousClaimsDto> previousClaimList = inspectionDetailsDao.getPreviousClaimList(claimNo, jobNo, connection);
            if (previousClaimList.size() > 0) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setClaimNo(claimNo);
                previousClaimsDto.setList(previousClaimList);
                list.add(previousClaimsDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> getPreviousInspectionList(Integer claimNo) throws Exception {
        Connection connection = null;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<PreviousClaimsDto> previousClaimList = inspectionDetailsDao.getInspectionList(claimNo, connection);
            if (previousClaimList.size() > 0) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setClaimNo(claimNo);
                previousClaimsDto.setList(previousClaimList);
                list.add(previousClaimsDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<ClaimImageDto> getClaimImageDtoList(Integer claimNo, Integer jobRefNo) {
        List<ClaimImageDto> list = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimImageDao.getClaimImageDtoList(connection, claimNo, jobRefNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<ClaimImageFormDto> getClaimImageFormDtoList(Integer claimNo) {
        Connection connection = null;
        List<ClaimImageFormDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<InspectionFormDto> inspectionFormDtoList = motorEngineerDetailsDao.getInspectionFormDtoList(connection, claimNo);
            for (InspectionFormDto inspectionFormDto :
                    inspectionFormDtoList) {
                ClaimImageFormDto claimImageFormDto = new ClaimImageFormDto();
                claimImageFormDto.setRefNo(inspectionFormDto.getRefNo());
                claimImageFormDto.setInspectionType(inspectionFormDto.getInspectionType());
                claimImageFormDto.setClaimImageDtoList(claimImageDao.getClaimImageDtoList(connection, claimNo, inspectionFormDto.getRefNo()));
                list.add(claimImageFormDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    private void saveThirdPartyAssessorDetails(InspectionDetailsDto inspectionDetailsDto, UserDto user, Connection connection) throws Exception {
        Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = inspectionDetailsDto.getThirdPartyAssessorMap();
        for (Map.Entry<String, ClaimThirdPartyDetailsGenericDto> entry : thirdPartyAssessorMap.entrySet()) {
            String key = entry.getKey();
            ClaimThirdPartyDetailsGenericDto value = entry.getValue();
            if ("NEW".equals(value.getStatus())) {
                thirdPartyAssessorDao.insertMaster(connection, value);
            } else if ("EDIT".equals(value.getStatus())) {
                thirdPartyAssessorDao.updateMaster(connection, value);
            }

        }
    }

    private List<ClaimLogTrailDto> saveTireCondtionLog(TireCondtionDto newValue, TireCondtionDto oldValue, int index, Integer claimNo, String inputUserId) {
        String[] postions = {"Condition", "Size", "Make", "New/RB"};

        List<ClaimLogTrailDto> list = new ArrayList<>();
        boolean isChanged = false;

        String postionValue = postions[index];
        if (!newValue.getLf().equals(oldValue.getLf())) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" LF"));
            claimLogTrailDto.setFieldValue(newValue.getLf());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);

        }

        if (!newValue.getRf().equals(oldValue.getRf()) && !newValue.getRf().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" RF"));
            claimLogTrailDto.setFieldValue(newValue.getRf());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        if (!newValue.getRr().equals(oldValue.getRr()) && !newValue.getRr().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" RR"));
            claimLogTrailDto.setFieldValue(newValue.getRr());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        if (!newValue.getRri().equals(oldValue.getRri()) && !newValue.getRri().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" RRI"));
            claimLogTrailDto.setFieldValue(newValue.getRri());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }
        if (!newValue.getLri().equals(oldValue.getLri()) && !newValue.getLri().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" LRI"));
            claimLogTrailDto.setFieldValue(newValue.getLri());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        if (!newValue.getOther().equals(oldValue.getOther()) && !newValue.getOther().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" Other"));
            claimLogTrailDto.setFieldValue(newValue.getOther());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        return list;

    }

    private void geClaimsDto(ClaimLogTrailDto claimLogTrailDto, TireCondtionDto newValue, String inputUserId, Integer claimNo) {
        claimLogTrailDto.setClaimNo(claimNo);
        claimLogTrailDto.setInputDateTime(Utility.sysDateTime());
        claimLogTrailDto.setUserId(inputUserId);
        claimLogTrailDto.setFormNameId(AppConstant.ASSESSOR_MODULE_LOG);

    }

    private void saveAri(InspectionDetailsDto inspectionDetailsDto, UserDto user, Connection connection) throws Exception {
        RequestAriDto requestAriDto = new RequestAriDto();
        try {
            requestAriDto.setClaimNo(inspectionDetailsDto.getClaimNo());
            requestAriDto.setVehicleNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getVehicleNo());
            requestAriDto.setCustomerName(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustName());
            requestAriDto.setContactNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustMobileNo());
            requestAriDto.setAccidentDate(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getAccidDate());
            requestAriDto.setRequestedUser(user.getUserId());
            requestAriDto.setRequestedDate(Utility.sysDateTime());
            requestAriDto.setAssignedAssessor(inspectionDetailsDto.getAssessorAllocationDto().getAssessorDto().getName());
            requestAriDto.setAddress1(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustAddressLine1());
            requestAriDto.setAddress2(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustAddressLine2());
            requestAriDto.setAddress3(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustAddressLine3());
            requestAriDto.setAddress3(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustAddressLine3());
            requestAriDto.setInpUserId(user.getUserId());
            requestAriDto.setInpDateTime(Utility.sysDateTime());
            requestAriDto.setAssignedAssessorCode(inspectionDetailsDto.getAssessorAllocationDto().getAssessorDto().getCode());
            //TODO
//            requestAriDto.setThirdPartyInvoled("");
            requestAriService.insert(requestAriDto, user, connection);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

//    private void saveNotification(UserDto user, InspectionDetailsDto inspectionDetailsDto, Connection connection) throws Exception {
//        NotificationDto notificationDto = new NotificationDto();
//        try {
//            notificationDto.setInpUserId(user.getV_usrid());
//            notificationDto.setMessage("Ari Requested to  ".concat(inspectionDetailsDto.getAssessorAllocationDto().getJobId()));
//            notificationDto.setClaimNo(inspectionDetailsDto.getClaimNo());
//            notificationDto.setNotifyDateTime(Utility.sysDateTime());
//            notificationDto.setReadDateTime(AppConstant.DEFAULT_DATE_TIME);
//            notificationDto.setRefNo(inspectionDetailsDto.getRefNo());
//            notificationDto.setUrl(AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(inspectionDetailsDto.getClaimNo())));
//            notificationDto.setAssignUserId(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getInpUser());
//            notificationDao.insertMaster(connection, notificationDto);
//        } catch (Exception e) {
//            LOGGER.error(e.getMessage());
//            throw new Exception(e);
//        }
    //}


    @Override
    public InspectionDetailsDto setAcrValue(InspectionDetailsDto inspectionDetails, BigDecimal acr, AssessorAllocationDto assessorAllocationDto) {
        if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
            GarageInspectionDetailsDto garageInspectionDetailsDto = inspectionDetails.getGarageInspectionDetailsDto();
            if (null != garageInspectionDetailsDto) {
                garageInspectionDetailsDto.setAcr(acr);
                inspectionDetails.setGarageInspectionDetailsDto(garageInspectionDetailsDto);
            }

        } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
            DrSupplementaryInspectionDetailsDto drSuppInspectionDetailsDto = inspectionDetails.getDrSuppInspectionDetailsDto();
            if (null != drSuppInspectionDetailsDto) {
                drSuppInspectionDetailsDto.setAcr(acr);
                inspectionDetails.setDrSuppInspectionDetailsDto(drSuppInspectionDetailsDto);
            }

        } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION
                || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {


        } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
            DesktopInspectionDetailsDto desktopInspectionDetailsDto = inspectionDetails.getDesktopInspectionDetailsDto();
            if (null != desktopInspectionDetailsDto) {
                desktopInspectionDetailsDto.setAcr(acr);
                inspectionDetails.setDesktopInspectionDetailsDto(desktopInspectionDetailsDto);
            }

        } else {
            OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = inspectionDetails.getOnSiteInspectionDetailsDto();
            onSiteInspectionDetailsDto.setAcr(acr);
            inspectionDetails.setOnSiteInspectionDetailsDto(onSiteInspectionDetailsDto);
        }

        return inspectionDetails;
    }

    @Override
    public InspectionDetailsDto setPenaltyValue(InspectionDetailsDto inspectionDetails, AssessorAllocationDto assessorAllocationDto) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchAndOrderRefNo(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), assessorAllocationDto.getRefNo());
            BigDecimal underPenaly = BigDecimal.ZERO;
            BigDecimal underPenalyPercentage = BigDecimal.ZERO;
            BigDecimal bladPenaty = BigDecimal.ZERO;
            BigDecimal bladPenatyPercentage = BigDecimal.ZERO;
            if (null != inspectionDetailsDto) {
                Integer id = inspectionDetailsDto.getRefNo();
                if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
                    GarageInspectionDetailsDto garageInspectionDetailsDto = garageInspectionDetailsDao.searchMaster(connection, id);
                    underPenaly = null != garageInspectionDetailsDto ? garageInspectionDetailsDto.getUnderInsurancePenaltyAmount() : BigDecimal.ZERO;
                    underPenalyPercentage = null != garageInspectionDetailsDto ? garageInspectionDetailsDto.getUnderPenaltyPercent() : BigDecimal.ZERO;
                    bladPenaty = null != garageInspectionDetailsDto ? garageInspectionDetailsDto.getBoldTyrePenaltyAmount() : BigDecimal.ZERO;
                    bladPenatyPercentage = null != garageInspectionDetailsDto ? garageInspectionDetailsDto.getBoldPercent() : BigDecimal.ZERO;

                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
                    DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsDao.searchMaster(connection, id);
                    underPenaly = null != drSupplementaryInspectionDetailsDto ? drSupplementaryInspectionDetailsDto.getUnderInsurancePenaltyAmount() : BigDecimal.ZERO;
                    bladPenaty = null != drSupplementaryInspectionDetailsDto ? drSupplementaryInspectionDetailsDto.getBoldTyrePenaltyAmount() : BigDecimal.ZERO;
                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION
                        || inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
                    ARIInspectionDetailsDto ariInspectionDetailsDto = aRIInspectionDetailsDao.searchMaster(connection, id);

                } else if (inspectionDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    DesktopInspectionDetailsDto desktopInspectionDetailsDto = desktopInspectionDetailsDao.searchMaster(connection, id);
                    underPenaly = null != desktopInspectionDetailsDto ? desktopInspectionDetailsDto.getUnderInsurancePenaltyAmount() : BigDecimal.ZERO;
                    underPenalyPercentage = null != desktopInspectionDetailsDto ? desktopInspectionDetailsDto.getUnderPenaltyPercent() : BigDecimal.ZERO;
                    bladPenaty = null != desktopInspectionDetailsDto ? desktopInspectionDetailsDto.getBoldTyrePenaltyAmount() : BigDecimal.ZERO;
                    bladPenatyPercentage = null != desktopInspectionDetailsDto ? desktopInspectionDetailsDto.getBoldPercent() : BigDecimal.ZERO;
                } else {
                    OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = onSiteInspectionDetailsDao.searchMaster(connection, id);
                    underPenaly = null != onSiteInspectionDetailsDto ? onSiteInspectionDetailsDto.getUnderPenaltyAmount() : BigDecimal.ZERO;
                    underPenalyPercentage = null != onSiteInspectionDetailsDto ? onSiteInspectionDetailsDto.getUnderPenaltyPercent() : BigDecimal.ZERO;
                    bladPenaty = null != onSiteInspectionDetailsDto ? onSiteInspectionDetailsDto.getBoldTirePenaltyAmount() : BigDecimal.ZERO;
                    bladPenatyPercentage = null != onSiteInspectionDetailsDto ? onSiteInspectionDetailsDto.getBoldPercent() : BigDecimal.ZERO;
                }

                //Add to main object
                setPenaltyValue(inspectionDetails, bladPenaty, bladPenatyPercentage, underPenaly, underPenalyPercentage, assessorAllocationDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            LOGGER.debug(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return inspectionDetails;
    }

    @Override
    public InspectionDetailsDto setPenaltyValue(InspectionDetailsDto inspectionDetails, BigDecimal bladTyre, BigDecimal baldPercentage, BigDecimal underInsure, BigDecimal underPercentage, AssessorAllocationDto assessorAllocationDto) {
        if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
            GarageInspectionDetailsDto garageInspectionDetailsDto = inspectionDetails.getGarageInspectionDetailsDto();
            if (null != garageInspectionDetailsDto) {
                garageInspectionDetailsDto.setUnderInsurancePenaltyAmount(underInsure);
                garageInspectionDetailsDto.setUnderPenaltyPercent(underPercentage);
                garageInspectionDetailsDto.setBoldTyrePenaltyAmount(bladTyre);
                garageInspectionDetailsDto.setBoldPercent(baldPercentage);
                inspectionDetails.setGarageInspectionDetailsDto(garageInspectionDetailsDto);
            }

        } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
            DrSupplementaryInspectionDetailsDto drSuppInspectionDetailsDto = inspectionDetails.getDrSuppInspectionDetailsDto();
            if (null != drSuppInspectionDetailsDto) {
                drSuppInspectionDetailsDto.setUnderInsurancePenaltyAmount(underInsure);
                drSuppInspectionDetailsDto.setBoldTyrePenaltyAmount(bladTyre);
                inspectionDetails.setDrSuppInspectionDetailsDto(drSuppInspectionDetailsDto);
            }

        } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION || assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {


        } else if (assessorAllocationDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
            DesktopInspectionDetailsDto desktopInspectionDetailsDto = inspectionDetails.getDesktopInspectionDetailsDto();
            if (null != desktopInspectionDetailsDto) {
                desktopInspectionDetailsDto.setUnderInsurancePenaltyAmount(underInsure);
                desktopInspectionDetailsDto.setUnderPenaltyPercent(underPercentage);
                desktopInspectionDetailsDto.setBoldTyrePenaltyAmount(bladTyre);
                desktopInspectionDetailsDto.setBoldPercent(baldPercentage);
                inspectionDetails.setDesktopInspectionDetailsDto(desktopInspectionDetailsDto);
            }

        } else {
            OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = inspectionDetails.getOnSiteInspectionDetailsDto();
            onSiteInspectionDetailsDto.setUnderPenaltyAmount(underInsure);
            onSiteInspectionDetailsDto.setUnderPenaltyPercent(underPercentage);
            onSiteInspectionDetailsDto.setBoldTirePenaltyAmount(bladTyre);
            onSiteInspectionDetailsDto.setBoldPercent(baldPercentage);
            inspectionDetails.setOnSiteInspectionDetailsDto(onSiteInspectionDetailsDto);
        }

        return inspectionDetails;
    }

    @Override
    public List<ListBoxItem> getDesktopReassignReasons() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return inspectionDao.getReassignReasons(connection, true);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<ListBoxItem> getReassignReasons() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return inspectionDao.getReassignReasons(connection, false);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public DataGridDto getAssessorPendingClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate) {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = inspectionDetailsDao.getAssessorPendingJobDataGridDto(connection, parameterList, drawRandomId, start, length, columnOrder, orderColumnName, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    private void updateMaxJobId(Connection connection, AssessorAllocationDto assessorAllocationDto) {
        try {
            LOCK.lock();
            SequenceTableDto selectedKeyValue = sequenceKeyTableDao.getSelectedKeyValue(connection, AppConstant.JOB_TABLE_ID);

            //System Date to Reset Sequence
            String sysDate = Utility.sysDate("yyyyMMdd");

            //New Values
            int newKeyValue;
            String newSysDate;
            String formatNewSysDate;
            String jobNo;

            if (sysDate.equals(selectedKeyValue.getMaxSysDate())) {
                newKeyValue = selectedKeyValue.getMaxKeyValue();
                newSysDate = selectedKeyValue.getMaxSysDate();

            } else {
                newKeyValue = 1;
                newSysDate = sysDate;
                //jobNo = newSysDate + Utility.addZeroRJ(String.valueOf(newKeyValue), 4);
            }

            formatNewSysDate = Utility.getCustomDateFormat(newSysDate, AppConstant.DATE_FORMAT_INT, AppConstant.JOB_DATE_FORMAT);
            jobNo = formatNewSysDate + Utility.addZeroRJ(String.valueOf(newKeyValue), 4);

            if (null != selectedKeyValue) {
                assessorAllocationDto.setJobId(jobNo);
            }
            selectedKeyValue.setMaxKeyValue(newKeyValue + 1);
            selectedKeyValue.setMaxSysDate(newSysDate);
            selectedKeyValue.setTableId(AppConstant.JOB_TABLE_ID);
            sequenceKeyTableDao.updateMaster(connection, selectedKeyValue);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            LOCK.unlock();
        }
    }
}
