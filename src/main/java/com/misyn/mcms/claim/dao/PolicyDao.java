package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.PolicyStatusEnum;

import java.sql.Connection;
import java.util.List;

public interface PolicyDao {
    String INSERT_CLAIM_VEHICLE_INFO_MAIN = "INSERT INTO claim_vehicle_info_main VALUES\n" +
            "(?,?,?,?,?,?,?,?,?,?,\n" +
            "?,?,?,?,?,?,?,?,?,?,\n" +
            "?,?,?,?,?,?,?,?,?,?,\n" +
            "?,?,?,?,?,?,?,?,?,?,\n" +
            "?,?,?,?,?,?,?,?,?,?,\n" +
            "?,?,?,?,?,?,?,?,?,?,\n" +
            "?,?,?,?,?,?,?,?,?,?,\n" +
            "?,?,?,?,?,?,?,?,?,?,?,?)";
    String UPDATE_CLAIM_VEHICLE_INFO_MAIN_LATEST_INTIM_DATE = "UPDATE claim_vehicle_info_main SET D_LATEST_CLM_INTIM_DATE=? WHERE N_POL_REF_NO=?";

    String SELECT_CLAIM_VEHICLE_INFO_MAIN = "SELECT\n" +
            "t1.*,\n" +
            "t2.V_STATUS_DESC\n" +
            "FROM\n" +
            "claim_vehicle_info_main AS t1\n" +
            "INNER JOIN claim_policy_status AS t2 ON t1.V_POL_STATUS = t2.V_STATUS_CODE\n" +
            "WHERE\n" +
            "t1.N_POL_REF_NO = ?";
    String SELECT_CLAIM_BODY_TYPE = "SELECT n_veh_cls_id FROM `claim_body_type` WHERE `V_BODY_TYPE` = ? ";
    String SELECT_COVER_DETAILS = "SELECT * FROM claim_cover_info_main WHERE v_pol_number=? AND N_REN_COUNT=? AND N_END_COUNT=?";
    String SELECT_EXCESS_DETAILS = "SELECT * FROM claim_excess_info_main WHERE v_pol_number=? AND N_REN_COUNT=? AND N_END_COUNT=?";
    String SELECT_CLAIM_PAID_DETAILS_MAIN = "SELECT * FROM claim_paid_details_main WHERE v_pol_number=? AND N_REN_COUNT=? AND N_END_COUNT=?";
    String SELECT_CLAIM_POLICY_MEMO_DETAILS_MAIN = "SELECT * FROM claim_policy_memo_details_main WHERE V_POL_NUMBER=? AND N_REN_COUNT=? AND N_END_COUNT=?";
    String SELECT_CLAIM_SELLING_AGENT_DETAILS_MAIN = "SELECT * FROM claim_selling_agent_details_main WHERE V_POL_NUMBER=? ORDER BY N_REN_COUNT DESC LIMIT 1";
    String SELECT_CLAIM_ENDOR_HIS_MAIN = "SELECT * FROM claim_endor_his_main WHERE V_POL_NUMBER=? AND N_REN_COUNT=? AND N_END_COUNT=?";
    String SELECT_CLAIM_BILLING_INFO_MAIN = "SELECT * FROM claim_billing_info_main WHERE V_POL_NUMBER=? AND N_REN_COUNT=? AND N_END_COUNT=?";
    String SELECT_CLAIM_LEARN_DRIVE_DETAILS_MAIN = "SELECT * FROM claim_learn_drive_details_main WHERE V_POL_NUMBER=? AND N_REN_COUNT=? AND N_END_COUNT=?";
    String SELECT_CLAIM_POLICY_PREMIUM_MAIN = "SELECT * FROM claim_policy_premium_main WHERE V_POLICY_NO=? AND N_REN_COUNT=? AND N_END_COUNT=? ORDER BY V_SEC_CODE DESC";
    String SELECT_CLAIM_POLICY_CHARGES_BREKUP_MAIN = "SELECT * FROM claim_policy_charges_brekup_main WHERE V_POLICY_NO=? AND N_REN_COUNT=? AND N_END_COUNT=?";

    String SELECT_CLAIM_POLICY_SEQUENCE = "SELECT * FROM claim_policy_sequence WHERE ref_no=?";
    String UPDATE_CLAIM_POLICY_SEQUENCE = "UPDATE claim_policy_sequence SET policy_no=? WHERE ref_no=?";

    String SELECT_POLICY_BY_VEHICLE_NO = "SELECT t1.* FROM claim_vehicle_info_main AS t1 WHERE t1.V_VEHICLE_NUMBER = ?";

    String SELECT_POLICY_BY_VALID_ACCIDENT_DATE = "SELECT t1.N_POL_REF_NO FROM claim_vehicle_info_main AS t1 WHERE t1.N_POL_REF_NO = ? AND t1.D_INSPEC_DATE <= ? AND ? <= t1.D_EXPIRE_DATE ";

    String SELECT_NCB_HISTORY_DETAILS = "SELECT V_POL_NUMBER, V_VEHICLE_NUMBER, N_REN_COUNT, N_END_COUNT, N_NCB_RATE, V_POL_STATUS, D_INSPEC_DATE,D_EXPIRE_DATE FROM claim_vehicle_info_main WHERE V_POL_NUMBER = ? ORDER BY N_REN_COUNT DESC, N_END_COUNT ASC";

    String SELECT_LATEST_INFO_FOR_WARNING_MESSAGE = "SELECT V_POL_STATUS, N_TOT_PREM_OUTSTAND FROM claim_vehicle_info_main WHERE V_POL_NUMBER = ? ORDER BY N_REN_COUNT DESC, N_END_COUNT DESC LIMIT 1";

    String SELECT_OLDEST_INTIMATION_DATE = "SELECT D_INSPEC_DATE FROM claim_vehicle_info_main  WHERE `V_POL_NUMBER` = ? ORDER BY N_END_COUNT, N_REN_COUNT LIMIT 1";

    String GET_CHANNEL_NUMBER = "SELECT V_CHANNEL_CODE FROM claim_channel WHERE V_DESCRIPTION = ? ";
    String GET_ALL_REMINDERS = "SELECT t1.* FROM claim_reminder_letter_print_summary AS t1 WHERE N_CLAIM_NO = ? ";

    PolicyDto insertMaster(Connection connection, PolicyDto policyDto) throws Exception;

    Boolean updateLatestIntimationDate(Connection connection, PolicyDto policyDto) throws Exception;

    PolicyDto searchMaster(Connection connection, Integer policyRefNo) throws Exception;

    DataGridDto getPolicyDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String accidentDate, String vehicleNumber);

    PolicyStatusEnum getValidatePolicyStatus(PolicyDto policyDto);

    List<CoverDto> getCoverDtoList(Connection connection, PolicyDto policyDto);

    List<ExcessDto> getExcessDtoList(Connection connection, PolicyDto policyDto);

    List<PremiumBreakupDto> getPremiumBreakupDtoList(Connection connection, PolicyDto policyDto);

    List<PaidDetailsDto> getPaidDetailsDtoList(Connection connection, PolicyDto policyDto);

    List<PolicyMemoDto> getPolicyMemoDtoList(Connection connection, PolicyDto policyDto);

    List<SellingAgentDetailsDto> getSellingAgentDetailsDtoList(Connection connection, PolicyDto policyDto);

    List<EndorsementHistoryDto> getEndorsementHistoryDtoList(Connection connection, PolicyDto policyDto);

    List<BillingInfoDto> getBillingInfoDtoList(Connection connection, PolicyDto policyDto);

    List<LearnerDriverDetailsDto> getLearnerDriverDetailsDtoList(Connection connection, PolicyDto policyDto);

    void setVehicleBodyType(Connection connection, PolicyDto policyDto);

    PolicyDto searchPolicyByVehicleNo(Connection connection, String vehicleNo) throws Exception;

    PolicyDto searchPolicyByValidPolicyDate(Connection connection, Integer policeRefNo, String accidentDate) throws Exception;

    PremiumBreakupFormDto getPremiumBreakupFormDto(Connection connection, PolicyDto policyDto);

    List<NcbHistoryDetailsDto> searchNcbHistoryDetails(Connection connection, String policyNumber);

    PolicyWarningMessageDto checkPolicyValidity(Connection connection, String policyNo) throws Exception;

    String getOldestPolicyInspecDate(Connection connection, String policyNumber) throws Exception;

    String getChannelCode(Connection connection, String channelNo) throws Exception;

    List<ClaimRemindersDto> getReminders(Connection connection, Integer claimNo) ;
}
