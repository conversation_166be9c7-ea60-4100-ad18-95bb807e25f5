package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.PolicyDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.enums.PolicyStatusEnum;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PolicyDaoImpl extends AbstractBaseDao<PolicyDaoImpl> implements PolicyDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(PolicyDaoImpl.class);


    @Override
    public PolicyDto insertMaster(Connection connection, PolicyDto policyDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {

            policyDto.setPolicyRefNo(this.getSequenceClaimPolicyId(connection));
            String coverNoteNo = AppConstant.PREFIX_POLICY + policyDto.getPolicyRefNo();
            policyDto.setPolicyNumber(coverNoteNo);
            policyDto.setVehicleNoLastDigit(null == policyDto.getVehicleNumber() ? AppConstant.STRING_EMPTY : getLastDigit(policyDto.getVehicleNumber()));
            policyDto.setPolicyNumberLastDigit(null == policyDto.getPolicyNumber() ? AppConstant.STRING_EMPTY : getLastDigit(policyDto.getPolicyNumber()));

            ps = connection.prepareStatement(INSERT_CLAIM_VEHICLE_INFO_MAIN);
            ps.setInt(++index, policyDto.getPolicyRefNo());
            ps.setString(++index, policyDto.getPolicyBranch());
            ps.setString(++index, policyDto.getPolicyType());
            ps.setString(++index, policyDto.getPolicyNumber());
            ps.setInt(++index, policyDto.getRenCount());
            ps.setInt(++index, policyDto.getEndCount());
            ps.setString(++index, policyDto.getVehicleNumber());
            ps.setString(++index, policyDto.getExpireDate());
            ps.setString(++index, policyDto.getInspecDate());
            ps.setString(++index, policyDto.getOrgInspecDate());
            ps.setString(++index, policyDto.getPolStatus());
            ps.setString(++index, policyDto.getCustName());
            ps.setString(++index, policyDto.getCustNic());
            ps.setString(++index, policyDto.getCustAddressLine1());
            ps.setString(++index, policyDto.getCustAddressLine2());
            ps.setString(++index, policyDto.getCustAddressLine3());
            ps.setString(++index, policyDto.getCustMobileNo());
            ps.setString(++index, policyDto.getCustLandNo());
            ps.setBigDecimal(++index, policyDto.getAnnualPremium());
            ps.setBigDecimal(++index, policyDto.getSumInsured());
            ps.setString(++index, policyDto.getLatestClmIntimDate());
            ps.setString(++index, policyDto.getLatestClmLossDate());
            ps.setString(++index, policyDto.getAgentBroker());
            ps.setBigDecimal(++index, policyDto.getTotPremOutstand());
            ps.setInt(++index, policyDto.getNoDayPremOutstand());
            ps.setString(++index, policyDto.getEngineNo());
            ps.setString(++index, policyDto.getChassisNo());
            ps.setString(++index, policyDto.getVehicleMake());
            ps.setString(++index, policyDto.getVehicleModel());
            ps.setInt(++index, policyDto.getManufactYear());
            ps.setBigDecimal(++index, policyDto.getExcess());
            ps.setInt(++index, policyDto.getNcbRate());
            ps.setBigDecimal(++index, policyDto.getNcbAmount());
            ps.setString(++index, coverNoteNo);
            ps.setString(++index, policyDto.getCoverType());
            ps.setString(++index, policyDto.getChannel());
            ps.setString(++index, policyDto.getUpdateFlag());
            ps.setString(++index, policyDto.getInsertFlag());
            ps.setString(++index, policyDto.getCreateUser());
            ps.setString(++index, policyDto.getCreateDate());
            ps.setString(++index, policyDto.getCreateTime());
            ps.setString(++index, policyDto.getCancelReason());
            ps.setString(++index, policyDto.getLapsedDate());
            ps.setString(++index, policyDto.getRegistDate());
            ps.setString(++index, policyDto.getPolCancelDate());
            ps.setString(++index, policyDto.getLocation());
            ps.setString(++index, policyDto.getRisk());
            ps.setString(++index, policyDto.getBodyType());
            ps.setString(++index, policyDto.getClientId());
            ps.setString(++index, policyDto.getIsThirdParty());
            ps.setString(++index, policyDto.getFinanceCompany());
            ps.setString(++index, policyDto.getVehicleUsage());
            ps.setString(++index, policyDto.getAgentCode());
            ps.setString(++index, policyDto.getFuelType());
            ps.setInt(++index, policyDto.getNoOfSeat());
            ps.setInt(++index, policyDto.getVehicleAge());
            ps.setString(++index, policyDto.getPolSuspend());
            ps.setString(++index, policyDto.getEngCapacity());
            ps.setString(++index, policyDto.getBranchCode());
            ps.setString(++index, policyDto.getProduct());
            ps.setString(++index, policyDto.getLastModifyUser());
            ps.setString(++index, policyDto.getLastModifyDateTime());
            ps.setString(++index, policyDto.getCurrentPolStatus());
            ps.setString(++index, policyDto.getVehicleColor());
            ps.setString(++index, policyDto.getTradePlateNo());
            ps.setString(++index, policyDto.getLastModifyDate());
            ps.setString(++index, policyDto.getIndComFlag());
            ps.setString(++index, policyDto.getCompanyBranch());
            ps.setString(++index, policyDto.getCompanyCode());
            ps.setString(++index, policyDto.getCmsUpdateDateTime());
            ps.setString(++index, policyDto.getFinCompanyCode());
            ps.setString(++index, policyDto.getFinCompanyBranch());
            ps.setString(++index, policyDto.getLoanAccNo());
            ps.setString(++index, policyDto.getIdenCode());
            ps.setString(++index, policyDto.getVehicleNoLastDigit());
            ps.setString(++index, policyDto.getPolicyNumberLastDigit());
            ps.setString(++index, policyDto.getPolicyChannelType());
            ps.setString(++index, policyDto.getIntroducer());
            ps.setString(++index, policyDto.getBizType());
            ps.setString(++index, policyDto.getBankRefNo());
            ps.setString(++index, policyDto.getWorkflow());
            ps.setString(++index, policyDto.getCategoryDescription());
            if (ps.executeUpdate() > 0) {
                this.updateSequenceClaimPolicyId(connection, policyDto.getPolicyRefNo());
                return policyDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    protected String getLastDigit(String value) {
        String _value = AppConstant.EMPTY_STRING;
        try {
            Pattern p = Pattern.compile("\\d+");
            Matcher m = p.matcher(value);
            while (m.find()) {
                _value = m.group();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return _value;
    }

    @Override
    public Boolean updateLatestIntimationDate(Connection connection, PolicyDto policyDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_VEHICLE_INFO_MAIN_LATEST_INTIM_DATE);
            ps.setString(1, Utility.sysDate(AppConstant.DATE_FORMAT));
            ps.setInt(2, policyDto.getPolicyRefNo());
            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error");
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return false;
    }

    @Override
    public PolicyDto searchMaster(Connection connection, Integer policyRefNo) throws Exception {
        PolicyDto policyDto = new PolicyDto();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_VEHICLE_INFO_MAIN);
            ps.setInt(1, policyRefNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                policyDto = this.getPolicyDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return policyDto;
    }

    @Override
    public DataGridDto getPolicyDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String accidentDate, String vehicleNumber) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List policyList = new ArrayList(200);
        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String SQL_ORDER = AppConstant.STRING_EMPTY;

        if (!AppConstant.STRING_EMPTY.equals(vehicleNumber)) {
            SQL_ORDER = SQL_ORDER.concat("ORDER BY V_POL_NUMBER_LAST_DIGIT DESC, N_REN_COUNT DESC, N_END_COUNT DESC");
        } else {
            SQL_ORDER = SQL_ORDER.concat(formatOrderSQL(start, length, orderType, orderField).toString());
        }

        if (!AppConstant.STRING_EMPTY.equals(accidentDate)) {
            SQL_SEARCH = SQL_SEARCH.concat(String.format("AND '%s' BETWEEN D_INSPEC_DATE AND D_EXPIRE_DATE ", accidentDate));
        }

        final String SEL_SQL = "SELECT N_POL_REF_NO,V_POL_NUMBER,V_VEHICLE_NUMBER,V_CHASSIS_NO,\n" +
                "D_EXPIRE_DATE,N_REN_COUNT,N_END_COUNT,D_INSPEC_DATE,D_ORG_INSPEC_DATE,V_POL_STATUS,V_CUST_NAME,\n" +
                "N_SUM_INSURED,D_LATEST_CLM_INTIM_DATE,D_LATEST_CLM_LOSS_DATE,V_COVER_NOTE_NO,V_COVER_TYPE,D_EXPIRE_DATE,V_CURRENT_POL_STATUS,V_POL_NUMBER as POL_NUMBER_DESC,V_POLICY_CHANNEL_TYPE "
                + "FROM "
                + "claim_vehicle_info_main ".concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = "SELECT count(N_POL_REF_NO) AS cnt  FROM  claim_vehicle_info_main ".concat(SQL_SEARCH);
        try {
            ps = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    PolicyDto policy = getPolicyForView(rs);
                    policy.setIndex(++index);
                    policyList.add(policy);
                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(policyList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    public PolicyStatusEnum getValidatePolicyStatus(PolicyDto policyDto) {
        try {
            if (PolicyStatusEnum.POLICY_EXPIRED.getPolicyStatus().equalsIgnoreCase(policyDto.getCurrentPolStatus())) {
                return PolicyStatusEnum.POLICY_EXPIRED;
            } else if (PolicyStatusEnum.POLICY_LAPSED.getPolicyStatus().equalsIgnoreCase(policyDto.getCurrentPolStatus())) {
                return PolicyStatusEnum.POLICY_LAPSED;
            } else if (PolicyStatusEnum.POLICY_SUSPENDED.getPolicyStatus().equalsIgnoreCase(policyDto.getCurrentPolStatus())) {
                return PolicyStatusEnum.POLICY_SUSPENDED;
            } else if (PolicyStatusEnum.POLICY_CANCELLED.getPolicyStatus().equalsIgnoreCase(policyDto.getPolStatus())) {
                return PolicyStatusEnum.POLICY_CANCELLED;
            } else if (PolicyStatusEnum.PROPOSAL.getPolicyStatus().equalsIgnoreCase(policyDto.getPolStatus())) {
                return PolicyStatusEnum.PROPOSAL;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return PolicyStatusEnum.INFORCE;
    }

    @Override
    public List<CoverDto> getCoverDtoList(Connection connection, PolicyDto policyDto) {
        List<CoverDto> coverDtoList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_COVER_DETAILS);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                CoverDto coverDto = new CoverDto();
                coverDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                coverDto.setCoverDesc(rs.getString("V_COVER_DESC"));
                coverDto.setCoverAmount(rs.getBigDecimal("N_COVER_AMOUNT"));
                coverDto.setCoverRate(rs.getBigDecimal("N_COVER_RATE"));
                coverDtoList.add(coverDto);
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return coverDtoList;
    }

    @Override
    public List<ExcessDto> getExcessDtoList(Connection connection, PolicyDto policyDto) {
        List<ExcessDto> excessDtoList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_EXCESS_DETAILS);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                ExcessDto excessDto = new ExcessDto();
                excessDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                excessDto.setExcessDesc(rs.getString("V_EXCESS_DESC"));
                excessDto.setExcessAmount(rs.getBigDecimal("N_EXCESS_AMOUNT"));
                excessDto.setExcessRate(rs.getBigDecimal("N_EXCESS_RATE"));
                excessDtoList.add(excessDto);
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return excessDtoList;
    }

    @Override
    public List<PremiumBreakupDto> getPremiumBreakupDtoList(Connection connection, PolicyDto policyDto) {
        List<PremiumBreakupDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_COVER_DETAILS);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                PremiumBreakupDto premiumBreakupDto = new PremiumBreakupDto();
                premiumBreakupDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                premiumBreakupDto.setRenCount(rs.getInt("N_REN_COUNT"));
                premiumBreakupDto.setEndCount(rs.getInt("N_END_COUNT"));
                premiumBreakupDto.setActualBasic(rs.getBigDecimal("N_ACTUAL_BASIC"));
                premiumBreakupDto.setAddBenefit(rs.getBigDecimal("N_ADD_BENEFIT"));
                premiumBreakupDto.setLoadAmt(rs.getBigDecimal("N_LOAD_AMT"));
                premiumBreakupDto.setDiscAmt(rs.getBigDecimal("N_DISC_AMT"));
                premiumBreakupDto.setNcdAmt(rs.getBigDecimal("N_NCD_AMT"));
                premiumBreakupDto.setGrossPrem(rs.getBigDecimal("N_GROSS_PREM"));
                premiumBreakupDto.setCess(rs.getBigDecimal("N_CESS"));
                premiumBreakupDto.setNbt(rs.getBigDecimal("N_NBT"));
                premiumBreakupDto.setPof(rs.getBigDecimal("N_POF"));
                premiumBreakupDto.setRt(rs.getBigDecimal("N_RT"));
                premiumBreakupDto.setSd(rs.getBigDecimal("N_SD"));
                premiumBreakupDto.setVat(rs.getBigDecimal("N_VAT"));
                premiumBreakupDto.setOtherChrg(rs.getBigDecimal("N_OTHER_CHRG"));
                list.add(premiumBreakupDto);
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<PaidDetailsDto> getPaidDetailsDtoList(Connection connection, PolicyDto policyDto) {
        BigDecimal tot = BigDecimal.ZERO;
        List<PaidDetailsDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_PAID_DETAILS_MAIN);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                PaidDetailsDto paidDetailsDto = new PaidDetailsDto();
                paidDetailsDto.setTxnRefNo(rs.getInt("n_ref_no"));
                paidDetailsDto.setPolicyNo(rs.getString("v_pol_number"));
                paidDetailsDto.setReceiptNumber(rs.getString("v_receipt_no"));
                paidDetailsDto.setPaidDate(rs.getString("d_paid_date"));
                paidDetailsDto.setPaidAmount(rs.getBigDecimal("n_paid_amount"));
                paidDetailsDto.setPaymentMode(rs.getString("v_payment_mode"));
                list.add(paidDetailsDto);
                tot = tot.add(rs.getBigDecimal("n_paid_amount"));
            }
            policyDto.setPaidTotalAmount(tot);
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<PolicyMemoDto> getPolicyMemoDtoList(Connection connection, PolicyDto policyDto) {

        List<PolicyMemoDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_POLICY_MEMO_DETAILS_MAIN);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                PolicyMemoDto policyMemoDto = new PolicyMemoDto();
                policyMemoDto.setTxnRefNo(rs.getInt("N_REF_NO"));
                policyMemoDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                policyMemoDto.setMemo(rs.getString("V_MEMO"));
                policyMemoDto.setPosition(rs.getString("V_POSITION_FLAG"));
                policyMemoDto.setMemoDate(rs.getString("D_DATE"));
                policyMemoDto.setExclusion(rs.getString("V_EXCLUSION"));
                policyMemoDto.setOrder(rs.getInt("N_ORDER"));
                list.add(policyMemoDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<SellingAgentDetailsDto> getSellingAgentDetailsDtoList(Connection connection, PolicyDto policyDto) {
        List<SellingAgentDetailsDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_SELLING_AGENT_DETAILS_MAIN);
            ps.setString(1, policyDto.getPolicyNumber());
            // ps.setInt(2, policyDto.getRenCount());
            //  ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            if (rs.next()) {
                SellingAgentDetailsDto sellingAgentDetailsDto = new SellingAgentDetailsDto();
                sellingAgentDetailsDto.setTxnRefNo(rs.getInt("N_REF_NO"));
                sellingAgentDetailsDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                sellingAgentDetailsDto.setAgentCode(rs.getString("V_AGENT_CODE"));
                sellingAgentDetailsDto.setAgentName(rs.getString("V_AGENT_NAME"));
                sellingAgentDetailsDto.setRank(rs.getString("V_RANK"));
                sellingAgentDetailsDto.setRankDesc(rs.getString("V_RANK_DESCRIPTION"));
                sellingAgentDetailsDto.setPercent(rs.getInt("N_PERCENT"));
                sellingAgentDetailsDto.setAgentStatus(rs.getString("V_STATUS"));
                sellingAgentDetailsDto.setStartDate(rs.getString("D_START_DATE"));
                sellingAgentDetailsDto.setEndDate(rs.getString("D_END_DATE"));
                list.add(sellingAgentDetailsDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<EndorsementHistoryDto> getEndorsementHistoryDtoList(Connection connection, PolicyDto policyDto) {
        List<EndorsementHistoryDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_ENDOR_HIS_MAIN);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                EndorsementHistoryDto endorsementHistoryDto = new EndorsementHistoryDto();
                endorsementHistoryDto.setTxnRefNo(rs.getInt("N_REF_NO"));
                endorsementHistoryDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                endorsementHistoryDto.setDescription(rs.getString("V_DESCRIPTION"));
                list.add(endorsementHistoryDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<BillingInfoDto> getBillingInfoDtoList(Connection connection, PolicyDto policyDto) {
        List<BillingInfoDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_BILLING_INFO_MAIN);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                BillingInfoDto billingInfoDto = new BillingInfoDto();
                billingInfoDto.setTxnRefNo(rs.getInt("N_REF_NO"));
                billingInfoDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                billingInfoDto.setRenewCount(rs.getInt("N_REN_COUNT"));
                billingInfoDto.setEndCount(rs.getInt("N_END_COUNT"));
                billingInfoDto.setBillNo(rs.getString("V_BILL_NO"));
                billingInfoDto.setBillAmount(rs.getBigDecimal("N_BILL_AMOUNT"));
                billingInfoDto.setBillStatus(rs.getString("V_BILL_STATUS"));
                billingInfoDto.setBillDate(rs.getString("D_BILL_DATE"));
                list.add(billingInfoDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<LearnerDriverDetailsDto> getLearnerDriverDetailsDtoList(Connection connection, PolicyDto policyDto) {
        List<LearnerDriverDetailsDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_LEARN_DRIVE_DETAILS_MAIN);
            ps.setString(1, policyDto.getPolicyNumber());
            ps.setInt(2, policyDto.getRenCount());
            ps.setInt(3, policyDto.getEndCount());
            rs = ps.executeQuery();
            while (rs.next()) {
                LearnerDriverDetailsDto learnerDriverDetailsDto = new LearnerDriverDetailsDto();
                learnerDriverDetailsDto.setTxnRefNo(rs.getInt("N_REF_NO"));
                learnerDriverDetailsDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                learnerDriverDetailsDto.setNicNo(rs.getString("V_NIC_NO"));
                learnerDriverDetailsDto.setIdType(rs.getString("V_ID_TYPE"));
                learnerDriverDetailsDto.setName(rs.getString("V_NAME"));
                learnerDriverDetailsDto.setAge(rs.getInt("N_AGE"));
                learnerDriverDetailsDto.setPremium(rs.getBigDecimal("N_PREMIUM"));
                list.add(learnerDriverDetailsDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public void setVehicleBodyType(Connection connection, PolicyDto policyDto) {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_BODY_TYPE);
            ps.setString(1, policyDto.getBodyType().trim());
            rs = ps.executeQuery();
            if (rs.next()) {
                policyDto.setVehicleClassId(rs.getInt("n_veh_cls_id"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public PolicyDto searchPolicyByVehicleNo(Connection connection, String vehicleNo) throws Exception {
        PolicyDto policyDto = new PolicyDto();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_POLICY_BY_VEHICLE_NO);
            ps.setString(1, vehicleNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                policyDto = this.getPolicyDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return policyDto;
    }

    @Override
    public PolicyDto searchPolicyByValidPolicyDate(Connection connection, Integer policeRefNo, String accidentDate) throws Exception {
        PolicyDto policyDto = new PolicyDto();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_POLICY_BY_VALID_ACCIDENT_DATE);
            ps.setInt(1, policeRefNo);
            ps.setString(2, accidentDate);
            ps.setString(3, accidentDate);
            rs = ps.executeQuery();
            if (rs.next()) {
                policyDto.setPolicyRefNo(rs.getInt("t1.N_POL_REF_NO"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return policyDto;
    }

    @Override
    public PremiumBreakupFormDto getPremiumBreakupFormDto(Connection connection, PolicyDto policyDto) {
        PremiumBreakupFormDto premiumBreakupFormDto = new PremiumBreakupFormDto();
        ChargesBreakupDto chargesBreakupDto = new ChargesBreakupDto();
        List<PolicyPremiumDto> list = new ArrayList<>();
        List<PolicyPremiumDto> policyPremiumRiotStrikeList = new ArrayList<>();
        PreparedStatement ps1;
        ResultSet rs;
        try {
            ps1 = connection.prepareStatement(SELECT_CLAIM_POLICY_PREMIUM_MAIN);
            ps1.setString(1, policyDto.getPolicyNumber());
            ps1.setInt(2, policyDto.getRenCount());
            ps1.setInt(3, policyDto.getEndCount());
            rs = ps1.executeQuery();
            while (rs.next()) {
                PolicyPremiumDto policyPremiumDto = new PolicyPremiumDto();
                policyPremiumDto.setRefNo(rs.getInt("N_TXN_NO"));
                policyPremiumDto.setPolicyNo(rs.getString("V_POLICY_NO"));
                policyPremiumDto.setRenCount(rs.getInt("N_REN_COUNT"));
                policyPremiumDto.setEndCount(rs.getInt("N_END_COUNT"));
                policyPremiumDto.setDescription(rs.getString("V_DESC"));
                policyPremiumDto.setSecCode(rs.getString("V_SEC_CODE") == null ? AppConstant.STRING_EMPTY : rs.getString("V_SEC_CODE"));
                policyPremiumDto.setPremiumAmount(rs.getBigDecimal("N_MAN_PREM"));
                switch (policyPremiumDto.getSecCode().trim()) {
                    case "TC":
                    case "SRCC":
                        policyPremiumRiotStrikeList.add(policyPremiumDto);
                        break;
                    default:
                        list.add(policyPremiumDto);
                }


            }
            rs.close();
            ps1.close();

            ps1 = connection.prepareStatement(SELECT_CLAIM_POLICY_CHARGES_BREKUP_MAIN);
            ps1.setString(1, policyDto.getPolicyNumber());
            ps1.setInt(2, policyDto.getRenCount());
            ps1.setInt(3, policyDto.getEndCount());
            rs = ps1.executeQuery();
            if (rs.next()) {
                chargesBreakupDto.setRefNo(rs.getInt("N_TXN_NO"));
                chargesBreakupDto.setPolicyNo(rs.getString("V_POLICY_NO"));
                chargesBreakupDto.setRenCount(rs.getInt("N_REN_COUNT"));
                chargesBreakupDto.setEndCount(rs.getInt("N_END_COUNT"));
                chargesBreakupDto.setCess(rs.getBigDecimal("N_CESS"));
                chargesBreakupDto.setNbt(rs.getBigDecimal("N_NBT"));
                chargesBreakupDto.setPof(rs.getBigDecimal("N_POF"));
                chargesBreakupDto.setRt(rs.getBigDecimal("N_RT"));
                chargesBreakupDto.setSd(rs.getBigDecimal("N_SD"));
                chargesBreakupDto.setVat(rs.getBigDecimal("N_VAT"));
            }
            rs.close();
            ps1.close();
            premiumBreakupFormDto.setPolicyPremiumDtoList(list);
            premiumBreakupFormDto.setPolicyPremiumRiotStrikeList(policyPremiumRiotStrikeList);
            premiumBreakupFormDto.setChargesBreakupDto(chargesBreakupDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return premiumBreakupFormDto;
    }

    @Override
    public List<NcbHistoryDetailsDto> searchNcbHistoryDetails(Connection connection, String policyNumber) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer index = 0;
        List<NcbHistoryDetailsDto> ncbHistoryDetails = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_NCB_HISTORY_DETAILS);
            ps.setString(1, policyNumber);
            rs = ps.executeQuery();
            while (rs.next()) {
                NcbHistoryDetailsDto ncb = new NcbHistoryDetailsDto();
                ncb.setPolicyNo(rs.getString("V_POL_NUMBER"));
                ncb.setVehicleNo(rs.getString("V_VEHICLE_NUMBER"));
                ncb.setRenewalCount(rs.getInt("N_REN_COUNT"));
                ncb.setEndorsementCount(rs.getInt("N_END_COUNT"));
                ncb.setNcbPer(rs.getInt("N_NCB_RATE"));
                ncb.setStatus(rs.getString("V_POL_STATUS"));
                ncb.setInspecDate(null == Utility.getDateValue(rs.getString("D_INSPEC_DATE")) ? AppConstant.STRING_EMPTY : Utility.getDateValue(rs.getString("D_INSPEC_DATE")));
                ncb.setExpiryDate(null == Utility.getDateValue(rs.getString("D_EXPIRE_DATE")) ? AppConstant.STRING_EMPTY : Utility.getDateValue(rs.getString("D_EXPIRE_DATE")));
                String inspecDate = Utility.getCustomDateFormat(ncb.getInspecDate(), AppConstant.DATE_ONLY_YEAR_FORMAT);
                String expDate = Utility.getCustomDateFormat(ncb.getExpiryDate(), AppConstant.DATE_ONLY_YEAR_FORMAT);
                ncb.setPolicyYear(inspecDate.concat(" - ").concat(expDate));
                if (index == 0) {
                    ncbHistoryDetails.add(ncb);
                    ++index;
                    continue;
                }
                if (ncbHistoryDetails.get(index - 1).getRenewalCount().equals(ncb.getRenewalCount())) {
                    if (ncbHistoryDetails.get(index - 1).getEndorsementCount() <= (ncb.getEndorsementCount())) {
                        ncbHistoryDetails.remove(index - 1);
                        ncbHistoryDetails.add(ncb);
                        continue;
                    }
                }
                ncbHistoryDetails.add(ncb);
                ++index;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return ncbHistoryDetails;
    }

    @Override
    public PolicyWarningMessageDto checkPolicyValidity(Connection connection, String policyNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_LATEST_INFO_FOR_WARNING_MESSAGE);
            ps.setString(1, policyNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                PolicyWarningMessageDto messageDto = new PolicyWarningMessageDto();
                messageDto.setPolicyNo(policyNo);
                messageDto.setOutstandingPremium(rs.getBigDecimal("N_TOT_PREM_OUTSTAND"));
                messageDto.setPolicyStatus(rs.getString("V_POL_STATUS"));
                return messageDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public String getOldestPolicyInspecDate(Connection connection, String policyNumber) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String inspecDate = null;
        try {
            ps = connection.prepareStatement(SELECT_OLDEST_INTIMATION_DATE);
            ps.setString(1, policyNumber);
            rs = ps.executeQuery();
            while (rs.next()) {
                inspecDate = rs.getString("D_INSPEC_DATE");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return inspecDate;
    }

    private PolicyDto getPolicyForView(ResultSet rs) {
        PolicyDto policy = new PolicyDto();
        try {
            policy.setPolicyRefNo(rs.getInt("N_POL_REF_NO"));
            policy.setPolicyNumber(rs.getString("V_POL_NUMBER"));
            policy.setVehicleNumber(rs.getString("V_VEHICLE_NUMBER"));
            policy.setChassisNo(null == rs.getString("V_CHASSIS_NO") ? AppConstant.STRING_EMPTY : rs.getString("V_CHASSIS_NO"));
            policy.setExpireDate(rs.getString(Utility.getDateValue("D_EXPIRE_DATE")));
            policy.setRenCount(rs.getInt("N_REN_COUNT"));
            policy.setEndCount(rs.getInt(Utility.getDateValue("N_END_COUNT")));
            policy.setInspecDate(rs.getString(Utility.getDateValue("D_INSPEC_DATE")));
            policy.setOrgInspecDate(rs.getString(Utility.getDateValue("D_ORG_INSPEC_DATE")));
            policy.setPolStatus(rs.getString("V_POL_STATUS"));
            policy.setCustName(null == rs.getString("V_CUST_NAME") ? AppConstant.STRING_EMPTY : rs.getString("V_CUST_NAME"));
            policy.setSumInsured(rs.getBigDecimal("N_SUM_INSURED"));
            policy.setLatestClmIntimDate(rs.getString(Utility.getDateValue("D_LATEST_CLM_INTIM_DATE")));
            policy.setLatestClmLossDate(rs.getString(Utility.getDateValue("D_LATEST_CLM_LOSS_DATE")));
            policy.setCoverNoteNo(null == rs.getString("V_COVER_NOTE_NO") ? AppConstant.EMPTY_STRING : rs.getString("V_COVER_NOTE_NO"));
            policy.setCoverType(rs.getString("V_COVER_TYPE"));
            policy.setExpireDate(rs.getString(Utility.getDateValue("D_EXPIRE_DATE")));
            policy.setCurrentPolStatus(rs.getString("V_CURRENT_POL_STATUS"));
            policy.setPolicyChannelType(null == rs.getString("V_POLICY_CHANNEL_TYPE") || rs.getString("V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("V_POLICY_CHANNEL_TYPE"));
            policy.setValidePolicyStatus(this.getValidatePolicyStatus(policy));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return policy;
    }

    private PolicyDto getPolicyDto(ResultSet rs) {
        PolicyDto policyDto = new PolicyDto();
        try {
            policyDto.setPolicyRefNo(rs.getInt("t1.N_POL_REF_NO"));
            policyDto.setPolicyBranch(rs.getString("t1.V_POL_BRANCH"));
            policyDto.setPolicyType(rs.getString("t1.V_POL_TYPE"));
            policyDto.setPolicyNumber(rs.getString("t1.V_POL_NUMBER"));
            policyDto.setRenCount(rs.getInt("t1.N_REN_COUNT"));
            policyDto.setEndCount(rs.getInt("t1.N_END_COUNT"));
            policyDto.setVehicleNumber(rs.getString("t1.V_VEHICLE_NUMBER"));
            policyDto.setExpireDate(rs.getString(Utility.getDateValue("t1.D_EXPIRE_DATE")));
            policyDto.setInspecDate(rs.getString(Utility.getDateValue("t1.D_INSPEC_DATE")));
            policyDto.setOrgInspecDate(rs.getString(Utility.getDateValue("t1.D_ORG_INSPEC_DATE")));
            policyDto.setPolStatus(rs.getString("t1.V_POL_STATUS"));
            policyDto.setCustName(StringEscapeUtils.escapeXml(rs.getString("t1.V_CUST_NAME")));
            policyDto.setCustNic(rs.getString("t1.V_CUST_NIC"));
            policyDto.setCustAddressLine1(rs.getString("t1.V_CUST_ADDRESS_LINE1") != null ? StringEscapeUtils.escapeXml(rs.getString("t1.V_CUST_ADDRESS_LINE1").trim()) : AppConstant.EMPTY_STRING);
            policyDto.setCustAddressLine2(rs.getString("t1.V_CUST_ADDRESS_LINE2") != null ? StringEscapeUtils.escapeXml(rs.getString("t1.V_CUST_ADDRESS_LINE2").trim()) : AppConstant.EMPTY_STRING);
            policyDto.setCustAddressLine3(rs.getString("t1.V_CUST_ADDRESS_LINE3") != null ? StringEscapeUtils.escapeXml(rs.getString("t1.V_CUST_ADDRESS_LINE3").trim()) : AppConstant.EMPTY_STRING);
            policyDto.setCustMobileNo(rs.getString("t1.V_CUST_MOBILE_NO") == null ? AppConstant.EMPTY_STRING : rs.getString("t1.V_CUST_MOBILE_NO"));
            policyDto.setCustLandNo(rs.getString("t1.V_CUST_LAND_NO"));
            policyDto.setAnnualPremium(rs.getBigDecimal("t1.N_ANNUAL_PREMIUM"));
            policyDto.setSumInsured(rs.getBigDecimal("t1.N_SUM_INSURED"));
            policyDto.setLatestClmIntimDate(rs.getString(Utility.getDateValue("t1.D_LATEST_CLM_INTIM_DATE")));
            policyDto.setLatestClmLossDate(rs.getString(Utility.getDateValue("t1.D_LATEST_CLM_LOSS_DATE")));
            policyDto.setAgentBroker(rs.getString("t1.V_AGENT_BROKER"));
            policyDto.setTotPremOutstand(rs.getBigDecimal("t1.N_TOT_PREM_OUTSTAND"));
            policyDto.setNoDayPremOutstand(rs.getInt("t1.N_NO_DAY_PREM_OUTSTAND"));
            policyDto.setEngineNo(rs.getString("t1.V_ENGINE_NO"));
            policyDto.setChassisNo(rs.getString("t1.V_CHASSIS_NO"));
            policyDto.setVehicleMake(rs.getString("t1.V_VEHICLE_MAKE"));
            policyDto.setVehicleModel(rs.getString("t1.V_VEHICLE_MODEL"));
            policyDto.setManufactYear(rs.getInt("t1.N_MANUFACT_YEAR"));
            policyDto.setExcess(rs.getBigDecimal("t1.N_EXCESS"));
            policyDto.setNcbRate(rs.getInt("t1.N_NCB_RATE"));
            policyDto.setNcbAmount(rs.getBigDecimal("t1.N_NCB_AMOUNT"));
            policyDto.setCoverNoteNo(rs.getString("t1.V_COVER_NOTE_NO"));
            policyDto.setCoverType(rs.getString("t1.V_COVER_TYPE"));
            policyDto.setChannel(rs.getString("t1.V_CHANNEL"));
            policyDto.setUpdateFlag(rs.getString("t1.V_UPDATE_FLAG"));
            policyDto.setInsertFlag(rs.getString("t1.V_INSERT_FLAG"));
            policyDto.setCreateUser(rs.getString("t1.V_CREATE_USER"));
            policyDto.setCreateDate(rs.getString(Utility.getDateValue("t1.D_CREATE_DATE")));
            policyDto.setCreateTime(rs.getString(Utility.getDateValue("t1.T_CREATE_TIME")));
            policyDto.setCancelReason(rs.getString("t1.V_CANCEL_REASON"));
            policyDto.setLapsedDate(rs.getString(Utility.getDateValue("t1.D_LAPSED_DATE")));
            policyDto.setRegistDate(rs.getString(Utility.getDateValue("t1.D_REGIST_DATE")));
            policyDto.setPolCancelDate(rs.getString(Utility.getDateValue("t1.D_POL_CANCEL_DATE")));
            policyDto.setLocation(rs.getString("t1.V_LOCATION"));
            policyDto.setRisk(rs.getString("t1.V_RISK"));
            policyDto.setBodyType(rs.getString("t1.V_BODY_TYPE"));
            policyDto.setClientId(rs.getString("t1.V_CLIENT_ID"));
            policyDto.setIsThirdParty(rs.getString("t1.V_IS_THIRD_PARTY"));
            policyDto.setFinanceCompany(rs.getString("t1.V_FINANCE_COMPANY"));
            policyDto.setVehicleUsage(rs.getString("t1.V_VEHICLE_USAGE"));
            policyDto.setAgentCode(rs.getString("t1.V_AGENT_CODE"));
            policyDto.setFuelType(rs.getString("t1.V_FUEL_TYPE"));
            policyDto.setNoOfSeat(rs.getInt("t1.N_NO_OF_SEAT"));
            policyDto.setVehicleAge(rs.getInt("t1.N_VEHICLE_AGE"));
            policyDto.setPolSuspend(rs.getString("t1.V_POL_SUSPEND"));
            policyDto.setEngCapacity(rs.getString("t1.V_ENG_CAPACITY"));
            policyDto.setBranchCode(rs.getString("t1.V_BRANCH_CODE"));
            policyDto.setProduct(rs.getString("t1.V_PRODUCT"));
            policyDto.setLastModifyUser(rs.getString("t1.V_LAST_MODIFY_USER"));
            policyDto.setLastModifyDateTime(Utility.getDateValue(rs.getString("t1.D_LAST_MODIFY_DATE_TIME")));
            policyDto.setLastModifyDate(Utility.getDateValue(rs.getString("t1.D_LAST_MODIFY_DATE")));
            policyDto.setCurrentPolStatus(rs.getString("t1.V_CURRENT_POL_STATUS"));
            policyDto.setVehicleColor(rs.getString("t1.V_VEHICLE_COLOR"));
            policyDto.setTradePlateNo(rs.getString("t1.V_TRADE_PLATE_NO"));
            policyDto.setIndComFlag(rs.getString("t1.V_IND_COM_FLAG"));
            policyDto.setCompanyBranch(rs.getString("t1.V_COMPANY_BRANCH"));
            policyDto.setCompanyCode(rs.getString("t1.V_COMPANY_CODE"));

            policyDto.setFinCompanyCode(null == rs.getString("t1.V_FIN_COMPANY_CODE") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_FIN_COMPANY_CODE"));
            policyDto.setFinCompanyBranch(null == rs.getString("t1.V_FIN_COMPANY_BRANCH") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_FIN_COMPANY_BRANCH"));
            policyDto.setLoanAccNo(null == rs.getString("t1.V_LOAN_ACC_NO") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_LOAN_ACC_NO"));
            policyDto.setIdenCode(null == rs.getString("t1.V_IDEN_CODE") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_IDEN_CODE"));
            policyDto.setVehicleNoLastDigit(null == rs.getString("t1.V_VEHICLE_NO_LAST_DIGIT") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_VEHICLE_NO_LAST_DIGIT"));
            policyDto.setPolicyNumberLastDigit(null == rs.getString("t1.V_POL_NUMBER_LAST_DIGIT") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_POL_NUMBER_LAST_DIGIT"));

            policyDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
            policyDto.setIntroducer(null == rs.getString("t1.V_INTRODUCER") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_INTRODUCER"));
            policyDto.setBankRefNo(null == rs.getString("t1.V_BANK_REF_NO") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_BANK_REF_NO"));
            policyDto.setWorkflow(null == rs.getString("t1.V_WORKFLOW") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_WORKFLOW"));
            policyDto.setCategoryDescription(null == rs.getString("t1.V_CATEGORY_DESC") ? AppConstant.STRING_EMPTY : rs.getString("t1.V_CATEGORY_DESC"));

            policyDto.setBizType(null == rs.getString("V_BIZ_TYPE") ? AppConstant.STRING_EMPTY : rs.getString("V_BIZ_TYPE"));
            BigDecimal ncb = policyDto.getNcbAmount() == null ? BigDecimal.ZERO : policyDto.getNcbAmount().multiply(new BigDecimal("-1"));
            policyDto.setNcbAmount(ncb);
            policyDto.setValidePolicyStatus(this.getValidatePolicyStatus(policyDto));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return policyDto;
    }

    private synchronized Integer getSequenceClaimPolicyId(Connection conn) {
        PreparedStatement ps = null;
        Integer sequenceId = 0;
        try {
            ps = conn.prepareStatement(SELECT_CLAIM_POLICY_SEQUENCE);
            ps.setInt(1, 1);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    sequenceId = rs.getInt("policy_no");
                    sequenceId++;
                }
                rs.close();
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return sequenceId;
    }

    private synchronized void updateSequenceClaimPolicyId(Connection connection, Integer sequenceId) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_POLICY_SEQUENCE);
            ps.setInt(1, sequenceId);
            ps.setInt(2, 1);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
    }

    @Override
    public String getChannelCode(Connection connection, String channelNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String inspecDate = null;
        try {
            ps = connection.prepareStatement(GET_CHANNEL_NUMBER);
            ps.setString(1, channelNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                inspecDate = rs.getString("V_CHANNEL_CODE");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return inspecDate;
    }

    @Override
    public List<ClaimRemindersDto> getReminders(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        List<ClaimRemindersDto> claimRemindersDtoList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_ALL_REMINDERS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimRemindersDto claimRemindersDto = new ClaimRemindersDto();
                claimRemindersDto.setdGeneratedDateTime(rs.getDate("D_GENERATE_DATE_TIME"));
                claimRemindersDtoList.add(claimRemindersDto);
                ++index;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return claimRemindersDtoList;
    }
}
