package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.UserDao;
import com.misyn.mcms.claim.dto.ListItemDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
public class UserDaoImpl implements UserDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(TireCondtionDaoImpl.class);

    @Override
    public List<UserDto> getUSersByAccessUsrType(Connection connection, Integer accessUsrType) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<UserDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_FROM_USR_MST_WHERE_ACCESSUSRTYPE);
            ps.setInt(1, accessUsrType);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserDto userDto = new UserDto();
                userDto.setUserCode(rs.getInt("n_usrcode"));
                userDto.setUserId(rs.getString("v_usrid"));
                list.add(userDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public UserDto getUserByUsrid(Connection connection, String usrid) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserDto userDto = new UserDto();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_USR_MST_BY_USRID);
            ps.setString(1, usrid);
            rs = ps.executeQuery();
            if (rs.next()) {
                userDto.setUserCode(rs.getInt("n_usrcode"));
                userDto.setUserId(rs.getString("v_usrid"));
                userDto.setFirstName(rs.getString("v_firstname"));
                userDto.setLastName(rs.getString("v_lastname"));
                userDto.setLandPhone(rs.getString("v_land_phone"));
                userDto.setMobile(rs.getString("v_mobile"));
                userDto.setAccessUserType(rs.getInt("n_accessusrtype"));
                userDto.setEmail(rs.getString("v_email"));
                userDto.setPaymentAuthLimit(rs.getDouble("N_PAYMENT_AUTH_LIMIT"));
            }
            return userDto;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (ps != null) {
                ps.close();
            }
            if (rs != null) {
                rs.close();
            }
        }
        return null;
    }

    @Override
    public String getAssessorType(Connection connection, String assessorCode) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserDto userDto = new UserDto();
        try {
            ps = connection.prepareStatement(SELCT_ASSESSOR_TYPE_BY_ASSESSOR_CODE);
            ps.setString(1, assessorCode);
            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getString("assessor_type");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<UserDto> getMembersForTeam(Connection connection, Integer teamId) throws Exception {
        List<UserDto> users = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_MEMBERS_FROM_TEAM_ID);
            ps.setInt(1, teamId);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserDto userDto = new UserDto();
                userDto.setUserCode(rs.getInt("n_usrcode"));
                userDto.setUserId(rs.getString("v_usrid"));
                userDto.setFirstName(rs.getString("v_firstname"));
                userDto.setLastName(rs.getString("v_lastname"));
                users.add(userDto);
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return users.size() > 0 ? users : null;
    }

    @Override
    public List<UserDto> findBranchUsersByBranchCode(Connection connection, String branchCode) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<UserDto> users = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_BRANCHUSERS_FROM_BRANCH_CODE);
            ps.setString(1, branchCode);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserDto userDto = new UserDto();
                userDto.setUserId(rs.getString("v_usrid"));
                users.add(userDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return users;
    }

    @Override
    public String getEmailByUserId(Connection connection, String assignUserId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_EMAIL_BY_USER_ID);
            ps.setString(1, assignUserId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getString("v_email");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isNeedToSendEmailByUserId(Connection connection, String assignUserId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_NEED_TO_SEND_EMAIL_BY_USER_CODE);
            ps.setString(1, assignUserId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return null != rs.getString("need_to_send_email") && AppConstant.YES.equalsIgnoreCase(rs.getString("need_to_send_email"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public List<ListItemDto> fetchSpecialTeam(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ListItemDto> userList = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SELECT_ALL_SPECIAL_TEAM_USERS);
            rs = ps.executeQuery();
            while (rs.next()) {
                ListItemDto listItemDto = new ListItemDto();
                listItemDto.setValue(rs.getString("v_usrid"));
                listItemDto.setLabel(rs.getString("full_name"));
                userList.add(listItemDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }

        return !userList.isEmpty() ? userList : null;
    }

    @Override
    public List<ListItemDto> fetchApprovalUserList(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ListItemDto> userList = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SELECT_ALL_APPROVAL_USERS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ListItemDto listItem = new ListItemDto();
                listItem.setValue(rs.getString("v_usrid"));
                listItem.setLabel(rs.getString("full_name"));
                userList.add(listItem);
            }
        } catch (Exception e) {
            LOGGER.error("Error fetching approval user list: ", e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }

        return !userList.isEmpty() ? userList : null;
    }

    @Override
    public int getAuthLevelByUserId(Connection connection, String assignUserId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int authLevel = AppConstant.ZERO_INT;
        try {
            ps = connection.prepareStatement(SELECT_AUTH_LEVEL_BY_USER_ID);
            ps.setString(1, assignUserId);
            rs = ps.executeQuery();
            if (rs.next()) {
                authLevel = rs.getInt("n_auth_level");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return authLevel;
    }

    @Override
    public BigDecimal getPaymentAuthLevelByUserName(Connection connection, String assignUser) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_PAYMENT_AUTH_LIMIT_BY_USER_NAME);
            ps.setString(1, assignUser);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getBigDecimal("N_PAYMENT_AUTH_LIMIT");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public int getAccessUserTypeByUserId(Connection connection, String assignUser) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int accessUserType = 0;
        try {
            ps = connection.prepareStatement(SELECT_ACCESS_USER_TYPE_FROM_USER_ID);
            ps.setString(1, assignUser);
            rs = ps.executeQuery();
            while (rs.next()) {
                accessUserType = rs.getInt("n_accessusrtype");
            }
            return accessUserType;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<String> getMotorEngineersByAuthLevels(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<String> userList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_ENGINEERS_WITH_AUTH_LEVEL.replace("?", AppConstant.ACCESS_LEVEL_RTE_ASS_RTE_OPE_RTE));
            rs = ps.executeQuery();
            while (rs.next()) {
                userList.add(rs.getString("user_name"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return userList;
    }

    @Override
    public List<String> getRteForReassign(Connection connection, String userName) throws Exception {
        PreparedStatement ps1;
        PreparedStatement ps2;
        ResultSet rs1;
        ResultSet rs2;
        List<String> userList = null;
        try {
            ps1 = connection.prepareStatement(GET_ASSIGN_ENGINEER_LEVEL);
            ps1.setString(1, userName);
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                userList = new ArrayList<>();
                Integer assignLevel = rs1.getInt("n_auth_level");
                String allowedLevels = null;
                switch (assignLevel) {
                    case 1:
                        allowedLevels = "1, 2";
                        break;
                    case 2:
                        allowedLevels = "2, 3, 4";
                        break;
                    case 3:
                        allowedLevels = "3, 4";
                        break;
                    case 4:
                        allowedLevels = "4";
                        break;
                    default:
                        allowedLevels = "0";
                }
//                Array allowedLevelsArray = connection.createArrayOf("INTEGER", allowedLevels);
                ps2 = connection.prepareStatement(GET_ENGINEERS_FOR_REASSIGN.replace("accessUsrTypes", AppConstant.ACCESS_LEVEL_RTE_ASS_RTE_OPE_RTE).replace("auth_levels", allowedLevels));
                rs2 = ps2.executeQuery();
                while (rs2.next()) {
                    userList.add(rs2.getString("user_name_value"));
                    userList.add(rs2.getString("user_name_text"));
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return userList;
    }

    @Override
    public List<String> getRteForReassign(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<String> userList = null;
        try {
            ps = connection.prepareStatement(GET_ENGINEERS_FOR_REASSIGN.replace("accessUsrTypes", AppConstant.ACCESS_LEVEL_RTE_ASS_RTE_OPE_RTE).replace("auth_levels", "1,2,3,4"));
            rs = ps.executeQuery();
            while (rs.next()) {
                userList = new ArrayList<>();
                userList.add(rs.getString("user_name_value"));
                userList.add(rs.getString("user_name_text"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return userList;
    }

    @Override
    public int getAccessUsertype(Connection connection, String userId) throws Exception {
        PreparedStatement ps;
        int usertype = AppConstant.ZERO_INT;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_ACCESS_USER_TYPE_FROM_USER_ID);
            ps.setString(1, userId);
            rs = ps.executeQuery();
            while (rs.next()) {
                usertype = Integer.parseInt(rs.getString("n_accessusrtype"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return usertype;
    }

    @Override
    public List<String> getTechnicalCodinators(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<String> userList = userList = new ArrayList<>();
        ;
        try {
            ps = connection.prepareStatement(GET_TECHNICAL_CORDINATORS_BY_USER_STATUS);
            rs = ps.executeQuery();
            while (rs.next()) {
                userList.add(rs.getString("v_usrid"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return userList;
    }


    @Override
    public UserDto getUser(Connection connection, String username) {
        UserDto user = null;
        PreparedStatement ps = null;
        PreparedStatement ps1;
        String strSql = "SELECT *"
                + "FROM "
                + "usr_mst u "
                + "where u.v_usrstatus<>'C' "
                + "  AND u.v_usrid=?";
        try {

            ps = connection.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setString(1, username);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                user = getUserDto(rs);
                if (user.getAccessUserType() == 22 || user.getAccessUserType() == 23 || user.getAccessUserType() == 24) {
                    ps1 = connection.prepareStatement("SELECT * FROM auth_assign_rte WHERE v_usrid = ?");
                    ps1.setString(1, username);
                    ResultSet rs1 = ps1.executeQuery();
                    while (rs1.next()) {
                        user.setRteLevel2(rs1.getString("rte_lvl_2"));
                        user.setRteLevel3(rs1.getString("rte_lvl_3"));
                        user.setRteLevel4(rs1.getString("rte_lvl_4"));
                    }
                    rs1.close();
                } else if (user.getAccessUserType() == 20) {
                    ps1 = connection.prepareStatement("SELECT V_NAME,V_DISTRICT_CODE FROM claim_assessor WHERE V_CODE = ? AND V_PARA_TYPE='ASSESSOR'");
                    ps1.setString(1, user.getEmployeeNumber());
                    ResultSet resultSet = ps1.executeQuery();
                    if (resultSet.next()) {
                        user.setFullName(resultSet.getString("V_NAME"));
                        user.setDistrictCode(resultSet.getString("V_DISTRICT_CODE"));
                    }
                    resultSet.close();
                }
            }
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());
            }
        }
        return user;
    }


    public UserDto userLoginValidate(Connection connection,String username) {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement("SELECT "
                    + "n_accessusrtype,"
                    + "v_usrstatus,"
                    + "v_usrid "
                    + "FROM usr_mst "
                    + "WHERE "
                    + "v_usrid=? "
                    + "AND "
                    + "v_usrstatus<>'C'");
            ps.setString(1, username);
            rs = ps.executeQuery();
            if (rs.next()) {
                UserDto user = new UserDto();
                user.setAccessUserType(rs.getInt("n_accessusrtype"));
                user.setUserStatus(rs.getString("v_usrstatus"));
                user.setUserId(rs.getString("v_usrid"));
                return user;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }


    private UserDto getUserDto(ResultSet rs){
        UserDto user = new UserDto();
        try {
            user.setUserCode(rs.getInt("u.n_usrcode"));
            user.setUserId(rs.getString("u.v_usrid").toLowerCase());
            user.setAccessUserType(rs.getInt("u.n_accessusrtype"));
            user.setTitle(rs.getString("u.v_title"));
            user.setFirstName(rs.getString("u.v_firstname"));
            user.setLastName(rs.getString("u.v_lastname"));
            user.setAddress1(rs.getString("u.v_address1"));
            user.setAddress2(rs.getString("u.v_address2"));
            user.setEmail(rs.getString("u.v_email"));
            user.setLandPhone(rs.getString("u.v_land_phone"));
            user.setMobile(rs.getString("u.v_mobile"));
            user.setNic(rs.getString("u.v_nic"));
            user.setEmployeeNumber(rs.getString("u.v_emp_no"));
            user.setUserStatus(rs.getString("u.v_usrstatus"));
            user.setReportingTo(rs.getString("u.V_REPORT_TO"));
            user.setTeamId(rs.getInt("u.N_TEAM_ID"));
            user.setLiabilityLimit(rs.getDouble("u.N_LIABLITY_LIMIT"));
            user.setPaymentLimit(rs.getDouble("u.N_PAYMENT_LIMIT"));
            user.setReserveLimit(rs.getDouble("u.N_RESERVE_LIMIT"));
            user.setPaymentAuthLimit(rs.getDouble("u.N_PAYMENT_AUTH_LIMIT"));
            user.setAssessorType(rs.getString("assessor_type"));
            user.setBranchCode(null == rs.getString("branch_code") ? AppConstant.STRING_EMPTY : rs.getString("branch_code"));
            user.setNeedToSendEmail(null == rs.getString("need_to_send_email") ? AppConstant.NO : rs.getString("need_to_send_email"));
            user.setRteReserveLimitLevel(rs.getInt("n_auth_level"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return user;
    }
}
