package com.misyn.mcms.claim.redis;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.misyn.mcms.claim.exception.BadRequestException;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.Map;
public class RedisService {
    private static final RedisConfig redisConfig = new RedisConfig(); // Static RedisConfig instance
    private static final ObjectMapper objectMapper = new ObjectMapper(); // Static ObjectMapper instance


    private RedisService() {

    }

    public static void main(String[] args) throws JsonProcessingException {


        // Cache some data
        // example.cacheData("Hello, Redis!");

        // Retrieve and print the cached data
        Map<String, String> cachedData = RedisService.getHCachedDataAsMap("supadmin");
        System.out.println("Cached Data: " + cachedData);

        Map<String, String> permission = new HashMap<>();
        permission.put("a1", "a1");
        permission.put("a2", "a2");
        permission.put("a3", "a3");


       // RedisService.cacheMapData("supadmin-permission", permission);
        RedisService.deleteCacheData("supadmin-permission");
        Map<String, String> cachedDataAsMap = RedisService.getCachedDataAsMap("supadmin-permission");

        System.out.println(cachedDataAsMap);


    }


    public static void cacheData(String key, String data) {
        try (Jedis jedis = redisConfig.createJedisClient()) {
            jedis.set(key, data);
        }
    }

    public static void deleteCacheData(String key) {
        try (Jedis jedis = redisConfig.createJedisClient()) {
            jedis.del(key);
        }
    }

    public static void cacheMapData(String key, Map<String, String> map) {
        try (Jedis jedis = redisConfig.createJedisClient()) {
            String jsonString = objectMapper.writeValueAsString(map);
            jedis.set(key, jsonString);
        } catch (JsonProcessingException e) {
            throw new BadRequestException(e);
        }
    }

    public static Map<String, String> getCachedDataAsMap(String key) {
        try (Jedis jedis = redisConfig.createJedisClient()) {
            String jsonString = jedis.get(key);
            return objectMapper.readValue(jsonString, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            throw new BadRequestException(e);
        }
    }

    public static String getFieldValue(String cachedKey, String fieldName) {
        Map<String, String> cachedDataAsMap = getCachedDataAsMap(cachedKey);
        if (cachedDataAsMap != null) {
            return cachedDataAsMap.get(fieldName);
        }
        return null;
    }

    public static Map<String, String> getHCachedDataAsMap(String key) {
        try (Jedis jedis = redisConfig.createJedisClient()) {
            return jedis.hgetAll("ApiAccessSummary:" + key); // Retrieve data from Redis using the specified key
        }
    }
}
