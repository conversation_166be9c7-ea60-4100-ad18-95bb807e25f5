/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class McmsClaimOfflinePaymentDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long nPaymentId;
    private String ovPayeeType;
    private String ovIdentificationNo;
    private String ovClaimNo;
    private BigDecimal onPaidAmount;
    private String ovInstitutionBranch;
    private String ovInstitutionCode;
    private BigDecimal onTotalPayable;
    private String ovIdentificationCode;
    private String ovVoucherFlag;
    private String dInsertDateTime;
    private Integer nRetryAttempt;
    private String vIsfsUpdateStat;
    private String dIsfsUpdateDateTime;
    private Integer referenceId;
    private Integer claimNo;
    private String policyChannelType;

    public McmsClaimOfflinePaymentDto() {
    }

    public McmsClaimOfflinePaymentDto(Long nPaymentId) {
        this.nPaymentId = nPaymentId;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getNPaymentId() {
        return nPaymentId;
    }

    public void setNPaymentId(Long nPaymentId) {
        this.nPaymentId = nPaymentId;
    }

    public String getOvPayeeType() {
        return ovPayeeType;
    }

    public void setOvPayeeType(String ovPayeeType) {
        this.ovPayeeType = ovPayeeType;
    }

    public String getOvIdentificationNo() {
        return ovIdentificationNo;
    }

    public void setOvIdentificationNo(String ovIdentificationNo) {
        this.ovIdentificationNo = ovIdentificationNo;
    }

    public String getOvClaimNo() {
        return ovClaimNo;
    }

    public void setOvClaimNo(String ovClaimNo) {
        this.ovClaimNo = ovClaimNo;
    }

    public BigDecimal getOnPaidAmount() {
        return onPaidAmount;
    }

    public void setOnPaidAmount(BigDecimal onPaidAmount) {
        this.onPaidAmount = onPaidAmount;
    }

    public String getOvInstitutionBranch() {
        return ovInstitutionBranch;
    }

    public void setOvInstitutionBranch(String ovInstitutionBranch) {
        this.ovInstitutionBranch = ovInstitutionBranch;
    }

    public String getOvInstitutionCode() {
        return ovInstitutionCode;
    }

    public void setOvInstitutionCode(String ovInstitutionCode) {
        this.ovInstitutionCode = ovInstitutionCode;
    }

    public BigDecimal getOnTotalPayable() {
        return onTotalPayable;
    }

    public void setOnTotalPayable(BigDecimal onTotalPayable) {
        this.onTotalPayable = onTotalPayable;
    }

    public String getOvIdentificationCode() {
        return ovIdentificationCode;
    }

    public void setOvIdentificationCode(String ovIdentificationCode) {
        this.ovIdentificationCode = ovIdentificationCode;
    }

    public String getOvVoucherFlag() {
        return ovVoucherFlag;
    }

    public void setOvVoucherFlag(String ovVoucherFlag) {
        this.ovVoucherFlag = ovVoucherFlag;
    }

    public Integer getNRetryAttempt() {
        return nRetryAttempt;
    }

    public void setNRetryAttempt(Integer nRetryAttempt) {
        this.nRetryAttempt = nRetryAttempt;
    }

    public String getVIsfsUpdateStat() {
        return vIsfsUpdateStat;
    }

    public void setVIsfsUpdateStat(String vIsfsUpdateStat) {
        this.vIsfsUpdateStat = vIsfsUpdateStat;
    }

    public String getdInsertDateTime() {
        return dInsertDateTime;
    }

    public void setdInsertDateTime(String dInsertDateTime) {
        this.dInsertDateTime = dInsertDateTime;
    }

    public String getdIsfsUpdateDateTime() {
        return dIsfsUpdateDateTime;
    }

    public void setdIsfsUpdateDateTime(String dIsfsUpdateDateTime) {
        this.dIsfsUpdateDateTime = dIsfsUpdateDateTime;
    }

    public Integer getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(Integer referenceId) {
        this.referenceId = referenceId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }
}
