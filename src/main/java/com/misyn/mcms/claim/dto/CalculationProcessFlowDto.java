package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;

import java.io.Serializable;
public class CalculationProcessFlowDto implements Serializable {
    private Integer txnId;
    private Integer calSheetId;
    private Integer claimNo;
    private Integer calSheetStatus;
    private String calSheetStatusDesc;
    private String inpUserId;
    private String inpDateTime;
    private String task;
    private String assignUserId;
    private String tat;

    private String taskCompletedDateTime = AppConstant.DEFAULT_DATE_TIME;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getCalSheetId() {
        return calSheetId;
    }

    public void setCalSheetId(Integer calSheetId) {
        this.calSheetId = calSheetId;
    }

    public Integer getCalSheetStatus() {
        return calSheetStatus;
    }

    public void setCalSheetStatus(Integer calSheetStatus) {
        this.calSheetStatus = calSheetStatus;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getCalSheetStatusDesc() {
        return calSheetStatusDesc;
    }

    public void setCalSheetStatusDesc(String calSheetStatusDesc) {
        this.calSheetStatusDesc = calSheetStatusDesc;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public String getTat() {
        long diff = Utility.getNoMiniutsTimeDiff(this.inpDateTime, this.taskCompletedDateTime, AppConstant.DATE_TIME_FORMAT);
        long[] dateTime = Utility.getDayHoursMinSecondDifferenceForWorkingHours(diff);
        tat = String.valueOf(dateTime[0]).concat(" d ").concat(String.valueOf(dateTime[1])).concat(" h ").concat(String.valueOf(dateTime[2])).concat(" m ");
        return tat;
    }

    public void setTat(String tat) {
        this.tat = tat;
    }

    public String getTaskCompletedDateTime() {
        return taskCompletedDateTime;
    }

    public void setTaskCompletedDateTime(String taskCompletedDateTime) {
        this.taskCompletedDateTime = taskCompletedDateTime;
    }
}
