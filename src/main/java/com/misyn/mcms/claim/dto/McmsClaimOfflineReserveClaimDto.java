/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class McmsClaimOfflineReserveClaimDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long nReserveClaimId;
    private String ovClaimNo;
    private BigDecimal onBillAmount;
    private BigDecimal onAllowedAmount;
    private String onDepPer;
    private BigDecimal onPaEstimateAmount;
    private String ovReportType;
    private String ovIdentificationNo;
    private String odDateOfAccssesSub;
    private String odDateOfAppointment;
    private String ovType;
    private String odDateOfAccssessment;
    private String ovPanelCategory;
    private String ovInstitutionBranch;
    private String ovInstitutionCode;
    private String ovIdentificationCode;
    private String ovPanelType;
    private String odReceivedDate;
    private String ovLossType;
    private String cvRequired;
    private String ovRiskNo;
    private String dInsertDateTime;
    private Integer nRetryAttempt;
    private String vIsfsUpdateStat;
    private String dIsfsUpdateDateTime;
    private Integer claimNo;
    private String policyChannelType;

    public McmsClaimOfflineReserveClaimDto() {
    }

    public McmsClaimOfflineReserveClaimDto(Long nReserveClaimId) {
        this.nReserveClaimId = nReserveClaimId;
    }

    public Long getNReserveClaimId() {
        return nReserveClaimId;
    }

    public void setNReserveClaimId(Long nReserveClaimId) {
        this.nReserveClaimId = nReserveClaimId;
    }

    public String getOvClaimNo() {
        return ovClaimNo;
    }

    public void setOvClaimNo(String ovClaimNo) {
        this.ovClaimNo = ovClaimNo;
    }

    public BigDecimal getOnBillAmount() {
        return onBillAmount;
    }

    public void setOnBillAmount(BigDecimal onBillAmount) {
        this.onBillAmount = onBillAmount;
    }

    public BigDecimal getOnAllowedAmount() {
        return onAllowedAmount;
    }

    public void setOnAllowedAmount(BigDecimal onAllowedAmount) {
        this.onAllowedAmount = onAllowedAmount;
    }

    public String getOnDepPer() {
        return onDepPer;
    }

    public void setOnDepPer(String onDepPer) {
        this.onDepPer = onDepPer;
    }

    public BigDecimal getOnPaEstimateAmount() {
        return onPaEstimateAmount;
    }

    public void setOnPaEstimateAmount(BigDecimal onPaEstimateAmount) {
        this.onPaEstimateAmount = onPaEstimateAmount;
    }

    public String getOvReportType() {
        return ovReportType;
    }

    public void setOvReportType(String ovReportType) {
        this.ovReportType = ovReportType;
    }

    public String getOvIdentificationNo() {
        return ovIdentificationNo;
    }

    public void setOvIdentificationNo(String ovIdentificationNo) {
        this.ovIdentificationNo = ovIdentificationNo;
    }

    public String getOdDateOfAccssesSub() {
        return odDateOfAccssesSub;
    }

    public void setOdDateOfAccssesSub(String odDateOfAccssesSub) {
        this.odDateOfAccssesSub = odDateOfAccssesSub;
    }

    public String getOdDateOfAppointment() {
        return odDateOfAppointment;
    }

    public void setOdDateOfAppointment(String odDateOfAppointment) {
        this.odDateOfAppointment = odDateOfAppointment;
    }

    public String getOvType() {
        return ovType;
    }

    public void setOvType(String ovType) {
        this.ovType = ovType;
    }

    public String getOdDateOfAccssessment() {
        return odDateOfAccssessment;
    }

    public void setOdDateOfAccssessment(String odDateOfAccssessment) {
        this.odDateOfAccssessment = odDateOfAccssessment;
    }

    public String getOvPanelCategory() {
        return ovPanelCategory;
    }

    public void setOvPanelCategory(String ovPanelCategory) {
        this.ovPanelCategory = ovPanelCategory;
    }

    public String getOvInstitutionBranch() {
        return ovInstitutionBranch;
    }

    public void setOvInstitutionBranch(String ovInstitutionBranch) {
        this.ovInstitutionBranch = ovInstitutionBranch;
    }

    public String getOvInstitutionCode() {
        return ovInstitutionCode;
    }

    public void setOvInstitutionCode(String ovInstitutionCode) {
        this.ovInstitutionCode = ovInstitutionCode;
    }

    public String getOvIdentificationCode() {
        return ovIdentificationCode;
    }

    public void setOvIdentificationCode(String ovIdentificationCode) {
        this.ovIdentificationCode = ovIdentificationCode;
    }

    public String getOvPanelType() {
        return ovPanelType;
    }

    public void setOvPanelType(String ovPanelType) {
        this.ovPanelType = ovPanelType;
    }

    public String getOdReceivedDate() {
        return odReceivedDate;
    }

    public void setOdReceivedDate(String odReceivedDate) {
        this.odReceivedDate = odReceivedDate;
    }

    public String getOvLossType() {
        return ovLossType;
    }

    public void setOvLossType(String ovLossType) {
        this.ovLossType = ovLossType;
    }

    public String getCvRequired() {
        return cvRequired;
    }

    public void setCvRequired(String cvRequired) {
        this.cvRequired = cvRequired;
    }

    public String getOvRiskNo() {
        return ovRiskNo;
    }

    public void setOvRiskNo(String ovRiskNo) {
        this.ovRiskNo = ovRiskNo;
    }

    public String getDInsertDateTime() {
        return dInsertDateTime;
    }

    public void setDInsertDateTime(String dInsertDateTime) {
        this.dInsertDateTime = dInsertDateTime;
    }

    public Integer getNRetryAttempt() {
        return nRetryAttempt;
    }

    public void setNRetryAttempt(Integer nRetryAttempt) {
        this.nRetryAttempt = nRetryAttempt;
    }

    public String getVIsfsUpdateStat() {
        return vIsfsUpdateStat;
    }

    public void setVIsfsUpdateStat(String vIsfsUpdateStat) {
        this.vIsfsUpdateStat = vIsfsUpdateStat;
    }

    public String getDIsfsUpdateDateTime() {
        return dIsfsUpdateDateTime;
    }

    public void setDIsfsUpdateDateTime(String dIsfsUpdateDateTime) {
        this.dIsfsUpdateDateTime = dIsfsUpdateDateTime;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }
}
