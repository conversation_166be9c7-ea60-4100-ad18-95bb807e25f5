/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class ClaimCalculationSheetDetailTypeDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer nCalSheetDetailTypeId;
    private String vCalSheetDetailTypeDesc;

    public ClaimCalculationSheetDetailTypeDto() {
    }

    public ClaimCalculationSheetDetailTypeDto(Integer nCalSheetDetailTypeId) {
        this.nCalSheetDetailTypeId = nCalSheetDetailTypeId;
    }

    public ClaimCalculationSheetDetailTypeDto(Integer nCalSheetDetailTypeId, String vCalSheetDetailTypeDesc) {
        this.nCalSheetDetailTypeId = nCalSheetDetailTypeId;
        this.vCalSheetDetailTypeDesc = vCalSheetDetailTypeDesc;
    }

    public Integer getNCalSheetDetailTypeId() {
        return nCalSheetDetailTypeId;
    }

    public void setNCalSheetDetailTypeId(Integer nCalSheetDetailTypeId) {
        this.nCalSheetDetailTypeId = nCalSheetDetailTypeId;
    }

    public String getVCalSheetDetailTypeDesc() {
        return vCalSheetDetailTypeDesc;
    }

    public void setVCalSheetDetailTypeDesc(String vCalSheetDetailTypeDesc) {
        this.vCalSheetDetailTypeDesc = vCalSheetDetailTypeDesc;
    }

}
