/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
public class ClaimCalculationSheetMainDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer calSheetId;
    private Integer claimNo;
    private Integer lossType;
    private Integer causeOfLoss;
    private Integer paymentType;
    private String paymentTypeDesc;
    private Integer calSheetType;
    private String calSheetTypeDesc;
    private BigDecimal labour;
    private BigDecimal parts;
    private BigDecimal totalLabourAndParts;
    private BigDecimal policyExcess = new BigDecimal("0.00");
    private BigDecimal underInsuranceRate;
    private BigDecimal underInsurance;
    private BigDecimal baldTyreRate;
    private BigDecimal baldTyre;
    private BigDecimal specialDeductions;
    private BigDecimal totalDeductions;
    private BigDecimal totalAfterDeductions;
    private BigDecimal paidAdvanceAmount;
    private BigDecimal payableAmount;
    private String remark;
    private Integer status;
    private String inputUser;
    private String inputDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String assignUserId;
    private String assignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String aprUserId;
    private String aprDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String aprAssignUser;
    private String aprAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String checkedUser;
    private String checkedDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String sparePartCordinatorAssignUserId;
    private String sparePartCordinatorAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String scrutinizeTeamAssignUserId;
    private String scrutinizeTeamAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String specialTeamAssignUserId;
    private String specialTeamAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String specialTeamMofaAssignUserId;
    private String specialTeamMofaAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String noObjectionStatus;
    private String premiumOutstandingStatus;
    private String ncbStatus = "P";
    private String claimStatus;
    private String isExcessInclude = AppConstant.NO;
    private String voucherNo;
    private String voucherGeneratedUserId;
    private String voucherGeneratedDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String isReleaseOrderGenerate = AppConstant.NO;
    private String releaseOrderGenerateUserId = AppConstant.EMPTY_STRING;
    private String releaseOrderGenerateDateTime = AppConstant.DEFAULT_DATE_TIME;
    private BigDecimal specialVatAmount;
    private BigDecimal specialNbtAmount;
    private String isNoObjectionUpload = AppConstant.STRING_PENDING;
    private Integer noObjectionDocRefNo = AppConstant.ZERO_INT;
    private String isPremiumOutstandingUpload = AppConstant.STRING_PENDING;
    private Integer premiumOutstandingDocRefNo = AppConstant.ZERO_INT;
    private String rteAssignUserId;
    private String rteAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String rteAction = AppConstant.STRING_EMPTY;

    private BigDecimal adjustVatAmount;
    private BigDecimal adjustNbtAmount;

    private String isAdjustVatAmount;
    private String isAdjustNbtAmount;

    private BigDecimal totalPaidAmount;

    private List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos;
    private List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos;
    private List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos;
    private ClaimCalculationSheetSupplierOrderDto claimCalculationSheetSupplierOrderDto = new ClaimCalculationSheetSupplierOrderDto();
    private ClaimDocumentStatusDto otherBillClaimDocumentStatusDto;
    private UserAuthorityLimitDto userAuthorityLimitDto;
    private List<BranchDetailDto> branchDetailDtos;


    private ClaimDocumentStatusDto billClaimDocumentStatusDto;
    private ClaimDocumentStatusDto estimateClaimDocumentStatusDto;
    private ClaimDocumentStatusDto drClaimDocumentStatusDto;
    private ClaimDocumentStatusDto supplyEstimateClaimDocumentStatusDto;
    private ClaimDocumentStatusDto otherEstimateClaimDocumentStatusDto;
    private ClaimDocumentStatusDto claimDocumentStatusNoObjectionDto;
    private ClaimDocumentStatusDto claimDocumentStatusPremiumOutstandingDto;
    private BigDecimal payableDiff = BigDecimal.ZERO;
    private String specialRemark = AppConstant.STRING_EMPTY;
    private BigDecimal payableAdvanced = BigDecimal.ZERO;
    private List<CalculationProcessFlowDto> calculationProcessFlowDtos = new ArrayList<>();
    private ClaimHandlerDto claimHandlerDto;
    private ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto;
    private List<CalSheetTypeDto> calSheetTypeList;
    private List<ClaimCalculationSheetSupplierOrderDto> supplierOrderCalculationList;
    private List<ClaimCalculationSheetMainDto> advanceListForClaim;
    private String supplyOrderSelectItem;
    private String lossTypeSelectItem;
    private String causeOfLossSelectItem;
    private String paymentTypeSelectItem;
    private String oaRateSelectItem;
    private String nbtRateSelectItem;
    private String vatRateSelectItem;
    private String garageSelectItem;
    private String leasingSelectItem;
    private String otherSelectItem;
    private String payeeSelectItem;
    private String isPayableAmount = AppConstant.NO;
    private String payeeDtosJson;
    private String branchDetailsJson;

    private List<CalculationSheetHistoryDto> calculationSheetHistoryDtoList;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public ClaimCalculationSheetPayeeDto getClaimCalculationSheetPayeeDto() {
        return claimCalculationSheetPayeeDto;
    }

    public void setClaimCalculationSheetPayeeDto(ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto) {
        this.claimCalculationSheetPayeeDto = claimCalculationSheetPayeeDto;
    }

    public BigDecimal getSpecialDeductions() {
        return specialDeductions;
    }

    public void setSpecialDeductions(BigDecimal specialDeductions) {
        this.specialDeductions = specialDeductions;
    }

    public String getIsExcessInclude() {
        return isExcessInclude;
    }

    public void setIsExcessInclude(String isExcessInclude) {
        this.isExcessInclude = isExcessInclude;
    }

    public List<ClaimCalculationSheetPayeeDto> getClaimCalculationSheetPayeeDtos() {
        return claimCalculationSheetPayeeDtos;
    }

    public void setClaimCalculationSheetPayeeDtos(List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos) {
        this.claimCalculationSheetPayeeDtos = claimCalculationSheetPayeeDtos;
    }

    public Integer getCalSheetId() {
        return calSheetId;
    }

    public void setCalSheetId(Integer calSheetId) {
        this.calSheetId = calSheetId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getLossType() {
        if (null == lossType) {
            return 0;
        } else {
            return lossType;
        }
    }

    public void setLossType(Integer lossType) {
        this.lossType = lossType;
    }

    public Integer getCauseOfLoss() {
        if (null == causeOfLoss) {
            return 0;
        } else {
            return causeOfLoss;
        }
    }

    public void setCauseOfLoss(Integer causeOfLoss) {
        this.causeOfLoss = causeOfLoss;
    }

    public Integer getPaymentType() {
        if (null == paymentType) {
            return 0;
        } else {
            return paymentType;
        }
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public Integer getCalSheetType() {
        if (null == calSheetType) {
            return 0;
        } else {
            return calSheetType;
        }
    }

    public void setCalSheetType(Integer calSheetType) {
        this.calSheetType = calSheetType;
    }

    public BigDecimal getLabour() {
        return labour;
    }

    public void setLabour(BigDecimal labour) {
        this.labour = labour;
    }

    public BigDecimal getParts() {
        return parts;
    }

    public void setParts(BigDecimal parts) {
        this.parts = parts;
    }

    public BigDecimal getPolicyExcess() {
        return policyExcess;
    }

    public void setPolicyExcess(BigDecimal policyExcess) {
        this.policyExcess = policyExcess;
    }

    public BigDecimal getUnderInsuranceRate() {
        return underInsuranceRate;
    }

    public void setUnderInsuranceRate(BigDecimal underInsuranceRate) {
        this.underInsuranceRate = underInsuranceRate;
    }

    public BigDecimal getUnderInsurance() {
        return underInsurance;
    }

    public void setUnderInsurance(BigDecimal underInsurance) {
        this.underInsurance = underInsurance;
    }

    public BigDecimal getBaldTyreRate() {
        return baldTyreRate;
    }

    public void setBaldTyreRate(BigDecimal baldTyreRate) {
        this.baldTyreRate = baldTyreRate;
    }

    public BigDecimal getBaldTyre() {
        return baldTyre;
    }

    public void setBaldTyre(BigDecimal baldTyre) {
        this.baldTyre = baldTyre;
    }

    public BigDecimal getPaidAdvanceAmount() {
        return paidAdvanceAmount;
    }

    public void setPaidAdvanceAmount(BigDecimal paidAdvanceAmount) {
        this.paidAdvanceAmount = paidAdvanceAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getInputDatetime() {
        if (null == inputDatetime || inputDatetime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return inputDatetime;
        }
    }

    public void setInputDatetime(String inputDatetime) {
        this.inputDatetime = inputDatetime;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public String getAssignDateTime() {
        if (null == assignDateTime || assignDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return assignDateTime;
        }
    }

    public void setAssignDateTime(String assignDateTime) {
        this.assignDateTime = assignDateTime;
    }

    public String getAprAssignUser() {
        return aprAssignUser;
    }

    public void setAprAssignUser(String aprAssignUser) {
        this.aprAssignUser = aprAssignUser;
    }

    public String getAprAssignDateTime() {
        if (null == aprAssignDateTime || aprAssignDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return aprAssignDateTime;
        }
    }

    public void setAprAssignDateTime(String aprAssignDateTime) {
        this.aprAssignDateTime = aprAssignDateTime;
    }

    public List<ClaimCalculationSheetDetailDto> getClaimCalculationSheetDetailLabourDtos() {
        return claimCalculationSheetDetailLabourDtos;
    }

    public void setClaimCalculationSheetDetailLabourDtos(List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos) {
        this.claimCalculationSheetDetailLabourDtos = claimCalculationSheetDetailLabourDtos;
    }

    public List<ClaimCalculationSheetDetailDto> getClaimCalculationSheetDetailReplacementDtos() {
        return claimCalculationSheetDetailReplacementDtos;
    }

    public void setClaimCalculationSheetDetailReplacementDtos(List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos) {
        this.claimCalculationSheetDetailReplacementDtos = claimCalculationSheetDetailReplacementDtos;
    }

    public BigDecimal getTotalLabourAndParts() {
        return totalLabourAndParts;
    }

    public void setTotalLabourAndParts(BigDecimal totalLabourAndParts) {
        this.totalLabourAndParts = totalLabourAndParts;
    }

    public BigDecimal getTotalDeductions() {
        return totalDeductions;
    }

    public void setTotalDeductions(BigDecimal totalDeductions) {
        this.totalDeductions = totalDeductions;
    }

    public BigDecimal getTotalAfterDeductions() {
        return totalAfterDeductions;
    }

    public void setTotalAfterDeductions(BigDecimal totalAfterDeductions) {
        this.totalAfterDeductions = totalAfterDeductions;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public String getAprUserId() {
        return aprUserId;
    }

    public void setAprUserId(String aprUserId) {
        this.aprUserId = aprUserId;
    }

    public String getAprDateTime() {
        if (null == aprDateTime || aprDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return aprDateTime;
        }
    }

    public void setAprDateTime(String aprDateTime) {
        this.aprDateTime = aprDateTime;
    }

    public String getCheckedUser() {
        return checkedUser;
    }

    public void setCheckedUser(String checkedUser) {
        this.checkedUser = checkedUser;
    }

    public String getCheckedDateTime() {
        if (null == checkedDateTime || checkedDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return checkedDateTime;
        }
    }

    public void setCheckedDateTime(String checkedDateTime) {
        this.checkedDateTime = checkedDateTime;
    }

    public ClaimDocumentStatusDto getOtherBillClaimDocumentStatusDto() {
        return otherBillClaimDocumentStatusDto;
    }

    public void setOtherBillClaimDocumentStatusDto(ClaimDocumentStatusDto otherBillClaimDocumentStatusDto) {
        this.otherBillClaimDocumentStatusDto = otherBillClaimDocumentStatusDto;
    }

    public ClaimDocumentStatusDto getBillClaimDocumentStatusDto() {
        return billClaimDocumentStatusDto;
    }

    public void setBillClaimDocumentStatusDto(ClaimDocumentStatusDto billClaimDocumentStatusDto) {
        this.billClaimDocumentStatusDto = billClaimDocumentStatusDto;
    }

    public ClaimDocumentStatusDto getEstimateClaimDocumentStatusDto() {
        return estimateClaimDocumentStatusDto;
    }

    public void setEstimateClaimDocumentStatusDto(ClaimDocumentStatusDto estimateClaimDocumentStatusDto) {
        this.estimateClaimDocumentStatusDto = estimateClaimDocumentStatusDto;
    }

    public ClaimDocumentStatusDto getDrClaimDocumentStatusDto() {
        return drClaimDocumentStatusDto;
    }

    public void setDrClaimDocumentStatusDto(ClaimDocumentStatusDto drClaimDocumentStatusDto) {
        this.drClaimDocumentStatusDto = drClaimDocumentStatusDto;
    }

    public ClaimDocumentStatusDto getSupplyEstimateClaimDocumentStatusDto() {
        return supplyEstimateClaimDocumentStatusDto;
    }

    public void setSupplyEstimateClaimDocumentStatusDto(ClaimDocumentStatusDto supplyEstimateClaimDocumentStatusDto) {
        this.supplyEstimateClaimDocumentStatusDto = supplyEstimateClaimDocumentStatusDto;
    }

    public ClaimDocumentStatusDto getOtherEstimateClaimDocumentStatusDto() {
        return otherEstimateClaimDocumentStatusDto;
    }

    public void setOtherEstimateClaimDocumentStatusDto(ClaimDocumentStatusDto otherEstimateClaimDocumentStatusDto) {
        this.otherEstimateClaimDocumentStatusDto = otherEstimateClaimDocumentStatusDto;
    }

    public ClaimCalculationSheetSupplierOrderDto getClaimCalculationSheetSupplierOrderDto() {
        return claimCalculationSheetSupplierOrderDto;
    }

    public void setClaimCalculationSheetSupplierOrderDto(ClaimCalculationSheetSupplierOrderDto claimCalculationSheetSupplierOrderDto) {
        this.claimCalculationSheetSupplierOrderDto = claimCalculationSheetSupplierOrderDto;
    }

    public String getNoObjectionStatus() {
        return noObjectionStatus;
    }

    public void setNoObjectionStatus(String noObjectionStatus) {
        this.noObjectionStatus = noObjectionStatus;
    }

    public String getPremiumOutstandingStatus() {
        return premiumOutstandingStatus;
    }

    public void setPremiumOutstandingStatus(String premiumOutstandingStatus) {
        this.premiumOutstandingStatus = premiumOutstandingStatus;
    }

    public String getNcbStatus() {
        return ncbStatus;
    }

    public void setNcbStatus(String ncbStatus) {
        this.ncbStatus = ncbStatus;
    }

    public String getSparePartCordinatorAssignUserId() {
        return sparePartCordinatorAssignUserId;
    }

    public void setSparePartCordinatorAssignUserId(String sparePartCordinatorAssignUserId) {
        this.sparePartCordinatorAssignUserId = sparePartCordinatorAssignUserId;
    }

    public String getSparePartCordinatorAssignDateTime() {
        if (null == sparePartCordinatorAssignDateTime || sparePartCordinatorAssignDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return sparePartCordinatorAssignDateTime;
        }
    }

    public void setSparePartCordinatorAssignDateTime(String sparePartCordinatorAssignDateTime) {
        this.sparePartCordinatorAssignDateTime = sparePartCordinatorAssignDateTime;
    }

    public String getScrutinizeTeamAssignUserId() {
        return scrutinizeTeamAssignUserId;
    }

    public void setScrutinizeTeamAssignUserId(String scrutinizeTeamAssignUserId) {
        this.scrutinizeTeamAssignUserId = scrutinizeTeamAssignUserId;
    }

    public String getScrutinizeTeamAssignDateTime() {
        if (null == scrutinizeTeamAssignDateTime || scrutinizeTeamAssignDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return scrutinizeTeamAssignDateTime;
        }
    }

    public void setScrutinizeTeamAssignDateTime(String scrutinizeTeamAssignDateTime) {
        this.scrutinizeTeamAssignDateTime = scrutinizeTeamAssignDateTime;
    }

    public String getSpecialTeamAssignUserId() {
        return specialTeamAssignUserId;
    }

    public void setSpecialTeamAssignUserId(String specialTeamAssignUserId) {
        this.specialTeamAssignUserId = specialTeamAssignUserId;
    }

    public String getSpecialTeamAssignDateTime() {
        if (null == specialTeamAssignDateTime || specialTeamAssignDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return specialTeamAssignDateTime;
        }
    }

    public void setSpecialTeamAssignDateTime(String specialTeamAssignDateTime) {
        this.specialTeamAssignDateTime = specialTeamAssignDateTime;
    }

    public String getSpecialTeamMofaAssignUserId() {
        return specialTeamMofaAssignUserId;
    }

    public void setSpecialTeamMofaAssignUserId(String specialTeamMofaAssignUserId) {
        this.specialTeamMofaAssignUserId = specialTeamMofaAssignUserId;
    }

    public String getSpecialTeamMofaAssignDateTime() {
        if (null == specialTeamMofaAssignDateTime || specialTeamMofaAssignDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return specialTeamMofaAssignDateTime;
        }
    }

    public void setSpecialTeamMofaAssignDateTime(String specialTeamMofaAssignDateTime) {
        this.specialTeamMofaAssignDateTime = specialTeamMofaAssignDateTime;
    }

    public String getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(String claimStatus) {
        this.claimStatus = claimStatus;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getVoucherGeneratedDateTime() {
        if (null == voucherGeneratedDateTime || voucherGeneratedDateTime.isEmpty()) {
            return AppConstant.DEFAULT_DATE_TIME;
        } else {
            return voucherGeneratedDateTime;
        }
    }

    public void setVoucherGeneratedDateTime(String voucherGeneratedDateTime) {
        this.voucherGeneratedDateTime = voucherGeneratedDateTime;
    }

    public String getVoucherGeneratedUserId() {
        return voucherGeneratedUserId;
    }

    public void setVoucherGeneratedUserId(String voucherGeneratedUserId) {
        this.voucherGeneratedUserId = voucherGeneratedUserId;
    }

    public BigDecimal getPayableDiff() {
        return payableDiff;
    }

    public void setPayableDiff(BigDecimal payableDiff) {
        this.payableDiff = payableDiff;
    }

    public String getSpecialRemark() {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
    }

    public BigDecimal getPayableAdvanced() {
        return payableAdvanced;
    }

    public void setPayableAdvanced(BigDecimal payableAdvanced) {
        this.payableAdvanced = payableAdvanced;
    }

    public List<CalculationProcessFlowDto> getCalculationProcessFlowDtos() {
        return calculationProcessFlowDtos;
    }

    public void setCalculationProcessFlowDtos(List<CalculationProcessFlowDto> calculationProcessFlowDtos) {
        this.calculationProcessFlowDtos = calculationProcessFlowDtos;
    }

    public String getPaymentTypeDesc() {
        return paymentTypeDesc;
    }

    public void setPaymentTypeDesc(String paymentTypeDesc) {
        this.paymentTypeDesc = paymentTypeDesc;
    }

    public String getCalSheetTypeDesc() {
        return calSheetTypeDesc;
    }

    public void setCalSheetTypeDesc(String calSheetTypeDesc) {
        this.calSheetTypeDesc = calSheetTypeDesc;
    }

    public ClaimHandlerDto getClaimHandlerDto() {
        return claimHandlerDto;
    }

    public void setClaimHandlerDto(ClaimHandlerDto claimHandlerDto) {
        this.claimHandlerDto = claimHandlerDto;
    }

    public String getIsReleaseOrderGenerate() {
        return isReleaseOrderGenerate;
    }

    public void setIsReleaseOrderGenerate(String isReleaseOrderGenerate) {
        this.isReleaseOrderGenerate = isReleaseOrderGenerate;
    }

    public String getReleaseOrderGenerateUserId() {
        return releaseOrderGenerateUserId;
    }

    public void setReleaseOrderGenerateUserId(String releaseOrderGenerateUserId) {
        this.releaseOrderGenerateUserId = releaseOrderGenerateUserId;
    }

    public String getReleaseOrderGenerateDateTime() {
        return releaseOrderGenerateDateTime;
    }

    public void setReleaseOrderGenerateDateTime(String releaseOrderGenerateDateTime) {
        this.releaseOrderGenerateDateTime = releaseOrderGenerateDateTime;
    }

    public BigDecimal getSpecialVatAmount() {
        return specialVatAmount;
    }

    public void setSpecialVatAmount(BigDecimal specialVatAmount) {
        this.specialVatAmount = specialVatAmount;
    }

    public BigDecimal getSpecialNbtAmount() {
        return specialNbtAmount;
    }

    public void setSpecialNbtAmount(BigDecimal specialNbtAmount) {
        this.specialNbtAmount = specialNbtAmount;
    }

    public ClaimDocumentStatusDto getClaimDocumentStatusNoObjectionDto() {
        return claimDocumentStatusNoObjectionDto;
    }

    public void setClaimDocumentStatusNoObjectionDto(ClaimDocumentStatusDto claimDocumentStatusNoObjectionDto) {
        this.claimDocumentStatusNoObjectionDto = claimDocumentStatusNoObjectionDto;
    }

    public ClaimDocumentStatusDto getClaimDocumentStatusPremiumOutstandingDto() {
        return claimDocumentStatusPremiumOutstandingDto;
    }

    public void setClaimDocumentStatusPremiumOutstandingDto(ClaimDocumentStatusDto claimDocumentStatusPremiumOutstandingDto) {
        this.claimDocumentStatusPremiumOutstandingDto = claimDocumentStatusPremiumOutstandingDto;
    }

    public List<CalSheetTypeDto> getCalSheetTypeList() {
        return calSheetTypeList;
    }

    public void setCalSheetTypeList(List<CalSheetTypeDto> calSheetTypeList) {
        this.calSheetTypeList = calSheetTypeList;
    }

    public List<ClaimCalculationSheetSupplierOrderDto> getSupplierOrderCalculationList() {
        return supplierOrderCalculationList;
    }

    public void setSupplierOrderCalculationList(List<ClaimCalculationSheetSupplierOrderDto> supplierOrderCalculationList) {
        this.supplierOrderCalculationList = supplierOrderCalculationList;
    }

    public List<ClaimCalculationSheetMainDto> getAdvanceListForClaim() {
        return advanceListForClaim;
    }

    public void setAdvanceListForClaim(List<ClaimCalculationSheetMainDto> advanceListForClaim) {
        this.advanceListForClaim = advanceListForClaim;
    }

    public String getSupplyOrderSelectItem() {
        return supplyOrderSelectItem;
    }

    public void setSupplyOrderSelectItem(String supplyOrderSelectItem) {
        this.supplyOrderSelectItem = supplyOrderSelectItem;
    }

    public String getLossTypeSelectItem() {
        return lossTypeSelectItem;
    }

    public void setLossTypeSelectItem(String lossTypeSelectItem) {
        this.lossTypeSelectItem = lossTypeSelectItem;
    }

    public String getCauseOfLossSelectItem() {
        return causeOfLossSelectItem;
    }

    public void setCauseOfLossSelectItem(String causeOfLossSelectItem) {
        this.causeOfLossSelectItem = causeOfLossSelectItem;
    }

    public String getPaymentTypeSelectItem() {
        return paymentTypeSelectItem;
    }

    public void setPaymentTypeSelectItem(String paymentTypeSelectItem) {
        this.paymentTypeSelectItem = paymentTypeSelectItem;
    }

    public String getOaRateSelectItem() {
        return oaRateSelectItem;
    }

    public void setOaRateSelectItem(String oaRateSelectItem) {
        this.oaRateSelectItem = oaRateSelectItem;
    }

    public String getNbtRateSelectItem() {
        return nbtRateSelectItem;
    }

    public void setNbtRateSelectItem(String nbtRateSelectItem) {
        this.nbtRateSelectItem = nbtRateSelectItem;
    }

    public String getVatRateSelectItem() {
        return vatRateSelectItem;
    }

    public void setVatRateSelectItem(String vatRateSelectItem) {
        this.vatRateSelectItem = vatRateSelectItem;
    }

    public String getGarageSelectItem() {
        return garageSelectItem;
    }

    public void setGarageSelectItem(String garageSelectItem) {
        this.garageSelectItem = garageSelectItem;
    }

    public String getLeasingSelectItem() {
        return leasingSelectItem;
    }

    public void setLeasingSelectItem(String leasingSelectItem) {
        this.leasingSelectItem = leasingSelectItem;
    }

    public String getOtherSelectItem() {
        return otherSelectItem;
    }

    public void setOtherSelectItem(String otherSelectItem) {
        this.otherSelectItem = otherSelectItem;
    }

    public String getPayeeSelectItem() {
        return payeeSelectItem;
    }

    public void setPayeeSelectItem(String payeeSelectItem) {
        this.payeeSelectItem = payeeSelectItem;
    }

    public String getIsPayableAmount() {
        return isPayableAmount;
    }

    public void setIsPayableAmount(String isPayableAmount) {
        this.isPayableAmount = isPayableAmount;
    }

    public String getPayeeDtosJson() {
        return payeeDtosJson;
    }

    public void setPayeeDtosJson(String payeeDtosJson) {
        this.payeeDtosJson = payeeDtosJson;
    }

    public String getIsNoObjectionUpload() {
        return isNoObjectionUpload;
    }

    public void setIsNoObjectionUpload(String isNoObjectionUpload) {
        this.isNoObjectionUpload = isNoObjectionUpload;
    }

    public Integer getNoObjectionDocRefNo() {
        return noObjectionDocRefNo;
    }

    public void setNoObjectionDocRefNo(Integer noObjectionDocRefNo) {
        this.noObjectionDocRefNo = noObjectionDocRefNo;
    }

    public String getIsPremiumOutstandingUpload() {
        return isPremiumOutstandingUpload;
    }

    public void setIsPremiumOutstandingUpload(String isPremiumOutstandingUpload) {
        this.isPremiumOutstandingUpload = isPremiumOutstandingUpload;
    }

    public Integer getPremiumOutstandingDocRefNo() {
        return premiumOutstandingDocRefNo;
    }

    public void setPremiumOutstandingDocRefNo(Integer premiumOutstandingDocRefNo) {
        this.premiumOutstandingDocRefNo = premiumOutstandingDocRefNo;
    }

    public UserAuthorityLimitDto getUserAuthorityLimitDto() {
        return userAuthorityLimitDto;
    }

    public void setUserAuthorityLimitDto(UserAuthorityLimitDto userAuthorityLimitDto) {
        this.userAuthorityLimitDto = userAuthorityLimitDto;
    }

    public String getRteAssignUserId() {
        return rteAssignUserId;
    }

    public void setRteAssignUserId(String rteAssignUserId) {
        this.rteAssignUserId = rteAssignUserId;
    }

    public String getRteAssignDateTime() {
        return rteAssignDateTime;
    }

    public void setRteAssignDateTime(String rteAssignDateTime) {
        this.rteAssignDateTime = rteAssignDateTime;
    }

    public List<BranchDetailDto> getBranchDetailDtos() {
        return branchDetailDtos;
    }

    public void setBranchDetailDtos(List<BranchDetailDto> branchDetailDtos) {
        this.branchDetailDtos = branchDetailDtos;
    }

    public String getBranchDetailsJson() {
        return branchDetailsJson;
    }

    public void setBranchDetailsJson(String branchDetailsJson) {
        this.branchDetailsJson = branchDetailsJson;
    }

    public List<CalculationSheetHistoryDto> getCalculationSheetHistoryDtoList() {
        return calculationSheetHistoryDtoList;
    }

    public void setCalculationSheetHistoryDtoList(List<CalculationSheetHistoryDto> calculationSheetHistoryDtoList) {
        this.calculationSheetHistoryDtoList = calculationSheetHistoryDtoList;
    }

    public String getRteAction() {
        return rteAction;
    }

    public void setRteAction(String rteAction) {
        this.rteAction = rteAction;
    }

    public BigDecimal getAdjustVatAmount() {
        return adjustVatAmount;
    }

    public void setAdjustVatAmount(BigDecimal adjustVatAmount) {
        this.adjustVatAmount = adjustVatAmount;
    }

    public BigDecimal getAdjustNbtAmount() {
        return adjustNbtAmount;
    }

    public void setAdjustNbtAmount(BigDecimal adjustNbtAmount) {
        this.adjustNbtAmount = adjustNbtAmount;
    }

    public String getIsAdjustVatAmount() {
        if (null == isAdjustVatAmount || isAdjustVatAmount.isEmpty()) {
            return AppConstant.NO;
        }
        return isAdjustVatAmount;
    }

    public void setIsAdjustVatAmount(String isAdjustVatAmount) {
        this.isAdjustVatAmount = isAdjustVatAmount;
    }

    public String getIsAdjustNbtAmount() {
        if (null == isAdjustNbtAmount || isAdjustNbtAmount.isEmpty()) {
            return AppConstant.NO;
        }
        return isAdjustNbtAmount;
    }

    public void setIsAdjustNbtAmount(String isAdjustNbtAmount) {
        this.isAdjustNbtAmount = isAdjustNbtAmount;
    }

    public BigDecimal getTotalPaidAmount() {
        return totalPaidAmount;
    }

    public void setTotalPaidAmount(BigDecimal totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }
}
