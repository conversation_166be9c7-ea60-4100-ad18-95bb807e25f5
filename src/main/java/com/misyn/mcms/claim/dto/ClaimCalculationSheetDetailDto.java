/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
public class ClaimCalculationSheetDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer calSheetDetailId;
    private Integer calSheetId;
    private Integer itemNo;
    private BigDecimal estimatedAmount;
    private BigDecimal billAmount;
    private BigDecimal approvedAmount;
    private String remarks;
    private BigDecimal nbtRate = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    private String nbtType;
    private BigDecimal nbtAmount;
    private BigDecimal vatRate = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    private String vatType;
    private BigDecimal vatAmount;
    private BigDecimal oa = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    private BigDecimal oaAmount;
    private BigDecimal totalAmount;
    private String billChecked;
    private String recordType;

    private String isRemoveVat;
    private String isAddNbt;
    private String addVatType;
    private BigDecimal amountWithoutVat = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    private BigDecimal totalAmountAfterOa = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    private BigDecimal amountWithNbt = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    private BigDecimal removeVatRate = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

    public ClaimCalculationSheetDetailDto() {
    }

    public ClaimCalculationSheetDetailDto(Integer itemNo, String addVatType) {
        this.addVatType = addVatType;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getCalSheetDetailId() {
        return calSheetDetailId;
    }

    public void setCalSheetDetailId(Integer calSheetDetailId) {
        this.calSheetDetailId = calSheetDetailId;
    }

    public Integer getCalSheetId() {
        return calSheetId;
    }

    public void setCalSheetId(Integer calSheetId) {
        this.calSheetId = calSheetId;
    }

    public Integer getItemNo() {
        return itemNo;
    }

    public void setItemNo(Integer itemNo) {
        this.itemNo = itemNo;
    }

    public BigDecimal getEstimatedAmount() {
        return estimatedAmount;
    }

    public void setEstimatedAmount(BigDecimal estimatedAmount) {
        this.estimatedAmount = estimatedAmount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public BigDecimal getApprovedAmount() {
        return approvedAmount;
    }

    public void setApprovedAmount(BigDecimal approvedAmount) {
        this.approvedAmount = approvedAmount;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BigDecimal getNbtRate() {
        return nbtRate;
    }

    public void setNbtRate(BigDecimal nbtRate) {
        this.nbtRate = nbtRate;
    }

    public BigDecimal getNbtAmount() {
        return nbtAmount;
    }

    public void setNbtAmount(BigDecimal nbtAmount) {
        this.nbtAmount = nbtAmount;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    public BigDecimal getOa() {
        return oa;
    }

    public void setOa(BigDecimal oa) {
        this.oa = oa;
    }

    public BigDecimal getOaAmount() {
        return oaAmount;
    }

    public void setOaAmount(BigDecimal oaAmount) {
        this.oaAmount = oaAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getBillChecked() {
        if (null == billChecked || billChecked.isEmpty()) {
            return "N";
        } else {
            return billChecked;
        }
    }

    public void setBillChecked(String billChecked) {
        this.billChecked = billChecked;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getVatType() {
        return vatType;
    }

    public void setVatType(String vatType) {
        this.vatType = vatType;
    }

    public String getNbtType() {
        return nbtType;
    }

    public void setNbtType(String nbtType) {
        this.nbtType = nbtType;
    }

    public String getIsRemoveVat() {
        if (null == isRemoveVat || isRemoveVat.isEmpty()) {
            return AppConstant.NO;
        } else {
            return isRemoveVat;
        }
    }

    public void setIsRemoveVat(String isRemoveVat) {
        this.isRemoveVat = isRemoveVat;
    }

    public String getIsAddNbt() {
        if (null == isAddNbt || isAddNbt.isEmpty()) {
            return AppConstant.NO;
        } else {
            return isAddNbt;
        }
    }

    public void setIsAddNbt(String isAddNbt) {
        this.isAddNbt = isAddNbt;
    }

    public String getAddVatType() {
        return addVatType;
    }

    public void setAddVatType(String addVatType) {
        this.addVatType = addVatType;
    }

    public BigDecimal getAmountWithoutVat() {
        return amountWithoutVat;
    }

    public void setAmountWithoutVat(BigDecimal amountWithoutVat) {
        this.amountWithoutVat = amountWithoutVat;
    }

    public BigDecimal getTotalAmountAfterOa() {
        return totalAmountAfterOa;
    }

    public void setTotalAmountAfterOa(BigDecimal totalAmountAfterOa) {
        this.totalAmountAfterOa = totalAmountAfterOa;
    }

    public BigDecimal getAmountWithNbt() {
        return amountWithNbt;
    }

    public void setAmountWithNbt(BigDecimal amountWithNbt) {
        this.amountWithNbt = amountWithNbt;
    }

    public BigDecimal getRemoveVatRate() {
        return removeVatRate;
    }

    public void setRemoveVatRate(BigDecimal removeVatRate) {
        this.removeVatRate = removeVatRate;
    }
}
