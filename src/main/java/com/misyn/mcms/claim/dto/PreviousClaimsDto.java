package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
public class PreviousClaimsDto implements Serializable {

    private Integer jobRefNo = AppConstant.ZERO_INT;
    private BigDecimal inspectionDetailsPav = BigDecimal.ZERO;
    private String assEstiAprStatus = AppConstant.STRING_EMPTY;
    private String assFeeAprStatus = AppConstant.STRING_EMPTY;
    private String jobNo = AppConstant.STRING_EMPTY;
    private Integer inspectionId = AppConstant.ZERO_INT;
    private Integer inspectionTypeId = AppConstant.ZERO_INT;
    private String inspectionType = AppConstant.STRING_EMPTY;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private String policyNo = AppConstant.STRING_EMPTY;
    private String dateOfAccident = AppConstant.STRING_EMPTY;
    private Integer claimNo = AppConstant.ZERO_INT;
    private Integer policyRefNo = AppConstant.ZERO_INT;
    private Integer refNo = AppConstant.ZERO_INT;
    private List<PreviousClaimsDto> list = new ArrayList<>();
    private String statusDesc = AppConstant.STRING_EMPTY;
    private Integer recordStatus = AppConstant.ZERO_INT;

    private String assignAssessor;
    private String assignRte;
    private String approveDateTime;
    private String approveAssignRte;

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public Integer getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(Integer inspectionId) {
        this.inspectionId = inspectionId;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getDateOfAccident() {
        return dateOfAccident;
    }

    public void setDateOfAccident(String dateOfAccident) {
        this.dateOfAccident = dateOfAccident;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public List<PreviousClaimsDto> getList() {
        return list;
    }

    public void setList(List<PreviousClaimsDto> list) {
        this.list = list;
    }

    public Integer getPolicyRefNo() {
        return policyRefNo;
    }

    public void setPolicyRefNo(Integer policyRefNo) {
        this.policyRefNo = policyRefNo;
    }

    public Integer getJobRefNo() {
        return jobRefNo;
    }

    public void setJobRefNo(Integer jobRefNo) {
        this.jobRefNo = jobRefNo;
    }

    public Integer getInspectionTypeId() {
        return inspectionTypeId;
    }

    public void setInspectionTypeId(Integer inspectionTypeId) {
        this.inspectionTypeId = inspectionTypeId;
    }

    public BigDecimal getInspectionDetailsPav() {
        return inspectionDetailsPav;
    }

    public void setInspectionDetailsPav(BigDecimal inspectionDetailsPav) {
        this.inspectionDetailsPav = inspectionDetailsPav;
    }

    public String getAssEstiAprStatus() {
        return assEstiAprStatus;
    }

    public void setAssEstiAprStatus(String assEstiAprStatus) {
        this.assEstiAprStatus = assEstiAprStatus;
    }

    public String getAssFeeAprStatus() {
        return assFeeAprStatus;
    }

    public void setAssFeeAprStatus(String assFeeAprStatus) {
        this.assFeeAprStatus = assFeeAprStatus;
    }

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(Integer recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getAssignRte() {
        return assignRte;
    }

    public void setAssignRte(String assignRte) {
        this.assignRte = assignRte;
    }

    public String getApproveDateTime() {
        return approveDateTime;
    }

    public void setApproveDateTime(String approveDateTime) {
        this.approveDateTime = approveDateTime;
    }

    public String getAssignAssessor() {
        return assignAssessor;
    }

    public void setAssignAssessor(String assignAssessor) {
        this.assignAssessor = assignAssessor;
    }

    public String getApproveAssignRte() {
        return approveAssignRte;
    }

    public void setApproveAssignRte(String approveAssignRte) {
        this.approveAssignRte = approveAssignRte;
    }
}
