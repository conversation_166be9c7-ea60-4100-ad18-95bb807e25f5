package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class MvwMemoCategoryDto implements Serializable {

    private String polNumber= AppConstant.EMPTY_STRING;
    private Integer renCount=AppConstant.ZERO_INT;
    private Integer endCount=AppConstant.ZERO_INT;
    private String text= AppConstant.EMPTY_STRING;
    private String lstUpdate= AppConstant.EMPTY_STRING;
    private String delFlag = AppConstant.STRING_EMPTY;

    public MvwMemoCategoryDto(String polNumber, Integer renCount, Integer endCount, String text, String lstUpdate, String delFlag) {
        this.polNumber = polNumber;
        this.renCount = renCount;
        this.endCount = endCount;
        this.text = text;
        this.lstUpdate = lstUpdate;
        this.delFlag = delFlag;
    }

    public MvwMemoCategoryDto() {
    }

    public String getPolNumber() {
        return polNumber;
    }

    public void setPolNumber(String polNumber) {
        this.polNumber = polNumber;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getLstUpdate() {
        return lstUpdate;
    }

    public void setLstUpdate(String lstUpdate) {
        this.lstUpdate = lstUpdate;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}
