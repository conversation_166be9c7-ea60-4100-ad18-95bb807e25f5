/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class ClaimCalculationSheetPayeeDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer itemNo;
    private Integer calSheetPayeeId;
    private int calSheetId;
    private Integer payeeId = 0;
    private String payeeDesc;
    private BigDecimal amount;
    private String isEftPayment = AppConstant.NO;
    private String accountNo;
    private String bankName;
    private String bankCode;
    private String branch;
    private String contactNo;
    private String emailAddress;
    private String voucherNo;
    private String emailStatus;
    private String responseDateTime;
    private BranchDetailDto branchDetailDto = new BranchDetailDto();
    private String chequeNo;
    private String chequeStatus;

    public Integer getCalSheetPayeeId() {
        return calSheetPayeeId;
    }

    public void setCalSheetPayeeId(Integer calSheetPayeeId) {
        this.calSheetPayeeId = calSheetPayeeId;
    }

    public int getCalSheetId() {
        return calSheetId;
    }

    public void setCalSheetId(int calSheetId) {
        this.calSheetId = calSheetId;
    }

    public Integer getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(Integer payeeId) {
        this.payeeId = payeeId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getIsEftPayment() {
        return isEftPayment;
    }

    public void setIsEftPayment(String isEftPayment) {
        this.isEftPayment = isEftPayment;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getContactNo() {
        return contactNo;
    }

    public void setContactNo(String contactNo) {
        this.contactNo = contactNo;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getPayeeDesc() {
        return payeeDesc;
    }

    public void setPayeeDesc(String payeeDesc) {
        this.payeeDesc = payeeDesc;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getEmailStatus() {
        return emailStatus;
    }

    public void setEmailStatus(String emailStatus) {
        this.emailStatus = emailStatus;
    }

    public String getResponseDateTime() {
        return responseDateTime;
    }

    public void setResponseDateTime(String responseDateTime) {
        this.responseDateTime = responseDateTime;
    }

    public Integer getItemNo() {
        return itemNo;
    }

    public void setItemNo(Integer itemNo) {
        this.itemNo = itemNo;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public BranchDetailDto getBranchDetailDto() {
        return branchDetailDto;
    }

    public void setBranchDetailDto(BranchDetailDto branchDetailDto) {
        this.branchDetailDto = branchDetailDto;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getChequeStatus() {
        return chequeStatus;
    }

    public void setChequeStatus(String chequeStatus) {
        this.chequeStatus = chequeStatus;
    }
}
