package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class PolicySrcctcDetailsDto implements Serializable {
    private String polNumber= AppConstant.EMPTY_STRING;
    private Integer renCount=AppConstant.ZERO_INT;
    private Integer endCount=AppConstant.ZERO_INT;
    private String addSec= AppConstant.EMPTY_STRING;
    private String desc= AppConstant.EMPTY_STRING;
    private String lstUpdUsr= AppConstant.EMPTY_STRING;
    private String lstUpdProg= AppConstant.EMPTY_STRING;
    private String lstUpd= AppConstant.EMPTY_STRING;
    private String sessionFlag= AppConstant.EMPTY_STRING;
    private String dltRecFlag= AppConstant.EMPTY_STRING;
    private String expDate= AppConstant.EMPTY_STRING;
    private String dltRefFlag= AppConstant.EMPTY_STRING;
    private String expDate1= AppConstant.EMPTY_STRING;
    private String dltRefFlag1= AppConstant.EMPTY_STRING;

    public PolicySrcctcDetailsDto(String polNumber, Integer renCount, Integer endCount, String addSec, String desc, String lstUpdUsr, String lstUpdProg, String lstUpd, String sessionFlag, String dltRecFlag, String expDate, String dltRefFlag, String expDate1, String dltRefFlag1) {
        this.polNumber = polNumber;
        this.renCount = renCount;
        this.endCount = endCount;
        this.addSec = addSec;
        this.desc = desc;
        this.lstUpdUsr = lstUpdUsr;
        this.lstUpdProg = lstUpdProg;
        this.lstUpd = lstUpd;
        this.sessionFlag = sessionFlag;
        this.dltRecFlag = dltRecFlag;
        this.expDate = expDate;
        this.dltRefFlag = dltRefFlag;
        this.expDate1 = expDate1;
        this.dltRefFlag1 = dltRefFlag1;
    }

    public PolicySrcctcDetailsDto() {
    }

    public String getPolNumber() {
        return polNumber;
    }

    public void setPolNumber(String polNumber) {
        this.polNumber = polNumber;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public String getAddSec() {
        return addSec;
    }

    public void setAddSec(String addSec) {
        this.addSec = addSec;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLstUpdUsr() {
        return lstUpdUsr;
    }

    public void setLstUpdUsr(String lstUpdUsr) {
        this.lstUpdUsr = lstUpdUsr;
    }

    public String getLstUpdProg() {
        return lstUpdProg;
    }

    public void setLstUpdProg(String lstUpdProg) {
        this.lstUpdProg = lstUpdProg;
    }

    public String getLstUpd() {
        return lstUpd;
    }

    public void setLstUpd(String lstUpd) {
        this.lstUpd = lstUpd;
    }

    public String getSessionFlag() {
        return sessionFlag;
    }

    public void setSessionFlag(String sessionFlag) {
        this.sessionFlag = sessionFlag;
    }

    public String getDltRecFlag() {
        return dltRecFlag;
    }

    public void setDltRecFlag(String dltRecFlag) {
        this.dltRecFlag = dltRecFlag;
    }

    public String getExpDate() {
        return expDate;
    }

    public void setExpDate(String expDate) {
        this.expDate = expDate;
    }

    public String getDltRefFlag() {
        return dltRefFlag;
    }

    public void setDltRefFlag(String dltRefFlag) {
        this.dltRefFlag = dltRefFlag;
    }

    public String getExpDate1() {
        return expDate1;
    }

    public void setExpDate1(String expDate1) {
        this.expDate1 = expDate1;
    }

    public String getDltRefFlag1() {
        return dltRefFlag1;
    }

    public void setDltRefFlag1(String dltRefFlag1) {
        this.dltRefFlag1 = dltRefFlag1;
    }
}
