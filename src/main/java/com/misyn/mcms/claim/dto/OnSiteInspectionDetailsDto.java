package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class OnSiteInspectionDetailsDto implements Serializable {

    private int inspectionId = AppConstant.ZERO_INT;
    private int refNo = AppConstant.ZERO_INT;
    private BigDecimal costPart = BigDecimal.ZERO;
    private BigDecimal costLabour = BigDecimal.ZERO;
    private BigDecimal excess = BigDecimal.ZERO;
    private BigDecimal acr = BigDecimal.ZERO;
    private ConditionType requestAri = ConditionType.No;
    private ConditionType boldTyrePenalty = ConditionType.No;
    private BigDecimal boldPercent = BigDecimal.ZERO;
    private BigDecimal boldTirePenaltyAmount = BigDecimal.ZERO;
    private ConditionType underInsuradPenalty = ConditionType.No;
    private BigDecimal underPenaltyPercent = BigDecimal.ZERO;
    private BigDecimal underPenaltyAmount = BigDecimal.ZERO;
    private ConditionType provideOffer = ConditionType.No;
    private int offerType = AppConstant.ZERO_INT; //1 --> On-Site Offer | 2 --> Off-Site Offer
    private ClaimOfferTypeDto claimOfferTypeDto = new ClaimOfferTypeDto();
    private BigDecimal payableAmount = BigDecimal.ZERO;
    private String specialRemark = AppConstant.STRING_EMPTY;
    private BigDecimal oldAcr = BigDecimal.ZERO;

    public int getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(int inspectionId) {
        this.inspectionId = inspectionId;
    }

    public BigDecimal getCostPart() {
        return costPart;
    }

    public void setCostPart(BigDecimal costPart) {
        this.costPart = costPart;
    }

    public BigDecimal getCostLabour() {
        return costLabour;
    }

    public void setCostLabour(BigDecimal costLabour) {
        this.costLabour = costLabour;
    }

    public BigDecimal getExcess() {
        return excess;
    }

    public void setExcess(BigDecimal excess) {
        this.excess = excess;
    }

    public BigDecimal getAcr() {
        return acr;
    }

    public void setAcr(BigDecimal acr) {
        this.acr = acr;
    }

    public ConditionType getBoldTyrePenalty() {
        return boldTyrePenalty;
    }

    public void setBoldTyrePenalty(ConditionType boldTyrePenalty) {
        this.boldTyrePenalty = boldTyrePenalty;
    }

    public BigDecimal getBoldPercent() {
        return boldPercent;
    }

    public void setBoldPercent(BigDecimal boldPercent) {
        this.boldPercent = boldPercent;
    }

    public BigDecimal getBoldTirePenaltyAmount() {
        return boldTirePenaltyAmount;
    }

    public void setBoldTirePenaltyAmount(BigDecimal boldTirePenaltyAmount) {
        this.boldTirePenaltyAmount = boldTirePenaltyAmount;
    }

    public ConditionType getUnderInsuradPenalty() {
        return underInsuradPenalty;
    }

    public void setUnderInsuradPenalty(ConditionType underInsuradPenalty) {
        this.underInsuradPenalty = underInsuradPenalty;
    }

    public BigDecimal getUnderPenaltyPercent() {
        return underPenaltyPercent;
    }

    public void setUnderPenaltyPercent(BigDecimal underPenaltyPercent) {
        this.underPenaltyPercent = underPenaltyPercent;
    }

    public BigDecimal getUnderPenaltyAmount() {
        return underPenaltyAmount;
    }

    public void setUnderPenaltyAmount(BigDecimal underPenaltyAmount) {
        this.underPenaltyAmount = underPenaltyAmount;
    }

    public ConditionType getProvideOffer() {
        return provideOffer;
    }

    public void setProvideOffer(ConditionType provideOffer) {
        this.provideOffer = provideOffer;
    }

    public ClaimOfferTypeDto getClaimOfferTypeDto() {
        return claimOfferTypeDto;
    }

    public void setClaimOfferTypeDto(ClaimOfferTypeDto claimOfferTypeDto) {
        this.claimOfferTypeDto = claimOfferTypeDto;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public int getOfferType() {
        return offerType;
    }

    public void setOfferType(int offerType) {
        this.offerType = offerType;
    }

    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public String getSpecialRemark() {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
    }

    public ConditionType getRequestAri() {
        return requestAri;
    }

    public void setRequestAri(ConditionType requestAri) {
        this.requestAri = requestAri;
    }

    public BigDecimal getOldAcr() {
        return oldAcr;
    }

    public void setOldAcr(BigDecimal oldAcr) {
        this.oldAcr = oldAcr;
    }
}
