package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class InspectionDetailsGridDto implements Serializable {
    private Integer refNo = AppConstant.ZERO_INT;
    private Integer claimNo = AppConstant.ZERO_INT;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private String jobNo = AppConstant.STRING_EMPTY;
    private String inspectionType = AppConstant.STRING_EMPTY;
    private String inspectionTypeId = AppConstant.STRING_EMPTY;
    private String status = AppConstant.STRING_EMPTY;
    private Integer index = AppConstant.ZERO_INT;
    private Integer statusId = AppConstant.ZERO_INT;
    private Integer polRefNo = AppConstant.ZERO_INT;
    private String assignDateTime = AppConstant.STRING_EMPTY;
    private String assignToRteDateTime = AppConstant.STRING_EMPTY;
    private Integer intimationType = AppConstant.ZERO_INT;
    private String jobStatus = AppConstant.STRING_EMPTY;
    private String estimationApprStatus = AppConstant.STRING_EMPTY;
    private String assFeeAprStatus = AppConstant.STRING_EMPTY;
    private String refNO = AppConstant.STRING_EMPTY;
    private int excessType = AppConstant.ZERO_INT;
    private int claimStatus = AppConstant.ZERO_INT;
    private String assignUser = AppConstant.STRING_EMPTY;
    private String offerName = AppConstant.STRING_EMPTY;
    private Integer offerType = AppConstant.ZERO_INT;
    private String priority = AppConstant.PRIORITY_NORMAL;

    private String assignRteUser = AppConstant.STRING_EMPTY;
    private String assignRteDateTime = AppConstant.DEFAULT_DATE_TIME;


    private String approveAssignRteUser = AppConstant.STRING_EMPTY;
    private String approveAssignRteDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String ccAssignedBy = AppConstant.EMPTY_STRING;

    private String isOnlineInspection = AppConstant.STRING_EMPTY;

    @Override
    public String toString() {
        return "InspectionDetailsGridDto{" +
                "refNo=" + refNo +
                ", claimNo=" + claimNo +
                ", assignUser='" + assignUser + '\'' +
                ", isOnlineInspection='" + isOnlineInspection + '\'' +
                '}';
    }

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public Integer getPolRefNo() {
        return polRefNo;
    }

    public void setPolRefNo(Integer polRefNo) {
        this.polRefNo = polRefNo;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getStatusId() {
        return statusId;
    }

    public void setStatusId(Integer statusId) {
        this.statusId = statusId;
    }


    public String getAssignDateTime() {
        return assignDateTime;
    }

    public void setAssignDateTime(String assignDateTime) {
        this.assignDateTime = assignDateTime;
    }

    public Integer getIntimationType() {
        return intimationType;
    }

    public void setIntimationType(Integer intimationType) {
        this.intimationType = intimationType;
    }

    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getRefNO() {
        return refNO;
    }

    public void setRefNO(String refNO) {
        this.refNO = refNO;
    }

    public int getExcessType() {
        return excessType;
    }

    public void setExcessType(int excessType) {
        this.excessType = excessType;
    }

    public String getEstimationApprStatus() {
        return estimationApprStatus;
    }

    public void setEstimationApprStatus(String estimationApprStatus) {
        this.estimationApprStatus = estimationApprStatus;
    }

    public String getAssFeeAprStatus() {
        return assFeeAprStatus;
    }

    public void setAssFeeAprStatus(String assFeeAprStatus) {
        this.assFeeAprStatus = assFeeAprStatus;
    }

    public String getAssignToRteDateTime() {
        return assignToRteDateTime;
    }

    public void setAssignToRteDateTime(String assignToRteDateTime) {
        this.assignToRteDateTime = assignToRteDateTime;
    }

    public String getInspectionTypeId() {
        return inspectionTypeId;
    }

    public void setInspectionTypeId(String inspectionTypeId) {
        this.inspectionTypeId = inspectionTypeId;
    }

    public int getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(int claimStatus) {
        this.claimStatus = claimStatus;
    }

    public String getAssignUser() {
        return assignUser;
    }

    public void setAssignUser(String assignUser) {
        this.assignUser = assignUser;
    }

    public String getOfferName() {
        return offerName;
    }

    public void setOfferName(String offerName) {
        this.offerName = offerName;
    }

    public Integer getOfferType() {
        return offerType;
    }

    public void setOfferType(Integer offerType) {
        this.offerType = offerType;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getApproveAssignRteUser() {
        return approveAssignRteUser;
    }

    public void setApproveAssignRteUser(String approveAssignRteUser) {
        this.approveAssignRteUser = approveAssignRteUser;
    }

    public String getApproveAssignRteDateTime() {
        return approveAssignRteDateTime;
    }

    public void setApproveAssignRteDateTime(String approveAssignRteDateTime) {
        this.approveAssignRteDateTime = approveAssignRteDateTime;
    }

    public String getAssignRteUser() {
        return assignRteUser;
    }

    public void setAssignRteUser(String assignRteUser) {
        this.assignRteUser = assignRteUser;
    }

    public String getAssignRteDateTime() {
        return assignRteDateTime;
    }

    public void setAssignRteDateTime(String assignRteDateTime) {
        this.assignRteDateTime = assignRteDateTime;
    }

    public String getCcAssignedBy() {
        return ccAssignedBy;
    }

    public void setCcAssignedBy(String ccAssignedBy) {
        this.ccAssignedBy = ccAssignedBy;
    }

    public String getIsOnlineInspection() {
        return isOnlineInspection;
    }

    public void setIsOnlineInspection(String isOnlineInspection) {
        this.isOnlineInspection = isOnlineInspection;
    }
}
