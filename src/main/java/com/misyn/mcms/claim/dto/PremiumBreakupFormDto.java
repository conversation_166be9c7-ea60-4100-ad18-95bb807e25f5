package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class PremiumBreakupFormDto implements Serializable {

    private List<PolicyPremiumDto> policyPremiumDtoList = new ArrayList<>();
    private List<PolicyPremiumDto> policyPremiumRiotStrikeList = new ArrayList<>();
    private ChargesBreakupDto chargesBreakupDto = new ChargesBreakupDto();

    public List<PolicyPremiumDto> getPolicyPremiumDtoList() {
        return policyPremiumDtoList;
    }

    public void setPolicyPremiumDtoList(List<PolicyPremiumDto> policyPremiumDtoList) {
        this.policyPremiumDtoList = policyPremiumDtoList;
    }

    public ChargesBreakupDto getChargesBreakupDto() {
        return chargesBreakupDto;
    }

    public void setChargesBreakupDto(ChargesBreakupDto chargesBreakupDto) {
        this.chargesBreakupDto = chargesBreakupDto;
    }

    public List<PolicyPremiumDto> getPolicyPremiumRiotStrikeList() {
        return policyPremiumRiotStrikeList;
    }

    public void setPolicyPremiumRiotStrikeList(List<PolicyPremiumDto> policyPremiumRiotStrikeList) {
        this.policyPremiumRiotStrikeList = policyPremiumRiotStrikeList;
    }
}
