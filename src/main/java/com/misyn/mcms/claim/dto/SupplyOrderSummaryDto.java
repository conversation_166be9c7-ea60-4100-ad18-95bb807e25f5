package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.SupplyOrderStatusEnum;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
public class SupplyOrderSummaryDto implements Serializable {

    private Integer supplyOrderRefNo = 0;
    private Integer claimNo;
    private Integer supplierId = AppConstant.ZERO_INT;
    private String supplyOrderSerialNo;
    private String supplyOrderStatus = SupplyOrderStatusEnum.PENDING.getSupplyOrderStatusEnum();
    private String supplierEmail;
    private String supplierContactNo;
    private BigDecimal totalAmount = BigDecimal.ZERO;
    private BigDecimal totalOwnersAccountAmount = BigDecimal.ZERO;
    private BigDecimal othertDeductionAmount = BigDecimal.ZERO;
    private BigDecimal policyExcess = BigDecimal.ZERO;
    private BigDecimal finalAmount = BigDecimal.ZERO;
    private String supplyOrderRemark;
    private String otherRemark;
    private String workShopName;
    private String workShopAddress1;
    private String workShopAddress2;
    private String workShopAddress3;
    private String workShopContactNo;
    private String approveAssignSparePartCoordinator;
    private String approveAssignSparePartCoordinatorDateTime;
    private String inputUserId;
    private String inputDateTime = AppConstant.DEFAULT_DATE_TIME;


    private String generateUserId;
    private String generateDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String isGenerate = AppConstant.NO;
    private List<SupplyOrderDetailsDto> supplyOrderDetailsDtoList = new ArrayList<>(1000);

    private String apprvAssignScrutinizingUserId;
    private String apprvAssignScrutinizingDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String apprvScrutinizingUserId;
    private String apprvScrutinizingDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String apprvAssignSpecialTeamUserId;
    private String apprvAssignSpecialTeamDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String apprvClaimHandlerUserId;
    private String apprvClaimHandlerDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String apprvSpecialTeamUserId;
    private String apprvSpecialTeamDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer no;
    private String supplierName;
    private ClaimsDto claimsDto = new ClaimsDto();

    private String inputUserFullName;
    private String apprvScrutinizingUserFullName;
    private String apprvClaimHandlerUserFullName;
    private String isExcessInclude = AppConstant.NO;
    private BigDecimal vatAmount = BigDecimal.ZERO;
    private String vatStatus;
    private String isUpdated = AppConstant.NO;

    private String masterRecord;
    private Integer supplyOrderDetailRefNo;
    private String voucherGenerated = AppConstant.NO;
    private Boolean limitExceeded = Boolean.FALSE;

    public Integer getSupplyOrderRefNo() {
        return supplyOrderRefNo;
    }

    public void setSupplyOrderRefNo(Integer supplyOrderRefNo) {
        this.supplyOrderRefNo = supplyOrderRefNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplyOrderSerialNo() {
        return supplyOrderSerialNo;
    }

    public void setSupplyOrderSerialNo(String supplyOrderSerialNo) {
        this.supplyOrderSerialNo = supplyOrderSerialNo;
    }

    public String getSupplyOrderStatus() {
        return supplyOrderStatus;
    }

    public void setSupplyOrderStatus(String supplyOrderStatus) {
        this.supplyOrderStatus = supplyOrderStatus;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalOwnersAccountAmount() {
        return totalOwnersAccountAmount;//
    }

    public void setTotalOwnersAccountAmount(BigDecimal totalOwnersAccountAmount) {
        this.totalOwnersAccountAmount = totalOwnersAccountAmount;
    }

    public BigDecimal getOthertDeductionAmount() {
        return othertDeductionAmount;
    }

    public void setOthertDeductionAmount(BigDecimal othertDeductionAmount) {
        this.othertDeductionAmount = othertDeductionAmount;
    }

    public BigDecimal getPolicyExcess() {
        return policyExcess;
    }

    public void setPolicyExcess(BigDecimal policyExcess) {
        this.policyExcess = policyExcess;
    }

    public BigDecimal getFinalAmount() {
        return finalAmount;
    }

    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    public String getSupplyOrderRemark() {
        return supplyOrderRemark;
    }

    public void setSupplyOrderRemark(String supplyOrderRemark) {
        this.supplyOrderRemark = supplyOrderRemark;
    }

    public String getOtherRemark() {
        return otherRemark;
    }

    public void setOtherRemark(String otherRemark) {
        this.otherRemark = otherRemark;
    }

    public String getWorkShopName() {
        return workShopName;
    }

    public void setWorkShopName(String workShopName) {
        this.workShopName = workShopName;
    }

    public String getWorkShopAddress1() {
        return workShopAddress1;
    }

    public void setWorkShopAddress1(String workShopAddress1) {
        this.workShopAddress1 = workShopAddress1;
    }

    public String getWorkShopAddress2() {
        return workShopAddress2;
    }

    public void setWorkShopAddress2(String workShopAddress2) {
        this.workShopAddress2 = workShopAddress2;
    }

    public String getWorkShopAddress3() {
        return workShopAddress3;
    }

    public void setWorkShopAddress3(String workShopAddress3) {
        this.workShopAddress3 = workShopAddress3;
    }

    public String getSupplierEmail() {
        return supplierEmail;
    }

    public void setSupplierEmail(String supplierEmail) {
        this.supplierEmail = supplierEmail;
    }

    public String getSupplierContactNo() {
        return supplierContactNo;
    }

    public void setSupplierContactNo(String supplierContactNo) {
        this.supplierContactNo = supplierContactNo;
    }

    public String getWorkShopContactNo() {
        return workShopContactNo;
    }

    public void setWorkShopContactNo(String workShopContactNo) {
        this.workShopContactNo = workShopContactNo;
    }

    public String getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(String inputUserId) {
        this.inputUserId = inputUserId;
    }

    public String getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(String inputDateTime) {
        this.inputDateTime = inputDateTime;
    }

    public String getApprvAssignScrutinizingUserId() {
        return apprvAssignScrutinizingUserId;
    }

    public void setApprvAssignScrutinizingUserId(String apprvAssignScrutinizingUserId) {
        this.apprvAssignScrutinizingUserId = apprvAssignScrutinizingUserId;
    }

    public String getApprvAssignScrutinizingDateTime() {
        return apprvAssignScrutinizingDateTime;
    }

    public void setApprvAssignScrutinizingDateTime(String apprvAssignScrutinizingDateTime) {
        this.apprvAssignScrutinizingDateTime = apprvAssignScrutinizingDateTime;
    }

    public String getApprvScrutinizingUserId() {
        return apprvScrutinizingUserId;
    }

    public void setApprvScrutinizingUserId(String apprvScrutinizingUserId) {
        this.apprvScrutinizingUserId = apprvScrutinizingUserId;
    }

    public String getApprvScrutinizingDateTime() {
        return apprvScrutinizingDateTime;
    }

    public void setApprvScrutinizingDateTime(String apprvScrutinizingDateTime) {
        this.apprvScrutinizingDateTime = apprvScrutinizingDateTime;
    }

    public String getApprvAssignSpecialTeamUserId() {
        return apprvAssignSpecialTeamUserId;
    }

    public void setApprvAssignSpecialTeamUserId(String apprvAssignSpecialTeamUserId) {
        this.apprvAssignSpecialTeamUserId = apprvAssignSpecialTeamUserId;
    }

    public String getApprvAssignSpecialTeamDateTime() {
        return apprvAssignSpecialTeamDateTime;
    }

    public void setApprvAssignSpecialTeamDateTime(String apprvAssignSpecialTeamDateTime) {
        this.apprvAssignSpecialTeamDateTime = apprvAssignSpecialTeamDateTime;
    }

    public String getApprvSpecialTeamUserId() {
        return apprvSpecialTeamUserId;
    }

    public void setApprvSpecialTeamUserId(String apprvSpecialTeamUserId) {
        this.apprvSpecialTeamUserId = apprvSpecialTeamUserId;
    }

    public String getApprvSpecialTeamDateTime() {
        return apprvSpecialTeamDateTime;
    }

    public void setApprvSpecialTeamDateTime(String apprvSpecialTeamDateTime) {
        this.apprvSpecialTeamDateTime = apprvSpecialTeamDateTime;
    }

    public String getGenerateUserId() {
        return generateUserId;
    }

    public void setGenerateUserId(String generateUserId) {
        this.generateUserId = generateUserId;
    }

    public String getGenerateDateTime() {
        return generateDateTime;
    }

    public void setGenerateDateTime(String generateDateTime) {
        this.generateDateTime = generateDateTime;
    }

    public String getIsGenerate() {
        return isGenerate;
    }

    public void setIsGenerate(String isGenerate) {
        this.isGenerate = isGenerate;
    }

    public List<SupplyOrderDetailsDto> getSupplyOrderDetailsDtoList() {
        return supplyOrderDetailsDtoList;
    }

    public void setSupplyOrderDetailsDtoList(List<SupplyOrderDetailsDto> supplyOrderDetailsDtoList) {
        this.supplyOrderDetailsDtoList = supplyOrderDetailsDtoList;
    }

    public String getApprvClaimHandlerUserId() {
        return apprvClaimHandlerUserId;
    }

    public void setApprvClaimHandlerUserId(String apprvClaimHandlerUserId) {
        this.apprvClaimHandlerUserId = apprvClaimHandlerUserId;
    }

    public String getApprvClaimHandlerDateTime() {
        return apprvClaimHandlerDateTime;
    }

    public void setApprvClaimHandlerDateTime(String apprvClaimHandlerDateTime) {
        this.apprvClaimHandlerDateTime = apprvClaimHandlerDateTime;
    }

    public Integer getNo() {
        return no;
    }

    public void setNo(Integer no) {
        this.no = no;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public ClaimsDto getClaimsDto() {
        return claimsDto;
    }

    public void setClaimsDto(ClaimsDto claimsDto) {
        this.claimsDto = claimsDto;
    }

    public String getInputUserFullName() {
        return inputUserFullName;
    }

    public void setInputUserFullName(String inputUserFullName) {
        this.inputUserFullName = inputUserFullName;
    }

    public String getApprvScrutinizingUserFullName() {
        return apprvScrutinizingUserFullName;
    }

    public void setApprvScrutinizingUserFullName(String apprvScrutinizingUserFullName) {
        this.apprvScrutinizingUserFullName = apprvScrutinizingUserFullName;
    }

    public String getApprvClaimHandlerUserFullName() {
        return apprvClaimHandlerUserFullName;
    }

    public void setApprvClaimHandlerUserFullName(String apprvClaimHandlerUserFullName) {
        this.apprvClaimHandlerUserFullName = apprvClaimHandlerUserFullName;
    }

    public String getIsExcessInclude() {
        return isExcessInclude;
    }

    public void setIsExcessInclude(String isExcessInclude) {
        this.isExcessInclude = isExcessInclude;
    }

    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    public String getVatStatus() {
        return vatStatus;
    }

    public void setVatStatus(String vatStatus) {
        this.vatStatus = vatStatus;
    }

    public String getApproveAssignSparePartCoordinator() {
        return approveAssignSparePartCoordinator;
    }

    public void setApproveAssignSparePartCoordinator(String approveAssignSparePartCoordinator) {
        this.approveAssignSparePartCoordinator = approveAssignSparePartCoordinator;
    }

    public String getApproveAssignSparePartCoordinatorDateTime() {
        return approveAssignSparePartCoordinatorDateTime;
    }

    public void setApproveAssignSparePartCoordinatorDateTime(String approveAssignSparePartCoordinatorDateTime) {
        this.approveAssignSparePartCoordinatorDateTime = approveAssignSparePartCoordinatorDateTime;
    }

    public String getIsUpdated() {
        return isUpdated;
    }

    public void setIsUpdated(String isUpdated) {
        this.isUpdated = isUpdated;
    }

    public String getMasterRecord() {
        return masterRecord;
    }

    public void setMasterRecord(String masterRecord) {
        this.masterRecord = masterRecord;
    }

    public Integer getSupplyOrderDetailRefNo() {
        return supplyOrderDetailRefNo;
    }

    public void setSupplyOrderDetailRefNo(Integer supplyOrderDetailRefNo) {
        this.supplyOrderDetailRefNo = supplyOrderDetailRefNo;
    }

    public String getVoucherGenerated() {
        return voucherGenerated;
    }

    public void setVoucherGenerated(String voucherGenerated) {
        this.voucherGenerated = voucherGenerated;
    }

    public Boolean getLimitExceeded() {
        return limitExceeded;
    }

    public void setLimitExceeded(Boolean limitExceeded) {
        this.limitExceeded = limitExceeded;
    }
}
