package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ReminderPrintSummaryDto implements Serializable {
    private Integer reminderSummaryRefId = AppConstant.ZERO_INT;
    private Integer claimNo;
    private String printUserId;
    private String printDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String generatedUserId;
    private String generatedDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String recordStatus = "A";
    private List<ReminderPrintDetailsDto> reminderPrintDetailsList = new ArrayList<>();
    private ClaimsDto claimsDto = new ClaimsDto();

    public Integer getReminderSummaryRefId() {
        return reminderSummaryRefId;
    }

    public void setReminderSummaryRefId(Integer reminderSummaryRefId) {
        this.reminderSummaryRefId = reminderSummaryRefId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getPrintUserId() {
        return printUserId;
    }

    public void setPrintUserId(String printUserId) {
        this.printUserId = printUserId;
    }

    public String getPrintDateTime() {
        return printDateTime;
    }

    public void setPrintDateTime(String printDateTime) {
        this.printDateTime = printDateTime;
    }

    public String getGeneratedUserId() {
        return generatedUserId;
    }

    public void setGeneratedUserId(String generatedUserId) {
        this.generatedUserId = generatedUserId;
    }

    public String getGeneratedDateTime() {
        return generatedDateTime;
    }

    public void setGeneratedDateTime(String generatedDateTime) {
        this.generatedDateTime = generatedDateTime;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    public List<ReminderPrintDetailsDto> getReminderPrintDetailsList() {
        return reminderPrintDetailsList;
    }

    public void setReminderPrintDetailsList(List<ReminderPrintDetailsDto> reminderPrintDetailsList) {
        this.reminderPrintDetailsList = reminderPrintDetailsList;
    }

    public ClaimsDto getClaimsDto() {
        return claimsDto;
    }

    public void setClaimsDto(ClaimsDto claimsDto) {
        this.claimsDto = claimsDto;
    }
}
