/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class ClaimUserAllocationDto implements Serializable {

    private Integer txnId;
    private String assignUserId;
    private Integer totAssignReport;
    private Integer assignIndex;
    private Integer totAssignReportToday;
    private String lastAssignDate;
    private Integer accessUsrType;
    private String status;

    /**
     * @return the txnId
     */
    public Integer getTxnId() {
        return txnId;
    }

    /**
     * @param txnId the txnId to set
     */
    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    /**
     * @return the assignUserId
     */
    public String getAssignUserId() {
        return assignUserId;
    }

    /**
     * @param assignUserId the assignUserId to set
     */
    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    /**
     * @return the totAssignReport
     */
    public Integer getTotAssignReport() {
        return totAssignReport;
    }

    /**
     * @param totAssignReport the totAssignReport to set
     */
    public void setTotAssignReport(Integer totAssignReport) {
        this.totAssignReport = totAssignReport;
    }

    /**
     * @return the totAssignReportToday
     */
    public Integer getTotAssignReportToday() {
        return totAssignReportToday;
    }

    /**
     * @param totAssignReportToday the totAssignReportToday to set
     */
    public void setTotAssignReportToday(Integer totAssignReportToday) {
        this.totAssignReportToday = totAssignReportToday;
    }

    /**
     * @return the lastAssignDate
     */
    public String getLastAssignDate() {
        return lastAssignDate;
    }

    /**
     * @param lastAssignDate the lastAssignDate to set
     */
    public void setLastAssignDate(String lastAssignDate) {
        this.lastAssignDate = lastAssignDate;
    }

    /**
     * @return the accessUsrType
     */
    public Integer getAccessUsrType() {
        return accessUsrType;
    }

    /**
     * @param accessUsrType the accessUsrType to set
     */
    public void setAccessUsrType(Integer accessUsrType) {
        this.accessUsrType = accessUsrType;
    }

    /**
     * @return the status
     */
    public String getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getAssignIndex() {
        return assignIndex;
    }

    public void setAssignIndex(Integer assignIndex) {
        this.assignIndex = assignIndex;
    }
}
