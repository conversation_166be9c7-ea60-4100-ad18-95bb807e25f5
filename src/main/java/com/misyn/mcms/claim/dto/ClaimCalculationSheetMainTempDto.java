package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class ClaimCalculationSheetMainTempDto implements Serializable {

    private Integer txnId;
    private Integer claimNo;
    private Integer calsheetId;
    private Integer calsheetType;
    private BigDecimal totalApprovedAcr;
    private BigDecimal reserveAmount;
    private BigDecimal prevReserveAmount;
    private BigDecimal reserveAmountAfterApproved;
    private BigDecimal prevReserveAmountAfterApproved;
    private BigDecimal payableAmount;
    private BigDecimal prevPayableAmount;
    private BigDecimal advanceAmount;
    private BigDecimal prevAdvanceAmount;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getCalsheetId() {
        return calsheetId;
    }

    public void setCalsheetId(Integer calsheetId) {
        this.calsheetId = calsheetId;
    }

    public Integer getCalsheetType() {
        return calsheetType;
    }

    public void setCalsheetType(Integer calsheetType) {
        this.calsheetType = calsheetType;
    }

    public BigDecimal getTotalApprovedAcr() {
        return totalApprovedAcr;
    }

    public void setTotalApprovedAcr(BigDecimal totalApprovedAcr) {
        this.totalApprovedAcr = totalApprovedAcr;
    }

    public BigDecimal getReserveAmount() {
        return reserveAmount;
    }

    public void setReserveAmount(BigDecimal reserveAmount) {
        this.reserveAmount = reserveAmount;
    }

    public BigDecimal getPrevReserveAmount() {
        return prevReserveAmount;
    }

    public void setPrevReserveAmount(BigDecimal prevReserveAmount) {
        this.prevReserveAmount = prevReserveAmount;
    }

    public BigDecimal getReserveAmountAfterApproved() {
        return reserveAmountAfterApproved;
    }

    public void setReserveAmountAfterApproved(BigDecimal reserveAmountAfterApproved) {
        this.reserveAmountAfterApproved = reserveAmountAfterApproved;
    }

    public BigDecimal getPrevReserveAmountAfterApproved() {
        return prevReserveAmountAfterApproved;
    }

    public void setPrevReserveAmountAfterApproved(BigDecimal prevReserveAmountAfterApproved) {
        this.prevReserveAmountAfterApproved = prevReserveAmountAfterApproved;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public BigDecimal getPrevPayableAmount() {
        return prevPayableAmount;
    }

    public void setPrevPayableAmount(BigDecimal prevPayableAmount) {
        this.prevPayableAmount = prevPayableAmount;
    }

    public BigDecimal getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(BigDecimal advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public BigDecimal getPrevAdvanceAmount() {
        return prevAdvanceAmount;
    }

    public void setPrevAdvanceAmount(BigDecimal prevAdvanceAmount) {
        this.prevAdvanceAmount = prevAdvanceAmount;
    }
}
