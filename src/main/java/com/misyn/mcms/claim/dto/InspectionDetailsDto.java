package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.AccidentStatus;
import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.claim.enums.SelectionType;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class InspectionDetailsDto implements Serializable {

    private Integer inspectionId = AppConstant.ZERO_INT;

    private String actionType = "";

    //Inspection Details View Data
    private AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
    private InspectionDto inspectionDto = new InspectionDto();
    private Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = new HashMap<>();

    //Inspection Details Input Data
    private int refNo = AppConstant.ZERO_INT;
    private String jobId = AppConstant.STRING_EMPTY;
    private int claimNo = AppConstant.ZERO_INT;
    private SelectionType makeConfirm = SelectionType.Confirm;
    private SelectionType modelConfirm = SelectionType.Confirm;
    private SelectionType engNoConfirm = SelectionType.Not_Checked;
    private String chassisNo = AppConstant.STRING_EMPTY;
    private SelectionType yearMakeConfirm = SelectionType.Confirm;
    private String inspectDateTime = AppConstant.DEFAULT_DATE_TIME;
    private BigDecimal pav = BigDecimal.ZERO;
    private String damageDetails = AppConstant.STRING_EMPTY;
    private String pad = AppConstant.STRING_EMPTY;
    private AccidentStatus genuineOfAccident = AccidentStatus.Non_Consistence;
    private ConditionType firstStatementReq = ConditionType.No;
    private String firstStatementReqReason = AppConstant.STRING_EMPTY;
    private ConditionType investReq = ConditionType.No;
    private String assessorSpecialRemark = AppConstant.STRING_EMPTY;
    private String inspectionSpecialRemark = AppConstant.STRING_EMPTY;
    private String rteRemarks = AppConstant.STRING_EMPTY;

    //Tyre Condition Data
    private List<TireCondtionDto> tireCondtionDtoList = new ArrayList<>();

    //Assessor Professional Fee
    private Integer jobType = AppConstant.ZERO_INT;
    private Integer assessorFeeDetailId;
    private String assignedLocation = AppConstant.STRING_EMPTY;
    private String currentLocation = AppConstant.STRING_EMPTY;
    private String placeOfInspection = AppConstant.STRING_EMPTY;
    private Integer mileage = AppConstant.ZERO_INT;
    private BigDecimal costOfCall = new BigDecimal(50.00);
    private BigDecimal deduction = BigDecimal.ZERO;
    private BigDecimal otherFee = BigDecimal.ZERO;
    private BigDecimal totalAssessorFee = BigDecimal.ZERO;
    private String feeDesc = AppConstant.STRING_EMPTY;

    //Audit Details
    private int recordStatus = AppConstant.ZERO_INT;
    private String inputUserId = AppConstant.STRING_EMPTY;
    private String inputDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String assessorFeeAuthStatus = AppConstant.STRING_EMPTY;
    private String assessorFeeAuthUserId = AppConstant.STRING_EMPTY;
    private String assessorFeeAuthDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String inspectionDetailsAuthStatus = AppConstant.STRING_EMPTY;
    private String inspectionDetailsAuthUserId = AppConstant.STRING_EMPTY;
    private String inspectionDetailsAuthDatetime = AppConstant.DEFAULT_DATE_TIME;

    private String assignAssessorUser = AppConstant.STRING_EMPTY;
    private String assignAssessorDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String assignRteUser = AppConstant.STRING_EMPTY;
    private String assignRteDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String approveAssignRteUser = AppConstant.STRING_EMPTY;
    private String approverAssignRteDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String fwdRteUser = AppConstant.STRING_EMPTY;
    private String fwdRteDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String fwdRteDesktopUser = AppConstant.STRING_EMPTY;
    private String fwdRteDesktopDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String assignTcUser = AppConstant.STRING_EMPTY;
    private String assignTcDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String fwdTcUser = AppConstant.STRING_EMPTY;
    private String fwdTcDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String fwdTcDesktopUser = AppConstant.STRING_EMPTY;
    private String fwdTcDesktopDatetime = AppConstant.DEFAULT_DATE_TIME;

    //Assessor’s Estimate for Repair Data
    private OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = new OnSiteInspectionDetailsDto();
    private GarageInspectionDetailsDto garageInspectionDetailsDto = new GarageInspectionDetailsDto();
    private ARIInspectionDetailsDto ariInspectionDetailsDto = new ARIInspectionDetailsDto();
    private DrSupplementaryInspectionDetailsDto drSuppInspectionDetailsDto = new DrSupplementaryInspectionDetailsDto();
    private DesktopInspectionDetailsDto desktopInspectionDetailsDto = new DesktopInspectionDetailsDto();


    private List<ClaimDocumentTypeDto> claimDocumentTypeDtoList;
    private List<ClaimLogTrailDto> logList = new ArrayList<>();
    private String recordStatusValue = AppConstant.STRING_EMPTY;
    private SelectionType chassisNoConfirm;
    private Integer notCheckedReason = AppConstant.ZERO_INT;
    private String isVehicleAvailable = AppConstant.YES;
    private String typeOnlineInspection = AppConstant.NO;

    private DesktopReassignReasonDto desktopReassignReasonDto = new DesktopReassignReasonDto();

    public Integer getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(Integer inspectionId) {
        this.inspectionId = inspectionId;
    }

    public AssessorAllocationDto getAssessorAllocationDto() {
        return assessorAllocationDto;
    }

    public void setAssessorAllocationDto(AssessorAllocationDto assessorAllocationDto) {
        this.assessorAllocationDto = assessorAllocationDto;
    }

    public SelectionType getMakeConfirm() {
        return makeConfirm;
    }

    public void setMakeConfirm(SelectionType makeConfirm) {
        this.makeConfirm = makeConfirm;
    }

    public SelectionType getModelConfirm() {
        return modelConfirm;
    }

    public void setModelConfirm(SelectionType modelConfirm) {
        this.modelConfirm = modelConfirm;
    }

    public SelectionType getEngNoConfirm() {
        return engNoConfirm;
    }

    public void setEngNoConfirm(SelectionType engNoConfirm) {
        this.engNoConfirm = engNoConfirm;
    }

    public String getChassisNo() {
        return chassisNo;
    }

    public void setChassisNo(String chassisNo) {
        this.chassisNo = chassisNo;
    }

    public SelectionType getYearMakeConfirm() {
        return yearMakeConfirm;
    }

    public void setYearMakeConfirm(SelectionType yearMakeConfirm) {
        this.yearMakeConfirm = yearMakeConfirm;
    }

    public BigDecimal getPav() {
        return pav;
    }

    public void setPav(BigDecimal pav) {
        this.pav = pav;
    }

    public String getDamageDetails() {
        return damageDetails;
    }

    public void setDamageDetails(String damageDetails) {
        this.damageDetails = damageDetails;
    }

    public String getPad() {
        return pad;
    }

    public void setPad(String pad) {
        this.pad = pad;
    }

    public String getAssessorSpecialRemark() {
        return assessorSpecialRemark;
    }

    public void setAssessorSpecialRemark(String assessorSpecialRemark) {
        this.assessorSpecialRemark = assessorSpecialRemark;
    }

    public InspectionDto getInspectionDto() {
        return inspectionDto;
    }

    public void setInspectionDto(InspectionDto inspectionDto) {
        this.inspectionDto = inspectionDto;
    }

    public Integer getJobType() {
        return jobType;
    }

    public void setJobType(Integer jobType) {
        this.jobType = jobType;
    }

    public String getCurrentLocation() {
        return currentLocation;
    }

    public void setCurrentLocation(String currentLocation) {
        this.currentLocation = currentLocation;
    }

    public String getPlaceOfInspection() {
        return placeOfInspection;
    }

    public void setPlaceOfInspection(String placeOfInspection) {
        this.placeOfInspection = placeOfInspection;
    }

    public Integer getMileage() {
        return mileage;
    }

    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public BigDecimal getCostOfCall() {
        return costOfCall;
    }

    public void setCostOfCall(BigDecimal costOfCall) {
        this.costOfCall = costOfCall;
    }

    public BigDecimal getOtherFee() {
        return otherFee;
    }

    public void setOtherFee(BigDecimal otherFee) {
        this.otherFee = otherFee;
    }

    public BigDecimal getTotalAssessorFee() {
        return totalAssessorFee;
    }

    public void setTotalAssessorFee(BigDecimal totalAssessorFee) {
        this.totalAssessorFee = totalAssessorFee;
    }

    public String getFeeDesc() {
        return feeDesc;
    }

    public void setFeeDesc(String feeDesc) {
        this.feeDesc = feeDesc;
    }

    public int getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(int recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(String inputUserId) {
        this.inputUserId = inputUserId;
    }

    public String getInputDatetime() {
        return inputDatetime;
    }

    public void setInputDatetime(String inputDatetime) {
        this.inputDatetime = inputDatetime;
    }

    public AccidentStatus getGenuineOfAccident() {
        return genuineOfAccident;
    }

    public void setGenuineOfAccident(AccidentStatus genuineOfAccident) {
        this.genuineOfAccident = genuineOfAccident;
    }

    public ConditionType getFirstStatementReq() {
        return firstStatementReq;
    }

    public void setFirstStatementReq(ConditionType firstStatementReq) {
        this.firstStatementReq = firstStatementReq;
    }

    public ConditionType getInvestReq() {
        return investReq;
    }

    public void setInvestReq(ConditionType investReq) {
        this.investReq = investReq;
    }

    public List<TireCondtionDto> getTireCondtionDtoList() {
        return tireCondtionDtoList;
    }

    public void setTireCondtionDtoList(List<TireCondtionDto> tireCondtionDtoList) {
        this.tireCondtionDtoList = tireCondtionDtoList;
    }

    public OnSiteInspectionDetailsDto getOnSiteInspectionDetailsDto() {
        return onSiteInspectionDetailsDto;
    }

    public void setOnSiteInspectionDetailsDto(OnSiteInspectionDetailsDto onSiteInspectionDetailsDto) {
        this.onSiteInspectionDetailsDto = onSiteInspectionDetailsDto;
    }

    public GarageInspectionDetailsDto getGarageInspectionDetailsDto() {
        return garageInspectionDetailsDto;
    }

    public void setGarageInspectionDetailsDto(GarageInspectionDetailsDto garageInspectionDetailsDto) {
        this.garageInspectionDetailsDto = garageInspectionDetailsDto;
    }

    public ARIInspectionDetailsDto getAriInspectionDetailsDto() {
        return ariInspectionDetailsDto;
    }

    public void setAriInspectionDetailsDto(ARIInspectionDetailsDto ariInspectionDetailsDto) {
        this.ariInspectionDetailsDto = ariInspectionDetailsDto;
    }

    public DrSupplementaryInspectionDetailsDto getDrSuppInspectionDetailsDto() {
        return drSuppInspectionDetailsDto;
    }

    public void setDrSuppInspectionDetailsDto(DrSupplementaryInspectionDetailsDto drSuppInspectionDetailsDto) {
        this.drSuppInspectionDetailsDto = drSuppInspectionDetailsDto;
    }

    public DesktopInspectionDetailsDto getDesktopInspectionDetailsDto() {
        return desktopInspectionDetailsDto;
    }

    public void setDesktopInspectionDetailsDto(DesktopInspectionDetailsDto desktopInspectionDetailsDto) {
        this.desktopInspectionDetailsDto = desktopInspectionDetailsDto;
    }

    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public int getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(int claimNo) {
        this.claimNo = claimNo;
    }

    public String getInspectionSpecialRemark() {
        return inspectionSpecialRemark;
    }

    public void setInspectionSpecialRemark(String inspectionSpecialRemark) {
        this.inspectionSpecialRemark = inspectionSpecialRemark;
    }

    public Map<String, ClaimThirdPartyDetailsGenericDto> getThirdPartyAssessorMap() {
        return thirdPartyAssessorMap;
    }

    public void setThirdPartyAssessorMap(Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap) {
        this.thirdPartyAssessorMap = thirdPartyAssessorMap;
    }

    public List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList() {
        return claimDocumentTypeDtoList;
    }

    public void setClaimDocumentTypeDtoList(List<ClaimDocumentTypeDto> claimDocumentTypeDtoList) {
        this.claimDocumentTypeDtoList = claimDocumentTypeDtoList;
    }

    public List<ClaimLogTrailDto> getLogList() {
        return logList;
    }

    public void setLogList(List<ClaimLogTrailDto> logList) {
        this.logList = logList;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getAssessorFeeAuthStatus() {
        return assessorFeeAuthStatus;
    }

    public void setAssessorFeeAuthStatus(String assessorFeeAuthStatus) {
        this.assessorFeeAuthStatus = assessorFeeAuthStatus;
    }

    public String getAssessorFeeAuthUserId() {
        return assessorFeeAuthUserId;
    }

    public void setAssessorFeeAuthUserId(String assessorFeeAuthUserId) {
        this.assessorFeeAuthUserId = assessorFeeAuthUserId;
    }

    public String getAssessorFeeAuthDatetime() {
        return assessorFeeAuthDatetime;
    }

    public void setAssessorFeeAuthDatetime(String assessorFeeAuthDatetime) {
        this.assessorFeeAuthDatetime = assessorFeeAuthDatetime;
    }

    public String getInspectionDetailsAuthStatus() {
        return inspectionDetailsAuthStatus;
    }

    public void setInspectionDetailsAuthStatus(String inspectionDetailsAuthStatus) {
        this.inspectionDetailsAuthStatus = inspectionDetailsAuthStatus;
    }

    public String getInspectionDetailsAuthUserId() {
        return inspectionDetailsAuthUserId;
    }

    public void setInspectionDetailsAuthUserId(String inspectionDetailsAuthUserId) {
        this.inspectionDetailsAuthUserId = inspectionDetailsAuthUserId;
    }

    public String getInspectionDetailsAuthDatetime() {
        return inspectionDetailsAuthDatetime;
    }

    public void setInspectionDetailsAuthDatetime(String inspectionDetailsAuthDatetime) {
        this.inspectionDetailsAuthDatetime = inspectionDetailsAuthDatetime;
    }

    public String getAssignAssessorUser() {
        return assignAssessorUser;
    }

    public void setAssignAssessorUser(String assignAssessorUser) {
        this.assignAssessorUser = assignAssessorUser;
    }

    public String getAssignAssessorDatetime() {
        return assignAssessorDatetime;
    }

    public void setAssignAssessorDatetime(String assignAssessorDatetime) {
        this.assignAssessorDatetime = assignAssessorDatetime;
    }

    public String getAssignRteUser() {
        return assignRteUser;
    }

    public void setAssignRteUser(String assignRteUser) {
        this.assignRteUser = assignRteUser;
    }

    public String getAssignRteDatetime() {
        return assignRteDatetime;
    }

    public void setAssignRteDatetime(String assignRteDatetime) {
        this.assignRteDatetime = assignRteDatetime;
    }

    public String getFwdRteUser() {
        return fwdRteUser;
    }

    public void setFwdRteUser(String fwdRteUser) {
        this.fwdRteUser = fwdRteUser;
    }

    public String getFwdRteDatetime() {
        return fwdRteDatetime;
    }

    public void setFwdRteDatetime(String fwdRteDatetime) {
        this.fwdRteDatetime = fwdRteDatetime;
    }

    public String getFwdRteDesktopUser() {
        return fwdRteDesktopUser;
    }

    public void setFwdRteDesktopUser(String fwdRteDesktopUser) {
        this.fwdRteDesktopUser = fwdRteDesktopUser;
    }

    public String getFwdRteDesktopDatetime() {
        return fwdRteDesktopDatetime;
    }

    public void setFwdRteDesktopDatetime(String fwdRteDesktopDatetime) {
        this.fwdRteDesktopDatetime = fwdRteDesktopDatetime;
    }

    public String getAssignTcUser() {
        return assignTcUser;
    }

    public void setAssignTcUser(String assignTcUser) {
        this.assignTcUser = assignTcUser;
    }

    public String getAssignTcDatetime() {
        return assignTcDatetime;
    }

    public void setAssignTcDatetime(String assignTcDatetime) {
        this.assignTcDatetime = assignTcDatetime;
    }

    public String getFwdTcUser() {
        return fwdTcUser;
    }

    public void setFwdTcUser(String fwdTcUser) {
        this.fwdTcUser = fwdTcUser;
    }

    public String getFwdTcDatetime() {
        return fwdTcDatetime;
    }

    public void setFwdTcDatetime(String fwdTcDatetime) {
        this.fwdTcDatetime = fwdTcDatetime;
    }

    public String getFwdTcDesktopUser() {
        return fwdTcDesktopUser;
    }

    public void setFwdTcDesktopUser(String fwdTcDesktopUser) {
        this.fwdTcDesktopUser = fwdTcDesktopUser;
    }

    public String getFwdTcDesktopDatetime() {
        return fwdTcDesktopDatetime;
    }

    public void setFwdTcDesktopDatetime(String fwdTcDesktopDatetime) {
        this.fwdTcDesktopDatetime = fwdTcDesktopDatetime;
    }

    public String getInspectDateTime() {
        return inspectDateTime;
    }

    public void setInspectDateTime(String inspectDateTime) {
        this.inspectDateTime = inspectDateTime;
    }

    public String getFirstStatementReqReason() {
        return firstStatementReqReason;
    }

    public void setFirstStatementReqReason(String firstStatementReqReason) {
        this.firstStatementReqReason = firstStatementReqReason;
    }

    public String getAssignedLocation() {
        return assignedLocation;
    }

    public void setAssignedLocation(String assignedLocation) {
        this.assignedLocation = assignedLocation;
    }

    public Integer getAssessorFeeDetailId() {
        return assessorFeeDetailId;
    }

    public void setAssessorFeeDetailId(Integer assessorFeeDetailId) {
        this.assessorFeeDetailId = assessorFeeDetailId;
    }

    public String getRteRemarks() {
        return rteRemarks;
    }

    public void setRteRemarks(String rteRemarks) {
        this.rteRemarks = rteRemarks;
    }

    public String getRecordStatusValue() {
        return recordStatusValue;
    }

    public void setRecordStatusValue(String recordStatusValue) {
        this.recordStatusValue = recordStatusValue;
    }

    public SelectionType getChassisNoConfirm() {
        return chassisNoConfirm;
    }

    public void setChassisNoConfirm(SelectionType chassisNoConfirm) {
        this.chassisNoConfirm = chassisNoConfirm;
    }

    public Integer getNotCheckedReason() {
        return notCheckedReason;
    }

    public void setNotCheckedReason(Integer notCheckedReason) {
        this.notCheckedReason = notCheckedReason;
    }

    public String getIsVehicleAvailable() {
        return isVehicleAvailable;
    }

    public void setIsVehicleAvailable(String isVehicleAvailable) {
        this.isVehicleAvailable = isVehicleAvailable;
    }

    public BigDecimal getDeduction() {
        return deduction;
    }

    public void setDeduction(BigDecimal deduction) {
        this.deduction = deduction;
    }

    public String getApproveAssignRteUser() {
        return approveAssignRteUser;
    }

    public void setApproveAssignRteUser(String approveAssignRteUser) {
        this.approveAssignRteUser = approveAssignRteUser;
    }

    public String getApproverAssignRteDateTime() {
        return approverAssignRteDateTime;
    }

    public void setApproverAssignRteDateTime(String approverAssignRteDateTime) {
        this.approverAssignRteDateTime = approverAssignRteDateTime;
    }

    public DesktopReassignReasonDto getDesktopReassignReasonDto() {
        return desktopReassignReasonDto;
    }

    public void setDesktopReassignReasonDto(DesktopReassignReasonDto desktopReassignReasonDto) {
        this.desktopReassignReasonDto = desktopReassignReasonDto;
    }

    public String getTypeOnlineInspection() {
        return typeOnlineInspection;
    }

    public void setTypeOnlineInspection(String typeOnlineInspection) {
        this.typeOnlineInspection = typeOnlineInspection;
    }

}
