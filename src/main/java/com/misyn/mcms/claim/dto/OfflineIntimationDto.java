package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class OfflineIntimationDto implements Serializable {
    private Long intimationId;
    private String ovPolicyNo;
    private Integer onRenCount;
    private Integer onEndCount;
    private String ovRiskNo;
    private String ovIntNo;
    private String odIntimation;
    private String ovIntimationTime;
    private String ovIntimatedSource;
    private String ovIntimatedMeans;
    private String ovIntType;
    private String ovIntimatorName;
    private String odLoss;
    private String odLossTime;
    private String odTravelStartDt;
    private Integer onNoOfDays;
    private String ovIdenNoDriver;
    private String ovIdenCodeDriver;
    private String ovCauseOfLoss;
    private String ovDescOfLoss;
    private String ovLossCode;
    private String ovReportType;
    private String ovPanelCategory;
    private String ovSurvIndComp;
    private String ovSurvIdenCode;
    private String odAppointment;
    private String odAssessment;
    private String odSubmit;
    private String ovPoliceStation;
    private String odReportDate;
    private String odServiceRequestDate;
    private String ovUserId;
    private String ovDriverName;
    private String insertDateTime;
    private String isfsUpdateStat = AppConstant.NO;
    private String isfsUpdateDateTime;
    private String policyChannelType;

    public Long getIntimationId() {
        return intimationId;
    }

    public void setIntimationId(Long intimationId) {
        this.intimationId = intimationId;
    }

    public String getOvPolicyNo() {
        return ovPolicyNo;
    }

    public void setOvPolicyNo(String ovPolicyNo) {
        this.ovPolicyNo = ovPolicyNo;
    }

    public Integer getOnRenCount() {
        return onRenCount;
    }

    public void setOnRenCount(Integer onRenCount) {
        this.onRenCount = onRenCount;
    }

    public Integer getOnEndCount() {
        return onEndCount;
    }

    public void setOnEndCount(Integer onEndCount) {
        this.onEndCount = onEndCount;
    }

    public String getOvRiskNo() {
        return ovRiskNo;
    }

    public void setOvRiskNo(String ovRiskNo) {
        this.ovRiskNo = ovRiskNo;
    }

    public String getOvIntNo() {
        return ovIntNo;
    }

    public void setOvIntNo(String ovIntNo) {
        this.ovIntNo = ovIntNo;
    }

    public String getOdIntimation() {
        return odIntimation;
    }

    public void setOdIntimation(String odIntimation) {
        this.odIntimation = odIntimation;
    }

    public String getOvIntimationTime() {
        return ovIntimationTime;
    }

    public void setOvIntimationTime(String ovIntimationTime) {
        this.ovIntimationTime = ovIntimationTime;
    }

    public String getOvIntimatedSource() {
        return ovIntimatedSource;
    }

    public void setOvIntimatedSource(String ovIntimatedSource) {
        this.ovIntimatedSource = ovIntimatedSource;
    }

    public String getOvIntimatedMeans() {
        return ovIntimatedMeans;
    }

    public void setOvIntimatedMeans(String ovIntimatedMeans) {
        this.ovIntimatedMeans = ovIntimatedMeans;
    }

    public String getOvIntType() {
        return ovIntType;
    }

    public void setOvIntType(String ovIntType) {
        this.ovIntType = ovIntType;
    }

    public String getOvIntimatorName() {
        return ovIntimatorName;
    }

    public void setOvIntimatorName(String ovIntimatorName) {
        this.ovIntimatorName = ovIntimatorName;
    }

    public String getOdLoss() {
        return odLoss;
    }

    public void setOdLoss(String odLoss) {
        this.odLoss = odLoss;
    }

    public String getOdLossTime() {
        return odLossTime;
    }

    public void setOdLossTime(String odLossTime) {
        this.odLossTime = odLossTime;
    }

    public String getOdTravelStartDt() {
        return odTravelStartDt;
    }

    public void setOdTravelStartDt(String odTravelStartDt) {
        this.odTravelStartDt = odTravelStartDt;
    }

    public Integer getOnNoOfDays() {
        return onNoOfDays;
    }

    public void setOnNoOfDays(Integer onNoOfDays) {
        this.onNoOfDays = onNoOfDays;
    }

    public String getOvIdenNoDriver() {
        return ovIdenNoDriver;
    }

    public void setOvIdenNoDriver(String ovIdenNoDriver) {
        this.ovIdenNoDriver = ovIdenNoDriver;
    }

    public String getOvIdenCodeDriver() {
        return ovIdenCodeDriver;
    }

    public void setOvIdenCodeDriver(String ovIdenCodeDriver) {
        this.ovIdenCodeDriver = ovIdenCodeDriver;
    }

    public String getOvCauseOfLoss() {
        return ovCauseOfLoss;
    }

    public void setOvCauseOfLoss(String ovCauseOfLoss) {
        this.ovCauseOfLoss = ovCauseOfLoss;
    }

    public String getOvDescOfLoss() {
        return ovDescOfLoss;
    }

    public void setOvDescOfLoss(String ovDescOfLoss) {
        this.ovDescOfLoss = ovDescOfLoss;
    }

    public String getOvLossCode() {
        return ovLossCode;
    }

    public void setOvLossCode(String ovLossCode) {
        this.ovLossCode = ovLossCode;
    }

    public String getOvReportType() {
        return ovReportType;
    }

    public void setOvReportType(String ovReportType) {
        this.ovReportType = ovReportType;
    }

    public String getOvPanelCategory() {
        return ovPanelCategory;
    }

    public void setOvPanelCategory(String ovPanelCategory) {
        this.ovPanelCategory = ovPanelCategory;
    }

    public String getOvSurvIndComp() {
        return ovSurvIndComp;
    }

    public void setOvSurvIndComp(String ovSurvIndComp) {
        this.ovSurvIndComp = ovSurvIndComp;
    }

    public String getOvSurvIdenCode() {
        return ovSurvIdenCode;
    }

    public void setOvSurvIdenCode(String ovSurvIdenCode) {
        this.ovSurvIdenCode = ovSurvIdenCode;
    }

    public String getOdAppointment() {
        return odAppointment;
    }

    public void setOdAppointment(String odAppointment) {
        this.odAppointment = odAppointment;
    }

    public String getOdAssessment() {
        return odAssessment;
    }

    public void setOdAssessment(String odAssessment) {
        this.odAssessment = odAssessment;
    }

    public String getOdSubmit() {
        return odSubmit;
    }

    public void setOdSubmit(String odSubmit) {
        this.odSubmit = odSubmit;
    }

    public String getOvPoliceStation() {
        return ovPoliceStation;
    }

    public void setOvPoliceStation(String ovPoliceStation) {
        this.ovPoliceStation = ovPoliceStation;
    }

    public String getOdReportDate() {
        return odReportDate;
    }

    public void setOdReportDate(String odReportDate) {
        this.odReportDate = odReportDate;
    }

    public String getOdServiceRequestDate() {
        return odServiceRequestDate;
    }

    public void setOdServiceRequestDate(String odServiceRequestDate) {
        this.odServiceRequestDate = odServiceRequestDate;
    }

    public String getOvUserId() {
        return ovUserId;
    }

    public void setOvUserId(String ovUserId) {
        this.ovUserId = ovUserId;
    }

    public String getOvDriverName() {
        return ovDriverName;
    }

    public void setOvDriverName(String ovDriverName) {
        this.ovDriverName = ovDriverName;
    }

    public String getInsertDateTime() {
        return insertDateTime;
    }

    public void setInsertDateTime(String insertDateTime) {
        this.insertDateTime = insertDateTime;
    }

    public String getIsfsUpdateStat() {
        return isfsUpdateStat;
    }

    public void setIsfsUpdateStat(String isfsUpdateStat) {
        this.isfsUpdateStat = isfsUpdateStat;
    }

    public String getIsfsUpdateDateTime() {
        return isfsUpdateDateTime;
    }

    public void setIsfsUpdateDateTime(String isfsUpdateDateTime) {
        this.isfsUpdateDateTime = isfsUpdateDateTime;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }
}
