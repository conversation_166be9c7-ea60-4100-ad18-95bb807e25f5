package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class SparePartDatabaseDto implements Serializable {
    private Integer txnId;
    private String vehicleMake;
    private String vehicleModel;
    private Integer manufactureYear;
    private Integer sparePartRefNo;
    private BigDecimal price;
    private String proceedDate = AppConstant.DEFAULT_DATE;
    private String inputDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String inputUserId;
    private Integer supplierId;
    private Integer index;
    private SupplierDetailsMasterDto supplierDetailsMasterDto = new SupplierDetailsMasterDto();
    private String sparePartName;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public String getVehicleMake() {
        return vehicleMake;
    }

    public void setVehicleMake(String vehicleMake) {
        this.vehicleMake = vehicleMake;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public Integer getManufactureYear() {
        return manufactureYear;
    }

    public void setManufactureYear(Integer manufactureYear) {
        this.manufactureYear = manufactureYear;
    }

    public Integer getSparePartRefNo() {
        return sparePartRefNo;
    }

    public void setSparePartRefNo(Integer sparePartRefNo) {
        this.sparePartRefNo = sparePartRefNo;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getProceedDate() {
        return proceedDate;
    }

    public void setProceedDate(String proceedDate) {
        this.proceedDate = proceedDate;
    }

    public String getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(String inputDateTime) {
        this.inputDateTime = inputDateTime;
    }

    public String getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(String inputUserId) {
        this.inputUserId = inputUserId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public SupplierDetailsMasterDto getSupplierDetailsMasterDto() {
        return supplierDetailsMasterDto;
    }

    public void setSupplierDetailsMasterDto(SupplierDetailsMasterDto supplierDetailsMasterDto) {
        this.supplierDetailsMasterDto = supplierDetailsMasterDto;
    }

    public String getSparePartName() {
        return sparePartName;
    }

    public void setSparePartName(String sparePartName) {
        this.sparePartName = sparePartName;
    }
}
