package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class ClaimHandlerGridDto implements Serializable {
    private Integer index = AppConstant.ZERO_INT;
    private String type = AppConstant.STRING_EMPTY;
    private Integer claimNo = AppConstant.ZERO_INT;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private Integer policyRefNo = AppConstant.ZERO_INT;
    private Integer inspectionStatus = AppConstant.ZERO_INT;
    private String accidentDate = AppConstant.DEFAULT_DATE_TIME;
    private String assignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String documentStatus = AppConstant.STRING_EMPTY;
    private String documentCheck = AppConstant.STRING_EMPTY;
    private String liabilityStatus = AppConstant.STRING_EMPTY;
    private String finalizeStatus = AppConstant.STRING_EMPTY;
    private String acr = AppConstant.ZERO;
    private String presentReverseAmount = AppConstant.ZERO;
    private String policyNumberValue = AppConstant.ZERO;
    private Integer txnId = AppConstant.ZERO_INT;
    private Integer claimStatus = AppConstant.ZERO_INT;
    private String claimStatusDesc = AppConstant.STRING_EMPTY;
    private String fileStore = AppConstant.STRING_EMPTY;
    private Integer partialLoss = AppConstant.ZERO_INT;
    private String assignUser = AppConstant.STRING_EMPTY;
    private String liabilityAssignUser = AppConstant.STRING_EMPTY;
    private String liabilityAssignDatetime = AppConstant.STRING_EMPTY;
    private String intLiabilityAssignUser = AppConstant.STRING_EMPTY;
    private String intLiabilityAssignDatetime = AppConstant.STRING_EMPTY;
    private String investigationStatus = AppConstant.STRING_EMPTY;
    private String calSheetInpUserId = AppConstant.STRING_EMPTY;
    private String sparePartCoordinatorId = AppConstant.STRING_EMPTY;
    private String scrutinizingTeamAssignUserId = AppConstant.STRING_EMPTY;
    private String paymentApproveUserId = AppConstant.STRING_EMPTY;
    private Integer calSheetNo;
    private String mofaTeamUserId = AppConstant.STRING_EMPTY;
    private String specialTeamUserId = AppConstant.STRING_EMPTY;
    private String isDoubt = AppConstant.NO;
    private String isOnSiteOffer = AppConstant.NO;
    private BigDecimal aprvAdvanceAmount = BigDecimal.ZERO;
    private String reOpenType = AppConstant.NO;
    private String closeStatus = AppConstant.CLOSE_STATUS_PENDING;
    private String closeUser = AppConstant.STRING_EMPTY;
    private String closeDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String calSheetStatus = AppConstant.STRING_EMPTY;
    private String noObjection = AppConstant.STRING_EMPTY;
    private String noObjectionLetterAttached = AppConstant.STRING_EMPTY;
    private String isNoObjectionUpload = AppConstant.STRING_EMPTY;
    private Integer noObjectionDocRefNo;
    private String premiumOutstanding = AppConstant.STRING_EMPTY;
    private String premiumOutstandingLetterAttached = AppConstant.STRING_EMPTY;
    private String isPremiumOutstandingUpload = AppConstant.STRING_EMPTY;
    private Integer premiumOutstandingDocRefNo;
    private String scrutinizingTeamAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String supplierStatus = AppConstant.STRING_EMPTY;
    private String supplierOrderAssignDateTime = AppConstant.DEFAULT_DATE_TIME;
    private boolean pendingInspectionFound;
    private String policyChannelType;
    private String priority;
    private String isfClaimNo;
    private String isRejectionAttached = AppConstant.NO;
    private String rejectedReason = AppConstant.EMPTY_STRING;


    public String getIsfClaimNo() {
        return isfClaimNo;
    }

    public void setIsfClaimNo(String isfClaimNo) {
        this.isfClaimNo = isfClaimNo;
    }

    public String getdMakingAssignUid() {
        return dMakingAssignUid;
    }

    public void setdMakingAssignUid(String dMakingAssignUid) {
        this.dMakingAssignUid = dMakingAssignUid;
    }

    public Integer getRepudiatedLetterType() {
        return repudiatedLetterType;
    }

    public void setRepudiatedLetterType(Integer repudiatedLetterType) {
        this.repudiatedLetterType = repudiatedLetterType;
    }

    private String dMakingAssignUid;
    private Integer repudiatedLetterType;

    private String rteAssignUserId = AppConstant.STRING_EMPTY;
    private String rteAssignDateTime = AppConstant.DEFAULT_DATE_TIME;

    public boolean isPendingInspectionFound() {
        return pendingInspectionFound;
    }

    public void setPendingInspectionFound(boolean pendingInspectionFound) {
        this.pendingInspectionFound = pendingInspectionFound;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }


    public Integer getInspectionStatus() {
        return inspectionStatus;
    }

    public void setInspectionStatus(Integer inspectionStatus) {
        this.inspectionStatus = inspectionStatus;
    }

    public String getAssignDateTime() {
        return assignDateTime;
    }

    public void setAssignDateTime(String assignDateTime) {
        this.assignDateTime = assignDateTime;
    }

    public String getDocumentStatus() {
        return documentStatus;
    }

    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    public String getDocumentCheck() {
        return documentCheck;
    }

    public void setDocumentCheck(String documentCheck) {
        this.documentCheck = documentCheck;
    }

    public String getLiabilityStatus() {
        return liabilityStatus;
    }

    public void setLiabilityStatus(String liabilityStatus) {
        this.liabilityStatus = liabilityStatus;
    }

    public String getFinalizeStatus() {
        return finalizeStatus;
    }

    public void setFinalizeStatus(String finalizeStatus) {
        this.finalizeStatus = finalizeStatus;
    }


    public Integer getPolicyRefNo() {
        return policyRefNo;
    }

    public void setPolicyRefNo(Integer policyRefNo) {
        this.policyRefNo = policyRefNo;
    }

    public String getPolicyNumberValue() {
        return policyNumberValue;
    }

    public void setPolicyNumberValue(String policyNumberValue) {
        this.policyNumberValue = policyNumberValue;
    }

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(Integer claimStatus) {
        this.claimStatus = claimStatus;
    }

    public String getClaimStatusDesc() {
        return claimStatusDesc;
    }

    public void setClaimStatusDesc(String claimStatusDesc) {
        this.claimStatusDesc = claimStatusDesc;
    }

    public String getAcr() {
        return acr;
    }

    public void setAcr(String acr) {
        this.acr = acr;
    }

    public String getPresentReverseAmount() {
        return presentReverseAmount;
    }

    public void setPresentReverseAmount(String presentReverseAmount) {
        this.presentReverseAmount = presentReverseAmount;
    }

    public String getFileStore() {
        return fileStore;
    }

    public void setFileStore(String fileStore) {
        this.fileStore = fileStore;
    }

    public Integer getPartialLoss() {
        return partialLoss;
    }

    public void setPartialLoss(Integer partialLoss) {
        this.partialLoss = partialLoss;
    }

    public String getLiabilityAssignUser() {
        return liabilityAssignUser;
    }

    public void setLiabilityAssignUser(String liabilityAssignUser) {
        this.liabilityAssignUser = liabilityAssignUser;
    }

    public String getLiabilityAssignDatetime() {
        return liabilityAssignDatetime;
    }

    public void setLiabilityAssignDatetime(String liabilityAssignDatetime) {
        this.liabilityAssignDatetime = liabilityAssignDatetime;
    }

    public String getIntLiabilityAssignUser() {
        return intLiabilityAssignUser;
    }

    public void setIntLiabilityAssignUser(String intLiabilityAssignUser) {
        this.intLiabilityAssignUser = intLiabilityAssignUser;
    }

    public String getIntLiabilityAssignDatetime() {
        return intLiabilityAssignDatetime;
    }

    public void setIntLiabilityAssignDatetime(String intLiabilityAssignDatetime) {
        this.intLiabilityAssignDatetime = intLiabilityAssignDatetime;
    }

    public String getInvestigationStatus() {
        return investigationStatus;
    }

    public void setInvestigationStatus(String investigationStatus) {
        this.investigationStatus = investigationStatus;
    }

    public String getCalSheetInpUserId() {
        return calSheetInpUserId;
    }

    public void setCalSheetInpUserId(String calSheetInpUserId) {
        this.calSheetInpUserId = calSheetInpUserId;
    }

    public String getSparePartCoordinatorId() {
        return sparePartCoordinatorId;
    }

    public void setSparePartCoordinatorId(String sparePartCoordinatorId) {
        this.sparePartCoordinatorId = sparePartCoordinatorId;
    }

    public String getScrutinizingTeamAssignUserId() {
        return scrutinizingTeamAssignUserId;
    }

    public void setScrutinizingTeamAssignUserId(String scrutinizingTeamAssignUserId) {
        this.scrutinizingTeamAssignUserId = scrutinizingTeamAssignUserId;
    }

    public String getPaymentApproveUserId() {
        return paymentApproveUserId;
    }

    public void setPaymentApproveUserId(String paymentApproveUserId) {
        this.paymentApproveUserId = paymentApproveUserId;
    }

    public Integer getCalSheetNo() {
        return calSheetNo;
    }

    public void setCalSheetNo(Integer calSheetNo) {
        this.calSheetNo = calSheetNo;
    }

    public String getMofaTeamUserId() {
        return mofaTeamUserId;
    }

    public void setMofaTeamUserId(String mofaTeamUserId) {
        this.mofaTeamUserId = mofaTeamUserId;
    }

    public String getSpecialTeamUserId() {
        return specialTeamUserId;
    }

    public void setSpecialTeamUserId(String specialTeamUserId) {
        this.specialTeamUserId = specialTeamUserId;
    }

    public String getIsDoubt() {
        return isDoubt;
    }

    public void setIsDoubt(String isDoubt) {
        this.isDoubt = isDoubt;
    }

    public String getIsOnSiteOffer() {
        return isOnSiteOffer;
    }

    public void setIsOnSiteOffer(String isOnSiteOffer) {
        this.isOnSiteOffer = isOnSiteOffer;
    }

    public BigDecimal getAprvAdvanceAmount() {
        return aprvAdvanceAmount;
    }

    public void setAprvAdvanceAmount(BigDecimal aprvAdvanceAmount) {
        this.aprvAdvanceAmount = aprvAdvanceAmount;
    }

    public String getReOpenType() {
        return reOpenType;
    }

    public void setReOpenType(String reOpenType) {
        this.reOpenType = reOpenType;
    }

    public String getCloseStatus() {
        return closeStatus;
    }

    public void setCloseStatus(String closeStatus) {
        this.closeStatus = closeStatus;
    }

    public String getCloseUser() {
        return closeUser;
    }

    public void setCloseUser(String closeUser) {
        this.closeUser = closeUser;
    }

    public String getCloseDateTime() {
        return closeDateTime;
    }

    public void setCloseDateTime(String closeDateTime) {
        this.closeDateTime = closeDateTime;
    }

    public String getCalSheetStatus() {
        return calSheetStatus;
    }

    public void setCalSheetStatus(String calSheetStatus) {
        this.calSheetStatus = calSheetStatus;
    }

    public String getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(String accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getAssignUser() {
        return assignUser;
    }

    public void setAssignUser(String assignUser) {
        this.assignUser = assignUser;
    }

    public String getNoObjection() {
        return noObjection;
    }

    public void setNoObjection(String noObjection) {
        this.noObjection = noObjection;
    }

    public String getPremiumOutstanding() {
        return premiumOutstanding;
    }

    public void setPremiumOutstanding(String premiumOutstanding) {
        this.premiumOutstanding = premiumOutstanding;
    }

    public String getScrutinizingTeamAssignDateTime() {
        return scrutinizingTeamAssignDateTime;
    }

    public void setScrutinizingTeamAssignDateTime(String scrutinizingTeamAssignDateTime) {
        this.scrutinizingTeamAssignDateTime = scrutinizingTeamAssignDateTime;
    }

    public String getSupplierStatus() {
        return supplierStatus;
    }

    public void setSupplierStatus(String supplierStatus) {
        this.supplierStatus = supplierStatus;
    }

    public String getSupplierOrderAssignDateTime() {
        return supplierOrderAssignDateTime;
    }

    public void setSupplierOrderAssignDateTime(String supplierOrderAssignDateTime) {
        this.supplierOrderAssignDateTime = supplierOrderAssignDateTime;
    }

    public String getNoObjectionLetterAttached() {
        return noObjectionLetterAttached;
    }

    public void setNoObjectionLetterAttached(String noObjectionLetterAttached) {
        this.noObjectionLetterAttached = noObjectionLetterAttached;
    }

    public String getPremiumOutstandingLetterAttached() {
        return premiumOutstandingLetterAttached;
    }

    public void setPremiumOutstandingLetterAttached(String premiumOutstandingLetterAttached) {
        this.premiumOutstandingLetterAttached = premiumOutstandingLetterAttached;
    }

    public String getIsNoObjectionUpload() {
        return isNoObjectionUpload;
    }

    public void setIsNoObjectionUpload(String isNoObjectionUpload) {
        this.isNoObjectionUpload = isNoObjectionUpload;
    }

    public Integer getNoObjectionDocRefNo() {
        return noObjectionDocRefNo;
    }

    public void setNoObjectionDocRefNo(Integer noObjectionDocRefNo) {
        this.noObjectionDocRefNo = noObjectionDocRefNo;
    }

    public String getIsPremiumOutstandingUpload() {
        return isPremiumOutstandingUpload;
    }

    public void setIsPremiumOutstandingUpload(String isPremiumOutstandingUpload) {
        this.isPremiumOutstandingUpload = isPremiumOutstandingUpload;
    }

    public Integer getPremiumOutstandingDocRefNo() {
        return premiumOutstandingDocRefNo;
    }

    public void setPremiumOutstandingDocRefNo(Integer premiumOutstandingDocRefNo) {
        this.premiumOutstandingDocRefNo = premiumOutstandingDocRefNo;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getRteAssignUserId() {
        return rteAssignUserId;
    }

    public void setRteAssignUserId(String rteAssignUserId) {
        this.rteAssignUserId = rteAssignUserId;
    }

    public String getRteAssignDateTime() {
        return rteAssignDateTime;
    }

    public void setRteAssignDateTime(String rteAssignDateTime) {
        this.rteAssignDateTime = rteAssignDateTime;
    }

    public String getIsRejectionAttached() {
        return isRejectionAttached;
    }

    public void setIsRejectionAttached(String isRejectionAttached) {
        this.isRejectionAttached = isRejectionAttached;
    }

    public String getRejectedReason() {
        return rejectedReason;
    }

    public void setRejectedReason(String rejectedReason) {
        this.rejectedReason = rejectedReason;
    }
}
