package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
public class CalculationSheetFormDto implements Serializable {
    private BigDecimal approvedTotalAmount;
    private BigDecimal nbtTotalAmount;
    private BigDecimal vatTotalAmount;
    private BigDecimal oaTotalAmount;
    private BigDecimal totalAmount;
    private BigDecimal totalAmountWithoutVat;
    private BigDecimal totalAmountAfterOa;
    private BigDecimal totalAmountWithNbt;
    private List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = new ArrayList<>();
    private List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos = new ArrayList<>();

    public BigDecimal getApprovedTotalAmount() {
        return approvedTotalAmount;
    }

    public void setApprovedTotalAmount(BigDecimal approvedTotalAmount) {
        this.approvedTotalAmount = approvedTotalAmount;
    }

    public BigDecimal getNbtTotalAmount() {
        return nbtTotalAmount;
    }

    public void setNbtTotalAmount(BigDecimal nbtTotalAmount) {
        this.nbtTotalAmount = nbtTotalAmount;
    }

    public BigDecimal getVatTotalAmount() {
        return vatTotalAmount;
    }

    public void setVatTotalAmount(BigDecimal vatTotalAmount) {
        this.vatTotalAmount = vatTotalAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<ClaimCalculationSheetDetailDto> getClaimCalculationSheetDetailReplacementDtos() {
        return claimCalculationSheetDetailReplacementDtos;
    }

    public void setClaimCalculationSheetDetailReplacementDtos(List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos) {
        this.claimCalculationSheetDetailReplacementDtos = claimCalculationSheetDetailReplacementDtos;
    }

    public List<ClaimCalculationSheetDetailDto> getClaimCalculationSheetDetailLabourDtos() {
        return claimCalculationSheetDetailLabourDtos;
    }

    public void setClaimCalculationSheetDetailLabourDtos(List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailLabourDtos) {
        this.claimCalculationSheetDetailLabourDtos = claimCalculationSheetDetailLabourDtos;
    }

    public BigDecimal getOaTotalAmount() {
        return oaTotalAmount;
    }

    public void setOaTotalAmount(BigDecimal oaTotalAmount) {
        this.oaTotalAmount = oaTotalAmount;
    }

    public BigDecimal getTotalAmountWithoutVat() {
        return totalAmountWithoutVat;
    }

    public void setTotalAmountWithoutVat(BigDecimal totalAmountWithoutVat) {
        this.totalAmountWithoutVat = totalAmountWithoutVat;
    }

    public BigDecimal getTotalAmountAfterOa() {
        return totalAmountAfterOa;
    }

    public void setTotalAmountAfterOa(BigDecimal totalAmountAfterOa) {
        this.totalAmountAfterOa = totalAmountAfterOa;
    }

    public BigDecimal getTotalAmountWithNbt() {
        return totalAmountWithNbt;
    }

    public void setTotalAmountWithNbt(BigDecimal totalAmountWithNbt) {
        this.totalAmountWithNbt = totalAmountWithNbt;
    }
}
