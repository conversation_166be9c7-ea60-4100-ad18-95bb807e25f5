package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class SupplierDetailsMasterDto implements Serializable {
    private Integer supplierId;
    private String supplerName;
    private String supplierAddressLine1;
    private String supplierAddressLine2;
    private String supplierAddressLine3;
    private String contactNo;
    private String contactPerson;
    private String email;
    private String recordStatus;
    private String inputUserId;
    private Integer index;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplerName() {
        return supplerName;
    }

    public void setSupplerName(String supplerName) {
        this.supplerName = supplerName;
    }

    public String getSupplierAddressLine1() {
        return supplierAddressLine1;
    }

    public void setSupplierAddressLine1(String supplierAddressLine1) {
        this.supplierAddressLine1 = supplierAddressLine1;
    }

    public String getSupplierAddressLine2() {
        return supplierAddressLine2;
    }

    public void setSupplierAddressLine2(String supplierAddressLine2) {
        this.supplierAddressLine2 = supplierAddressLine2;
    }

    public String getSupplierAddressLine3() {
        return supplierAddressLine3;
    }

    public void setSupplierAddressLine3(String supplierAddressLine3) {
        this.supplierAddressLine3 = supplierAddressLine3;
    }

    public String getContactNo() {
        return contactNo;
    }

    public void setContactNo(String contactNo) {
        this.contactNo = contactNo;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(String inputUserId) {
        this.inputUserId = inputUserId;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}
