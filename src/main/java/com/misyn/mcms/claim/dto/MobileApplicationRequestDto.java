package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;
public class MobileApplicationRequestDto {
    //    private Integer refNo;
//    private String jobRefNo = AppConstant.STRING_EMPTY;
    private Integer index;
    private String claimNo = AppConstant.STRING_EMPTY;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private String assignUserId = AppConstant.STRING_EMPTY;
    private String notifyDate = AppConstant.STRING_EMPTY;
    private String readStatus = AppConstant.STRING_EMPTY;
    private String mobileReadStatus = AppConstant.STRING_EMPTY;

    public MobileApplicationRequestDto() {
    }

    public MobileApplicationRequestDto(Integer index, String claimNo, String vehicleNo, String assignUserId, String notifyDate, String readStatus, String mobileReadStatus) {
        this.index = index;
        this.claimNo = claimNo;
        this.vehicleNo = vehicleNo;
        this.assignUserId = assignUserId;
        this.notifyDate = notifyDate;
        this.readStatus = readStatus;
        this.mobileReadStatus = mobileReadStatus;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public String getNotifyDate() {
        return notifyDate;
    }

    public void setNotifyDate(String notifyDate) {
        this.notifyDate = notifyDate;
    }

    public String getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(String readStatus) {
        this.readStatus = readStatus;
    }

    public String getMobileReadStatus() {
        return mobileReadStatus;
    }

    public void setMobileReadStatus(String mobileReadStatus) {
        this.mobileReadStatus = mobileReadStatus;
    }

    //    private String recordStatus = AppConstant.STRING_EMPTY;
//    private String jobId = AppConstant.STRING_EMPTY;
//    private Integer index;

//    public Integer getRefNo() {
//        return refNo;
//    }
//
//    public void setRefNo(Integer refNo) {
//        this.refNo = refNo;
//    }
//
//    public String getJobRefNo() {
//        return jobRefNo;
//    }
//
//    public void setJobRefNo(String jobRefNo) {
//        this.jobRefNo = jobRefNo;
//    }
//
//    public String getClaimNo() {
//        return claimNo;
//    }
//
//    public void setClaimNo(String claimNo) {
//        this.claimNo = claimNo;
//    }
//
//    public String getAssignUserId() {
//        return assignUserId;
//    }
//
//    public void setAssignUserId(String assignUserId) {
//        this.assignUserId = assignUserId;
//    }
//
//    public String getNotifyDateTime() {
//        return notifyDateTime;
//    }
//
//    public void setNotifyDateTime(String notifyDateTime) {
//        this.notifyDateTime = notifyDateTime;
//    }
//
//    public String getRequestDateTime() {
//        return requestDateTime;
//    }
//
//    public void setRequestDateTime(String requestDateTime) {
//        this.requestDateTime = requestDateTime;
//    }
//
//    public String getRequestStatus() {
//        return requestStatus;
//    }
//
//    public void setRequestStatus(String requestStatus) {
//        this.requestStatus = requestStatus;
//    }
//
//    public Integer getIndex() {
//        return index;
//    }
//
//    public void setIndex(Integer index) {
//        this.index = index;
//    }
//
//    public String getRecordStatus() {
//        return recordStatus;
//    }
//
//    public void setRecordStatus(String recordStatus) {
//        this.recordStatus = recordStatus;
//    }
//
//    public String getJobId() {
//        return jobId;
//    }
//
//    public void setJobId(String jobId) {
//        this.jobId = jobId;
//    }
//
//    public String getInspectionId() {
//        return inspectionId;
//    }
//
//    public void setInspectionId(String inspectionId) {
//        this.inspectionId = inspectionId;
//    }
}
