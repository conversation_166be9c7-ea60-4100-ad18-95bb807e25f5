package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ReminderLetterFormDto implements Serializable {
    private ReminderPrintSummaryDto reminderPrintSummaryDto = new ReminderPrintSummaryDto();
    private List<ReminderPrintSummaryDto> reminderPrintSummaryList = new ArrayList<>();

    public ReminderPrintSummaryDto getReminderPrintSummaryDto() {
        return reminderPrintSummaryDto;
    }

    public void setReminderPrintSummaryDto(ReminderPrintSummaryDto reminderPrintSummaryDto) {
        this.reminderPrintSummaryDto = reminderPrintSummaryDto;
    }

    public List<ReminderPrintSummaryDto> getReminderPrintSummaryList() {
        return reminderPrintSummaryList;
    }

    public void setReminderPrintSummaryList(List<ReminderPrintSummaryDto> reminderPrintSummaryList) {
        this.reminderPrintSummaryList = reminderPrintSummaryList;
    }
}
