package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;

import java.io.Serializable;
public class ClaimThirdPartyDetailsGenericDto implements Serializable {

    private Integer txnId = AppConstant.ZERO_INT;
    private Integer ccTpdId = AppConstant.ZERO_INT;
    private Integer mappingId = AppConstant.ZERO_INT;
    private String mappingType = AppConstant.STRING_EMPTY;
    private Integer claimNo = AppConstant.ZERO_INT;
    private String thirdPartyInvolved = "No";
    private Integer lossType = AppConstant.ZERO_INT;
    private Integer itemType = AppConstant.ZERO_INT;
    private String vehicleNo = AppConstant.STRING_EMPTY;
    private String contactNo = AppConstant.STRING_EMPTY;
    private String insurerDetails = AppConstant.STRING_EMPTY;
    private String intendClaim = "No";
    private String remark = AppConstant.STRING_EMPTY;
    private String inpUserId = AppConstant.STRING_EMPTY;
    private String inpDateTime = AppConstant.STRING_EMPTY;
    private String type = AppConstant.STRING_EMPTY; //ASSESSOR or CALL_CENTER or MOTOR_ENGINEER
    private String status = AppConstant.STRING_EMPTY; //NEW or EDIT

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getCcTpdId() {
        return ccTpdId;
    }

    public void setCcTpdId(Integer ccTpdId) {
        this.ccTpdId = ccTpdId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getThirdPartyInvolved() {
        return thirdPartyInvolved;
    }

    public void setThirdPartyInvolved(String thirdPartyInvolved) {
        this.thirdPartyInvolved = thirdPartyInvolved;
    }

    public Integer getLossType() {
        return lossType;
    }

    public void setLossType(Integer lossType) {
        this.lossType = lossType;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getContactNo() {
        return contactNo;
    }

    public void setContactNo(String contactNo) {
        this.contactNo = contactNo;
    }

    public String getInsurerDetails() {
        return insurerDetails;
    }

    public void setInsurerDetails(String insurerDetails) {
        this.insurerDetails = insurerDetails;
    }

    public String getIntendClaim() {
        return intendClaim;
    }

    public void setIntendClaim(String intendClaim) {
        this.intendClaim = intendClaim;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDateTime() {
        if (null != inpDateTime && !inpDateTime.isEmpty()) {
            return Utility.getDate(inpDateTime, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm");
        } else {
            return inpDateTime;
        }
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    public String getMappingType() {
        return mappingType;
    }

    public void setMappingType(String mappingType) {
        this.mappingType = mappingType;
    }

}
