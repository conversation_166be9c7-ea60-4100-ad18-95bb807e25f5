package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.InvestigationStatusEnum;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
public class InvestigationDetailsDto implements Serializable {
    private Integer investTxnNo;
    private String investJobNo;
    private Integer claimNo;
    private String investigationStatus = InvestigationStatusEnum.DEFAULT.getInvestigationStatus();
    private String isAccident = AppConstant.NO;
    private String isTheft = AppConstant.NO;
    private String isFire = AppConstant.NO;
    private Integer assignInvestigatorUserRefId;
    private String assignInvestigatorName;
    private String investArrangeUser;
    private String investArrangeDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String investCompletedUser;
    private String investCompletedDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String isFirstStatement = AppConstant.NO;
    private String isDl = AppConstant.NO;
    private String isClaimForm = AppConstant.NO;
    private String isPhotos = AppConstant.NO;
    private String reason;
    private List<InvestigationReasonDetailsDto> investigationReasonDetailsDtoList = new ArrayList<>();

    private BigDecimal profFee = BigDecimal.ZERO;
    private BigDecimal travelFee = BigDecimal.ZERO;
    private BigDecimal otherFee = BigDecimal.ZERO;
    private BigDecimal totalFee = BigDecimal.ZERO;
    private String paymentStatus = "P";
    private String paymentInputUser;
    private String paymentInputDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String paymentAprvUser;
    private String paymentAprvDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String investReqUser;
    private String investReqDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String investReqAprvdUser;
    private String investReqAprvdDateTime = AppConstant.DEFAULT_DATE_TIME;


    public Integer getInvestTxnNo() {
        return investTxnNo;
    }

    public void setInvestTxnNo(Integer investTxnNo) {
        this.investTxnNo = investTxnNo;
    }

    public String getInvestJobNo() {
        return investJobNo;
    }

    public void setInvestJobNo(String investJobNo) {
        this.investJobNo = investJobNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getInvestigationStatus() {
        return investigationStatus;
    }

    public void setInvestigationStatus(String investigationStatus) {
        this.investigationStatus = investigationStatus;
    }

    public String getIsAccident() {
        return isAccident;
    }

    public void setIsAccident(String isAccident) {
        this.isAccident = isAccident;
    }

    public String getIsTheft() {
        return isTheft;
    }

    public void setIsTheft(String isTheft) {
        this.isTheft = isTheft;
    }

    public String getIsFire() {
        return isFire;
    }

    public void setIsFire(String isFire) {
        this.isFire = isFire;
    }

    public Integer getAssignInvestigatorUserRefId() {
        return assignInvestigatorUserRefId;
    }

    public void setAssignInvestigatorUserRefId(Integer assignInvestigatorUserRefId) {
        this.assignInvestigatorUserRefId = assignInvestigatorUserRefId;
    }

    public String getInvestArrangeDateTime() {
        return investArrangeDateTime;
    }

    public void setInvestArrangeDateTime(String investArrangeDateTime) {
        this.investArrangeDateTime = investArrangeDateTime;
    }

    public String getInvestCompletedUser() {
        return investCompletedUser;
    }

    public void setInvestCompletedUser(String investCompletedUser) {
        this.investCompletedUser = investCompletedUser;
    }

    public String getInvestCompletedDateTime() {
        return investCompletedDateTime;
    }

    public void setInvestCompletedDateTime(String investCompletedDateTime) {
        this.investCompletedDateTime = investCompletedDateTime;
    }

    public String getIsFirstStatement() {
        return isFirstStatement;
    }

    public void setIsFirstStatement(String isFirstStatement) {
        this.isFirstStatement = isFirstStatement;
    }

    public String getIsDl() {
        return isDl;
    }

    public void setIsDl(String isDl) {
        this.isDl = isDl;
    }

    public String getIsClaimForm() {
        return isClaimForm;
    }

    public void setIsClaimForm(String isClaimForm) {
        this.isClaimForm = isClaimForm;
    }

    public String getIsPhotos() {
        return isPhotos;
    }

    public void setIsPhotos(String isPhotos) {
        this.isPhotos = isPhotos;
    }

    public List<InvestigationReasonDetailsDto> getInvestigationReasonDetailsDtoList() {
        return investigationReasonDetailsDtoList;
    }

    public void setInvestigationReasonDetailsDtoList(List<InvestigationReasonDetailsDto> investigationReasonDetailsDtoList) {
        this.investigationReasonDetailsDtoList = investigationReasonDetailsDtoList;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getInvestArrangeUser() {
        return investArrangeUser;
    }

    public void setInvestArrangeUser(String investArrangeUser) {
        this.investArrangeUser = investArrangeUser;
    }

    public String getAssignInvestigatorName() {
        return assignInvestigatorName;
    }

    public void setAssignInvestigatorName(String assignInvestigatorName) {
        this.assignInvestigatorName = assignInvestigatorName;
    }

    public BigDecimal getProfFee() {
        return profFee;
    }

    public void setProfFee(BigDecimal profFee) {
        this.profFee = profFee;
    }

    public BigDecimal getTravelFee() {
        return travelFee;
    }

    public void setTravelFee(BigDecimal travelFee) {
        this.travelFee = travelFee;
    }

    public BigDecimal getOtherFee() {
        return otherFee;
    }

    public void setOtherFee(BigDecimal otherFee) {
        this.otherFee = otherFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getPaymentInputUser() {
        return paymentInputUser;
    }

    public void setPaymentInputUser(String paymentInputUser) {
        this.paymentInputUser = paymentInputUser;
    }

    public String getPaymentInputDateTime() {
        return paymentInputDateTime;
    }

    public void setPaymentInputDateTime(String paymentInputDateTime) {
        this.paymentInputDateTime = paymentInputDateTime;
    }

    public String getPaymentAprvUser() {
        return paymentAprvUser;
    }

    public void setPaymentAprvUser(String paymentAprvUser) {
        this.paymentAprvUser = paymentAprvUser;
    }

    public String getPaymentAprvDateTime() {
        return paymentAprvDateTime;
    }

    public void setPaymentAprvDateTime(String paymentAprvDateTime) {
        this.paymentAprvDateTime = paymentAprvDateTime;
    }

    public String getInvestReqUser() {
        return investReqUser;
    }

    public void setInvestReqUser(String investReqUser) {
        this.investReqUser = investReqUser;
    }

    public String getInvestReqDateTime() {
        return investReqDateTime;
    }

    public void setInvestReqDateTime(String investReqDateTime) {
        this.investReqDateTime = investReqDateTime;
    }

    public String getInvestReqAprvdUser() {
        return investReqAprvdUser;
    }

    public void setInvestReqAprvdUser(String investReqAprvdUser) {
        this.investReqAprvdUser = investReqAprvdUser;
    }

    public String getInvestReqAprvdDateTime() {
        return investReqAprvdDateTime;
    }

    public void setInvestReqAprvdDateTime(String investReqAprvdDateTime) {
        this.investReqAprvdDateTime = investReqAprvdDateTime;
    }
}
