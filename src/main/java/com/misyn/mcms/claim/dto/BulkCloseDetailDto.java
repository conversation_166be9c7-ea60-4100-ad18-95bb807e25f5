package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class BulkCloseDetailDto implements Serializable {
    private Integer id = AppConstant.ZERO_INT;
    private Integer claimNo = AppConstant.ZERO_INT;
    private Integer days = AppConstant.ZERO_INT;
    private String userId = AppConstant.STRING_EMPTY;
    private String dateTime = AppConstant.DEFAULT_DATE_TIME;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }
}
