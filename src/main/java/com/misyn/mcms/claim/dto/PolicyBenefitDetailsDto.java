package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class PolicyBenefitDetailsDto implements Serializable {
    private String polNumber= AppConstant.EMPTY_STRING;
    private String code= AppConstant.EMPTY_STRING;
    private String desc= AppConstant.EMPTY_STRING;
    private Integer renCount=AppConstant.ZERO_INT;
    private Integer endCount=AppConstant.ZERO_INT;
    private BigDecimal rate= BigDecimal.ZERO;
    private BigDecimal amount= BigDecimal.ZERO;
    private BigDecimal manualPrem= BigDecimal.ZERO;
    private BigDecimal excessAmt= BigDecimal.ZERO;
    private String calBasis= AppConstant.EMPTY_STRING;
    private String lstUpdate= AppConstant.EMPTY_STRING;

    public PolicyBenefitDetailsDto(String polNumber, String code, String desc, Integer renCount, Integer endCount, BigDecimal rate, BigDecimal amount, BigDecimal manualPrem, BigDecimal excessAmt, String calBasis, String lstUpdate) {
        this.polNumber = polNumber;
        this.code = code;
        this.desc = desc;
        this.renCount = renCount;
        this.endCount = endCount;
        this.rate = rate;
        this.amount = amount;
        this.manualPrem = manualPrem;
        this.excessAmt = excessAmt;
        this.calBasis = calBasis;
        this.lstUpdate = lstUpdate;
    }

    public PolicyBenefitDetailsDto() {
    }

    public String getPolNumber() {
        return polNumber;
    }

    public void setPolNumber(String polNumber) {
        this.polNumber = polNumber;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getManualPrem() {
        return manualPrem;
    }

    public void setManualPrem(BigDecimal manualPrem) {
        this.manualPrem = manualPrem;
    }

    public BigDecimal getExcessAmt() {
        return excessAmt;
    }

    public void setExcessAmt(BigDecimal excessAmt) {
        this.excessAmt = excessAmt;
    }

    public String getCalBasis() {
        return calBasis;
    }

    public void setCalBasis(String calBasis) {
        this.calBasis = calBasis;
    }

    public String getLstUpdate() {
        return lstUpdate;
    }

    public void setLstUpdate(String lstUpdate) {
        this.lstUpdate = lstUpdate;
    }
}
