/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;


import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class FieldParameterDto implements Serializable {
    private static final long serialVersionUID = -2284519882382516560L;
    private String fieldName = AppConstant.STRING_EMPTY;
    private String fieldValue = AppConstant.STRING_EMPTY;
    private String dbFieldName = AppConstant.STRING_EMPTY;
    private boolean stringType = false;
    private SearchType searchType = SearchType.Equal; //1=Equal,2=like

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getDbFieldName() {
        return dbFieldName;
    }

    public void setDbFieldName(String dbFieldName) {
        this.dbFieldName = dbFieldName;
    }

    public boolean isStringType() {
        return stringType;
    }

    public void setStringType(boolean stringType) {
        this.stringType = stringType;
    }

    public SearchType getSearchType() {
        return searchType;
    }

    public void setSearchType(SearchType searchType) {
        this.searchType = searchType;
    }

    public enum SearchType {

        Equal(1),
        Like(2),
        NOT_Equal(3),
        IN(4),
        IS_NULL(5),
        Equal_And_Greater_Than(6),
        Equal_And_Less_Than(7),
        NOT_IN(8);
        private final int searchType;

        private SearchType(int searchType) {
            this.searchType = searchType;
        }

        public int getSearchType() {
            return searchType;
        }
    }
}
