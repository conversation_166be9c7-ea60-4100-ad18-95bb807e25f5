package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class PolicyBaseCategoryDataDto implements Serializable {
    private String type = AppConstant.STRING_EMPTY;
    private String code = AppConstant.STRING_EMPTY;
    private String name = AppConstant.STRING_EMPTY;
    private String description = AppConstant.STRING_EMPTY;
    private String amount = AppConstant.STRING_EMPTY;
    private String more = AppConstant.STRING_EMPTY;
    private boolean deleted= false;



    public PolicyBaseCategoryDataDto() {
    }

    public PolicyBaseCategoryDataDto(String type, String code, String description, String amount, String more, boolean deleted) {
        this.type = type;
        this.code = code;
        this.description = description;
        this.amount = amount;
        this.more = more;
        this.deleted = deleted;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getMore() {
        return more;
    }

    public void setMore(String more) {
        this.more = more;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }
}
