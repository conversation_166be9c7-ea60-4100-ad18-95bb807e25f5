package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.ResponseStatus;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.math.BigDecimal;
public class AssessorAllocationDto implements Serializable {
    private int refNo = AppConstant.ZERO_INT;
    private String jobId = AppConstant.STRING_EMPTY;
    @NotNull
    private InspectionDto inspectionDto = new InspectionDto();
    @NotNull
    private InspectionReasonDto inspectionReasonDto = new InspectionReasonDto();
    private String placeOfinspection = AppConstant.STRING_EMPTY;
    @NotNull
    private DistrictDto districtDto = new DistrictDto();
    @NotNull
    private CityDto cityDto = new CityDto();
    @NotNull
    private AssessorDto assessorDto = new AssessorDto();
    private ResponseStatus responseStatus = ResponseStatus.NoResponse;
    private String currentLocation = AppConstant.STRING_EMPTY;

    private String assignDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String assignHours = AppConstant.DEFAULT_DATE_TIME;
    private String assignMinutes = AppConstant.DEFAULT_DATE_TIME;
    private String assignPeriod = AppConstant.DEFAULT_DATE_TIME;

    private String custExpectedDatetime = AppConstant.DEFAULT_DATE_TIME;
    private int duration = AppConstant.ZERO_INT;
    private String actualDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String smsBody = AppConstant.STRING_EMPTY;
    private String jobFinishedDatetime = AppConstant.DEFAULT_DATE_TIME;
    private int recordStatus = AppConstant.ZERO_INT;
    private String inputUserId = AppConstant.STRING_EMPTY;
    private String inputDatetime = AppConstant.STRING_EMPTY;
    private RejectReasonDto rejectReasonDto = new RejectReasonDto();
    private int durationHours = AppConstant.ZERO_INT;
    private int durationMinutes = AppConstant.ZERO_INT;
    private String specialRemark = AppConstant.STRING_EMPTY;
    private int response = AppConstant.ZERO_INT;
    private ClaimStatusParameterDto claimStatusParameterDto = new ClaimStatusParameterDto();
    private String jobCloseDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String assigningDate = Utility.sysDate();
    private int jobStatusId = AppConstant.ZERO_INT;
    private String responseValue = "24";
    private String smsStatus = AppConstant.STRING_EMPTY;
    private String assessorJobStatus = AppConstant.STRING_EMPTY;
    private ClaimsDto claimsDto = new ClaimsDto();
    private String previousJobId = AppConstant.STRING_EMPTY;
    private String remark = AppConstant.STRING_EMPTY;
    private String customerMobileNo = AppConstant.STRING_EMPTY;
    private int claimsId = AppConstant.ZERO_INT;
    private String isReAssign = AppConstant.NO;
    private ReassigningReasonDto reassigningReasonDto = new ReassigningReasonDto();
    private String rteCode = AppConstant.STRING_EMPTY;
    private String rteDatetTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer previousRefId = AppConstant.ZERO_INT;
    private String garageContactNo = AppConstant.STRING_EMPTY;
    private String isPartnerGarage = AppConstant.NO;
    private String callCenterRemark = AppConstant.STRING_EMPTY;
    private String priority = AppConstant.PRIORITY_NORMAL;
    private String assessmentType = AppConstant.STRING_EMPTY;
    private String assessorType = AppConstant.STRING_EMPTY;
    private String typeOnlineInspection = AppConstant.NO;
    private RejectReasonNewDto rejectReasonNewDto = new RejectReasonNewDto();
    private BigDecimal acrAmount = BigDecimal.ZERO;

    private String isCommenceAssessment = AppConstant.NO;
    private String commenceAssessmentDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String isOnsiteReview = AppConstant.NO;
    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }


    public InspectionDto getInspectionDto() {
        return inspectionDto;
    }

    public void setInspectionDto(InspectionDto inspectionDto) {
        this.inspectionDto = inspectionDto;
    }

    public InspectionReasonDto getInspectionReasonDto() {
        return inspectionReasonDto;
    }

    public void setInspectionReasonDto(InspectionReasonDto inspectionReasonDto) {
        this.inspectionReasonDto = inspectionReasonDto;
    }

    public String getPlaceOfinspection() {
        return placeOfinspection;
    }

    public void setPlaceOfinspection(String placeOfinspection) {
        this.placeOfinspection = placeOfinspection;
    }

    public DistrictDto getDistrictDto() {
        return districtDto;
    }

    public void setDistrictDto(DistrictDto districtDto) {
        this.districtDto = districtDto;
    }

    public CityDto getCityDto() {
        return cityDto;
    }

    public void setCityDto(CityDto cityDto) {
        this.cityDto = cityDto;
    }

    public AssessorDto getAssessorDto() {
        return assessorDto;
    }

    public void setAssessorDto(AssessorDto assessorDto) {
        this.assessorDto = assessorDto;
    }

    public ResponseStatus getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(ResponseStatus responseStatus) {
        this.responseStatus = responseStatus;
    }

    public String getCurrentLocation() {
        return currentLocation;
    }

    public void setCurrentLocation(String currentLocation) {
        this.currentLocation = currentLocation;
    }

    public String getAssignDatetime() {
        return assignDatetime;
    }

    public void setAssignDatetime(String assignDatetime) {
        this.assignDatetime = assignDatetime;
    }

    public String getCustExpectedDatetime() {
        return custExpectedDatetime;
    }

    public void setCustExpectedDatetime(String custExpectedDatetime) {
        this.custExpectedDatetime = custExpectedDatetime;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getActualDatetime() {
        return actualDatetime;
    }

    public void setActualDatetime(String actualDatetime) {
        this.actualDatetime = actualDatetime;
    }

    public String getSmsBody() {
        return smsBody;
    }

    public void setSmsBody(String smsBody) {
        this.smsBody = smsBody;
    }

    public String getJobFinishedDatetime() {
        return jobFinishedDatetime;
    }

    public void setJobFinishedDatetime(String jobFinishedDatetime) {
        this.jobFinishedDatetime = jobFinishedDatetime;
    }

    public int getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(int recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(String inputUserId) {
        this.inputUserId = inputUserId;
    }

    public String getInputDatetime() {
        return inputDatetime;
    }

    public void setInputDatetime(String inputDatetime) {
        this.inputDatetime = inputDatetime;
    }

    public RejectReasonDto getRejectReasonDto() {
        return rejectReasonDto;
    }

    public void setRejectReasonDto(RejectReasonDto rejectReasonDto) {
        this.rejectReasonDto = rejectReasonDto;
    }

    public String getAssignHours() {
        return assignHours;
    }

    public void setAssignHours(String assignHours) {
        this.assignHours = assignHours;
    }

    public String getAssignMinutes() {
        return assignMinutes;
    }

    public void setAssignMinutes(String assignMinutes) {
        this.assignMinutes = assignMinutes;
    }

    public String getAssignPeriod() {
        return assignPeriod;
    }

    public void setAssignPeriod(String assignPeriod) {
        this.assignPeriod = assignPeriod;
    }

    public int getDurationHours() {
        return durationHours;
    }

    public void setDurationHours(int durationHours) {
        this.durationHours = durationHours;
    }

    public int getDurationMinutes() {
        return durationMinutes;
    }

    public void setDurationMinutes(int durationMinutes) {
        this.durationMinutes = durationMinutes;
    }

    public String getSpecialRemark() {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
    }

    public int getResponse() {
        return response;
    }

    public void setResponse(int response) {
        this.response = response;
    }

    public ClaimStatusParameterDto getClaimStatusParameterDto() {
        return claimStatusParameterDto;
    }

    public void setClaimStatusParameterDto(ClaimStatusParameterDto claimStatusParameterDto) {
        this.claimStatusParameterDto = claimStatusParameterDto;
    }

    public String getJobCloseDateTime() {
        return jobCloseDateTime;
    }

    public void setJobCloseDateTime(String jobCloseDateTime) {
        this.jobCloseDateTime = jobCloseDateTime;
    }

    public String getAssigningDate() {
        return assigningDate;
    }

    public void setAssigningDate(String assigningDate) {
        this.assigningDate = assigningDate;
    }

    public int getJobStatusId() {
        return jobStatusId;
    }

    public void setJobStatusId(int jobStatusId) {
        this.jobStatusId = jobStatusId;
    }

    public String getResponseValue() {
        return responseValue;
    }

    public void setResponseValue(String responseValue) {
        this.responseValue = responseValue;
    }

    public String getSmsStatus() {
        return smsStatus;
    }

    public void setSmsStatus(String smsStatus) {
        this.smsStatus = smsStatus;
    }

    public String getAssessorJobStatus() {
        return assessorJobStatus;
    }

    public void setAssessorJobStatus(String assessorJobStatus) {
        this.assessorJobStatus = assessorJobStatus;
    }

    public ClaimsDto getClaimsDto() {
        return claimsDto;
    }

    public void setClaimsDto(ClaimsDto claimsDto) {
        this.claimsDto = claimsDto;
    }

    public String getPreviousJobId() {
        return previousJobId;
    }

    public void setPreviousJobId(String previousJobId) {
        this.previousJobId = previousJobId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCustomerMobileNo() {
        return customerMobileNo;
    }

    public void setCustomerMobileNo(String customerMobileNo) {
        this.customerMobileNo = customerMobileNo;
    }


    public int getClaimsId() {
        return claimsId;
    }

    public void setClaimsId(int claimsId) {
        this.claimsId = claimsId;
    }

    public String getIsReAssign() {
        return isReAssign;
    }

    public void setIsReAssign(String isReAssign) {
        this.isReAssign = isReAssign;
    }

    public ReassigningReasonDto getReassigningReasonDto() {
        return reassigningReasonDto;
    }

    public void setReassigningReasonDto(ReassigningReasonDto reassigningReasonDto) {
        this.reassigningReasonDto = reassigningReasonDto;
    }

    public void setAccidentTimeFileds(String[] t) {
        setAssignHours(t[0]);
        setAssignMinutes(t[1]);
        setAssignPeriod(t[2]);
    }

    public String getRteCode() {
        return rteCode;
    }

    public void setRteCode(String rteCode) {
        this.rteCode = rteCode;
    }

    public String getRteDatetTime() {
        return rteDatetTime;
    }

    public void setRteDatetTime(String rteDatetTime) {
        this.rteDatetTime = rteDatetTime;
    }

    public Integer getPreviousRefId() {
        return previousRefId;
    }

    public void setPreviousRefId(Integer previousRefId) {
        this.previousRefId = previousRefId;
    }

    public String getGarageContactNo() {
        return garageContactNo;
    }

    public void setGarageContactNo(String garageContactNo) {
        this.garageContactNo = garageContactNo;
    }

    public String getIsPartnerGarage() {
        return isPartnerGarage;
    }

    public void setIsPartnerGarage(String isPartnerGarage) {
        this.isPartnerGarage = isPartnerGarage;
    }

    public String getCallCenterRemark() {
        return callCenterRemark;
    }

    public void setCallCenterRemark(String callCenterRemark) {
        this.callCenterRemark = callCenterRemark;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getAssessmentType() {
        return assessmentType;
    }

    public void setAssessmentType(String assessmentType) {
        this.assessmentType = assessmentType;
    }

    public String getAssessorType() {
        return assessorType;
    }

    public void setAssessorType(String assessorType) {
        this.assessorType = assessorType;
    }

    public RejectReasonNewDto getRejectReasonNewDto() {
        return rejectReasonNewDto;
    }

    public void setRejectReasonNewDto(RejectReasonNewDto rejectReasonNewDto) {
        this.rejectReasonNewDto = rejectReasonNewDto;
    }

    public String getIsCommenceAssessment() {
        return isCommenceAssessment;
    }

    public void setIsCommenceAssessment(String isCommenceAssessment) {
        this.isCommenceAssessment = isCommenceAssessment;
    }

    public String getCommenceAssessmentDatetime() {
        return commenceAssessmentDatetime;
    }

    public void setCommenceAssessmentDatetime(String commenceAssessmentDatetime) {
        this.commenceAssessmentDatetime = commenceAssessmentDatetime;
    }

    public String getTypeOnlineInspection() {
        return typeOnlineInspection;
    }

    public void setTypeOnlineInspection(String typeOnlineInspection) {
        this.typeOnlineInspection = typeOnlineInspection;
    }

    public String getIsOnsiteReview() {
        return isOnsiteReview;
    }

    public void setIsOnsiteReview(String isOnsiteReview) {
        this.isOnsiteReview = isOnsiteReview;
    }

    public BigDecimal getAcrAmount() {
        return acrAmount;
    }

    public void setAcrAmount(BigDecimal acrAmount) {
        this.acrAmount = acrAmount;
    }
}
