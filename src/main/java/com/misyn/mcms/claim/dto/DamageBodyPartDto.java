package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class DamageBodyPartDto implements Serializable {

    private Integer txnId = 0;
    private Integer vehClsId = 0;
    private String partCode = "";
    private String partName = "";
    private String partDesc = "";
    private Integer claimNo = 0;
    private String damegType = "";
    private String otherText = "";
    private boolean isRecFoundMainTable = false;
    private boolean isBodyPartsCheck = false;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getVehClsId() {
        return vehClsId;
    }

    public void setVehClsId(Integer vehClsId) {
        this.vehClsId = vehClsId;
    }

    public String getPartCode() {
        return partCode;
    }

    public void setPartCode(String partCode) {
        this.partCode = partCode;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public String getPartDesc() {
        return partDesc;
    }

    public void setPartDesc(String partDesc) {
        this.partDesc = partDesc;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getDamegType() {
        return damegType;
    }

    public void setDamegType(String damegType) {
        this.damegType = damegType;
    }

    public String getOtherText() {
        return otherText;
    }

    public void setOtherText(String otherText) {
        this.otherText = otherText;
    }

    public boolean isRecFoundMainTable() {
        return isRecFoundMainTable;
    }

    public void setRecFoundMainTable(boolean recFoundMainTable) {
        isRecFoundMainTable = recFoundMainTable;
    }

    public boolean isBodyPartsCheck() {
        return isBodyPartsCheck;
    }

    public void setBodyPartsCheck(boolean bodyPartsCheck) {
        isBodyPartsCheck = bodyPartsCheck;
    }
}
