package com.misyn.mcms.claim.dto;


import java.io.Serializable;
public class PolicySellingAgentDetailsDto implements Serializable {

    private Integer agentNo;
    private String agentCode;
    private Integer channelNo;

    private String channel;

    private String channelAcctCode;

    private String agentType;

    private String idenNo;

    private Integer custRefNo;

    private String agentName;

    private String rankCode;

    private String rankName;

    private Integer level;

    private String status;

    private String contactNo;

    private String email;

    public PolicySellingAgentDetailsDto() {

    }

    public Integer getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(Integer agentNo) {
        this.agentNo = agentNo;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public Integer getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(Integer channelNo) {
        this.channelNo = channelNo;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getChannelAcctCode() {
        return channelAcctCode;
    }

    public void setChannelAcctCode(String channelAcctCode) {
        this.channelAcctCode = channelAcctCode;
    }

    public String getAgentType() {
        return agentType;
    }

    public void setAgentType(String agentType) {
        this.agentType = agentType;
    }

    public String getIdenNo() {
        return idenNo;
    }

    public void setIdenNo(String idenNo) {
        this.idenNo = idenNo;
    }

    public Integer getCustRefNo() {
        return custRefNo;
    }

    public void setCustRefNo(Integer custRefNo) {
        this.custRefNo = custRefNo;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getRankCode() {
        return rankCode;
    }

    public void setRankCode(String rankCode) {
        this.rankCode = rankCode;
    }

    public String getRankName() {
        return rankName;
    }

    public void setRankName(String rankName) {
        this.rankName = rankName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getContactNo() {
        return contactNo;
    }

    public void setContactNo(String contactNo) {
        this.contactNo = contactNo;
    }

    public String getEmail() {return email;}

    public void setEmail(String email) {this.email = email;}
}
