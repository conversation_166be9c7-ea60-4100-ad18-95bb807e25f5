/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class ClaimCalculationSheetSupplierOrderDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer calSheetSoId;
    private int calSheetId;
    private int supplierOrderId = 0;
    private String serialNumber;
    private BigDecimal amount;

    public Integer getCalSheetSoId() {
        return calSheetSoId;
    }

    public void setCalSheetSoId(Integer calSheetSoId) {
        this.calSheetSoId = calSheetSoId;
    }

    public int getCalSheetId() {
        return calSheetId;
    }

    public void setCalSheetId(int calSheetId) {
        this.calSheetId = calSheetId;
    }

    public int getSupplierOrderId() {
        return supplierOrderId;
    }

    public void setSupplierOrderId(int supplierOrderId) {
        this.supplierOrderId = supplierOrderId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
