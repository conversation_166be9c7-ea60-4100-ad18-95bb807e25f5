package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class ARIInspectionDetailsDto implements Serializable {

    private Integer inspectionId = AppConstant.ZERO_INT;
    private int refNo = AppConstant.ZERO_INT;
    private ConditionType isAri = ConditionType.No;
    private ConditionType ariOrder = ConditionType.No;
    private ConditionType salvageOrder = ConditionType.No;
    private ConditionType isSalvage = ConditionType.No;
    private String assessorSpecialRemark = AppConstant.STRING_EMPTY;
    private String inspectionRemark = AppConstant.STRING_EMPTY;

    private BigDecimal professionalFee = BigDecimal.ZERO;
    private BigDecimal miles = BigDecimal.ZERO;
    private BigDecimal telephoneCharge = BigDecimal.ZERO;
    private BigDecimal otherCharge = BigDecimal.ZERO;
    private BigDecimal specialDeduction = BigDecimal.ZERO;
    private String reason = AppConstant.STRING_EMPTY;
    private BigDecimal totalCharge = BigDecimal.ZERO;
    private String specialRemark = AppConstant.STRING_EMPTY;
    private String partiallyReview = AppConstant.NO;

    public Integer getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(Integer inspectionId) {
        this.inspectionId = inspectionId;
    }

    public ConditionType getAriOrder() {
        return ariOrder;
    }

    public void setAriOrder(ConditionType ariOrder) {
        this.ariOrder = ariOrder;
    }

    public ConditionType getSalvageOrder() {
        return salvageOrder;
    }

    public void setSalvageOrder(ConditionType salvageOrder) {
        this.salvageOrder = salvageOrder;
    }

    public BigDecimal getProfessionalFee() {
        return professionalFee;
    }

    public void setProfessionalFee(BigDecimal professionalFee) {
        this.professionalFee = professionalFee;
    }

    public String getAssessorSpecialRemark() {
        return assessorSpecialRemark;
    }

    public void setAssessorSpecialRemark(String assessorSpecialRemark) {
        this.assessorSpecialRemark = assessorSpecialRemark;
    }

    public String getInspectionRemark() {
        return inspectionRemark;
    }

    public void setInspectionRemark(String inspectionRemark) {
        this.inspectionRemark = inspectionRemark;
    }

    public BigDecimal getTelephoneCharge() {
        return telephoneCharge;
    }

    public void setTelephoneCharge(BigDecimal telephoneCharge) {
        this.telephoneCharge = telephoneCharge;
    }

    public BigDecimal getOtherCharge() {
        return otherCharge;
    }

    public void setOtherCharge(BigDecimal otherCharge) {
        this.otherCharge = otherCharge;
    }

    public BigDecimal getSpecialDeduction() {
        return specialDeduction;
    }

    public void setSpecialDeduction(BigDecimal specialDeduction) {
        this.specialDeduction = specialDeduction;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public BigDecimal getTotalCharge() {
        return totalCharge;
    }

    public void setTotalCharge(BigDecimal totalCharge) {
        this.totalCharge = totalCharge;
    }

    public BigDecimal getMiles() {
        return miles;
    }

    public void setMiles(BigDecimal miles) {
        this.miles = miles;
    }

    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public ConditionType getIsAri() {
        return isAri;
    }

    public void setIsAri(ConditionType isAri) {
        this.isAri = isAri;
    }

    public ConditionType getIsSalvage() {
        return isSalvage;
    }

    public void setIsSalvage(ConditionType isSalvage) {
        this.isSalvage = isSalvage;
    }

    public String getSpecialRemark() {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
    }

    public String getPartiallyReview() {
        return partiallyReview;
    }

    public void setPartiallyReview(String partiallyReview) {
        this.partiallyReview = partiallyReview;
    }
}
