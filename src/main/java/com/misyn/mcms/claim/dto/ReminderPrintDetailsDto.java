package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class ReminderPrintDetailsDto implements Serializable {
    private Integer reminderDetailsRefId;
    private Integer reminderSummaryRefId;
    private Integer docTypeId;
    private String checkReminderPrint = AppConstant.YES;
    private String reminderDocDisplayName;

    public String getReminderGeneratedDate() {
        ReminderPrintSummaryDto reminderPrintSummaryDto = new ReminderPrintSummaryDto();
        reminderPrintSummaryDto.setGeneratedDateTime(reminderPrintSummaryDto.getGeneratedDateTime());
        return reminderPrintSummaryDto.getGeneratedDateTime();
    }

    public Integer getReminderDetailsRefId() {
        return reminderDetailsRefId;
    }

    public void setReminderDetailsRefId(Integer reminderDetailsRefId) {
        this.reminderDetailsRefId = reminderDetailsRefId;
    }

    public Integer getReminderSummaryRefId() {
        return reminderSummaryRefId;
    }

    public void setReminderSummaryRefId(Integer reminderSummaryRefId) {
        this.reminderSummaryRefId = reminderSummaryRefId;
    }

    public Integer getDocTypeId() {
        return docTypeId;
    }

    public void setDocTypeId(Integer docTypeId) {
        this.docTypeId = docTypeId;
    }

    public String getCheckReminderPrint() {
        return checkReminderPrint;
    }

    public void setCheckReminderPrint(String checkReminderPrint) {
        this.checkReminderPrint = checkReminderPrint;
    }

    public String getReminderDocDisplayName() {
        return reminderDocDisplayName;
    }

    public void setReminderDocDisplayName(String reminderDocDisplayName) {
        this.reminderDocDisplayName = reminderDocDisplayName;
    }
}
