package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class DrSupplementaryInspectionDetailsDto implements Serializable {

    private Integer inspectionId = AppConstant.ZERO_INT;
    private int refNo = AppConstant.ZERO_INT;
    private BigDecimal sumInsured = BigDecimal.ZERO;
    private BigDecimal preAccidentValue = BigDecimal.ZERO;
    private BigDecimal excess = BigDecimal.ZERO;
    private BigDecimal acr = BigDecimal.ZERO;
    private BigDecimal boldTyrePenaltyAmount = BigDecimal.ZERO;
    private BigDecimal underInsurancePenaltyAmount = BigDecimal.ZERO;
    private BigDecimal payableAmount = BigDecimal.ZERO;
    private String assessorRemark = AppConstant.STRING_EMPTY;
    private String inspectionRemark = AppConstant.STRING_EMPTY;
    private ConditionType ariAndSalvage = ConditionType.No;

    private BigDecimal professionalFee = BigDecimal.ZERO;
    private BigDecimal miles = BigDecimal.ZERO;
    private BigDecimal telephoneCharge = BigDecimal.ZERO;
    private BigDecimal otherCharge = BigDecimal.ZERO;
    private BigDecimal specialDeduction = BigDecimal.ZERO;
    private String reason = AppConstant.STRING_EMPTY;
    private BigDecimal totalCharge = BigDecimal.ZERO;
    private String specialRemark = AppConstant.STRING_EMPTY;

    private BigDecimal oldAcr = BigDecimal.ZERO;

    public BigDecimal getSumInsured() {
        return sumInsured;
    }

    public void setSumInsured(BigDecimal sumInsured) {
        this.sumInsured = sumInsured;
    }

    public BigDecimal getPreAccidentValue() {
        return preAccidentValue;
    }

    public void setPreAccidentValue(BigDecimal preAccidentValue) {
        this.preAccidentValue = preAccidentValue;
    }

    public BigDecimal getExcess() {
        return excess;
    }

    public void setExcess(BigDecimal excess) {
        this.excess = excess;
    }

    public BigDecimal getAcr() {
        return acr;
    }

    public void setAcr(BigDecimal acr) {
        this.acr = acr;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public String getAssessorRemark() {
        return assessorRemark;
    }

    public void setAssessorRemark(String assessorRemark) {
        this.assessorRemark = assessorRemark;
    }

    public String getInspectionRemark() {
        return inspectionRemark;
    }

    public void setInspectionRemark(String inspectionRemark) {
        this.inspectionRemark = inspectionRemark;
    }

    public ConditionType getAriAndSalvage() {
        return ariAndSalvage;
    }

    public void setAriAndSalvage(ConditionType ariAndSalvage) {
        this.ariAndSalvage = ariAndSalvage;
    }

    public BigDecimal getProfessionalFee() {
        return professionalFee;
    }

    public void setProfessionalFee(BigDecimal professionalFee) {
        this.professionalFee = professionalFee;
    }

    public BigDecimal getTelephoneCharge() {
        return telephoneCharge;
    }

    public void setTelephoneCharge(BigDecimal telephoneCharge) {
        this.telephoneCharge = telephoneCharge;
    }

    public BigDecimal getOtherCharge() {
        return otherCharge;
    }

    public void setOtherCharge(BigDecimal otherCharge) {
        this.otherCharge = otherCharge;
    }

    public BigDecimal getSpecialDeduction() {
        return specialDeduction;
    }

    public void setSpecialDeduction(BigDecimal specialDeduction) {
        this.specialDeduction = specialDeduction;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public BigDecimal getTotalCharge() {
        return totalCharge;
    }

    public void setTotalCharge(BigDecimal totalCharge) {
        this.totalCharge = totalCharge;
    }

    public Integer getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(Integer inspectionId) {
        this.inspectionId = inspectionId;
    }

    public BigDecimal getUnderInsurancePenaltyAmount() {
        return underInsurancePenaltyAmount;
    }

    public void setUnderInsurancePenaltyAmount(BigDecimal underInsurancePenaltyAmount) {
        this.underInsurancePenaltyAmount = underInsurancePenaltyAmount;
    }

    public BigDecimal getMiles() {
        return miles;
    }

    public void setMiles(BigDecimal miles) {
        this.miles = miles;
    }

    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public BigDecimal getBoldTyrePenaltyAmount() {
        return boldTyrePenaltyAmount;
    }

    public void setBoldTyrePenaltyAmount(BigDecimal boldTyrePenaltyAmount) {
        this.boldTyrePenaltyAmount = boldTyrePenaltyAmount;
    }

    public BigDecimal getOldAcr() {
        return oldAcr;
    }

    public void setOldAcr(BigDecimal oldAcr) {
        this.oldAcr = oldAcr;
    }

    public String getSpecialRemark() {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
    }
}
