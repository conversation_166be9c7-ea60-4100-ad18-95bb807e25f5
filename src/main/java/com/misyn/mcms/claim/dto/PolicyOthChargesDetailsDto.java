package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class PolicyOthChargesDetailsDto implements Serializable {
    private String polNumber= AppConstant.EMPTY_STRING;
    private Integer renCount=AppConstant.ZERO_INT;
    private Integer endCount=AppConstant.ZERO_INT;
    private String code= AppConstant.EMPTY_STRING;
    private String desc= AppConstant.EMPTY_STRING;
    private String flatFlag= AppConstant.EMPTY_STRING;
    private String perWhat= AppConstant.EMPTY_STRING;
    private BigDecimal perAmount=BigDecimal.ZERO;
    private BigDecimal chgAmount=BigDecimal.ZERO;
    private String currencyCode= AppConstant.EMPTY_STRING;
    private BigDecimal exRate=BigDecimal.ZERO;
    private BigDecimal chgAmtLocalCurrency=BigDecimal.ZERO;
    private String commDate= AppConstant.EMPTY_STRING;
    private String lstUpdate= AppConstant.EMPTY_STRING;

    public PolicyOthChargesDetailsDto(String polNumber, Integer renCount, Integer endCount, String code, String desc, String flatFlag, String perWhat, BigDecimal perAmount, BigDecimal chgAmount, String currencyCode, BigDecimal exRate, BigDecimal chgAmtLocalCurrency, String commDate, String lstUpdate) {
        this.polNumber = polNumber;
        this.renCount = renCount;
        this.endCount = endCount;
        this.code = code;
        this.desc = desc;
        this.flatFlag = flatFlag;
        this.perWhat = perWhat;
        this.perAmount = perAmount;
        this.chgAmount = chgAmount;
        this.currencyCode = currencyCode;
        this.exRate = exRate;
        this.chgAmtLocalCurrency = chgAmtLocalCurrency;
        this.commDate = commDate;
        this.lstUpdate = lstUpdate;
    }

    public PolicyOthChargesDetailsDto() {
    }

    public String getPolNumber() {
        return polNumber;
    }

    public void setPolNumber(String polNumber) {
        this.polNumber = polNumber;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getFlatFlag() {
        return flatFlag;
    }

    public void setFlatFlag(String flatFlag) {
        this.flatFlag = flatFlag;
    }

    public String getPerWhat() {
        return perWhat;
    }

    public void setPerWhat(String perWhat) {
        this.perWhat = perWhat;
    }

    public BigDecimal getPerAmount() {
        return perAmount;
    }

    public void setPerAmount(BigDecimal perAmount) {
        this.perAmount = perAmount;
    }

    public BigDecimal getChgAmount() {
        return chgAmount;
    }

    public void setChgAmount(BigDecimal chgAmount) {
        this.chgAmount = chgAmount;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getExRate() {
        return exRate;
    }

    public void setExRate(BigDecimal exRate) {
        this.exRate = exRate;
    }

    public BigDecimal getChgAmtLocalCurrency() {
        return chgAmtLocalCurrency;
    }

    public void setChgAmtLocalCurrency(BigDecimal chgAmtLocalCurrency) {
        this.chgAmtLocalCurrency = chgAmtLocalCurrency;
    }

    public String getCommDate() {
        return commDate;
    }

    public void setCommDate(String commDate) {
        this.commDate = commDate;
    }

    public String getLstUpdate() {
        return lstUpdate;
    }

    public void setLstUpdate(String lstUpdate) {
        this.lstUpdate = lstUpdate;
    }
}
