package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class CalculationSheetMofaLevelHistoryDto implements Serializable {

    private String mofaUserId;
    private Integer mofaLevel;
    private String approvedDateTime;
    private BigDecimal fromLimit;
    private BigDecimal toLimit;

    public Integer getMofaLevel() {
        return mofaLevel;
    }

    public void setMofaLevel(Integer mofaLevel) {
        this.mofaLevel = mofaLevel;
    }

    public String getMofaUserId() {
        return mofaUserId;
    }

    public void setMofaUserId(String mofaUserId) {
        this.mofaUserId = mofaUserId;
    }

    public String getApprovedDateTime() {
        return approvedDateTime;
    }

    public void setApprovedDateTime(String approvedDateTime) {
        this.approvedDateTime = approvedDateTime;
    }

    public BigDecimal getFromLimit() {
        return fromLimit;
    }

    public void setFromLimit(BigDecimal fromLimit) {
        this.fromLimit = fromLimit;
    }

    public BigDecimal getToLimit() {
        return toLimit;
    }

    public void setToLimit(BigDecimal toLimit) {
        this.toLimit = toLimit;
    }
}
