package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class InvestigationDetailsFormDto implements Serializable {
    List<AssessorDto> assessorDtoList = new ArrayList<>();
    private InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
    private List<InvestigationDetailsDto> investigationDetailsDtoList = new ArrayList<>();
    private ClaimDocumentStatusDto investigationReportStatus;
    private List<ClaimDocumentDto> drivenLicenseDocumentList = new ArrayList<>();
    private List<ClaimDocumentDto> estimateDocumentList = new ArrayList<>();
    private List<ClaimDocumentDto> policeReportList = new ArrayList<>();
    private List<InvestigationSelectImageDto> investigationSelectImageDtoList = new ArrayList<>();
    private boolean selectedInvestigationImages = false;
    private ClaimHandlerDto investigationClaimHandlerDto;

    public InvestigationDetailsDto getInvestigationDetailsDto() {
        return investigationDetailsDto;
    }

    public void setInvestigationDetailsDto(InvestigationDetailsDto investigationDetailsDto) {
        this.investigationDetailsDto = investigationDetailsDto;
    }

    public List<InvestigationDetailsDto> getInvestigationDetailsDtoList() {
        return investigationDetailsDtoList;
    }

    public void setInvestigationDetailsDtoList(List<InvestigationDetailsDto> investigationDetailsDtoList) {
        this.investigationDetailsDtoList = investigationDetailsDtoList;
    }

    public ClaimDocumentStatusDto getInvestigationReportStatus() {
        return investigationReportStatus;
    }

    public void setInvestigationReportStatus(ClaimDocumentStatusDto investigationReportStatus) {
        this.investigationReportStatus = investigationReportStatus;
    }

    public List<AssessorDto> getAssessorDtoList() {
        return assessorDtoList;
    }

    public void setAssessorDtoList(List<AssessorDto> assessorDtoList) {
        this.assessorDtoList = assessorDtoList;
    }

    public List<ClaimDocumentDto> getDrivenLicenseDocumentList() {
        return drivenLicenseDocumentList;
    }

    public void setDrivenLicenseDocumentList(List<ClaimDocumentDto> drivenLicenseDocumentList) {
        this.drivenLicenseDocumentList = drivenLicenseDocumentList;
    }

    public List<ClaimDocumentDto> getEstimateDocumentList() {
        return estimateDocumentList;
    }

    public void setEstimateDocumentList(List<ClaimDocumentDto> estimateDocumentList) {
        this.estimateDocumentList = estimateDocumentList;
    }

    public List<ClaimDocumentDto> getPoliceReportList() {
        return policeReportList;
    }

    public void setPoliceReportList(List<ClaimDocumentDto> policeReportList) {
        this.policeReportList = policeReportList;
    }

    public List<InvestigationSelectImageDto> getInvestigationSelectImageDtoList() {
        return investigationSelectImageDtoList;
    }

    public void setInvestigationSelectImageDtoList(List<InvestigationSelectImageDto> investigationSelectImageDtoList) {
        this.investigationSelectImageDtoList = investigationSelectImageDtoList;
    }

    public boolean isSelectedInvestigationImages() {
        return selectedInvestigationImages;
    }

    public void setSelectedInvestigationImages(boolean selectedInvestigationImages) {
        this.selectedInvestigationImages = selectedInvestigationImages;
    }

    public ClaimHandlerDto getInvestigationClaimHandlerDto() {
        return investigationClaimHandlerDto;
    }

    public void setInvestigationClaimHandlerDto(ClaimHandlerDto investigationClaimHandlerDto) {
        this.investigationClaimHandlerDto = investigationClaimHandlerDto;
    }
}
