package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class NcbHistoryDetailsDto implements Serializable {
    private String policyNo;
    private String vehicleNo;
    private String policyYear;
    private Integer renewalCount;
    private Integer endorsementCount;
    private String status;
    private String inspecDate;
    private String expiryDate;
    private Integer ncbYear;
    private Integer ncbPer;

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getPolicyYear() {
        return policyYear;
    }

    public void setPolicyYear(String policyYear) {
        this.policyYear = policyYear;
    }

    public Integer getRenewalCount() {
        return renewalCount;
    }

    public void setRenewalCount(Integer renewalCount) {
        this.renewalCount = renewalCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public Integer getEndorsementCount() {
        return endorsementCount;
    }

    public void setEndorsementCount(Integer endorsementCount) {
        this.endorsementCount = endorsementCount;
    }

    public Integer getNcbYear() {
        return ncbYear;
    }

    public void setNcbYear(Integer ncbYear) {
        this.ncbYear = ncbYear;
    }

    public Integer getNcbPer() {
        return ncbPer;
    }

    public void setNcbPer(Integer ncbPer) {
        this.ncbPer = ncbPer;
    }

    public String getInspecDate() {
        return inspecDate;
    }

    public void setInspecDate(String inspecDate) {
        this.inspecDate = inspecDate;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }
}
