package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class DesktopInspectionDetailsDto implements Serializable {

    private Integer inspectionId = AppConstant.ZERO_INT;
    private int refNo = AppConstant.ZERO_INT;

    //Main
    private BigDecimal appCostReport = BigDecimal.ZERO;
    private BigDecimal preAccidentValue = BigDecimal.ZERO;
    private BigDecimal excess = BigDecimal.ZERO;
    private BigDecimal acr = BigDecimal.ZERO;
    private BigDecimal boldTyrePenaltyAmount = BigDecimal.ZERO;
    private BigDecimal underInsurancePenaltyAmount = BigDecimal.ZERO;
    private BigDecimal payableAmount = BigDecimal.ZERO;
    private ConditionType desktopOffer = ConditionType.No;
    private ConditionType ariSalvage = ConditionType.No;

    //Assessor Remarks
    private String settlementMethod = AppConstant.STRING_EMPTY; //Drop Down *Partial Loss *cash In lieu *Total Loss
    private String inspectionRemark = AppConstant.STRING_EMPTY;
    private ConditionType policeReportRequested = ConditionType.No;
    private String specialRemark = AppConstant.STRING_EMPTY;
    private ConditionType investigaedClaim = ConditionType.No;

    private BigDecimal professionalFee = BigDecimal.ZERO;
    private BigDecimal miles = BigDecimal.ZERO;
    private BigDecimal telephoneCharge = BigDecimal.ZERO;
    private BigDecimal otherCharge = BigDecimal.ZERO;
    private BigDecimal specialDeduction = BigDecimal.ZERO;
    private String reason = AppConstant.STRING_EMPTY;
    private BigDecimal totalCharge = BigDecimal.ZERO;

    private ConditionType provideOffer = ConditionType.No;
    private int offerType = AppConstant.ZERO_INT; //1 --> On-Site Offer | 2 --> Off-Site Offer
    private ClaimOfferTypeDto claimOfferTypeDto = new ClaimOfferTypeDto();

    private String informToGarageName = AppConstant.STRING_EMPTY;
    private String informToGarageContact = AppConstant.STRING_EMPTY;
    private String isAgreeGarage = AppConstant.STRING_EMPTY;
    private String informToCustomerName = AppConstant.STRING_EMPTY;
    private String informToCustomerContact = AppConstant.STRING_EMPTY;
    private String isAgreeCustomer = AppConstant.STRING_EMPTY;
    private String reasonForDisagree = AppConstant.STRING_EMPTY;
    private String isInformed = AppConstant.STRING_EMPTY;
    private String desktopComment = AppConstant.STRING_EMPTY;
    private String informedUser = AppConstant.STRING_EMPTY;
    private String informedDateTime = AppConstant.DEFAULT_DATE_TIME;
    private BigDecimal advancedAmount = BigDecimal.ZERO;
    private String advanceChange = AppConstant.ADD;

    private BigDecimal boldPercent = BigDecimal.ZERO;
    private BigDecimal underPenaltyPercent = BigDecimal.ZERO;

    private String isOnsitePending = AppConstant.STRING_EMPTY;

    private BigDecimal oldAcr = BigDecimal.ZERO;

    public BigDecimal getAppCostReport() {
        return appCostReport;
    }

    public void setAppCostReport(BigDecimal appCostReport) {
        this.appCostReport = appCostReport;
    }

    public BigDecimal getPreAccidentValue() {
        return preAccidentValue;
    }

    public void setPreAccidentValue(BigDecimal preAccidentValue) {
        this.preAccidentValue = preAccidentValue;
    }

    public BigDecimal getExcess() {
        return excess;
    }

    public void setExcess(BigDecimal excess) {
        this.excess = excess;
    }

    public BigDecimal getAcr() {
        return acr;
    }

    public void setAcr(BigDecimal acr) {
        this.acr = acr;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public String getSettlementMethod() {
        return settlementMethod;
    }

    public void setSettlementMethod(String settlementMethod) {
        this.settlementMethod = settlementMethod;
    }

    public String getInspectionRemark() {
        return inspectionRemark;
    }

    public void setInspectionRemark(String inspectionRemark) {
        this.inspectionRemark = inspectionRemark;
    }

    public ConditionType getInvestigaedClaim() {
        return investigaedClaim;
    }

    public void setInvestigaedClaim(ConditionType investigaedClaim) {
        this.investigaedClaim = investigaedClaim;
    }

    public BigDecimal getProfessionalFee() {
        return professionalFee;
    }

    public void setProfessionalFee(BigDecimal professionalFee) {
        this.professionalFee = professionalFee;
    }

    public BigDecimal getTelephoneCharge() {
        return telephoneCharge;
    }

    public void setTelephoneCharge(BigDecimal telephoneCharge) {
        this.telephoneCharge = telephoneCharge;
    }

    public BigDecimal getOtherCharge() {
        return otherCharge;
    }

    public void setOtherCharge(BigDecimal otherCharge) {
        this.otherCharge = otherCharge;
    }

    public BigDecimal getSpecialDeduction() {
        return specialDeduction;
    }

    public void setSpecialDeduction(BigDecimal specialDeduction) {
        this.specialDeduction = specialDeduction;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public BigDecimal getTotalCharge() {
        return totalCharge;
    }

    public void setTotalCharge(BigDecimal totalCharge) {
        this.totalCharge = totalCharge;
    }

    public Integer getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(Integer inspectionId) {
        this.inspectionId = inspectionId;
    }

    public BigDecimal getUnderInsurancePenaltyAmount() {
        return underInsurancePenaltyAmount;
    }

    public void setUnderInsurancePenaltyAmount(BigDecimal underInsurancePenaltyAmount) {
        this.underInsurancePenaltyAmount = underInsurancePenaltyAmount;
    }

    public ConditionType getDesktopOffer() {
        return desktopOffer;
    }

    public void setDesktopOffer(ConditionType desktopOffer) {
        this.desktopOffer = desktopOffer;
    }

    public ConditionType getAriSalvage() {
        return ariSalvage;
    }

    public void setAriSalvage(ConditionType ariSalvage) {
        this.ariSalvage = ariSalvage;
    }

    public ConditionType getPoliceReportRequested() {
        return policeReportRequested;
    }

    public void setPoliceReportRequested(ConditionType policeReportRequested) {
        this.policeReportRequested = policeReportRequested;
    }

    public String getSpecialRemark() {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
    }

    public BigDecimal getMiles() {
        return miles;
    }

    public void setMiles(BigDecimal miles) {
        this.miles = miles;
    }

    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public BigDecimal getBoldTyrePenaltyAmount() {
        return boldTyrePenaltyAmount;
    }

    public void setBoldTyrePenaltyAmount(BigDecimal boldTyrePenaltyAmount) {
        this.boldTyrePenaltyAmount = boldTyrePenaltyAmount;
    }

    public ConditionType getProvideOffer() {
        return provideOffer;
    }

    public void setProvideOffer(ConditionType provideOffer) {
        this.provideOffer = provideOffer;
    }

    public int getOfferType() {
        return offerType;
    }

    public void setOfferType(int offerType) {
        this.offerType = offerType;
    }

    public ClaimOfferTypeDto getClaimOfferTypeDto() {
        return claimOfferTypeDto;
    }

    public void setClaimOfferTypeDto(ClaimOfferTypeDto claimOfferTypeDto) {
        this.claimOfferTypeDto = claimOfferTypeDto;
    }

    public String getInformToGarageName() {
        return informToGarageName;
    }

    public void setInformToGarageName(String informToGarageName) {
        this.informToGarageName = informToGarageName;
    }

    public String getInformToGarageContact() {
        return informToGarageContact;
    }

    public void setInformToGarageContact(String informToGarageContact) {
        this.informToGarageContact = informToGarageContact;
    }

    public String getInformToCustomerName() {
        return informToCustomerName;
    }

    public void setInformToCustomerName(String informToCustomerName) {
        this.informToCustomerName = informToCustomerName;
    }

    public String getInformToCustomerContact() {
        return informToCustomerContact;
    }

    public void setInformToCustomerContact(String informToCustomerContact) {
        this.informToCustomerContact = informToCustomerContact;
    }

    public String getReasonForDisagree() {
        return reasonForDisagree;
    }

    public void setReasonForDisagree(String reasonForDisagree) {
        this.reasonForDisagree = reasonForDisagree;
    }

    public String getIsInformed() {
        return isInformed;
    }

    public void setIsInformed(String isInformed) {
        this.isInformed = isInformed;
    }

    public String getDesktopComment() {
        return desktopComment;
    }

    public void setDesktopComment(String desktopComment) {
        this.desktopComment = desktopComment;
    }

    public String getInformedUser() {
        return informedUser;
    }

    public void setInformedUser(String informedUser) {
        this.informedUser = informedUser;
    }

    public String getInformedDateTime() {
        return informedDateTime;
    }

    public void setInformedDateTime(String informedDateTime) {
        this.informedDateTime = informedDateTime;
    }

    public String getIsAgreeGarage() {
        return isAgreeGarage;
    }

    public void setIsAgreeGarage(String isAgreeGarage) {
        this.isAgreeGarage = isAgreeGarage;
    }

    public String getIsAgreeCustomer() {
        return isAgreeCustomer;
    }

    public void setIsAgreeCustomer(String isAgreeCustomer) {
        this.isAgreeCustomer = isAgreeCustomer;
    }

    public BigDecimal getBoldPercent() {
        return boldPercent;
    }

    public void setBoldPercent(BigDecimal boldPercent) {
        this.boldPercent = boldPercent;
    }

    public BigDecimal getUnderPenaltyPercent() {
        return underPenaltyPercent;
    }

    public void setUnderPenaltyPercent(BigDecimal underPenaltyPercent) {
        this.underPenaltyPercent = underPenaltyPercent;
    }

    public BigDecimal getAdvancedAmount() {
        return advancedAmount;
    }

    public void setAdvancedAmount(BigDecimal advancedAmount) {
        this.advancedAmount = advancedAmount;
    }

    public String getAdvanceChange() {
        return advanceChange;
    }

    public void setAdvanceChange(String advanceChange) {
        this.advanceChange = advanceChange;
    }

    public String getIsOnsitePending() {
        return isOnsitePending;
    }

    public void setIsOnsitePending(String isOnsitePending) {
        this.isOnsitePending = isOnsitePending;
    }

    public BigDecimal getOldAcr() {
        return oldAcr;
    }

    public void setOldAcr(BigDecimal oldAcr) {
        this.oldAcr = oldAcr;
    }
}
