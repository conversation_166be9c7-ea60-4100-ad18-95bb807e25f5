package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class PolicyCoverRequestDto implements Serializable {
   private String polNumber;
    private String  polChannelType;
    private int  renCount;
    private int  endCount;

    public PolicyCoverRequestDto(String polNumber, String polChannelType, int renCount, int endCount) {
        this.polNumber = polNumber;
        this.polChannelType = polChannelType;
        this.renCount = renCount;
        this.endCount = endCount;
    }

    public PolicyCoverRequestDto() {
    }

    public String getPolNumber() {
        return polNumber;
    }

    public void setPolNumber(String polNumber) {
        this.polNumber = polNumber;
    }

    public String getPolChannelType() {
        return polChannelType;
    }

    public void setPolChannelType(String polChannelType) {
        this.polChannelType = polChannelType;
    }

    public int getRenCount() {
        return renCount;
    }

    public void setRenCount(int renCount) {
        this.renCount = renCount;
    }

    public int getEndCount() {
        return endCount;
    }

    public void setEndCount(int endCount) {
        this.endCount = endCount;
    }
}
