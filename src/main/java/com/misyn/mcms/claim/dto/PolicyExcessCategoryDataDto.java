package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class PolicyExcessCategoryDataDto implements Serializable {
    private String code= AppConstant.STRING_EMPTY;
    private String excessDesc= AppConstant.STRING_EMPTY;
    private BigDecimal excessAmount= BigDecimal.ZERO;

    public PolicyExcessCategoryDataDto(String code, String excessDesc, BigDecimal excessAmount) {
        this.code = code;
        this.excessDesc = excessDesc;
        this.excessAmount = excessAmount;
    }

    public PolicyExcessCategoryDataDto() {
    }

    public String getExcessDesc() {
        return excessDesc;
    }

    public void setExcessDesc(String excessDesc) {
        this.excessDesc = excessDesc;
    }

    public BigDecimal getExcessAmount() {
        return excessAmount;
    }

    public void setExcessAmount(BigDecimal excessAmount) {
        this.excessAmount = excessAmount;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
