package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class UserAuthorityLimitDto implements Serializable {
    private Integer levelId;
    private int levelCode;
    private String levelName;
    private int departmentId;
    private BigDecimal fromLimit;
    private BigDecimal toLimit;
    private int logMofaUserLevelCode;

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public int getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(int departmentId) {
        this.departmentId = departmentId;
    }

    public BigDecimal getFromLimit() {
        return fromLimit;
    }

    public void setFromLimit(BigDecimal fromLimit) {
        this.fromLimit = fromLimit;
    }

    public BigDecimal getToLimit() {
        return toLimit;
    }

    public void setToLimit(BigDecimal toLimit) {
        this.toLimit = toLimit;
    }

    public int getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(int levelCode) {
        this.levelCode = levelCode;
    }

    public int getLogMofaUserLevelCode() {
        return logMofaUserLevelCode;
    }

    public void setLogMofaUserLevelCode(int logMofaUserLevelCode) {
        this.logMofaUserLevelCode = logMofaUserLevelCode;
    }
}
