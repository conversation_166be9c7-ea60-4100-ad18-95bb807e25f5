package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class RejectReasonNewDto implements Serializable {
    private int reasonId = AppConstant.ZERO_INT;
    private String rejectReasom = AppConstant.STRING_EMPTY;
    private String recordStatus = AppConstant.STRING_EMPTY;

    public int getReasonId() {
        return reasonId;
    }

    public void setReasonId(int reasonId) {
        this.reasonId = reasonId;
    }

    public String getRejectReasom() {
        return rejectReasom;
    }

    public void setRejectReasom(String rejectReasom) {
        this.rejectReasom = rejectReasom;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }
}
