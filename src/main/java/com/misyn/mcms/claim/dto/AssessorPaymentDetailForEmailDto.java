package com.misyn.mcms.claim.dto;

import java.math.BigDecimal;
public class AssessorPaymentDetailForEmailDto {
    private Integer keyId;
    private Integer refNo;
    private Integer claimNo;
    private String registrationNo;
    private String jobDesc;
    private Integer jobType;
    private String voucherNo;
    private String voucherDate;
    private BigDecimal costOfCall;
    private BigDecimal otherCharges;
    private BigDecimal deductionFee;
    private BigDecimal professionalFee;
    private BigDecimal travelFee;
    private BigDecimal voucherAmount;
    private String approveDatetime;
    private String approveUser;
    private String fromDate;
    private String toDate;
    private String name;
    private BigDecimal beforeDeductionScheduleAmount;
    private BigDecimal scheduleAmount;
    private String inputUser;
    private String policyChannelType;
    private String isfClaimNo;

    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getRegistrationNo() {
        return registrationNo;
    }

    public void setRegistrationNo(String registrationNo) {
        this.registrationNo = registrationNo;
    }

    public String getJobDesc() {
        return jobDesc;
    }

    public void setJobDesc(String jobDesc) {
        this.jobDesc = jobDesc;
    }

    public Integer getJobType() {
        return jobType;
    }

    public void setJobType(Integer jobType) {
        this.jobType = jobType;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getVoucherDate() {
        return voucherDate;
    }

    public void setVoucherDate(String voucherDate) {
        this.voucherDate = voucherDate;
    }

    public BigDecimal getCostOfCall() {
        return costOfCall;
    }

    public void setCostOfCall(BigDecimal costOfCall) {
        this.costOfCall = costOfCall;
    }

    public BigDecimal getOtherCharges() {
        return otherCharges;
    }

    public void setOtherCharges(BigDecimal otherCharges) {
        this.otherCharges = otherCharges;
    }

    public BigDecimal getDeductionFee() {
        return deductionFee;
    }

    public void setDeductionFee(BigDecimal deductionFee) {
        this.deductionFee = deductionFee;
    }

    public BigDecimal getProfessionalFee() {
        return professionalFee;
    }

    public void setProfessionalFee(BigDecimal professionalFee) {
        this.professionalFee = professionalFee;
    }

    public BigDecimal getTravelFee() {
        return travelFee;
    }

    public void setTravelFee(BigDecimal travelFee) {
        this.travelFee = travelFee;
    }

    public BigDecimal getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(BigDecimal voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public String getApproveDatetime() {
        return approveDatetime;
    }

    public void setApproveDatetime(String approveDatetime) {
        this.approveDatetime = approveDatetime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getApproveUser() {
        return approveUser;
    }

    public void setApproveUser(String approveUser) {
        this.approveUser = approveUser;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public BigDecimal getBeforeDeductionScheduleAmount() {
        return beforeDeductionScheduleAmount;
    }

    public void setBeforeDeductionScheduleAmount(BigDecimal beforeDeductionScheduleAmount) {
        this.beforeDeductionScheduleAmount = beforeDeductionScheduleAmount;
    }

    public BigDecimal getScheduleAmount() {
        return scheduleAmount;
    }

    public void setScheduleAmount(BigDecimal scheduleAmount) {
        this.scheduleAmount = scheduleAmount;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }

    public String getIsfClaimNo() {
        return isfClaimNo;
    }

    public void setIsfClaimNo(String isfClaimNo) {
        this.isfClaimNo = isfClaimNo;
    }
}
