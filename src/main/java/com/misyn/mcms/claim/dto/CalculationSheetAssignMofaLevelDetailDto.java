package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class CalculationSheetAssignMofaLevelDetailDto implements Serializable {
    private Integer txnId;
    private Integer calsheetId;
    private Integer claimNo;
    private String inputUserId;
    private int inputMofaLevel;
    private String assignUserId;
    private int assignMofaLevel;
    private String assignDatetime;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getCalsheetId() {
        return calsheetId;
    }

    public void setCalsheetId(Integer calsheetId) {
        this.calsheetId = calsheetId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(String inputUserId) {
        this.inputUserId = inputUserId;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public int getInputMofaLevel() {
        return inputMofaLevel;
    }

    public void setInputMofaLevel(int inputMofaLevel) {
        this.inputMofaLevel = inputMofaLevel;
    }

    public int getAssignMofaLevel() {
        return assignMofaLevel;
    }

    public void setAssignMofaLevel(int assignMofaLevel) {
        this.assignMofaLevel = assignMofaLevel;
    }

    public String getAssignDatetime() {
        return assignDatetime;
    }

    public void setAssignDatetime(String assignDatetime) {
        this.assignDatetime = assignDatetime;
    }
}
