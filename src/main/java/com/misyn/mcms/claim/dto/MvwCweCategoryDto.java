package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class MvwCweCategoryDto implements Serializable {
    private String polNumber= AppConstant.EMPTY_STRING;
    private Integer renCount=AppConstant.ZERO_INT;
    private Integer endCount=AppConstant.ZERO_INT;
    private String code= AppConstant.EMPTY_STRING;
    private String text= AppConstant.EMPTY_STRING;
    private String headPrtFlag= AppConstant.EMPTY_STRING;
    private String textPrtFlag= AppConstant.EMPTY_STRING;
    private Integer order=AppConstant.ZERO_INT;
    private String lstUpdUser= AppConstant.EMPTY_STRING;
    private String lstUpdProg= AppConstant.EMPTY_STRING;
    private String lstUpdate= AppConstant.EMPTY_STRING;
    private String expiryDate= AppConstant.EMPTY_STRING;
    private String commDate= AppConstant.EMPTY_STRING;
    private String sessionFlag= AppConstant.EMPTY_STRING;
    private String dltRecFlag= AppConstant.EMPTY_STRING;

    public MvwCweCategoryDto(String polNumber, Integer renCount, Integer endCount, String code, String text, String headPrtFlag, String textPrtFlag, Integer order, String lstUpdUser, String lstUpdProg, String lstUpdate, String expiryDate, String commDate, String sessionFlag, String dltRecFlag) {
        this.polNumber = polNumber;
        this.renCount = renCount;
        this.endCount = endCount;
        this.code = code;
        this.text = text;
        this.headPrtFlag = headPrtFlag;
        this.textPrtFlag = textPrtFlag;
        this.order = order;
        this.lstUpdUser = lstUpdUser;
        this.lstUpdProg = lstUpdProg;
        this.lstUpdate = lstUpdate;
        this.expiryDate = expiryDate;
        this.commDate = commDate;
        this.sessionFlag = sessionFlag;
        this.dltRecFlag = dltRecFlag;
    }

    public MvwCweCategoryDto() {
    }

    public String getPolNumber() {
        return polNumber;
    }

    public void setPolNumber(String polNumber) {
        this.polNumber = polNumber;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getHeadPrtFlag() {
        return headPrtFlag;
    }

    public void setHeadPrtFlag(String headPrtFlag) {
        this.headPrtFlag = headPrtFlag;
    }

    public String getTextPrtFlag() {
        return textPrtFlag;
    }

    public void setTextPrtFlag(String textPrtFlag) {
        this.textPrtFlag = textPrtFlag;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getLstUpdUser() {
        return lstUpdUser;
    }

    public void setLstUpdUser(String lstUpdUser) {
        this.lstUpdUser = lstUpdUser;
    }

    public String getLstUpdProg() {
        return lstUpdProg;
    }

    public void setLstUpdProg(String lstUpdProg) {
        this.lstUpdProg = lstUpdProg;
    }

    public String getLstUpdate() {
        return lstUpdate;
    }

    public void setLstUpdate(String lstUpdate) {
        this.lstUpdate = lstUpdate;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getCommDate() {
        return commDate;
    }

    public void setCommDate(String commDate) {
        this.commDate = commDate;
    }

    public String getSessionFlag() {
        return sessionFlag;
    }

    public void setSessionFlag(String sessionFlag) {
        this.sessionFlag = sessionFlag;
    }

    public String getDltRecFlag() {
        return dltRecFlag;
    }

    public void setDltRecFlag(String dltRecFlag) {
        this.dltRecFlag = dltRecFlag;
    }
}
