/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class ClaimCalculationSheetTypeDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer nCalSheetTypeId;
    private String vCalSheetTypeDesc;
    private String code;

    public ClaimCalculationSheetTypeDto() {
    }

    public ClaimCalculationSheetTypeDto(Integer nCalSheetTypeId) {
        this.nCalSheetTypeId = nCalSheetTypeId;
    }

    public ClaimCalculationSheetTypeDto(Integer nCalSheetTypeId, String vCalSheetTypeDesc) {
        this.nCalSheetTypeId = nCalSheetTypeId;
        this.vCalSheetTypeDesc = vCalSheetTypeDesc;
    }

    public Integer getNCalSheetTypeId() {
        return nCalSheetTypeId;
    }

    public void setNCalSheetTypeId(Integer nCalSheetTypeId) {
        this.nCalSheetTypeId = nCalSheetTypeId;
    }

    public String getVCalSheetTypeDesc() {
        return vCalSheetTypeDesc;
    }

    public void setVCalSheetTypeDesc(String vCalSheetTypeDesc) {
        this.vCalSheetTypeDesc = vCalSheetTypeDesc;
    }

    public Integer getnCalSheetTypeId() {
        return nCalSheetTypeId;
    }

    public void setnCalSheetTypeId(Integer nCalSheetTypeId) {
        this.nCalSheetTypeId = nCalSheetTypeId;
    }

    public String getvCalSheetTypeDesc() {
        return vCalSheetTypeDesc;
    }

    public void setvCalSheetTypeDesc(String vCalSheetTypeDesc) {
        this.vCalSheetTypeDesc = vCalSheetTypeDesc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
