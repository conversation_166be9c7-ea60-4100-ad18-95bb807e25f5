package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.DocumentStatusEnum;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ClaimDocumentStatusDto implements Serializable {
    private DocumentStatusEnum documentStatusEnum;
    private List<ClaimDocumentDto> claimDocumentDtoList = new ArrayList<>();

    public DocumentStatusEnum getDocumentStatusEnum() {
        return documentStatusEnum;
    }

    public void setDocumentStatusEnum(DocumentStatusEnum documentStatusEnum) {
        this.documentStatusEnum = documentStatusEnum;
    }

    public List<ClaimDocumentDto> getClaimDocumentDtoList() {
        return claimDocumentDtoList;
    }

    public void setClaimDocumentDtoList(List<ClaimDocumentDto> claimDocumentDtoList) {
        this.claimDocumentDtoList = claimDocumentDtoList;
    }
}
