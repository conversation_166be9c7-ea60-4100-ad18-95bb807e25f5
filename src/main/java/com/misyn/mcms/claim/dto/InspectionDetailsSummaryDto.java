package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class InspectionDetailsSummaryDto implements Serializable {
    private Integer refNo;
    private String jobRefNo;
    private Integer inspectionId;
    private String inspectionDesc;
    private BigDecimal acr;
    private int status;
    private String statusDesc;
    private String consistency;

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public String getJobRefNo() {
        return jobRefNo;
    }

    public void setJobRefNo(String jobRefNo) {
        this.jobRefNo = jobRefNo;
    }

    public Integer getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(Integer inspectionId) {
        this.inspectionId = inspectionId;
    }

    public String getInspectionDesc() {
        return inspectionDesc;
    }

    public void setInspectionDesc(String inspectionDesc) {
        this.inspectionDesc = inspectionDesc;
    }

    public BigDecimal getAcr() {
        return acr;
    }

    public void setAcr(BigDecimal acr) {
        this.acr = acr;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getConsistency() {
        return consistency;
    }

    public void setConsistency(String consistency) {
        this.consistency = consistency;
    }
}
