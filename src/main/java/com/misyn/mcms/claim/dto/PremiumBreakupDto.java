package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class PremiumBreakupDto implements Serializable {
    private Integer refNo;
    private String policyNo;
    private Integer renCount;
    private Integer endCount;
    private BigDecimal actualBasic;
    private BigDecimal addBenefit;
    private BigDecimal loadAmt;
    private BigDecimal discAmt;
    private BigDecimal ncdAmt;
    private BigDecimal grossPrem;
    private BigDecimal cess;
    private BigDecimal nbt;
    private BigDecimal pof;
    private BigDecimal rt;
    private BigDecimal sd;
    private BigDecimal vat;
    private BigDecimal otherChrg;
    private String inputUser;
    private String inputDateTime;

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public BigDecimal getActualBasic() {
        return actualBasic;
    }

    public void setActualBasic(BigDecimal actualBasic) {
        this.actualBasic = actualBasic;
    }

    public BigDecimal getAddBenefit() {
        return addBenefit;
    }

    public void setAddBenefit(BigDecimal addBenefit) {
        this.addBenefit = addBenefit;
    }

    public BigDecimal getLoadAmt() {
        return loadAmt;
    }

    public void setLoadAmt(BigDecimal loadAmt) {
        this.loadAmt = loadAmt;
    }

    public BigDecimal getDiscAmt() {
        return discAmt;
    }

    public void setDiscAmt(BigDecimal discAmt) {
        this.discAmt = discAmt;
    }

    public BigDecimal getNcdAmt() {
        return ncdAmt;
    }

    public void setNcdAmt(BigDecimal ncdAmt) {
        this.ncdAmt = ncdAmt;
    }

    public BigDecimal getGrossPrem() {
        return grossPrem;
    }

    public void setGrossPrem(BigDecimal grossPrem) {
        this.grossPrem = grossPrem;
    }

    public BigDecimal getCess() {
        return cess;
    }

    public void setCess(BigDecimal cess) {
        this.cess = cess;
    }

    public BigDecimal getNbt() {
        return nbt;
    }

    public void setNbt(BigDecimal nbt) {
        this.nbt = nbt;
    }

    public BigDecimal getPof() {
        return pof;
    }

    public void setPof(BigDecimal pof) {
        this.pof = pof;
    }

    public BigDecimal getRt() {
        return rt;
    }

    public void setRt(BigDecimal rt) {
        this.rt = rt;
    }

    public BigDecimal getSd() {
        return sd;
    }

    public void setSd(BigDecimal sd) {
        this.sd = sd;
    }

    public BigDecimal getVat() {
        return vat;
    }

    public void setVat(BigDecimal vat) {
        this.vat = vat;
    }

    public BigDecimal getOtherChrg() {
        return otherChrg;
    }

    public void setOtherChrg(BigDecimal otherChrg) {
        this.otherChrg = otherChrg;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(String inputDateTime) {
        this.inputDateTime = inputDateTime;
    }
}
