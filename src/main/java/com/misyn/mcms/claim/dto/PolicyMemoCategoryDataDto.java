package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class PolicyMemoCategoryDataDto implements Serializable {
    private String no = AppConstant.STRING_EMPTY;
    private String memo = AppConstant.STRING_EMPTY;
    private String date = AppConstant.STRING_EMPTY;
    private String exclusion = AppConstant.STRING_EMPTY;
    private String more = AppConstant.STRING_EMPTY;
    private boolean deleted= false;

    public PolicyMemoCategoryDataDto(String no, String memo, String date, String exclusion, String more, boolean deleted) {
        this.no = no;
        this.memo = memo;
        this.date = date;
        this.exclusion = exclusion;
        this.more = more;
        this.deleted = deleted;
    }

    public PolicyMemoCategoryDataDto(String no, String memo, String date, String exclusion, String more) {
        this.no = no;
        this.memo = memo;
        this.date = date;
        this.exclusion = exclusion;
        this.more = more;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getExclusion() {
        return exclusion;
    }

    public void setExclusion(String exclusion) {
        this.exclusion = exclusion;
    }

    public String getMore() {
        return more;
    }

    public void setMore(String more) {
        this.more = more;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }
}
