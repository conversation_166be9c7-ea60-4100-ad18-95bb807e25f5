package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class DocumentNotificationDto implements Serializable {
    private Integer txnId;
    private Long formRelId;
    private Integer claimNo;
    private Integer docType;
    private String updateDate;
    private String inputUser;
    private String assignUser;
    private String status;
    private String docName = AppConstant.STRING_EMPTY;
    private Integer count = AppConstant.ZERO_INT;

    public DocumentNotificationDto() {
    }

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Long getFormRelId() {
        return formRelId;
    }

    public void setFormRelId(Long formRelId) {
        this.formRelId = formRelId;
    }

    public Integer getDocType() {
        return docType;
    }

    public void setDocType(Integer docType) {
        this.docType = docType;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getAssignUser() {
        return assignUser;
    }

    public void setAssignUser(String assignUser) {
        this.assignUser = assignUser;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
