package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ClaimUploadViewDto implements Serializable {
    private Integer refNo = AppConstant.ZERO_INT;
    private Integer jobRefNo = AppConstant.ZERO_INT;
    private Integer claimNo = AppConstant.ZERO_INT;
    private ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
    private List<ClaimDocumentDto> claimDocumentDtoList = new ArrayList<>();


    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getJobRefNo() {
        return jobRefNo;
    }

    public void setJobRefNo(Integer jobRefNo) {
        this.jobRefNo = jobRefNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public List<ClaimDocumentDto> getClaimDocumentDtoList() {
        return claimDocumentDtoList;
    }

    public void setClaimDocumentDtoList(List<ClaimDocumentDto> claimDocumentDtoList) {
        this.claimDocumentDtoList = claimDocumentDtoList;
    }

    public ClaimDocumentTypeDto getClaimDocumentTypeDto() {
        return claimDocumentTypeDto;
    }

    public void setClaimDocumentTypeDto(ClaimDocumentTypeDto claimDocumentTypeDto) {
        this.claimDocumentTypeDto = claimDocumentTypeDto;
    }
}
