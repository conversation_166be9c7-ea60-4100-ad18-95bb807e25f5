package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ClaimDocumentTypeDto implements Serializable {
    private Integer documentTypeId;
    private String documentTypeName;
    private Integer departmentId;
    private String isMandatory;
    private String isPartialLoss;
    private String isTotLoss;
    private String isLumpSum;
    private String isThirdPartyPropVehicle;
    private String isThirdPartyDeath;
    private String isThirdPartyInjuries;
    private Integer docReqFrom;
    private String reminderDocDisplayName;
    private Integer sequenceOrder = AppConstant.ZERO_INT;
    private String branchAllow = AppConstant.NO;
    private String recStatus = AppConstant.STRING_EMPTY;
    private String inpUserId = AppConstant.STRING_EMPTY;
    private String inpDateTime = AppConstant.DEFAULT_DATE_TIME;
    private Integer index;
    private List<ClaimDepartmentDto> claimDepartmentDtoList = new ArrayList<>();
    private List<DocReqFormDto> docReqFormDtoList = new ArrayList();


    public Integer getDocumentTypeId() {
        return documentTypeId;
    }

    public void setDocumentTypeId(Integer documentTypeId) {
        this.documentTypeId = documentTypeId;
    }

    public String getDocumentTypeName() {
        return documentTypeName;
    }

    public void setDocumentTypeName(String documentTypeName) {
        this.documentTypeName = documentTypeName;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(String isMandatory) {
        this.isMandatory = isMandatory;
    }

    public String getIsPartialLoss() {
        return isPartialLoss;
    }

    public void setIsPartialLoss(String isPartialLoss) {
        this.isPartialLoss = isPartialLoss;
    }

    public String getIsTotLoss() {
        return isTotLoss;
    }

    public void setIsTotLoss(String isTotLoss) {
        this.isTotLoss = isTotLoss;
    }

    public String getIsLumpSum() {
        return isLumpSum;
    }

    public void setIsLumpSum(String isLumpSum) {
        this.isLumpSum = isLumpSum;
    }

    public Integer getDocReqFrom() {
        return docReqFrom;
    }

    public void setDocReqFrom(Integer docReqFrom) {
        this.docReqFrom = docReqFrom;
    }


    public String getIsThirdPartyPropVehicle() {
        return isThirdPartyPropVehicle;
    }

    public void setIsThirdPartyPropVehicle(String isThirdPartyPropVehicle) {
        this.isThirdPartyPropVehicle = isThirdPartyPropVehicle;
    }

    public String getIsThirdPartyDeath() {
        return isThirdPartyDeath;
    }

    public void setIsThirdPartyDeath(String isThirdPartyDeath) {
        this.isThirdPartyDeath = isThirdPartyDeath;
    }

    public String getIsThirdPartyInjuries() {
        return isThirdPartyInjuries;
    }

    public void setIsThirdPartyInjuries(String isThirdPartyInjuries) {
        this.isThirdPartyInjuries = isThirdPartyInjuries;
    }

    public String getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(String recStatus) {
        this.recStatus = recStatus;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }

    public String getReminderDocDisplayName() {
        return reminderDocDisplayName;
    }

    public void setReminderDocDisplayName(String reminderDocDisplayName) {
        this.reminderDocDisplayName = reminderDocDisplayName;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }


    public List<ClaimDepartmentDto> getClaimDepartmentDtoList() {
        return claimDepartmentDtoList;
    }

    public void setClaimDepartmentDtoList(List<ClaimDepartmentDto> claimDepartmentDtoList) {
        this.claimDepartmentDtoList = claimDepartmentDtoList;
    }

    public List<DocReqFormDto> getDocReqFormDtoList() {
        return docReqFormDtoList;
    }

    public void setDocReqFormDtoList(List<DocReqFormDto> docReqFormDtoList) {
        this.docReqFormDtoList = docReqFormDtoList;
    }

    public Integer getSequenceOrder() {
        return sequenceOrder;
    }

    public void setSequenceOrder(Integer sequenceOrder) {
        this.sequenceOrder = sequenceOrder;
    }

    public String getBranchAllow() {
        return branchAllow;
    }

    public void setBranchAllow(String branchAllow) {
        this.branchAllow = branchAllow;
    }
}
