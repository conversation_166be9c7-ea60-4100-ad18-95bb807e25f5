package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class GarageInspectionDetailsDto implements Serializable {

    //Approximate Cost Of Repair
    private int inspectionId = AppConstant.ZERO_INT;
    private int refNo = AppConstant.ZERO_INT;
    private BigDecimal excess = BigDecimal.ZERO;
    private BigDecimal acr = BigDecimal.ZERO;
    private BigDecimal boldTyrePenaltyAmount = BigDecimal.ZERO;
    private BigDecimal underInsurancePenaltyAmount = BigDecimal.ZERO;
    private BigDecimal payableAmount = BigDecimal.ZERO;
    private BigDecimal advancedAmount = BigDecimal.ZERO;
    private String advanceChange = AppConstant.ADD;

    //Assessor Remarks
    private String settlementMethod = AppConstant.ZERO;//Drop Down -*Repair Basis *Total Loss *Cash In Lieu
    private BigDecimal offerAmount = BigDecimal.ZERO;
    private String inspectionRemark = AppConstant.STRING_EMPTY;
    private ConditionType policeReportRequested = ConditionType.No;
    private String specialRemark = AppConstant.STRING_EMPTY;
    private ConditionType investigateClaim = ConditionType.No;
    private ConditionType ariAndSalvage = ConditionType.No;
    private BigDecimal preAccidentValue = BigDecimal.ZERO;

    //Assessor Payment
    private BigDecimal professionalFee = BigDecimal.ZERO;
    private BigDecimal miles = BigDecimal.ZERO;
    private BigDecimal telephoneCharge = BigDecimal.ZERO;
    private BigDecimal otherCharge = BigDecimal.ZERO;
    private BigDecimal specialDeduction = BigDecimal.ZERO;
    private String reason = AppConstant.STRING_EMPTY;
    private BigDecimal totalCharge = BigDecimal.ZERO;

    private BigDecimal boldPercent = BigDecimal.ZERO;
    private BigDecimal underPenaltyPercent = BigDecimal.ZERO;

    private String isOnsitePending = AppConstant.STRING_EMPTY;

    private BigDecimal oldAcr = BigDecimal.ZERO;

    public int getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(int inspectionId) {
        this.inspectionId = inspectionId;
    }

    public BigDecimal getExcess() {
        return excess;
    }

    public void setExcess(BigDecimal excess) {
        this.excess = excess;
    }

    public BigDecimal getAcr() {
        return acr;
    }

    public void setAcr(BigDecimal acr) {
        this.acr = acr;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    public BigDecimal getAdvancedAmount() {
        return advancedAmount;
    }

    public void setAdvancedAmount(BigDecimal advancedAmount) {
        this.advancedAmount = advancedAmount;
    }

    public String getSettlementMethod() {
        return settlementMethod;
    }

    public void setSettlementMethod(String settlementMethod) {
        this.settlementMethod = settlementMethod;
    }

    public BigDecimal getOfferAmount() {
        return offerAmount;
    }

    public void setOfferAmount(BigDecimal offerAmount) {
        this.offerAmount = offerAmount;
    }

    public String getInspectionRemark() {
        return inspectionRemark;
    }

    public void setInspectionRemark(String inspectionRemark) {
        this.inspectionRemark = inspectionRemark;
    }

    public ConditionType getAriAndSalvage() {
        return ariAndSalvage;
    }

    public void setAriAndSalvage(ConditionType ariAndSalvage) {
        this.ariAndSalvage = ariAndSalvage;
    }

    public BigDecimal getPreAccidentValue() {
        return preAccidentValue;
    }

    public void setPreAccidentValue(BigDecimal preAccidentValue) {
        this.preAccidentValue = preAccidentValue;
    }

    public BigDecimal getProfessionalFee() {
        return professionalFee;
    }

    public void setProfessionalFee(BigDecimal professionalFee) {
        this.professionalFee = professionalFee;
    }

    public BigDecimal getTelephoneCharge() {
        return telephoneCharge;
    }

    public void setTelephoneCharge(BigDecimal telephoneCharge) {
        this.telephoneCharge = telephoneCharge;
    }

    public BigDecimal getOtherCharge() {
        return otherCharge;
    }

    public void setOtherCharge(BigDecimal otherCharge) {
        this.otherCharge = otherCharge;
    }

    public BigDecimal getSpecialDeduction() {
        return specialDeduction;
    }

    public void setSpecialDeduction(BigDecimal specialDeduction) {
        this.specialDeduction = specialDeduction;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public BigDecimal getTotalCharge() {
        return totalCharge;
    }

    public void setTotalCharge(BigDecimal totalCharge) {
        this.totalCharge = totalCharge;
    }

    public BigDecimal getUnderInsurancePenaltyAmount() {
        return underInsurancePenaltyAmount;
    }

    public void setUnderInsurancePenaltyAmount(BigDecimal underInsurancePenaltyAmount) {
        this.underInsurancePenaltyAmount = underInsurancePenaltyAmount;
    }

    public ConditionType getPoliceReportRequested() {
        return policeReportRequested;
    }

    public void setPoliceReportRequested(ConditionType policeReportRequested) {
        this.policeReportRequested = policeReportRequested;
    }

    public String getSpecialRemark() {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark) {
        this.specialRemark = specialRemark;
    }

    public ConditionType getInvestigateClaim() {
        return investigateClaim;
    }

    public void setInvestigateClaim(ConditionType investigateClaim) {
        this.investigateClaim = investigateClaim;
    }

    public BigDecimal getMiles() {
        return miles;
    }

    public void setMiles(BigDecimal miles) {
        this.miles = miles;
    }

    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public BigDecimal getBoldTyrePenaltyAmount() {
        return boldTyrePenaltyAmount;
    }

    public void setBoldTyrePenaltyAmount(BigDecimal boldTyrePenaltyAmount) {
        this.boldTyrePenaltyAmount = boldTyrePenaltyAmount;
    }

    public BigDecimal getBoldPercent() {
        return boldPercent;
    }

    public void setBoldPercent(BigDecimal boldPercent) {
        this.boldPercent = boldPercent;
    }

    public BigDecimal getUnderPenaltyPercent() {
        return underPenaltyPercent;
    }

    public void setUnderPenaltyPercent(BigDecimal underPenaltyPercent) {
        this.underPenaltyPercent = underPenaltyPercent;
    }

    public String getAdvanceChange() {
        return advanceChange;
    }

    public void setAdvanceChange(String advanceChange) {
        this.advanceChange = advanceChange;
    }

    public String getIsOnsitePending() {
        return isOnsitePending;
    }

    public void setIsOnsitePending(String isOnsitePending) {
        this.isOnsitePending = isOnsitePending;
    }

    public BigDecimal getOldAcr() {
        return oldAcr;
    }

    public void setOldAcr(BigDecimal oldAcr) {
        this.oldAcr = oldAcr;
    }
}
