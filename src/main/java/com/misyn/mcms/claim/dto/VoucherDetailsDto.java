package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class VoucherDetailsDto implements Serializable {

    private String voucherNo;
    private String sourceRefNo;
    private String vehicleRegNo;
    private String payeeName;
    private BigDecimal voucherAmount;
    private String statusDesc;
    private String lastUpdatedUser;
    private String voucherDate;
    private String payeeType;
    private String instrumentType;
    private String bankCode;
    private String branchCode;
    private String bankAccountNo;
    private String claimNo;
    private String chequeNumber;
    private String chequeDate;
    private int financeReasonTypeId = 0;
    private ClaimPaymentDispatchDto claimPaymentDispatchDto = new ClaimPaymentDispatchDto();

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getSourceRefNo() {
        return sourceRefNo;
    }

    public void setSourceRefNo(String sourceRefNo) {
        this.sourceRefNo = sourceRefNo;
    }

    public String getVehicleRegNo() {
        return vehicleRegNo;
    }

    public void setVehicleRegNo(String vehicleRegNo) {
        this.vehicleRegNo = vehicleRegNo;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public BigDecimal getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(BigDecimal voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getLastUpdatedUser() {
        return lastUpdatedUser;
    }

    public void setLastUpdatedUser(String lastUpdatedUser) {
        this.lastUpdatedUser = lastUpdatedUser;
    }

    public String getVoucherDate() {
        return voucherDate;
    }

    public void setVoucherDate(String voucherDate) {
        this.voucherDate = voucherDate;
    }

    public String getPayeeType() {
        return payeeType;
    }

    public void setPayeeType(String payeeType) {
        this.payeeType = payeeType;
    }

    public String getInstrumentType() {
        return instrumentType;
    }

    public void setInstrumentType(String instrumentType) {
        this.instrumentType = instrumentType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getChequeNumber() {
        return chequeNumber;
    }

    public void setChequeNumber(String chequeNumber) {
        this.chequeNumber = chequeNumber;
    }

    public String getChequeDate() {
        return chequeDate;
    }

    public void setChequeDate(String chequeDate) {
        this.chequeDate = chequeDate;
    }

    public int getFinanceReasonTypeId() {
        return financeReasonTypeId;
    }

    public void setFinanceReasonTypeId(int financeReasonTypeId) {
        this.financeReasonTypeId = financeReasonTypeId;
    }

    public ClaimPaymentDispatchDto getClaimPaymentDispatchDto() {
        return claimPaymentDispatchDto;
    }

    public void setClaimPaymentDispatchDto(ClaimPaymentDispatchDto claimPaymentDispatchDto) {
        this.claimPaymentDispatchDto = claimPaymentDispatchDto;
    }
}
