package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class AssessorPaymentDeductionDetailDto implements Serializable {
    private Integer txnId;
    private Integer claimNo;
    private Integer refNo;
    private BigDecimal beforeDeductionOtherAmount;
    private BigDecimal beforeDeductionScheduleAmount;
    private BigDecimal deduction;
    private BigDecimal otherAmount;
    private BigDecimal scheduleAmount;
    private String inputdatetime;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public BigDecimal getBeforeDeductionOtherAmount() {
        return beforeDeductionOtherAmount;
    }

    public void setBeforeDeductionOtherAmount(BigDecimal beforeDeductionOtherAmount) {
        this.beforeDeductionOtherAmount = beforeDeductionOtherAmount;
    }

    public BigDecimal getBeforeDeductionScheduleAmount() {
        return beforeDeductionScheduleAmount;
    }

    public void setBeforeDeductionScheduleAmount(BigDecimal beforeDeductionScheduleAmount) {
        this.beforeDeductionScheduleAmount = beforeDeductionScheduleAmount;
    }

    public BigDecimal getDeduction() {
        return deduction;
    }

    public void setDeduction(BigDecimal deduction) {
        this.deduction = deduction;
    }

    public BigDecimal getOtherAmount() {
        return otherAmount;
    }

    public void setOtherAmount(BigDecimal otherAmount) {
        this.otherAmount = otherAmount;
    }

    public BigDecimal getScheduleAmount() {
        return scheduleAmount;
    }

    public void setScheduleAmount(BigDecimal scheduleAmount) {
        this.scheduleAmount = scheduleAmount;
    }

    public String getInputdatetime() {
        return inputdatetime;
    }

    public void setInputdatetime(String inputdatetime) {
        this.inputdatetime = inputdatetime;
    }
}
