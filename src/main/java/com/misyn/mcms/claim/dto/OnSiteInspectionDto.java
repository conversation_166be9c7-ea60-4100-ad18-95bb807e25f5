package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class OnSiteInspectionDto implements Serializable {
    private String districtCode = AppConstant.STRING_EMPTY;
    private String districtName = AppConstant.STRING_EMPTY;
    private int cityCode = AppConstant.ZERO_INT;
    private String cityName = AppConstant.STRING_EMPTY;
    private String placeOfInspection = AppConstant.STRING_EMPTY;
    private List<AssessorDto> assessorDtos = new ArrayList<>();

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public int getCityCode() {
        return cityCode;
    }

    public void setCityCode(int cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getPlaceOfInspection() {
        return placeOfInspection;
    }

    public void setPlaceOfInspection(String placeOfInspection) {
        this.placeOfInspection = placeOfInspection;
    }

    public List<AssessorDto> getAssessorDtos() {
        return assessorDtos;
    }

    public void setAssessorDtos(List<AssessorDto> assessorDtos) {
        this.assessorDtos = assessorDtos;
    }
}
