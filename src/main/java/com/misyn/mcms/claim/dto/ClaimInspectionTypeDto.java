/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class ClaimInspectionTypeDto implements Serializable {

    private Integer inspectionTypeId;
    private String inspectionTypeCode;
    private String inspectionTypeDesc;
    private BigDecimal permanantDayDutyAmt;
    private BigDecimal permanantNightDutyAmt;
    private BigDecimal inhouseDayDutyAmt;
    private BigDecimal inhouseNightDutyAmt;
    private BigDecimal freelanceDayDutyAmt;
    private BigDecimal freelanceNightDutyAmt;
    private String jobNo;

    public ClaimInspectionTypeDto() {
    }

    public ClaimInspectionTypeDto(Integer inspectionTypeId) {
        this.inspectionTypeId = inspectionTypeId;
    }

    public ClaimInspectionTypeDto(Integer inspectionTypeId, String inspectionTypeCode, String inspectionTypeDesc, BigDecimal permanantDayDutyAmt, BigDecimal permanantNightDutyAmt, BigDecimal inhouseDayDutyAmt, BigDecimal freelanceDayDutyAmt) {
        this.inspectionTypeId = inspectionTypeId;
        this.inspectionTypeCode = inspectionTypeCode;
        this.inspectionTypeDesc = inspectionTypeDesc;
        this.permanantDayDutyAmt = permanantDayDutyAmt;
        this.permanantNightDutyAmt = permanantNightDutyAmt;
        this.inhouseDayDutyAmt = inhouseDayDutyAmt;
        this.freelanceDayDutyAmt = freelanceDayDutyAmt;
    }

    public Integer getInspectionTypeId() {
        return inspectionTypeId;
    }

    public void setInspectionTypeId(Integer inspectionTypeId) {
        this.inspectionTypeId = inspectionTypeId;
    }

    public String getInspectionTypeCode() {
        return inspectionTypeCode;
    }

    public void setInspectionTypeCode(String inspectionTypeCode) {
        this.inspectionTypeCode = inspectionTypeCode;
    }

    public String getInspectionTypeDesc() {
        return inspectionTypeDesc;
    }

    public void setInspectionTypeDesc(String inspectionTypeDesc) {
        this.inspectionTypeDesc = inspectionTypeDesc;
    }

    public BigDecimal getPermanantDayDutyAmt() {
        return permanantDayDutyAmt;
    }

    public void setPermanantDayDutyAmt(BigDecimal permanantDayDutyAmt) {
        this.permanantDayDutyAmt = permanantDayDutyAmt;
    }

    public BigDecimal getPermanantNightDutyAmt() {
        return permanantNightDutyAmt;
    }

    public void setPermanantNightDutyAmt(BigDecimal permanantNightDutyAmt) {
        this.permanantNightDutyAmt = permanantNightDutyAmt;
    }

    public BigDecimal getInhouseDayDutyAmt() {
        return inhouseDayDutyAmt;
    }

    public void setInhouseDayDutyAmt(BigDecimal inhouseDayDutyAmt) {
        this.inhouseDayDutyAmt = inhouseDayDutyAmt;
    }

    public BigDecimal getInhouseNightDutyAmt() {
        return inhouseNightDutyAmt;
    }

    public void setInhouseNightDutyAmt(BigDecimal inhouseNightDutyAmt) {
        this.inhouseNightDutyAmt = inhouseNightDutyAmt;
    }

    public BigDecimal getFreelanceDayDutyAmt() {
        return freelanceDayDutyAmt;
    }

    public void setFreelanceDayDutyAmt(BigDecimal freelanceDayDutyAmt) {
        this.freelanceDayDutyAmt = freelanceDayDutyAmt;
    }

    public BigDecimal getFreelanceNightDutyAmt() {
        return freelanceNightDutyAmt;
    }

    public void setFreelanceNightDutyAmt(BigDecimal freelanceNightDutyAmt) {
        this.freelanceNightDutyAmt = freelanceNightDutyAmt;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }
}
