package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;
public class ClaimDocumentLossTypeOldDto {
    private Integer txnNo = AppConstant.ZERO_INT;
    private Integer documentTypeId = AppConstant.ZERO_INT;
    private Integer claimNo = AppConstant.ZERO_INT;
    private String isMandatory = AppConstant.NO;
    private String oldStatus = AppConstant.NO;
    private String inpUser = AppConstant.STRING_EMPTY;
    private String inpDateTime = AppConstant.DEFAULT_DATE_TIME;

    public Integer getTxnNo() {
        return txnNo;
    }

    public void setTxnNo(Integer txnNo) {
        this.txnNo = txnNo;
    }

    public Integer getDocumentTypeId() {
        return documentTypeId;
    }

    public void setDocumentTypeId(Integer documentTypeId) {
        this.documentTypeId = documentTypeId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(String isMandatory) {
        this.isMandatory = isMandatory;
    }

    public String getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(String oldStatus) {
        this.oldStatus = oldStatus;
    }

    public String getInpUser() {
        return inpUser;
    }

    public void setInpUser(String inpUser) {
        this.inpUser = inpUser;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }
}
