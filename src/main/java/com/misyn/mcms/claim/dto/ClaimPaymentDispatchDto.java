package com.misyn.mcms.claim.dto;

import com.misyn.mcms.admin.admin.dto.BranchDetailDto;

import java.io.Serializable;
public class ClaimPaymentDispatchDto implements Serializable {
    private Integer txnId;
    private Integer payeeId;
    private Integer calSheetId;
    private Integer claimNo;
    private String voucherNo;
    private String chequeNo;
    private String dispatchLocation;
    private BranchDetailDto dispatchedLocation = new BranchDetailDto();
    private String dispatchUser;
    private String dispatchDateTime;
    private String chequeDispatchStatus;
    private BranchDetailDto branchDetailDto = new BranchDetailDto();

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getDispatchUser() {
        return dispatchUser;
    }

    public void setDispatchUser(String dispatchUser) {
        this.dispatchUser = dispatchUser;
    }

    public String getDispatchDateTime() {
        return dispatchDateTime;
    }

    public void setDispatchDateTime(String dispatchDateTime) {
        this.dispatchDateTime = dispatchDateTime;
    }

    public String getChequeDispatchStatus() {
        return chequeDispatchStatus;
    }

    public void setChequeDispatchStatus(String chequeDispatchStatus) {
        this.chequeDispatchStatus = chequeDispatchStatus;
    }

    public BranchDetailDto getBranchDetailDto() {
        return branchDetailDto;
    }

    public void setBranchDetailDto(BranchDetailDto branchDetailDto) {
        this.branchDetailDto = branchDetailDto;
    }

    public String getDispatchLocation() {
        return dispatchLocation;
    }

    public void setDispatchLocation(String dispatchLocation) {
        this.dispatchLocation = dispatchLocation;
    }

    public BranchDetailDto getDispatchedLocation() {
        return dispatchedLocation;
    }

    public void setDispatchedLocation(BranchDetailDto dispatchedLocation) {
        this.dispatchedLocation = dispatchedLocation;
    }

    public Integer getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(Integer payeeId) {
        this.payeeId = payeeId;
    }

    public Integer getCalSheetId() {
        return calSheetId;
    }

    public void setCalSheetId(Integer calSheetId) {
        this.calSheetId = calSheetId;
    }
}
