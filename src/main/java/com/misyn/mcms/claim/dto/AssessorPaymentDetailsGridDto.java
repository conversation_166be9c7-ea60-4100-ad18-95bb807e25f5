package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class AssessorPaymentDetailsGridDto implements Serializable {
    private Integer txtId = AppConstant.ZERO_INT;
    private Integer refNumber = AppConstant.ZERO_INT;
    private String jobNumber = AppConstant.EMPTY_STRING;
    private String policyNumber = AppConstant.EMPTY_STRING;
    private String isfClaimNumber = AppConstant.EMPTY_STRING;
    private Integer claimNumber = AppConstant.ZERO_INT;
    private String vehicleNumber = AppConstant.EMPTY_STRING;
    private String dateOfInspection = AppConstant.EMPTY_STRING;
    private String inspectionType = AppConstant.EMPTY_STRING;
    private String placeOfInspection = AppConstant.EMPTY_STRING;
    private String assessorName = AppConstant.EMPTY_STRING;
    private BigDecimal allocatedProfessionalFee =BigDecimal.ZERO;
    private BigDecimal approvedProfessionalFee = BigDecimal.ZERO;
    private Integer mileage = AppConstant.ZERO_INT;
    private BigDecimal travelFee = BigDecimal.ZERO;
    private BigDecimal costOfCall = BigDecimal.ZERO;
    private BigDecimal otherFee = BigDecimal.ZERO;
    private BigDecimal deductions = BigDecimal.ZERO;
    private BigDecimal totalFee = BigDecimal.ZERO;
    private String type = AppConstant.STRING_EMPTY;
    private String action = AppConstant.EMPTY_STRING;
    private String paymentStatus = AppConstant.EMPTY_STRING;
    private boolean isfPending;
    private boolean is_approval_team;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getTxtId() {
        return txtId;
    }

    public void setTxtId(Integer txtId) {
        this.txtId = txtId;
    }

    public Integer getRefNumber() {
        return refNumber;
    }

    public void setRefNumber(Integer refNumber) {
        this.refNumber = refNumber;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getIsfClaimNumber() {
        return isfClaimNumber;
    }

    public void setIsfClaimNumber(String isfClaimNumber) {
        this.isfClaimNumber = isfClaimNumber;
    }

    public Integer getClaimNumber() {
        return claimNumber;
    }

    public void setClaimNumber(Integer claimNumber) {
        this.claimNumber = claimNumber;
    }

    public String getVehicleNumber() {
        return vehicleNumber;
    }

    public void setVehicleNumber(String vehicleNumber) {
        this.vehicleNumber = vehicleNumber;
    }

    public String getDateOfInspection() {
        return dateOfInspection;
    }

    public void setDateOfInspection(String dateOfInspection) {
        this.dateOfInspection = dateOfInspection;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getPlaceOfInspection() {
        return placeOfInspection;
    }

    public void setPlaceOfInspection(String placeOfInspection) {
        this.placeOfInspection = placeOfInspection;
    }

    public String getAssessorName() {
        return assessorName;
    }

    public void setAssessorName(String assessorName) {
        this.assessorName = assessorName;
    }

    public BigDecimal getAllocatedProfessionalFee() {
        return allocatedProfessionalFee;
    }

    public void setAllocatedProfessionalFee(BigDecimal allocatedProfessionalFee) {
        this.allocatedProfessionalFee = allocatedProfessionalFee;
    }

    public BigDecimal getApprovedProfessionalFee() {
        return approvedProfessionalFee;
    }

    public void setApprovedProfessionalFee(BigDecimal approvedProfessionalFee) {
        this.approvedProfessionalFee = approvedProfessionalFee;
    }

    public Integer getMileage() {
        return mileage;
    }

    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public BigDecimal getTravelFee() {
        return travelFee;
    }

    public void setTravelFee(BigDecimal travelFee) {
        this.travelFee = travelFee;
    }

    public BigDecimal getCostOfCall() {
        return costOfCall;
    }

    public void setCostOfCall(BigDecimal costOfCall) {
        this.costOfCall = costOfCall;
    }

    public BigDecimal getOtherFee() {
        return otherFee;
    }

    public void setOtherFee(BigDecimal otherFee) {
        this.otherFee = otherFee;
    }

    public BigDecimal getDeductions() {
        return deductions;
    }

    public void setDeductions(BigDecimal deductions) {
        this.deductions = deductions;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public boolean isIsfPending() {
        return isfPending;
    }

    public void setIsfPending(boolean isfPending) {
        this.isfPending = isfPending;
    }

    public boolean isIs_approval_team() {
        return is_approval_team;
    }

    public void setIs_approval_team(boolean is_approval_team) {
        this.is_approval_team = is_approval_team;
    }
}


