package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class ApplicationAlertDto implements Serializable {
    private Integer txnId;
    private Integer keyValue = AppConstant.ZERO_INT;
    private Integer type = AppConstant.ZERO_INT;
    private String processDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String inpUser = AppConstant.STRING_EMPTY;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(Integer keyValue) {
        this.keyValue = keyValue;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getProcessDateTime() {
        return processDateTime;
    }

    public void setProcessDateTime(String processDateTime) {
        this.processDateTime = processDateTime;
    }

    public String getInpUser() {
        return inpUser;
    }

    public void setInpUser(String inpUser) {
        this.inpUser = inpUser;
    }
}
