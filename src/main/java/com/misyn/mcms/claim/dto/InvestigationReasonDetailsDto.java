package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class InvestigationReasonDetailsDto implements Serializable {
    private Integer txnNo;
    private Integer invesTxnNo;
    private Integer invesReasonRefNo;
    private String isCheck = AppConstant.NO;
    private String invesReason = AppConstant.STRING_EMPTY;

    public Integer getTxnNo() {
        return txnNo;
    }

    public void setTxnNo(Integer txnNo) {
        this.txnNo = txnNo;
    }

    public Integer getInvesTxnNo() {
        return invesTxnNo;
    }

    public void setInvesTxnNo(Integer invesTxnNo) {
        this.invesTxnNo = invesTxnNo;
    }

    public Integer getInvesReasonRefNo() {
        return invesReasonRefNo;
    }

    public void setInvesReasonRefNo(Integer invesReasonRefNo) {
        this.invesReasonRefNo = invesReasonRefNo;
    }

    public String getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(String isCheck) {
        this.isCheck = isCheck;
    }

    public String getInvesReason() {
        return invesReason;
    }

    public void setInvesReason(String invesReason) {
        this.invesReason = invesReason;
    }
}
