package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
public class ClaimProcessFlowDto {
    private Integer id;
    private Integer claimNo = AppConstant.ZERO_INT;
    private Integer claimStatus = AppConstant.ZERO_INT;
    private String task = AppConstant.STRING_EMPTY;
    private String assignUserId = AppConstant.STRING_EMPTY;
    private String inpUserId = AppConstant.STRING_EMPTY;
    private String inpDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String isVisible = AppConstant.YES;
    private String tat;
    private String updateDate;

    private String taskCompletedDateTime = AppConstant.DEFAULT_DATE_TIME;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(Integer claimStatus) {
        this.claimStatus = claimStatus;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDatetime() {
        return inpDatetime;
    }

    public void setInpDatetime(String inpDatetime) {
        this.inpDatetime = inpDatetime;
    }

    public String getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(String isVisible) {
        this.isVisible = isVisible;
    }

    public String getTat() {
        long diff = Utility.getNoMiniutsTimeDiff(this.inpDatetime, this.taskCompletedDateTime, AppConstant.DATE_TIME_FORMAT);
        long[] dateTime = Utility.getDayHoursMinSecondDifferenceForWorkingHours(diff);
        tat = String.valueOf(dateTime[0]).concat(" d ").concat(String.valueOf(dateTime[1])).concat(" h ").concat(String.valueOf(dateTime[2])).concat(" m ");
        return tat;
    }

    public void setTat(String tat) {
        this.tat = tat;
    }

    public String getTaskCompletedDateTime() {
        return taskCompletedDateTime;
    }

    public void setTaskCompletedDateTime(String taskCompletedDateTime) {
        this.taskCompletedDateTime = taskCompletedDateTime;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }
}
