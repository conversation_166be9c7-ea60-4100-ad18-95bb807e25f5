package com.misyn.mcms.claim.dto;

import java.math.BigDecimal;

public class ClaimReserveAdjustmentTypeDto {
    private Integer claimReserveAdjustmentId;
    private Integer periodId;
    private Integer categoryId;
    private BigDecimal amountMin;
    private BigDecimal amountMax;
    private String amountCurrency;
    private BigDecimal movingAcrAbove;
    private String amount;
    private BigDecimal movingAdvanceOrAcrAbove;
    private BigDecimal movingAcrLessThan;
    private BigDecimal nonMoving;
    private String recordStatus;
    private String inputDateTime;
    private String inputUser;
    private String lastModifiedDateTime;
    private String lastModifiedUser;
    private int index;
    private String periodLabel;
    private String categoryLabel;

    public String getPeriodLabel() { return periodLabel; }
    public void setPeriodLabel(String periodLabel) { this.periodLabel = periodLabel; }
    public String getCategoryLabel() { return categoryLabel; }
    public void setCategoryLabel(String categoryLabel) { this.categoryLabel = categoryLabel; }

    public Integer getClaimReserveAdjustmentId() {
        return claimReserveAdjustmentId;
    }

    public void setClaimReserveAdjustmentId(Integer claimReserveAdjustmentId) {
        this.claimReserveAdjustmentId = claimReserveAdjustmentId;
    }

    public Integer getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Integer periodId) {
        this.periodId = periodId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public BigDecimal getAmountMin() {
        return amountMin;
    }

    public void setAmountMin(BigDecimal amountMin) {
        this.amountMin = amountMin;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public BigDecimal getAmountMax() {
        return amountMax;
    }

    public void setAmountMax(BigDecimal amountMax) {
        this.amountMax = amountMax;
    }

    public String getAmountCurrency() {
        return amountCurrency;
    }

    public void setAmountCurrency(String amountCurrency) {
        this.amountCurrency = amountCurrency;
    }

    public BigDecimal getMovingAcrAbove() {
        return movingAcrAbove;
    }

    public void setMovingAcrAbove(BigDecimal movingAcrAbove) {
        this.movingAcrAbove = movingAcrAbove;
    }

    public BigDecimal getMovingAdvanceOrAcrAbove() {
        return movingAdvanceOrAcrAbove;
    }

    public void setMovingAdvanceOrAcrAbove(BigDecimal movingAdvanceOrAcrAbove) {
        this.movingAdvanceOrAcrAbove = movingAdvanceOrAcrAbove;
    }

    public BigDecimal getMovingAcrLessThan() {
        return movingAcrLessThan;
    }

    public void setMovingAcrLessThan(BigDecimal movingAcrLessThan) {
        this.movingAcrLessThan = movingAcrLessThan;
    }

    public BigDecimal getNonMoving() {
        return nonMoving;
    }

    public void setNonMoving(BigDecimal nonMoving) {
        this.nonMoving = nonMoving;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(String inputDateTime) {
        this.inputDateTime = inputDateTime;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public void setLastModifiedDateTime(String lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
    }

    public String getLastModifiedUser() {
        return lastModifiedUser;
    }

    public void setLastModifiedUser(String lastModifiedUser) {
        this.lastModifiedUser = lastModifiedUser;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
