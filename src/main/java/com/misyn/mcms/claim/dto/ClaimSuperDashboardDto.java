package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ClaimSuperDashboardDto implements Serializable {
    private ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
    private List<MotorEngineerDetailsDto> motorEngineerDetailsDtoList = new ArrayList<>();
    private List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList = new ArrayList<>();
    private List<AssessorAllocationDto> assessorAllocationDtoList = new ArrayList<>();
    private List<ClaimProcessFlowDto> claimProcessFlowDtos = new ArrayList<>();
    private List<VoucherDetailsDto> voucherDetailsDtos = new ArrayList<>();

    public ClaimHandlerDto getClaimHandlerDto() {
        return claimHandlerDto;
    }

    public void setClaimHandlerDto(ClaimHandlerDto claimHandlerDto) {
        this.claimHandlerDto = claimHandlerDto;
    }

    public List<MotorEngineerDetailsDto> getMotorEngineerDetailsDtoList() {
        return motorEngineerDetailsDtoList;
    }

    public void setMotorEngineerDetailsDtoList(List<MotorEngineerDetailsDto> motorEngineerDetailsDtoList) {
        this.motorEngineerDetailsDtoList = motorEngineerDetailsDtoList;
    }

    public List<ClaimCalculationSheetMainDto> getClaimCalculationSheetMainDtoList() {
        return claimCalculationSheetMainDtoList;
    }

    public void setClaimCalculationSheetMainDtoList(List<ClaimCalculationSheetMainDto> claimCalculationSheetMainDtoList) {
        this.claimCalculationSheetMainDtoList = claimCalculationSheetMainDtoList;
    }

    public List<AssessorAllocationDto> getAssessorAllocationDtoList() {
        return assessorAllocationDtoList;
    }

    public void setAssessorAllocationDtoList(List<AssessorAllocationDto> assessorAllocationDtoList) {
        this.assessorAllocationDtoList = assessorAllocationDtoList;
    }

    public List<ClaimProcessFlowDto> getClaimProcessFlowDtos() {
        return claimProcessFlowDtos;
    }

    public void setClaimProcessFlowDtos(List<ClaimProcessFlowDto> claimProcessFlowDtos) {
        this.claimProcessFlowDtos = claimProcessFlowDtos;
    }

    public List<VoucherDetailsDto> getVoucherDetailsDtos() {
        return voucherDetailsDtos;
    }

    public void setVoucherDetailsDtos(List<VoucherDetailsDto> voucherDetailsDtos) {
        this.voucherDetailsDtos = voucherDetailsDtos;
    }
}
