package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class PhotoComparisionDto implements Serializable {
    private List<PopupItemDto> claimList = new ArrayList<>();
    private List<String> comperisionTypeList = new ArrayList<>();
    private List<ClaimDocumentTypeDto> claimDocumentTypeList = new ArrayList<>();
    private List<ClaimInspectionTypeDto> claimInspectionTypeList = new ArrayList<>();
    private List<ClaimDocumentDto> claimDocumentDtoList = new ArrayList<>();
    private List<ClaimImageDto> claimImageDtoList = new ArrayList<>();

    public PhotoComparisionDto() {
        comperisionTypeList.add("Photo");
        comperisionTypeList.add("Document");
    }

    public List<PopupItemDto> getClaimList() {
        return claimList;
    }

    public void setClaimList(List<PopupItemDto> claimList) {
        this.claimList = claimList;
    }

    public List<String> getComperisionTypeList() {
        return comperisionTypeList;
    }

    public void setComperisionTypeList(List<String> comperisionTypeList) {
        this.comperisionTypeList = comperisionTypeList;
    }

    public List<ClaimDocumentTypeDto> getClaimDocumentTypeList() {
        return claimDocumentTypeList;
    }

    public void setClaimDocumentTypeList(List<ClaimDocumentTypeDto> claimDocumentTypeList) {
        this.claimDocumentTypeList = claimDocumentTypeList;
    }

    public List<ClaimInspectionTypeDto> getClaimInspectionTypeList() {
        return claimInspectionTypeList;
    }

    public void setClaimInspectionTypeList(List<ClaimInspectionTypeDto> claimInspectionTypeList) {
        this.claimInspectionTypeList = claimInspectionTypeList;
    }

    public List<ClaimDocumentDto> getClaimDocumentDtoList() {
        return claimDocumentDtoList;
    }

    public void setClaimDocumentDtoList(List<ClaimDocumentDto> claimDocumentDtoList) {
        this.claimDocumentDtoList = claimDocumentDtoList;
    }

    public List<ClaimImageDto> getClaimImageDtoList() {
        return claimImageDtoList;
    }

    public void setClaimImageDtoList(List<ClaimImageDto> claimImageDtoList) {
        this.claimImageDtoList = claimImageDtoList;
    }
}
