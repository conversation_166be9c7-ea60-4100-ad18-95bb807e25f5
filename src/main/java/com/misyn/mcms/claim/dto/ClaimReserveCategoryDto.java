package com.misyn.mcms.claim.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ClaimReserveCategoryDto {
    private Integer categoryId;
    private String categoryLabel;
    private Integer periodId;
    private String periodLabel;
    private BigDecimal amountMin;
    private BigDecimal amountMax;
    private List<ClaimReserveAdjustmentTypeDto> periods = new ArrayList<>();

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Integer periodId) {
        this.periodId = periodId;
    }

    public String getPeriodLabel() {
        return periodLabel;
    }

    public void setPeriodLabel(String periodLabel) {
        this.periodLabel = periodLabel;
    }

    public BigDecimal getAmountMin() {
        return amountMin;
    }

    public void setAmountMin(BigDecimal amountMin) {
        this.amountMin = amountMin;
    }

    public BigDecimal getAmountMax() {
        return amountMax;
    }

    public void setAmountMax(BigDecimal amountMax) {
        this.amountMax = amountMax;
    }

    public String getCategoryLabel() {
        return categoryLabel;
    }

    public void setCategoryLabel(String categoryLabel) {
        this.categoryLabel = categoryLabel;
    }

    public List<ClaimReserveAdjustmentTypeDto> getPeriods() {
        return periods;
    }

    public void setPeriods(List<ClaimReserveAdjustmentTypeDto> periods) {
        this.periods = periods;
    }
}
