/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class McmsClaimOfflineReserveAssessorDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long nReserveAssessorId;
    private String ovClaimNo;
    private String ovIdentificationNo;
    private BigDecimal onOtherAmt;
    private BigDecimal onScheduleAmt;
    private String ovPanelCategory;
    private String ovInstitutionBranch;
    private String ovInstitutionCode;
    private String ovIdentificationCode;
    private String dInsertDateTime;
    private Integer nRetryAttempt;
    private String vIsfsUpdateStat;
    private String dIsfsUpdateDateTime;
    private Integer claimNo;
    private String policyChannelType;

    public McmsClaimOfflineReserveAssessorDto() {
    }

    public McmsClaimOfflineReserveAssessorDto(Long nReserveAssessorId) {
        this.nReserveAssessorId = nReserveAssessorId;
    }

    public Long getNReserveAssessorId() {
        return nReserveAssessorId;
    }

    public void setNReserveAssessorId(Long nReserveAssessorId) {
        this.nReserveAssessorId = nReserveAssessorId;
    }

    public String getOvClaimNo() {
        return ovClaimNo;
    }

    public void setOvClaimNo(String ovClaimNo) {
        this.ovClaimNo = ovClaimNo;
    }

    public String getOvIdentificationNo() {
        return ovIdentificationNo;
    }

    public void setOvIdentificationNo(String ovIdentificationNo) {
        this.ovIdentificationNo = ovIdentificationNo;
    }

    public BigDecimal getOnOtherAmt() {
        return onOtherAmt;
    }

    public void setOnOtherAmt(BigDecimal onOtherAmt) {
        this.onOtherAmt = onOtherAmt;
    }

    public BigDecimal getOnScheduleAmt() {
        return onScheduleAmt;
    }

    public void setOnScheduleAmt(BigDecimal onScheduleAmt) {
        this.onScheduleAmt = onScheduleAmt;
    }

    public String getOvPanelCategory() {
        return ovPanelCategory;
    }

    public void setOvPanelCategory(String ovPanelCategory) {
        this.ovPanelCategory = ovPanelCategory;
    }

    public String getOvInstitutionBranch() {
        return ovInstitutionBranch;
    }

    public void setOvInstitutionBranch(String ovInstitutionBranch) {
        this.ovInstitutionBranch = ovInstitutionBranch;
    }

    public String getOvInstitutionCode() {
        return ovInstitutionCode;
    }

    public void setOvInstitutionCode(String ovInstitutionCode) {
        this.ovInstitutionCode = ovInstitutionCode;
    }

    public String getOvIdentificationCode() {
        return ovIdentificationCode;
    }

    public void setOvIdentificationCode(String ovIdentificationCode) {
        this.ovIdentificationCode = ovIdentificationCode;
    }

    public String getDInsertDateTime() {
        return dInsertDateTime;
    }

    public void setDInsertDateTime(String dInsertDateTime) {
        this.dInsertDateTime = dInsertDateTime;
    }

    public Integer getNRetryAttempt() {
        return nRetryAttempt;
    }

    public void setNRetryAttempt(Integer nRetryAttempt) {
        this.nRetryAttempt = nRetryAttempt;
    }

    public String getVIsfsUpdateStat() {
        return vIsfsUpdateStat;
    }

    public void setVIsfsUpdateStat(String vIsfsUpdateStat) {
        this.vIsfsUpdateStat = vIsfsUpdateStat;
    }

    public String getDIsfsUpdateDateTime() {
        return dIsfsUpdateDateTime;
    }

    public void setDIsfsUpdateDateTime(String dIsfsUpdateDateTime) {
        this.dIsfsUpdateDateTime = dIsfsUpdateDateTime;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }
}
