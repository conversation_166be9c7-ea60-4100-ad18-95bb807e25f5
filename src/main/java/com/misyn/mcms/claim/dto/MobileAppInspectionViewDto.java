package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class MobileAppInspectionViewDto implements Serializable {
    private Integer txnId;
    private Integer claimNo;
    private String readDatetime;
    private String notifyDatetime;
    private String vehicleNo;
    private String accidentDate;
    private String isMobileRead;
    private String inspectionType;
    private String jobId;
    private String assignUserId;
    private Integer index;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getReadDatetime() {
        return readDatetime;
    }

    public void setReadDatetime(String readDatetime) {
        this.readDatetime = readDatetime;
    }

    public String getNotifyDatetime() {
        return notifyDatetime;
    }

    public void setNotifyDatetime(String notifyDatetime) {
        this.notifyDatetime = notifyDatetime;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(String accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getIsMobileRead() {
        return isMobileRead;
    }

    public void setIsMobileRead(String isMobileRead) {
        this.isMobileRead = isMobileRead;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getAssignUserId() {
        return assignUserId;
    }

    public void setAssignUserId(String assignUserId) {
        this.assignUserId = assignUserId;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}
