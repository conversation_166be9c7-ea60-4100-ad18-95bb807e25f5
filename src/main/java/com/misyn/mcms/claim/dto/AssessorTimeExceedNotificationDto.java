package com.misyn.mcms.claim.dto;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.sql.Date;


/**
 * <AUTHOR>
 */

public class AssessorTimeExceedNotificationDto implements Serializable {
    private int id;
    private String callCenterUser;
    private String inspectionTime;
    private String inspectionAssessor;
    @NotNull
    private Date createdDate;
    @NotNull
    private String createdBy;
    private String colourCode;
    private String status;
    private String assignedDateTime;
    private int duration;
    private String assessorType;
    private int inspectionType;
    private String inspectionRefNo;
    private Integer claimNo;

    public AssessorTimeExceedNotificationDto() {
    }

    public AssessorTimeExceedNotificationDto(int id, String callCenterUser, String inspectionTime, String inspectionAssessor, Date createdDate, String createdBy, String colourCode, String status, String assignedDateTime, int duration, String assessorType, int inspectionType, String inspectionRefNo, Integer claimNo) {
        this.id = id;
        this.callCenterUser = callCenterUser;
        this.inspectionTime = inspectionTime;
        this.inspectionAssessor = inspectionAssessor;
        this.createdDate = createdDate;
        this.createdBy = createdBy;
        this.colourCode = colourCode;
        this.status = status;
        this.assignedDateTime = assignedDateTime;
        this.duration = duration;
        this.assessorType = assessorType;
        this.inspectionType = inspectionType;
        this.inspectionRefNo = inspectionRefNo;
        this.claimNo = claimNo;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCallCenterUser() {
        return callCenterUser;
    }

    public void setCallCenterUser(String callCenterUser) {
        this.callCenterUser = callCenterUser;
    }

    public String getInspectionTime() {
        return inspectionTime;
    }

    public void setInspectionTime(String inspectionTime) {
        this.inspectionTime = inspectionTime;
    }

    public String getInspectionAssessor() {
        return inspectionAssessor;
    }

    public void setInspectionAssessor(String inspectionAssessor) {
        this.inspectionAssessor = inspectionAssessor;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getColourCode() {
        return colourCode;
    }

    public void setColourCode(String colourCode) {
        this.colourCode = colourCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getAssignedDateTime() {
        return assignedDateTime;
    }

    public void setAssignedDateTime(String assignedDateTime) {
        this.assignedDateTime = assignedDateTime;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getAssessorType() {
        return assessorType;
    }

    public void setAssessorType(String assessorType) {
        this.assessorType = assessorType;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }
    public int getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(int inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getInspectionRefNo() {
        return inspectionRefNo;
    }

    public void setInspectionRefNo(String inspectionRefNo) {
        this.inspectionRefNo = inspectionRefNo;
    }
}
