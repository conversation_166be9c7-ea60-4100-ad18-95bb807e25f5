package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class SupplyOrderDetailsDto implements Serializable {
    private Integer index;
    private Integer refNo;
    private Integer supplyOrderRefNo;
    private Integer sparePartRefNo;
    private String sparePartName = AppConstant.STRING_EMPTY;
    private Integer quantity;
    private BigDecimal individualPrice = BigDecimal.ZERO;
    private BigDecimal oaRate = BigDecimal.ZERO;
    private BigDecimal totalAmount = BigDecimal.ZERO;
    private Boolean isPendingIndividualPrice = Boolean.FALSE;

    public Integer getRefNo() {
        return refNo;
    }

    public Boolean getIsPendingIndividualPrice() {
        return isPendingIndividualPrice;
    }

    public void setIsPendingIndividualPrice(Boolean isPendingIndividualPrice) {
        this.isPendingIndividualPrice = isPendingIndividualPrice;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getSupplyOrderRefNo() {
        return supplyOrderRefNo;
    }

    public void setSupplyOrderRefNo(Integer supplyOrderRefNo) {
        this.supplyOrderRefNo = supplyOrderRefNo;
    }

    public Integer getSparePartRefNo() {
        return sparePartRefNo;
    }

    public void setSparePartRefNo(Integer sparePartRefNo) {
        this.sparePartRefNo = sparePartRefNo;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getIndividualPrice() {
        return individualPrice;
    }

    public void setIndividualPrice(BigDecimal individualPrice) {
        this.individualPrice = individualPrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getSparePartName() {
        return sparePartName;
    }

    public void setSparePartName(String sparePartName) {
        this.sparePartName = sparePartName;
    }

    public BigDecimal getOaRate() {
        return oaRate;
    }

    public void setOaRate(BigDecimal oaRate) {
        this.oaRate = oaRate;
    }
}
