package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class RequestAriRemarkDto implements Serializable {
    private Integer remarkId;
    private Integer requestAriId;
    private String remark;
    private Integer departmentId;
    private String inputUser;
    private String inputDatetime;
    private String sectionName;

    public Integer getRemarkId() {
        return remarkId;
    }

    public void setRemarkId(Integer remarkId) {
        this.remarkId = remarkId;
    }

    public Integer getRequestAriId() {
        return requestAriId;
    }

    public void setRequestAriId(Integer requestAriId) {
        this.requestAriId = requestAriId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getInputDatetime() {
        return inputDatetime;
    }

    public void setInputDatetime(String inputDatetime) {
        this.inputDatetime = inputDatetime;
    }

    public String getSectionName() {
        return sectionName;
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }
}
