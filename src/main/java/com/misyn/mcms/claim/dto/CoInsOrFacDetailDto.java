package com.misyn.mcms.claim.dto;

import java.math.BigDecimal;
public class CoInsOrFacDetailDto {

    private String claimNo;
    private BigDecimal Per;
    private BigDecimal prov_Amount;
    private String name;

    public CoInsOrFacDetailDto() {
    }

    public CoInsOrFacDetailDto(String claimNo, BigDecimal per, BigDecimal prov_Amount, String name) {
        this.claimNo = claimNo;
        Per = per;
        this.prov_Amount = prov_Amount;
        this.name = name;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public BigDecimal getPer() {
        return Per;
    }

    public void setPer(BigDecimal per) {
        Per = per;
    }

    public BigDecimal getProv_Amount() {
        return prov_Amount;
    }

    public void setProv_Amount(BigDecimal prov_Amount) {
        this.prov_Amount = prov_Amount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
