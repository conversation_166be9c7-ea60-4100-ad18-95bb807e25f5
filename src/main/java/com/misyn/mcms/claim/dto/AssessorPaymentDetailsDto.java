package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.PaymentStatus;
import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class AssessorPaymentDetailsDto implements Serializable {
    private Integer txnId;
    private Integer keyId;
    private Integer claimNo;
    private String type = AppConstant.STRING_EMPTY;
    private Integer milleage;
    private BigDecimal costOfCall;
    private BigDecimal otherFee;
    private BigDecimal deductionFee;
    private BigDecimal professionalFee;
    private BigDecimal travelFee;
    private BigDecimal totalFee;
    private PaymentStatus paymentStatus = PaymentStatus.Pending;
    private Integer recordStatus;
    private String inpUserId;
    private String inpDate;
    private String placeOfInspection = AppConstant.STRING_EMPTY;
    private String vehicleNumber = AppConstant.STRING_EMPTY;
    private String dateOfAccident = AppConstant.DEFAULT_DATE_TIME;
    private String inspectionType = AppConstant.STRING_EMPTY;
    private String name = AppConstant.STRING_EMPTY;
    private String remark = AppConstant.STRING_EMPTY;
    private String voucherNo = AppConstant.STRING_EMPTY;
    private String apprvUser = AppConstant.STRING_EMPTY;
    private String apprvDatetime = AppConstant.DEFAULT_DATE_TIME;
    private String emailSendStatus;
    private Integer generateOccurrency;
    private String voucherStatus;
    private String jobNo;
    private String fromDate;
    private String toDate;
    private BigDecimal allocatedProfessionalfee;
    private BigDecimal approvedProfessionalfee;
    private String polNumber;
    private String isfClaimNo;
    private boolean isIsfPending;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getMilleage() {
        return milleage;
    }

    public void setMilleage(Integer milleage) {
        this.milleage = milleage;
    }

    public BigDecimal getCostOfCall() {
        return costOfCall;
    }

    public void setCostOfCall(BigDecimal costOfCall) {
        this.costOfCall = costOfCall;
    }

    public BigDecimal getOtherFee() {
        return otherFee;
    }

    public void setOtherFee(BigDecimal otherFee) {
        this.otherFee = otherFee;
    }

    public BigDecimal getDeductionFee() {
        return deductionFee;
    }

    public void setDeductionFee(BigDecimal deductionFee) {
        this.deductionFee = deductionFee;
    }

    public BigDecimal getProfessionalFee() {
        return professionalFee;
    }

    public void setProfessionalFee(BigDecimal professionalFee) {
        this.professionalFee = professionalFee;
    }

    public BigDecimal getTravelFee() {
        return travelFee;
    }

    public void setTravelFee(BigDecimal travelFee) {
        this.travelFee = travelFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Integer getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(Integer recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getInpUserId() {
        return inpUserId;
    }

    public void setInpUserId(String inpUserId) {
        this.inpUserId = inpUserId;
    }

    public String getInpDate() {
        return inpDate;
    }

    public void setInpDate(String inpDate) {
        this.inpDate = inpDate;
    }

    public String getPlaceOfInspection() {
        return placeOfInspection;
    }

    public void setPlaceOfInspection(String placeOfInspection) {
        this.placeOfInspection = placeOfInspection;
    }

    public String getVehicleNumber() {
        return vehicleNumber;
    }

    public void setVehicleNumber(String vehicleNumber) {
        this.vehicleNumber = vehicleNumber;
    }

    public String getDateOfAccident() {
        return dateOfAccident;
    }

    public void setDateOfAccident(String dateOfAccident) {
        this.dateOfAccident = dateOfAccident;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getApprvUser() {
        return apprvUser;
    }

    public void setApprvUser(String apprvUser) {
        this.apprvUser = apprvUser;
    }

    public String getApprvDatetime() {
        return apprvDatetime;
    }

    public void setApprvDatetime(String apprvDatetime) {
        this.apprvDatetime = apprvDatetime;
    }

    public String getEmailSendStatus() {
        return emailSendStatus;
    }

    public void setEmailSendStatus(String emailSendStatus) {
        this.emailSendStatus = emailSendStatus;
    }

    public Integer getGenerateOccurrency() {
        return generateOccurrency;
    }

    public void setGenerateOccurrency(Integer generateOccurrency) {
        this.generateOccurrency = generateOccurrency;
    }

    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public BigDecimal getAllocatedProfessionalfee() {
        return allocatedProfessionalfee;
    }

    public void setAllocatedProfessionalfee(BigDecimal allocatedProfessionalfee) {
        this.allocatedProfessionalfee = allocatedProfessionalfee;
    }

    public BigDecimal getApprovedProfessionalfee() {
        return approvedProfessionalfee;
    }

    public void setApprovedProfessionalfee(BigDecimal approvedProfessionalfee) {
        this.approvedProfessionalfee = approvedProfessionalfee;
    }

    public String getPolNumber() {
        return polNumber;
    }

    public void setPolNumber(String polNumber) {
        this.polNumber = polNumber;
    }

    public String getIsfClaimNo() {
        return isfClaimNo;
    }

    public void setIsfClaimNo(String isfClaimNo) {
        this.isfClaimNo = isfClaimNo;
    }

    public boolean isIsfPending() {
        return isIsfPending;
    }

    public void setIsfPending(boolean isfPending) {
        isIsfPending = isfPending;
    }
}
