/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.userProfile;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.sql.*;
import java.util.LinkedList;
import java.util.List;
public class UserProfileManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserProfileManager.class);

    private static UserProfileManager profileManager = null;
    private ConnectionPool cp = null;

    public UserProfileManager() {
        try {
            cp = ConnectionPool.getInstance();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static UserProfileManager getInstance() {
        if (profileManager == null) {
            profileManager = new UserProfileManager();
        }
        return profileManager;
    }

    private synchronized UserProfile getUserProfile(ResultSet rs) {
        UserProfile userProfile = new UserProfile();
        try {
            userProfile.setUsrcode(rs.getInt("t1.usrcode"));
            userProfile.setDispalyName(rs.getString("t1.fullname"));
            userProfile.setDesignation(rs.getString("t1.designation"));
            userProfile.setForeign_tour(rs.getString("t1.foreignTour"));
            userProfile.setAwards(rs.getString("t1.annualAward"));
            userProfile.setCertifications(rs.getString("t1.certification"));
            userProfile.setWpCode(rs.getString("t1.codenumber"));

            userProfile.setJoindDate(rs.getString("t1.datejoined"));

            userProfile.setSurname(rs.getString("t2.v_lastname"));
            userProfile.setOthername(rs.getString("t2.v_firstname"));
            userProfile.setEmail(rs.getString("t2.v_email"));
            userProfile.setB_image(rs.getBinaryStream("t1.photo"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userProfile;
    }

    public synchronized List<UserProfile> getUserProfileList(int usrcode) {
        List<UserProfile> m_UserProfileList = new LinkedList<UserProfile>();
        Connection conn = null;
        PreparedStatement ps = null;

        String strSql = "SELECT t1.*,t2.v_email,t2.v_lastname,t2.v_firstname "
                + "FROM "
                + "`profile_mst` as t1,"
                + "`usr_mst` as t2 "
                + "where "
                + "t1.usrcode=t2.n_usrcode  "
                + "AND "
                + "t1.usrcode=?";


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setInt(1, usrcode);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_UserProfileList.add(getUserProfile(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);

        }
        return m_UserProfileList;
    }


    public synchronized int getNextID(String tblName, String idFieldName, String searchKey) {
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(" + idFieldName + ") as txnID from " + tblName + " " + searchKey;
        int maxid = 0;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                maxid = rs.getInt("txnID");
            }
            maxid++;
            rs.close();

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return maxid;
    }

    public synchronized boolean insertUserProfile(int usrcode, String fullname, String codenumber,
                                                  String datejoined, String designation, String location, String imagename, byte data[], String foreignTour,
                                                  String annualAward, String certification, String inpuser) {

        boolean updateStat = false;

        Connection conn = null;
        PreparedStatement ps = null;
        Statement stmt = null;

        try {
            conn = getJDBCConnection();
            stmt = conn.createStatement();
            ps = conn.prepareStatement("insert into profile_mst (usrcode, fullname, codenumber, datejoined,"
                    + "designation, location, imagename, photo, foreignTour, annualAward, certification, inpuser, inptime) "
                    + "values(?,?,?,?,?,?,?,?,?,?,?,?,current_timestamp)");

            ps.setInt(1, usrcode);
            ps.setString(2, fullname);
            ps.setString(3, codenumber);
            ps.setString(4, datejoined);
            ps.setString(5, designation);
            ps.setString(6, location);

            try {
                if (data != null) {
                    //fis = new FileInputStream(reportObject);
                    //ps.setString(6,reportObject.getName());
                    //ps.setBinaryStream(7,fis,(int) reportObject.length());
                    //---------------------------------------
                    ps.setString(7, imagename);
                    //ps.setBytes(7,data);
                    ps.setBinaryStream(8, new ByteArrayInputStream(data), data.length);
                } else {

                    ps.setString(7, "");
                    ps.setBytes(8, data);//(8,new ByteArrayInputStream(null));


                }
            } catch (Exception e) {
                ps.setString(7, "");
                ps.setBytes(8, data);
            }
            ps.setString(9, foreignTour);
            ps.setString(10, annualAward);
            ps.setString(11, certification);
            ps.setString(12, inpuser);



            int i = ps.executeUpdate();

            if (i > 0) {
                updateStat = true;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
                if (stmt != null) {
                    stmt.close();
                    stmt = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateStat;
    }

    public synchronized boolean updateUserProfile(int usrcode, String fullname, String codenumber,
                                                  String datejoined, String designation, String location, String imagename, byte data[], String foreignTour,
                                                  String annualAward, String certification, String inpuser) {

        boolean updateStat = false;

        Connection conn = null;
        PreparedStatement ps = null;
        Statement stmt = null;
        String logString = "Update User Profile -> UserID=" + inpuser;
        //FileInputStream fis = null;
        //String imagePath = parameters.getImagesDirectory();


        try {
            conn = getJDBCConnection();//getConnection();///
            stmt = conn.createStatement();
            if (data == null) {
                ps = conn.prepareStatement("UPDATE profile_mst set fullname=?,codenumber=?,datejoined=?,"
                        + "designation=?,location=?,foreignTour=?,annualAward=?,certification=?,inpuser=?,inptime=current_timestamp WHERE usrcode=?");

                ps.setInt(10, usrcode);
                ps.setString(1, fullname);
                ps.setString(2, codenumber);
                ps.setString(3, datejoined);
                ps.setString(4, designation);
                ps.setString(5, location);

                ps.setString(6, foreignTour);
                ps.setString(7, annualAward);
                ps.setString(8, certification);
                ps.setString(9, inpuser);


            } else {
                ps = conn.prepareStatement("UPDATE profile_mst set fullname=?,codenumber=?,datejoined=?,"
                        + "designation=?,location=?,imagename=?,photo=?,foreignTour=?,annualAward=?,certification=?,inpuser=?,inptime=current_timestamp WHERE usrcode=?");

                ps.setInt(12, usrcode);
                ps.setString(1, fullname);
                ps.setString(2, codenumber);
                ps.setString(3, datejoined);
                ps.setString(4, designation);
                ps.setString(5, location);

                try {
                    if (data != null) {

                        ps.setString(6, imagename);
                        ps.setBinaryStream(7, new ByteArrayInputStream(data), data.length);
                        ps.setString(8, foreignTour);
                        ps.setString(9, annualAward);
                        ps.setString(10, certification);
                        ps.setString(11, inpuser);

                    }
                } catch (Exception e) {
                    ps.setString(6, "");
                    ps.setBytes(7, data);
                }

            }

            int i = ps.executeUpdate();

            if (i > 0) {
                updateStat = true;

            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
                if (stmt != null) {
                    stmt.close();
                    stmt = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateStat;
    }

    public synchronized boolean SaveOrModifyRec(String usrcode, String fullname, String codenumber,
                                                String datejoined, String designation, String location, String imagename, byte data[], String foreignTour,
                                                String annualAward, String certification, String inpuser) {
        boolean b = false;
        int n_usrcode = Integer.parseInt(usrcode);

        if (DbRecordCommonFunction.getInstance().isRecExists("profile_mst", "usrcode='" + usrcode.trim() + "'")) {

            b = updateUserProfile(n_usrcode, fullname, codenumber, datejoined, designation, location, imagename, data, foreignTour, annualAward, certification, inpuser);

        } else {

            b = insertUserProfile(n_usrcode, fullname, codenumber, datejoined, designation, location, imagename, data, foreignTour, annualAward, certification, inpuser);

        }

        return b;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
